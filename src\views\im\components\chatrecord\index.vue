<!-- im 右侧聊天记录 -->
<template>
  <div style="height:100%;width:104%;margin-top: -15px;overflow:hidden;">
    <chat-record class="content"
                 :get-data-list="getDataList"></chat-record>
    <div class="pager"
         v-if="getDataList.length > 0">
      <!-- <el-button type="text"
                 v-if="getDataList.length < total "
                 :loading="loadMore_loading"
                 @click="loadMoreMessageHistory">{{loadMore_loading ?'加载中':'加载更多'}}</el-button>
      <span v-else>- 没有更多了 -</span> -->
      <el-pagination background
                     style="text-align:right"
                     layout="prev, pager, next"
                     :current-page="pageNum"
                     :total="total"
                     :page-size="size"
                     :pager-count="5"
                     @current-change=loadMoreMessageHistory>
      </el-pagination>
    </div>
  </div>
</template>

<script>
// 例如：import 《组件名称》 from '《组件路径》';
import chatRecord from '@/components/im/chatRecord'
import { getbeforeHistoryMessage } from '@/api/im_view/index';
import { containerMessage } from '@/views/im_view/components/im_container_tool/message_tool';
export default {
  components: { chatRecord },
  data () {
    return {
      // 新版本增加变量
      searchTime: '',
      pageNum: 0, // 历史记录页数,0：从最后一页开始
      size: 20, // 历史记录条数
      total: 0, // 总条数
      getDataList: [], // 消息数组
      loadMore_loading: false // 加载更多loading
    };
  },
  // 监听属性 类似于data概念
  computed: {
    containerid: function () {
      return this.$store.getters.containerid;
    },
  },
  watch: {
    containerid: function (oldval, newval) {
      this.$nextTick(() => {
        this.pageNum = 0;
        this.total = 0;
        this.getMessageHistory();
      })

    }
  },
  // 方法集合
  methods: {
    /** 
     * 分页加载 
     */
    loadMoreMessageHistory (currentPage) {
      this.pageNum = currentPage;
      // this.loadMore_loading = true;
      this.getMessageHistory();
    },

    /** 
     * 获取历史聊天记录
     */
    getMessageHistory () {
      const that = this;

      const params = {
        pageNum: that.pageNum,
        size: that.size,
        time: new Date().getTime(),
        userId: that.$store.getters.khid,
        appId: that.$store.getters.chat.appId,
        guestId: that.$store.getters.chat.oldShopId,
        currentDialogId: that.$store.getters.containerid == that.$store.getters.khid ? undefined : that.$store.getters.containerid,
        type: '4' // 客服工作台右边栏历史消息查询类型
      };
      if (!params.userId) {
        return;
      }
      getbeforeHistoryMessage(params).then(res => {
        // that.loadMore_loading = false
        if (res.code && res.code == 1 && res.data && res.data.records) {
          let messageArray = {
            messages: res.data.records
          };
          let data = containerMessage(messageArray);
          // if (that.pageNum > 1) {
          //   // 向数组追加消息
          //   that.getDataList = that.getDataList.concat(data.messages);
          // } else {

          // }
          that.getDataList = data.messages

          that.searchTime = res.data.time
            ? Number(res.data.time)
            : that.searchTime;
          that.total = Number(res.data.total);
          that.pageNum = res.data.pageNum;
        } else {
          that.$XyyMessage.error(res.msg || '数据异常！');
        }
        that.requestTime = false;
      })
    }
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created () {
    this.getMessageHistory(); // 获取会话记录
  },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted () {

  },
  beforeCreate () { }, // 生命周期 - 创建之前
  beforeMount () { }, // 生命周期 - 挂载之前
  beforeUpdate () { }, // 生命周期 - 更新之前
  updated () { }, // 生命周期 - 更新之后
  beforeDestroy () { }, // 生命周期 - 销毁之前
  destroyed () { }, // 生命周期 - 销毁完成
  activated () { }, // 如果页面有keep-alive缓存功能，这个函数会触发
}
</script>
<style lang='scss' scoped>
.content {
  height: calc(100% + 15px - 50px);
  overflow-y: scroll;
}

.pager {
  width: 100%;
  padding: 10px 10px;
  font-size: 12px;
  color: #909399;
  text-align: center;
  position: absolute;
  right: 0;
  bottom: 0;
  span {
    font-size: 12px;
    color: #909399;
    text-align: center;
  }
}
</style>