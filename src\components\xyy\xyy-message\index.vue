<template>
  <transition name="xyy-message-fade">
    <div
      v-show="visible"
      :class="[type ? `xyy-message--${ type }` : '']"
      :style="positionStyle"
      class="xyy-message"
      @mouseenter="clearTimer"
      @mouseleave="startTimer"
    >
      <svg-icon :icon-class="typeClass" class="xyy-message-icon" />
      <slot>
        <p v-if="!htmlString" class="xyy-message__content">{{ message }}</p>
        <p v-else class="xyy-message__content" v-html="message"></p>
      </slot>
      <i
        v-if="showClose || duration <= 0"
        class="el-message__closeBtn el-icon-close"
        @click="close"
      ></i>
    </div>
  </transition>
</template>

<script type="text/babel">
export default {
  name: 'XyyMessage',
  data() {
    return {
      visible: false, // 当前是显示or隐藏
      message: '提示', // 传入的文字或者html模板
      duration: 3000, // 多少秒后消失
      type: 'info', // 提示类型  'success', 'warning', 'info', 'error'
      iconClass: '', // 提示类型对应的icon
      closed: false, // 关闭后visible为false
      verticalOffset: 20, // 距离顶部的高度 动态配置
      timer: null, // 关闭的定时器
      showClose: false, // 是否显示关闭按钮
      htmlString: false // 支持传入html
    };
  },

  computed: {
    typeClass() {
      return this.type || 'info';
    },
    positionStyle() {
      return {
        top: `${this.verticalOffset}px`
      };
    }
  },

  watch: {
    closed(newVal) {
      if (newVal) {
        this.visible = false;
        this.$el.addEventListener('transitionend', this.handleAfterLeave);
      }
    }
  },
  beforeMount() {
    // 如果已经存在，则阻止出现第二次
    const node = document.querySelector('.xyy-message');
    if (node && node.parentNode) {
      node.parentNode.removeChild(node);
    }
  },
  mounted() {
    this.startTimer();
    document.addEventListener('keydown', this.keydown);
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.keydown);
  },

  methods: {
    handleAfterLeave() {
      this.$el.removeEventListener('transitionend', this.handleAfterLeave);
      this.$destroy(true);
      this.$el.parentNode.removeChild(this.$el);
    },

    close() {
      this.closed = true;
    },

    clearTimer() {
      clearTimeout(this.timer);
    },

    startTimer() {
      if (this.duration > 0) {
        this.timer = setTimeout(() => {
          if (!this.closed) {
            this.close();
          }
        }, this.duration);
      }
    },
    keydown(e) {
      if (e.keyCode === 27) {
        // esc关闭消息
        if (!this.closed) {
          this.close();
        }
      }
    }
  }
};
</script>
<style lang="scss" scoped>
div,
p {
  margin: 0;
  padding: 0;
}
.xyy-message {
  position: fixed;
  top: 80px;
  left: 50%;
  min-width: 200px;
  box-sizing: border-box;
  border-radius: 4px;
  font-size: 14px;
  color: #000;
  line-height: 20px;
  padding: 10px 20px;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  z-index: 100000;
  /* icon 的图标*/
  &-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
  &--info {
    border-color: #91d5ff;
    background-color: #e6f7ff;
  }
  &--success {
    border-color: #b7eb8f;
    background-color: #f6ffed;
  }
  &--warning {
    border-color: #ffe58f;
    background-color: #fffbe6;
  }
  &--error {
    border-color: #ffa39e;
    background-color: #fff1f0;
  }
  /* 文字显示 */
  &__content {
    max-width: 444px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  /* 动画过渡 */
  &-fade {
    transition: opacity 500ms cubic-bezier(0.23, 1, 0.32, 1);
  }
  &-fade-enter-active,
  &-fade-leave-active {
    margin-top: 0;
    transition: all 0.5s;
  }

  &-fade-enter,
  &-fade-leave-active {
    margin-top: -20px;
    opacity: 0;
  }
}
</style>
