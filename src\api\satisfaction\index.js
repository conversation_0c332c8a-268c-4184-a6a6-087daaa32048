import request from '@/utils/request';
import { formData } from '@/utils/index';
/**
 * 获取满意度评价列表
 */
export function getSatisfactionList(page) {
  return request({
    url: '/satisfaction/list',
    method: 'get',
    params: page
  });
}

/**
 * 改变模板状态
 * @param {状态数据} data
 */
export function changeTemplateStatus(data) {
  return request({
    url: '/satisfaction/statusChange',
    method: 'post',
    data: formData(data)
  });
}

/**
 * 删除模板数据
 * @param {模板id} id
 */
export function delTemplateData(id) {
  return request({
    url: '/satisfaction/delete',
    method: 'post',
    data: formData({ id })
  });
}

/**
 * 获取表单数据
 */
export function getFormDatas(params) {
  return request({
    url: '/satisfaction/fromInfoList',
    method: 'get',
    params: params
  });
}

/**
 * 保存模板数据
 * @param {表单数据} data
 */
export function saveTemplateData(data) {
  return request({
    url: '/satisfaction/add',
    method: 'post',
    data
  });
}

/**
 * 更新模板数据
 * @param {表单数据} data
 */
export function editTemplateData(data) {
  return request({
    url: '/satisfaction/update',
    method: 'post',
    data
  });
}

/**
 * 获取模板详情数据
 */
export function getTemplateData(params) {
  return request({
    url: '/satisfaction/details',
    method: 'get',
    params
  });
}
