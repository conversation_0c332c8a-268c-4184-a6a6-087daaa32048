<template>
  <div>
    <el-dialog
      :title="'指派'"
      :visible.sync="dialogFormVisible"
      width="400px"
      :before-close="befoClose"
    >
      <el-input placeholder="输入关键字进行过滤" v-model="filterText" class="searchinput">
        <el-button slot="append" icon="el-icon-search"></el-button>
      </el-input>

      <div class="filter-tree">
        <el-tree
          :data="data"
          :props="defaultProps"
          :default-expand-all="false"
          highlight-current
          @node-click="checkAction"
          :filter-node-method="filterNode"
          ref="tree"
        >
          <span slot-scope="{ node, data }">
            <i :class="data.icon"></i>
            {{ data.name }}
          </span>
        </el-tree>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAction()">取 消</el-button>
        <el-button type="primary" @click="sureAction()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>


<script>
export default {
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {
      filterText: '',
      chooseNode: {},
      defaultProps: {
        children: 'kefus',
        label: 'name'
      },
      otherkefu: false // 对话框是否被选中过
    };
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    cancelAction() {
      this.otherkefu = false;
      this.$emit('treedialog', 'hiddenAction', false);
    },
    sureAction() {
      if (this.otherkefu) {
        this.otherkefu = false;
        this.$emit('treedialog', 'clickNode', this.chooseNode);
        // this.$emit('treedialog', 'hiddenAction', false);
      } else {
        this.$emit('treedialog', 'clickNode', '11');
      }
    },
    checkAction(data, node, tree) {
      this.otherkefu = true;
      if (node.level === 1) return;
      this.chooseNode = data;
    },
    befoClose(done) {
      this.$emit('treedialog', 'hiddenAction', false);
    }
  }
};
</script>

<style>
.el-icon-transfer-group {
  background: url('/src/assets/im/transfer_group.png') center no-repeat;
  background-size: cover;
}

.el-icon-transfer-group:before {
  content: '\e7ac';
  font-size: 16px;
  visibility: hidden;
}

.el-icon-transfer-user {
  background: url('/src/assets/im/transfer_user.png') center no-repeat;
  background-size: cover;
}

.el-icon-transfer-user:before {
  content: '\e7ac';
  font-size: 16px;
  visibility: hidden;
}
</style>

<style scoped>
.searchinput {
  /* border: 1px solid #e4e4eb; */
  margin-bottom: 20px;
}

.filter-tree {
  border: 1px solid #e4e4eb;
  height: 356px;
  border-radius: 2px;
}
</style>
