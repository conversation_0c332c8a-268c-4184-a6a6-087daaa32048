import Layout from '@/views/layout/Layout';
export default [
  {
    path: '/chat',
    component: Layout,
    meta: {
      title: '会话',
      mark: 'config',
      code: 'menu:cs:config'
    },
    children: [
      {
        path: 'PersonnelManage',
        name: 'PersonnelManage',
        meta: {
          title: '人员管理',
          code: 'menu:csim:staff',
          mark: 'config',
          componentName:'compPersonnelManage'
        },
        component: () => import('@/views/configuration/PersonnelManage')
      },
      {
        path: 'addPersonnel/:id',
        name: 'addPersonnel',
        meta: {
          title: '添加人员',
          code: 'menu:csim:staff',
          mark: 'config'
        },
        component: () => import('@/views/configuration/addPersonnel'),
        hidden: true
      },
      {
        path: 'employeeList',
        name: 'employeeList',
        meta: {
          title: '员工组',
          code: 'menu:csim:staffgroup',
          mark: 'config',
          componentName:'compEmployeeList'
        },
        component: () => import('@/views/im-config/employees/list')
      },
      {
        path: 'employeeEdit/:id',
        name: 'employeeEdit',
        meta: {
          title: '新建员工组',
          code: 'menu:csim:staffgroup',
          mark: 'config'
        },
        component: () => import('@/views/im-config/employees/edit'),
        hidden: true
      },
      {
        path: 'localSetUpNew',
        name: 'localSetUpNew',
        meta: {
          title: '地域组设置',
          code: 'menu:csim:areagroup',
          mark: 'config'
        },
        component: () => import('@/views/configuration/localSetUpNew'),
        hidden: true
      },
      {
        path: 'localSetUp',
        name: 'localSetUp',
        meta: {
          title: '地域组管理',
          code: 'menu:csim:areagroup',
          mark: 'config',
          componentName:'compLocalSetUp'
        },
        component: () => import('@/views/configuration/localSetUp')
      },
      {
        path: 'regionalMenu',
        name: 'regionalMenu',
        meta: {
          title: '微信地域菜单',
          mark: 'config',
          code: 'menu:csim:areamenu',
          componentName:'compRegionalMenu'
        },
        component: () => import('@/views/configuration/regionalMenu')
      },
      {
        path: 'seesion_config',
        name: 'seesionConfig',
        meta: {
          title: '会话分配',
          code: 'menu:csim:assign',
          mark: 'config',
          componentName:'compSeesionConfig'
        },
        component: () => import('@/views/configuration/seesion_config')
      },
      {
        path: 'personalSetting',
        name: 'personalSetting',
        meta: {
          title: '个人设置',
          code: 'menu:csim:setmyinfo',
          mark: 'config',
          componentName:'compPersonalSetting'
        },
        component: () => import('@/views/im-config/personal-setting/edit')
      },
      {
        path: 'WeLanguage',
        name: 'WeLanguage',
        meta: {
          title: '欢迎语',
          code: 'menu:csim:sethi',
          mark: 'config',
          componentName:'compWeLanguage'
        },
        component: () => import('@/views/configuration/WeLanguage')
      },
      {
        path: 'auto_news',
        name: 'autoNews',
        meta: {
          title: '自动消息',
          code: 'menu:csim:autoreply',
          mark: 'config',
          componentName:'compAutoNews'
        },
        component: () => import('@/views/configuration/auto_news')
      },
      {
        path: 'autoReply',
        name: 'autoReply',
        meta: {
          title: '自动回复配置',
          code: 'menu:csim:autolink',
          mark: 'config',
          componentName:'compAutoReply'
        },
        component: () => import('@/views/im-config/autoreply/autore')
      },
      {
        path: 'autoList/:id',
        name: 'autoList',
        meta: {
          title: '新建模板',
          // code: 'menu:csim:link',
          mark: 'config'
        },
        component: () => import('@/views/im-config/autoreply/autoply'),
        hidden: true
      },
      {
        path: 'DisableWord',
        name: 'DisableWord',
        meta: {
          title: '禁用词',
          code: 'menu:csim:forbiddenwords',
          mark: 'config',
          componentName:'compDisableWord'
        },
        component: () => import('@/views/configuration/DisableWord')
      },
      {
        path: 'quickReply',
        name: 'quickReply',
        meta: {
          title: '快捷回复',
          code: 'menu:csim:quickreply',
          mark: 'config',
          componentName:'compQuickReply'
        },
        component: () => import('@/views/im-config/quick-reply/list')
      },
      {
        path: 'image',
        name: 'image',
        meta: {
          title: '常用图片',
          code: 'menu:csim:picture',
          mark: 'config',
          componentName:'compImage'
        },
        component: () => import('@/views/im-config/use_image/list')
      },
      {
        path: 'link',
        name: 'link',
        meta: {
          title: '常用链接',
          code: 'menu:csim:link',
          mark: 'config',
          componentName:'compLink'
        },
        component: () => import('@/views/im-config/link/list')
      },
      {
        path: 'linkEdit/:id',
        name: 'linkEdit',
        meta: {
          title: '新建链接',
          code: 'menu:csim:link',
          mark: 'config'
        },
        component: () => import('@/views/im-config/link/edit'),
        hidden: true
      },
      {
        path: 'faq',
        name: 'faq',
        meta: {
          title: 'FAQ',
          code: 'menu:csim:faq',
          mark: 'config',
          componentName:'compFaq'
        },
        component: () => import('@/views/im-config/faq/list')
      },
      {
        path: 'BlacklistManage',
        name: 'BlacklistManage',
        meta: {
          title: '黑名单管理',
          code: 'menu:csim:blacklist',
          mark: 'config',
          componentName:'compBlacklistManage'
        },
        component: () => import('@/views/configuration/BlacklistManage')
      },
      {
        path: 'WeChatBind',
        name: 'WeChatBind',
        meta: {
          title: '微信访客绑定',
          code: 'menu:csim:binduser',
          mark: 'config',
          componentName:'compWeChatBind'
        },
        component: () => import('@/views/configuration/WeChatBind')
      },
      {
        path: 'serviceConfig',
        name: 'serviceConfig',
        meta: {
          title: '服务总结',
          code: 'menu:csim:servicesummary',
          mark: 'config',
          componentName:'compServiceConfig'
        },
        component: () => import('@/views/configuration/serviceConfig')
      },

      {
        path: 'commentDistrbiute',
        name: 'commentDistrbiute',
        meta: {
          title: '留言分发',
          code: 'menu:csim:assignleavemsg',
          mark: 'config',
          componentName:'compCommentDistrbiute'
        },
        component: () => import('@/views/im-config/comment/distribute')
      },
    ]
  }

];
