<template>
  <div class="history-list">
    <el-form :model="search" class="search-box">
      <el-form-item class="top-item" label="时间筛选">
        <el-date-picker
          v-model="search.dateRange"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item class="top-item">
        <field :keys="6" :preview="search.el" refs="field"></field>
        <el-button type="primary">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="list-header">
      <xyy-button icon-class="btn-add" @click="toAdd">新建工单</xyy-button>
      <el-checkbox v-model="search.isPending">只显示在途工单</el-checkbox>
    </div>
    <ul class="list-body">
      <li class="list-item">
        <div class="list-item-title">
          <span class="order-code">12233332233223</span>
          <span class="order-date">2019-12-13 08:02</span>
          <span class="order-status">待领取</span>
        </div>
        <div class="list-item-body">
          <dl>
            <dt>
              <span>渠道类型：</span>
              <span>微信</span>
            </dt>
            <dt>
              <span>渠道类型：</span>
              <span>微信</span>
            </dt>
            <dt>
              <span>渠道类型：</span>
              <span>微信</span>
            </dt>
            <dt>
              <span>渠道类型：</span>
              <span>微信</span>
            </dt>
          </dl>
          <el-button type="text">
            查看
            <i class="el-icon-arrow-right el-icon--right"></i>
          </el-button>
        </div>
      </li>
      <li class="list-item">
        <div class="list-item-title">
          <span class="order-code">12233332233223</span>
          <span class="order-date">2019-12-13 08:02</span>
          <span class="order-status received">已领取</span>
        </div>
        <div class="list-item-body">
          <dl>
            <dt>
              <span>渠道类型：</span>
              <span>微信</span>
            </dt>
            <dt>
              <span>渠道类型：</span>
              <span>微信</span>
            </dt>
            <dt>
              <span>渠道类型：</span>
              <span>微信</span>
            </dt>
            <dt>
              <span>渠道类型：</span>
              <span>微信</span>
            </dt>
          </dl>
          <el-button type="text">
            查看
            <i class="el-icon-arrow-right el-icon--right"></i>
          </el-button>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import field from '@/components/Fields/preview'; // 字段组件
export default {
  components: {
    field
  },
  props: {
    status: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      search: {
        dateRange: '',
        el: {
          id: 33,
          formId: 1,
          nodeId: 71,
          fieldId: 23,
          authType: 0,
          authTypeCondition: 2,
          required: 1,
          requiredCondition: 0,
          ganged: 0,
          deleted: null,
          duplicate: null,
          creatorId: null,
          orgId: null,
          gmtCreate: null,
          gmtModified: null,
          fieldText: '问题分类',
          fieldName: '问题分类',
          fieldKey: '1161472515860729856',
          fieldType: 6,
          defaultValue: '0',
          optionSettings: {
            // 选项设置卡
            treeOptions: {
              // 级联选项
              optionsValue: [1, 2, 3],
              optionsArray: [
                {
                  value: 1,
                  label: '一级 1',
                  children: [
                    {
                      value: 2,
                      label: '2级'
                    }
                  ]
                },
                {
                  value: 2,
                  label: '2级 2',
                  children: [
                    {
                      value: 2,
                      label: '2级'
                    }
                  ]
                }
              ]
            }
          },
          tips: '',
          nodeConditionList: null
        },
        isPending: false
      }
    };
  },
  methods: {
    toAdd() {
      this.$emit('update:status', false);
    },
    reset() {}
  }
};
</script>

<style lang="scss" scoped>
.search-box {
  border-bottom: 1px dashed #e4e4eb;
  padding-bottom: 20px;
  /deep/.el-form-item.top-item {
    margin-bottom: 0;
    &:first-child {
      margin-bottom: 20px;
    }
    .el-form-item__label {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(41, 41, 51, 1);
      padding-right: 8px;
    }
    .el-date-editor.el-input__inner {
      height: 36px;
      line-height: 36px;
      width: calc(100% - 64px);
      /deep/.el-range__icon,
      /deep/.el-range-separator,
      /deep/.el-range__close-icon {
        line-height: 28px;
      }
    }

    .preview {
      width: calc(100% - 162px);
      float: left;
      margin-right: 20px;
      .single {
        margin: 2px 0;
        width: 100%;
        .el-form-item__label {
          width: auto;
        }
        .el-form-item__content {
          width: calc(100% - 64px);
          .el-cascader {
            height: 36px;
            line-height: 36px;
            min-width: 100%;
          }
        }
      }
    }

    button {
      width: 68px;
      height: 36px;
      line-height: 36px;
      padding: 0;
      &.el-button--default {
        border: 1px solid rgba(228, 228, 235, 1);
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
        &:hover,
        &:focus {
          border: 1px solid rgba(228, 228, 235, 1);
          background: #fff;
          color: rgba(87, 87, 102, 1);
        }
      }
    }
    .el-button + .el-button {
      margin-left: 2px;
    }
  }
}
.list-header {
  margin: 15px 0 20px;
  .el-checkbox {
    float: right;
    line-height: 36px;
  }
}
.list-body {
  padding: 0;
  margin: 0;
  .list-item {
    border: 1px solid rgba(241, 242, 244, 1);
    box-sizing: border-box;
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
    .list-item-title {
      background: rgba(241, 242, 244, 0.33);
      height: 36px;
      line-height: 36px;
      padding: 0 12px;
      span {
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(144, 147, 153, 1);
        &:first-child {
          margin-right: 24px;
        }
        &.order-status {
          float: right;
          height: 20px;
          background: rgba(253, 246, 236, 1);
          border-radius: 2px;
          width: 52px;
          text-align: center;
          margin: 8px 0;
          line-height: 20px;
          color: rgba(230, 162, 60, 1);
          &.received {
            background: rgba(237, 237, 240, 1);
            color: rgba(174, 174, 191, 1);
          }
        }
      }
    }
    .list-item-body {
      position: relative;
      dl {
        margin: 0;
        padding: 12px;
        dt {
          margin-bottom: 12px;
          span {
            height: 20px;
            line-height: 20px;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(87, 87, 102, 1);
          }
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
      .el-button {
        position: absolute;
        top: 12px;
        right: 12px;
        height: 20px;
        padding: 0;
        line-height: 20px;
        .el-icon--right {
          margin-left: 0;
        }
      }
    }
  }
}
</style>
