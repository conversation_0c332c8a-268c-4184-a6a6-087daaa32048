<template>
  <div class="addPersonnel">
    <xyy-list-page>
      <template slot="header">
        <img :src="avatar" height="72" width="72" v-if="showOn" class="moren" />
        <img :src="formPersonnel.avatar" height="72" width="72" v-else class="bumoren" />
        <el-upload
          class="upload-demo"
          action="#"
          accept=".jpg, .jpeg, .png, .bmp"
          :show-file-list="false"
          :before-upload="beforeUpload"
          :http-request="uploadFileRequest"
          :on-change="UploadFile"
        >
          <el-button size="small" type="primary">更换头像</el-button>
        </el-upload>
      </template>
      <template slot="body">
        <el-form ref="formPersonnel" :rules="rules" :model="formPersonnel" label-width="150px">
          <el-form-item label="用户名" prop="username">
            <!--<el-input v-model="formPersonnel.username" placeholder="请输入内容" @input="handleSelect" :disabled="disabledTrue"></el-input>-->
            <el-select
              v-model="formPersonnel.username"
              :remote-method="remoteMethod"
              @change="handleSelect"
              :disabled="disabledTrue"
              filterable
              remote
              clearable
              placeholder="请输入"
            >
              <el-option
                v-for="item in options2"
                :key="item.id"
                :label="item.label"
                :value="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="工牌号" prop="code">
            <el-input v-model="formPersonnel.code" :disabled="disabledTrue"></el-input>
          </el-form-item>
          <el-form-item label="姓名" prop="name">
            <el-input v-model="formPersonnel.name"></el-input>
          </el-form-item>
          <el-form-item label="昵称" prop="nickname">
            <el-input v-model="formPersonnel.nickname"></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="state">
            <el-select v-model="formPersonnel.state" placeholder="请选择">
              <el-option
                v-for="item in personnelState"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="角色" prop="role">
            <el-select v-model="formPersonnel.role" placeholder="请选择">
              <el-option
                v-for="item in personnelRole"
                :key="item.value"
                :label="item.name"
                :value="item.roleNo"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="服务数上线" prop="servicecount">
            <el-input v-model="formPersonnel.servicecount"></el-input>
          </el-form-item>
          <el-form-item label="所属员工组" prop="imkefugropuid">
            <el-select v-model="formPersonnel.imkefugropuid" placeholder="请选择" :clearable="true">
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.groupName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属渠道" prop="appIds">
            <el-select v-model="formPersonnel.appIds" multiple placeholder="请选择" :clearable="true">
              <el-option
                v-for="item in options1"
                :key="item.appId"
                :label="item.appName"
                :value="item.appId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="添加访客黑名单" prop="blackListAuth">
            <el-radio-group v-model="formPersonnel.blacklistauth">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              maxlength="100"
              show-word-limit
              v-model="formPersonnel.remark"
            ></el-input>
          </el-form-item>

          <el-form-item label="企业微信二维码">
            <!-- <img :src="avatar" height="140" width="140" v-if="showOnWX" class="moren" /> -->
            <div v-if="showOnWX" class="wx-box">
              <span class="wx-text">请上传企业微信二维码</span>
            </div>
            <img :src="formPersonnel.wechatImg" height="140" width="140" v-else class="bumoren" />
            <el-upload
              class="upload-demo"
              action="#"
              accept=".jpg, .jpeg, .png, .bmp"
              :show-file-list="false"
              :before-upload="beforeUpload"
              :http-request="uploadFileRequestWX"
              :on-change="UploadFile"
            >
              <el-button size="small" type="primary" style=" width: 140px;">上传图片</el-button>
            </el-upload>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="checkTimer(handerSave('formPersonnel'),'timer')()">保存</el-button>
            <el-button @click="resetForm('formPersonnel')">取消</el-button>
          </el-form-item>
        </el-form>
      </template>
    </xyy-list-page>
  </div>
</template>

<script>
import XyyListPage from '../../components/xyy/xyy-list-page/index';
import {
  uploadPictures,
  userNameQuery,
  newModified,
  getAddModify,
  fuzzyQuery,
  getRole,
  getSources
} from '@/api/configuration/PersManage';
import { EmployeeGroups1 } from '@/api/configuration/RegionalMenu';
import Cookies from 'js-cookie';
import message from '../../store/modules/message';
export default {
  name: 'addPersonnel',
  components: { XyyListPage },
  data() {
    let checkServicecount = (rule, value, callback) => {
      if (value < 1 || value > 100) {
        callback(new Error('输入的值在1-100之间'));
      } else {
        callback();
      }
    };
    return {
      rules: {
        // 表单验证
        username: [
          { required: true, message: '用户名不能为空', trigger: 'blur' }
        ],
        nickname: [
          { required: true, message: '用户名不能为空', trigger: 'blur' }
        ],
        code: [{ required: true, message: '工牌号不能为空', trigger: 'blur' }],
        state: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
        role: [{ required: true, message: '角色不能为空', trigger: 'blur' }],
        servicecount: [
          { required: true, message: '服务不能为空', trigger: 'blur' },
          { validator: checkServicecount, trigger: 'blur' }
        ]
      },
      timer: null,
      showOn: true, // 控制默认图片的现实隐藏
      showOnWX: true, // 控制默认图片的现实隐藏
      avatar:
        'https://upload.ybm100.com/im/image/0746BD2E-1D72-45B3-B47C-86ADE516E786.png', // 默认头像的url
      formPersonnel: {
        // 表单
        username: '', // 用户名
        code: '', // 工牌号
        nickname: '', // 昵称
        name: '', // 姓名
        state: '', // 状态
        role: '', // 角色
        servicecount: '', // 服务数
        appIds: [], // 所属渠道
        imkefugropuid: '', // 所属员工组
        blacklistauth: '', // 添加访客黑名单
        remark: '', // 备注
        avatar: ' ', // 头像的url
        wechatImg: '' // 微信企业二维码
      },
      disabledTrue: false,
      UploadFileSuccess: false,
      restaurants: [],
      timeout: null,
      personnelState: [
        {
          // 添加人员状态
          value: '0',
          label: '正常'
        },
        {
          value: '1',
          label: '禁用'
        },
        {
          value: '2',
          label: '删除'
        }
      ],
      personnelRole: [], // 添加人员角色
      options: [],
      options1: [],
      options2: [],
      valueTo: {}, // 点击编辑传递过来的值
      readOnly: false // 只读状态
    };
  },
  mounted() {
    this.refreshPage();
    this.getValue();
    //判断是否是编辑还是添加
    //编辑时进入的添加人员页面-传递的参数
    this.readOnly = this.$route.query.obj
      ? Boolean(this.$route.query.obj)
      : false;
    this.$store.dispatch('tagsView/updateVisitedView', {
      ...this.$route,
      meta: {
        title: this.readOnly ? '编辑人员' : '添加人员'
      }
    });
  },
  methods: {
    // 文件上传前
    beforeUpload(file) {
      if (file.size / 1024 / 1024 > 10) {
        // 附件上传大小的限制
        this.$XyyMessage.error('图片过大');
        return false;
      }
    },
    // 自定义上传事件；
    uploadFileRequest(file) {
      let that = this;
      let config = {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      };
      var formdata = new FormData();
      formdata.append('file', file.file);
      uploadPictures(formdata, config)
        .then(response => {
          that.UploadFileSuccess = true;
          if (response.code === 1) {
            that.$message('上传成功');
            that.showOn = false;
          } else {
            that.$message(response.msg);
          }
          this.formPersonnel = {
            ...this.formPersonnel,
            avatar: response.data // 头像
          };
        })
        .catch(function(error) {
          console.log(error);
        });
    },

    // 自定义上传事件；
    uploadFileRequestWX(file) {
      let that = this;
      let config = {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      };
      var formdata = new FormData();
      formdata.append('file', file.file);
      uploadPictures(formdata, config)
        .then(response => {
          that.UploadFileSuccess = true;
          if (response.code === 1) {
            that.$message('上传成功');
            that.showOnWX = false;
          } else {
            that.$message(response.msg);
          }
          this.formPersonnel = {
            ...this.formPersonnel,
            wechatImg: response.data // 头像
          };
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    // 上传文件
    UploadFile(file) {
      // let config = {
      //   headers: {
      //     'Content-Type': 'multipart/form-data'
      //   }
      // }
      // var formdata = new FormData()
      // formdata.append('file', file.raw)
      // uploadPictures(formdata, config).then(response => {
      // this.formPersonnel.avatar = response.data
      // this.UploadFileSuccess = true;
      // this.$message(response.msg);
      //   // 返回头像的url设置avatar的路径
      // }).catch(function(error) {
      //   console.log(error);
      // });
    },
    // 用户名远程搜索
    async remoteMethod(query) {
      if (query !== '') {
        var restaurants = await fuzzyQuery({
          username: query
        }).then(res => {
          this.restaurants = res.data;
          return res.data;
        });
        this.options2 = query
          ? restaurants.filter(this.createStateFilter(query))
          : restaurants;
      } else {
        this.options2 = [];
      }
    },
    async querySearchAsync(queryString, cb) {
      var restaurants = await fuzzyQuery({
        username: queryString
      }).then(res => {
        return res.data;
      });

      var results = queryString
        ? restaurants.filter(this.createStateFilter(queryString))
        : restaurants;
      cb(results);
    },

    createStateFilter(queryString) {
      return state => {
        return (
          state.realname.toLowerCase().indexOf(queryString.toLowerCase()) >
            -1 ||
          state.userId.toLowerCase().indexOf(queryString.toLowerCase()) > -1 ||
          state.staffNum.indexOf(queryString) > -1
        );
      };
    },

    //    选中用户名input中的文字
    handleSelect(val) {
      let that = this;
      this.restaurants.forEach((item, index) => {
        if (val === item.label) {
          that.formPersonnel.nickname = item.nickname;
          that.formPersonnel.oaid = item.oaId;
          that.formPersonnel.name = item.realname;
          that.formPersonnel.code = item.staffNum;
        }
      });
    },
    // 保存
    handerSave(formName) {
      let that = this;
      this.$refs[formName].validate(valid => {
        if (valid) {
          let params = {};
          // options2的length大于0 说明是新建的
          if (this.options2.length > 0) {
            this.options2.forEach((item, index) => {
              if (item.label === this.formPersonnel.username) {
                this.formPersonnel.username = item.account;
              }
            });
          }

          newModified(this.formPersonnel)
            .then(response => {
              if (response.code === 1) {
                this.$store.dispatch('tagsView/delView', this.$route);
                this.$router.replace({
                  name: 'PersonnelManage'
                });
                this.$message('保存成功');
                // 保存成功清空session
                sessionStorage.removeItem('getInfo');
                this.formPersonnel = {};
              } else {
                this.$message(response.msg);
              }
            })
            .catch(function(error) {
              this.$message(error);
            });
        } else {
          return false;
        }
      });
    },
    // 取消按钮事件
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.$store.dispatch('tagsView/delView', this.$route);
      this.$router.replace({
        name: 'PersonnelManage'
      });
    },
    // 刷新页面事件
    refreshPage() {
      // 判断时候有session存储
      if (sessionStorage.getItem('getInfo')) {
        this.formPersonnel = JSON.parse(sessionStorage.getItem('getInfo'));
        let dataListList = this.formPersonnel.avatar;
        let nullType =
          !dataListList &&
          typeof dataListList !== 'undefined' &&
          dataListList !== 0;
        if (dataListList === ' ' || nullType) {
          this.showOn = true;
          this.formPersonnel = {
            ...this.formPersonnel,
            avatar: ' '
          };
        } else {
          this.showOn = false;
        }

        let dataListListWX = this.formPersonnel.wechatImg;
        let nullTypeWX =
          !dataListListWX &&
          typeof dataListListWX !== 'undefined' &&
          dataListListWX !== 0;
        if (dataListListWX === ' ' || nullTypeWX) {
          this.showOnWX = true;
          this.formPersonnel = {
            ...this.formPersonnel,
            wechatImg: ' '
          };
        } else {
          this.showOnWX = false;
        }
      }
    },
    // 获取点击编辑传递过来的值
    async getValue() {
      let that = this;
      // 获取角色
      await getRole()
        .then(response => {
          response.data.push({ name: '请选择', roleNo: '0' });
          this.personnelRole = response.data;
        })
        .catch(function(error) {
          console.log(error);
        });
      //       编辑时进入的添加人员页面-传递的参数
      this.valueTo = this.$route.query.obj;
      //      如果是编辑进来的存在id
      if (this.valueTo) {
        this.disabledTrue = true;
        this.UploadFileSuccess = true;
        getAddModify({ kefuid: this.valueTo.id })
          .then(response => {
            // 点击编辑进入判断是否有头像，没有取默认值
            let dataListList = response.data.userKefu.avatar;
            let nullType =
              !dataListList &&
              typeof dataListList !== 'undefined' &&
              dataListList !== 0;
            if (dataListList === ' ' || nullType) {
              this.showOn = true;
              this.formPersonnel = {
                ...this.formPersonnel,
                avatar: ' '
              };
            } else {
              this.showOn = false;
            }

            let dataListListWX = response.data.userKefu.wechatImg;
            let nullTypeWX =
              !dataListListWX &&
              typeof dataListListWX !== 'undefined' &&
              dataListListWX !== 0;
            if (dataListListWX === ' ' || nullTypeWX) {
              this.showOnWX = true;
              this.formPersonnel = {
                ...this.formPersonnel,
                wechatImg: ' '
              };
            } else {
              this.showOnWX = false;
            }

            this.formPersonnel = {
              kefuid: this.valueTo.id,
              name: response.data.userKefu.name,
              avatar: response.data.userKefu.avatar, // 头像
              username: response.data.userKefu.username, // 用户名
              code: response.data.userKefu.code, // 工牌号
              nickname: response.data.userKefu.nickname, // 昵称
              state: String(response.data.userKefu.state), // 状态
              role: String(response.data.userKefu.role), // 角色
              imkefugropuid: Number(response.data.userKefu.imkefugropuid), // 员工组
              servicecount: response.data.userKefu.maxdialog, // 服务数
              appIds: response.data.userKefu.appIds, // 所属渠道
              blacklistauth: String(response.data.userKefu.blacklistauth), // 添加访客黑名单
              oaid: response.data.userKefu.oaid,
              remark: response.data.userKefu.remark, // 添加人员备注
              wechatImg: response.data.userKefu.wechatImg // 头像
            };
            // this.appIds.forEach((item, index) => {
            //   this.options1.forEach((itemL, indexL) => {
            //     if (item === itemL.appId) {
            //       item.name = itemL.name;
            //     }
            //   });
            // });
            sessionStorage.setItem(
              'getInfo',
              JSON.stringify(this.formPersonnel)
            );
          })
          .catch(function(error) {
            console.log(error);
          });
      } else {
        //  这是从新建进去的 清空sessionStorage
        sessionStorage.removeItem('getInfo');
        this.formPersonnel = {
          username: '', // 用户名
          code: '', // 工牌号
          nickname: '', // 昵称
          name: '', // 姓名
          state: '', // 状态
          role: '', // 角色
          servicecount: '', // 服务数
          appIds: [], // 所属渠道
          imkefugropuid: '', // 所属员工组
          blacklistauth: '0', // 添加访客黑名单
          remark: '', // 备注
          avatar: ' ' // 头像的url
        };
        this.showOn = true;
      }
      // 所屬员工组
      EmployeeGroups1()
        .then(response => {
          response.data.push({ groupName: '请选择', id: 0 });
          this.options = response.data;
        })
        .catch(function(error) {
          console.log(error);
        });

      // 首先先将头像上传并返回

      // 所屬渠道
      getSources()
        .then(response => {
          // response.data.push({ name: '请选择', corpid: 0 });
          this.options1 = response.data;
        })
        .catch(function(error) {
          console.log(error);
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.addPersonnel {
  /deep/.el-input__inner {
    height: 36px;
  }
  /deep/.el-input__icon {
    line-height: 36px;
  }
  /deep/.el-form-item {
    margin-bottom: 18px;
  }
}
</style>

<style lang="scss">
.addPersonnel {
  .el-upload-list--text {
    display: none;
  }
  .page {
    display: flex;
    flex-direction: row !important;
    padding-top: 21px;
    padding-left: 5%;
    .page-header {
      width: auto;
    }
    .page-body {
      width: auto;
      .el-input {
        width: 460px;
        height: 36px;
      }
      .el-textarea {
        width: 460px;
        height: 122px;
        border-radius: 2px;
        .el-textarea__inner {
          height: 100%;
        }
      }
    }
  }

  .wx-box {
    width: 140px;
    height: 140px;
    border: 1px solid #dcdfe6;
    font-size: 12px;
    color: rgba(144, 147, 153, 1);
    text-align: center;
    .wx-text {
      line-height: 140px;
    }
  }
}
</style>
