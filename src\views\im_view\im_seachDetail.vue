<template>
  <el-row type="flex" justify="start" align="top">
    <div class="detailLeft">
      <div class="content">
        <div class="title">服务总结</div>
        <el-row v-for="item in imFormDataExtend" :key="item.id" class="info">
          <span class="infoTit">{{ item.label }}:</span>
          <span class="infoCon">{{ item.labelValue }}</span>
        </el-row>
      </div>
      <div class="intro">
        <!-- <el-row class="title">
          创建人：
          <span class="con">{{ imFormData.creatorName }}</span>
        </el-row>
        <el-row class="title">
          创建时间：
          <span class="con">{{ imFormData.createTime }}</span>
        </el-row>
        <el-row class="title">
          所属坐席组：
          <span class="con">{{ imFormData.groupName }}</span>
        </el-row>
        <el-row class="title">
          修改时间：
          <span class="con">{{ imFormData.modifyTime }}</span>
        </el-row>-->

        <el-timeline>
          <el-timeline-item v-for="(record, index) in records" :key="index" color="#3B95A8">
            <!--标题-->
            <el-row class="title" v-if="index===records.length-1">
              创建人：
              <span class="con">{{ record.creatorName }}</span>
            </el-row>
            <el-row class="title" v-if="index!==records.length-1">
              修改人：
              <span class="con">{{ record.creatorName }}</span>
            </el-row>

            <el-row class="title" v-if="index===records.length-1">
              创建时间：
              <span class="con">{{ record.createTime }}</span>
            </el-row>
            <el-row class="title" v-if="index!==records.length-1">
              修改时间：
              <span class="con">{{ record.createTime }}</span>
            </el-row>
            <el-row class="title">
              所属坐席组：
              <span class="con">{{ record.groupName }}</span>
            </el-row>
            <div>
              <div class="content-box">
                <!--流程节点-->
                <div class="edit-content">
                  <div v-if="record.oldList">
                    <div class="edit-label">编辑前</div>
                    <open-info :data-info="getUneditDatas(record.oldList)" class="field-content"></open-info>
                  </div>
                  <div v-if="record.newList">
                    <div class="edit-label">编辑后</div>
                    <open-info :data-info="getEditedDatas(record.newList)" class="field-content"></open-info>
                  </div>
                </div>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
    <el-divider direction="vertical"></el-divider>
    <div class="detailright">
      <div class="content">
        <div class="title">历史对话</div>
        <el-row class="info" type="flex" justify="start" align="middle">
          <div>
            <span class="infoTit">接待坐席：</span>
            <span class="infoCon">{{ admitInfo }}</span>
          </div>
          <div class="source">
            <span class="infoTit">来源渠道：</span>
            <span class="infoCon">{{ referer }}</span>
          </div>
        </el-row>
        <div>
          <chatRecordChild :get-data-list="getDataList"></chatRecordChild>
        </div>
      </div>
    </div>
  </el-row>
</template>

<script>
import utils from '@/utils/filter';
import {
  serviceDetail,
  servceSummaryLog
} from '@/api/im_view/serviceConfigForm';
import { getConversation } from '@/api/configuration/RegionalMenu';
import chatRecordChild from '@/components/im/chatRecord';
import openInfo from '@/views/work-sheet/components/openInfo';

export default {
  components: {
    chatRecordChild,
    openInfo
  },
  filters: {
    dateFormat(val) {
      return utils.dataTime(val, 'yy-mm-dd HH:ss:nn');
    }
  },
  data() {
    return {
      isSelf: false,
      dataId: '',
      imFormDataExtend: [],
      imFormData: {},
      getDataList: [],
      referer: '--',
      admitInfo: '--',
      records: [] // 流转记录
    };
  },
  mounted() {
    const { dataId } = this.$route.query;
    this.dataId = dataId;
    this.serviceDetail(dataId);
    this.servceSummaryLog(dataId);
  },
  methods: {
    servceSummaryLog(dataId) {
      servceSummaryLog(dataId).then(res => {
        if (res.code === 1) {
          this.records = res.data;
          // const arr = this.dataResolving(res.data);
          // arr.forEach(item => {
          //   if (item.appendix) {
          //     item.appendix = JSON.parse(item.appendix);
          //   }
          // });
          // this.records = arr;
          this.$nextTick(() => {
            document.querySelector('.el-divider--vertical').style.height =
              'auto';
          });
        }
      });
    },
    /**
     * 获取编辑前数据
     */
    getUneditDatas(datas) {
      // 0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
      return datas.map(el => {
        return {
          fieldKey: el.fieldKey,
          fieldSingleValue: el.value,
          fieldMultipleValue: el.value,
          optionName: el.value,
          fieldType: 0,
          fieldText: el.name || ''
        };
        // if (el.field_type === undefined || el.field_type === null) {
        //   return {
        //     fieldKey: '',
        //     fieldSingleValue: '',
        //     fieldMultipleValue: '',
        //     fieldType: '',
        //     fieldText: ''
        //   };
        // }
      });
    },
    /**
     * 获取编辑后数据
     */
    getEditedDatas(datas) {
      return datas.map(el => {
        return {
          fieldKey: el.fieldKey,
          fieldSingleValue: el.value,
          fieldMultipleValue: el.value,
          optionName: el.value,
          fieldType: 0,
          fieldText: el.name || ''
        };
        // if (el.field_type === undefined || el.field_type === null) {
        //   return {
        //     fieldKey: '',
        //     fieldSingleValue: '',
        //     fieldMultipleValue: '',
        //     fieldType: '',
        //     fieldText: ''
        //   };
        // }
      });
    },
    serviceDetail(dataId) {
      serviceDetail(dataId).then(res => {
        if (res.code === 1) {
          this.imFormDataExtend = res.data.imFormDataExtend;
          this.imFormData = res.data.imFormData;
          this.dialogId = res.data.imFormData.dialogId;
          this.getConversation(this.dialogId);
        }
      });
    },
    getConversation(dialogId) {
      getConversation(dialogId)
        .then(response => {
          if (response.code === 1) {
            if (Object.keys(response.data).length) {
              this.referer = response.data.referer;
              this.admitInfo = `${response.data.nickname || ''}-${response.data
                .kefuCode || '-'}`;
              this.getDataList = response.data.messages;
            }
            setTimeout(() => {
              this.$nextTick(() => {
                document.querySelector('.el-divider--vertical').style.height =
                  '100%';
              });
            }, 1000);
          }
        })
        .catch(function(error) {
          console.log(error);
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.detailright {
  width: 43%;
  padding: 20px 80px 20px 20px;
  .content {
    padding-bottom: 20px;
    border-bottom: 1px dotted #e4e4eb;
    .title {
      font-size: 16px;
      font-weight: 600;
      color: rgba(48, 49, 51, 1);
      padding-bottom: 20px;
    }
  }
}
.info {
  font-size: 14px;
  color: rgba(41, 41, 51, 1);
  padding-bottom: 14px;
  .source {
    padding-left: 50px;
  }
}
.detailLeft {
  padding: 20px;
  width: 55%;
  .content {
    padding-bottom: 20px;
    border-bottom: 1px dotted #e4e4eb;
    .title {
      font-size: 16px;
      font-weight: 600;
      color: rgba(48, 49, 51, 1);
      padding-bottom: 20px;
    }
    .info {
      .infoTit {
        font-size: 14px;
        color: rgba(41, 41, 51, 1);
      }
      .infoCon {
        font-size: 14px;
        color: rgba(87, 87, 102, 1);
      }
    }
  }
  .intro {
    padding-top: 20px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(144, 147, 153, 1);
    .title {
      padding-bottom: 14px;
    }
    .con {
      padding-left: 3px;
    }
  }
}
/deep/.el-divider--vertical {
  height: 100%;
}

.el-timeline {
  padding-left: 5px;
  .el-timeline-item {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    .content-box {
      margin-top: 8px;

      .edit-content {
        > div {
          overflow: hidden;
          width: 100%;
          .edit-label {
            width: 70px;
            float: left;
            height: 20px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(144, 147, 153, 1);
            line-height: 20px;
          }
          .field-content {
            width: calc(100% - 70px);
            float: left;
            /deep/ span {
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: rgba(144, 147, 153, 1);
              line-height: 20px;
              white-space: normal;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }
}
</style>
