<template>
  <div class="process-box">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="工单记录"
                   name="process">
        <el-checkbox-group v-model="filterKey"
                           @change="getRecordDatas">
          <el-checkbox label="circulation">流转记录</el-checkbox>
          <el-checkbox label="append">追加说明</el-checkbox>
          <el-checkbox label="urage">催单记录</el-checkbox>
          <el-checkbox label="edit">工单编辑</el-checkbox>
        </el-checkbox-group>
        <el-timeline>
          <el-timeline-item v-for="(record, index) in records"
                            :key="index"
                            color="#3B95A8">
            <svg-icon v-if="record.recordType!=='circulation' && record.recordType!=='urge' && record.recordType!=='customer_satisfaction'"
                      :icon-class="record.recordType"></svg-icon>
            <svg-icon v-if="record.recordType==='circulation' && record.auditStatus===4"
                      icon-class="restart"></svg-icon>
            <svg-icon v-if="record.recordType==='circulation' && record.transferType ==0 || record.transferType ==1"
                      icon-class="zhuanSheet-icon"></svg-icon>
            <svg-icon v-if="record.recordType==='urge'"
                      icon-class="urgeSheet"></svg-icon>
            <!-- <i
              v-if="record.recordType==='append' && userId===String(record.creatorId)"
              class="el-icon-delete"
              @click="delRecord(record.id)"
            ></i>-->
            <!--标题-->
            <span v-if="record.recordType==='customer_satisfaction'">{{ record.nodeName }}</span>
            <span v-if="record.recordType==='circulation' && record.auditStatus!==4">
              <el-popover placement="bottom-start"
                          popper-class="node-time"
                          trigger="hover"
                          @show="getRecordTime(record.id)">
                <ul>
                  <li>
                    <label>转入时间：</label>
                    <span>{{ recordTime.receiveTime | dateFormat }}</span>
                  </li>
                  <li>
                    <label>领取时间：</label>
                    <span>{{ recordTime.claimTime | dateFormat }}</span>
                  </li>
                  <li>
                    <label>受理时间：</label>
                    <span>{{ recordTime.startTime | dateFormat }}</span>
                  </li>
                  <li>
                    <label>转出时间：</label>
                    <span>{{ recordTime.actualFinishTime | dateFormat }}</span>
                  </li>
                  <li>
                    <label>是否超时：</label>
                    <span>{{ record.timeoutState===1?'是':'否' }}</span>
                  </li>
                </ul>
                <span slot="reference">{{ record.nodeName }}</span>
              </el-popover>
            </span>
            <!-- <span v-if="record.recordType==='urge'">{{ record.nodeName }}</span> -->
            <span v-if="record.recordType==='circulation' && record.transferType ==0 || record.transferType ==1">{{ record.operatorName }}</span>

            <!--分隔符-->
            <span v-if="record.recordType==='circulation' && record.auditStatus!==4">{{ record.nodeName?'-':'' }}</span>
            <!--处理人-->
            <span v-if="record.recordType==='circulation'">{{ record.processorName }}</span>
            <span v-if="record.recordType==='append'">{{ record.creatorName }}</span>
            <span v-if="record.recordType==='edit'">{{ record.editorName }}</span>
            <span v-if="record.recordType==='urge'">{{ record.reminderPersonName }}</span>
            <!-- 流转记录标识 -->
            <span v-if="record.flag"
                  class="no-handler-mark">{{record.text}}</span>
            <svg-icon v-if="record.recordType==='circulation' && record.auditStatus===3"
                      icon-class="back"
                      class-name="suffix"></svg-icon>
            <svg-icon v-if="record.recordType==='circulation' && record.timeoutState===1"
                      icon-class="overtime"
                      class-name="suffix"></svg-icon>
            <span v-if="record.recordType==='circulation' && record.isCurrentNode"
                  class="current-node">当前</span>
            <div>
              <template v-if="Number(record.nodeType)===1">
<!--                处理节点-->
                <div class="node-date">{{ record.gmtModified | dateFormat }}</div>
              </template>
              <template v-else>
                <!--处理时间-->
                <div class="node-date">{{ record.actualFinishTime?record.actualFinishTime:record.gmtCreate | dateFormat }}</div>
              </template>
              <div class="content-box">
                <!--流程节点-->

                <!-- 2020.05.09,rl,1.7.7新增显示满意度评价 -->
                <div v-if="record.recordType === 'customer_satisfaction'"
                     class="edit-content">
                  <div style="margin-bottom:5px;">
                    <div class="edit-label">{{ record.vdoing1 }}</div>
                    <div class="field-content">
                      <p>
                        <el-rate disabled
                                 :value="record.vdoing1Rank | starLevelFormat"></el-rate>
                      </p>
                    </div>
                  </div>
                  <div style="margin-bottom:5px;">
                    <div class="edit-label">{{ record.vdoing2 }}</div>
                    <div class="field-content">
                      <p>
                        <el-rate disabled
                                 :value="record.vdoing2Rank | starLevelFormat"></el-rate>
                      </p>
                    </div>
                  </div>
                  <div style="margin-bottom:5px;">
                    <div class="edit-label">评价描述</div>
                    <div class="field-content">
                      <p v-html="evaluateFormat(record.evaluate)"></p>
                    </div>
                  </div>
                </div>

                <div v-if="record.recordType==='circulation' && [1,2].includes(record.auditStatus)"
                     class="reply-content">
                  <open-info :clear-empty="true"
                             :data-info="adapters(record.replyFieldInfo)"
                             class="field-content"></open-info>
                </div>
                <div v-if="record.recordType==='circulation' && record.auditStatus===3">
                  <p class="content-textarea-formate">
                    退回原因：
                  <pre>{{ record.sendBackReason }}</pre>
                  </p>
                </div>
                <div v-if="record.recordType==='circulation' && record.auditStatus===4">
                  <p class="content-textarea-formate">
                    重启原因：
                  <pre>{{ record.reason }}</pre>
                  </p>
                  <div v-if="record.transferInfo"
                       class="edit-content">
                    <div>
                      <div class="edit-label">转移后</div>
                      <div class="field-content">
                        <p>节点名称：{{ record.transferInfo.afterNodeName }}</p>
                        <p>用户组：{{ record.transferInfo.afterUserGroupName?record.transferInfo.afterUserGroupName:'无' }}</p>
                        <p>人员：{{ record.transferInfo.afterUserName?record.transferInfo.afterUserName:'无' }}</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-if="record.recordType==='circulation' && record.transferType ==0 || record.transferType ==1"
                     class="edit-content">
                  <div>
                    <div class="edit-label">转移前</div>
                    <div class="field-content">
                      <p>节点名称：{{ record.beforeNodeName }}</p>
                      <p>用户组：{{ record.beforeUserGroupName }}</p>
                      <p>人员：{{ record.beforeUserName }}</p>
                    </div>
                  </div>
                  <div>
                    <div class="edit-label">转移后</div>
                    <div class="field-content">
                      <p>节点名称：{{ record.afterNodeName }}</p>
                      <p>用户组：{{ record.afterUserGroupName?record.afterUserGroupName:'无' }}</p>
                      <p>人员：{{ record.afterUserName?record.afterUserName:'无' }}</p>
                    </div>
                  </div>
                </div>
                <div v-if="record.recordType==='append'">
                  <p class="content-textarea-formate">
                    追加说明：
                  <pre>{{ record.appendContent }}</pre>
                  </p>
                  <div v-for="(item,i) in initFileDatas(record.appendix)"
                       :key="i"
                       class="echoImages echoImages_1">
                    <file-preview :file="item"
                                  :percent="100"
                                  :editable="false" />
                  </div>
                </div>
                <div v-if="record.recordType==='edit'"
                     class="edit-content">
                  <div>
                    <div class="edit-label">编辑前</div>
                    <open-info :data-info="getUneditDatas(record.recordFields)"
                               class="field-content"></open-info>
                  </div>
                  <div>
                    <div class="edit-label">编辑后</div>
                    <open-info :data-info="getEditedDatas(record.recordFields)"
                               class="field-content"></open-info>
                  </div>
                </div>
                <div>
                  <p>{{ record.urgeContents }}</p>
                </div>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-tab-pane>
      <el-tab-pane label="工单属性"
                   name="attribute">
        <ul class="form-attr">
          <li>工单编号：{{ attr.workorder_num }}</li>
          <li>工单类型：{{ attr.form_type_name }}</li>
          <li>工单模板：{{ attr.form_name }}</li>
          <li>创建人：{{ attr.creator_name }}</li>
          <li>创建时间：{{ attr.gmt_create }}</li>
          <li>状态：{{ attr.current_state | statusFormat }}</li>
        </ul>
      </el-tab-pane>
      <el-tab-pane label="会话记录"
                   name="chatRecord">
        <chatRecordChild :get-data-list="getDataList"></chatRecordChild>
      </el-tab-pane>
    </el-tabs>

    <div class="process-box__right">
      <el-button icon="el-icon-s-promotion"
                 type="text"
                 @click="$emit('publishCallback')">发布追加说明</el-button>

      <!-- 关注 -->
      <template v-if="isFollowed!==-1">
        <template v-if="isFollowed===0">
          <el-button type="primary"
                     size="mini"
                     icon="el-icon-plus"
                     class="btn-follow"
                     :disabled="btnFollowDisabled"
                     @click="btnFollowHandler">关注</el-button>
        </template>
        <template v-else-if="isFollowed===1">
          <el-popover placement="bottom"
                      width="256"
                      popper-class="popover-custom"
                      trigger="click"
                      v-model="followedPopoverVisible">
            <p>取消关注后将不再收到追加说明通知</p>
            <div style="text-align: right; margin: 0">
              <el-button type="text"
                         size="mini"
                         class="btn-followed-cancel"
                         :disabled="btnFollowCancelDisabled"
                         @click="btnFollowCancelHandler">取消关注</el-button>
            </div>
            <el-button type="info"
                       size="mini"
                       slot="reference"
                       class="btn-followed">已关注</el-button>
          </el-popover>
        </template>
        <!-- 问号 -->
        <el-popover placement="bottom"
                    width="256"
                    popper-class="popover-custom"
                    trigger="hover">
          <p>关注后，如果该工单填写了追加说明则会收到相应通知</p>
          <svg-icon slot="reference"
                    class="icon-info"
                    icon-class="info"
                    style="cursor:pointer;margin-left:5px;"></svg-icon>
        </el-popover>
      </template>
    </div>
  </div>
</template>

<script>
import {
  getWorkorderRecords,
  getRecordTimeInfo,
  delAppendRecord,
  getUserInfo,
  focusOnWorkOrder,
} from '@/api/mySheetManage';
import dateFormatObj from '@/utils/filter.js';
import filePreview from '@/components/Fields/file-preview';
import chatRecordChild from '@/components/im/chatRecord';
import { getConversation } from '@/api/configuration/RegionalMenu';
import openInfo from './openInfo';
import { adapter, initFileDatas } from '@/utils/tools.js';
export default {
  components: {
    openInfo,
    filePreview,
    chatRecordChild,
  },
  filters: {
    starLevelFormat (val) {
      let retStartLv = 0;
      try {
        retStartLv = JSON.parse(val).starLevel;
      } catch (err) { }
      return retStartLv;
    },

    dateFormat (val) {
      return dateFormatObj.dataTime(val, 'yy-mm-dd HH:ss:nn');
    },
    statusFormat (val) {
      let name = '';
      switch (val) {
        case 0:
          name = '待领取';
          break;
        case 1:
          name = '处理中';
          break;
        case 2:
          name = '完结';
          break;
        case 3:
          name = '已退回';
          break;
        case 4:
          name = '异常';
          break;
        case 5:
          name = '作废';
          break;
      }
      return name;
    },
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    attr: {
      type: Object,
      default: null,
    },
  },
  data () {
    return {
      activeTab: 'process',
      filterKey: [
        'circulation',
        'append',
        'edit',
        'urage',
        'customer_satisfaction', //满意度评价
      ],
      records: [], // 流转记录
      userId: '', // 登录人id
      recordTime: {
        receiveTime: '', // 转入
        claimTime: '', // 领取
        startTime: '', // 受理
        actualFinishTime: '', // 转出
      },
      getDataList: [], // 会话记录

      isFollowed: -1,//-1:初始不显示,0:未关注,1:已关注
      btnFollowDisabled: false,//关注按钮禁用
      btnFollowCancelDisabled: false,//取消关注按钮禁用
      followedPopoverVisible: false,//取消关注按钮浮层是否显示
    };
  },
  computed: {
    curNodeId () {
      const arr = this.records.filter((el) => el.recordType === 'circulation');
      return arr.length ? arr[0].nodeId : '';
    },
  },
  watch: {
    attr: {
      handler: function (val) {
        this.isFollowed = val.isFocusOnWorkOrder;
      },
      deep: true,
      immediate: true
    }
  },
  mounted () {
    this.getRecordDatas();
    this.getUserData();
  },
  methods: {
    /**
     * 评价描述，处理换行
     */
    evaluateFormat (val) {
      return val.replace(/\n/g, '<br>');
    },
    adapters (arr) {
      if (arr === undefined) return [];
      const replyExtendFieldInfo = adapter(arr); // 适配器
      return replyExtendFieldInfo;
    },
    initFileDatas (arr) {
      return initFileDatas(arr);
    },
    /**
     * 获取用户信息
     */
    getUserData () {
      getUserInfo()
        .then((res) => {
          if (res.code === 1) {
            this.userId = res.data.attributes.oaId;
          }
        })
        .catch(() => { });
    },
    /**
     * 获取流程记录时间
     */
    getRecordTime (id) {
      getRecordTimeInfo({
        flowerId: id,
      }).then((res) => {
        if (res.code === 1) {
          this.recordTime = {
            receiveTime: res.data.receiveTime, // 转入
            claimTime: res.data.claimTime, // 领取
            startTime: res.data.startTime, // 受理
            actualFinishTime: res.data.actualFinishTime, // 转出
          };
        }
      });
    },
    /**
     * 获取流程记录
     */
    getRecordDatas () {
      if (!this.id && !this.$route.query.id) return // 防止在极端情况下没有id会查询所有流程记录的情况
      getWorkorderRecords({
        workorderId: this.id || this.$route.query.id,
        appendRecord: this.filterKey.includes('append'),
        editRecord: this.filterKey.includes('edit'),
        circulation: this.filterKey.includes('circulation'),
        urage: this.filterKey.includes('urage'),
        userSft: this.filterKey.includes('customer_satisfaction'),
      })
        .then((res) => {
          if (res.code === 1) {
            // this.dataResolving(res.data);
            const arr = this.dataResolving(res.data);
            arr.forEach((item) => {
              if (item.appendix) {
                item.appendix = JSON.parse(item.appendix);
              }
            });
            // this.records = this.dataResolving(res.data);
            this.records = arr;
            // this.records.appendix = JSON.parse(this.records.appendix )

            this.$nextTick(() => {
              document.querySelector('.el-divider--vertical').style.height =
                'auto';
            });
          }
        })
        .catch(() => { });
    },
    /**
     * 获取编辑前数据
     */
    getUneditDatas (datas) {
      // 0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
      return datas.map((el) => {
        if (
          el.field_type === 0 ||
          el.field_type === 1 ||
          el.field_type === 5 ||
          el.field_type === 7 ||
          el.field_type === 8 ||
          el.field_type === 9
        ) {
          return {
            fieldKey: el.edit_field_code,
            fieldSingleValue: el.edit_before_value,
            fieldMultipleValue: el.edit_before_value,
            fieldType: el.field_type,
            optionName: el.edit_before_value,
            fieldText: el.field_text || '',
          };
        }
        if (el.field_type === 2 || el.field_type === 3) {
          return {
            fieldKey: el.edit_field_code,
            fieldSingleValue: el.edit_before_value
              ? el.edit_before_value.option_value
              : '',
            fieldMultipleValue: el.edit_before_value
              ? el.edit_before_value.option_value
              : '',
            fieldType: el.field_type,
            optionName: el.edit_before_value
              ? el.edit_before_value.option_name
              : '',
            fieldText: el.field_text || '',
          };
        }
        if (el.field_type === 4 || el.field_type === 6) {
          const fieldMultipleValue = {};
          let lable;
          let value;
          if (el.edit_before_value) {
            if (el.edit_before_value.option_name.indexOf('/')) {
              lable = el.edit_before_value.option_name.split('/'); // [一级,二级,三级,四级,五级]
            } else {
              lable = [el.edit_before_value.option_name];
            }
            if (el.edit_before_value.option_value.indexOf(',')) {
              value = el.edit_before_value.option_value.split(','); // [一,二,三,四,五]
            } else {
              value = [el.edit_before_value.option_value];
            }
            lable.forEach((item, index) => {
              fieldMultipleValue[value[index]] = item; // {"1003": "物流中心", "1006": "药品发错"},
            });
          }

          return {
            fieldKey: el.edit_field_code,
            fieldSingleValue: fieldMultipleValue,
            fieldMultipleValue: fieldMultipleValue,
            fieldType: el.field_type,
            optionName: el.edit_before_value.option_name,
            fieldText: el.field_text || '',
          };
        }
        if (el.field_type === 10) {
          return {
            fieldKey: el.edit_field_code,
            fieldSingleValue: el.edit_before_value,
            fieldMultipleValue: el.edit_before_value,
            fieldType: el.field_type,
            fieldText: el.field_text || '',
          };
        }

        if (el.field_type === undefined || el.field_type === null) {
          return {
            fieldKey: '',
            fieldSingleValue: '',
            fieldMultipleValue: '',
            fieldType: '',
            fieldText: '',
          };
        }
      });
    },
    /**
     * 获取编辑后数据
     */
    getEditedDatas (datas) {
      return datas.map((el) => {
        if (
          el.field_type === 0 ||
          el.field_type === 1 ||
          el.field_type === 5 ||
          el.field_type === 7 ||
          el.field_type === 8 ||
          el.field_type === 9
        ) {
          return {
            fieldKey: el.edit_field_code,
            fieldSingleValue: el.edit_after_value,
            fieldMultipleValue: el.edit_after_value,
            fieldType: el.field_type,
            optionName: el.edit_after_value,
            fieldText: el.field_text || '',
          };
        }
        if (el.field_type === 2 || el.field_type === 3) {
          return {
            fieldKey: el.edit_field_code,
            fieldSingleValue: el.edit_after_value.option_value,
            fieldMultipleValue: el.edit_after_value.option_value,
            fieldType: el.field_type,
            optionName: el.edit_after_value.option_name,
            fieldText: el.field_text || '',
          };
        }
        if (el.field_type === 4 || el.field_type === 6) {
          const fieldMultipleValue = {};
          let lable;
          let value;
          if (el.edit_after_value.option_name.indexOf('/')) {
            lable = el.edit_after_value.option_name.split('/'); // [一级,二级,三级,四级,五级]
          } else {
            lable = [el.edit_after_value.option_name];
          }
          if (el.edit_after_value.option_value.indexOf(',')) {
            value = el.edit_after_value.option_value.split(','); // [一,二,三,四,五]
          } else {
            value = [el.edit_after_value.option_value];
          }
          lable.forEach((item, index) => {
            fieldMultipleValue[value[index]] = item; // {"1003": "物流中心", "1006": "药品发错"},
          });
          return {
            fieldKey: el.edit_field_code,
            fieldSingleValue: fieldMultipleValue,
            fieldMultipleValue: fieldMultipleValue,
            fieldType: el.field_type,
            optionName: el.edit_after_value.option_name,
            fieldText: el.field_text || '',
          };
        }
        if (el.field_type === 10) {
          return {
            fieldKey: el.edit_field_code,
            fieldSingleValue: el.edit_after_value,
            fieldMultipleValue: el.edit_after_value,
            fieldType: el.field_type,
            fieldText: el.field_text || '',
          };
        }
        if (el.field_type === undefined || el.field_type === null) {
          return {
            fieldKey: '',
            fieldSingleValue: '',
            fieldMultipleValue: '',
            fieldType: '',
            fieldText: '',
          };
        }
      });
    },
    /**
     * 删除追加说明
     */
    delRecord (id) {
      this.$XyyMsg({
        title: '提示',
        content: `确定删除当前追加说明吗?`,
        onSuccess: () => {
          delAppendRecord(id)
            .then((res) => {
              if (res.code === 1) {
                this.getRecordDatas();
              }
            })
            .catch(() => { });
        },
      });
    },

    /** *
     * 数据拆分
     */
    dataResolving (data) {
      var dataArr = [];
      for (let index = 0; index < data.length; index++) {
        const element = this.returnNewKey(data[index]);
        switch (element.recordType) {
          case 'circulation':
            // element['nodeName'] = element.beforeNodeName;
            dataArr.push(element);

            break;
          case 'edit':
            element['id'] = element.editRecordId;
            element['recordFields'] = element.editRecordInfoList;
            dataArr.push(element);

            break;
          case 'append':
            dataArr.push(element);

            break;
          case 'urge':
            dataArr.push(element);

            break;

          case 'customer_satisfaction':
            dataArr.push(element);
            break;

          default:
            break;
        }
      }
      return dataArr;
    },

    // key重新赋值
    returnNewKey (oldData) {
      var newData = {};
      for (const key in oldData) {
        if (oldData.hasOwnProperty(key)) {
          const element = oldData[key];
          const newkeys = key.split('_');
          let newkey = '';
          for (let index = 0; index < newkeys.length; index++) {
            const keyelement = newkeys[index];
            if (index !== 0) {
              const first = keyelement.substring(0, 1);
              newkey += first.toUpperCase();
              const last = keyelement.substring(1, keyelement.length);
              newkey += last;
            } else {
              newkey += keyelement;
            }
          }
          newData[newkey] = element;
        }
      }
      return newData;
    },

    /**
     * 获取聊天记录
     */
    getConversation (dialogId) {
      this.getDataList = [];
      if (dialogId) {
        getConversation(dialogId)
          .then((response) => {
            this.getDataList = [];
            if (response.code === 1) {
              if (Object.keys(response.data).length) {
                this.getDataList = this.getDataList.concat(
                  response.data.messages
                );
              }
            }
          })
          .catch(function (error) {
            console.log(error);
          });
      }
    },

    /**
     * 关注按钮点击
     */
    btnFollowHandler () {
      this.btnFollowDisabled = true;
      focusOnWorkOrder({
        status: '1',
        workorderId: this.id || this.$route.query.id
      }).then(resp => {
        if (resp.code === 1) {
          this.isFollowed = 1;
          this.$XyyMessage.success('关注成功！');
        } else {
          this.$XyyMessage.error(resp.msg);
        }
      }).finally(() => {
        this.btnFollowDisabled = false;
      })
    },

    /**
     * 取消关注按钮点击
     */
    btnFollowCancelHandler () {
      this.btnFollowCancelDisabled = true;
      focusOnWorkOrder({
        status: '0',
        workorderId: this.id || this.$route.query.id
      }).then(resp => {
        if (resp.code === 1) {
          this.isFollowed = 0;
          this.followedPopoverVisible = false;
          this.$XyyMessage.success('取消关注成功！');
        } else {
          this.$XyyMessage.error(resp.msg);
        }
      }).finally(() => {
        this.btnFollowCancelDisabled = false;
      });
    }
  },
};
</script>
<style lang="scss">
.el-popover.node-time {
  width: 260px;
  padding: 12px 16px;
  box-sizing: border-box;
  ul {
    margin: 0;
    padding: 0;
    li {
      margin-bottom: 8px;
      &:last-child {
        margin-bottom: 0;
      }
      label,
      span {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
        height: 20px;
        line-height: 20px;
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.echoImages {
  display: inline-block;
}
.echoImages_1 {
  /deep/.preview-box {
    border: 1px solid #e4e4eb;
    border-radius: 4px;
    overflow: hidden;
  }
}
.process-box {
  /deep/.el-timeline-item__node--normal {
    width: 7px;
    height: 7px;
    left: 0;
    top: 2px;
  }
  /deep/.el-timeline-item__wrapper {
    padding-left: 17px;
  }
  position: relative;
  /deep/.el-tabs__header {
    margin-bottom: 0;
  }
  /deep/ .el-tabs__nav-wrap::after {
    background: none;
  }
  /deep/ .el-tabs__item {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(144, 147, 153, 1);
    &.is-active {
      font-weight: 600;
      color: rgba(48, 49, 51, 1);
    }
  }

  /deep/ .el-tabs__content {
    padding-top: 20px;
    .no-handler-mark {
      width: auto;
      height: 17px;
      padding: 2px 7px 4px 7px;
      line-height: 17px;
      font-size: 12px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #e89a25;
      border-radius: 8px;
      background: #fff1db;
      border-radius: 2px;
      &::before {
        display: inline-block;
        content: '';
        width: 14px;
        height: 14px;
        background: url('../../layout/components/images/xyy-warning-info.png')
          center;
        margin-right: 5px;
        background-size: cover;
        vertical-align: text-bottom;
      }
    }
    .el-checkbox-group {
      line-height: 30px;
      background: rgba(245, 247, 250, 1);
      padding-left: 10px;
      margin-bottom: 20px;
      .el-checkbox {
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
      }
    }
    .el-timeline {
      padding-left: 5px;
      /deep/ .el-timeline-item__tail {
        border-color: #3b95a8;
        left: 3px;
        top: 2px;
        border-width: 1px;
      }
      .el-timeline-item {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        /deep/ .el-timeline-item__content {
          color: rgba(41, 41, 51, 1);
          position: relative;
          .svg-icon {
            margin-right: 2px;
          }
          .svg-icon.suffix {
            margin-right: 0;
          }
          i.el-icon-delete {
            position: absolute;
            top: 3px;
            right: 0;
            cursor: pointer;
          }
        }
        .current-node {
          display: inline-block;
          width: 40px;
          height: 20px;
          line-height: 20px;
          background: rgba(230, 247, 250, 1);
          border-radius: 2px;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(59, 149, 168, 1);
          text-align: center;
          margin-left: 8px;
        }
        .node-date {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(174, 174, 191, 1);
          margin-top: 7px;
        }
        .content-box {
          margin-top: 8px;

          p {
            width: 100%;
            white-space: normal;
            word-break: break-all;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(144, 147, 153, 1);
            line-height: 20px;
            margin: 0;
          }

          .content-textarea-formate {
            display: flex;
            white-space: nowrap !important;

            pre {
              margin-top: 0;
              margin-bottom: 0;
              white-space: pre-wrap !important;
              font-family: inherit !important;
              word-break: break-all;
            }
          }

          .edit-content {
            > div {
              overflow: hidden;
              width: 100%;
              .edit-label {
                width: 70px;
                float: left;
                height: 20px;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: rgba(144, 147, 153, 1);
                line-height: 20px;
              }
              .field-content {
                width: calc(100% - 70px);
                float: left;
                /deep/ span {
                  font-size: 14px;
                  font-family: PingFangSC-Regular, PingFang SC;
                  font-weight: 400;
                  color: rgba(144, 147, 153, 1);
                  line-height: 20px;
                  white-space: normal;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  &.priority0,
                  &.priority1,
                  &.priority2 {
                    padding: 0;
                    background: none;
                  }
                }
                /deep/ div.instrLs {
                  margin-bottom: 8px;
                }
              }
            }
          }
          .reply-content {
            .field-content {
              width: 100%;
              /deep/span {
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: rgba(144, 147, 153, 1);
                line-height: 20px;
              }
              /deep/ div.instrLs {
                margin-bottom: 8px;
                &.empty-row {
                  margin-bottom: 0;
                }
              }
            }
          }
        }
      }
    }
    ul.form-attr {
      margin: 0;
      padding: 0;
      li {
        list-style-type: none;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
        line-height: 20px;
        margin-bottom: 14px;
      }
    }
  }

  & > .process-box__right {
    height: 40px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: absolute;
    right: 0;
    top: 0;

    .el-button {
      padding: 6px 8px;
    }

    .btn-follow,
    .btn-followed {
      border-radius: 1px;
      margin-left: 10px;

      /deep/ .el-icon-plus {
        margin-right: -5px;
      }
    }
  }
}
</style>
<style lang="scss">
.popover-custom {
  p {
    color: #909399;
    margin-top: 0;
    margin-bottom: 0;
  }

  .btn-followed-cancel {
    color: #e86452;
    font-size: 14px;
    margin-top: 5px;

    &:hover {
      color: #ff0000;
    }

    &:focus {
      color: #e86452;
    }
  }
}
</style>
