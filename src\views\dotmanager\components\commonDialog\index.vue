<template>
  <el-dialog :title="title" :visible.sync="dialogVisible" :before-close="handleClose" width="30%">
    <ul class="table">
      <li
        v-for="(item, index) in data"
        :key="index"
        class="row"
        @click="clickRow(item)"
      >{{ item.title }}</li>
    </ul>
  </el-dialog>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '新建发起节点'
    },
    data: {
      type: Array,
      default: () => {
        return [
          { title: '发起节点', type: 0 },
          { title: '处理节点', type: 1 },
          { title: '抄送节点', type: 2 },
          { title: '关闭节点', type: 3 }
        ];
      }
    }
  },
  data() {
    return {
      dialogVisible: false
    };
  },
  methods: {
    showview() {
      this.dialogVisible = true;
    },
    clickRow(val) {
      this.dialogVisible = false;
      this.$emit('commondialog', val);
    },
    handleClose(done) {
      this.dialogVisible = false;
    }
  }
};
</script>

<style scoped>
.table {
  border: 1px solid gray;
  border-bottom: 0px;
  padding-inline-start: 0px;
}

.row {
  height: 40px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid gray;
}
</style>
