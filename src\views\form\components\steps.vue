<template>
  <div class="step-container">
    <el-steps :active="active" finish-status="success" align-center>
      <template v-for="step in steps">
        <el-step :title="step.name" :key="step.name" />
      </template>
    </el-steps>
  </div>
</template>

<script>
export default {
  name: 'Steps',
  props: {
    steps: {
      type: Array,
      default: () => []
    },
    active: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      // active: 0
    };
  },
  methods: {
    next() {
      if (this.active++ > this.steps.length) this.active = 0;
    }
  }
};
</script>
<style lang="scss" scoped>
.step-container {
  padding: 10px 200px;
  /deep/ .el-step__head.is-success {
    color: #3b95a8;
    border-color: #3b95a8;
  }
  /deep/ .el-step__title.is-success {
    color: #3b95a8;
  }
  /deep/ .el-step__line {
    height: 1px;
    background: rgba(59, 149, 168, 1);
    margin: 0 24px !important;
  }
  /deep/ .is-process .is-text {
    color: #ffffff;
    background: #3b95a8;
    border-color: #3b95a8;
  }
}
</style>
