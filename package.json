{"name": "xyy-me-worksheet-vue", "version": "1.4.4", "license": "MIT", "description": "xyy-me-worksheet-vue", "author": "xyy-me-front", "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "build": "node build/build.js", "build-prod": "node build/build.js", "build-dev": "node build/build-dev.js", "build-test": "node build/build-test.js", "build-stage": "node build/build-stage.js", "build:report": "npm_config_report=true npm run build", "lint": "eslint --ext .js,.vue src", "test": "npm run lint", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "fix": "cross-env LIMIT=30720 increase-memory-limit"}, "dependencies": {"@tinymce/tinymce-vue": "^3.0.1", "axios": "^0.19.0", "babel-polyfill": "^6.26.0", "components-mediator": "^0.3.1-alpha", "cross-env": "^6.0.3", "dayjs": "^1.10.4", "echarts": "^4.7.0", "el-cascader-multi": "^1.1.8", "element-ui": "^2.12.0", "increase-memory-limit": "^1.0.7", "js-cookie": "2.2.0", "kindeditor": "^4.1.10", "mockjs": "1.0.1-beta3", "moment": "^2.24.0", "normalize.css": "7.0.0", "nprogress": "0.2.0", "qrcodejs2": "^0.0.2", "quill": "^1.3.7", "v-viewer": "^1.5.1", "vue": "2.5.17", "vue-kindeditor": "0.4.5", "vue-native-websocket": "^2.0.14", "vue-quill-editor": "3.0.6", "vue-router": "3.0.1", "vue-tree-halower": "^1.8.1", "vuedraggable": "^2.24.3", "vuex": "3.0.1", "wangeditor": "^4.6.6"}, "devDependencies": {"autoprefixer": "8.5.0", "babel-core": "6.26.0", "babel-eslint": "8.2.6", "babel-helper-vue-jsx-merge-props": "2.0.3", "babel-loader": "7.1.5", "babel-plugin-syntax-jsx": "6.18.0", "babel-plugin-transform-runtime": "6.23.0", "babel-plugin-transform-vue-jsx": "3.7.0", "babel-preset-env": "1.7.0", "babel-preset-stage-2": "6.24.1", "chalk": "2.4.1", "compression-webpack-plugin": "2.0.0", "copy-webpack-plugin": "4.5.2", "css-loader": "1.0.0", "eslint": "4.19.1", "eslint-friendly-formatter": "4.0.1", "eslint-loader": "2.0.0", "eslint-plugin-vue": "4.7.1", "eventsource-polyfill": "0.9.6", "file-loader": "1.1.11", "friendly-errors-webpack-plugin": "1.7.0", "html-webpack-plugin": "4.0.0-alpha", "mini-css-extract-plugin": "0.4.1", "node-notifier": "5.2.1", "node-sass": "^4.13.1", "optimize-css-assets-webpack-plugin": "5.0.0", "ora": "3.0.0", "path-to-regexp": "2.4.0", "portfinder": "1.0.16", "postcss-import": "12.0.0", "postcss-loader": "2.1.6", "postcss-url": "7.3.2", "rimraf": "2.6.2", "sass-loader": "7.0.3", "sass-resources-loader": "^2.0.1", "script-ext-html-webpack-plugin": "2.0.1", "semver": "5.5.0", "shelljs": "0.8.2", "svg-sprite-loader": "3.8.0", "svgo": "^1.3.0", "uglifyjs-webpack-plugin": "1.2.7", "url-loader": "1.0.1", "vue-loader": "15.3.0", "vue-style-loader": "4.1.2", "vue-template-compiler": "2.5.17", "webpack": "^4.41.5", "webpack-bundle-analyzer": "^3.4.1", "webpack-cli": "3.1.0", "webpack-dev-server": "3.1.14", "webpack-merge": "4.1.4"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}