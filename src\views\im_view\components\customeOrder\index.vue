<template>
  <div class="custome-order-page">
    <!--订单信息-->
    <div id="orderInfo" class="order-container">
      <!-- 订单状态start: 全部, 未完成, 已完成, 已关闭 -->
      <el-radio-group
        v-model="radioValue"
        size="medium"
        class="sss-radio-box"
        @change="changeTabTitle">
        <el-radio-button
          :label="item.label"
          v-for="(item, index) in radioOpts"
          :key="index">
          {{item.value}}
        </el-radio-button>
      </el-radio-group>
      <!-- 订单状态end -->
      <el-menu
        v-if="isOrderList && radioValue === 0"
        :default-active="activeIndex"
        :collapse-transition="false"
        class="el-menu-demo"
        mode="horizontal"
        active-text-color="#3b95a8"
        @select="handleOrderSelect"
      >
        <el-menu-item index="1">最近订单</el-menu-item>
        <el-menu-item index="2">退款单</el-menu-item>
      </el-menu>
      <div v-show="radioValue === 0" class="order-info">
        <header v-if="isOrderList" class="box row-between order-header">
          <el-input v-model.trim="orderNo" :placeholder="placeholder" :maxlength="200"></el-input>
          <button class="search-btn" @click="handleOrderSearch">搜索</button>
        </header>
        <div v-if="orderIndex==1&&orderList.length==0" class="center">
          <img src="../../../../assets/common/no_order.png" alt="暂无订单" />
        </div>
        <div v-if="orderIndex==2&&refundList.length==0" class="center">
          <img src="../../../../assets/common/no_back_order.png" alt="暂无退款单" />
        </div>
        <ul v-if="orderIndex==1&&orderList.length>0&&isOrderList" class="order-list">
          <li v-for="item in orderList" :key="item.orderNo">
            <header class="box row-between column-center">
              <p>
                <em>订单编号:</em>&nbsp;
                <span>{{ item.orderNo }}</span>
              </p>
              <i :class="'status-'+item.status">{{ item.statusName }}</i>
            </header>
            <div class="box row-start order-list-wrapper">
              <a class="box row-center img" href="javascript:void(0);">
                <img :src="item.imageUrl?item.imageUrl:defaultUrl" :alt="item.merchantName" />
              </a>
              <div class="detail">
                <header class="box row-between column-start">
                  <h2 class="ellipsis">
                    <el-tooltip
                      :content="item.merchantName"
                      class="item"
                      effect="dark"
                      placement="top-start"
                    >
                      <span>{{ item.merchantName }}</span>
                    </el-tooltip>
                  </h2>
                  <el-button
                    type="text"
                    style="width: 42px; font-size: 12px;"
                    @click="checkProductDetail(item)"
                  >
                    查看
                    <i class="el-icon-arrow-right" style="padding: 2px 2px;"></i>
                  </el-button>
                </header>
                <time>{{ item.createTime|formatTime }}</time>
                <p class="box row-start">
                  <span>共{{ item.varietyNum }}种商品</span>
                  <span>
                    实付总额：¥
                    <i>{{ item.money }}</i>
                  </span>
                </p>
              </div>
            </div>
          </li>
        </ul>
        <ul v-if="orderIndex==2&&refundList.length&&isOrderList" class="order-list">
          <li v-for="item in refundList" :key="item.refundOrderNo">
            <header class="box row-between column-center">
              <p>
                <em>退款编号:</em>&nbsp;
                <span>{{ item.refundOrderNo }}</span>
              </p>
              <i :class="'status-4'">{{ item.auditStateStr }}</i>
            </header>
            <div class="box row-start order-list-wrapper">
              <a class="box row-center img" href="javascript:void(0);">
                <img :src="item.imageUrl?item.imageUrl:defaultUrl" :alt="item.merchantName" />
              </a>
              <div class="detail">
                <header class="box row-between column-start">
                  <h2 class="ellipsis">
                    <el-tooltip
                      :content="item.merchantName"
                      class="item"
                      effect="dark"
                      placement="top-start"
                    >
                      <span>{{ item.merchantName }}</span>
                    </el-tooltip>
                  </h2>
                  <el-button
                    type="text"
                    style="width: 42px; font-size: 12px;"
                    @click="checkRefundDetail(item)"
                  >
                    查看
                    <i class="el-icon-arrow-right" style="padding: 2px 2px;"></i>
                  </el-button>
                </header>
                <time>{{ item.createTime }}</time>
                <p class="box row-start">
                  <span>共{{ item.refundVarietyNum }}种商品</span>
                  <span>
                    退款总额：¥
                    <i>{{ item.refundFee }}</i>
                  </span>
                </p>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <!--订单详情-->
      <!-- <div v-if="orderDetail.orderInfo&&!isOrderList" class="order-detail">
        <dl>
          <dt class="order-detail-header">
            <button class="back-btn" @click="reBack">返回</button>
            <h1>{{ orderIndex==1?"订单详情":"退款订单信息" }}</h1>
          </dt>
          <dd v-if="orderIndex==1" class="order-detail-user">
            <p class="box row-start">
              <em>收货人:</em>
              <span>{{ orderDetail.orderInfo.contactor }}</span>
            </p>
            <p class="box row-start">
              <em>手机号:</em>
              <span>{{ orderDetail.orderInfo.mobile }}</span>
            </p>
            <p class="box row-start">
              <em>收货地址:</em>
              <el-tooltip
                :content="orderDetail.orderInfo.address"
                class="item"
                effect="dark"
                placement="top-start"
              >
                <span>{{ orderDetail.orderInfo.address }}</span>
              </el-tooltip>
            </p>
          </dd>
          <dd v-if="orderIndex==1" class="box row-start order-detail-type">
            <em class="left">订单信息</em>
            <div class="right">
              <p>
                订单编号：
                <el-tooltip
                  :content="orderDetail.orderInfo.orderNo"
                  class="item"
                  effect="dark"
                  placement="top-start"
                >
                  <span>{{ orderDetail.orderInfo.orderNo }}</span>
                </el-tooltip>
              </p>
              <p>下单日期：{{ orderDetail.orderInfo.createTime|formatTime }}</p>
              <p>
                订单状态：
                <span
                  :class="'status-'+orderDetail.orderInfo.status"
                  class="bg-none"
                >{{ orderDetail.orderInfo.statusName }}</span>
              </p>
              <p>发票类型：{{ orderDetail.orderInfo.billTypeStr }}</p>
              <p>支付方式：{{ changeStatus(orderDetail.orderInfo.payType,["在线支付","货到付款","线下转账"]) }}</p>
            </div>
          </dd>
          <dd v-if="orderIndex==2" class="order-detail-user">
            <p class="box row-start">
              <em>申请日期:</em>
              <span>{{ orderDetail.orderInfo.refundCreateTime|formatTime }}</span>
            </p>
            <p class="box row-start">
              <em>审核日期:</em>
              <span>{{ orderDetail.orderInfo.refundAuditTime|formatTime }}</span>
            </p>
            <p class="box row-start">
              <em>退款日期:</em>
              <span>{{ orderDetail.orderInfo.refundAuditTime|formatTime }}</span>
            </p>
            <p class="box row-start">
              <em>退款状态:</em>
              <span class="status-90 bg-none">{{ orderDetail.orderInfo.auditStateStr }}</span>
            </p>
          </dd>
          <dd v-if="orderIndex==2" class="box row-start order-detail-type">
            <em class="left">退款信息</em>
            <div class="right">
              <p>
                退款订单编号：
                <el-tooltip
                  :content="orderDetail.orderInfo.refundOrderNo"
                  class="item"
                  effect="dark"
                  placement="top-start"
                >
                  <span>{{ orderDetail.orderInfo.refundOrderNo }}</span>
                </el-tooltip>
              </p>
              <p>申请退款金额：{{ orderDetail.orderInfo.refundFee }}</p>
              <p>退回余额：{{ orderDetail.orderInfo.refundBalance }}</p>
              <p>实际优惠金额：{{ orderDetail.orderInfo.refundDiscount }}</p>
              <p>退货商品种数：{{ orderDetail.orderInfo.goodsCount }}</p>
              <p>退货商品件数：{{ orderDetail.orderInfo.productAmount }}</p>
              <p>退款说明：{{ orderDetail.orderInfo.refundExplain }}</p>
              <div class="refundPic">
                退款凭证:
                <span>
                  <img
                    v-for="(item,index) in orderDetail.evidence"
                    :src="item"
                    :key="index"
                    alt
                    style="width:100px;height:75px;margin:3px"
                    @click="showDialog"
                  />
                </span>
                <el-dialog :visible.sync="centerDialogVisible" width="30%" center>
                  <el-carousel :interval="5000" arrow="always">
                    <el-carousel-item v-for="item in orderDetail.orderInfo.evidence" :key="item">
                      <img :src="item" alt style="width:100%; height:100%" />
                    </el-carousel-item>
                  </el-carousel>
                  <span slot="footer" class="dialog-footer">
                    <el-button @click="centerDialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="centerDialogVisible = false">确 定</el-button>
                  </span>
                </el-dialog>
              </div>
              <p>退款金额：{{ orderDetail.orderInfo.refundActualFee }}</p>
              <p>
                退款计算：订单编号:{{ orderDetail.orderInfo.orderNo }}:
                于{{ orderDetail.orderInfo.refundCreateTime|formatTime }}申请了{{ orderDetail.orderInfo.refundTypeStr }}
                <span
                  v-if="orderDetail.orderInfo.cancelChannel>0"
                >
                  ----------------------------------------
                  当前订单于{{ orderDetail.orderInfo.refundAuditTime|formatTime }}由{{ orderDetail.orderInfo.refundChannelStr }}进行了'取消退款单'操作!
                </span>
              </p>
            </div>
          </dd>
          <dd class="box row-between order-detail-info">
            <el-collapse v-model="productDetail" accordion>
              <el-collapse-item title="商品明细" name="1">
                <ol class="product-list-wrapper">
                  <li v-for="item in orderDetail.listGoods" :key="item.productCode">
                    <div class="box row-start product-list-wrapper-header">
                      <a class="box row-center img" href="#">
                        <img :src="item.imageUrl?item.imageUrl:defaultUrl" :alt="item.productName" />
                      </a>
                      <div class="detail">
                        <header class="box row-between column-start">
                          <h2 class="ellipsis">
                            <el-tooltip
                              :content="item.productName"
                              class="item"
                              effect="dark"
                              placement="top-start"
                            >
                              <span>{{ item.productName }}</span>
                            </el-tooltip>
                          </h2>
                          <a href="#">¥ {{ item.productPrice }}</a>
                        </header>
                        <p class="box row-between column-center">
                          <el-tooltip
                            :content="item.productCode"
                            class="item"
                            effect="dark"
                            placement="top-start"
                          >
                            <span v-if="item.productCode">编码：{{ item.productCode }}</span>
                          </el-tooltip>
                          <span>X {{ item.productAmount }}</span>
                        </p>
                        <p class="box row-start column-center">
                          <span>
                            <i>厂</i>
                          </span>
                          <el-tooltip
                            :content="item.manufacturer"
                            class="item"
                            effect="dark"
                            placement="top-start"
                          >
                            <span>{{ item.manufacturer }}</span>
                          </el-tooltip>
                        </p>
                        <p class="box row-start column-center">
                          <span>
                            <i>规</i>
                          </span>
                          <span>{{ item.spec }}</span>
                        </p>
                      </div>
                    </div>
                    <div
                      v-if="orderIndex==1"
                      class="box row-wrap row-start column-center product-list-wrapper-bottom"
                    >
                      <span>实付金额：¥{{ item.realPayAmount }}</span>
                      <span>满减金额：¥{{ item.promoAmount }}</span>
                      <span>优惠券金额：¥{{ item.voucherAmount }}</span>
                      <span>余额抵扣：¥{{ item.useBalanceAmount }}</span>
                      <span>返点金额：¥{{ item.balanceAmount }}</span>
                      <span>
                        小计：¥
                        <i>{{ item.subTotal }}</i>
                      </span>
                    </div>
                  </li>
                </ol>
              </el-collapse-item>
            </el-collapse>
          </dd>
          <dd v-if="orderIndex==1" class="box row-between order-detail-count">
            <em class="left">数量</em>
            <div class="right">
              <p>商品种数：{{ orderDetail.orderInfo.varietyNum }}种</p>
              <p>总件数：{{ orderDetail.orderInfo.productNum }}件</p>
            </div>
          </dd>
          <dd v-if="orderIndex==1" class="box row-between order-detail-price">
            <em class="left">金额</em>
            <div class="right">
              <p>订单金额：¥{{ orderDetail.orderInfo.totalAmount }}</p>
              <p>满减金额：¥{{ orderDetail.orderInfo.wholeDiscountAmount }}</p>
              <p>优惠券金额：¥{{ orderDetail.orderInfo.wholeVoucherAmount }}</p>
              <p>余额抵扣：¥{{ orderDetail.orderInfo.wholeVoucherAmount }}</p>
              <p>返点金额：¥{{ orderDetail.orderInfo.useBalanceAmount }}</p>
              <p>
                实付金额：
                <span>
                  ¥
                  <i>{{ orderDetail.orderInfo.money }}</i>
                </span>
              </p>
            </div>
          </dd>
          <dd v-if="orderIndex==2 && orderinfo.mobile" class="box row-start order-detail-type">
            <em class="left">订单信息</em>
            <div class="right">
              <p class="box row-start">
                <em>订单编号：</em>
                <el-tooltip
                  :content="orderDetail.orderInfo.orderNo"
                  class="item"
                  effect="dark"
                  placement="top-start"
                >
                  <span>{{ orderDetail.orderInfo.orderNo }}</span>
                </el-tooltip>
              </p>
              <p class="box row-start">
                <em>客户名称：</em>
                <el-tooltip
                  :content="orderDetail.orderInfo.realName"
                  class="item"
                  effect="dark"
                  placement="top-start"
                >
                  <span>{{ orderDetail.orderInfo.realName }}</span>
                </el-tooltip>
              </p>
              <p class="box row-start">
                <em>手机号码：</em>
                <span>{{ orderinfo.mobile }}</span>
              </p>
              <p class="box row-start">
                <em>收货地址：</em>
                <el-tooltip
                  :content="orderinfo.address"
                  class="item"
                  effect="dark"
                  placement="top-start"
                >
                  <span>{{ orderinfo.address }}</span>
                </el-tooltip>
              </p>
              <p class="box row-start">
                <em>支付方式：</em>
                <span>{{ orderinfo.payTypeStr }}</span>
              </p>
              <p class="box row-start">
                <em>订单状态：</em>
                <span>{{ orderinfo.statusName }}</span>
              </p>
              <p class="box row-start">
                <em>商品总金额：</em>
                <span>{{ orderinfo.totalAmount }}</span>
              </p>
              <p class="box row-start">
                <em>优惠总金额：</em>
                <span>{{ orderinfo.discount }}</span>
              </p>
              <p class="box row-start">
                <em>实付总金额：</em>
                <span>{{ orderinfo.money }}</span>
              </p>
              <p class="box row-start">
                <em>订单商品总数：</em>
                <span>{{ orderinfo.productNum }}</span>
              </p>
              <p class="box row-start">
                <em>订单商品件数：</em>
                <span>{{ orderinfo.varietyNum }}</span>
              </p>
              <p class="box row-start">
                <em>订单可返余额：</em>
                <span>{{ orderinfo.balanceAmount }}</span>
              </p>
              <p class="box row-start">
                <em>订单抵扣余额：</em>
                <span>{{ orderinfo.useBalanceAmount }}</span>
              </p>
            </div>
          </dd>
        </dl>
      </div> -->
      <component
        v-if="radioValue !== 0"
        :is="tabView"
        :radio-status="radioValue"
        :order-data="orderData">
      </component>
      <p v-if="isHasMore && (orderList.length || refundList.length)" class="no-more-tip">没有更多了</p>
    </div>
    <div
      v-if="loading"
      v-loading="loading"
      class="box-loading"
      element-loading-text="拼命加载中">
    </div>
  </div>
</template>

<script>
import {
  getOrderInfoByNo,
  getRefundOrderInfoByNo,
  getOrderBaseByNo,
  queryOrderInfoPage,
  queryRefundOrderInfoPage
} from '@/api/im_view/customeOrder';
import orderInfo from './order-info';
export default {
  name: 'CustomeOrder',
  components: { orderInfo },
  data: function() {
    return {
      loading: false,
      placeholder: '请输入订单编号',
      radioValue: 0,
      activeIndex: '1', // 当前订单列表状态1为最近订单，2为退款订单
      orderIndex: '1',
      productDetail: '',
      orderList: [],
      refundList: [],
      merchantId: '',
      orderNo: '', // 搜索框的订单编号
      isOrderList: true,
      orderDetail: {}, // 订单详情
      activeOrderNo: '', // 当前查看的订单编号
      defaultUrl: '/css/images/default.png',
      centerDialogVisible: false,
      orderinfo: {},
      pageOpt: {
        pageNum: 1, // 当前页数
        pageSize: 20, // 每页显示条目个数
        total: 0 // 总条目数
      },
      orderData: [],
      dataList: [], // 订单信息已完成
      recentOrderList: [], // 最近的订单
      refundOrderList: [], // 退款的订单
      flag: false, // 节流
      changeKey: Math.random(),
      allPage: {
        page: 1, // 当前页数
        pageSize: 20, // 每页显示条目个数
        total: 0 // 总条目数
      },
      isHasMore: false,
      tabView: 'orderInfo',
      radioOpts: [
        {label: 0, value: '全部'},
        {label: 1, value: '未完成'},
        {label: 2, value: '已完成'},
        {label: 3, value: '已关闭'},
      ]
    };
  },
  computed: {
    customId: function() {
      return this.$store.getters.khid;
      // return 142550;
    },
    dialogId: function() {
      return this.$store.getters.containerid;
    }
  },
  watch: {
    customId: function() {
      this.getInfo();
    }
  },
  destroyed() {
    window.removeEventListener('scroll', this.listenScroll, true);
  },
  mounted() {
    this.getInfo();
    const warpper = document.querySelector('.el-tabs__content');
    warpper.addEventListener('scroll', this.listenScroll);
  },
  methods: {
    listenScroll() {
      // 监听滚动
      this.flag = false; // 节流
      const wrap = document.querySelector('.el-tabs__content');
      // const sclDistance = scrollHeight - scrollTop - clientHeight
      if (
        wrap.scrollTop + wrap.offsetHeight > wrap.scrollHeight - 0 &&
        this.flag === false
      ) {
        this.flag = true;
        console.log('滚动到底部了......');
        // 监听是否滚动到底部
        this.addMoreData();
      }
    },
    // 加载更多
    addMoreData() {
      if (this.radioValue === 0) {
        if (
          +this.allPage.total ===
          (this.recentOrderList.length || this.refundOrderList.length)
        ) {
          this.isHasMore = true;
          return false;
        } else {
          this.isHasMore = false;
        }
        this.allPage.page++;
        this.orderIndex == '1'
          ? this.getOrderList('scoll')
          : this.backOrderList('scoll');
      } else {
        if (+this.pageOpt.total === this.dataList.length) {
          this.isHasMore = true;
          return false;
        } else {
          this.isHasMore = false;
        }
        this.pageOpt.pageNum++;
        this.changeTabTitle(this.radioValue, 'scoll');
      }
    },
    showDialog() {
      if (this.centerDialogVisible) return;
      this.centerDialogVisible = true;
    },
    reBack() {
      this.isOrderList = true;
    },
    checkProductDetail(item) {
      // item && window.open(item.orderDetailUrl, '_search');
      item && window.open(item.orderDetailUrl, '_blank');
      // this.activeOrderNo = orderNo;
      // // 查看订单详情
      // this.orderIndex == 1 ? this.getOrderDetail() : this.getBackOrderDetail();
    },
    checkRefundDetail(item) {
      item && window.open(item.refundOrderDetailUrl, '_blank');
    },
    async changeTabTitle(val, type = '') {
      if (val === 0) {
        this.orderIndex == 1;
        this.orderList = [];
        this.refundList = [];
        return false;
      };
      if (!type) {
        this.pageOpt.pageNum = 1;
        this.dataList = [];
      }
      this.isHasMore = false;
      try {
        this.radioValue = val;
        this.loading = true;
        const param = {
          merchantId: this.merchantId,
          status: val,
          pageNum: this.pageOpt.pageNum,
          pageSize: this.pageOpt.pageSize
        };
        const res = await queryOrderInfoPage(param);
        if (res && res.code === 1) {
          this.loading = false;
          this.flag = false;
          const list = (res.data && res.data.list) || [];
          this.pageOpt.total = Number(res.data.total);
          if (list.length) {
            // 追加在已有数据后
            list.forEach(item => this.dataList.push(item));
            this.orderData = this.filterRepeat(this.dataList);
          } else {
            this.orderData = [];
          }
        } else {
          return this.$XyyMessage.error(res.msg || '接口错误');
        }
      } catch (error) {
        this.loading = false;
        console.log(error);
      }
    },
    getOrderDetail() {
      getOrderInfoByNo({ orderNo: this.activeOrderNo }).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.error(res.msg || '');
          return;
        }
        this.orderDetail = res.data;
        this.isOrderList = false;
      });
    },
    getBackOrderDetail() {
      getRefundOrderInfoByNo({ orderNo: this.activeOrderNo }).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.waning(res.msg);
          return;
        }
        this.orderDetail = res.data;
        this.isOrderList = false;
        // 获取订单信息
        this.getOrderInfo(
          this.orderDetail.orderInfo.merchantId,
          this.orderDetail.orderInfo.orderNo
        );
      });
    },
    getOrderInfo(memberId, orderNo) {
      getOrderBaseByNo({ memberId, orderNo }).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.error(res.msg || '');
          return;
        }
        this.orderinfo = res.data;
        this.isOrderList = false;
      });
    },
    getInfo() {
      if (!this.customId && !this.orderNo) return;
      this.isOrderList = true;
      // 如果是已登录用户查询订单
      this.merchantId = this.customId ? this.customId : null;
      parseInt(this.orderIndex) == 1
        ? this.getOrderList()
        : this.backOrderList();
    },
    async getOrderList(type = null) {
      // 最近订单
      if (!type) {
        this.recentOrderList = [];
      }
      const params = {
        merchantId: this.merchantId || '',
        orderNo: this.orderNo || '',
        status: this.radioValue,
        pageNum: this.allPage.page,
        pageSize: this.allPage.pageSize
      };
      try {
        const res = await queryOrderInfoPage(params);
        if (res.code !== 1) {
          return this.$XyyMessage.error(res.msg || '');
        }
        this.loading = false;
        this.flag = false;
        const list = (res.data && res.data.list) || [];
        this.allPage.total = Number(res.data.total);
        if (list.length) {
          list.forEach(element => this.recentOrderList.push(element));
          this.orderList = this.filterRepeat(this.recentOrderList);
        } else {
          this.orderList = list;
        }
        // console.log(this.orderList, 'this.orderList')
      } catch (error) {
        this.loading = false;
      }
    },
    // 退款订单
    async backOrderList(type = null) {
      if (!type) {
        this.refundOrderList = [];
      }
      const params = {
        memberId: this.merchantId || '',
        refundOrderNo: this.orderNo,
        status: this.radioValue,
        pageNum: this.allPage.page,
        pageSize: this.allPage.pageSize
      };
      try {
        const res = await queryRefundOrderInfoPage(params);
        if (res.code !== 1) {
          return this.$XyyMessage.error(res.msg || '');
        }
        this.loading = false;
        this.flag = false;
        const list = (res.data && res.data.list) || [];
        this.allPage.total = Number(res.data.total);
        if (list.length) {
          list.forEach(element => this.refundOrderList.push(element));
          this.refundList = this.filterRepeat(this.refundOrderList);
        } else {
          this.refundList = list;
        }
        console.log(this.refundList, 'this.refundList')
      } catch (error) {
        this.loading = false;
      }
    },
    handleOrderSelect(key, keyPath) {
      console.log(key, keyPath);
      this.orderNo = '';
      this.allPage.page = 1;
      this.orderIndex = key;
      this.placeholder = (key == 2 ? '请输入退单编号' : '请输入订单编号');
      this.orderList.splice(0, this.orderList.length);
      this.orderDetail = {};
      this.getInfo();
    },
    handleOrderSearch() {
      /* 查询信息*/
      this.getInfo();
    },
    // 去掉相同id的项目
    filterRepeat(arr) {
      return arr.filter(function(element, index, self) {
        return self.findIndex(el => el.id === element.id) === index;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../../../styles/custome-order.css';
.custome-order-page {
  position: relative;
}
.center {
  text-align: center;
}
.center img {
  width: 220px;
}
.order-container {
  height: 100%;
}
.order-container .el-menu li:first-child {
  margin-right: 10px;
}
.sss-radio-box {
  display: flex;
  flex-direction: row;
  /deep/ .el-radio-button {
    flex: 1;
    .el-radio-button__inner {
      width: 100%;
    }
  }
}
.no-more-tip {
  height: 50px;
  line-height: 50px;
  margin-top: -16px;
  text-align: center;
  font-size: 14px;
  color: #5e6d82;
}
.box-loading {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
</style>
