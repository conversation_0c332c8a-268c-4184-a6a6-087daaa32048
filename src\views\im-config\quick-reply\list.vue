<template>
  <div class="quick-reply-box">
    <div class="left-container">
      <h1 class="title-box">问题分类</h1>
      <div class="btn-box">
        <xyy-button icon-class="btn-add" @click="toAddClassify">新建分类</xyy-button>
        <span class="right-box">
          <xyy-button icon-class="im-import" type="normal" @click="importOpen=true">导入</xyy-button>
          <xyy-button icon-class="im-export" type="normal" @click="exportData">导出</xyy-button>
        </span>
      </div>
      <drag-tree
        ref="tree"
        :props="{label:(data)=>data.name||data.question,children:'questions'}"
        :list="datas"
        :expandedKeys="expandedKeysData"
        @delChildCallback="toDelQuestion"
        @clickCallback="getData"
        @addChildCallback="toAddQuestion"
        @delCallback="toDelClassify"
        @dropCallback="sortData"
      ></drag-tree>
      <import-box :status.sync="importOpen" @importCallback="initTree"></import-box>
    </div>
    <div class="right-container">
      <div v-show="!activeForm" class="null-data">
        <img :src="nullPath" />
      </div>
      <div v-show="activeForm==='classify'" class="classify-box">
        <h1 class="title-box">分类信息</h1>
        <el-form ref="cForm" :model="classify" :rules="cRules" label-width="80px">
          <el-form-item label="分类名称" prop="name">
            <el-input v-model="classify.name" :disabled="!classifyEditable" maxlength="30"></el-input>
          </el-form-item>
          <el-form-item label=" ">
            <xyy-button
              v-show="!classifyEditable"
              icon-class="im-edit"
              @click="classifyEditable=true"
            >编辑</xyy-button>
            <span v-show="classifyEditable">
              <el-button type="primary" class="form-btn" @click="editClassifyData">保存</el-button>
              <!-- <el-button class="form-btn" @click="reset('classify')">取消</el-button> -->
            </span>
          </el-form-item>
        </el-form>
      </div>
      <div v-show="activeForm==='question'" class="question-box">
        <h1 class="title-box">问题信息</h1>
        <el-form ref="qForm" :model="question" :rules="qRules" label-width="80px">
          <el-form-item label="所属分类">
            <el-input v-model="question.type" :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="问题描述" prop="question">
            <el-input v-model="question.question" :disabled="!questionEditable" maxlength="100"></el-input>
          </el-form-item>
          <el-form-item label="问题答案" prop="answer">
            <el-input
              v-model="question.answer"
              :disabled="!questionEditable"
              show-word-limit
              type="textarea"
              resize="none"
              maxlength="300"
            ></el-input>
          </el-form-item>
          <el-form-item label=" ">
            <xyy-button
              v-show="!questionEditable"
              icon-class="btn-add"
              @click="questionEditable=true"
            >编辑</xyy-button>
            <span v-show="questionEditable">
              <el-button type="primary" class="form-btn" @click="editQuestionData">保存</el-button>
              <!-- <el-button class="form-btn" @click="reset('question')">取消</el-button> -->
            </span>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import dragTree from '@/components/im/drag-tree';
import importBox from './components/import';
import nullData from '@/assets/common/null.png';
import {
  saveClassifyData,
  saveQuestionData,
  getQRDatas,
  delClassifyData,
  delQuestionData,
  updateSort
} from '@/api/im-config/quick-reply';
export default {
  name: 'compQuickReply',
  components: {
    dragTree,
    importBox
  },
  data() {
    return {
      classify: {
        name: '',
        id: ''
      },
      cRules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
          {
            max: 30,
            message: '不能超过30个字',
            trigger: 'blur'
          }
        ]
      },
      question: {
        type: '',
        typeid: '',
        question: '',
        answer: '',
        id: ''
      },
      qRules: {
        question: [
          { required: true, message: '请输入问题描述', trigger: 'blur' },
          {
            max: 100,
            message: '不能超过100个字',
            trigger: 'blur'
          }
        ],
        answer: [
          { required: true, message: '请输入问题答案', trigger: 'blur' },
          {
            max: 300,
            message: '不能超过300个字',
            trigger: 'blur'
          }
        ]
      },
      activeForm: '',
      nullPath: nullData,
      classifyEditable: false,
      questionEditable: false,
      importOpen: false,
      datas: [], // 树数据
      expandedKeysData: [] //默认展开节点
    };
  },
  mounted() {
    this.initTree();
  },
  methods: {
    /**
     * 获取数据
     */
    getData({ data, node }) {
      this.expandedKeysData = [];
      this.expandedKeysData.push(data.id);
      if (data.question) {
        this.activeForm = 'question';
        this.question.type = node.parent.data.name;
        this.question.question = data.question;
        this.question.answer = data.answer;
        this.question.typeid = data.typeid;
        this.question.id = data.id;
        this.questionEditable = false;
      } else {
        this.activeForm = 'classify';
        this.classify.name = data.name;
        this.classify.id = data.id;
        this.classifyEditable = false;
      }
    },
    /**
     * 初始化左侧树
     */
    initTree() {
      getQRDatas().then(res => {
        if (res.code === 1) {
          this.datas = res.data;
          this.activeForm = '';
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    /**
     * 重置类型数据
     */
    // reset(type) {
    //   this.$nextTick(() => {
    //     const data = this.$refs['tree'].$refs['drag-tree'].getCurrentNode();
    //     if (type === 'question') {
    //       this.question.question = data.question;
    //       this.question.answer = data.answer;
    //       this.questionEditable = false;
    //     } else {
    //       this.classify.name = data.name;
    //       this.classifyEditable = false;
    //       this.question.question = '';
    //       this.question.answer = '';
    //       this.questionEditable = false;
    //     }
    //   });
    // },
    /**
     * 添加问题
     */
    toAddQuestion(data) {
      this.activeForm = 'question';
      this.question.type = data.name;
      this.question.question = '';
      this.question.answer = '';
      this.question.typeid = data.id;
      this.question.id = '';
      this.questionEditable = true;
    },
    /**
     * 添加分类
     */
    toAddClassify() {
      this.activeForm = 'classify';
      this.classify.name = '';
      this.classify.id = '';
      this.classifyEditable = true;
    },
    toDelClassify(data) {
      this.$XyyMsg({
        title: '提示',
        content:
          data.questions && data.questions.length
            ? '删除该分类将会删除下面的子类,确定删除吗？'
            : '确定删除该分类？',
        onSuccess: () => {
          delClassifyData({ id: data.id }).then(res => {
            if (res.code === 1) {
              this.initTree();
              this.$XyyMessage.success('删除成功');
            } else {
              this.$XyyMessage.error(res.msg);
            }
          });
        }
      });
    },
    toDelQuestion(data) {
      this.$XyyMsg({
        title: '提示',
        content: '确定删除该问题？',
        onSuccess: () => {
          delQuestionData({ id: data.id }).then(res => {
            if (res.code === 1) {
              this.initTree();
              this.$XyyMessage.success('删除成功');
            } else {
              this.$XyyMessage.error(res.msg);
            }
          });
        }
      });
    },
    sortData({ dragNode, dropNode, list }) {
      if (dragNode.question) {
        // 二级节点
        const ids = [dragNode.typeid, dropNode.typeid];
        const datas = list
          .filter(el => ids.includes(el.id))
          .map(el => {
            el.questions = el.questions.map((q, i) => {
              q.orderkey = i;
              q.typeid = el.id;
              return q;
            });
            return el;
          });
        updateSort({
          modifyType: 2,
          typeList: datas
        }).then(res => {
          if (res.code === 1) {
            this.initTree();
          } else {
            this.$XyyMessage.error(res.msg);
          }
        });
      } else {
        // 一级节点
        const datas = list.map((el, index) => {
          el.orderKey = index;
          return el;
        });
        updateSort({
          modifyType: 1,
          typeList: datas
        }).then(res => {
          if (res.code === 1) {
            this.initTree();
          } else {
            this.$XyyMessage.error(res.msg);
          }
        });
      }
    },
    /**
     * 保存分类数据
     */
    editClassifyData() {
      this.$refs['cForm'].validate(valid => {
        if (valid) {
          saveClassifyData(this.classify)
            .then(res => {
              if (res.code === 1) {
                this.$XyyMessage.success(
                  this.classify.id ? '修改成功' : '保存成功'
                );
                this.initTree();
              } else {
                this.$XyyMessage.error(res.msg);
              }
            })
            .catch(() => {});
        }
      });
    },
    /**
     * 保存问题数据
     */
    editQuestionData() {
      this.$refs['qForm'].validate(valid => {
        if (valid) {
          saveQuestionData(this.question)
            .then(res => {
              if (res.code === 1) {
                this.$XyyMessage.success(
                  this.question.id ? '修改成功' : '保存成功'
                );
                this.initTree();
              } else {
                this.$XyyMessage.error(res.msg);
              }
            })
            .catch(() => {});
        }
      });
    },
    /**
     * 导出数据
     */
    exportData() {
      // const a = document.createElement('a');
      // a.href = `${process.env.BASE_API_IM}/quickreply/faqinfo/download`;
      // a.click();

      let url = `${process.env.BASE_API_IM}/quickreply/faqinfo/download`;
      if (
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
      ) {
        url =
          url +
          `?businessPartCode=${this.$store.getters.channel.businessPartCode}`;
      }
      const a = document.createElement('a');
      a.href = url;
      a.click();
    }
  }
};
</script>

<style lang="scss" scoped>
.quick-reply-box {
  padding: 20px;
  overflow: hidden;
  > div {
    float: left;
  }
  h1.title-box {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: rgba(51, 51, 51, 1);
    height: 22px;
    line-height: 22px;
  }
  .left-container {
    width: 350px;
    height: 100%;
    border-radius: 2px;
    border: 1px solid rgba(220, 222, 227, 1);
    padding: 15px 20px;
    box-sizing: border-box;
    overflow-y: auto;
    div.btn-box {
      margin-bottom: 20px;
      .right-box {
        float: right;
        .el-button + .el-button {
          margin-left: 8px;
        }
      }
    }
  }
  .right-container {
    width: calc(100% - 350px);
    height: 100%;
    padding: 15px 30px;
    .el-form {
      width: calc(100% - 300px);
      .el-form-item {
        margin-bottom: 20px;

        /deep/.el-form-item__label {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(41, 41, 51, 1);
        }

        /deep/ .el-form-item__content {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(87, 87, 102, 1);
          .el-input {
            /deep/.el-input__inner {
              height: 36px;
              line-height: 36px;
              border: 1px solid rgba(228, 228, 235, 1);
            }
          }
          .el-textarea {
            .el-textarea__inner {
              min-height: 95px !important;
            }
          }
          .el-button {
            height: 36px;
            padding: 0 12px;
            line-height: 36px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            &.el-button--default {
              border: 1px solid rgba(228, 228, 235, 1);
              color: rgba(87, 87, 102, 1);
              &:hover,
              &:focus {
                border: 1px solid rgba(228, 228, 235, 1);
                background: #fff;
                color: rgba(87, 87, 102, 1);
              }
            }
            &.form-btn {
              padding: 0 20px;
            }
          }
        }
      }
    }
    div.null-data {
      height: 100%;
      position: relative;
      img {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translateX(-50%) translateY(-50%);
      }
    }
  }
}
</style>
