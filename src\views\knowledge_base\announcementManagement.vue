<template >
  <xyy-list-page class="announcement-management">
    <template slot="header">
      <div>
        <el-form ref="queryList" :model="queryList" class="announcement-management-box">
          <el-row class="innerEl" type="flex" justify="start" align="middle">
            <el-form-item class="serverNum" label="公告标题" label-width="80px" prop="serveNum">
              <el-input
                v-model="queryList.title"
                class="serverName"
                size="small"
                placeholder="请输入标题"
              />
            </el-form-item>

            <el-form-item class="serverNum" label="发布时间" label-width="80px" prop="dateRange">
              <el-date-picker
                :picker-options="pickerOptions"
                v-model="queryList.dateRange"
                type="daterange"
                range-separator="-"
                size="small"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                text="erdf"
                prefix-icon="el-icon-date"
                class="timeSel"
              />
            </el-form-item>
          </el-row>

          <el-row type="flex" class="innerEl top_20" justify="space-between" align="middle">
            <el-form-item>
              <!-- <el-button class="btnNew" size="medium" @click="toEdit">
                <span class="el-icon-plus"></span>新建
              </el-button>-->
              <xyy-button class="btnNew" icon-class="btn-add" size="medium" @click="toEdit">新建</xyy-button>
            </el-form-item>
            <el-form-item>
              <el-button
                plain
                type="primary"
                size="medium"
                class="searchCondition"
                @click="checkTimer(handerSearch('queryList'))"
              >查询</el-button>
              <el-button plain size="medium" @click="resetForm">重置</el-button>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
    </template>

    <template slot="body">
      <el-row type="flex" justify="space-between" align="top">
        <xyy-table
          style="width: 100%"
          :data="list"
          :list-query="listQuery"
          :col="col"
          :operation="operation"
          :offset-top="240"
          @get-data="getList"
          @operation-click="operationClick"
        ></xyy-table>
      </el-row>
    </template>
  </xyy-list-page>
</template>

<script>
import {
  delKnowledgeNotice,
  findKnowledgeNoticeList
} from '@/api/knowledge_base';
export default {
  name: 'announcementManagement',
  data() {
    return {
      // tabLoading: false,
      created: false,
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      queryList: {
        dateRange: [],
        title: ''
      },
      list: [],
      datas: {},
      col: [
        { index: 'title', name: '公告标题', resizable: true },
        { index: 'viewNum', name: '浏览量', resizable: true },
        { index: 'modifyTime', name: '发布时间', resizable: true },
        { index: 'modifyName', name: '发布人', resizable: true },
        {
          index: 'operation',
          name: '操作',
          width: 250,
          operation: true,
          resizable: true
        }
      ],
      operation: [
        {
          name: '详情',
          type: 0
        },
        {
          name: '编辑',
          type: 1
        },
        {
          name: '删除',
          type: 2
          // disabled: function(row) {
          //   return !!row.status;
          // }
        }
      ],
      searchstartTime: '开始时间',
      searchendTime: '结束时间',
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.minDate = minDate;
          this.maxDate = maxDate;
        },
        disabledDate: time => {
          const curDate = new Date().getTime();
          const two = 365 * 2 * 24 * 3600 * 1000;
          const twoyear = curDate - two;
          const three = 92 * 24 * 3600 * 1000;
          if (this.minDate) {
            return (
              time.getTime() > Date.now() ||
              time.getTime() < twoyear ||
              time > new Date(this.minDate.getTime() + three) ||
              time < new Date(this.minDate.getTime() - three)
            );
          }
          return time.getTime() > Date.now() || time.getTime() < twoyear;
        }
      },
      dateRange: ''
    };
  },
  created() {
    this.getNowTimeDate();
    this.created = true;
  },
  activated() {
    if (!this.created) this.getList(this.listQuery);
    this.created = false;
  },
  mounted() {},
  methods: {
    handerSearch(formName) {
      (this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      }),
        this.getList(this.listQuery);
    },
    getList: function(listQuery) {
      const { page, pageSize } = listQuery;
      // this.tabLoading = true;
      let params = {
        pageNum: page,
        pageSize,
        // endTime: this.queryList.dateRange[1],
        // startTime: this.queryList.dateRange[0],
        title: this.queryList.title
      };

      if (this.queryList.dateRange) {
        params.endTime = this.queryList.dateRange[1];
        params.startTime = this.queryList.dateRange[0];
      }

      findKnowledgeNoticeList(params)
        .then(res => {
          // this.tabLoading = false;
          const { total } = res.data;
          this.list = res.data.list;

          this.listQuery = {
            ...this.listQuery,
            page: Number(res.data.pageNum),
            pageSize: Number(res.data.pageSize),
            total: Number(res.data.total)
          };
          // this.listQuery = {
          //   ...this.listQuery,
          //   total: Number(total)
          // };
        })
        .catch(() => {
          // this.tabLoading = false;
        });
    },
    /**
     * 跳转到模板编辑页
     */
    toEdit(row) {
      if (row) {
        this.$router.replace({
          path: `/knowledge_base/announcementManagementEdit/${row.id}`,
          query: { templateId: row.id }
        });
      } else {
        this.$router.replace({
          path: `/knowledge_base/announcementManagementEdit/${new Date().getTime()}`
        });
      }
    },
    // 删除
    delForm(row) {
      this.$XyyMsg({
        title: '提示',
        content: '删除公告后不可恢复，是否继续？',
        onSuccess: () => {
          delKnowledgeNotice({ id: row.id }).then(res => {
            if (res.code === 1) {
              this.$XyyMessage.success('删除成功');
              this.getList(this.listQuery);
            } else {
              this.$XyyMsg({
                title: '提示',
                closeBtn: false,
                content: res.msg, // html代码串
                onSuccess: () => {}
              });
            }
          });
        }
      });
    },
    /**
     * 查看详情
     */
    showDetail(row) {
      this.$router.push({
        path: `/knowledge_base/announcementDetails/${row.id}`,
        query: { templateId: row.id }
      });
    },
    operationClick: function(type, row) {
      switch (type) {
        case 0:
          this.showDetail(row);
          break;
        case 1:
          this.toEdit(row);
          break;
        case 2:
          this.delForm(row);
          break;
      }
    },

    // 当前时间
    getNowTimeDate() {
      const time = new Date(8 * 3600 * 1000 + +new Date())
        .toJSON()
        .substr(0, 10);

      const cc = new Date().getTime();
      var halfYear = (365 / 2) * 24 * 3600 * 1000;
      var pastResult = cc - halfYear;
      var pastDate = new Date(pastResult),
        pastYear = pastDate.getFullYear(),
        pastMonth =
          pastDate.getMonth() + 1 < 10
            ? '0' + (pastDate.getMonth() + 1)
            : pastDate.getMonth() + 1,
        pastDay =
          pastDate.getDate() < 10
            ? '0' + pastDate.getDate()
            : pastDate.getDate();

      const oldTime = pastYear + '-' + pastMonth + '-' + pastDay;
      this.queryList.dateRange = [oldTime, time];
    },
    dateTimeFocus() {
      this.minDate = undefined;
      // 条件时间查询
      const that = this;
      that.$nextTick(function() {
        document
          .getElementsByClassName('el-picker-panel')[0]
          .addEventListener('click', function() {});
      });
    },
    queryTime() {},

    resetForm() {
      (this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      }),
        (this.queryList = {
          dateRange: [], // 日期
          title: ''
        });
      this.getNowTimeDate();
      this.getList(this.listQuery);
    }
  }
};
</script>

<style lang="scss">
.announcement-management {
  /deep/.page-body {
    width: 100%;
  }
  /deep/.table-containter {
    width: 100%;
    min-height: 400px;
  }
  /deep/.el-table__empty-block {
    width: 100 !important;
  }
  .announcement-management-box {
    padding-bottom: 20px;
    margin-bottom: 10px;
    border-bottom: 1px dotted #e4e4eb;
    .innerEl {
      height: 38px;
      /deep/.el-input__inner {
        width: 100%;
        height: 36px;
        line-height: 36px;
      }
      /deep/.el-form-item {
        margin: 0;
      }
      .timeSel {
        width: 300px;
      }
      /deep/input[type='number']::-webkit-inner-spin-button,
      /deep/input[type='number']::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      .btnNew {
        color: #fff;
        background-color: rgba(59, 149, 168, 1);
      }
      .serverName {
        width: 200px;
      }
      .serverNum {
        margin-right: 20px;
      }
      .searchCondition.is-plain {
        background: rgba(59, 149, 168, 1);
        color: #fff;
      }
      &.top_20 {
        margin-top: 20px;
      }
    }
  }
}
</style>
