<template>
  <div class="editor-page">
    <steps :active="1"></steps>
    <div class="editor-container">
      <div class="fields-container">
        <div class="fields-type">
          <el-dropdown trigger="click" placement="bottom-start" @command="setFieldType">
            <span :title="$options.filters['fieldTypeFormat'](fieldType)" class="el-dropdown-link">
              <svg-icon icon-class="filter" />
              {{ fieldType | fieldTypeFormat }}
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="item in fieldTypeOptions"
                :key="item.value"
                :command="item.value"
              >{{ item.label }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-input
            v-model.trim="fieldName"
            suffix-icon="el-icon-search"
            maxlength="20"
            @input="getFieldDatas"
          ></el-input>
          <!-- <description :info="info" class="msg-tips" @callback="toField"></description> -->
        </div>
        <draggable
          :list="fields"
          :group="{name: 'fields', pull: 'clone', put: false}"
          :sort="false"
          :clone="addField"
          class="fields-body"
          @start="isDragging = true"
          @end="isDragging = false"
        >
          <div v-for="el in fields" :key="el.fieldCode" class="fields-item">
            <el-popover
              :disabled="!el.remarks"
              :content="el.remarks"
              placement="bottom-end"
              trigger="hover"
              popper-class="field-title"
            >
              <div slot="reference" class="field-name">{{ el.fieldName }}</div>
            </el-popover>
            <div class="field-type">{{ el.fieldType | fieldTypeFormat }}</div>
          </div>
        </draggable>
      </div>
      <div class="editor-main">
        <div ref="editor-head" class="editor-head">预览</div>
        <div class="editor-body">
          <layout
            ref="layout"
            :col-datas="datas"
            :lay-datas="layDatas"
            :editing="true"
            :class="{'is-dragging':isDragging}"
            @setLayDatas="setLayDatas"
            @resizeLayWidth="setLayWidth"
          >
            <template slot="col" slot-scope="{ arr,row,col }">
              <draggable
                :list="tempDatas"
                :class="{'active':arr[row][col]}"
                :group="{name:'fields',put:!isCenterDragging || !arr[row][col]}"
                class="col-box"
                filter=".svg-icon"
                @add="leftEnd(row,col,arr)"
                @start="centerStart"
                @end="centerEnd($event,row,col,arr)"
              >
                <template v-if="arr[row][col]">
                  <div class="field-content">
                    <field
                      :keys="arr[row][col].fieldType"
                      :preview="arr[row][col]"
                      :read="arr[row][col].fieldType===10"
                      refs="field"
                    ></field>
                  </div>
                  <!-- <svg-icon
                    v-popover="`info${row}${col}`"
                    icon-class="op-info"
                    @click="infoStatus[row].splice(col,1,true)"
                  ></svg-icon>-->
                  <i
                    v-popover="`info${row}${col}`"
                    class="op-icon op-info"
                    title="详情"
                    @click="infoStatus[row].splice(col,1,true)"
                  ></i>
                  <el-popover
                    :ref="`info${row}${col}`"
                    v-model="infoStatus[row][col]"
                    placement="bottom-end"
                    title="字段信息"
                    width="240"
                    trigger="manual"
                    popper-class="template-field-info"
                  >
                    <ul v-for="(value, name) in getDisplayField(arr[row][col])" :key="name">
                      <li>{{ name }}{{ value }}</li>
                    </ul>
                    <ul>
                      <li>
                        <i>*修改字段属性请前往字段管理页面</i>
                      </li>
                    </ul>
                    <i class="el-icon-close" @click="infoStatus[row].splice(col,1,false)"></i>
                  </el-popover>
                  <!-- <svg-icon
                    icon-class="op-del"
                    @click="delField(row,col,arr)"
                  ></svg-icon>-->
                  <i class="op-icon op-del" title="删除" @click="delField(row,col,arr)"></i>
                </template>
              </draggable>
            </template>
          </layout>
        </div>
      </div>
    </div>
    <div class="btn-container">
      <el-button @click="checkTimer(prevStep,'timer1')()">上一步</el-button>
      <el-button type="primary" @click="checkTimer(nextStep,'timer2')()">下一步</el-button>
    </div>
  </div>
</template>

<script>
import draggable from 'vuedraggable'; // 拖拽插件
import field from '@/components/Fields/preview'; // 字段组件
import dateFormatObj from '@/utils/filter'; // 日期格式化
import steps from './components/steps'; // 导航
import description from '@/components/tools/description'; // 提示信息
import layout from '@/components/templates/layout'; // 布局组件
import {
  FIELD_TYPES,
  getFieldDatas,
  saveTemplateData,
  updateTemplateData,
  getTemplateData,
  validateField
} from '@/api/templates';
export default {
  components: {
    draggable,
    field,
    steps,
    description,
    layout
  },
  filters: {
    fieldTypeFormat: val =>
      FIELD_TYPES.filter(el => el.value === val).length
        ? FIELD_TYPES.filter(el => el.value === val)[0].label
        : ''
  },
  data() {
    return {
      fieldTypeOptions: FIELD_TYPES,
      fieldType: -1, // 字段类型
      fieldName: '', // 字段名称
      fields: [], // 字段数据
      datas: [], // 模板数据
      templateId: '', // 模板id
      templateType: 0, // 模板类型
      isUpdate: false, // 修改状态
      delDatas: [], // 修改状态下 要删除的id
      timer1: null,
      timer2: null,
      oldDatas: [], // 更新前数据
      // info: [
      //   {
      //     title: '',
      //     info:
      //       '如未配置任何字段，请点击 <a>新建字段</a> 跳自定义段配置 。完成字段配置后，再创建模板表单'
      //   }
      // ],
      layDatas: '0-0,0-0,0-0', // 布局数据
      oldLayDatas: '', // 旧布局数据
      tempDatas: [], // 暂存数据
      infoStatus: [], // 信息显示状态
      isDragging: false, // 拖动状态
      isCenterDragging: false // 中间区域拖动
    };
  },
  computed: {
    /**
     * 重复字段数据
     */
    duplicateDatas() {
      const indexs = []; // 未通过字段索引
      const keys = []; // 未通过字段key
      this.datas.forEach((el, i) => {
        const _i = keys.indexOf(el.fieldCode);
        if (_i >= 0) {
          indexs.push(_i, i);
          keys.push(null);
        } else {
          keys.push(el.fieldCode);
        }
      });
      if (indexs.length) {
        this.$XyyMessage.error('存在重名字段');
      }
      return indexs;
    }
  },
  watch: {
    layDatas(layout) {
      this.initInfoStatus(layout);
    }
  },
  activated() {
    this.fieldType = -1;
    this.fieldName = '';
    this.getFieldDatas();
    this.delDatas = [];
    this.templateId = this.$route.query.templateId
      ? this.$route.query.templateId
      : '';
    this.templateType = Number(this.$route.query.templateType);
    this.isUpdate = this.$route.query.isUpdate
      ? this.$route.query.isUpdate
      : false;
    if (this.isUpdate) {
      this.$store.dispatch('tagsView/updateVisitedView', {
        ...this.$route,
        meta: {
          title: '编辑模板'
        }
      });
      // 初始化模板数据
      this.layDatas = '';
      this.oldLayDatas = '';
      this.datas = [];
      this.oldDatas = [];
      getTemplateData({
        templateId: this.templateId,
        needSystemField: false
      })
        .then(res => {
          if (res.code === 1) {
            const _data = res.data.list;
            this.datas = _data.map(el => {
              return {
                systemFieldFlag: el.systemFieldFlag,
                fieldName: el.fieldName,
                fieldText: el.fieldText,
                fieldType: el.fieldType,
                limitNumberCharacters: el.limitNumberCharacters,
                defaultValue: el.defaultValue,
                tips: el.tips,
                remarks: el.remarks,
                optionSettings: el.optionSettings,
                fieldCode: el.fieldCode,
                sort: el.sort,
                id: el.fieldId,
                _id: el.id
              };
            });
            this.oldDatas = JSON.parse(JSON.stringify(this.datas)); // 更新前数据
            this.layDatas = res.data.layout;
            this.oldLayDatas = res.data.layout;
            if (!this.layDatas) {
              if (this.datas.length) {
                const arr = [];
                const len = Math.floor(this.datas.length / 2).toFixed(0);
                for (let i = 0; i < len; i++) {
                  arr.push([1, 1]);
                }
                if (this.datas.length % 2 !== 0) {
                  arr.push([1, 0]);
                }
                this.layDatas = arr.map(cols => cols.join('-')).join();
              } else {
                this.layDatas = '0-0,0-0,0-0';
              }
            }
            this.$nextTick(() => {
              if (this.$refs['layout']) {
                this.$refs['layout'].initRowHeight();
              }
            });
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    } else {
      this.datas = [];
      this.layDatas = '0-0,0-0,0-0';
    }
  },
  methods: {
    initInfoStatus(layout) {
      const layArray = layout.split(',').map(el => el.split('-'));
      const arr = [];
      layArray.forEach((cols, i) => {
        arr.push([]);
        cols.forEach((col, j) => {
          arr[i][j] = false;
        }, this);
      }, this);
      this.infoStatus = arr;
    },
    setLayDatas(datas) {
      this.layDatas = datas;
    },
    setLayWidth() {
      this.$nextTick(() => {
        this.$refs['editor-head'].style.width =
          this.$refs['layout'].$children[0].$el.offsetWidth + 'px';
      });
    },
    leftEnd(row, col, cols) {
      this.isDragging = false;
      if (this.tempDatas.length) {
        const arr = this.layDatas.split(',').map(el => el.split('-'));
        if (Number(arr[row][col])) {
          this.$XyyMessage.warning('该区域已有字段');
          this.tempDatas = [];
          return;
        }
        arr[row][col] = 1;
        this.layDatas = arr.map(cols => cols.join('-')).join();
        cols[row][col] = this.tempDatas[0];
        const datas = [];
        cols.forEach(_arr => {
          _arr.forEach(el => {
            if (el) {
              datas.push(el);
            }
          });
        });
        this.datas = datas;
        this.tempDatas = [];
        // console.log(this.datas);
      }
    },
    centerStart() {
      this.isDragging = true;
      this.isCenterDragging = true;
    },
    centerEnd(e, row, col, cols) {
      this.isDragging = false;
      this.isCenterDragging = false;
      this.$nextTick(() => {
        const pos = this.$refs['layout'].getPosition(
          e.originalEvent.clientX,
          e.originalEvent.clientY
        );
        if (pos.row !== -1 && pos.col !== -1) {
          // 位置没变
          if (pos.row === row && pos.col === col) {
            return;
          }
          const arr = this.layDatas.split(',').map(el => el.split('-'));
          // 格中有数据
          if (Number(arr[pos.row][pos.col])) {
            this.$XyyMessage.warning('该区域已有字段');
            return;
          }
          // 互换数据
          arr[pos.row][pos.col] = 1;
          arr[row][col] = 0;
          this.layDatas = arr.map(cols => cols.join('-')).join();
          cols[pos.row][pos.col] = cols[row][col];
          cols[row][col] = '';
          const datas = [];
          cols.forEach(_arr => {
            _arr.forEach(el => {
              if (el) {
                datas.push(el);
              }
            });
          });
          this.datas = datas;
        }
      });
    },
    /**
     * 跳转到字段
     */
    toField() {
      this.$router.push({
        path: `/fields/newAddFields/` + new Date().getTime()
      });
    },
    /**
     * 设置字段类型
     */
    setFieldType(type) {
      this.fieldType = type;
      this.getFieldDatas();
    },
    /**
     * 添加字段 拖拽初始化返回数据
     */
    addField(data) {
      if (this.validateField(data)) {
        this.$XyyMessage.error('模板中此字段已存在');
        this.tempDatas = [];
      } else {
        return {
          systemFieldFlag: data.systemFieldFlag,
          fieldName: data.fieldName,
          fieldText: data.fieldText,
          fieldType: data.fieldType,
          fieldCode: data.fieldCode,
          limitNumberCharacters: data.limitNumberCharacters,
          defaultValue: data.defaultValue,
          tips: data.tips,
          remarks: data.remarks,
          optionSettings: data.optionSettings,
          id: data.id,
          _id: ''
        };
      }
    },
    /**
     * 校验重名字段
     */
    validateField(data) {
      return this.datas.filter(el => el.fieldCode === data.fieldCode).length;
    },
    /**
     * 删除字段
     */
    delField(row, col, arr) {
      const data = arr[row][col];
      let index = -1;
      this.datas.forEach((el, i) => {
        if (el.fieldCode === data.fieldCode) {
          index = i;
        }
      });
      const layArray = this.layDatas.split(',').map(el => el.split('-'));

      if (this.isUpdate && data._id) {
        validateField(data.id, this.templateId).then(res => {
          if (res.code === 1) {
            if (res.data.length) {
              const msg = `该字段正在被引用作为表单应用（${res.data
                .map(el => el.name)
                .join('、')}）的流程分支条件，请先修改表单应用`;
              this.$XyyMsg({
                title: '提示',
                closeBtn: false,
                content: msg,
                onSuccess: () => {}
              });
            } else {
              this.delDatas.push({
                id: data._id,
                fieldId: data.id,
                fieldCode: data.fieldCode,
                templateId: this.templateId
              });
              this.datas.splice(index, 1);
              layArray[row][col] = 0;
              this.layDatas = layArray.map(cols => cols.join('-')).join();
            }
          } else {
            this.$XyyMessage.error(res.msg);
          }
        });
      } else {
        this.datas.splice(index, 1);
        layArray[row][col] = 0;
        this.layDatas = layArray.map(cols => cols.join('-')).join();
      }
    },

    /**
     * 根据字段类型获取字段数据
     */
    getFieldDatas() {
      getFieldDatas(this.fieldType >= 0 ? this.fieldType : '', this.fieldName)
        .then(res => {
          if (res.code === 1) {
            this.fields = [].concat(res.data.list);
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },
    /**
     * 获取字段展示项
     */
    getDisplayField(data) {
      const text = [0, 1, 8, 9];
      const select = [2, 6, 7, 10];
      const option = [3, 4];
      const date = [5];
      if (text.indexOf(data.fieldType) >= 0) {
        return {
          '字段名称：': data.fieldName,
          '字段文本：': data.fieldText,
          '字段类型：': this.$options.filters['fieldTypeFormat'](
            data.fieldType
          ),
          '限制字符数：': data.limitNumberCharacters,
          '提示信息：': data.tips,
          '默认值：': data.defaultValue,
          '备注说明：': data.remarks
        };
      } else if (select.indexOf(data.fieldType) >= 0) {
        return {
          '字段名称：': data.fieldName,
          '字段文本：': data.fieldText,
          '字段类型：': this.$options.filters['fieldTypeFormat'](
            data.fieldType
          ),
          '提示信息：': data.tips,
          '备注说明：': data.remarks
        };
      } else if (option.indexOf(data.fieldType) >= 0) {
        return {
          '字段名称：': data.fieldName,
          '字段文本：': data.fieldText,
          '字段类型：': this.$options.filters['fieldTypeFormat'](
            data.fieldType
          ),
          '备注说明：': data.remarks
        };
      } else if (date.indexOf(data.fieldType) >= 0) {
        // 针对后台返回数据的处理
        let opts = data.optionSettings;
        if (opts && typeof data.optionSettings === 'string') {
          opts = JSON.parse(opts.replace(/(&quot;)/g, '"'));
        }
        return {
          '字段名称：': data.fieldName,
          '字段文本：': data.fieldText,
          '字段类型：': this.$options.filters['fieldTypeFormat'](
            data.fieldType
          ),
          '日期格式：': dateFormatObj.dataTime(
            new Date().getTime(),
            opts.dateOptions.dateSelect
          ),
          '提示信息：': data.tips,
          '默认值：': opts.dateOptions.defaultDateArray.filter(
            el => el.value === opts.dateOptions.defaultDate
          )[0].label,
          '备注说明：': data.remarks
        };
      }
    },
    /**
     * 保存数据 并跳转到基础信息
     */
    prevStep() {
      const old = this.$store.state.tagsView.visitedViews.filter(
        el => el.path === this.$route.path
      )[0];
      const path = `/worksheet/templateBase/${this.templateId}`;
      const view = this.$router.options.routes
        .filter(el => el.name === 'worksheet')[0]
        .children.filter(el => el.name === 'templateBase')[0];
      const query = {
        templateId: this.templateId,
        templateType: this.templateType,
        isUpdate: true
      };
      if (this.datas.length) {
        this.saveData(copy => {
          if (copy || this.$route.query.copy) {
            query.copy = true;
          }
          this.updateTab(
            {
              ...view,
              path,
              query,
              fullPath: this.getFullPath(path, query),
              meta: {
                title: '编辑模板'
              }
            },
            old
          );
        });
      } else {
        this.updateTab(
          {
            ...view,
            path,
            query,
            fullPath: this.getFullPath(path, query),
            meta: {
              title: '编辑模板'
            }
          },
          old
        );
      }
    },
    updateTab(view, old) {
      this.$store
        .dispatch('tagsView/updateCurrentView', {
          old: old,
          view
        })
        .then(() => {
          const { fullPath } = view;
          this.$nextTick(() => {
            this.$router.replace({
              path: fullPath
            });
          });
        });
    },
    getFullPath(path, query) {
      const params = [];
      for (const key in query) {
        params.push(key + '=' + query[key]);
      }
      return path + '?' + params.join('&');
    },
    /**
     * 保存数据
     */
    saveData(callback) {
      if (!this.datas.length) {
        this.$XyyMessage.warning('请配置模板后保存');
        return;
      } else if (this.duplicateDatas.length) {
        this.$XyyMessage.error('存在重名字段');
        return;
      } else {
        const arr = this.layDatas.split(',');
        // 删除末尾空行
        while (arr[arr.length - 1] === '0-0' || arr[arr.length - 1] === '0') {
          arr.splice(arr.length - 1, 1);
        }
        // 判断是否还有空行
        if (arr.indexOf('0-0') >= 0 || arr.indexOf('0') >= 0) {
          this.$XyyMessage.error('模板中有空行，请先删除行');
          return;
        } else {
          this.layDatas = arr.join();
        }
      }
      let loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      let _datas = this.datas.map((el, index) => {
        return {
          id: el._id,
          fieldId: el.id,
          fieldCode: el.fieldCode,
          templateId: this.templateId,
          sort: index
        };
      });

      // 过滤更新字段
      _datas = _datas.filter(el => {
        if (!el.id) {
          return true;
        } else {
          return this.oldDatas.some(
            _el => _el._id === el.id && _el.sort !== el.sort
          );
        }
      });

      if (this.isUpdate && this.delDatas.length) {
        _datas = _datas.concat(this.delDatas);
      }
      if (this.isUpdate) {
        if (_datas.length || this.layDatas !== this.oldLayDatas) {
          updateTemplateData(
            _datas,
            this.layDatas,
            this.templateId,
            false
          ).then(res => {
            loading.close();
            if (res.code === 1) {
              if (callback) {
                callback();
              }
            } else if (res.code === 2) {
              this.$XyyMsg({
                title: '提示',
                content: res.msg,
                onSuccess: () => {
                  loading = this.$loading({
                    lock: true,
                    text: 'Loading',
                    spinner: 'el-icon-loading',
                    background: 'rgba(255,255,255, 0.8)'
                  });
                  updateTemplateData(
                    _datas,
                    this.layDatas,
                    this.templateId,
                    true
                  ).then(res => {
                    loading.close();
                    if (res.code === 1) {
                      this.templateId = res.data;
                      if (callback) {
                        callback(true); // 生成副本
                      }
                    } else {
                      this.$XyyMsg({
                        title: '提示',
                        closeBtn: false,
                        content: res.msg, // html代码串
                        onSuccess: () => {}
                      });
                    }
                  });
                }
              });
            } else {
              this.$XyyMsg({
                title: '提示',
                closeBtn: false,
                content: res.msg, // html代码串
                onSuccess: () => {}
              });
            }
          });
        } else {
          loading.close();
          if (callback) {
            callback();
          }
        }
      } else {
        saveTemplateData(_datas, this.layDatas).then(res => {
          loading.close();
          if (res.code === 1) {
            if (callback) {
              callback();
            }
          } else {
            this.$XyyMsg({
              title: '提示',
              closeBtn: false,
              content: res.msg, // html代码串
              onSuccess: () => {}
            });
          }
        });
      }
    },
    /**
     * 保存数据 并跳转到字段配置
     */
    nextStep() {
      const old = this.$store.state.tagsView.visitedViews.filter(
        el => el.path === this.$route.path
      )[0];
      const view = this.$router.options.routes
        .filter(el => el.name === 'worksheet')[0]
        .children.filter(el => el.name === 'templateConfig')[0];
      this.saveData(copy => {
        const templateId = this.templateId;
        const path = `/worksheet/templateConfig/${templateId}`;
        const query = {
          templateId: templateId,
          templateType: this.templateType
        };
        if (this.isUpdate) {
          query.isUpdate = this.isUpdate;
        }
        if (copy || this.$route.query.copy) {
          query.copy = true;
        }
        this.updateTab(
          {
            ...view,
            path,
            query,
            fullPath: this.getFullPath(path, query),
            meta: {
              title: this.isUpdate ? '编辑模板' : '新建模板'
            }
          },
          old
        );
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.editor-page {
  box-sizing: border-box;
  padding: 6px 20px 36px;
  position: relative;
  .editor-container {
    height: calc(100% - 77px);
    overflow: hidden;
    box-sizing: border-box;
    padding-top: 6px;
    > div {
      float: left;
      height: 100%;
      border-bottom: none !important;
    }
    > div.fields-container {
      width: 250px;
      margin-right: 20px;
      border-radius: 2px;
      border: 1px solid rgba(228, 228, 235, 1);
      box-sizing: border-box;
      .fields-type {
        height: 60px;
        padding: 12px 15px;
        box-sizing: border-box;
        background: #f0f2f5;
        position: relative;
        .el-dropdown {
          float: left;
          top: 50%;
          transform: translateY(-50%);
          width: calc(100% - 140px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .el-input {
          width: 140px;
          float: left;
          /deep/ .el-input__inner {
            line-height: 36px;
            height: 36px;
          }
        }
        .msg-tips {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          margin-left: 5px;
        }
      }
      .fields-body {
        height: calc(100% - 60px);
        overflow: auto;
        .fields-item {
          height: 75px;
          box-sizing: border-box;
          border-bottom: 1px solid #e4e4eb;
          padding: 15px;
          cursor: pointer;
          .field-name {
            height: 20px;
            line-height: 20px;
            font-size: 14px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            color: rgba(41, 41, 51, 1);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .field-type {
            height: 17px;
            line-height: 17px;
            font-size: 12px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            color: rgba(144, 147, 153, 1);
            margin-top: 8px;
          }
        }
        /deep/.sortable-drag {
          background-image: linear-gradient(
            to bottom right,
            rgba(59, 149, 168, 0.5),
            rgba(255, 255, 255, 0)
          );
          overflow: hidden;
          * {
            opacity: 0;
          }
        }
      }
    }

    > div.editor-main {
      width: calc(100% - 270px);
      div.editor-head {
        height: 60px;
        line-height: 60px;
        background: #f0f2f5;
        font-size: 14px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        color: rgba(41, 41, 51, 1);
        padding: 0 20px;
        border: 1px solid #e4e4eb;
        width: calc(100% - 25px);
      }
      div.editor-body {
        height: calc(100% - 60px);
        background: #fff;
        /deep/.template-layout {
          position: relative;
          overflow-y: auto;
          height: 100%;
          .el-row {
            width: calc(100% - 25px);
          }
          .col-box {
            min-height: 66px;
            position: relative;
            &.active {
              background: #f5f7fa;
            }

            .preview {
              .single {
                margin: 15px;
              }
            }

            .sortable-ghost {
              background: rgba(218, 233, 236, 0.5);
              width: 100%;
              height: 60px;
              * {
                opacity: 0;
              }
            }

            .sortable-drag {
              background: rgba(59, 149, 168, 0.5);
              overflow: hidden;
              * {
                opacity: 0;
              }
            }
            div.field-content {
              width: calc(100% - 56px);
              float: left;
            }
            > .op-icon {
              position: absolute;
              width: 16px;
              height: 16px;
              display: inline-block;
              top: 27px;
              cursor: pointer;
              &.op-info {
                background: url(../../assets/icons/op-info.png) 0 0 no-repeat;
                right: 40px;
                &:hover {
                  background: url(../../assets/icons/op-info-active.png) 0 0
                    no-repeat;
                }
              }
              &.op-del {
                background: url(../../assets/icons/op-del.png) 0 0 no-repeat;
                right: 12px;
                &:hover {
                  background: url(../../assets/icons/op-del-active.png) 0 0
                    no-repeat;
                }
              }
            }
          }
          &.is-dragging {
            .el-row:hover {
              .layout-operation {
                display: none;
              }
            }
            .col-box.active {
              background: #fff;
            }
            div.field-content {
              width: 100%;
            }
            .op-icon {
              display: none;
            }
          }
        }
        div.editor-item.active {
          div.content-container {
            background: #dae9ec;
            i.el-icon-close {
              display: inline;
            }
          }
        }
        div.editor-item.error {
          border: 1px solid #f56c6c;
        }
      }
    }
  }
  div.btn-container {
    position: fixed;
    bottom: 0;
    width: calc(100% - 211px);
    box-sizing: border-box;
    left: 211px;
    height: 56px;
    background: #fff;
    box-shadow: 0px -2px 2px 0px rgba(145, 144, 144, 0.11);
    margin: 0;
    text-align: right;
    button {
      height: 36px;
      margin: 10px 20px 10px 0;
    }
    button:first-child {
      margin: 10px 10px 10px 0;
    }
  }
}
</style>
<style lang="scss">
.el-popover.el-popper.field-title {
  margin-left: 118px;
  width: 270px;
}
.el-popover.el-popper.template-field-info {
  position: relative;
  max-height: 250px;
  overflow-x: hidden;
  overflow-y: auto;
  /deep/.el-popover__title {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(41, 41, 51, 1);
    margin-bottom: 10px;
  }
  /deep/ul {
    padding: 0;
    margin: 5px 0;
    li {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(144, 147, 153, 1);
      white-space: break-all;
    }
    i {
      font-style: normal;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(255, 149, 0, 1);
    }
  }
  i.el-icon-close {
    position: absolute;
    top: 12px;
    right: 12px;
    font-size: 12px;
    cursor: pointer;
    color: #909399;
  }
}
</style>
