<template>
  <div class="info-detail-component-container">
    <!-- 药店名 -->
    <div class="header-content">
      <span class="shop-name">{{ customer.realName }}</span>
      <template v-if="showBind">
        <el-button type="primary" size="mini" plain @click="handlBindBtnClick">绑定</el-button>
      </template>
    </div>
    <!-- 详情信息 -->
    <div class="detail-section">
      <div class="item-info whole-line" style="display: flex;align-items: center;">
        <span class="info-field">开票信息：</span>
        <span class="info-value"><el-button type="primary" size="mini" plain
            @click="handlePreviewInvoiceInfo">查看</el-button></span>
      </div>
      <div class="item-info">
        <span class="info-field">绑定CA：</span>
        <span class="info-value">{{ cAExclusiveKeFu.kefuName ? cAExclusiveKeFu.kefuName : '无' }}</span>
      </div>
      <div class="item-info">
        <span style="color: #292933;font-size: 14px;font-weight: 500;">客户添加CA企业微信：</span>
        <span style=" width: calc(100% - 150px);color: #575766;font-size: 14px;font-weight: 500;">{{
          cAExclusiveKeFu.isAddWechat == 1 ? '是' : '否' }}</span>
      </div>
      <div class="item-info whole-line">
        <span style="color: #292933;font-size: 14px;font-weight: 500;">关联CA信息：</span>
        <el-link v-if="cAExclusiveKeFu.kefuName" :underline="false" @click="checkCA">
          <span class="item-value-ca">查看</span>
        </el-link>
        <span v-if="!cAExclusiveKeFu.kefuName" style="color: #575766;font-size: 14px;font-weight: 500;">无</span>
      </div>
      <div class="item-info">
        <span class="info-field">药店编码：</span>
        <span class="info-value">{{ customer.id }}</span>
      </div>
      <div class="item-info">
        <span class="info-field">会员姓名：</span>
        <span class="info-value">{{ customer.nickname }}</span>
      </div>
      <div class="item-info">
        <span class="info-field">手机号码：</span>
        <span class="info-value">{{ customer.mobile }}</span>
      </div>
      <div class="item-info">
        <span class="info-field">客户类型：</span>
        <span class="info-value">{{ customer.businessTypeStr }}</span>
      </div>
      <div class="item-info">
        <span class="info-field">状态：</span>
        <span class="info-value" :class="{ light: customer.status === 1 }">{{ customer.statusStr }}</span>
      </div>
      <div class="item-info">
        <span class="info-field">资质状态：</span>
        <span class="info-value">{{ customer.licenseStatusStr }}</span>
      </div>
      <div class="item-info">
        <span class="info-field">当前等级：</span>
        <span class="info-value">{{ customer.currentLevel }}</span>
      </div>
      <div class="item-info">
        <span class="info-field">普药销售：</span>
        <span class="info-value">{{ customer.sysRealName }}</span>
      </div>
      <div class="item-info">
        <span class="info-field">邀请码：</span>
        <span class="info-value">{{ customer.authCode }}</span>
      </div>
      <div class="item-info">
        <span class="info-field">销售电话：</span>
        <span class="info-value">{{ customer.sysJobNumber }}</span>
      </div>
      <div class="item-info">
        <span class="info-field">注册时间：</span>
        <span class="info-value">{{ customer.activeTime | formatTime }}</span>
      </div>
      <div class="item-info">
        <span class="info-field">注册地区：</span>
        <span class="info-value">{{ customer.registAddress }}</span>
      </div>
      <div class="item-info">
        <span class="info-field">地址：</span>
        <span class="info-value">{{ customer.ckaddress }}</span>
      </div>
      <div class="item-info whole-line">
        <span class="info-field">经营范围：</span>
        <span class="info-value">{{ customer.businesscont }}</span>
      </div>
    </div>

    <!-- 银行卡信息 -->
    <template v-if="banks.length">
      <div class="detail-bank-section" v-for="bank in banks" :key="bank.id">
        <div class="item-bank">
          <span class="info-field">开户银行：</span>
          <span class="info-value">{{ bank.bankName }}</span>
        </div>
        <div class="item-bank">
          <span class="info-field">开户户名：</span>
          <span class="info-value">{{ bank.accountName }}</span>
        </div>
        <div class="item-bank">
          <span class="info-field">银行卡号：</span>
          <span class="info-value">{{ bank.cardNo }}</span>
        </div>
      </div>
    </template>

    <!-- 二维码弹窗 -->
    <el-dialog :visible="qrCodeDialogVisible" custom-class="qrCodeDialog" :show-close="false" title="关联CA信息"
      class="dialogInner" width="40%">
      <div class="qrtips">图片已生成，可截图分享或右键保存到本地</div>
      <img id="avatar" :src="cAExclusiveKeFu.avatar ? cAExclusiveKeFu.avatar : defaultAvatar" shape="square" />
      <img id="qrcode" :src="cAExclusiveKeFu.qrCode ? cAExclusiveKeFu.qrCode : defaultAvatar" shape="square" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="qrCodeDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>

    <!-- 开票信息 -->
    <dialog-invoice-info ref="refDialogInvoiceInfo"></dialog-invoice-info>
  </div>
</template>

<script>
import { bindOrCancelUser } from '@/api/im_view/customeInfo';
import _avatar from '@/assets/common/user-avatar.png';
import DialogInvoiceInfo from './dialog-invoice-info.vue';
export default {
  name: 'compBaseInfoDetailYBM',
  components: {
    DialogInvoiceInfo
  },
  filters: {},
  props: {
    customer: {
      type: Object,
      default: () => { }
    },
    banks: {
      type: Array,
      default: () => []
    },
    showBind: {
      type: Boolean,
      default: false
    },
    cAExclusiveKeFu: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      qrCodeDialogVisible: false,
      defaultAvatar: _avatar
    };
  },
  computed: {},
  watch: {},
  created() { },
  mounted() { },
  methods: {
    /**
     * 绑定
     */
    handlBindBtnClick() {
      if (this.$store.getters.containerid && this.customer.id) {
        bindOrCancelUser({
          dialogId: this.$store.getters.containerid,
          uid: this.customer.id
        }).then(resp => {
          if (resp.code === 1) {
            this.$parent.emitCustomerBind(resp.data);
          } else {
            this.$XyyMessage.warning(resp.msg);
          }
        });
      } else {
        this.$XyyMessage.error('请选择会话');
      }
    },

    /**
     * 查看CA
     */
    checkCA() {
      this.qrCodeDialogVisible = true;
    },

    // 查看开票信息
    handlePreviewInvoiceInfo() {
      this.$refs.refDialogInvoiceInfo.init({ merchantId: this.customer.id })
    }
  }
};
</script>

<style lang="scss" scoped>
.info-detail-component-container {
  width: 100%;

  .header-title {
    width: 100%;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .header-content {
    width: 100%;
    padding: 18px 0 18px;
    border-bottom: 1px dashed #dcdee3;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .shop-name {
      color: #303133;
      font-size: 15px;
      font-weight: 600;
    }
  }

  .detail-section {
    width: 100%;
    padding: 20px 0 0;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;

    .item-info {
      width: 49%;
      line-height: 20px;
      margin-bottom: 10px;
      flex-grow: 0;
      flex-shrink: 0;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;

      // &:nth-child(even) {
      //   margin-left: 2%;
      // }

      .info-field {
        width: 70px;
        color: #292933;
        font-size: 14px;
        font-weight: 500;
      }

      .info-value {
        width: calc(100% - 70px);
        color: #575766;
        font-size: 14px;
        font-weight: 500;
      }

      .light {
        color: #67c23a;
      }

      .item-value-ca {
        color: #575766;
        font-size: 12px;
        font-weight: 500;
        background: rgba(0, 0, 0, 0);
        color: rgba(59, 149, 168, 1);
      }

      &.whole-line {
        width: 100%;

        &:nth-child(even) {
          margin-left: 0;
        }
      }
    }
  }

  .detail-bank-section {
    width: 100%;

    .item-bank {
      width: 100%;
      margin-bottom: 10px;

      .info-field {
        width: 70px;
        color: #292933;
        font-size: 14px;
        font-weight: 500;
      }

      .info-value {
        width: calc(100% - 70px);
        color: #575766;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  .qrCodeDialog {
    .qrtips {
      padding: 0 20px 30px;
      font-size: 16px;
    }

    #avatar {
      margin: 0 20px;
      width: 160px;
      height: 160px;
    }

    #qrcode {
      width: 160px;
      height: 160px;
      display: inline-block;

      /deep/ img {
        margin: 0 auto;
      }
    }
  }
}
</style>