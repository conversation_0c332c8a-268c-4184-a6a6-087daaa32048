<template>
  <div class="page">
    <xyy-list-page>
      <template slot="header">
        <el-row class="herader-title" type="flex" justify="space-between" align="middle">
          <span style="font-size:16px">工单信息报表</span>
        </el-row>
        <el-form
          ref="listQuery"
          :rules="rules"
          :model="listQuery"
          :inline="true"
          label-position="right"
          class="search-form"
        >
          <el-row :gutter="10" type="flex" class="row-bg" justify="space-between">
            <!-- 发起时间-->
            <el-form-item label="日期" prop="dataRange">
              <el-date-picker
                v-model="listQuery.dataRange"
                :picker-options="pickerOptions"
                type="daterange"
                range-separator="-"
                size="small"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                text="erdf"
                prefix-icon="el-icon-date"
                class="innerSelTime"
                @focus="dateTimeFocus()"
              />
            </el-form-item>
            <!--工单类型-->
            <el-form-item class="form-item" label="工单类型" prop="formTypeId">
              <el-select v-model="listQuery.formTypeId" placeholder="请选择" @change="selectEvents">
                <el-option label="全部" value="-1"></el-option>
                <el-option
                  v-for="item in sheetTypeList"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <!--客户所在地-->
            <el-form-item class="form-item" label="客户所在地" prop="customerSource">
              <el-select v-model="listQuery.customerSource" placeholder="请选择">
                <el-option label="全部" value="-1"></el-option>
                <el-option
                  v-for="item in workLocation"
                  :key="item.id"
                  :label="item.sourceName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row :gutter="10" type="flex" class="row-bg" justify="space-between">
            <el-form-item label="问题分类" prop="problemClassification">
              <el-cascader
                :key="isResouceShow"
                v-model="listQuery.problemClassification"
                placeholder="请先选择工单类型"
                :options="options"
                :props="optionProps"
                clearable
                :show-all-levels="true"
                style="width: 330px;"
                :disabled="!showOn"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="工单编号" prop="workOrderNum" class="nodeClass">
              <el-input v-model.trim="listQuery.workOrderNum" maxlength="30" clearable></el-input>
            </el-form-item>
            <el-form-item class="form-item" label="当前状态" prop="currentState">
              <el-select
                v-model="listQuery.currentState"
                placeholder="请选择"
                multiple
                collapse-tags
                clearable
              >
                <el-option
                  v-for="item in stateList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row :gutter="10" type="flex" class="row-bg" justify="space-between">
            <!-- <el-form-item label="当前受理节点" prop="groups">
              <el-cascader
                v-model="listQuery.groups"
                placeholder="请选择节点"
                :options="options"
                :props="optionProps"
                clearable
                :show-all-levels="true"
                style="width: 300px;"
              ></el-cascader>
            </el-form-item>-->
            <el-form-item class="form-item" label="是否超时" prop="timeout">
              <el-select v-model="listQuery.timeout" placeholder="请选择">
                <el-option label="全部" value="-1"></el-option>
                <el-option
                  v-for="item in timeoutList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item style="text-align:right;float:right">
              <el-button
                plain
                type="primary"
                size="small"
                class="searchCondition"
                @click="handerSearch('listQuery')"
              >查询</el-button>
              <el-button plain size="small" @click="resetForm">重置</el-button>
              <el-button class="export-excel" @click="exportData">导出</el-button>
            </el-form-item>
          </el-row>
        </el-form>
      </template>
      <template slot="body">
        <xyy-table
          v-loading="tabLoading"
          :data="list"
          :col="col"
          :list-query="queryList"
          :operation="operation"
          :is-pagination="true"
          :is-stripe="false"
          @operation-click="operationClick"
          @get-data="handerSearch('listQuery')"
        >
          <template slot="workOrderNum" slot-scope="{col}">
            <el-table-column :key="col.index" :label="col.name" :width="col.width">
              <template slot-scope="{row}">
                <el-link :underline="false" @click="toWorkOrder(row)">
                  <span class="work-order-num">{{ row.workOrderNum}}</span>
                </el-link>
              </template>
            </el-table-column>
          </template>
        </xyy-table>
      </template>
    </xyy-list-page>

    <el-dialog
      :visible.sync="dialogVisible"
      custom-class="codeDialog"
      :show-close="true"
      title="节点信息"
      class="dialogInner"
      width="80%"
    >
      <xyy-table :data="listNode" :col="colNode" :is-pagination="true" :is-stripe="false">
        <template slot="unclaimedTime" slot-scope="{ col }">
          <el-table-column
            :key="col.index"
            :prop="col.index"
            :label="col.name"
            :width="col.width"
            :formatter="getFormatDuration"
          />
        </template>
      </xyy-table>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </span>-->
    </el-dialog>

    <expor-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    ></expor-tip>
  </div>
</template>

<script>
import {
  getCustomerSourceList,
  getWorkorderTypeList,
  listWorkOrderInfoReport,
  getWorkOrderInfoExport,
  getNodeInfoByWorkOrderId,
  getAllTree
} from '@/api/report/index';
import utils from '@/utils/filter';
import exporTip from '@/views/work-sheet/components/exportTip';
export default {
  name: 'workOrderInformation',
  components: {
    exporTip
  },
  data() {
    return {
      tabLoading: false,
      loading: false,
      queryList: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      list: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0,
        dataRange: [new Date(new Date().setHours(0, 0, 0, 0)), new Date()], // 日期
        formTypeId: '-1', // 工单类型
        customerSource: '-1', // 客户所在地,
        workOrderNum: '', // 工单编号
        problemClassification: [], // 问题分类
        currentState: [], // 当前状态
        timeout: '-1' // 超时状态
      },
      listNode: [],
      created: false,
      sheetTypeList: [],
      workLocation: [], // 客户所在地
      options: [], // 问题分类报表
      optionProps: {
        value: 'typeCode',
        label: 'typeName',
        checkStrictly: true
      }, // 字段适配
      dialogVisible: false,
      currentUrl: '',
      showOn: false,
      isResouceShow: 0,
      changeExport: false,
      stateList: [
        { id: 0, name: '待领取' },
        { id: 1, name: '处理中' },
        { id: 2, name: '完结' },
        { id: 3, name: '退回' },
        { id: 4, name: '异常' }
        // { id: 5, name: '作废' }
      ],
      timeoutList: [
        { id: 0, name: '未超时' },
        { id: 1, name: '已超时' },
        { id: 2, name: '即将超时' }
      ],
      col: [
        {
          index: 'workOrderNum',
          name: '工单编号',
          width: 180,
          slot: true
        },
        {
          index: 'typeName',
          name: '问题分类',
          width: 150,
          resizable: true
        },
        {
          index: 'currentState',
          name: '当前状态',
          width: 130,
          resizable: true
        },
        {
          index: 'currentAcceptNode',
          name: '当前受理节点',
          width: 130,
          resizable: true
        },
        {
          index: 'currentAcceptUser',
          name: '当前受理人',
          width: 150,
          resizable: true
        },
        {
          index: 'userGroup',
          name: '用户组',
          width: 150,
          resizable: true
        },
        {
          index: 'startTime',
          name: '发起时间',
          width: 200,
          resizable: true
        },
        {
          index: 'completeTime',
          name: '完成时间',
          width: 200,
          resizable: true
        },
        {
          index: 'processTime',
          name: '已处理时长(h)',
          width: 140,
          resizable: true
        },
        {
          index: 'timeoutState',
          name: '是否超时',
          width: 120,
          resizable: true
        },
        {
          index: 'timeoutProcessTime',
          name: '超时处理时长(h)',
          width: 140,
          resizable: true
        },
        {
          index: 'remainingProcessTime',
          name: '剩余处理时长(h)',
          width: 140,
          resizable: true
        },
        {
          index: 'afterSaleAcceptUser',
          name: '工单售后受理人',
          width: 150,
          resizable: true
        },
        {
          index: 'operation',
          name: '操作',
          width: 140,
          operation: true,
          resizable: true
        }
      ],
      operation: [
        {
          name: '查看节点',
          type: 0
        }
      ],
      colNode: [
        {
          index: 'nodeName',
          name: '节点信息',
          width: 150,
          resizable: true
        },
        {
          index: 'unclaimedTime',
          name: '待领取时长',
          width: 150,
          slot: true
        },
        {
          index: 'receiveTime',
          name: '转入时间',
          width: 200,
          resizable: true
        },
        {
          index: 'claimTime',
          name: '领取时间',
          width: 200,
          resizable: true
        },
        {
          index: 'startTime',
          name: '受理时间',
          width: 200,
          resizable: true
        },
        {
          index: 'outTime',
          name: '转出时间',
          width: 200,
          resizable: true
        },
        {
          index: 'processorName',
          name: '受理人',
          width: 150,
          resizable: true
        },
        {
          index: 'processTime',
          name: '已处理时长(h)',
          width: 140,
          resizable: true
        },
        {
          index: 'timeoutState',
          name: '是否超时',
          width: 120,
          resizable: true
        },
        {
          index: 'timeoutTime',
          name: '超时处理时长(h)',
          width: 140,
          resizable: true
        },
        {
          index: 'remainingTime',
          name: '剩余处理时长(h)',
          width: 140,
          resizable: true
        }
      ],
      rules: {
        dataRange: [
          { required: true, message: '日期为必填项', trigger: 'blur' }
        ]
      },
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.minDate = minDate;
          this.maxDate = maxDate;
        },
        disabledDate: time => {
          const curDate = new Date().getTime();
          const two = 365 * 2 * 24 * 3600 * 1000;
          const twoyear = curDate - two;
          const three = 92 * 24 * 3600 * 1000;
          if (this.minDate) {
            return (
              time.getTime() > Date.now() ||
              time.getTime() < twoyear ||
              time > new Date(this.minDate.getTime() + three) ||
              time < new Date(this.minDate.getTime() - three)
            );
          }
          return time.getTime() > Date.now() || time.getTime() < twoyear;
        }
      }
    };
  },
  created() {
    this.getNowTimeDate();
    this.getWorkorderTypeList();
    this.getCustomerSourceList();
    this.created = true;
  },
  activated() {
    this.currentUrl = this.$route.path;
    this.created = false;
  },
  mounted() {
    this.getParams();
  },
  methods: {
    getParams() {
      this.listQuery.formTypeId = this.$route.query.formTypeId
        ? this.$route.query.formTypeId
        : '-1';

      if (this.$route.query.problemClassification) {
        this.listQuery.problemClassification = this.$route.query.problemClassification.split(
          ','
        );
      } else {
        this.listQuery.problemClassification = [];
      }

      this.listQuery.timeout = this.$route.query.timeout
        ? this.$route.query.timeout
        : '-1';

      if (this.$route.query.currentState) {
        this.listQuery.currentState = [0, 1];
      } else {
        this.listQuery.currentState = [];
      }

      if (this.$route.query.dataRange) {
        this.listQuery.dataRange = this.$route.query.dataRange.split(',');
      }

      this.listQuery.customerSource = this.$route.query.customerSource
        ? this.$route.query.customerSource
        : '-1';
      this.selectEvents(this.listQuery.formTypeId, true);
      this.getList(this.queryList);
    },
    getList: function(queryList) {
      const { page, pageSize } = queryList;
      let param = {
        pageNum: page,
        pageSize: pageSize,
        endTime: this.listQuery.dataRange[1],
        startTime: this.listQuery.dataRange[0],
        formTypeId: this.listQuery.formTypeId,
        customerSource: this.listQuery.customerSource,
        workOrderNum: this.listQuery.workOrderNum,
        timeout: this.listQuery.timeout
        //  problemClassification: this.listQuery.problemClassification[
        //   this.listQuery.problemClassification.length - 1
        // ],
        //   currentState: this.listQuery.currentState,
      };
      if (
        this.listQuery.problemClassification &&
        this.listQuery.problemClassification.length > 0
      ) {
        param.problemClassification = this.listQuery.problemClassification.join(
          ','
        );
      }
      if (
        this.listQuery.currentState &&
        this.listQuery.currentState.length > 0
      ) {
        param.currentState = this.listQuery.currentState.join(',');
      }

      this.tabLoading = true;
      listWorkOrderInfoReport(param)
        .then(res => {
          this.tabLoading = false;
          const { total } = res.data;
          this.list = res.data.list;

          let startTime = {};
          let completeTime = {};
          let currentState = {};
          let timeoutState = {};
          this.list.forEach((item, index) => {
            startTime = {};
            completeTime = {};
            currentState = {};
            timeoutState = {};
            if (item.startTime == null) {
              startTime = '-';
            } else {
              startTime = utils.dataTime(item.startTime, 'yy-mm-dd HH:ss:nn');
            }
            if (item.completeTime == null) {
              completeTime = '-';
            } else {
              completeTime = utils.dataTime(
                item.completeTime,
                'yy-mm-dd HH:ss:nn'
              );
            }
            if (item.currentState == 0) {
              currentState = '待领取';
            } else if (item.currentState == 1) {
              currentState = '处理中';
            } else if (item.currentState == 2) {
              currentState = '完结';
            } else if (item.currentState == 3) {
              currentState = '退回';
            } else if (item.currentState == 4) {
              currentState = '异常';
            } else {
              currentState = '-';
            }
            //  else if (item.currentState == 5) {
            //   currentState = '作废';
            // }
            if (item.timeoutState == 0) {
              timeoutState = '未超时';
            } else if (item.timeoutState == 1) {
              timeoutState = '已超时';
            } else if (item.timeoutState == 2) {
              timeoutState = '即将超时';
            } else {
              timeoutState = '-';
            }

            this.list[index] = Object.assign({}, this.list[index], {
              startTime: startTime,
              completeTime: completeTime,
              currentState: currentState,
              timeoutState: timeoutState
            });
          });

          this.queryList = {
            ...this.queryList,
            page: Number(res.data.pageNum),
            pageSize: Number(res.data.size),
            total: Number(res.data.total)
          };
        })
        .catch(() => {
          this.tabLoading = false;
        });
    },
    // 导出
    handleChangeExport(info) {
      const that = this;
      this.changeExport = false;
      if (info === 'go') {
        that.$router.push({
          path: '/other/index/'
        });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    exportData() {
      const that = this;
      if (!that.list || !that.list.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      let param = {
        endTime: this.listQuery.dataRange[1],
        startTime: this.listQuery.dataRange[0],
        formTypeId: this.listQuery.formTypeId,
        customerSource: this.listQuery.customerSource,
        workOrderNum: this.listQuery.workOrderNum,
        timeout: this.listQuery.timeout
        //  problemClassification: this.listQuery.problemClassification[
        //   this.listQuery.problemClassification.length - 1
        // ],
        // currentState: this.listQuery.currentState,
      };
      if (
        this.listQuery.problemClassification &&
        this.listQuery.problemClassification.length > 0
      ) {
        param.problemClassification = this.listQuery.problemClassification.join(
          ','
        );
      }
      if (
        this.listQuery.currentState &&
        this.listQuery.currentState.length > 0
      ) {
        param.currentState = this.listQuery.currentState.join(',');
      }
      let url = ``;
      getWorkOrderInfoExport(param).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.changeExport = true;
        // if (res.hasOwnProperty('code')) {
        //   that.$XyyMessage.error(res.msg);
        // } else {
        //   url = `${process.env.BASE_API}/workOrderInfo/getWorkOrderInfoExport?endTime=${that.listQuery.dataRange[1]}&startTime=${that.listQuery.dataRange[0]}&formTypeId=${that.listQuery.formTypeId}&customerSource=${that.listQuery.customerSource}&workOrderNum=${that.listQuery.workOrderNum}&currentState=${that.listQuery.currentState}&timeout=${that.listQuery.timeout}`;
        //   if (
        //     this.listQuery.problemClassification != null &&
        //     this.listQuery.problemClassification.length > 0
        //   ) {
        //     this.url =
        //       this.url +
        //       `&problemClassification=${
        //         this.listQuery.problemClassification[
        //           this.listQuery.problemClassification.length - 1
        //         ]
        //       }`;
        //   } else {
        //     this.url = this.url + `&problemClassification=-1`;
        //   }
        //   if (
        //     this.$store.getters.channel &&
        //     this.$store.getters.channel.businessPartCode
        //   ) {
        //     url =
        //       url +
        //       `&businessPartCode=${this.$store.getters.channel.businessPartCode}`;
        //   }
        //   const a = document.createElement('a');
        //   a.href = url;
        //   a.click();
        // }
      });
    },
    handerSearch(formName) {
      this.getList(this.queryList);
    },
    // 时间格式化
    getFormatDate: function(row, column, cellValue, index) {
      return new Date(cellValue + 8 * 3600 * 1000)
        .toJSON()
        .substr(0, 19)
        .replace('T', ' ');
    },
    // 当前时间
    getNowTimeDate() {
      const time = new Date(8 * 3600 * 1000 + +new Date())
        .toJSON()
        .substr(0, 10);
      this.listQuery.dataRange = [time, time];
    },
    getFormatDuration(row, column, cellValue, index) {
      // 计算天
      var Day = Math.floor(cellValue / (24 * 3600 * 1000));
      //计算出小时数
      var leave1 = cellValue % (24 * 3600 * 1000);
      var hours = Math.floor(leave1 / (3600 * 1000));
      //计算分钟数
      var leave2 = leave1 % (3600 * 1000);
      var minutes = Math.floor(leave2 / (60 * 1000));
      //计算秒数
      var leave3 = leave2 % (60 * 1000);
      var seconds = Math.round(leave3 / 1000);

      const D = Day === 0 ? '' : Day + '天';
      const h = hours === 0 ? '' : hours + '小时';
      const m = minutes === 0 ? '' : minutes + '分钟';
      const s = seconds === 0 ? '' : seconds + '秒';
      return D + h + m + s;
    },
    // 获取工单类型
    getWorkorderTypeList() {
      getWorkorderTypeList()
        .then(res => {
          if (res.code !== 1) {
            this.$XyyMessage.warning(res.msg);
            return;
          }
          this.sheetTypeList = res.data;
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 获取客户所在地
    getCustomerSourceList() {
      getCustomerSourceList().then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.workLocation = res.data;
      });
    },
    // 搜索条件选择角色时
    selectEvents(val, isselect) {
      if (!isselect) {
        this.listQuery.problemClassification = [];
      }
      // this.options = [];
      if (val !== '-1') {
        this.showOn = true;
        this.getAllTree();
      } else {
        this.showOn = false;
      }
    },
    // 获取问题分类列表树
    getAllTree() {
      const param = {
        formTypeId: this.listQuery.formTypeId
      };
      ++this.isResouceShow;
      getAllTree(param).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.options = res.data;
      });
    },
    operationClick: function(type, row) {
      switch (type) {
        case 0:
          this.toNode(row); // 节点
          break;
      }
    },
    /**
     * 打开节点
     */
    toNode(row) {
      this.dialogVisible = true;
      const param = {
        workOrderId: row.workOrderId
      };
      getNodeInfoByWorkOrderId(param).then(res => {
        if (res.code === 1) {
          this.listNode = res.data;
          let receiveTime = {};
          let startTime = {};
          let claimTime = {};
          let outTime = {};
          let timeoutState = {};
          this.listNode.forEach((item, index) => {
            receiveTime = {};
            startTime = {};
            claimTime = {};
            outTime = {};
            timeoutState = {};
            if (item.receiveTime == null) {
              receiveTime = '-';
            } else {
              receiveTime = utils.dataTime(
                item.receiveTime,
                'yy-mm-dd HH:ss:nn'
              );
            }
            if (item.startTime == null) {
              startTime = '-';
            } else {
              startTime = utils.dataTime(item.startTime, 'yy-mm-dd HH:ss:nn');
            }
            if (item.claimTime == null) {
              claimTime = '-';
            } else {
              claimTime = utils.dataTime(item.claimTime, 'yy-mm-dd HH:ss:nn');
            }
            if (item.outTime == null) {
              outTime = '-';
            } else {
              outTime = utils.dataTime(item.outTime, 'yy-mm-dd HH:ss:nn');
            }
            if (item.timeoutState == 0) {
              timeoutState = '未超时';
            } else if (item.timeoutState == 1) {
              timeoutState = '已超时';
            } else if (item.timeoutState == 2) {
              timeoutState = '即将超时';
            } else {
              timeoutState = '-';
            }

            this.listNode[index] = Object.assign({}, this.listNode[index], {
              id: index + 1
            });

            this.listNode[index] = Object.assign({}, this.listNode[index], {
              receiveTime: receiveTime,
              startTime: startTime,
              claimTime: claimTime,
              outTime: outTime,
              timeoutState: timeoutState
            });
          });
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 跳转工单详情
    toWorkOrder(row) {
      this.$router.push({
        path:
          '/workStatus' + row.formTypeId + '/sheetDetail/' + row.workOrderId,
        query: {
          id: row.workOrderId,
          type: 'wordSheet',
          currentUrl: this.currentUrl
        }
      });
    },
    dateTimeFocus() {
      this.minDate = undefined;
      // 条件时间查询
      const that = this;
      that.$nextTick(function() {
        document
          .getElementsByClassName('el-picker-panel')[0]
          .addEventListener('click', function() {});
      });
    },
    resetForm() {
      this.showOn = false;
      this.queryList = {
        page: 1,
        pageSize: 10,
        total: 0
      };
      this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0,
        dataRange: [new Date(new Date().setHours(0, 0, 0, 0)), new Date()], // 日期
        formTypeId: '-1', // 工单类型
        customerSource: '-1', // 客户所在地,
        workOrderNum: '', // 工单编号
        problemClassification: [], // 问题分类
        currentState: [], // 当前状态
        timeout: '-1' // 超时状态
      };
      this.getNowTimeDate();
      this.getList(this.queryList);
    }
  }
};
</script>

<style scoped lang="scss">
// /deep/.el-table {
//   td.is-hidden {
//     .cell {
//       visibility: inherit !important;
//     }
//   }
// }
// /deep/.el-table {
//   .el-table__fixed {
//     z-index: 1;
//   }
//   .el-table__body-wrapper {
//     z-index: 1;
//   }
//   .el-table__fixed-body-wrapper {
//     top: 65px !important;
//   }
// }

// /deep/.el-table th {
//   padding-top: 10px;
//   padding-bottom: 10px;
//   text-align: left;
//   padding-right: 19px;
//   &:first-child {
//     text-align: left;
//   }
//   p {
//     margin: 0;
//     height: 20px;
//   }
//   small {
//     color: #909399;
//     font-size: 12px;
//     height: 17px;
//   }
// }

// /deep/.el-table tr td .cell {
//   height: 100%;
//   line-height: 25px;
//   overflow-x: hidden;
//   text-overflow: ellipsis;
//   word-wrap: break-word;
//   word-break: normal;
//   display: flex;
//   flex-direction: column;
//   align-items: flex-end;
//   padding-left: 10px;
//   padding-right: 10px;
//   white-space: normal;
//   display: block;
//   text-align: center;
// }

.work-order-num {
  color: #575766;
  font-size: 14px;
  font-weight: 500;
  background: rgba(0, 0, 0, 0);
  color: rgb(1, 91, 109);
}

/deep/.dialogInner {
  .el-table {
    overflow-y: scroll;
    max-height: 460px;
  }
  .el-table th:nth-child(8) > .cell {
    color: #fb2929;
  }
  .el-table th:nth-child(10) > .cell {
    color: #fb2929;
  }
  .el-table th:nth-child(11) > .cell {
    color: #fb2929;
  }
}

/deep/.el-form-item__label {
  font-weight: 500;
}

/deep/.page-header {
  padding-bottom: 0 !important;
}
.search-form {
  border-bottom: 1px dashed #e4e4eb;
}

.herader-title {
  font-size: 14px;
  color: #393943;
  padding-bottom: 20px;
  border-bottom: 1px dashed #e4e4eb;
  margin-bottom: 20px;
}
/deep/.el-input__inner {
  height: 36px !important;
  line-height: 36px !important;
}
/deep/.el-button {
  height: 36px;
}
/deep/.el-table__body tr td {
  border-bottom: 1px solid #ebeef5;
  background: #fff;
}
.searchCondition.is-plain {
  background: rgba(59, 149, 168, 1);
  color: #fff;
}
/deep/.searchCondition.is-plain:hover {
  background: rgba(40, 126, 144, 1);
}

/deep/.codeDialog {
  .qrtips {
    padding: 0 20px 30px;
    font-size: 16px;
  }

  #avatar {
    margin: 0 20px;
    width: 160px;
    height: 160px;
  }

  #qrcode {
    width: 160px;
    height: 160px;
    display: inline-block;
    /deep/ img {
      margin: 0 auto;
    }
  }
}
</style>

