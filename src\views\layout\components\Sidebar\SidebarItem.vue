<template>
  <div v-if="!item.hidden" class="menu-wrapper">
    <template
      v-if="hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow"
    >
      <app-link v-show="!(item.path.indexOf('message')>=0)" :to="resolvePath(onlyOneChild.path)">
        <el-menu-item
          :index="resolvePath(onlyOneChild.path)"
          :class="{'submenu-title-noDropdown':!isNest,'submenu-title-only-child':!onlyOneChild.noShowingChildren&&$route.path == resolvePath(onlyOneChild.path)}"
        >
          <div
            v-if="onlyOneChild.noShowingChildren"
            :class="{'is-active-sub':$route.path == resolvePath(onlyOneChild.path)}"
            :style="{'float':'left','background': 'none','margin-right':'20px',width:'2px','height': '100%'}"
          ></div>
          <item
            :meta="Object.assign({},item.meta,onlyOneChild.meta)"
            :is-hover="mainPath==item.path"
          />
        </el-menu-item>
      </app-link>
    </template>
    <el-submenu v-else ref="subMenu" :index="resolvePath(item.path)" popper-append-to-body>
      <template slot="title">
        <item :meta="item.meta" :is-hover="mainPath==item.path" />
      </template>
      <sidebar-item
        v-for="child in item.children"
        :is-nest="true"
        :item="child"
        :key="child.path"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path';
import { isExternal } from '@/utils/validate';
import variables from '@/styles/variables.scss';
import Item from './Item';
import AppLink from './Link';

export default {
  name: 'SidebarItem',
  components: { Item, AppLink },
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  data() {
    // To fix https://github.com/PanJiaChen/vue-admin-template/issues/237
    // TODO: refactor with render function
    this.onlyOneChild = null;
    return {};
  },
  computed: {
    variables() {
      return variables;
    },
    mainPath() {
      return '/' + this.$route.path.split('/')[1];
    }
  },
  methods: {
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false;
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item;
          return true;
        }
      });

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true;
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true };
        return true;
      }

      return false;
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath;
      }
      return path.resolve(this.basePath, routePath);
    }
  }
};
</script>

<style lang="scss" scoped>
.svg-icon {
  width: 18px !important;
  height: 18px !important;
  vertical-align: middle !important;
}
@import '@/styles/variables.scss';
.menu-wrapper {
  .submenu-title-only-child.is-active {
    //background: $menuActive !important;
    background: #f5f7fa !important;
  }
  .is-active-sub {
    position: relative;
    .is-active-sub-active {
      position: absolute;
      top: 20%;
      width: 3px;
      height: 60%;
      background: $subMenuActiveText;
    }
  }
  .el-menu-item.is-active {
    font-weight: 500;
  }
  .el-menu-item,
  /deep/.el-submenu__title {
    height: 36px !important;
    line-height: 36px !important;
    /*margin-top:2px;*/
    /*margin-bottom:2px;*/
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .nest-menu li {
    padding: 0 !important;
    padding-left: 10px;
    color: #909399 !important;
    /*font-weight:normal !important;*/
    div {
      margin-right: 0 !important;
      margin-left: 30px;
    }
  }
  .nest-menu li.is-active {
    color: $menuActiveText !important;
    font-weight: normal !important;
    background-color: #f5f7fa !important;
  }
}

/* .el-submenu.is-active i {
  color: red;
  font-weight: bold;
} */
</style>
