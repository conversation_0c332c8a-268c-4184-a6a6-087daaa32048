<template>
  <div>
    <el-dialog
      :title="promptAialog"
      :visible="panelShow"
      custom-class="tableTreeInner"
      width="850px"
      @close="closeDialog"
    >
      <div class="tableTree">
        <div class="tableTreeRight tableTreeTitle">
          <span>
            <div style="padding-left: 16px;">问题分类</div>
          </span>
          <span
            ref="tableTreeTwoInner"
            :style="{width: tableTreeTwoInner}"
            class="tableTreeTwoInner"
          >
            <span class="tableTreeTwoSpan1">表单模板</span>
            <span class="tableTreeTwoSpan2" style="text-align: right;padding-right: 132px;">操作</span>
          </span>
        </div>
        <div class="tableTreeBody">
          <div ref="treeContainerInner" class="tree-container">
            <el-tree
              :data="dataListNewList"
              :indent="0"
              :expand-on-click-node="false"
              node-key="id"
              default-expand-all
            >
              <el-row
                class="custom-tree-node"
                slot-scope="{ node, data }"
                style="width:calc(100% - 40px)"
              >
                <div
                  class="tableTreeRight"
                  ref="tableTreeRight"
                  style="justify-content: flex-start;"
                >
                  <span
                    style="flex-grow:1;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"
                    :title="data.typeName"
                  >{{ data.typeName }}</span>
                  <!--<span>{{ data.templateCode }}</span>-->
                  <span
                    :style="{width: tableTreeTwoInner}"
                    ref="tableTreeTwoInner"
                    class="tableTreeTwoInner"
                    style="flex-shrink:0"
                  >
                    <span
                      class="tableTreeTwoSpan1"
                      style="padding-left: 8px;"
                    >{{ data.templateName }}</span>
                    <span class="tableTreeTwoSpan2" style="text-align: right;">
                      <el-button type="text" size="mini" @click="() => handleAdd(node, data)">添加下一级</el-button>
                      <span class="vertical"></span>
                      <el-button type="text" size="mini" @click="() => handleEdit(node, data)">编辑</el-button>
                      <span class="vertical"></span>
                      <el-button type="text" size="mini" @click="() => handleDelete(node, data)">删除</el-button>
                    </span>
                  </span>
                </div>
              </el-row>
            </el-tree>
            <div class="addLevel">
              <span class="addLevelList" @click="handleAdd">
                <span class="el-icon-plus"></span>添加一级选项
              </span>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handerCancel">取 消</el-button>
        <el-button type="primary" @click="checkTimer(handerSave,'timer')()">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 修改下一级弹框-开始-->
    <el-dialog title="编辑问题分类" :visible.sync="dialogFormAddInner" custom-class="elDialog">
      <el-form ref="formAdd" :model="formAdd" label-width="80px" :rules="addRules">
        <el-form-item label="选项名称" prop="typeName">
          <el-input v-model="formAdd.typeName" maxlength="30"></el-input>
        </el-form-item>
        <el-form-item label="模板" prop="templateName">
          <el-select
            v-model="formAdd.templateName"
            filterable
            placeholder
            :clearable="true"
            @change="handelModifyChange"
          >
            <el-option
              v-for="item in repairOrderTemp"
              :key="item.id"
              :label="item.name"
              :value="item.versionCode"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addCancel">取 消</el-button>
        <el-button type="primary" @click="addDetermine('formAdd')">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 修改下一级弹框-结束-->
    <!-- 添加问题分类弹框-开始-->
    <el-dialog title="添加问题分类" :visible.sync="dialogFormVisibleInner" custom-class="elDialog">
      <el-form ref="formAditorInner" :model="formAditorInner" label-width="80px" :rules="addRules">
        <el-form-item label="选项名称" prop="typeName">
          <el-input v-model.trim="formAditorInner.typeName" maxlength="30"></el-input>
        </el-form-item>
        <el-form-item label="模板" prop="templateCode">
          <el-select
            v-model="formAditorInner.templateCode"
            filterable
            placeholder
            :clearable="true"
            @clear="handerChange"
            @change="handelAddChange"
          >
            <el-option
              v-for="item in repairOrderTemp"
              :key="item.id"
              :label="item.name"
              :value="item.versionCode"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editorCancel">取 消</el-button>
        <el-button type="primary" @click="editorDetermine('formAditorInner')">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 添加问题分类弹框-结束-->
  </div>
</template>

<script>
import {
  getTemplate,
  deleteSaved,
  getProblemClassTree
} from '@/api/QClassification/index';
export default {
  props: {
    dataListNewList: {
      type: Array
    },
    panelShow: {
      type: Boolean
    },
    promptAialog: {
      type: String
    }
  },
  data() {
    return {
      tableTreeTwoInner: '',
      dialogFormAddInner: false,
      dialogFormVisibleInner: false,
      formAdd: {
        typeName: '',
        templateName: '',
        templateCode: ''
      },
      formAditorInner: {
        // 编辑问题表单
        typeName: '', // 选项名称
        templateName: '', // 模板
        templateCode: ''
      },
      // 编辑问题分类 校验
      addRules: {
        typeName: [
          { required: true, message: '选项名称为必填项', trigger: 'blur' }
        ]
      },
      repairOrderTemp: [], // 工单模板下拉
      timer: null
    };
  },
  created() {
    this.getWidthInner();
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        const treeContainerInner = this.$refs.treeContainerInner.clientWidth; // 最外边的长度
        this.tableTreeTwoInner = (treeContainerInner / 4) * 2 + 'px';
      })();
    };
  },
  methods: {
    //工单类型改变事件--结束
    // ------------------------------------添加下一级事件和一级选项------------------
    //          添加下一级清空事件
    handerChange() {
      this.formAditorInner.templateName = '';
      this.formAditorInner.templateCode = '';
    },
    // 添加下一级事件
    handleAdd(node, data) {
      this.currentRow = [];
      getTemplate().then(res => {
        this.repairOrderTemp = res.data;
      });
      if (data === undefined) {
        this.formAditorInner.typeName = '';
        this.formAditorInner.templateName = '';
        this.formAditorInner.templateCode = '';
      } else {
        // 添加下一级
        this.formAditorInner.typeName = '';
        this.formAditorInner.templateName = '';
        this.formAditorInner.templateCode = '';
        this.currentRow = data;
      }
      this.dialogFormVisibleInner = true;
    },
    // 添加下一级change事件
    handelAddChange(val) {
      let obj = {};
      obj = this.repairOrderTemp.find(item => {
        // 这里的userList就是上面遍历的数据源
        return item.versionCode === val; // 筛选出匹配数据
      });
      this.formAditorInner.templateName = obj.name;
      this.formAditorInner.templateCode = obj.versionCode;
    },
    // 添加下一级确定事件
    editorDetermine(name) {
      const that = this;
      this.$refs[name].validate(valid => {
        if (valid) {
          if (this.currentRow.length === 0) {
            const obj = {
              codeEach: that.codeEach++,
              typeName: that.formAditorInner.typeName,
              templateCode: that.formAditorInner.templateCode,
              templateName: that.formAditorInner.templateName,
              deleted: 0,
              children: []
            };
            this.dataListNewList.push(obj);
          } else {
            const obj = {
              codeEach: that.codeEach++,
              typeName: that.formAditorInner.typeName,
              templateCode: that.formAditorInner.templateCode,
              templateName: that.formAditorInner.templateName,
              deleted: 0,
              children: []
            };
            // 判断data是否是数组；
            if (!this.currentRow.children) {
              this.$set(this.currentRow, 'children', []);
            }
            // this.currentRow 是传递给后台的数据
            this.currentRow.children.push(obj);
          }
          this.dialogFormVisibleInner = false;
        }
      });
    },
    // 添加下一级取消事件
    editorCancel() {
      this.dialogFormVisibleInner = false;
    },

    // -----------------------修改事件

    // 修改事件
    handleEdit(node, data) {
      this.formAdd.typeName = data.typeName;
      this.formAdd.templateName = data.templateName;
      this.formAdd.templateCode = data.templateCode;
      this.dialogFormAddInner = true;
      getTemplate().then(res => {
        this.repairOrderTemp = res.data;
      });
      this.currentRowModify = data;
    },
    // 修改下一级change事件
    handelModifyChange(val) {
      let obj = {};
      obj = this.repairOrderTemp.find(item => {
        // 这里的userList就是上面遍历的数据源
        return item.versionCode === val; // 筛选出匹配数据
      });
      this.formAdd.templateName = obj.name;
      this.formAdd.templateCode = obj.versionCode;
    },
    // 修改弹框确定事件
    addDetermine(name) {
      this.$refs[name].validate(valid => {
        if (valid) {
          this.dialogFormAddInner = false;
          this.$set(this.currentRowModify, 'typeName', this.formAdd.typeName);
          this.$set(
            this.currentRowModify,
            'templateName',
            this.formAdd.templateName
          );
          this.$set(
            this.currentRowModify,
            'templateCode',
            this.formAdd.templateCode
          );
          // Object.assign(this.currentRowModify, {
          //   typeName: this.formAdd.typeName,
          //   templateName: this.formAdd.templateName,
          //   templateCode: this.formAdd.templateCode
          // });
        }
      });
    },
    // 修改弹框取消事件
    addCancel() {
      this.dialogFormAddInner = false;
    },
    // 删除事件
    handleDelete(node, data) {
      //            data.typeCode === undefined
      if (node.parent.data.length === 1) {
        this.$message('最后一条数据不可被删除！');
        return;
      }
      const parent = node.parent;
      const children = parent.data.children || parent.data;
      const index = children.findIndex(d => d.id === data.id);
      children.splice(index, 1);
    },
    // ---------------------------------------取消保存事件-开始
    //          弹框关闭事件
    closeDialog() {
      this.$emit('setValue', false); // 关闭弹框的传值
    },
    // 取消事件
    handerCancel() {
      this.$emit('setValue', false); // 关闭弹框的传值
    },
    // 保存事件
    handerSave() {
      const that = this;
      if (that.dataListNewList.length === 0) {
        that.$alert('问题分类不能为空。保存失败）', '提示', {
          confirmButtonText: '确定',
          callback: action => {}
        });
      } else {
        //              保存事件
        deleteSaved(
          that.dataListNewList,
          sessionStorage.getItem('treeIndex')
        ).then(res => {
          if (res.data === true) {
            that.$message('保存成功');
            //                刷新列表数据
            this.$emit('setValue', false); // 关闭弹框的传值
            this.$emit('add-comment');
            //                 关闭弹框重新加载数据
            const params = {
              formTypeId: sessionStorage.getItem('treeIndex')
            };

            getProblemClassTree(params).then(res => {
              that.$emit('passValue', res);
            });
          } else {
            that.$message('保存失败');
          }
        });
      }
    },
    // ---------------------------------------取消保存事件-开结束

    getWidthInner() {
      this.$nextTick(() => {
        const treeContainerInner = this.$refs.treeContainerInner.clientWidth; // 最外边的长度
        console.log(treeContainerInner);
        this.tableTreeTwoInner = (treeContainerInner / 4) * 2 + 'px';
      });
    }
  }
};
</script>
<style lang="scss" scoped>
/deep/.tableTreeInner {
  /deep/.el-dialog__body {
    height: 400px;
    overflow-y: auto;
    padding-top: 10px !important;
  }
}
</style>
<style lang="scss">
.formTypeClass {
  /deep/.el-form-item__content {
    width: 200px;
  }
}
.temConfiguration {
  .page-header > span {
    display: inline-block;
  }
  .el-select {
    width: 100%;
  }
  .el-popover__reference {
    width: 18px;
  }
}
.popoverAll {
  p {
    color: rgba(144, 147, 153, 1);
    font-size: 12px;
    span {
      font-size: 14px;
      color: rgba(41, 41, 51, 1);
    }
  }
}
.tableTreeBody {
  .addLevel {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #ccc;
    padding-left: 16px;
    font-size: 14px;
    color: rgba(64, 163, 184, 1);
    .addLevelList {
      cursor: pointer;
    }
  }
}
.tree-container {
  height: auto;
  .el-row::before {
    display: contents !important;
  }
  .el-tree-node:focus > .el-tree-node__content {
    background-color: #fff !important;
  }
  .el-tree-node__content {
    height: 40px;
    &:visited {
      background: #fff !important;
    }
    &:active {
      background: #fff !important;
    }
    &:hover {
      background-color: #f5f7fa;
    }
  }
}
.tableTreeTitle {
  height: 40px;
  background: rgba(238, 238, 238, 1);
  line-height: 40px;
  font-size: 14px;
}
.tree-container {
  border: none !important;
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    font-size: 14px;
    padding-right: 8px;
    span {
      /*width: 30%;*/
      display: inline-block;
    }
  }
  .el-tree-node__expand-icon.expanded {
    transform: rotate(0deg);
  }
  .el-icon-caret-right:before {
    background: url('../../../assets/repeat/icon_open.svg') no-repeat;
    content: '';
    display: block;
    width: 18px;
    height: 18px;
    font-size: 18px;
    background-size: 18px;
  }
}
.tree-container /deep/ {
  .el-tree > .el-tree-node:after {
    border-top: none !important;
  }
  /** 迷之代码。。。*/
  // .el-tree-node__expand-icon {
  //   pointer-events: none;
  // }
  /** 迷之代码。。。*/

  .el-tree-node {
    position: relative;
    padding-left: 16px;
  }
  //节点有间隙，隐藏掉展开按钮就好了,如果觉得空隙没事可以删掉
  .el-tree-node__expand-icon.is-leaf:before {
    background: url('../../../assets/repeat/icon_close.png') no-repeat;
    content: '';
    display: block;
    width: 18px;
    height: 18px;
    font-size: 18px;
    background-size: 18px;
  }
  .el-tree-node__children {
    padding-left: 16px;
  }

  .el-tree-node :last-child:before {
    height: 33px;
    top: -16px;
  }

  .el-tree > .el-tree-node:before {
    border-left: none;
  }

  .el-tree > .el-tree-node:after {
    border-top: none;
  }

  .el-tree-node:before {
    content: '';
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:after {
    content: '';
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:before {
    border-left: 1px dashed rgba(228, 228, 235, 1);
    bottom: 0px;
    height: 79%;
    top: -13px;
    width: 1px;
  }

  .el-tree-node:after {
    border-top: 1px dashed rgba(228, 228, 235, 1);
    height: 20px;
    top: 19px;
    width: 24px;
  }
}
.tableTree {
  border: 1px solid rgba(228, 228, 235, 1);
  border-bottom: 0;
  margin-bottom: 45px;
  .tree-container {
    //树的parent，样式自定
    width: 100% !important;
    .el-tree-node {
      border-bottom: 1px solid rgba(228, 228, 235, 1);
      padding-bottom: 12px;
      .el-tree-node__children {
        .el-tree-node {
          border: 0 !important;
          padding-bottom: 0;
        }
      }
    }
  }
}
.DownloadTem {
  color: rgba(64, 163, 184, 1);
}
.tableTreeRight {
  width: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px !important;
  padding-right: 8px;
  .el-button--mini {
    font-size: 14px !important;
  }
}
.tableTreeTwoInner {
  span {
    display: inline-block;
  }
  .tableTreeTwoSpan1 {
    width: 55%;
    /*margin-right: 130px;*/
    @extend span;
  }
  .tableTreeTwoSpan2 {
    width: 41%;
    .vertical {
      display: inline-block;
      width: 1px;
      height: 12px;
      background: rgba(220, 223, 230, 1);
      margin: 0 6px;
    }
    @extend span;
  }
}
.temConfiguration {
  .el-tree-node > .el-tree-node__children {
    overflow: visible;
  }
  .elDialog {
    .el-dialog__header {
      padding-top: 15px;
    }
    .el-dialog__footer {
      padding-top: 8px;
    }
    .el-form-item {
      margin-bottom: 18px;
    }
    .el-button {
      height: 36px;
      line-height: 36px;
      padding-top: 0;
      padding-bottom: 0;
    }
    width: 502px;
    height: 224px;
    .el-dialog__body {
      padding: 0px 20px;
    }
    .el-input__inner {
      height: 36px;
    }
  }
}

.el-dialog.import-box {
  width: 400px;
  height: 240px;
  top: 50%;
  transform: translateY(-50%);
  .el-dialog__header {
    height: 50px;
    padding: 14px 20px;
    box-sizing: border-box;
    border-bottom: 1px solid rgba(238, 238, 238, 1);
    .el-dialog__title {
      height: 22px;
      font-size: 16px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: rgba(48, 49, 51, 1);
      line-height: 22px;
      float: left;
    }
    .el-dialog__headerbtn {
      top: 16px;
    }
  }
  .el-dialog__body {
    height: 190px;
    padding: 20px;
    position: relative;
    .import-content {
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(41, 41, 51, 1);
      a {
        color: #3b95a8;
      }
    }
    .import-result {
      margin: 0;
      padding: 0;
      overflow: hidden;
      dt {
        float: left;
        margin: 0 30px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(144, 147, 153, 1);
        &:first-child {
          margin-left: 0;
        }
        &:last-child {
          margin-right: 0;
        }
      }
    }
    > div {
      position: relative;
      height: 113px;
      .el-button {
        height: 36px;
        line-height: 36px;
        padding: 0 11px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        position: absolute;
        bottom: 0px;
        left: 50%;
        transform: translateX(-50%);
      }
      .el-upload-list {
        position: absolute;
        top: 18px;
        width: 100%;
        li {
          margin: 0;
          padding-top: 16px;
          height: 95px;
          .el-progress {
            height: 6px;
            top: 0px;
            .el-progress-bar {
              border-radius: 3px;
              height: 100%;
              width: calc(100% - 40px);
              float: left;
              .el-progress-bar__outer {
                height: 100% !important;
              }
            }
            .el-progress__text {
              top: 50%;
              transform: translateY(-50%);
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: rgba(144, 147, 153, 1);
            }
          }
          &:hover {
            background: none;
            .el-progress__text {
              display: inline;
            }
          }
          &:focus {
            outline: none;
          }
          span.file-name {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(144, 147, 153, 1);
          }
        }
      }
    }
    > .el-button {
      height: 36px;
      line-height: 36px;
      padding: 0 19px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      position: absolute;
      bottom: 20px;
      right: 20px;
    }
  }
}
</style>
<style scoped lang="scss">
.temConfiguration {
  .el-table__body-wrapper {
    button {
      color: #3b95a8;
      background: transparent;
      border: 0;
    }
  }
  .importClass {
    width: 74px;
    height: 36px;
    border: 1px solid rgba(228, 228, 235, 1) !important;
    color: rgba(87, 87, 102, 1) !important;
  }
}
.el-button.importClass.el-button--primary.is-plain:hover,
.el-button.importClass.el-button--primary.is-plain:focus {
  background: #fff;
  color: #40a3b8 !important;
  border: 1px solid !important;
}
.submitBtnTemplate {
  position: fixed;
  bottom: 0px;
  right: 0;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px -2px 2px 0px rgba(145, 144, 144, 0.11);
  filter: blur(0px);
  width: 100%;
  height: 57px;
  .submitBtnTemplate-1 {
    display: inline;
    position: absolute;
    right: 20px;
    top: 10px;
  }
}
</style>
