import request from '@/utils/request-im';

/* 初始加载未关闭对话 */
export function currentMessageList(data) {
  return request({
    url: '/chat/currentlist?pageId=' + data,
    method: 'get',
    data
  });
}

/* 初始加载未关闭对话 传用户id的 */
export function currentMessageListId(data) {
  return request({
    url: '/overall/dialoglistByKefuId?kefuId=' + data,
    method: 'get',
    data
  });
}

/* 根据会话id获取对话信息 */
export function dialogsMessageId(obj) {
  const url =
    '/overall/getmessage?kefuId=' + obj.kefuId + '&dialogid=' + obj.dialogid;
  return request({
    url: url,
    method: 'get'
    // params: data
  });
}

/* 初始加载未关闭对话 转接给指定客服 */
export function toMessageDialog(data) {
  return request({
    url:
      '/overall/dialogaccept?kefuId=' +
      data.kefuId +
      '&dialogid=' +
      data.dialogid,
    method: 'get',
    data
  });
}

/* 客服端读取消息 */
export function readMessageList(data) {
  return request({
    /*
    url:
      '/chat/readmessage?pageId=' +
      data.pageId +
      '&currentDialogId=' +
      data.currentDialogId +
      '&readVersionId=' +
      data.readVersionId +
      '&t=' +
      data.t,
    */
    url: `/chat/readmessage?pageId=${data.pageId}&currentDialogId=${
      data.currentDialogId
      }&readVersionId=${data.readVersionId}&t=${data.t}`, //&sign=${data.sign}
    method: 'get'
  });
}

/* 客服端历史会话列表 */
export function readHistoryMessageList(data) {
  return request({
    url: '/dialogs/kefu/history',
    method: 'get',
    params: data
  });
}

/* 根据会话id获取对话内容（不带会话信息） */
export function historyMessage(data) {
  const url = `/dialogs/${data.containerid}/messages`;
  return request({
    url: url,
    method: 'get'
    // params: data
  });
}

/* 根据会话id获取对话内容（不带会话信息） */
export function sayIngMessage(data) {
  const url = `/chat/historymessage/${data.containerid}`;
  return request({
    url: url,
    method: 'get'
    // params: data
  });
}

/* 根据会话id获取对话信息 */
export function dialogsDetail(data) {
  const url = `/dialogs/${data.containerid}/detail`;
  return request({
    url: url,
    method: 'get'
    // params: data
  });
}

/* 根据会话id获取留言对话信息 */
export function liuyanDialogsDetail(data) {
  const url = `/dialogs/dialog-detail-info/${data.containerid}`;
  return request({
    url: url,
    method: 'get'
    // params: data
  });
}

/* 客服发送消息 */
export function sendMessage(dialogid, data) {
  return request({
    url: '/chat/sendmessage/' + dialogid,
    method: 'put',
    data: data
  });
}

/* 初始加载未关闭会话 */
export function currentlist(dialogid, data) {
  return request({
    url: '/chat/currentlist' + dialogid,
    method: 'post',
    data: data
  });
}

/* 获取客服状态列表 */
export function Kefustatus(data) {
  return request({
    url: '/staff/Kefustatus',
    method: 'get',
    data
  });
}

/* 切换客服状态 */
export function kfChangeState(data) {
  return request({
    url: '/agent/changestate',
    method: 'post',
    data: data
  });
}

/* 图片上传接口 */
export function uploadimg(data) {
  return request({
    url: '/file/uploadimg',
    method: 'post',
    params: data
  });
}

/* 关闭对话 */
export function closeContainer(data) {
  const url = `/chat/message/close/${data.dialogid}`;
  return request({
    url: url,
    method: 'put'
  });
}

/* 发起会话 */
export function startChart(data) {
  const url = `/dialogs/start-chat/${data.dialogid}`;
  return request({
    url: url,
    method: 'post'
  });
}

/* 添加黑名单New */
export function addblack(data) {
  return request({
    url: '/blackList/addblack',
    method: 'post',
    data: data
  });
}

// 获取客服状态接口
export function kfCurrentState(data) {
  return request({
    url: '/blackList/addblack',
    method: 'get',
    data
  });
}

// 获取客服详情接口
export function kfUserInfo(data) {
  return request({
    url: '/agent/userInfo',
    method: 'get',
    data
  });
}

// 获取客服详情接口
export function kfdialogQueueSize(data) {
  return request({
    url: '/agent/dialogQueueSize?t=' + data.t,
    method: 'get'
  });
}

// 向客户推送服务评价
export function kfAdvice(data) {
  const url = `/chat/advice/${data.dialogid}`;
  return request({
    url: url,
    method: 'put'
  });
}

// 获取有接线能力的客服
export function onlinekefu(params) {
  return request({
    url: '/transfer/onlinekefu',
    method: 'get',
    params
  });
}

// 发起对话转接
export function transferSend(data) {
  return request({
    url: '/transfer/transfer-dialog',
    method: 'post',
    data: data
  });
}

// 会话转至待跟进
export function settodooo(data) {
  return request({
    url: `/dialogs/settodo/${data.dialogid}`,
    method: 'post',
    data: data
  });
}

// 拒绝对话转接
export function transferReject(data) {
  return request({
    url: '/transfer/transfer-reject/' + data,
    method: 'post'
  });
}

// 接受对话转接
export function transferAccept(data) {
  return request({
    url: '/transfer/transfer-accept/' + data,
    method: 'post'
  });
}

/* 搜索获取信息列表 */
export function searchKeyword(data) {
  return request({
    url: '/crm/getMerchantByKeyword?keyword=' + data,
    method: 'get'
  });
}

/* 点击弹出会话窗口 */
export function startConversation(data) {
  return request({
    url: '/kefuDialog/launchDialogByMerchantId',
    method: 'get',
    params: data
  });
}

/* 查找当前会话登录状态 */
export function getCurrentState() {
  return request({
    url: '/agent/currentState',
    method: 'get'
  });
}

/**
 * v2.1.4
 * 查询会话列表
 * dialogType:1-查询会话中列表,2-查询留言列表，3-查询待跟进列表
 */
export function getDialogPageList(params) {
  return request({
    url: '/dialogs/getDialogPageList',
    method: 'get',
    params
  });
}

/**
 *
 * 会话列表保存排序方式到服务端
 */
export function saveDialogSort(params) {
  return request({
    url: '/dialogs/saveDialogSort',
    methods: 'get',
    params
  });
}

/**
 * v2.1.4
 * 移除某一会话
 */
export function dialogRemove(params) {
  return request({
    url: '/chat/dialogRemove',
    method: 'get',
    params
  });
}

/**
 * 读取以往会话历史消息
 * {
 * currentDialogId：当前会话ID,
 * offset：起始游标,
 * size：页长度,
 * time：时间,
 * type:查询类型（1-搜索查询（传：type,userId）2-待跟进.历史.留言（传：type,currentDialogId,userId）3-会话中（type,currentDialogId,userId,time(查询第二页的时间),
 * userId:用户ID
 * }
 */
export function getbeforeHistoryMessage(params) {
  return request({
    url: '/chat/beforeHistoryMessage',
    method: 'get',
    params
  });
}

/**
 * im根据表单id查询发起节点模板字段及权限（带客户信息）
 * @param { formId: 1，dialogId：2 }
 */
export function listFieldAndAuthByFormId(params) {
  return request({
    url: '/workOrder/listFieldAndAuthByFormId',
    method: 'get',
    params
  });
}

/**
 * 根据会话id查询会话详情
 */
export function getDialogInfo(params) {
  return request({
    url: '/dialogs/getDialogInfo',
    method: 'get',
    params
  });
}

/**
 * 撤回指定消息
 * @param {dialogId：会话Id，recallMessageId：被撤回消息Id} data
 */
export function msgRecall(params) {
  return request({
    url: '/chat/msgRecall',
    method: 'get',
    params
  });
}

/**
 * 上报信息接口
 */
export function sendlog(data) {
  return request({
    url: '/front/sendlog',
    method: 'post',
    data
  });
}

/**
 * 上报信息接口
 */
export function sendcard(dialogId) {
  return request({
    url: `/chat/sendcard/${dialogId}`,
    method: 'post',
  });
}

// 获取客服第一次发送时间
export function getKefuFirstMsgTimeExcludeAutoMsg(params) {
  return request({
    url: '/dialogs/getKefuFirstMsgTimeExcludeAutoMsg',
    method: 'get',
    params
  });
}