<template>
  <div>
    <div class="WeLanguage">
      <h1>自定义欢迎语</h1>
      <el-form ref="weLanguageForm">
        <el-form-item>
          <div class="weLanguageTemWrap" v-for="(item,index) in weLanguageForm">
            <div class="weLanguageTemTitle">
              <span class="welcomeLange">网站欢迎语</span>
              <span>
                <el-select
                  ref="selectEl"
                  v-model="item.areaGroupList"
                  multiple
                  placeholder="请选择"
                  @visible-change="choiceGroups"
                  @remove-tag="handerRemove"
                  class="dropDown"
                >
                  <el-option
                    v-for="iteml in weLanguageOptions"
                    :key="iteml.value"
                    :label="iteml.name"
                    :disabled="iteml.disabledSingle"
                    :value="iteml.id"
                  ></el-option>
                </el-select>
              </span>
            </div>
            <editor-bar
              v-model="item.dicVal"
              :is-clear="isClear"
              :index="index"
              :class="[dialogDelete==true ?'dialogDelete':' ']"
              @change="change"
              @deleteIndex="delWelLange"
            ></editor-bar>
          </div>
        </el-form-item>
        <el-form-item class="addWelangeAllList">
          <div class="addWelangeAll">
            <span class="el-icon-plus" @click="addWelLange" v-if="showOn">添加欢迎语</span>
          </div>
        </el-form-item>
        <el-form-item class="marginTopList">
          <h1>系统欢迎语</h1>
          <div class="weLanguageTemWrap weLanguageTemWrap_1 weLanguageTemWrap_2">
            <div class="weLanguageTemTitle">
              <span class="welcomeLange">坐席报工号/昵称</span>
              <span></span>
            </div>
            <editor-bar
              v-model="SystemEditorText_1.dicVal"
              :is-clear="isClear"
              :index="1"
              @change="change"
            ></editor-bar>
          </div>
        </el-form-item>
        <el-form-item class="marginTopList">
          <div class="weLanguageTemWrap weLanguageTemWrap_2">
            <div class="weLanguageTemTitle">
              <span class="welcomeLange">坐席报欢迎词</span>
              <span></span>
            </div>
            <editor-bar
              v-model="SystemEditorText_2.dicVal"
              :is-clear="isClear"
              :index="2"
              @change="change"
            ></editor-bar>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="weLanguageTemWrap weLanguageTemWrap_2">
            <div class="weLanguageTemTitle">
              <span class="welcomeLange">网站客户排队温馨提示</span>
              <span></span>
            </div>
            <editor-bar
              v-model="SystemEditorText_3.dicVal"
              :is-clear="isClear"
              :index="2"
              @change="change"
            ></editor-bar>
          </div>
        </el-form-item>
        <el-form-item class="saveStyle">
          <xyy-button @click="handerSave">保存</xyy-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import XyyButton from '../../components/xyy/xyy-button/index';
import EditorBar from './richTextEditor/wangeditor';
import { ShowWelcome, saveWelcome } from '@/api/configuration/WelcomeLan';
import { EmployeeGroups } from '@/api/configuration/RegionalMenu';
export default {
  name: 'compWeLanguage',
  components: {
    XyyButton,
    EditorBar,
  },
  data() {
    return {
      showOn: true, // 控制欢迎语显示隐藏
      dialogDelete: false, // 控制class显示隐藏
      weLanguageForm: [],
      SystemEditorText_1: {}, // 系统欢迎语
      SystemEditorText_2: {}, // 坐席报欢迎词
      SystemEditorText_3: {}, // 网站客户排队温馨提示
      isClear: false,
      weLanguageOptions: [], // 选择组
    };
  },
  created() {
    this.getWelcome();
  },
  activated() {
    this.getWelcome();
  },
  methods: {
    // 添加欢迎语事件
    addWelLange() {
      if (this.weLanguageForm.length < 15) {
        this.weLanguageForm.push({
          areaGroupList: [],
          dicVal: '',
          type: 1,
          dicName: '网络欢迎语',
        });
      }
      if (this.weLanguageForm.length === 15) {
        this.showOn = false;
      }
      // 判断删除按钮的显示隐藏
      if (this.weLanguageForm.length === 1) {
        this.dialogDelete = true;
      }
      if (this.weLanguageForm.length > 1) {
        this.dialogDelete = false;
      }
    },
    // 页面保存事件
    handerSave() {
      const that = this;
      // 初始没有数据 保存添加数据
      if (
        this.SystemEditorText_1.dicName === undefined &&
        this.SystemEditorText_1.dicVal !== undefined
      ) {
        this.$set(this.SystemEditorText_1, 'type', 2);
        this.$set(this.SystemEditorText_1, 'dicName', '坐席报工号');
      }
      if (
        this.SystemEditorText_2.dicName === undefined &&
        this.SystemEditorText_2.dicVal !== undefined
      ) {
        this.$set(this.SystemEditorText_2, 'type', 3);
        this.$set(this.SystemEditorText_2, 'dicName', '坐席欢迎词');
      }
      if (
        this.SystemEditorText_3.dicName === undefined &&
        this.SystemEditorText_3.dicVal !== undefined
      ) {
        this.$set(this.SystemEditorText_3, 'type', 4);
        this.$set(this.SystemEditorText_3, 'dicName', '客服排队');
      }
      const aotoRepleList = [];
      this.weLanguageForm.forEach(function (item, index) {
        aotoRepleList.push(item);
      });

      if (Object.keys(that.SystemEditorText_1).length !== 0) {
        aotoRepleList.push(that.SystemEditorText_1);
      }

      if (Object.keys(that.SystemEditorText_2).length !== 0) {
        aotoRepleList.push(that.SystemEditorText_2);
      }
      if (Object.keys(that.SystemEditorText_3).length !== 0) {
        aotoRepleList.push(that.SystemEditorText_3);
      }
      let aotoRepleListNew = [];
      aotoRepleListNew = JSON.parse(JSON.stringify(aotoRepleList));
      aotoRepleListNew.forEach(function (item, index) {
        item.dicVal = JSON.stringify(item.dicVal).replace(/\"/g, "'");
      });

      const preParam = {
        aotoRepleList: aotoRepleList,
      };
      //    判断是否有超出100个字符的
      let indexList = 0;
      console.log('aotoRepleList');
      console.log(aotoRepleList);
      aotoRepleList.forEach(function (item, index) {
        //        去掉除img以外的所有标签；
        var regL = /<\/?(?!img)[a-z]+?[^>]*>/gi;
        var z = item.dicVal.replace(regL, '');
        console.log(z);
        item.dicVal = z;
        const bbb = item.dicVal;
        //      替换img标签
        var infoNewList = bbb.replace(/<[^>]+>/g, '0');
        // 去掉所有空格
        infoNewList = infoNewList.replace(/\s+/g, '');
        console.log(infoNewList.length);
        console.log(infoNewList);
        if (infoNewList.length > 101) {
          indexList++;
        }
      });
      if (indexList > 0) {
        this.$message({
          message: '编辑内容不能超过100个字符',
          customClass: 'messageTip',
        });
        return false;
      }
      saveWelcome(preParam)
        .then((response) => {
          if (response.code === 0) {
            this.$message({
              message: response.msg,
              customClass: 'messageTip',
            });
            return false;
          }
          if (response.msg === 'success') {
            this.$message('保存成功');
            EmployeeGroups()
              .then((response) => {
                response.data.forEach((item, index) => {
                  item.disabledSingle = false;
                });
                this.weLanguageOptions = response.data;
              })
              .catch(function (error) {
                console.log(error);
              });
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    // 输入框改变事件
    change() {},
    // 欢迎语框里的删除事件
    delWelLange(index) {
      if (this.weLanguageForm.length <= 15) {
        this.showOn = true;
      }
      if (this.weLanguageForm.length !== 1) {
        this.weLanguageForm.splice(index, 1);
      }
      // 判断删除按钮的显示隐藏
      if (this.weLanguageForm.length === 1) {
        this.dialogDelete = true;
      }
      if (this.weLanguageForm.length > 1) {
        this.dialogDelete = false;
      }
    },
    // 员工组选择事件
    choiceGroups(val) {
      let arr = []; // 存储选择的选项
      this.weLanguageForm.forEach((item, index) => {
        arr = arr.concat(item.areaGroupList);
      });
      this.weLanguageOptions.forEach((item, index) => {
        arr.forEach((itemL, indexL) => {
          if (itemL === item.id) {
            item.disabledSingle = true;
          }
        });
      });
    },
    // 删除事件
    handerRemove(val) {
      this.weLanguageOptions.forEach((item, index) => {
        if (item.id === val) {
          item.disabledSingle = false;
        }
      });
    },
    // 获取欢迎语和员工组
    getWelcome() {
      let preParam;
      const that = this;
      // 获取欢迎语
      ShowWelcome(preParam)
        .then((response) => {
          response.data.forEach(function (item, index) {
            // 1-网络欢迎语 2-坐席报工号 3-坐席欢迎词 4-客服排队
            // 如果返回数值的时候 赋值
            if (item.type === 1) {
              let itemL = {};
              itemL = Object.assign(
                {},
                {
                  areaGroupList: item.areaGroupList,
                  dicName: item.dicName,
                  dicVal: item.dicVal,
                  id: item.id,
                  type: 1,
                }
              );
              that.weLanguageForm.push(itemL);
            } else if (item.type === 2) {
              let itemL = {};
              itemL = Object.assign(
                {},
                {
                  dicName: item.dicName,
                  dicVal: item.dicVal,
                  id: item.id,
                  type: 2,
                }
              );
              that.SystemEditorText_1 = Object.assign({}, itemL);
            } else if (item.type === 3) {
              let itemL = {};
              itemL = Object.assign(
                {},
                {
                  dicName: item.dicName,
                  dicVal: item.dicVal,
                  id: item.id,
                  type: 3,
                }
              );
              that.SystemEditorText_2 = Object.assign({}, itemL);
            } else if (item.type === 4) {
              let itemL = {};
              itemL = Object.assign(
                {},
                {
                  dicName: item.dicName,
                  dicVal: item.dicVal,
                  id: item.id,
                  type: 4,
                }
              );
              that.SystemEditorText_3 = Object.assign({}, itemL);
            }
          });
          if (that.weLanguageForm.length === 0) {
            const emptyData = {
              areaGroupList: [],
              dicVal: '',
              type: 1,
              dicName: '网络欢迎语',
            };
            that.weLanguageForm.push(emptyData);
          }
          if (that.weLanguageForm.length === 1) {
            this.dialogDelete = true;
          }
        })
        .catch(function (error) {
          console.log(error);
        });
      //   获取员工组
      EmployeeGroups(preParam)
        .then((response) => {
          response.data.forEach((item, index) => {
            item.disabledSingle = false;
          });
          this.weLanguageOptions = response.data;
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    //   获取文字，去掉标签
    removeTAG(str) {
      return str.replace(/<[^>]+>/g, '');
    },
  },
};
</script>
<style lang="scss" scoped>
.saveStyle {
  /deep/.el-button.el-button--primary.is-plain {
    background: rgba(59, 149, 168, 1);
    border-radius: 2px;
    color: #fff;
  }
}

.marginTopList {
  margin-bottom: 0 !important;
}
/deep/.weLanguageTemWrap_2 {
  /deep/.elIconDelete {
    display: none;
  }
}
/deep/.dialogDelete {
  .elIconDelete {
    display: none;
  }
}
.addWelangeAllList {
  margin-bottom: 0 !important;
  /deep/.el-form-item__content {
    line-height: 32px;
    border-radius: 2px;
    border: 1px dashed rgba(220, 223, 230, 1);
  }
}
</style>
<style lang="scss">
.messageTip {
  background: rgba(255, 241, 240, 1);
  border: 1px solid rgba(255, 163, 158, 1);
  /deep/.el-message__content {
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/.el-icon-info {
    &:before {
      color: rgba(255, 48, 36, 1);
    }
  }
}
.dropDown {
  /deep/.el-select__caret {
    line-height: 28px;
  }
}
.WeLanguage {
  width: 724px;
  height: auto;
  margin: 0 auto;
  position: relative;
  font-size: 14px;
  /*.weLanguageTemWrap:first-child .el-icon-delete, .weLanguageTemWrap_1 .el-icon-delete{*/
  /*display: none;*/
  /*}*/
  .weLanguageTemWrap {
    margin-top: 20px;
  }
  .addWelangeAll {
    text-align: center;
    cursor: pointer;
  }
  h1 {
    font-size: 16px;
  }
  .weLanguageTemTitle {
    width: 100%;
    height: 39px;
    border: 1px solid rgba(220, 222, 227, 1);
    border-bottom: 0;
    line-height: 39px;
    background: rgba(245, 247, 250, 1);
    border-radius: 2px 2px 0px 0px;
    display: flex;
    justify-content: space-between;

    .welcomeLange {
      padding-left: 12px;
    }

    .el-select {
      width: 448px !important;
      height: 28px;

      .el-input {
        line-height: 28px !important;
      }

      .el-input__inner {
        @extend .el-select;
      }
    }
  }

  .addWelWords {
    width: 723px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    color: rgba(87, 87, 102, 1);
    background: rgba(255, 255, 255, 1);
    border-radius: 2px;
    border: 1px solid rgba(220, 223, 230, 1);
    cursor: pointer;
  }
}
</style>
