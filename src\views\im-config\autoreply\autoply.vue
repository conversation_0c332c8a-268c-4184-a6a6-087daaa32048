<template>
  <div class="im-link-box">
    <el-form ref="from" :model="form" :rules="rules" label-width="80px" class="im-link-boxxiao">
      <el-form-item label="名称" prop="name" id="ming">
        <el-input v-model.trim="form.name" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="描述" prop="link">
        <el-input v-model="form.link" placeholder="请输入" />
      </el-form-item>
      <!-- <el-form-item label="说明" prop="describe">
        <el-input v-model="form.describe" type="textarea" />
      </el-form-item>-->
      <el-form-item class="marginTopList" label="模板内容">
        <div class="weLanguageTemWrap weLanguageTemWrap_2">
          <div class="weLanguageTemTitle">
            <span class="zhushi">注：自动回复在5分钟内不会重复回复</span>
            <el-checkbox v-model="checkedFirst" class="welcomeLange">当天第一次收到买家消息时的自动回复</el-checkbox>
          </div>
          <!-- <editor-bar v-model="form.link1" class="editor1" v-if="biaoqing"></editor-bar> -->
          <chatinput
            :containerID="currentContainerID"
            :editorTextProps="form.firstMessageReply"
            v-if="topvuewType==='ing'"
            ref="contentinput"
            :uploadurl="imgUploadUrl"
            class="editor1"
            @clickInputTool="clickInputTool"
          ></chatinput>
          <el-checkbox v-model="checkedAll" class="welcomeLange">当所有客服的工作状态都离线时自动回复</el-checkbox>
          <!-- <editor-bar v-model="form.link2" class="editor1" v-if="biaoqing"></editor-bar> -->
          <!-- <editor
            id="editor_msg"
            height="100%"
            width="100%"
            ref="kindeditor"
            :content="editorText"
            :loadStyleMode="false"
            :pluginsPath="'/pc/static/plugins/'"
            :items="['emoticonss', 'fontsize','forecolor']"
            @on-content-change="onContentChange"
            :designMode="true"
            :allowPreviewEmoticons="false"
            class="editor1"
          ></editor>-->

          <chatinsuts
            :containerID="currentContainerID"
            :editorTextProups="form.customOfflineReply"
            v-if="topvuewType==='ing'"
            ref="contentinput"
            :uploadurl="imgUploadUrl"
            class="editor1"
            @clickInputBool="clickInputBool"
          ></chatinsuts>
        </div>
      </el-form-item>
      <el-form-item label="关联问题" prop="checkProvince" class="shengshi">
        <div class="sheng_guan">
          <span>{{guangli}}</span>
        </div>
        <el-button @click="AddProvCities">
          <span class="el-icon-plus">添加</span>
        </el-button>
        <!-- 添加弹出框-开始-->
        <el-dialog :visible.sync="dialogTableVisible" :closeOnClickModal="false" :showClose="false">
          <el-form-item class="shezhiyin">
            <p class="line">
              添加关联问题
              <span>首问自动回复只能关联10个问题</span>
            </p>
          </el-form-item>
          <el-form-item class="NavigationSettings">
            <el-row type="flex" justify="space-between" style="background:#EEEEEE">
              <el-col :span="1" class="biaoge_xu">
                <span class="circular biaotou">序号</span>
              </el-col>
              <el-col
                :span="7"
                style="width: 22.16667%;border: 0.5px solid #e4e4eb;
  border-bottom: none;
  border-right: none;"
              >
                <span class="biaotou">问题描述</span>
              </el-col>
              <el-col
                :span="15"
                style="border: 0.5px solid #e4e4eb;
  border-bottom: none;
  border-right: none;"
              >
                <span class="biaotou">问题回答</span>
              </el-col>
              <el-col
                :span="1"
                class="removeRight"
                style="border: 0.5px solid #e4e4eb;border-bottom: none;"
              >
                <span class="biaotou">操作</span>
              </el-col>
            </el-row>
            <!-- <draggable
              element="ul"
              v-model="form.menus"
              :options="options"
              @update="datadragEnd"
              dragable="true"
              style="padding:0;margin:0;"
            >-->
            <transition-group>
              <el-row
                v-for="(item,index) in frombox"
                :key="index"
                type="flex"
                justify="space-between"
                style="line-height: 100px;"
              >
                <el-col :span="1" class="biaoge_xu">
                  <span class="circular">{{ index+1 }}</span>
                </el-col>
                <el-col :span="7" style="width: 22.16667%;">
                  <input
                    v-model="item.questionDesc"
                    maxlength="30"
                    placeholder="请输入"
                    class="biaoge_sheng"
                  />
                </el-col>
                <el-col :span="15">
                  <div class="qwer">
                    <el-input
                      class="paperview-input-text"
                      v-model="item.questionAnswer"
                      placeholder="请输入"
                      maxlength="400"
                      type="textarea"
                      :autosize="{minRows: 4 , maxRows: 4 }"
                    />
                  </div>
                </el-col>
                <el-col
                  :span="1"
                  class="removeRight"
                  style="border: 0.5px solid #e4e4eb;border-bottom: none;cursor:pointer"
                >
                  <span @click="handerRemove(item,index)">删除</span>
                </el-col>
              </el-row>
            </transition-group>
            <!-- </draggable> -->

            <el-row type="flex" justify="space-between" class="biaoge_xia">
              <el-col :span="1"></el-col>
              <el-col :span="7">
                <div v-if="addShow">
                  <span class="el-icon-plus" @click="addMenu">添加下一个问题</span>
                </div>
              </el-col>
              <el-col :span="15" style="border:none"></el-col>
              <el-col :span="1"></el-col>
            </el-row>
          </el-form-item>

          <div slot="footer" class="dialog-footer">
            <el-button @click="makeNo">取 消</el-button>
            <el-button type="primary" @click=" makeSure">确 定</el-button>
          </div>
        </el-dialog>
        <!-- 添加弹出框结束-->
      </el-form-item>
      <el-form-item label="应用范围" prop="groups">
        <el-select v-model="form.groups" multiple placeholder="请接入应用范围" style="width: 680px;">
          <el-option
            v-for="item in employeGroups"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 应用渠道 -->
      <el-form-item label="应用渠道" prop="appName">
        <el-select v-model="form.appName" multiple placeholder="请选择应用渠道" style="width: 680px;">
          <el-option
            v-for="item in applicationChannelsData"
            :key="item.appId"
            :label="item.appName"
            :value="item.appId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="生效日期" prop="linkiy">
        <el-checkbox v-model="checkedTime" class="welcomeLange">保存立即生效</el-checkbox>
      </el-form-item>
      <!-- <editor
        id="editor_msg"
        height="170px"
        width="100%"
        ref="kindeditor"
        :content="editorText"
        :loadStyleMode="false"
        :pluginsPath="'/pc/static/plugins/'"
        :items="['emoticonss', 'imageinsert', 'transfer','fontsize','forecolor','history', 'assess']"
        @on-content-change="onContentChange"
        style="color:green;"
      ></editor>-->
      <el-form-item label=" ">
        <el-button type="primary" @click="checkTimer(save,'timer')()">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import chatinput from '../../im_view/components/container_message/im_container_chatinput_auto';
import chatinsuts from '../../im_view/components/container_message/im_container_chatinput_reply';
// import { linkEdit } from '@/api/configuration/use_link';
import EditorBar from '../../configuration/richTextEditor/wangeditor';
// import getters from '../../../store/getters';
import {
  regionalMenuTo,
  EmployeeGroups,
  getRegGroup,
  saveMenu,
  getAuto,
  linkEdit,
  listEdit,
  listGoods,
  getSources
} from '@/api/configuration/RegionalMenu';
import draggable from 'vuedraggable';
export default {
  components: {
    EditorBar,
    draggable,
    chatinput,
    chatinsuts
  },
  data() {
    return {
      guanbox: [],
      currentContainerID: '0', //当前会话id
      currentContainerID1: '1', //当前会话id
      topvuewType: 'ing', //顶部客户信息简介类型
      imgUploadUrl: process.env.BASE_API_IM + '/im-file/uploadimg',
      showOn: true, // 控制欢迎语显示隐藏
      dialogDelete: false, // 控制class显示隐藏
      SystemEditorText_1: {}, // 第一次
      SystemEditorText_2: {}, // 当前所有用户
      isClear: false,
      weLanguageOptions: [], // 选择组
      id: '',
      guangli: '',
      form: {
        name: '',
        link: '',
        describe: '',
        groups: '',
        firstMessageReply: '',
        firstMessageReplyState: 2,
        customOfflineReply: '',
        customOfflineReplyState: 2,
        imAutoReplyQuestionList: [
          {
            questionAnswer: '',
            questionDesc: '',
            selectStatus: '',
            sortValue: ''
          }
        ],
        appName: '' //渠道
      },
      // frombox: [
      //   { menu: '湖北', groups: '阿卡多办卡都是可拨打', sort: 1 },
      //   { menu: '山东', groups: '都是可拨打', sort: 2 }
      // ],
      frombox: [
        { questionAnswer: '', questionDesc: '', selectStatus: 2, sortValue: 1 }
      ],
      frombox11: [
        { questionAnswer: '', questionDesc: '', selectStatus: 2, sortValue: 1 }
      ],
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { max: 20, message: '不超过20个字', trigger: 'blur' }
        ],
        link: [{ required: false, message: '请输入链接', trigger: 'blur' }],
        describe: [{ max: 200, message: '不超过200个字', trigger: 'blur' }],
        groups: [{ required: true, message: '请输入范围', trigger: 'blur' }],
        checkProvince: [{ required: false, trigger: 'blur' }],
        appName: [{ required: true, message: '请选择渠道', trigger: 'blur' }]
      },
      timer: '',
      checkedTime: true,
      checkedFirst: true,
      checkedAll: true,
      employeGroups: [],
      applicationChannelsData: [],
      dialogTableVisible: false, // 控制弹出框
      biaoqing: true,
      options: {
        animation: 150,
        handle: '.el-row',
        filter: ''
      },
      addShow: true, // 控制添加菜单显示隐藏
      id: '',
      immediateState: 2,
      autoReplayQuestions: [],
      listGo: []
    };
  },
  mounted() {
    // this.$store.getters.khid;
    if (this.$route.query.data) {
      const { id, creatorName, applicationScope, immediateState } = JSON.parse(
        this.$route.query.data
      );
      // var autoReplyConfigId = Number(this.id);

      // listGoods(autoReplyConfigId).then(res => {
      //   console.log(res);
      //   this.frombox = res.data;
      //   if (!(res.data && res.data.length)) {
      //     this.frombox = [];
      //   }
      // });
      // this.form.name = creatorName;
      // this.form.groups = applicationScope.split(',');
      this.form.describe = immediateState;
      this.id = id;
      const param = id;
      listEdit(param).then(res => {
        // this.form.groups = res.data.imAreaGroup;

        // var nameid = [];
        // var name = [];
        // debugger;
        // this.form.groups.forEach(item => {
        //   nameid.push(item.id);
        //   item.name = item.groupName;
        // });

        res.data.imAreaGroup.forEach(item => {
          this.form.groups.push(item.id);
        });
        res.data.apps.forEach(item => {
          this.form.appName.push(item.appId);
        });

        const goodList = res.data;
        this.form.name = goodList.name;
        //this.form.groups = nameid;
        this.form.link = goodList.remark;
        this.form.firstMessageReply = goodList.firstMessageReply;
        this.form.firstMessageReplyState = goodList.firstMessageReplyState;
        this.form.customOfflineReply = goodList.customOfflineReply;
        this.form.customOfflineReplyState = goodList.customOfflineReplyState;
        this.form.imAutoReplyQuestionList = goodList.imAutoReplyQuestionList;
        if (this.form.customOfflineReplyState === 2) {
          this.checkedAll = true;
        } else if (this.form.customOfflineReplyState === 1) {
          this.checkedAll = false;
        }
        if (this.form.firstMessageReplyState === 2) {
          this.checkedFirst = true;
        } else if (this.form.firstMessageReplyState === 1) {
          this.checkedFirst = false;
        }
        if (this.form.imAutoReplyQuestionList.length == 0) {
          this.form.imAutoReplyQuestionList = this.frombox;
        } else {
          this.frombox = this.form.imAutoReplyQuestionList;
          this.form.imAutoReplyQuestionList.forEach((item, index) => {
            this.guangli += index + 1 + '.' + item.questionDesc + '  ';
          });
        }
        // var okuyw = '';

        if (this.form.imAutoReplyQuestionList[0].questionDesc == '') {
          this.guangli = '';
        }
        // for (let k = 0; k < this.form.imAutoReplyQuestionList.length; k++) {
        //   okuyw += this.form.imAutoReplyQuestionList[k].questionAnswer + ' ';
        //   this.guangli = okuyw;
        // }
      });
    }
    this.getSourcesList();
  },
  activated() {
    if (this.$route.query.data) {
      this.$store.dispatch('tagsView/updateVisitedView', {
        ...this.$route,
        meta: {
          title: '编辑模板'
        }
      });
    }
  },
  created() {
    this.getValue();
  },
  methods: {
    clickInputTool(val) {
      this.form.firstMessageReply = val;
      if (this.form.firstMessageReply.length > 450) {
        this.$message.error(
          '当天第一次收到买家消息时的回复内容不能超过450个字'
        );
      }
    },
    clickInputBool(val) {
      this.form.customOfflineReply = val;
      if (this.form.customOfflineReply.length > 450) {
        this.$message.error(
          '当所有客服的工作状态都离线时回复内容不能超过450个字'
        );
      }
    },
    // 输入框改变事件
    change() {},
    // 关联问题弹窗确定
    makeSure() {
      var okuy = '';
      // for (let a = 0; a < this.frombox.length; a++) {
      //   okuy += this.frombox[a].questionAnswer + ' ';
      //   console.log(okuy);
      //   this.guangli = okuy;
      // }
      // var autoReplayQuestions = [];
      if (this.frombox.length == 0) {
        this.guangli = ' ';
        this.form.imAutoReplyQuestionList = this.frombox11;
      } else {
        this.frombox.forEach((itemQue, index) => {
          okuy += index + 1 + '.' + itemQue.questionDesc + '  ';
          this.guangli = okuy;
        });
        this.form.imAutoReplyQuestionList = this.frombox;
      }
      this.dialogTableVisible = false;
      console.log(this.form.imAutoReplyQuestionList);
    },
    makeNo() {
      this.frombox = this.listGo;
      this.dialogTableVisible = false;
      this.addShow = true;
    },
    save() {
      // createTime	创建时间	string	不必填
      // des	链接描述	string	不必填
      // link	链接	string	必填
      // linkId	链接id		不必填，传空添加链接，不为空编辑链接
      // title	链接名称	string	必填
      if (this.checkedTime === false) {
        this.form.describe = 1;
      } else {
        this.form.describe = 2;
      }
      if (this.checkedFirst === false) {
        this.form.firstMessageReplyState = 1;
      } else {
        this.form.firstMessageReplyState = 2;
      }
      if (this.checkedAll === false) {
        this.form.customOfflineReplyState = 1;
      } else {
        this.form.customOfflineReplyState = 2;
      }
      if (this.form.name == '') {
        this.$message.error('请填写名称');
        return;
      }
      const groups = [];
      this.form.groups.forEach(item => {
        groups.push({
          id: item
        });
      });

      const appNames = [];
      this.form.appName.forEach(item => {
        appNames.push({
          appId: item
        });
      });

      const autoReplayQuestions = [];
      this.form.imAutoReplyQuestionList.forEach(itemWen => {
        autoReplayQuestions.push({
          questionAnswer: itemWen.questionAnswer,
          questionDesc: itemWen.questionDesc,
          selectStatus: itemWen.selectStatus,
          sortValue: itemWen.sortValue
        });
      });
      // console.log(groups);
      // console.log(autoReplayQuestions);
      // console.log(this.form.imAutoReplyQuestionList);
      const param = {
        id: this.id,
        name: this.form.name,
        imAreaGroup: groups,
        apps: appNames,
        imAutoReplyQuestionList: autoReplayQuestions,
        customOfflineReply: this.form.customOfflineReply,
        customOfflineReplyState: this.form.customOfflineReplyState,
        firstMessageReply: this.form.firstMessageReply,
        firstMessageReplyState: this.form.firstMessageReplyState,
        immediateState: this.form.describe,
        remark: this.form.link
      };
      // console.log(param);
      // console.log(this.form.groups);
      // console.log(this.form.link);
      linkEdit(param).then(res => {
        // console.log(res);
        if (res.code === 1) {
          if (this.id) {
            this.$XyyMessage.success('修改成功！');
          } else {
            this.$XyyMessage.success('添加成功！');
          }

          this.$store.dispatch('tagsView/delView', this.$route);
          this.$router.push({
            path: '/chat/autoReply'
          });
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 点击添加事件
    async AddProvCities() {
      this.dialogTableVisible = true;
      this.biaoqing = false;
      console.log(this.frombox);
      sessionStorage.setItem('key', JSON.stringify(this.frombox));
      this.listGo = JSON.parse(sessionStorage.getItem('key'));
      if (this.frombox.length === 10) {
        this.addShow = false;
      }
    },
    // 获取区域范围
    async getValue() {
      let that = this;
      that.selectedValue = [];
      //   获取范围
      await getAuto()
        .then(response => {
          this.employeGroups = response.data;
          console.log(this.employeGroups);
        })
        .catch(function(error) {
          console.log(error);
        });
    },

    // 添加菜单
    addMenu() {
      if (this.frombox.length === 9) {
        this.addShow = false;
        this.$message({
          message: '最多添加10个导航菜单',
          customClass: 'messageTip'
        });
      }
      if (this.frombox.length <= 9) {
        let index = this.frombox.length + 1;
        console.log(index);
        this.frombox.push({
          questionAnswer: '',
          questionDesc: '',
          sortValue: index,
          selectStatus: 2
        });
        sessionStorage.setItem('key', this.frombox);
      }
    },
    // 删除菜单;
    handerRemove(item, index) {
      console.log(this.frombox.length);
      if (index !== 0) {
        this.frombox.splice(index, 1);
      }
      if (index == 0) {
        this.frombox.splice(index, 1);
      }
      if (this.frombox.length <= 10) {
        this.addShow = true;
      }
      sessionStorage.setItem('key', this.frombox);
    },
    //列表拖动
    datadragEnd(evt) {
      console.log('拖动前的索引 :' + evt.oldIndex);
      console.log('拖动后的索引 :' + evt.newIndex);
      let filters = this.frombox;
      for (let a = 0; a < filters.length; a++) {
        filters[a].sortValue = a + 1;
      }
      this.frombox = filters;
      console.log(filters);
    },
    // 获取来源渠道数据
    getSourcesList() {
      getSources()
        .then(response => {
          this.applicationChannelsData = response.data;
        })
        .catch(function(error) {
          console.log(error);
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.qwer {
  height: 101px;
  width: 100%;
  border: 0.5px solid #e4e4eb;
  border-bottom: none;
  border-right: none;

  .el-input {
    /deep/.el-input__inner {
      border: none !important;
    }
  }
  .el-textarea {
    /deep/.el-textarea__inner {
      border: none !important;
    }
  }
}

.im-link-box {
  .el-form {
    width: calc(100% - 280px);
    margin: 20px auto;
    .el-form-item {
      margin-bottom: 20px;

      /deep/.el-form-item__label {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(41, 41, 51, 1);
      }
      .el-input {
        /deep/.el-input__inner {
          height: 36px;
          line-height: 36px;
          border: 1px solid rgba(228, 228, 235, 1);
        }
      }
      .el-textarea {
        /deep/.el-textarea__inner {
          height: 211px;
          border: 1px solid rgba(228, 228, 235, 1);
        }
      }

      .el-button {
        height: 36px;
        padding: 0 12px;
        line-height: 36px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
        border-radius: 2px;
        border: 1px solid rgba(228, 228, 235, 1);
        &:focus,
        &:hover {
          background: #fff;
          border-color: rgba(228, 228, 235, 1);
        }
        &.el-button--primary {
          color: rgba(255, 255, 255, 1);
          padding: 0 20px;
          border: none;
        }
        &.el-button--primary:focus,
        &.el-button--primary:hover {
          background: #3b95a8;
          border-color: #3b95a8;
        }
      }
      /deep/ .el-form-item__content {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
      }
    }
  }
}
.marginTopList {
  margin-bottom: 0 !important;
}
/deep/.weLanguageTemWrap_2 {
  background: #f5f7fa;
  /deep/.elIconDelete {
    display: none;
  }
}
.weLanguageTemWrap {
  margin: 20px 0;
  padding-bottom: 20px;
}
.zhushi {
  display: block;
  font-size: 14px;
  padding-left: 12px;
  height: 30px;
  color: rgba(174, 174, 191, 1);
}
.welcomeLange {
  padding-left: 12px;
}
.editor1 {
  width: 96%;
  height: 165px;
  background: #fff;
  margin: auto;
}

.ke-icon-imageinsert {
  background-image: url('/pc/static/plugins/imageinsert/images/imageinset.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.ke-icon-transfer {
  background-image: url('/pc/static/plugins/transfer/image/zhuanyi.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  /* background-color: green; */
  width: 18px;
  height: 18px;
}

.ke-icon-history {
  background-image: url('/pc/static/plugins/history/images/history.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.ke-icon-assess {
  background-image: url('/pc/static/plugins/assess/images/assess.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.ke-icon-emoticonss {
  background-image: url('/pc/static/plugins/emoticonss/images/emotion.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.ke-icon-fontsize {
  background-image: url('/pc/static/plugins/fontsize/images/fontsize.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.ke-icon-forecolor {
  background-image: url('/pc/static/plugins/fontcolor/images/fontcolor.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.ke-toolbar {
  border-bottom: 0px;
  background-color: #fff;
  padding: 2px 5px;
  text-align: left;
  overflow: hidden;
  zoom: 1;
}

.ke-statusbar {
  position: relative;
  background-color: #fff;
  border-top: 0px solid #cccccc;
  font-size: 0;
  line-height: 0;
  height: 0px;
  overflow: hidden;
  text-align: center;
  cursor: s-resize;
}

.ke-container {
  display: block;
  border: 0px;
  border-top: 1px solid #dcdee3;
  background-color: #fff;
  overflow: hidden;
  margin: 0;
  padding: 0;
}
.autoreply {
  position: absolute;
  left: 0;
  top: 0;
}
.el-dialog {
  width: 400px;
  height: 602px;
}
.levelPanel {
  height: 100%;
}
.page-body {
  display: flex;
  justify-content: center;
  /*align-items: center;*/
  .levelPanel {
    @extend .page-body;
  }
  .el-dialog__body {
    height: 76%;
    overflow-y: auto;
    .levelPanel {
      justify-content: left;
      .el-tree-node__children {
        position: absolute;
        top: 0;
        left: 150%;
        background: #fff;
      }
    }
  }
  .el-form {
    width: 50%;
    .el-select {
      width: 100%;
    }
  }
}
.pag /deep/.el-dialog__header {
  padding-top: 15px;
}
/deep/.el-dialog__body {
  padding-top: 10px !important;
}
.el-icon-plus {
  cursor: pointer;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(59, 149, 168, 1);
}
.guanzhu {
  font-size: 14px;
  position: absolute;
  top: 18px;
  left: 140px;
  color: #aeaebf;
}
/deep/.el-form-item.NavigationSettings {
  margin-bottom: 0 !important;
}
.NavigationSettings {
  margin-top: 20px;
  .el-form-item__content {
    margin-left: 0px !important;
  }
}
.removeRight {
  font-size: 12px;
  width: 8.16667%;
  text-align: center;
  color: #40a3b8;
}
.shezhiyin {
  position: absolute;
  top: 5px;
  left: 20px;
  /deep/.el-form-item__content {
    margin-left: 0 !important;
  }
}
.line {
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: rgba(41, 41, 51, 1);
  margin-top: 0;
  margin-bottom: 0;
  span {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(174, 174, 191, 1);
    margin-left: 8px;
  }
}
.biaoge_xu {
  width: 8.16667%;
  text-align: center;
  border: 0.5px solid #e4e4eb;
  border-bottom: none;
  border-right: none;
}
.biaoge_sheng {
  height: 101px;
  width: 100%;
  border: 0.5px solid #e4e4eb;
  border-bottom: none;
  border-right: none;
}
.biaoge_xia {
  border: 0.5px solid #e4e4eb;
}
.biaotou {
  color: #292933;
}
</style>
