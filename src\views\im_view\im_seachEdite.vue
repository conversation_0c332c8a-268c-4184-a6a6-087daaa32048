<template>
  <el-row type="flex" justify="start" align="top">
    <el-form ref="dynamicForm" :rules="rules" :model="dynamicForm" class="dynamic-form" label-width="100px">
      <dynimic-form v-for="(item,index) in fileds" :key="index" :item="item" :form="dynamicForm"></dynimic-form>
      <p style="padding-left:90px;">
        <el-button type="primary" @click="submitForm('dynamicForm')">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </p>
    </el-form>
  </el-row>
</template>

<script>
import dynimicForm from '@/components/serviceConfig/dynimicForm';
import { updateFormDate, serviceDetail } from '@/api/im_view/serviceConfigForm';
export default {
  components: {
    dynimicForm
  },
  data() {
    return {
      dynamicForm: {},
      fileds: [],
      rules: {},
      dataId: '',
      imFormData: {}
    };
  },
  created() {
    // this.getTemplate();
  },
  mounted() {
    const { dataId } = this.$route.query;
    this.dataId = dataId;
    this.serviceDetail(dataId);
  },
  methods: {
    serviceDetail(dataId) {
      serviceDetail(dataId).then(res => {
        // console.log('resres:', res);
        if (res.code === 1) {
          this.imFormData = res.data.imFormData;
          this.fileds = res.data.imFormDataExtend;
          console.log('this.fileds', this.fileds);

          this.fileds.forEach(item => {
            let key = '';
            if (item.fieldKey) {
              if (!isNaN(item.fieldKey)) {
                item.fieldKey = item.fieldKey.toString();
                key = item.fieldKey;
              } else {
                key = item.fieldKey;
              }
            } else {
              key = item.fieldName;
            }
            item.fieldType == 4
              ? this.$set(this.dynamicForm, key, JSON.parse(item.value))
              : this.$set(this.dynamicForm, key, item.value);
          });
          this.fileds.forEach(item => {
            if (
              typeof item.optionSettings === 'string' &&
              item.optionSettings
            ) {
              item.optionSettings = JSON.parse(item.optionSettings);
            }
          });
          // 0:单行输入,1:多行输入,2:下拉,3:单选,4:多选
          this.fileds.forEach(item => {
            if (
              item.optionSettings &&
              typeof item.optionSettings === 'string'
            ) {
              item.optionSettings = JSON.parse(item.optionSettings);
            }
            let arr = [];
            if (item.required) {
              switch (parseInt(item.fieldType)) {
                case 0:
                case 1:
                  arr = [{ required: true, message: '必填', trigger: 'blur' }];
                  break;
                case 2:
                case 3:
                  arr = [
                    { required: true, message: '必填', trigger: 'change' }
                  ];
                  break;
                case 4:
                  arr = [
                    {
                      type: 'array',
                      required: true,
                      message: '必填',
                      trigger: 'change'
                    }
                  ];
                  break;
              }
              this.$set(this.rules, item.fieldKey, arr);
            }
          });
        }
      });
    },
    submitForm(formName) {
      const that = this;
      this.$refs[formName].validate(valid => {
        if (valid) {
          const sendData = {
            dataId: this.dataId,
            imFormDataExtend: []
          };
          const arr = [];
          Object.keys(this[formName]).forEach(item => {
            arr.push({ fieldKey: item, value: this[formName][item] });
          });
          sendData.imFormDataExtend = arr;
          console.log('JSON.stringify(sendData):', JSON.stringify(sendData));

          updateFormDate(JSON.stringify(sendData)).then(res => {
            if (res.code !== 1) {
              this.$XyyMessage.warning(res.msg);
              return;
            }
            this.$XyyMessage.success('编辑成功');
            that.$store.dispatch('tagsView/delView', that.$route);
            that.$router.push({
              path: '/imServerTotal/list'
            });
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    cancel() {
      const that = this;
      that.$XyyMsg({
        title: '提示',
        content: '确认取消编辑？',
        onSuccess: function() {
          that.$store.dispatch('tagsView/delView', that.$route);
          that.$router.push({
            path: '/imServerTotal',
            query: { t: new Date().getTime() }
          });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.dynamic-form {
  margin: 20px auto;
}
</style>
