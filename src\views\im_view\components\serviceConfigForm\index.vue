<template>
  <div>
    <div id="service" class="xia-container">
      <header>服务总结</header>
      <el-form
        ref="dynamicForm"
        :rules="rules"
        :model="dynamicForm"
        class="dynamic-form"
        label-width="100px"
      >
        <dynimic-form v-for="(item,index) in fileds" :key="index" :item="item" :form="dynamicForm"></dynimic-form>
        <p style="padding-left:100px;">
          <el-button type="primary" @click="submitForm('dynamicForm')">提交</el-button>
        </p>
      </el-form>
    </div>
  </div>
</template>

<script>
import dynimicForm from '@/components/serviceConfig/dynimicForm';
import { getTemplate, saveFormDate } from '@/api/im_view/serviceConfigForm';
export default {
  name: 'ServiceConfigForm',
  components: {
    dynimicForm
  },
  data() {
    return {
      dynamicForm: {},
      fileds: [],
      rules: {},
      oldContainerID: '0', //上一个会话id
      currentContainerID: '0', // 当前会话id
      dynamicFormLocal: []
    };
  },
  computed: {
    containerId: function() {
      this.oldContainerID = this.currentContainerID;
      this.currentContainerID = this.$store.getters.containerid;
      return this.currentContainerID;
      // return this.$store.getters.containerid;
    }
  },
  watch: {
    containerId: function(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.saveFormContent();
        this.updateFormContent();
      }
    }
  },
  created() {
    this.getTemplate();
  },
  methods: {
    getTemplate() {
      getTemplate().then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.fileds = res.data;
        this.fileds.forEach(item => {
          let key = '';
          if (item.fieldKey) {
            if (!isNaN(item.fieldKey)) {
              item.fieldKey = item.fieldKey.toString();
              key = item.fieldKey;
            } else {
              key = item.fieldKey;
            }
          } else {
            key = item.fieldName;
          }
          item.fieldType == 4
            ? this.$set(this.dynamicForm, key, [])
            : this.$set(this.dynamicForm, key, '');
        });
        this.fileds.forEach(item => {
          if (typeof item.optionSettings === 'string' && item.optionSettings)
            item.optionSettings = JSON.parse(item.optionSettings);
        });
        // 0:单行输入,1:多行输入,2:下拉,3:单选,4:多选
        this.fileds.forEach(item => {
          if (item.optionSettings && typeof item.optionSettings === 'string')
            item.optionSettings = JSON.parse(item.optionSettings);
          let arr = [];
          if (item.required) {
            switch (parseInt(item.fieldType)) {
              case 0:
              case 1:
                arr = [{ required: true, message: '必填', trigger: 'blur' }];
                break;
              case 2:
              case 3:
                arr = [{ required: true, message: '必填', trigger: 'change' }];
                break;
              case 4:
                arr = [
                  {
                    type: 'array',
                    required: true,
                    message: '必填',
                    trigger: 'change'
                  }
                ];
                break;
            }
            this.$set(this.rules, item.fieldKey, arr);
          }
        });
      });
    },
    submitForm(formName) {
      if (!this.$store.getters.containerid || !this.$store.getters.khid) {
        this.$XyyMessage.error('请选择会话');
        return;
      }
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (
            !this.$store.getters.containerid ||
            !this.$store.getters.khid ||
            !this.$store.getters.kfid
          ) {
            this.$XyyMessage.warning('请选择会话');
            return;
          }
          const sendData = {
            imFormData: {
              formId: 1,
              dialogId: this.$store.getters.containerid,
              customerId: this.$store.getters.khid,
              kefuId: this.$store.getters.kfid
            },
            imFormDataExtend: []
          };
          const arr = [];
          Object.keys(this[formName]).forEach(item => {
            arr.push({ fieldKey: item, value: this[formName][item] });
          });
          sendData.imFormDataExtend = arr;
          saveFormDate(JSON.stringify(sendData)).then(res => {
            if (res.code !== 1) {
              this.$XyyMessage.warning(res.msg);
              return;
            }
            this.emits(
              this,
              'send',
              'im_result',
              this.$store.getters.containerid
            );
            this.$XyyMessage.success('保存成功');
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 存
    saveFormContent() {
      this.fileds.forEach(item => {
        Object.keys(this.dynamicForm).forEach(itemForm => {
          if (item.fieldKey === itemForm) {
            item.content = this.dynamicForm[itemForm];
          }
        });
      });

      if (this.oldContainerID) {
        this.$store.commit('updataEditDynamicFormData', {
          key: this.oldContainerID,
          value: JSON.parse(JSON.stringify(this.fileds))
        });
      }

      this.fileds.forEach(item => {
        let key = '';
        if (item.fieldKey) {
          if (!isNaN(item.fieldKey)) {
            item.fieldKey = item.fieldKey.toString();
            key = item.fieldKey;
          } else {
            key = item.fieldKey;
          }
        } else {
          key = item.fieldName;
        }
        item.fieldType == 4
          ? this.$set(this.dynamicForm, key, [])
          : this.$set(this.dynamicForm, key, '');
      });
    },
    // 取
    updateFormContent() {
      this.dynamicFormLocal = this.$store.getters.editDynamicFormData(
        this.currentContainerID
      );

      if (this.dynamicFormLocal) {
        this.fileds = [];
        this.fileds = this.dynamicFormLocal;
        this.fileds.forEach(item => {
          let key = '';
          if (item.fieldKey) {
            if (!isNaN(item.fieldKey)) {
              item.fieldKey = item.fieldKey.toString();
              key = item.fieldKey;
            } else {
              key = item.fieldKey;
            }
          } else {
            key = item.fieldName;
          }
          this.$set(this.dynamicForm, key, item.content);
        });
      }
    }
  }
};
</script>

<style lang="scss"  scoped>
.xia-container {
  margin-bottom: 30px;
}
.xia-container header {
  padding: 15px 20px 20px 20px;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  color: rgba(48, 49, 51, 1);
  line-height: 22px;
}
.xia-container form {
  /*padding-right: 48px;*/
}
el-input {
  height: 36px;
  /* line-height:36px; */
}
.el-input input {
  height: 100% !important;
}
.el-radio__input.is-checked .el-radio__inner {
  border-color: #3b95a8 !important;
  background: #3b95a8 !important;
}

.tab_clom {
  display: flex;
  flex-direction: column;
}

.tab_row {
  display: flex;
  flex-direction: row;
  min-height: 30px;
}

.user_title {
  font-size: 14px;
  color: rgb(48, 49, 51);
}

.user_datail {
  margin-left: 10px;
  font-size: 14px;
  color: #575766;
}

/deep/.el-form-item__label {
  font-weight: normal;
}
/deep/.el-button--text {
  border: none !important;
}
/deep/.el-button {
  padding-left: 15px;
  padding-right: 15px;
  font-size: 14px;
  color: rgba(87, 87, 102, 1);
  height: 36px;
  border-radius: 2px;
  border: 1px solid rgba(228, 228, 235, 1);
}
/deep/.el-button:focus,
.el-button:hover {
  font-size: 14px;
  color: rgba(87, 87, 102, 1);
  height: 36px;
  border-radius: 2px;
  border: 1px solid rgba(228, 228, 235, 1);
}
/deep/.el-button--primary {
  color: #fff;
  height: 36px;
  background: rgba(59, 149, 168, 1);
  border-radius: 2px;
}
/deep/.el-button--primary:focus,
.el-button--primary:hover {
  height: 36px;
  background: rgba(59, 149, 168, 1);
  border-radius: 2px;
  color: #fff;
}
/deep/.el-radio__input.is-checked + .el-radio__label {
  color: #3b95a8;
}
/deep/.el-radio-group,
.el-checkbox-group {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -moz-justify-content: flex-start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -moz-align-items: center;
  -webkit-align-items: center;
  align-items: center;
}
/deep/.el-radio,
.el-checkbox {
  height: 36px;
  line-height: 36px;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -moz-justify-content: flex-start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -moz-align-items: center;
  -webkit-align-items: center;
  align-items: center;
}
/deep/.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: rgba(59, 149, 168, 1);
  border-color: rgba(59, 149, 168, 1);
}
/deep/.el-checkbox__inner:hover {
  border-color: rgba(59, 149, 168, 1);
}
/deep/.el-checkbox__input.is-checked + .el-checkbox__label {
  color: rgba(59, 149, 168, 1);
}
/deep/.el-radio__inner:hover {
  border-color: rgba(59, 149, 168, 1);
}
/deep/.el-form-item {
  margin-bottom: 16px;
}
/deep/.el-form-item__content {
  line-height: 30px;
}
/deep/.el-form-item__error {
  padding-top: 0;
}
</style>
