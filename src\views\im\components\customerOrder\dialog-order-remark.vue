<template>
  <!-- 订单备注 -->
  <el-dialog class="dialog-order-remark-container" width="480px" append-to-body destroy-on-close :show-close="false"
    :close-on-press-escape="false" :close-on-click-modal="false" :visible.sync="dialogVisible" v-if="dialogVisible">
    <div class="dialog-body">
      <div class="dialog-title">
        <span class="title-txt">{{ dialogTitle }}</span>
        <i class="el-icon-close" @click="handleCloseDialog"></i>
      </div>
      <div class="dialog-content">
        <div class="content-box">
          <template v-if="viewType == 'view'">
            <div class="type-view-content">
              <div class="remark merchant">
                <span class="side">商家</span><span class="txt">{{ viewData.sellerRemark }}</span>
              </div>
              <div class="remark custom">
                <span class="side">客户</span><span class="txt">{{ viewData.customerRemark }}</span>
              </div>
            </div>
          </template>
          <template v-else-if="viewType == 'add'">
            <div class="type-add-content">
              <div class="add-form-item" style="align-items: center;">
                <div class="form-item-label">快速备注语</div>
                <div class="form-item-content">
                  <el-select v-model="viewModel.remarkQuick" :popper-append-to-body="false" placeholder="请选择"
                    @change="handleRemarkQuickChange">
                    <el-option v-for="item in remarkQuickList" :key="item.value" :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </div>
              </div>
              <div class="add-form-item">
                <div class="form-item-label">备注</div>
                <div class="form-item-content">
                  <el-input v-model="viewModel.remarkInput" type="textarea" :rows="6" placeholder="请输入" :maxlength="200"
                    show-word-limit></el-input>
                </div>
              </div>
              <div class="add-form-item">
                <div class="form-item-label"></div>
                <div class="form-item-content">
                  <div class="self-checkbox">
                    <el-checkbox v-model="viewModel.remarkVisible" size="medium"></el-checkbox>
                    <span class="txt-checkbox"
                      @click="viewModel.remarkVisible = !viewModel.remarkVisible">客户可见(勾选后，客户可在订单上看到备注内容，请仔细核查备注内容)</span>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="dialog-footer">
        <template v-if="viewType == 'view'">
          <el-button class="btn-primary" @click="handleCloseDialog">关闭</el-button>
        </template>
        <template v-else-if="viewType == 'add'">
          <el-button class="btn-cancel" @click="handleCloseDialog">取消</el-button>
          <el-button :class="['btn-submit', submitBtnDisabled ? 'disabled' : '']"
            :loading="viewModel.remarkSubmitLoading" @click="handleSubmitDialog">确定</el-button>
        </template>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { querySelectRemarkDict, submitAddRemark, queryRemark } from '@/api/im_view/customeOrder';
export default {
  name: "dialog-order-remark",
  computed: {
    dialogTitle: function () {
      let title = ""
      switch (this.viewType) {
        case 'view':
          title = "查看订单备注"
          break;
        case 'add':
          title = "添加订单备注"
          break;
      }
      return title
    },
    submitBtnDisabled: function () {
      // return !this.viewModel.remarkInput.length
      return !this.viewModel.remarkInput.trim().length
    }
  },
  data() {
    return {
      viewType: '', // view:查看;edit:新增
      orderNo: '',

      dialogVisible: false,

      // 查看模式
      viewData: {},

      // 新增模式
      remarkQuickList: [], // 快捷语字典
      viewModel: {
        remarkQuick: '', // 快捷语
        remarkInput: '', // 自定义备注输入
        remarkVisible: true, // 客户是否可见
        remarkSubmitLoading: false
      },
    }
  },
  methods: {
    init({ viewType, orderNo }) {
      this.viewType = viewType
      this.orderNo = orderNo

      this.viewData = {}

      this.remarkQuickList = []
      this.viewModel.remarkQuick = ''
      this.viewModel.remarkInput = ''
      this.viewModel.remarkVisible = true
      this.viewModel.remarkSubmitLoading = false

      if (this.viewType == 'view') {
        this.fetchRemark()
      } else if (this.viewType == 'add') {
        this.queryRemarkDict()
      }

      this.$nextTick(() => {
        this.dialogVisible = true;
      })
    },

    // 查询订单备注信息
    async fetchRemark() {
      try {
        const { code, data, msg } = await queryRemark({
          orderNo: this.orderNo
        })
        if (code == 1) {
          if (data) {
            this.viewData = data
          }
        } else {
          throw new Error(msg)
        }
      } catch (e) {
        this.$XyyMessage.error(e.message);
      }
    },

    // 查询订单备注下拉字典
    async queryRemarkDict() {
      try {
        const { code, data, msg } = await querySelectRemarkDict()
        if (code == 1) {
          if (Array.isArray(data) && data.length) {
            this.remarkQuickList = data.map((item, index) => {
              return {
                label: item,
                value: index
              }
            })
          }
        } else {
          throw new Error(msg)
        }
      } catch (e) {
        console.log(e.message)
      }
    },
    // 快捷语select的change
    handleRemarkQuickChange(value) {
      this.viewModel.remarkInput = (this.viewModel.remarkInput.length ? (this.viewModel.remarkInput + ';') : ('')) + this.remarkQuickList.find(l => l.value == value).label
    },
    // 提交弹窗
    async handleSubmitDialog() {
      if (this.viewModel.remarkSubmitLoading) {
        return false
      }

      if (this.submitBtnDisabled) {
        // this.$XyyMessage.warning("请输入备注内容");
        return false
      }

      try {
        this.viewModel.remarkSubmitLoading = true
        const { code, data, msg } = await submitAddRemark({
          orderNo: this.orderNo,
          sellerRemark: this.viewModel.remarkInput,
          isVisible: this.viewModel.remarkVisible ? 1 : 0, // 0不可见,1可见
        });
        if (code == 1) {
          this.$XyyMessage.success('操作成功');
          this.dialogVisible = false;
        } else {
          throw new Error(msg)
        }
      } catch (e) {
        this.$XyyMessage.error(e.message);
      } finally {
        this.viewModel.remarkSubmitLoading = false
      }
    },

    // 关闭弹窗
    handleCloseDialog() {
      this.dialogVisible = false;
    },
  }
}
</script>

<style lang="scss" scoped>
.dialog-order-remark-container {
  /deep/ {
    .el-dialog {
      border-radius: 4px;

      .el-dialog__header {
        display: none;
      }

      .el-dialog__body {
        width: 100%;
        padding: 20px;
        box-sizing: border-box;

        .dialog-body {
          width: 100%;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: stretch;

          .dialog-title {
            width: 100%;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .title-txt {
              flex-grow: 1;
              color: #222222;
              font-size: 16px;
              font-weight: 400;
            }

            .el-icon-close {
              flex-shrink: 0;
              cursor: pointer;
              font-size: 16px;
              font-weight: bold;

              &:hover {
                opacity: 0.6;
              }
            }
          }

          .dialog-content {
            width: 100%;
            margin-bottom: 20px;

            .content-box {
              width: 100%;
              display: flex;
              flex-direction: column;
              justify-content: flex-start;
              align-items: stretch;

              .type-view-content {
                width: 100%;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: stretch;

                .remark {
                  width: 100%;
                  padding: 16px;
                  box-sizing: border-box;
                  margin-bottom: 8px;
                  background-color: #F5F7F9;
                  border-radius: 4px;

                  &:last-child {
                    margin-bottom: 0;
                  }

                  &.merchant {
                    .side {
                      background-color: #00B955;
                    }
                  }

                  &.custom {
                    .side {
                      background-color: #FB6330;
                    }
                  }

                  .side {
                    display: inline-flex;
                    justify-content: center;
                    align-items: center;
                    color: #FFFFFF;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 1;
                    border-radius: 2px;
                    padding: 3px 6px;
                    box-sizing: border-box;
                    margin-right: 8px;
                  }

                  .txt {
                    line-height: 1.5;
                    color: #222222;
                    font-size: 14px;
                    font-weight: 400;
                  }
                }
              }

              .type-add-content {
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: stretch;

                .add-form-item {
                  width: 100%;
                  margin-bottom: 16px;
                  display: flex;
                  justify-content: flex-start;
                  align-items: flex-start;

                  &:last-child {
                    margin-bottom: 0;
                  }

                  .form-item-label {
                    flex-shrink: 0;
                    width: 70px;
                    margin-right: 12px;
                    text-align: right;
                    color: #333333;
                    font-size: 14px;
                    font-weight: 400;
                  }

                  .form-item-content {
                    flex-grow: 1;
                    display: flex;
                    justify-content: flex-start;
                    align-items: flex-start;

                    .el-select {
                      flex-grow: 1;

                      /deep/ {
                        .el-input__inner {
                          &:focus {
                            border-color: #3B95A8;
                          }
                        }

                        .el-select-dropdown {
                          .el-select-dropdown__item {
                            &.selected {
                              color: #3B95A8;
                            }

                            &:hover {
                              background-color: #F1FFF7;
                            }
                          }
                        }
                      }
                    }

                    .el-textarea {
                      flex-grow: 1;

                      .el-textarea__inner {
                        &:focus {
                          border-color: #3B95A8;
                        }
                      }
                    }

                    .self-checkbox {
                      flex-grow: 1;
                      display: flex;
                      justify-content: flex-start;
                      align-items: flex-start;

                      .el-checkbox {
                        margin-right: 8px;

                        /deep/ {
                          .el-checkbox__input {
                            &.is-focus {
                              border-color: #3B95A8;
                            }
                          }
                        }

                        &.is-checked {
                          /deep/ {
                            .el-checkbox__inner {
                              background-color: #3B95A8;
                            }
                          }
                        }
                      }

                      .txt-checkbox {
                        cursor: pointer;
                        user-select: none;
                        color: #999999;
                        font-size: 13px;
                        font-weight: 400;
                      }
                    }
                  }
                }
              }
            }
          }

          .dialog-footer {
            width: 100%;
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .el-button {
              padding: 9px 18px;
              border-radius: 4px;
              font-size: 14px;
              font-weight: 500;

              &:hover {
                opacity: 0.6;
              }

              &:active {
                opacity: 1;
              }

              +.el-button {
                margin-left: 16px;
              }

              &.btn-primary {
                color: #ffffff;
                background-color: #3B95A8;
                border-color: #3B95A8;
              }

              &.btn-cancel {
                color: #222222;
                background-color: #fff;
                border-color: #D4D9DD;
              }

              &.btn-submit {
                color: #ffffff;
                background-color: #00b955;
                border-color: #00b955;

                &.disabled {
                  color: #666666;
                  background-color: #ECEEF0;
                  border-color: #ECEEF0;

                  &:hover {
                    opacity: 1;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>