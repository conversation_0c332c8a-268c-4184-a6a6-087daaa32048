<template>
  <div class="temConfiguration">
    <xyy-list-page>
      <template slot="header">
        <xyy-button @click="handerImport">
          <span class="el-icon-download"></span>导入
        </xyy-button>
      </template>
      <template slot="body">
        <div class="tableTree">
          <div class="tableTreeRight tableTreeTitle">
            <span>
              <div style="padding-left: 16px;">问题分类</div>
            </span>
            <span :style="{width: tableTreeTwo}" ref="tableTreeTwo" class="tableTreeTwo">
              <span class="tableTreeTwoSpan1">表单模板</span>
              <span class="tableTreeTwoSpan2">操作</span>
            </span>
          </div>
          <div class="tableTreeBody">
            <div class="tree-container" ref="treeContainer">
              <el-tree
                :data="dataListNew"
                :indent="0"
                node-key="id"
                default-expand-all
                :expand-on-click-node="false"
              >
                <el-row class="custom-tree-node" slot-scope="{ node, data }">
                  <div class="tableTreeRight" ref="tableTreeRight">
                    <span>{{ data.typeName }}</span>
                    <!--<span>{{ data.templateCode }}</span>-->
                    <span :style="{width: tableTreeTwo}" ref="tableTreeTwo" class="tableTreeTwo">
                      <span class="tableTreeTwoSpan1">{{ data.templateName }}</span>
                      <span class="tableTreeTwoSpan2">
                        <el-button
                          type="text"
                          size="mini"
                          @click="() => handleAdd(node, data)"
                        >添加下一级</el-button>
                        <el-button type="text" size="mini" @click="() => handleEdit(node, data)">修改</el-button>
                        <el-button
                          type="text"
                          size="mini"
                          @click="() => handleDelete(node, data)"
                        >删除</el-button>
                      </span>
                    </span>
                  </div>
                </el-row>
              </el-tree>
              <div class="addLevel">
                <span class="addLevelList" @click="handleAdd">
                  <span class="el-icon-plus"></span>添加一级选项
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="submitBtnTemplate">
          <el-button size="small" @click="handerCancel">取消</el-button>
          <el-button type="primary" size="small" @click="handerSave">保存</el-button>
        </div>
      </template>
    </xyy-list-page>
    <!-- 点击导入按钮弹出框-开始-->
    <el-dialog
      :visible="centerDialogImport"
      :close-on-click-modal="false"
      :show-close="!uploading"
      title="提示"
      custom-class="import-box"
      top="0"
      @close="close"
      @open="open"
    >
      <span ref="content" class="import-content" v-html="content"></span>
      <dl v-show="completed" class="import-result">
        <dt>导入数据项{{ result.total }}</dt>
        <dt>成功{{ result.success }}</dt>
        <dt>失败{{ result.fail }}</dt>
      </dl>
      <el-upload
        ref="importFile"
        :before-upload="beforeUpload"
        :on-progress="handleProgress"
        :on-success="handleSuccess"
        :on-error="handleError"
        :action="url"
        accept=".xls, .xlsx"
      >
        <el-button v-show="!uploading && !completed" size="small" type="primary">上传文件</el-button>
        <div slot="file" slot-scope="{file}">
          <el-progress :percentage="percent"></el-progress>
          <span class="file-name">{{ file.name }}</span>
          <el-button size="small" type="primary" @click="abortFile(file)">取消上传</el-button>
        </div>
      </el-upload>
      <el-button v-show="completed" size="small" type="primary" @click="done">确定</el-button>
    </el-dialog>

    <!-- 点击导入按钮弹出框-结束-->
    <!-- 修改下一级弹框-开始-->
    <el-dialog title="添加问题分类" :visible.sync="dialogFormAdd">
      <el-form ref="formAdd" :model="formAdd" label-width="80px" :rules="addRules">
        <el-form-item label="选项名称" prop="typeName">
          <el-input v-model="formAdd.typeName"></el-input>
        </el-form-item>
        <el-form-item label="模板" prop="templateName">
          <el-select v-model="formAdd.templateName" placeholder>
            <el-option
              v-for="item in repairOrderTemp"
              :key="item.id"
              :label="item.name"
              :value="item.versionCode"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addCancel">取 消</el-button>
        <el-button type="primary" @click="addDetermine('formAdd')">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 修改下一级弹框-结束-->
    <!-- 添加问题分类弹框-开始-->
    <el-dialog title="编辑问题分类" :visible.sync="dialogFormVisible">
      <el-form ref="formAditor" :model="formAditor" label-width="80px" :rules="addRules">
        <el-form-item label="选项名称" prop="typeName">
          <el-input v-model="formAditor.typeName"></el-input>
        </el-form-item>
        <el-form-item label="模板" prop="templateName">
          <el-select v-model="formAditor.templateName" placeholder filterable>
            <el-option
              v-for="item in repairOrderTemp"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editorCancel">取 消</el-button>
        <el-button type="primary" @click="editorDetermine('formAditor')">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 添加问题分类弹框-结束-->
  </div>
</template>

<script>
import XyyListPage from '../../components/xyy/xyy-list-page/index';
import XyyButton from '../../components/xyy/xyy-button/index';
import {
  getProblemClassTree,
  deleteSaved,
  getTemplate,
  judgeProcessClass,
  UploadFile,
  UploadFileFailure
} from '@/api/QClassification/index';
export default {
  name: 'configuration',
  components: { XyyButton, XyyListPage },
  data() {
    return {
      dataListNew: [],
      sourceData: [],
      tableTreeTwo: '',
      repairOrderTemp: [], // 工单模板下拉
      // ------------以下为导入弹框变量
      dialogFormAdd: false,
      fileList: [],
      fileShow: true, // 控制提交文件按钮显示隐藏
      fileName: '', // 文件名字
      successlFail: true, // 上传成功，未上传成功的显示
      url: process.env.BASE_API + '/businessType/analysisExcel',
      uploading: false, // 上传标识
      percent: 0, // 上传进度
      completed: false, // 导入完成
      content: '',
      result: {
        total: 0,
        success: 0,
        fail: 0
      }, // 导入结果
      datas: [], // 导入数据
      downloadUrl: '', // 失败数据地址
      // ------------以下为添加问题分类变量
      formAdd: {
        typeName: '',
        templateName: ''
      },
      rowList: [], // 存放添加后的源数据
      rowListNew: [],
      nodeList: [], // 节点存储
      currentRow: {}, // 当前节点储存
      saveValueList: [],
      codeEach: 0,
      // ------------以下为修改问题分类变量--------
      dialogFormVisible: false, // 编辑问题分类 弹框控制显示隐藏
      centerDialogImport: false, // 点击导入按钮弹出框
      formAditor: {
        // 编辑问题表单
        typeName: '', // 选项名称
        templateName: '' // 模板
      },
      // 编辑问题分类 校验
      addRules: {
        typeName: [
          { required: true, message: '选项名称为必填项', trigger: 'blur' }
        ]
      }
    };
  },
  created() {
    this.getDate();
    this.getWidth();
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        let treeContainer = this.$refs.treeContainer.clientWidth; // 最外边的长度
        this.tableTreeTwo = (treeContainer / 4) * 2 + 'px';
      })();
    };
  },
  methods: {
    // 点击导入按钮事件
    handerImport() {
      // 判断：当前级联的选项数据是否被设置为流程分支的判断条件
      judgeProcessClass().then(res => {
        if (res.data === false) {
          // 否 出弹层
          this.centerDialogImport = true;
          this.content = `请先<a class="DownloadTem">下载导入模板</a>，再点击上传按钮进行数据上传`;
          this.uploading = false;
          this.percent = 0;
          this.completed = false;
          this.result = {
            total: 0,
            success: 0,
            fail: 0
          };
          this.datas = [];
          this.downloadUrl = '';
        } else if (res.data === true) {
          // 是  提示：无法进行导入操作（级联选项已作为流程分支条件，导入替换新数据时会导致现有流程异常，建议先解除绑定关系）
          this.$alert(
            '无法进行导入操作（级联选项已作为流程分支条件，导入替换新数据时会导致现有流程异常，建议先解除绑定关系）',
            '提示',
            {
              confirmButtonText: '确定',
              callback: action => {}
            }
          );
        }
      });
    },
    // // 上传事件
    // uploadRequest(parameter) {
    //   let formData = new FormData()
    //   formData.append('file', parameter.file)
    //   UploadFile(formData).then(res => {
    //     this.result = res.data
    //   });
    // },
    /**
     * 上传中回调
     */
    handleProgress(event, file) {
      if (file) {
        this.uploading = true;
        this.percent = Number(event.percent.toFixed(0));
        if (this.percent > 99) this.percent = 99;
      }
    },
    /**
     * 取消上传回调
     */
    abortFile(file) {
      this.$refs['importFile'].abort(file);
      this.$refs['importFile'].uploadFiles = [];
      this.uploading = false;
    },
    /**
     * 上传成功回调
     */
    handleSuccess(res, file) {
      if (res.code === 1) {
        this.percent = 100;
        this.completed = true;
        this.content = '上传完成';
        this.result = {
          total: res.data.totalNum,
          success: res.data.okNum,
          fail: res.data.failNum
        };
        this.datas = res.data.options;
        if (res.data.failNum) {
          this.downloadUrl = res.data.failUrl;
        }
        this.$refs['importFile'].uploadFiles = [];
      } else {
        this.$XyyMessage.error(res.msg);
        this.$refs['importFile'].uploadFiles = [];
      }
      this.uploading = false;
    },
    /**
     * 上传失败回调
     */
    handleError(res) {
      this.$refs['importFile'].uploadFiles = [];
      this.uploading = false;
      this.$XyyMessage.error(`文件上传失败`);
    },
    /**
     * 上传之前
     */
    beforeUpload(file) {
      if (file.type) {
        if (
          ![
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          ].includes(file.type)
        ) {
          this.$XyyMessage.error(`只能选择.xls或.xlsx格式的文件`);
          return false;
        }
      }
    },
    /**
     * 关闭回调
     */
    close() {
      this.centerDialogImport = false;
    },
    /**
     * 确定操作
     */
    done() {
      if (this.result.fail) {
        // 下载操作
        const url = `${process.env.BASE_API}${this.downloadUrl}`;
        this.download(url);
        this.close();
      } else {
        // 保存数据
        this.$emit('importCallback', this.datas);
        this.close();
      }
    },
    /**
     * 下载方法
     */
    download(url) {
      const a = document.createElement('a');
      a.href = url;
      a.click();
    },
    open() {
      this.$nextTick(() => {
        const a = this.$refs['content'].children[0];
        const url = `http://upload.ybm100.com/G2/M00/10/C2/CiFJIl3qIzyAZw2hAAAhdyDEkE803.xlsx`;
        a.href = url;
      });
    },

    // 表单-修改事件
    handleEdit(node, row) {
      this.rowList = [];
      this.dialogFormAdd = true;
      getTemplate().then(res => {
        this.repairOrderTemp = res.data;
      });
      this.nodeList = Object.assign({}, node);
      this.currentRow = Object.assign({}, row);
      this.formAdd.typeName = row.typeName;
      this.formAdd.templateName = row.templateName;
    },
    // 修改弹框-确定事件
    addDetermine(name) {
      this.$refs[name].validate(valid => {
        if (valid) {
          if (this.nodeList.length === 0) {
            // 点击的是添加一级选项
            // this.rowList.push({ 'typeName': this.formAdd.typeName, 'templateCode': this.formAdd.templateCode})
          } else {
            // 获取整个父级数据
            this.getParent(this.nodeList);
            // 获取父级中选中的那个数据
            // this.rowListNew = JSON.parse(JSON.stringify(this.rowList)); // 对象的深拷贝 为了不影响从后台获取的数据；
            this.getSelectedData(
              this.rowList,
              this.currentRow.id,
              this.currentRow.codeEach
            );
          }
          this.saveValueList.push(this.rowList);
          this.dialogFormAdd = false;
        } else {
          // this.$message('选项名称为必填项')
        }
      });
    },

    // 表单-添加下一级事件
    handleAdd(node, row) {
      this.rowList = [];
      this.nodeList = [];
      this.currentRow = [];
      this.dialogFormVisible = true;
      getTemplate().then(res => {
        this.repairOrderTemp = res.data;
      });
      if (row === undefined) {
        this.formAditor.typeName = '';
        this.formAditor.templateName = '';
      } else {
        // 添加下一级
        this.nodeList = Object.assign({}, node);
        this.currentRow = Object.assign({}, row);
        this.formAditor.typeName = row.typeName;
        this.formAditor.templateName = row.templateName;
      }
    },
    // 添加下一级-确定事件
    editorDetermine(name) {
      this.$refs[name].validate(valid => {
        if (valid) {
          if (this.nodeList.length === 0) {
            // 点击的是添加一级选项
            let obj = {
              codeEach: this.codeEach++,
              typeName: this.formAditor.typeName,
              templateCode: '',
              templateName: this.formAditor.templateName,
              deleted: 0,
              children: []
            };
            this.dataListNew.push(obj);
            this.saveValueList.push(obj);
          } else {
            // 获取整个父级数据
            this.getParent(this.nodeList);
            // 获取父级中选中的那个数据
            // this.rowListNew = JSON.parse(JSON.stringify(this.rowList)); // 对象的深拷贝 为了不影响从后台获取的数据；
            this.AddSelectedData(
              this.rowList,
              this.currentRow.id,
              this.currentRow.codeEach
            );
            console.log('this.rowList');
            console.log(this.rowList);
          }
          this.saveValueList.push(this.rowList[0]);
          this.dialogFormVisible = false;
        } else {
          // this.$message('选项名称为必填项')
        }
      });
    },

    // 保存事件
    handerSave() {
      if (this.datas.length === 0) {
        // 验证通过提交数据
        deleteSaved(this.saveValueList).then(res => {
          this.$message('添加成功');
          this.getDate();
        });
      } else {
        this.saveValueList = this.datas;
        // 验证通过提交数据
        deleteSaved(this.saveValueList).then(res => {
          this.$message('添加成功');
          // 添加成功后 所有数据清空
          this.saveValueList = [];
        });
      }
    },
    // 取消事件
    handerCancel() {
      this.datas = [];
      getProblemClassTree().then(res => {
        this.dataListNew = res.data;
      });
    },
    // 获取选中的那个数据；并修改值；
    getSelectedData(data, id, codeEach) {
      let that = this;
      // 判断data是否是数组；
      if (Array.isArray(data)) {
        data.forEach(function(item, index) {
          if (item.id !== id) {
            that.getSelectedData(item.children, id, codeEach);
          } else if (item.codeEach !== codeEach) {
            that.getSelectedData(item.children, id, codeEach);
          } else {
            Object.assign(item, {
              typeName: that.formAdd.typeName,
              templateName: that.formAdd.templateName
            });
          }
        });
      }
    },
    // 获取选中的那个数据；并添加下一级；
    AddSelectedData(data, id, codeEach) {
      let that = this;
      // 判断data是否是数组；
      if (Array.isArray(data)) {
        data.forEach(function(item, index) {
          if (item.id !== id) {
            that.AddSelectedData(item.children, id, codeEach);
          } else if (item.codeEach !== codeEach) {
            that.AddSelectedData(item.children, id, codeEach);
          } else {
            let obj = {
              codeEach: that.codeEach++,
              typeName: that.formAditor.typeName,
              templateCode: '',
              templateName: that.formAditor.templateName,
              deleted: 0,
              children: []
            };
            if (!item.children || item.children.length === 0) {
              that.$set(item, 'children', []);
            }
            item.children.push(obj);
          }
        });
      }
    },
    // 获取选中的那个数据-删除；
    getSelectedRemoveData(node, data, id, codeEach) {
      let that = this;
      // 判断data是否是数组；
      if (Array.isArray(data)) {
        data.forEach(function(item, index) {
          if (item.id !== id) {
            that.getSelectedRemoveData(node, item.children, id, codeEach);
          } else if (item.codeEach !== codeEach) {
            that.getSelectedRemoveData(node, item.children, id, codeEach);
          } else {
            item.deleted = 1;
          }
        });
      }
    },
    // 获取选中的那个数据-页面上删除
    getSelectedRemovePageData(node, data, id, codeEach) {
      let that = this;
      // 判断data是否是数组；
      if (Array.isArray(data)) {
        data.forEach(function(item, index) {
          if (item.id !== id) {
            that.getSelectedRemovePageData(node, item.children, id, codeEach);
          } else if (item.codeEach !== codeEach) {
            that.getSelectedRemovePageData(node, item.children, id, codeEach);
          } else {
            console.log(data);
            const parent = node.parent;
            const children = parent.data.children || parent.data;
            const index = children.findIndex(d => {
              if (!d.codeEach) {
                d.id === data.id;
              } else {
                d.codeEach === data.codeEach;
              }
            });
            children.splice(index, 1);
          }
        });
      }
    },
    // 将父级的数据全部添加到一个数组中；
    getParent(node) {
      this.rowList = [];
      // 判断当前节点是否有父节点，并且父节点上的data不能是数组
      if (node.parent && !Array.isArray(node.parent.data)) {
        // 将父节点上data的menuid存储在 `parentlist` 里
        node.parent.data instanceof Object &&
          this.rowList.push(node.parent.data);
        // 递归调用 父节点上有父节点
        this.getParent(node.parent);
        // 当父节点上data是数组时点击对象为当前tree最高目录 并且parentList必须是0
      } else if (node.parent && Array.isArray(node.parent.data)) {
        // 存在父节点 但是父节点的data是数组；
        // 将父节点上data的menuid存储在 `parentlist` 里
        node.data instanceof Object && this.rowList.push(node.data);
      } else if (this.rowList.length === 0) {
        // 存储当前节点到parentList
        if (node.length === 0) {
          // 点击添加最上层
          this.rowList.push(this.formAdd);
        } else {
          this.rowList.push(node.data);
        }
      }
    },

    // 添加弹框-取消事件
    addCancel() {
      this.dialogFormAdd = false;
    },

    // 表单-删除事件
    handleDelete(node, row) {
      this.nodeList = Object.assign({}, node);
      this.currentRow = Object.assign({}, row);
      // 获取整个父级数据
      this.getParent(this.nodeList);
      // 获取父级中选中的那个数据
      this.getSelectedRemoveData(
        this.nodeList,
        this.rowList,
        this.currentRow.id,
        this.currentRow.codeEach
      );
      this.saveValueList.push(this.rowList);
      // 获取父级中选中的那个数据-页面上删除；
      this.rowListNew = JSON.parse(JSON.stringify(this.rowList));
      // this.getSelectedRemovePageData(this.nodeList, this.rowListNew, this.currentRow.id, this.currentRow.codeEach)
    },
    // 修改弹框-取消事件
    editorCancel() {
      this.dialogFormVisible = false;
    },

    // 获取表单数据
    getDate() {
      getProblemClassTree().then(res => {
        this.dataListNew = res.data;
        // this.sourceData = JSON.parse(JSON.stringify(res.data))
        // this.sourceData = this.deepCopy(res.data)
      });
    },
    deepCopy(obj) {
      var that = this;
      var result = Array.isArray(obj) ? [] : {};
      for (var key in obj) {
        if (obj.hasOwnProperty(key)) {
          if (typeof obj[key] === 'object' && obj[key] !== null) {
            result[key] = that.deepCopy(obj[key]); // 递归复制
          } else {
            result[key] = obj[key];
          }
        }
      }
      return result;
    },
    // 获取页面div的宽度
    getWidth() {
      this.$nextTick(() => {
        let treeContainer = this.$refs.treeContainer.clientWidth; // 最外边的长度
        this.tableTreeTwo = (treeContainer / 4) * 2 + 'px';
      });
    }
  }
};
</script>
<style lang="scss">
.tableTreeBody {
  .addLevel {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #ccc;
    padding-left: 16px;
    font-size: 14px;
    color: rgba(64, 163, 184, 1);
    .addLevelList {
      cursor: pointer;
    }
  }
}
.tree-container {
  height: auto;
  .el-row::before {
    display: contents !important;
  }
  .el-tree-node__content {
    height: 40px;
    &:hover {
      background: #fff;
    }
  }
}
.tableTreeTitle {
  height: 40px;
  background: rgba(238, 238, 238, 1);
  line-height: 40px;
  font-size: 14px;
}
.tree-container {
  border: none !important;
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    font-size: 14px;
    padding-right: 8px;
    span {
      /*width: 30%;*/
      display: inline-block;
    }
  }
  .el-tree-node__expand-icon.expanded {
    transform: rotate(0deg);
  }
  .el-icon-caret-right:before {
    background: url('../../assets/repeat/icon_open.png') no-repeat;
    content: '';
    display: block;
    width: 18px;
    height: 18px;
    font-size: 18px;
    background-size: 18px;
  }
}
.tree-container /deep/ {
  .el-tree > .el-tree-node:after {
    border-top: none !important;
  }
  .el-tree-node {
    position: relative;
    padding-left: 16px;
  }
  //节点有间隙，隐藏掉展开按钮就好了,如果觉得空隙没事可以删掉
  .el-tree-node__expand-icon.is-leaf {
    display: none;
  }
  .el-tree-node__children {
    padding-left: 16px;
  }

  .el-tree-node :last-child:before {
    height: 38px;
  }

  .el-tree > .el-tree-node:before {
    border-left: none;
  }

  .el-tree > .el-tree-node:after {
    border-top: none;
  }

  .el-tree-node:before {
    content: '';
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:after {
    content: '';
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:before {
    border-left: 1px dashed rgba(228, 228, 235, 1);
    bottom: 0px;
    height: 100%;
    top: -26px;
    width: 1px;
  }

  .el-tree-node:after {
    border-top: 1px dashed rgba(228, 228, 235, 1);
    height: 20px;
    top: 19px;
    width: 24px;
  }
}
.tableTree {
  border: 1px solid #ccc;
  border-bottom: 0;
  .tree-container {
    //树的parent，样式自定
    width: 100% !important;
    .el-tree-node {
      border-bottom: 1px solid #ccc;
      padding-bottom: 12px;
      .el-tree-node__children {
        .el-tree-node {
          border: 0 !important;
          padding-bottom: 0;
        }
      }
    }
  }
}
.DownloadTem {
  color: rgba(64, 163, 184, 1);
}
.tableTreeRight {
  width: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.tableTreeTwo {
  span {
    display: inline-block;
  }
  .tableTreeTwoSpan1 {
    width: 60%;
    @extend span;
  }
  .tableTreeTwoSpan2 {
    width: 38%;
    @extend span;
  }
}

.el-dialog.import-box {
  width: 400px;
  height: 240px;
  top: 50%;
  transform: translateY(-50%);
  .el-dialog__header {
    height: 50px;
    padding: 14px 20px;
    box-sizing: border-box;
    border-bottom: 1px solid rgba(238, 238, 238, 1);
    .el-dialog__title {
      height: 22px;
      font-size: 16px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: rgba(48, 49, 51, 1);
      line-height: 22px;
      float: left;
    }
    .el-dialog__headerbtn {
      top: 16px;
    }
  }
  .el-dialog__body {
    height: 190px;
    padding: 20px;
    position: relative;
    .import-content {
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(41, 41, 51, 1);
      a {
        color: #3b95a8;
      }
    }
    .import-result {
      margin: 0;
      padding: 0;
      overflow: hidden;
      dt {
        float: left;
        margin: 0 30px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(144, 147, 153, 1);
        &:first-child {
          margin-left: 0;
        }
        &:last-child {
          margin-right: 0;
        }
      }
    }
    > div {
      position: relative;
      height: 113px;
      .el-button {
        height: 36px;
        line-height: 36px;
        padding: 0 11px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        position: absolute;
        bottom: 0px;
        left: 50%;
        transform: translateX(-50%);
      }
      .el-upload-list {
        position: absolute;
        top: 18px;
        width: 100%;
        li {
          margin: 0;
          padding-top: 16px;
          height: 95px;
          .el-progress {
            height: 6px;
            top: 0px;
            .el-progress-bar {
              border-radius: 3px;
              height: 100%;
              width: calc(100% - 40px);
              float: left;
              .el-progress-bar__outer {
                height: 100% !important;
              }
            }
            .el-progress__text {
              top: 50%;
              transform: translateY(-50%);
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: rgba(144, 147, 153, 1);
            }
          }
          &:hover {
            background: none;
            .el-progress__text {
              display: inline;
            }
          }
          &:focus {
            outline: none;
          }
          span.file-name {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(144, 147, 153, 1);
          }
        }
      }
    }
    > .el-button {
      height: 36px;
      line-height: 36px;
      padding: 0 19px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      position: absolute;
      bottom: 20px;
      right: 20px;
    }
  }
}
</style>
<style scoped lang="scss">
.temConfiguration {
  .el-table__body-wrapper {
    button {
      color: #3b95a8;
      background: transparent;
      border: 0;
    }
  }
}
.submitBtnTemplate {
  position: fixed;
  bottom: 30px;
  right: 57px;
}
</style>
