<template>
  <div>
    <xyy-list-page>
      <template slot="body">
        <el-collapse
          v-model="activeNames"
          @change="handleChange(item, index)"
          v-for="(item, index) in getData"
          accordion
        >
          <el-collapse-item :name="index" :key="index">
            <template slot="title">
              <div class="folding">
                <span>{{item.createtime}}</span>
                <span>{{item.kefuCode}} 接待</span>
              </div>
            </template>
            <div style="height: auto">
              <chatRecordChild :getDataList="getDataList"></chatRecordChild>
            </div>
          </el-collapse-item>
        </el-collapse>
      </template>
    </xyy-list-page>
  </div>
</template>

<script>
import XyyListPage from '../../../../components/xyy/xyy-list-page/index';
import chatRecordChild from './chatRecordChild';
import {
  historicalRecord,
  getConversation
} from '@/api/configuration/RegionalMenu';
import getters from '../../../../store/getters';
export default {
  name: 'index',
  components: { XyyListPage, chatRecordChild },
  computed: {
    khId: function() {
      return this.$store.getters.khid;
    }
  },
  watch: {
    khId: function(newVal, oldVal) {
      this.customid = newVal;
      this.getInfo();
    },
    // immediate: true
  },
  data() {
    return {
      activeNames: '', // 那条下拉展示
      getData: [], // 获取的数据
      getDataList: [], // 获取的下拉数据的内容
      conversationDate: [], //  每条的会话内容
      customid: this.$store.getters.khid || '',
    };
  },
  methods: {
    // 面板改变时触发
    handleChange(item, index) {
      this.getDataList = [];
      this.activeNames = index;
      // 获取历史记录对话内容
      //     '1573685301509427840'
      getConversation(item.id)
        .then(response => {
          console.log(response);
          this.getDataList = [];
          if (response.code !== 1) {
            return;
          }
          if (response.data) {
            this.getDataList = response.data.messages;
          }
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    // 获取历史记录标题信息
    getInfo() {
      let params = {
        customid: this.customid
      };
      historicalRecord(params)
        .then(response => {
          this.getData = [];
          this.getDataList = [];
          if (response.code !== 1) {
            return;
          }
          if (response.data) {
            this.getData = response.data;
          }
        })
        .catch(function(error) {
          console.log(error);
        });
    }
  }
};
</script>

<style scoped lang="scss">
.endSession {
  text-align: center;
  position: relative;
  &:after {
    content: '';
    width: 52px;
    height: 1px;
    border-bottom: 1px solid #ccc;
    display: inline-block;
    position: absolute;
    top: 10px;
    right: 29px;
  }
  &:before {
    content: '';
    width: 52px;
    height: 1px;
    border-bottom: 1px solid #ccc;
    display: inline-block;
    position: absolute;
    top: 11px;
    left: 29px;
  }
}
/deep/ .page-header {
  display: none;
}
/deep/ .el-collapse-item__content {
  padding-bottom: 0;
}
/deep/ .page .page-body {
  padding-left: 0;
  padding-right: 0;
}
.customerService {
  color: rgba(59, 149, 168, 1);
}
.visitors {
  color: rgba(230, 162, 60, 1);
}
/deep/ .el-collapse-item__header {
  position: relative;
  /*height: 36px;*/
  min-height:36px;
  line-height: 36px;
  .folding {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding-left: 20px;
    background: #fafbfb;
    border: 1px solid #f2f4f5;
    padding: 0 15px;
    padding-left: 28px;
  }
  .el-collapse-item__arrow {
    position: absolute;
    left: 10px;
  }
}
</style>
