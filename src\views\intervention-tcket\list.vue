<template>
  <div>
    <xyy-list-page>
      <template slot="header">
        <el-form
          ref="formInline"
          :rules="rules"
          :inline="true"
          :model="formInline"
          label-position="left"
          class="input_serch">
          <el-row>

            <el-row>
              <el-form-item label="工单类型" class="form-item">
                <el-select v-model="formInline.sheetStyle" size="small" class="elInput" placeholder="请选择内容">
                  <el-option label="全部" value></el-option>
                  <el-option v-for="item in allType" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="form-item" label="工单编号" prop="sheetNum">
                <el-input
                  v-model="formInline.sheetNum"
                  class="elInput"
                  size="small"
                  placeholder="请输入内容"
                  maxlength="28"
                  @blur="inputTrim()" />
              </el-form-item>
              <el-form-item v-if="isYBMBusinessPartCode" class="form-item" label="工单创建人" prop="creator">
                <el-input
                  v-model="formInline.creator"
                  class="elInput"
                  size="small"
                  placeholder="请输入内容"
                  maxlength="60"
                  @blur="inputTrim()" />
              </el-form-item>
              <el-form-item class="form-item" label="订单编号" prop="orderNum">
                <el-input
                  v-model="formInline.orderNum"
                  class="elInput"
                  size="small"
                  maxlength="35"
                  placeholder="请输入内容"
                  @blur="inputTrim()" />
              </el-form-item>
              <el-form-item class="form-item" label="客户名称" prop="customerName">
                <el-input
                  v-model="formInline.customerName"
                  class="elInput"
                  size="small"
                  placeholder="请输入内容"
                  maxlength="60"
                  @blur="inputTrim()" />
              </el-form-item>
              <el-form-item v-if="isYBMBusinessPartCode" class="form-item" label="客户ID" prop="customer_id">
                <el-input
                  v-model="formInline.customer_id"
                  class="elInput"
                  size="small"
                  placeholder="请输入内容"
                  maxlength="60"
                  @blur="inputTrim()" />
              </el-form-item>
              <el-form-item class="form-item" label="客户电话" prop="customerPhoneNumber">
                <el-input
                  v-model="formInline.customerPhoneNumber"
                  class="elInput"
                  size="small"
                  placeholder="请输入内容"
                  maxlength="20"
                  @blur="inputTrim()" />
              </el-form-item>
              <el-form-item v-if="isYBMBusinessPartCode" class="form-item" label="商家ID" prop="merchantId">
                <el-input
                  v-model="formInline.merchantId"
                  class="elInput"
                  size="small"
                  placeholder="请输入内容"
                  maxlength="60"
                  @blur="inputTrim()" />
              </el-form-item>
            </el-row>

            <el-row type="flex" justify="space-between" align="middle">
              <el-form-item>
                <el-button plain type="primary" size="small" class="searchCondition" @click="handleCondition">查询
                </el-button>
                <el-button plain size="small" @click="resetForm('formInline')">重置</el-button>
              </el-form-item>

              <el-form-item class="form-item">
                <el-button class="sheetMainBtn" @click="batchSwitchstate">
                  批量转移{{ batchSelection === false ? '（查询）' : '（转出）' }}
                </el-button>
                <!-- 2020.4.14,rl,更多查询条件,begin -->
                <!--
                <el-dropdown ref="messageDrop" :hide-on-click="false" trigger="click">
                  <el-button plain class="sheetMainBtn">
                    更多条件
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown" class="dropDownBox">
                    <el-dropdown-item>
                      <el-form label-width="70px">
                        <el-form-item class="innerStyle" label="订单编号" prop="orderNum">
                          <el-input
                            v-model="formInline.orderNum"
                            class="innerElInput"
                            placeholder="请输入内容"
                            maxlength="25"
                          />
                        </el-form-item>
                        <el-form-item label="工单状态" prop="customerName" class="innerStyle">
                          <el-select
                            v-model="formInline.currentState"
                            class="innerElInput"
                            placeholder="请选择内容"
                            @change="queryTime()"
                          >
                            <el-option label="全部" value></el-option>
                            <el-option label="待领取" value="0"></el-option>
                            <el-option label="处理中" value="1"></el-option>
                            <el-option label="已结单" value="2"></el-option>
                            <el-option label="退回" value="3"></el-option>
                          </el-select>
                        </el-form-item>
                        <el-form-item class="innerStyle" label="发起时间" prop="dataRange">
                          <el-date-picker
                            v-model="formInline.dataRange"
                            :default-time="['00:00:00', '23:59:00']"
                            type="datetimerange"
                            range-separator="-"
                            size="small"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            format="yyyy-MM-dd HH:mm"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            text="erdf"
                            prefix-icon="el-icon-date"
                            class="innerSelTime"
                            @change="queryTime()"
                            @focus="dateTimeFocus()"
                          />
                        </el-form-item>
                      </el-form>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                -->
                <el-popover v-model="queryMoreVisible" placement="bottom-end" width="580" trigger="click">
                  <query-more
                    :form-type-id="formInline.sheetStyle"
                    menu-index="8"
                    @hide="setVisibleQueryMore"
                    @search="searchQueryMore"></query-more>

                  <el-button slot="reference" plain class="sheetMainBtn">
                    更多条件
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                </el-popover>
                <!-- 2020.4.14,rl,更多查询条件,end -->
              </el-form-item>
            </el-row>

          </el-row>
        </el-form>
      </template>

      <template slot="body">
        <div class="xyyTable">
          <el-dropdown v-if="checkPermission('btn:cs:workorderexport')" trigger="click" class="exportExcel">
            <el-button type="text" icon="el-icon-upload2">导出Excel</el-button>
            <el-dropdown-menu slot="dropdown" class="exoprtShow">
              <el-dropdown-item>
                <el-button type="text" @click="checkTimer(exportOpen, 'time3')(1)">工单明细</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text" @click="checkTimer(exportOpen, 'time4')(2)">流转记录</el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <xyy-table
            ref="orderTable"
            :data="list"
            :col="filterCol"
            :list-query="listQuery"
            :operation="operation"
            :offset-top="240"
            :assign-params="assignParams"
            :has-selection="batchSelection"
            @get-data="
              batchSelection ? getBatchList() : seachDataList(arguments[0])
            "
            @handleCellMouseEnter="handleCellMouseEnter"
            @selectionCallback="selectionCallback"
            @sortChange="sortChange">
            <!-- 工单编号 -->
            <template slot="workorderNum" slot-scope="{ col }">
              <el-table-column :key="col.index" :prop="col.index" :label="col.name" :width="col.width">
                <template slot-scope="item">
                  <el-popover popper-class="hoverStyle" placement="right-start" trigger="hover">
                    <el-row v-if="hoverItem" class="title">
                      <span v-if="hoverItem">{{ hoverItem.nodeName }}</span>
                      <span v-if="hoverItem">{{ hoverItem.nodeName ? '-' : '' }}
                      </span>
                      <span v-if="hoverItem">{{ hoverItem.processorName }}</span>
                      <span v-if="hoverItem &&hoverItem.recordType &&hoverItem.recordType === 'append'">追加记录-</span>
                      <span
                        v-if="hoverItem &&hoverItem.recordType &&hoverItem.recordType === 'append'">{{ hoverItem.creatorName
                        }}</span>
                    </el-row>
                    <el-row v-if="hoverItem" class="time">
                      <span v-if="hoverItem" class="node-date">{{ hoverItem.gmtCreate | dateFormat }}</span>
                    </el-row>
                    <template v-if="hoverItem">
                      <div v-if="[1, 2].includes(hoverItem.auditStatus)" class="content">
                        <open-info :data-info="hoverItem.replyExtendFieldInfo" class="field-content"></open-info>
                      </div>
                      <div v-if="hoverItem &&hoverItem.auditStatus &&hoverItem.auditStatus === 3">
                        <p class="content-textarea-formate">
                          退回原因：
                          <pre>{{ hoverItem.sendBackReason }}</pre>
                        </p>
                      </div>
                      <div v-if="hoverItem &&hoverItem.auditStatus &&hoverItem.auditStatus === 4">
                        <p class="content-textarea-formate">
                          重启原因：
                          <pre>{{ hoverItem.reason }}</pre>
                        </p>
                      </div>
                      <div v-if="hoverItem &&hoverItem.recordType && hoverItem.recordType === 'append'">
                        <p class="content-textarea-formate">
                          <pre>{{ hoverItem.appendContent }}</pre>
                        </p>
                      </div>
                    </template>
                    <template v-if="!hoverItem">
                      <el-row class="noData">暂无流转记录</el-row>
                    </template>
                    <el-row slot="reference" type="flex" justify="start" align="middle">
                      <span style="margin-right:4px;">{{ item.row['workorderNum'] }}
                      </span>
                      <img
                        v-if="item.row['timeoutState'] == 1"
                        style="height:16px;width:16px"
                        src="../../assets/work_sheet/icon-chao.png" />
                      <img
                        v-if="item.row['timeoutState'] == 2"
                        style="height:16px;width:16px"
                        src="../../assets/work_sheet/icon-jchao.png" />
                      <img
                        v-if="item.row['currentState'] == 3"
                        style="height:16px;width:16px"
                        src="../../assets/work_sheet/icon-tui.png" />
                    </el-row>
                  </el-popover>
                </template>
              </el-table-column>
            </template>
            <!-- 优先级 -->
            <template slot="1164456270271483904" slot-scope="{ col }">
              <el-table-column :key="col.index" :prop="col.index" :label="col.name" :width="col.width">
                <template
                  v-if="item.row['1164456270271483904']"
                  slot-scope="item">
                  <span
                    :class="
                      'levelBlock ' +
                        (item.row['1164456270271483904'].option_name === '加急'
                          ? 'urgent'
                          : item.row['1164456270271483904'].option_name ===
                            '紧急'
                            ? 'pressing'
                            : 'plain')
                    ">
                    {{
                      item.row['1164456270271483904'].option_name === '加急'
                        ? '加急'
                        : item.row['1164456270271483904'].option_name === '紧急'
                          ? '紧急'
                          : '普通'
                    }}
                  </span>
                </template>
              </el-table-column>
            </template>
            <!-- 当前状态 -->
            <template slot="currentState" slot-scope="{ col }">
              <el-table-column :key="col.index" :prop="col.index" :width="col.width" :label="col.name">
                <template slot-scope="{ row }">
                  <span
                    v-if="row.currentState === 0"
                    class="currentState1">待领取</span>
                  <span
                    v-else-if="row.currentState === 1"
                    class="currentState1">处理中</span>
                  <span
                    v-else-if="row.currentState === 2"
                    class="currentState2">已结单</span>
                  <span
                    v-else-if="row.currentState === 3"
                    class="currentState3">退回</span>
                  <span
                    v-else-if="row.currentState === 4"
                    class="currentState4">异常</span>
                  <span
                    v-else-if="row.currentState === 5"
                    class="currentState5">作废</span>
                  <span
                    v-else
                    class="currentState0">待领取</span>
                </template>
              </el-table-column>
            </template>
            <!-- 最近受理时间 -->
            <template slot="recentAcceptanceTime" slot-scope="{ col }">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :label="col.name"
                :width="col.width"
                :formatter="getFormatDate"
                :sortable="col.sortable || false" />
            </template>
            <!-- 最近提交时间 -->
            <template slot="latestSubmissionTime" slot-scope="{ col }">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :label="col.name"
                :width="col.width"
                :formatter="getFormatDate"
                :sortable="col.sortable || false" />
            </template>
            <!-- 工单发起时间 -->
            <template slot="gmtCreate" slot-scope="{ col }">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :label="col.name"
                :width="col.width"
                :formatter="getFormatDate"
                :sortable="col.sortable || false" />
            </template>
            <!-- 工单完成时间 -->
            <template slot="completionTime" slot-scope="{ col }">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :label="col.name"
                :width="col.width"
                :formatter="getFormatDate"
                :sortable="col.sortable || false" />
            </template>
            <!-- 问题分类 -->
            <template slot="1164724692221825024" slot-scope="{ col }">
              <el-table-column :key="col.index" :prop="col.index" :label="col.name" :width="col.width">
                <template slot-scope="item">
                  <span>
                    {{
                      item.row['1164724692221825024']
                        ? item.row['1164724692221825024'].option_name
                        : ''
                    }}
                  </span>
                </template>
              </el-table-column>
            </template>
            <!-- 来源渠道 -->
            <template slot="1164723673773510656" slot-scope="{ col }">
              <el-table-column :key="col.index" :prop="col.index" :label="col.name" :width="col.width">
                <template slot-scope="item">
                  <el-row>
                    <span>
                      {{
                        item.row['1164723673773510656']
                          ? item.row['1164723673773510656'].option_name
                          : ''
                      }}
                    </span>
                  </el-row>
                </template>
              </el-table-column>
            </template>
            <!-- 来电号码 -->
            <template slot="1164723822566445056" slot-scope="{ col }">
              <el-table-column :key="col.index" :prop="col.index" :label="col.name" :width="col.width">
                <template slot-scope="item">
                  <el-row>
                    <span>{{ item.row['1164723822566445056'] }}</span>
                  </el-row>
                </template>
              </el-table-column>
            </template>
            <template slot="processingDuration" slot-scope="{ col }">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :label="col.name"
                :width="col.width"
                :formatter="getFormatDuration" />
            </template>
            <!-- 标签 -->
            <template slot="tag" slot-scope="{ col }">
              <el-table-column :label="col.name" :width="col.width" class-name="table-tag" fixed="right">
                <template slot-scope="{ row }">
                  <el-popover placement="right" width="400" trigger="click">
                    <el-button
                      v-if="row.tagId"
                      size="mini"
                      class="tag-btn"
                      type="danger"
                      icon="el-icon-delete"
                      @click="clearTag(row)">清除标签
                    </el-button>
                    <el-button
                      v-for="(tag, key) in tags.filter(item => item.id != row.tagId)"
                      :key="key"
                      size="mini"
                      class="tag-btn"
                      @click="switchTag(tag, row)">{{ tag.tagName }}
                    </el-button>
                    <el-button
                      v-if="!row.tagId"
                      slot="reference"
                      :disabled="isDisabledTag"
                      type="info"
                      size="mini"
                      class="tag-btn"
                      icon="el-icon-plus"
                      circle></el-button>
                    <el-button v-if="row.tagId" slot="reference" :disabled="isDisabledTag" size="mini" class="tag-btn">
                      {{ row.tagName }}
                    </el-button>
                  </el-popover>
                </template>
              </el-table-column>
            </template>
            <!-- 评价回复时间 -->
            <template slot="evaluategmtCreate" slot-scope="{ col }">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :label="col.name"
                :width="col.width"
                :formatter="getFormatDate"
                :sortable="col.sortable || false" />
            </template>
            <!-- 满意度评价 -->
            <template slot="customerSatisfaction" slot-scope="{ col }">
              <el-table-column :key="col.index" :prop="col.index" :label="col.name" :width="col.width">
                <template slot-scope="item">
                  <el-row>
                    <span>
                      {{
                        item.row['customer_satisfaction'] &&
                          item.row['customer_satisfaction'].customerSatisfaction
                          ? '是'
                          : '否'
                      }}
                    </span>
                  </el-row>
                </template>
              </el-table-column>
            </template>
            <!-- 评价维度1 -->
            <template slot="evaluateRate1" slot-scope="{ col }">
              <el-table-column :key="col.index" :prop="col.index" :label="col.name" :width="col.width">
                <template slot-scope="{ row }">
                  <el-rate
                    :value="
                      row['customer_satisfaction'] &&
                        row['customer_satisfaction'].starLevel1
                        ? row['customer_satisfaction'].starLevel1
                        : 0
                    "
                    disabled></el-rate>
                </template>
              </el-table-column>
            </template>
            <!-- 评价维度2 -->
            <template slot="evaluateRate2" slot-scope="{ col }">
              <el-table-column :key="col.index" :prop="col.index" :label="col.name" :width="col.width">
                <template slot-scope="{ row }">
                  <el-rate
                    :value="
                      row['customer_satisfaction'] &&
                        row['customer_satisfaction'].starLevel2
                        ? row['customer_satisfaction'].starLevel2
                        : 0
                    "
                    disabled></el-rate>
                </template>
              </el-table-column>
            </template>
            <!-- 评价回复 -->
            <template slot="evaluateRateReply" slot-scope="{ col }">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :label="col.name"
                :width="col.width"
                :show-overflow-tooltip="true">
                <template slot-scope="{ row }">
                  <span>
                    {{
                      row['customer_satisfaction'] &&
                        row['customer_satisfaction'].evaluate
                        ? row['customer_satisfaction'].evaluate
                        : ''
                    }}
                  </span>
                </template>
              </el-table-column>
            </template>
            <template slot="operation" slot-scope="{ col }">
              <el-table-column :label="col.name" :width="col.width" fixed="right" class-name="operation-box">
                <template slot-scope="{ row }">
                  <el-row v-if="row.currentState != 0">
                    <el-button
                      v-for="item in filterOper(row)"
                      :key="item.type"
                      type="text"
                      @click="operationClick(item.type, row)">{{ item.name }}
                    </el-button>
                  </el-row>
                  <el-row v-else>
                    <el-button
                      v-if="checkPermission('btn:cs:workorderassignment')"
                      type="text"
                      @click="sheetAssignment(row)">工单分配
                    </el-button>
                    <el-button v-if="row.seeDetails" type="text" @click="operationClick(1, row)">查看</el-button>
                  </el-row>
                </template>
              </el-table-column>
            </template>
          </xyy-table>
          <!-- 工单分配 -->
          <el-row class="assignmentSheet">
            <el-dialog
              :visible.sync="assignmentDialog"
              title="工单分配"
              width="400px"
              @open="initAss('assSheet')"
              @close="assCacel">
              <el-row class="sheetNumber">将工单编号：{{ workorderNum }}的工单，分配给</el-row>
              <el-form ref="assSheet" :model="assSheet" :rules="assRules" label-width="88px" class="demo-ruleForm">
                <el-form-item label="用户组" prop="userGroupVal" class="userGroup">
                  <el-select
                    v-model="assSheet.userGroupVal"
                    placeholder="请选择用户组"
                    @change="handleChangeUserGroup(assSheet.userGroupVal)">
                    <el-option
                      v-for="item in CurrentNodeGroupInfo"
                      :key="item.userGroupId"
                      :label="item.userGroupName"
                      :value="item.userGroupId" />
                  </el-select>
                </el-form-item>
                <el-form-item label="组内成员" prop="memberVal" class="userGroup">
                  <el-select v-model="assSheet.memberVal" placeholder="请选择组内成员" class="input_sheetType">
                    <el-option
                      v-for="item in memberArray"
                      :key="item.userId"
                      :label="item.nickname"
                      :value="item.userId" />
                  </el-select>
                </el-form-item>
                <el-form-item type="flex" justify="end" align="end" class="assSubmit">
                  <xyy-button type="normal" @click="assignmentDialog = false">取 消</xyy-button>
                  <xyy-button class="queryAss" type="primary" @click="queryAss('assSheet')">确定分配</xyy-button>
                </el-form-item>
              </el-form>
            </el-dialog>
          </el-row>
        </div>
      </template>
    </xyy-list-page>
    <expor-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"></expor-tip>

    <!-- 工单批量分配查询Dialog -->
    <el-row class="assignmentSheet">
      <el-dialog :visible.sync="batchSelectDialog" title="工单转移" width="476px">
        <el-form
          ref="refSearchForm"
          :model="batchQueryForm"
          :rules="batchSearchRules"
          label-width="88px"
          class="demo-ruleForm">

          <el-form-item label="工单类型" prop="orderType" class="userGroup">
            <el-select v-model="batchQueryForm.orderType" placeholder="请选择筛选条件" @change="getTypeformatList">
              <el-option v-for="item in allType" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="工单模板" prop="orderTemplate" class="userGroup">
            <el-select v-model="batchQueryForm.orderTemplate" placeholder="请选择筛选条件" @change="getCurrtNodeLists">
              <el-option v-for="item in orderTemplateList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="问题分类" class="userGroup">
            <el-cascader
              v-model="batchQueryForm.questionType"
              :options="options"
              :props="optionProps"
              clearable></el-cascader>
          </el-form-item>
          <el-form-item label="当前状态" prop="state" class="userGroup">
            <el-select
              v-model="batchQueryForm.state"
              :disabled="['4', '3'].includes(secActiveIndex)"
              placeholder="请选择筛选条件"
              @change="batchQueryForm.currentNode=''">
              <el-option
                key="1"
                label="处理中"
                value="1" />
              <el-option
                key="0"
                label="待领取"
                value="0" />
                <!-- <el-option key="2" label="已结单" value="2" />
                <el-option key="3" label="退回" value="3" />
                <el-option key="4" label="异常" value="4" />
          <el-option key="5" label="作废" value="5" />-->
            </el-select>
          </el-form-item>
          <el-form-item label="客户所在地">
            <el-select v-model="batchQueryForm.customerLoca" placeholder="请选择筛选条件" clearable>
              <el-option v-for="item in customerAddList" :key="item.id" :label="item.sourceName" :value="item.id" />
            </el-select>
          </el-form-item>
          <!-- v-if="batchQueryForm.state=='1'||batchQueryForm.state=='0'" -->
          <el-form-item label="节点选择" prop="currentNode" class="userGroup">
            <el-select v-model="batchQueryForm.currentNode" placeholder="请选择筛选条件" clearable>
              <el-option v-for="item in CurrtNodeList" :key="item.id" :label="item.nodeName" :value="item.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="当前受理人" class="userGroup">
            <el-input
              v-model="batchQueryForm.currentProcessor"
              :disabled="['1', '2', '3', '5'].includes(secActiveIndex)"></el-input>
          </el-form-item>
          <el-form-item label="工单创建人" class="userGroup">
            <el-input v-model="batchQueryForm.founder" :disabled="secActiveIndex == 6"></el-input>
          </el-form-item>
          <el-form-item label="工单发起时间" class="userGroup">
            <el-date-picker
              v-if="batchSelectDialog"
              v-model="batchQueryForm.createTime"
              :default-time="['00:00:00', '23:59:00']"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
          </el-form-item>
          <el-form-item type="flex" justify="end" align="end" class="assSubmit">
            <xyy-button type="normal" @click="batchSelectDialog = false">取 消</xyy-button>
            <xyy-button class="queryAss" type="primary" @click="getBatchList(true)">确定</xyy-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </el-row>
    <!-- 工单批量分配Dialog -->
    <el-row class="assignmentSheet">
      <el-dialog :visible.sync="batchMatchDialog" title="工单分配" width="420px">
        <el-form
          ref="batchTransferForm"
          :model="batchMatchForm"
          :rules="batchSearchRules"
          label-width="88px"
          class="demo-ruleForm">
          <el-row class="sheetNumber">
            <el-radio v-model="assRadioSign" label="people">转移给人</el-radio>
            <el-radio v-model="assRadioSign" label="node">转移给节点</el-radio>
          </el-row>
          <el-form-item
            label="节点选择"
            prop="nodeId"
            class="userGroup">
            <el-select v-model="batchMatchForm.nodeId" placeholder="请选择节点" @change="getListUserGroupsByNodeId">
              <el-option v-for="item in CurrtNodeList" :key="item.id" :label="item.nodeName" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="assRadioSign == 'people'"
            label="用户组"
            class="userGroup"
            prop="userGroupId">
            <el-select v-model="batchMatchForm.userGroupId" placeholder="请选择用户组" @change="getUserLists">
              <el-option
                v-for="(item, index) in UserGroupList"
                :key="index"
                :label="item.userGroupName"
                :value="item.userGroupId" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="assRadioSign == 'people'" label="组内成员" class="userGroup" prop="userId">
            <el-select v-model="batchMatchForm.userId" placeholder="请选择组内成员">
              <el-option
                v-for="item in GroupUserList"
                :key="item.staffNum"
                :label="item.nickname"
                :value="item.userId" />
            </el-select>
          </el-form-item>
          <el-form-item type="flex" justify="end" align="end" class="assSubmit">
            <xyy-button type="normal" @click="batchMatchDialog = false">取 消</xyy-button>
            <xyy-button class="queryAss" type="primary" @click="handleBatchTransfer">确定</xyy-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </el-row>
    <!-- 确认转移工单提示 -->
    <el-dialog :visible.sync="batchTipsDialog" width="400px">
      <span>确定批量转移工单?</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="batchTipsDialog = false">取 消</el-button>
        <el-button type="primary" @click="handleBatchTransfer(true)">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 转移失败工单列表 -->
    <el-dialog :visible.sync="batchFailDialog" width="420px">
      <!-- style="color:red;" -->
      <span>以下工单转出成功</span>
      <div style="max-height:500px;overflow-y:scroll;">
        <div
          v-for="item in batchFailList"
          :key="item.workorderId"
          style="padding-top:16px;display: flex;">
          <div style="width: 200px;padding: 0 20px;">{{ item.workorderNum }}</div>
          <div style="width: 130px;">{{ item.workTypeName }}</div>
        </div>
      </div>
      <span
        slot="footer"
        class="dialog-footer">
        <el-button
          type="primary"
          @click="batchFailDialog = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  assignmentUorkorder,
  deleteTag,
  getCustomerSourceList,
  getLatestFlowInfo,
  getListNodesByFormId,
  getNodeGroupInfo,
  getSheetTypeList,
  getTagList,
  getTypeformat,
  getUserGroupsByNodeIdAndWorkorderIds,
  getUserInfo,
  getUserList,
  listAllWorkorder,
  listAllWorkorderByBatch,
  putSwitchTag,
  saveFlowStartTime,
  seleSheetList,
  setBatchTransfer,
  startExport
} from '@/api/mySheetManage';
import utils from '@/utils/filter';
import openInfo from '@/views/work-sheet/components/openInfo';
import exporTip from '@/views/work-sheet/components/exportTip';
import { adapterList } from '@/utils/tools.js';
import queryMore from '../work-sheet/components/queryMore';

export default {
  name: 'InterventionTicketList',
  components: {
    openInfo,
    exporTip,
    queryMore
  },
  filters: {
    dateFormat(val) {
      return utils.dataTime(val, 'yy-mm-dd HH:ss:nn');
    }
  },
  data() {
    return {
      time3: null,
      time4: null,
      changeExport: false,
      formName: '',
      allType: [],
      formTypeId: '', // 标签类型/工单类型 all
      secActiveIndex: '', // 激活的index
      isCondition: false,
      secActiveName: '',
      formInline: {
        sheetStyle: '', // 工单类型
        sheetNum: '', // 工单编号
        customerName: '', // 客户名称
        currentState: '全部', // 查询状态
        dataRange: '', // 时间范围
        orderNum: '', // 订单编号
        customerPhoneNumber: '', // 客户电话
        customer_id: '', // 客户ID
        creator: '', // 工单创建人
        merchantId: '' // 商家ID
      },
      dataRange: '', // 时间范围
      rules: {
        sheetNum: [
          { required: false, message: '请选择工单编号', trigger: 'change' }
        ],
        customerName: [
          { required: false, message: '请选择客户名称', trigger: 'change' }
        ],
        dataRange: [
          {
            type: 'date',
            required: false,
            message: '请选择日期',
            trigger: 'change'
          }
        ]
      },
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      list: [],
      col: [
        { index: 'workorderNum', width: 200, name: '工单编号', slot: true },
        // { index: 'formTypeName', width: 200, name: '工单类型' },
        { index: '1164456058157142016', name: '客户名称', width: 150 },
        { index: 'customer_id', name: '客户ID', width: 150 },
        { index: '1164723822566445056', name: '客户电话', width: 150, slot: true },
        { index: '1870039678284075008', name: '商家ID', width: 150 },
        { index: '1164724692221825024', name: '问题分类', width: 200, slot: true },
        { index: '1164456270271483904', name: '优先级', width: 150, slot: true },
        { index: 'currentState', name: '当前状态', width: 150, slot: true },
        { index: 'currentProcessingPersonName', name: '当前受理人', width: 150 },
        { index: 'recentAcceptanceTime', name: '最近受理时间', width: 200, slot: true, sortable: 'custom' },
        { index: 'creatorName', name: '工单创建人', width: 150 },
        { index: 'endPersonName', name: '工单结单人', width: 150 },
        { index: 'completionTime', name: '工单完成时间', width: 200, slot: true, sortable: 'custom' },
        { index: 'customerSatisfaction', name: '满意度评价', width: 100, slot: true },
        { index: 'evaluategmtCreate', name: '评价时间', width: 200, slot: true, sortable: 'custom' },
        { index: 'evaluateRate1', name: '评价维度1', width: 150, slot: true },
        { index: 'evaluateRate2', name: '评价维度2', width: 150, slot: true },
        { index: 'evaluateRateReply', name: '评价回复', width: 150, slot: true },
        { index: 'tag', name: '标签', width: 150, slot: true },
        { index: 'operation', name: '操作', width: 220, slot: true }
        // { index: '1164723673773510656', name: '来源渠道', width: 150, slot: true },
        // { index: 'latestSubmissionTime', name: '最近提交时间', width: 200, slot: true, sortable: 'custom' },
        // { index: 'gmtCreate', name: '工单发起时间', width: 200, slot: true, sortable: 'custom' },
        // { index: 'processingDuration', name: '工单处理时长', width: 200, slot: true },
      ],
      operation: [
        {
          name: '查看',
          type: 1
        },
        {
          name: '去关闭',
          type: 2
        },
        // {
        //   name: '催办',
        //   type: 3
        // },
        {
          name: '去处理',
          type: 4
        }
      ],
      hoverData: [],
      hoverItem: [],
      assignmentDialog: false, // 工单分配
      CurrentNodeGroupInfo: [], //

      assSheet: {
        userGroupVal: '',
        memberVal: ''
      },
      memberArray: [],
      assRules: {
        userGroupVal: [
          { required: true, message: '请输入用户组名称', trigger: 'blur' }
        ],
        memberVal: [
          { required: true, message: '请输入组内成员', trigger: 'blur' }
        ]
      },
      workorderNum: '',
      flowId: '',
      currentUrl: '',
      batchSelection: true, // 1.7.4工单批量选择flag
      batchSelectDialog: false, // 批量查询Dialog
      batchMatchDialog: false, // 批量分配Dialog
      batchTipsDialog: false, // 转移前提示Dialog
      batchFailDialog: false, // 转移失败Dialog
      batchQueryForm: {
        orderType: '', // 工单类型
        questionType: '', // 问题分类
        state: '', // 当前状态
        customerLoca: '', // 客户所在地
        currentProcessor: '', // 当前受理人
        founder: '', // 工单创建人
        createTime: '', // 工单发起时间
        orderTemplate: '', // 工单模板
        currentNode: '' // 当前节点
      }, // 批量查询表单
      batchMatchForm: {
        nodeId: '',
        userGroupId: '',
        userId: ''
      }, // 批量转移选择表单
      assRadioSign: 'people',
      batchSelected: [], // 批量转移工单
      options: [], // 问题分类数据
      CurrtNodeList: [], // 节点选择列表
      customerAddList: [], // 客户所在地列表
      orderTemplateList: [], // 工单模板列表
      UserGroupList: [], // 用户组列表
      GroupUserList: [], // 组内用户列表
      batchFailList: [], // 转移失败工单列表
      batchSearchRules: {
        orderType: [
          { required: true, message: '请选择工单类型', trigger: 'change' }
        ],
        orderTemplate: [
          { required: true, message: '请选择工单模板', trigger: 'change' }
        ],
        state: [
          { required: true, message: '请选择当前状态', trigger: 'change' }
        ],
        // currentNode: [
        //   { required: true, message: '请选择节点', trigger: 'change' },
        // ],
        nodeId: [{ required: true, message: '请选择节点', trigger: 'change' }],
        userGroupId: [
          { required: true, message: '请选择用户组', trigger: 'change' }
        ],
        userId: [
          { required: true, message: '请选择组内成员', trigger: 'change' }
        ]
      },
      optionProps: {
        value: 'typeCode',
        label: 'typeFullName'
      }, // 问题分类字段适配
      tags: [],
      queryMoreVisible: false, // 更多查询条件是否可见
      queryType: '1', // 1:简易查询,2:更多查询 //页面维护的查询方式，用于分页判断
      assignParams: '', // 保存 查询更多 组件设置的条件

      isFromCreatedHook: false, // activated钩子是否从created钩子顺序执行

      orderBy: '', // 排序规则 ascending(升序)、descending(降序)
      propField: '', // 排序字段

      isYBMBusinessPartCode: false // 药帮忙业务线
    };
  },
  computed: {
    isDisabledTag() {
      return !this.$store.getters.validBtn.some((item) => {
        return item.code === 'btn:cs:workordertag';
      });
    },
    filterCol() {
      let colView = this.col;
      if (!this.isYBMBusinessPartCode) {
        colView = colView.filter(colItem => colItem.index !== 'customer_id' && colItem.index !== '1870039678284075008'); // 客户ID && 商家ID
      }
      return this.tags.length
        ? colView
        : colView.filter((item) => {
          return item.index !== 'tag';
        });
    }
  },
  watch: {
    list: {
      handler(val, oldVal) {
        const that = this;
        if (val && val.length > 0) {
          const worderId = val.map((item) => item.id);
          // 获取hoverData
          getLatestFlowInfo(worderId).then((res) => {
            if (res.code === 1) {
              that.hoverData = res.data;
            } else {
              that.$XyyMessage.error(res.msg);
            }
          });
        }
      },
      deep: true
    },
    batchSelection(val) {
      this.batchSelected = [];
      this.batchMatchForm.nodeId = '';
      this.batchMatchForm.userGroupId = '';
      this.batchMatchForm.userId = '';
      this.$refs.orderTable.clearSelection();
    },
    assRadioSign() {
      this.batchMatchForm.userGroupId = '';
      this.batchMatchForm.userId = '';
    }
    // 去除空格
    /* formInline: {
      handler(val, oldVal) {
        this.formInline.sheetNum = val.sheetNum.trim();
        this.formInline.customerName = val.customerName.trim();
      },
      deep: true
    },

    } */
    // $route(val, old) {
    //   if (val.name === 'list') {
    //     this.formTypeId = '';
    //     this.secActiveIndex = '1';
    //     this.seachDataList();
    //   }
    // }
  },
  created() {
    this.isFromCreatedHook = true;
    this.getBusinessPartCode();
  },
  mounted: function() {
    this.currentUrl = this.$route.path;

    const preParam = { status: 1 };
    this.getSheetTypeList(preParam); // 获取表单类型
    this.seachDataList();
    this.getTags(); // 获取标签字典
  },
  activated() {
    if (!this.isFromCreatedHook) {
      const reloadRouteList = this.$store.getters.orderModifyFlagtoList.filter(
        (item) => {
          return item === this.$route.path;
        }
      );

      if (reloadRouteList.length) {
        // 根据条件重新请求列表数据
        if (this.batchSelection) {
          this.getBatchList();
        } else {
          this.seachDataList(
            Object.assign({}, this.listQuery, {
              assignParams: this.assignParams
            })
          );
        }
        // 删除当前路由
        this.$store.commit('workSheet/DEL_MODIFY_FLAG', this.$route.path);
      }
    }
  },
  deactivated() {
    this.isFromCreatedHook = false;
  },
  methods: {
    getSheetTypeList(preParam) {
      // 表单类型渲染
      const that = this;
      return getSheetTypeList(preParam)
        .then((response) => {
          if (response.msg === 'success') {
            const data = response.data;
            that.$nextTick(() => {
              that.allType = data;
            });
          }
        })
        .catch(function(error) {
          that.$$XyyMessage.error(error);
        });
    },
    // 工单分配查询开始
    sheetAssignment(row) {
      const that = this;
      getNodeGroupInfo(row.id).then((res) => {
        if (res.code) {
          that.workorderNum = row.workorderNum;
          that.CurrentNodeGroupInfo = res.data;
          that.flowId = row.flowId;
          that.assignmentDialog = true;
        } else {
          that.$XyyMessage.error(res.msg);
        }
      });
    },
    handleChangeUserGroup(userGroupVal) {
      const that = this;
      this.assSheet.memberVal = '';
      this.memberArray = [];
      getUserList(userGroupVal).then((res) => {
        that.memberArray = res.data;
      });
    },
    queryAss(formName) {
      const that = this;
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const param = {
            flowId: that.flowId,
            processingPersonGroupId: that.assSheet.userGroupVal,
            processingPersonGroupName: that.CurrentNodeGroupInfo.find(
              (item) => item.userGroupId === that.assSheet.userGroupVal
            ).userGroupName,
            processingPersonId: that.assSheet.memberVal,
            processingPersonName: that.memberArray.find(
              (item) => item.userId === that.assSheet.memberVal
            ).nickname,
            workorderId: that.list.find(
              (item) => item.workorderNum === that.workorderNum
            ).id,
            workorderNum: that.workorderNum
          };
          assignmentUorkorder(param).then((res) => {
            if (res.code === 1) {
              that.$XyyMessage.success('工单分配成功');
              that.assSheet.userGroupVal = '';
              that.assSheet.memberVal = '';
              this.assignmentDialog = false;
              that.seachDataList({
                page: that.listQuery.page,
                pageSize: that.listQuery.pageSize,
                assignParams: this.assignParams
              });
            } else {
              that.assSheet.userGroupVal = '';
              that.assSheet.memberVal = '';
              that
                .$confirm(res.msg, '提示', {
                  confirmButtonText: '确定',
                  showCancelButton: false,
                  type: 'warning'
                })
                .then(() => {
                  that.assignmentDialog = false;
                  that.seachDataList({
                    page: that.listQuery.page,
                    pageSize: that.listQuery.pageSize,
                    assignParams: this.assignParams
                  });
                })
                .catch(() => {
                });

              // that.$XyyMessage.error(res.msg);
            }
          });
        } else {
          console.log('error submit!!');
          // this.assignmentDialog = true;
          return false;
        }
      });
    },
    assCacel() {
      this.assSheet.userGroupVal = '';
      this.assSheet.memberVal = '';
      this.assignmentDialog = false;
    },
    initAss(formName) {
      this.$nextTick(() => {
        this.$refs[formName].resetFields();
      });
    },
    handleCellMouseEnter(row, column, cell, event) {
      this.hoverItem = this.hoverData.find(
        (item) => item.workorderId === row.id
      );
    },
    operationClick(type, row) {
      if (type === 1 || type === 2 || type === 4) {
        if (type === 4 || type === 2) {
          saveFlowStartTime({ workorderId: row.id }).then((res) => {
            //
          });
        }

        this.$router.push({
          path: '/workStatus' + row.formTypeId + '/intervDetail/' + row.id,
          query: {
            id: row.id,
            type: 'wordSheet',
            currentUrl: this.currentUrl,
            jump: false
          } // 类型先指向我的工单
        });
      }
    },
    dateTimeFocus() {
      const that = this;
      that.$nextTick(function() {
        document
          .getElementsByClassName('el-picker-panel')[0]
          .addEventListener('click', function() {
            that.$refs.messageDrop.show();
          });
        document.getElementsByClassName('el-time-panel')[1].style.width =
          133 + 'px';
      });
    },
    queryTime() {
      this.$refs.messageDrop.show();
    },
    filterOper(row) {
      return this.operation.filter((el) =>
        row.toDealWith
          ? el.type === 4
          : row.toClose
            ? el.type === 2
            : row.seeDetails && row.urge && row.currentState !== 2
              ? el.type !== 2 && el.type !== 4
              : row.seeDetails && !row.urge
                ? el.type !== 2 && el.type !== 4 && el.type !== 3
                : row.urge && row.currentState !== 2
                  ? el.type !== 2 && el.type !== 4
                  : el.type === 1
      );
    },
    toNewSheet: function() {
      this.$router.push({
        path: 'newSheet/' + new Date().getTime()
      });
    },
    resetForm(formName) {
      // 重置表单
      this.formInline.sheetStyle = '';
      this.formInline.currentState = '全部';
      this.formInline.dataRange = '';
      this.formInline.orderNum = '';
      this.$refs[formName].resetFields();
      this.listQuery = {
        page: 1,
        pageSize: 10
      };
      this.batchSelection = false; // 重置批量转移状态
      this.orderBy = '';
      this.propField = '';
      this.$refs.orderTable.$refs.table.clearSort();
      this.seachDataList(this.listQuery);
    },
    handleCondition() {
      // 条件查询
      this.queryType = '1'; // 简易查询
      this.assignParams = '';

      // 重置时间字段排序
      this.orderBy = '';
      this.propField = '';
      this.$refs.orderTable.$refs.table.clearSort();

      this.isCondition = true;
      if (
        this.formInline.sheetStyle !== '' ||
        this.formInline.customerName !== '' ||
        this.formInline.dataRange !== '' ||
        this.formInline.sheetNum !== '' ||
        this.formInline.orderNum !== '' ||
        this.formInline.currentState !== '全部' ||
        this.formInline.customerPhoneNumber !== '' ||
        this.formInline.customer_id !== '' ||
        this.formInline.creator !== '' ||
        this.formInline.merchantId !== ''
      ) {
        this.listQuery.page = 1;
        this.listQuery.pageSize = 10;
        this.seachDataList(this.listQuery);
      } else {
        this.isCondition = false;
        this.listQuery.page = 1;
        this.listQuery.pageSize = 10;
        this.seachDataList(this.listQuery);
      }
    },
    seachDataList(listQuery) {
      this.batchSelection = false; // 重置批量转移状态
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      // 查询列表数据
      const that = this;
      const { page, pageSize } = listQuery || this.listQuery;
      const preParam = {
        pageNum: page,
        pageSize: pageSize
      };

      if (that.formInline.sheetStyle === '') {
        // 全部
        preParam.queryParameterJson = {};
      } else {
        preParam.queryParameterJson = Object.assign(
          {},
          {
            form_type_id: that.formInline.sheetStyle
          }
        );
      }

      // 判断是否是更多查询,2020.4.17,rl,v1.7.4
      // 简易查询
      if (this.queryType === '1') {
        if (that.formInline.customerName !== '') {
          preParam.queryParameterJson['s1164456058157142016'] = that.formInline.customerName;
        }

        if (that.formInline.customerPhoneNumber !== '') {
          preParam.queryParameterJson['s1164723822566445056'] = that.formInline.customerPhoneNumber;
        }

        // 客户ID
        if (that.formInline.customer_id !== '') {
          preParam.queryParameterJson['customer_id'] = that.formInline.customer_id; // s1863875885430607872
        }

        // 工单创建人
        if (that.formInline.creator !== '') {
          preParam.queryParameterJson['creator'] = that.formInline.creator;
        }

        // 商家ID
        if (that.formInline.merchantId !== '') {
          preParam.queryParameterJson['s1870039678284075008'] = that.formInline.merchantId;
        }

        if (
          that.formInline.dataRange !== '' &&
          that.formInline.dataRange.length !== 1
        ) {
          preParam.queryParameterJson.query_start_time =
            that.formInline.dataRange[0];
          preParam.queryParameterJson.query_end_time =
            that.formInline.dataRange[1];
        }
        if (that.formInline.sheetNum !== '') {
          preParam.queryParameterJson.workorder_num = that.formInline.sheetNum;
        }
        if (that.formInline.orderNum !== '') {
          preParam.queryParameterJson['s1176037234466492416'] =
            that.formInline.orderNum; // 订单编号
        }
        if (that.formInline.currentState !== '全部') {
          preParam.queryParameterJson.current_state =
            that.formInline.currentState;
        }
      } else if (this.queryType === '2') {
        preParam.queryParameterJson = Object.assign(
          {},
          preParam.queryParameterJson,
          listQuery.assignParams
        );
      }

      // 排序查询
      if (this.orderBy && this.propField) {
        preParam.order = this.orderBy;
        preParam.prop = this.propField;
      }

      that.listAllStateWorkorder(preParam);
    },
    listAllStateWorkorder(preParam) {
      // 全部状态请求
      const that = this;
      return listAllWorkorder(preParam)
        .then((response) => {
          if (response.msg === 'success') {
            that.rander(response);
          } else {
            that.list = [];
            that.listQuery = {
              page: 1,
              pageSize: 0,
              total: 0
            };
            that.$XyyMessage.error(response.msg);
            that.loading.close();
          }
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    rander(response) {
      const that = this;
      const { list, total, pageSize, pageNum } = response.data;
      that.list = adapterList(list);

      that.listQuery = {
        page: pageNum,
        pageSize: pageSize,
        total: total
      };
      setTimeout(() => {
        that.loading.close();
      }, 200);
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    handleChangeExport(info) {
      const that = this;
      this.changeExport = false;
      if (info === 'go') {
        that.$router.push({
          path: '/other/index/'
        });
      }
    },
    exportOpen(sorce) {
      // 导出列表
      const that = this;
      if (!that.list || !that.list.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      let preParam;
      const { page, pageSize } = this.listQuery;
      if (!that.formTypeId) {
        preParam = {
          pageNum: page,
          pageSize: pageSize,
          queryAllState: false, // 全部
          queryCarbonCopyMy: false, // 抄送我的
          queryAllWorkOrder: true,
          queryMyDealt: false, // 我处理的
          queryIInitiatedIt: false, // 我发起的
          queryUnclaimed: false, // 待领取的
          exportSource: '所有工单',
          exportType: sorce,
          workorderJson: {}
        };
      } else {
        preParam = {
          pageNum: page,
          pageSize: pageSize,
          queryAllWorkOrder: true,
          queryAllState: false, // 全部
          queryCarbonCopyMy: false, // 抄送我的
          queryMyDealt: false, // 我处理的
          queryIInitiatedIt: false, // 我发起的
          queryUnclaimed: false, // 待领取的
          exportSource: '所有工单',
          exportType: sorce,
          workorderJson: {
            formTypeId: that.formTypeId
          }
        };
      }

      switch (that.secActiveIndex) {
        case '8':
          preParam.queryAllState = true;
          break;
        case '7':
          preParam.queryCarbonCopyMy = true;
          break;
        case '6':
          preParam['tabType'] = 5;
          preParam.queryIInitiatedIt = true;
          break;
        case '5':
          preParam['tabType'] = 4;
          preParam.queryMyDealt = true;
          break;
        case '4':
          preParam.queryUnclaimed = true;
          break;
        case '1':
          preParam.workorderJson.timeoutState = 1;
          preParam['tabType'] = 0;
          break;
        case '2':
          preParam.workorderJson.timeoutState = 2;
          preParam['tabType'] = 1;
          break;
        case '3':
          preParam.workorderJson.currentState = 1;
          preParam['tabType'] = 2;
          break;
      }

      // 新增更多条件查询,2020.4.17,rl
      if (this.queryType === '1') {
        // 简易查询
        if (this.isCondition) {
          if (that.formInline.sheetStyle !== '') {
            preParam.workorderJson['form_type_id'] = that.formInline.sheetStyle;
          }
          if (that.formInline.customerName !== '') {
            preParam.workorderJson['s1164456058157142016'] =
              that.formInline.customerName;
          }

          if (that.formInline.customerPhoneNumber !== '') {
            preParam.workorderJson['s1164723822566445056'] = that.formInline.customerPhoneNumber;
          }

          // 客户ID
          if (that.formInline.customer_id !== '') {
            preParam.workorderJson['customer_id'] = that.formInline.customer_id; // s1863875885430607872
          }

          // 工单创建人
          if (that.formInline.creator !== '') {
            preParam.workorderJson['creator'] = that.formInline.creator;
          }

          // 商家ID
          if (that.formInline.merchantId !== '') {
            preParam.workorderJson['s1870039678284075008'] = that.formInline.merchantId;
          }

          if (that.formInline.dataRange !== '') {
            preParam.workorderJson.gmtCreate = that.formInline.dataRange[0];
            preParam.workorderJson.querGmt = that.formInline.dataRange[1];
          }
          if (that.formInline.sheetNum !== '') {
            preParam.workorderJson.workorderNum = that.formInline.sheetNum;
          }
          if (that.formInline.orderNum !== '') {
            preParam.workorderJson['s1176037234466492416'] =
              that.formInline.orderNum; // 订单编号
          }
          if (that.formInline.currentState !== '全部') {
            preParam.workorderJson.currentState = that.formInline.currentState;
          }
        }
      } else if (this.queryType === '2') {
        // 更多条件查询
        preParam.workorderJson = Object.assign(
          {},
          preParam.workorderJson,
          this.assignParams
        );
      }

      // 排序查询
      if (this.orderBy && this.propField) {
        preParam.order = this.orderBy;
        preParam.prop = this.propField;
      }

      preParam.workorderJson = JSON.stringify(preParam.workorderJson);
      startExport(preParam).then((res) => {
        if (res.code === 1) {
          this.changeExport = true;
        } else {
          that.$XyyMessage.error(res.msg);
        }
      });
    },
    exportExcel() {
      // 导出列表
      // const that = this;
      // if (!that.list.length) {
      //   that.$message({
      //     type: 'info',
      //     message: '无导出数据！'
      //   });
      //   return false;
      // }
      // let preParam;
      // const { page, pageSize } = this.listQuery;
      // if (!that.formTypeId) {
      //   preParam = {
      //     pageNum: page,
      //     pageSize: pageSize,
      //     queryAllState: false, // 全部
      //     queryCarbonCopyMy: false, // 抄送我的
      //     queryMyDealt: false, // 我处理的
      //     queryIInitiatedIt: false, // 我发起的
      //     queryUnclaimed: false, // 待领取的
      //     queryAllWorkorder: true,
      //     fileName: '所有工单',
      //     workorderJson: {}
      //   };
      // } else {
      //   preParam = {
      //     pageNum: page,
      //     pageSize: pageSize,
      //     queryAllState: false, // 全部
      //     queryCarbonCopyMy: false, // 抄送我的
      //     queryMyDealt: false, // 我处理的
      //     queryIInitiatedIt: false, // 我发起的
      //     queryUnclaimed: false, // 待领取的
      //     queryAllWorkorder: true,
      //     fileName: '所有工单',
      //     workorderJson: {
      //       formTypeId: that.formTypeId
      //     }
      //   };
      // }
      // switch (that.secActiveIndex) {
      //   case '8':
      //     preParam.queryAllState = true;
      //     break;
      //   case '7':
      //     preParam.queryCarbonCopyMy = true;
      //     break;
      //   case '6':
      //     preParam.queryIInitiatedIt = true;
      //     break;
      //   case '5':
      //     preParam.queryMyDealt = true;
      //     break;
      //   case '4':
      //     preParam.queryUnclaimed = true;
      //     break;
      //   case '1':
      //     preParam.workorderJson.timeoutState = 1;
      //     break;
      //   case '2':
      //     preParam.workorderJson.timeoutState = 2;
      //     break;
      //   case '3':
      //     preParam.workorderJson.currentState = 1;
      //     break;
      // }
      // if (that.formInline.customerName !== '') {
      //   preParam.workorderJson['1164456058157142016'] =
      //     that.formInline.customerName;
      // }
      // if (that.formInline.dataRange !== '') {
      //   preParam.workorderJson.gmtCreate = that.formInline.dataRange[0];
      //   preParam.workorderJson.querGmt = that.formInline.dataRange[1];
      // }
      // if (that.formInline.sheetNum !== '') {
      //   preParam.workorderJson.workorderNum = that.formInline.sheetNum;
      // }
      // if (that.formInline.orderNum !== '') {
      //   preParam.workorderJson['1176037234466492416'] =
      //     that.formInline.orderNum; // 订单编号
      // }
      // if (that.formInline.currentState !== '全部') {
      //   preParam.workorderJson.currentState = that.formInline.currentState;
      // }
      // preParam.workorderJson = JSON.stringify(preParam.workorderJson);
      // function httpPost(URL, PARAMS) {
      //   var temp = document.createElement('form');
      //   temp.action = URL;
      //   temp.method = 'get';
      //   temp.style.display = 'none';
      //   for (var x in PARAMS) {
      //     var opt = document.createElement('textarea');
      //     opt.name = x;
      //     opt.value = PARAMS[x];
      //     temp.appendChild(opt);
      //   }
      //   document.body.appendChild(temp);
      //   temp.submit();
      //   return temp;
      // }
      // let domain = document.domain;
      // if (domain === 'localhost') {
      //   domain = 'ec-service.dev.ybm100.com';
      // }
      // httpPost('http://' + domain + '/workorder/export/excel', preParam);
    },
    getFormatDuration(row, column, cellValue, index) {
      // 计算天
      var Day = Math.floor(cellValue / (24 * 3600 * 1000));
      // 计算出小时数
      var leave1 = cellValue % (24 * 3600 * 1000);
      var hours = Math.floor(leave1 / (3600 * 1000));
      // 计算分钟数
      var leave2 = leave1 % (3600 * 1000);
      var minutes = Math.floor(leave2 / (60 * 1000));
      // 计算秒数
      var leave3 = leave2 % (60 * 1000);
      var seconds = Math.round(leave3 / 1000);

      const D = Day === 0 ? '' : Day + '天';
      const h = hours === 0 ? '' : hours + '小时';
      const m = minutes === 0 ? '' : minutes + '分钟';
      const s = seconds === 0 ? '' : seconds + '秒';
      return D + h + m + s;
    },
    getFormatDate(row, column, cellValue, index) {
      if (!cellValue) {
        return '无';
      } else {
        return new Date(cellValue + 8 * 3600 * 1000)
          .toJSON()
          .substr(0, 19)
          .replace('T', ' ');
      }
    },
    // 工单批量转移状态切换
    batchSwitchstate() {
      if (!this.batchSelection) {
        this.batchSelectDialog = !this.batchSelectDialog;
      } else {
        const length = this.batchSelected.length;
        if (length <= 0) {
          this.$XyyMessage.warning('请选择需要转移的工单');
          return;
        }
        this.batchMatchDialog = !this.batchMatchDialog;
        // this.getCurrtNodeLists(this.batchQueryForm.orderTemplate);
      }
    },
    // 工单批量转移
    handleBatchTransfer(flag) {
      this.$refs['batchTransferForm'].validate((valid) => {
        if (!valid) {
          return;
        }

        const that = this;
        const length = this.batchSelected.length;
        const list = [];

        // if (
        //   that.batchQueryForm.state == 1 &&
        //   that.batchQueryForm.currentNode !== that.batchMatchForm.nodeId
        // ) {
        //   that.$XyyMessage.warning(
        //     '转移工单有误。选中工单中节点与 接收节点不匹配'
        //   );
        //   return;
        // }

        if (!flag) {
          this.batchTipsDialog = true;
          return;
        }

        for (let i = 0; i < length; i++) {
          list.push({
            id: this.batchSelected[i].id
            // fromFlowId: this.batchSelected[i].flowId
          });
        }

        const param = {
          toNodeId: this.batchMatchForm.nodeId,
          toUserGroupId: this.batchMatchForm.userGroupId,
          toUserId: this.batchMatchForm.userId,
          transferType: this.batchMatchForm.userId ? 1 : 0,
          workorderIdList: list
        };

        setBatchTransfer(param).then((res) => {
          this.batchTipsDialog = false;
          this.batchSelection = false;
          this.batchMatchDialog = false;
          this.getBatchList();
          if (res.code === 1) {
            // this.handleCondition();
            that.$XyyMessage.success('批量转移成功！');
          } else {
            const total = res.data.total;
            this.batchFailList = res.data.list;
            const length = this.batchFailList.length;
            if (length <= 0) {
              this.$XyyMessage.warning(res.msg);
            } else {
              this.$XyyMessage.warning('剩余' + total + '条转移失败');
              this.batchFailDialog = true;
            }
            // that.$XyyMessage.warning('部分转移成功！');
          }
        });
      });
    },
    // 批量转移选中数据
    selectionCallback(data) {
      this.batchSelected = data;
    },
    // 获取问题分类，节点选择
    getTypeformatList(id) {
      const that = this;
      this.batchQueryForm.questionType = '';
      this.batchQueryForm.orderTemplate = '';
      // 问题分类列表
      getTypeformat({ formTypeId: id }).then((res) => {
        that.options = res.data.treeOptions.optionsArray;
      });

      this.getSeleSheetList(id);
    },
    // 获取工单模板列表
    getSeleSheetList(id) {
      const that = this;
      seleSheetList({ formTypeId: id, status: 1 }).then((res) => {
        that.orderTemplateList = res.data;
      });
    },
    // 获取客户所在地
    getCustomerSourceLists() {
      getCustomerSourceList().then((res) => {
        this.customerAddList = res.data;
      });
    },
    // 获取工单节点列表
    getCurrtNodeLists(id) {
      this.batchQueryForm.currentNode = '';
      getListNodesByFormId(id).then((res) => {
        this.CurrtNodeList = res.data;
      });
    },
    // 工单批量转移查询
    // resetOrderByFlag 是否重置时间排序字段 true:重置,false:不重置
    getBatchList(resetOrderByFlag) {
      if (resetOrderByFlag) {
        // 重置时间字段排序
        this.orderBy = '';
        this.propField = '';
        this.$refs.orderTable.$refs.table.clearSort();
        this.listQuery.page = 1;
        this.listQuery.pageSize = 10;
      }

      this.$refs['refSearchForm'].validate((valid) => {
        if (!valid) {
          return;
        }

        const that = this;
        const { page, pageSize } = this.listQuery;
        const param = {
          interfaceType: '8', // that.secActiveIndex,
          // nodeId: that.batchQueryForm.currentNode,
          pageNum: page,
          pageSize: pageSize,
          queryParameterJson: {
            form_id: that.batchQueryForm.orderTemplate,
            form_type_id: that.batchQueryForm.orderType,
            // s1164724692221825024: '', // 'c1245225948782137344',
            current_state: that.batchQueryForm.state
            // s1176037136521105408: that.batchQueryForm.customerLoca,
            // current_processing_person: that.batchQueryForm.currentProcessor,
            // creator: that.batchQueryForm.founder,
            // query_start_time: that.batchQueryForm.createTime
          }
        };

        if (that.batchQueryForm.currentNode) {
          param.nodeId = that.batchQueryForm.currentNode;
        } else {
          param.nodeId = '';
        }

        if (that.batchQueryForm.questionType) {
          param.queryParameterJson.s1164724692221825024 =
            that.batchQueryForm.questionType[
              that.batchQueryForm.questionType.length - 1
            ];
        }

        if (that.batchQueryForm.customerLoca) {
          param.queryParameterJson.s1176037136521105408 =
            that.batchQueryForm.customerLoca;
        }

        if (that.batchQueryForm.currentProcessor) {
          param.queryParameterJson.current_processing_person =
            that.batchQueryForm.currentProcessor;
        }

        if (that.batchQueryForm.founder) {
          param.queryParameterJson.creator = that.batchQueryForm.founder;
        }

        if (
          that.batchQueryForm.createTime &&
          that.batchQueryForm.createTime.length === 2
        ) {
          param.queryParameterJson.query_start_time =
            that.batchQueryForm.createTime[0];

          param.queryParameterJson.query_end_time =
            that.batchQueryForm.createTime[1];
        }

        // 排序查询
        if (this.orderBy && this.propField) {
          param.order = this.orderBy;
          param.prop = this.propField;
        }

        listAllWorkorderByBatch(param).then((response) => {
          if (response.msg === 'success') {
            that.rander(response);
            this.batchSelection = true;
            this.batchSelectDialog = false;
          } else {
            that.$XyyMessage.error(response.msg);
            that.loading.close();
          }
        });
      });
    },
    // 获取用户组
    getListUserGroupsByNodeId(id) {
      const workorderIds = [];
      const batchLength = this.batchSelected.length;
      this.batchMatchForm.userGroupId = '';
      for (let i = 0; i < batchLength; i++) {
        workorderIds.push(this.batchSelected[i].id);
      }

      getUserGroupsByNodeIdAndWorkorderIds({
        nodeId: id,
        workorderIds: workorderIds.join('_')
      }).then((res) => {
        this.UserGroupList = res.data;
        const length = this.UserGroupList.length;
        if (this.assRadioSign === 'people' && length <= 0) {
          this.$XyyMessage.warning('该节点版本无用户组，请选择其他节点！');
        }
      });
    },
    // 获取当前人信息
    getUserInfos() {
      getUserInfo().then((res) => {
        if (['1', '2', '3', '5'].includes(this.secActiveIndex)) {
          this.batchQueryForm.currentProcessor = res.data.attributes.staffNum;
        }
        if (['6'].includes(this.secActiveIndex)) {
          this.batchQueryForm.founder = res.data.attributes.staffNum;
        }
      });
    },
    // 获取组内成员
    getUserLists(id) {
      this.batchMatchForm.userId = '';
      getUserList(id).then((res) => {
        this.GroupUserList = res.data;
      });
    },
    // 获取标签字典
    getTags() {
      getTagList().then((res) => {
        this.tags = res.data;
      });
    },
    // 切换标签
    switchTag(tag, row) {
      putSwitchTag(tag.id, row.id).then((res) => {
        if (res.code === 1) {
          this.list = this.list.map((item) => {
            if (row.id === item.id) {
              item.tagId = tag.id;
              item.tagName = tag.tagName;
            }
            return item;
          });
        }
      });
    },
    // 清除标签
    clearTag(row) {
      deleteTag(row.id).then((res) => {
        if (res.code === 1) {
          this.list = this.list.map((item) => {
            if (row.id === item.id) {
              item.tagId = '';
              item.tagName = '';
            }
            return item;
          });
        }
      });
    },
    // 更多查询条件是否可见
    setVisibleQueryMore() {
      this.queryMoreVisible = false;
    },

    // 更多查询条件查询
    searchQueryMore(assignParams) {
      this.queryMoreVisible = false;
      this.queryType = '2';
      this.assignParams = assignParams;

      // 重置时间字段排序
      this.orderBy = '';
      this.propField = '';
      this.$refs.orderTable.$refs.table.clearSort();

      this.seachDataList({
        page: 1,
        pageSize: 10,
        assignParams
      });
    },
    // 去除前后空格
    inputTrim() {
      this.formInline.sheetNum = this.formInline.sheetNum.trim();
      this.formInline.customerName = this.formInline.customerName.trim();
      this.formInline.orderNum = this.formInline.orderNum.trim();
      this.formInline.customerPhoneNumber = this.formInline.customerPhoneNumber.trim();
      this.formInline.customer_id = this.formInline.customer_id.trim();
      this.formInline.creator = this.formInline.creator.trim();
      this.formInline.merchantId = this.formInline.merchantId.trim();
    },

    /**
     * 自定义排序
     * 重新请求列表
     */
    sortChange({ column, prop, order }) {
      // 排序方式
      switch (order) {
        case 'ascending':
          this.orderBy = 'asc';
          break;
        case 'descending':
          this.orderBy = 'desc';
          break;
        default:
          this.orderBy = '';
          break;
      }

      // 排序字段
      /*
        recentAcceptanceTime(最近受理时间)    recent_acceptance_time
        latestSubmissionTime(最近提交时间)     latest_submission_time
        gmtCreate(工单发起时间)                        gmt_create
        completionTime(工单完成时间)、           completion_time
        evaluategmtCreate(评价时间)          evaluategmtCreate
      */
      switch (prop) {
        case 'recentAcceptanceTime':
          this.propField = 'recent_acceptance_time';
          break;
        case 'latestSubmissionTime':
          this.propField = 'latest_submission_time';
          break;
        case 'gmtCreate':
          this.propField = 'gmt_create';
          break;
        case 'completionTime':
          this.propField = 'completion_time';
          break;
        case 'evaluategmtCreate':
          this.propField = 'evaluategmtCreate';
          break;
        default:
          this.propField = '';
          break;
      }

      // 重新请求数据
      if (this.batchSelection) {
        this.getBatchList();
      } else {
        this.seachDataList(
          Object.assign({}, this.listQuery, {
            assignParams: this.assignParams
          })
        );
      }
    },

    // 获取业务线,判断是否是药帮忙业务线
    getBusinessPartCode() {
      if (
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
      ) {
        this.isYBMBusinessPartCode = this.$store.getters.channel.businessPartCode === 'S00009999';
      }
    }
  }
};
</script>
<style lang="scss">
.hoverStyle {
  padding-bottom: 12px;
  width: 279px;
  max-height: 165px;
  overflow-y: scroll;
  overflow-x: hidden;

  .title {
    color: #292933;
    font-size: 14px;
    padding-bottom: 7px;
  }

  .time {
    color: #aeaebf;
    font-size: 12px;
    padding-bottom: 8px;
  }

  .content {
    .instrLs {
      margin-bottom: 8px;
    }

    .lsItem {
      white-space: normal;
      text-align: left;
      font-size: 14px;
      color: #909399;
      max-width: 160px;
    }

    .lsCon {
      font-size: 14px;
      color: #909399;
      text-align: left;
    }
  }

  .noData {
    text-align: center;
    margin-top: -10px;
  }
}

/* 浏览器滚动条样式 */
/* width */
.el-popover.el-popper.hoverStyle::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

/* Track */
.el-popover.el-popper.hoverStyle::-webkit-scrollbar-track {
  background: rgb(255, 255, 255);
  border-radius: 8px;
}

/* Handle */
.el-popover.el-popper.hoverStyle::-webkit-scrollbar-thumb {
  background: rgb(201, 201, 202);
  border-radius: 8px;
}

/* Handle on hover */
.el-popover.el-popper.hoverStyle::-webkit-scrollbar-thumb:hover {
  background: rgb(162, 162, 163);
}

.urgeCon .el-textarea__inner {
  height: 211px;
}

.urgeStyle {
  .el-dialog__body {
    padding: 0 20px;
  }

  .el-dialog__title {
    color: #292933;
  }

  .el-dialog__footer {
    padding-bottom: 20px;
  }
}

/* 表格内 icon 样式 */
.el-icon-plus:before {
  background-color: transparent;
}

/* 标签按钮 */
.tag-btn {
  height: auto !important;
  margin: 2px 0;
}

.el-popover {
  text-align: unset;
}

.table-tag .cell {
  text-overflow: clip;
}
</style>
<style lang="scss" scoped>
.exoprtShow {
  padding: 0;
}

.assignmentSheet {
  .queryAss {
    background-color: rgba(59, 149, 168, 1);
    color: #fff;
    border-radius: 2px;
  }

  /deep/ .el-button.el-button--primary.is-plain {
    background-color: rgba(59, 149, 168, 1);
    color: #fff;
  }

  .userGroup {
    margin-bottom: 20px;
  }

  .label {
    display: inline-block;
    width: 56px;
    text-align: right;
    color: #292933;
    margin-right: 8px;
  }

  .sheetNumber {
    font-size: 14px;
    color: rgba(144, 147, 153, 1);
    padding: 0 0 20px 40px;
  }

  .assSubmit {
    padding: 0 20px 20px 0;
  }

  /deep/ .el-dialog__header {
    padding: 0 20px;
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    font-weight: 600;
    color: rgba(48, 49, 51, 1);
    border-bottom: 1px solid rgba(238, 238, 238, 1);
  }

  /deep/ .el-dialog__body {
    padding: 14px 0 0 0px;
  }

  /deep/ .el-input {
    width: 290px;
  }

  /deep/ .el-button {
    height: 36px;
  }

  /deep/ .el-form-item__label {
    width: 112px !important;
    padding: 0 12px 0 12px;
  }
}

/deep/ label {
  font-weight: 400 !important;
}

.sheeTitle {
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.searchCondition.is-plain {
  background: rgba(59, 149, 168, 1);
  color: #fff;
}

/deep/ .searchCondition.is-plain:hover {
  background: rgba(40, 126, 144, 1);
}

.el-range-editor--small.el-input__inner {
  height: 36px;
}

.el-button {
  height: 36px;
}

.sheetMainBtn {
  padding: 12px 12px;
}

.el-button--small {
  font-size: 14px;
}

.el-icon--right {
  margin-left: 0;
}

.input_serch {
  font-size: 14px;
  border-bottom: 1px dashed #e4e4eb;

  .el-form-item {
    margin-bottom: 12px;
  }
}

.innerStyle {
  width: 100%;

  /deep/ .el-form-item__content {
    width: 350px;
  }

  /deep/ .el-date-editor--datetimerange.el-input__inner {
    width: 100%;
  }
}

.dropDownBox {
  .el-dropdown-menu__item:hover {
    background: none;
  }

  /deep/ .el-range-editor--small .el-range-input {
    font-size: 13px !important;
  }

  .innerElInput {
    width: 350px;
  }

  .innerStyle {
    margin: 20px 0 0;
  }

  .innerSelTime {
    margin-bottom: 20px;
  }
}

.currentState0 {
  color: #fb720c;
}

.currentState1 {
  color: #fb720c;
}

.currentState2 {
  color: #67c23a;
}

.currentState3 {
  color: #fb720c;
}

.currentState4 {
  color: #fb720c;
}

.currentState5 {
  color: #fb720c;
}

.xyyTable {
  position: relative;

  .exportExcel {
    position: absolute;
    right: 20px;
    top: 0px;
    z-index: 10;
  }
}

/deep/ .el-range-separator {
  padding: 0px !important;
}

/deep/ .el-form-item__error {
  left: 23px !important;
}

.el-table .el-rate {
  margin-top: 10px;
}

/deep/ .el-table ::before {
  background-color: transparent !important;
}

.content-textarea-formate {
  display: flex;
  white-space: nowrap !important;

  pre {
    margin-top: 0;
    margin-bottom: 0;
    white-space: pre-wrap !important;
    font-family: inherit !important;
    word-break: break-all;
  }
}
</style>
