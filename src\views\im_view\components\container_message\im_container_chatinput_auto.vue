<template>
  <div class="im_kindeditor">
    <editor
      id="editor_msgte"
      height="170px"
      width="100%"
      ref="kindeditor"
      :content="editorText"
      :loadStyleMode="false"
      :pluginsPath="'/pc/static/plugins/'"
      :items="['emoticonss', 'fontsize','forecolor']"
      @on-content-change="onContentChange"
      style="color:green;"
    ></editor>
    <!-- 自动回复浮窗 :designMode ='false'-->
    <div class="autoreply" v-show="searchFlag">
      <div
        v-for="(item,index) in replyKeyWorkds.alltypes"
        :name="index"
        :key="index"
        class="auto-box"
      >
        <div
          v-for="(info,sIndex) in replyKeyWorkds.allinfos"
          v-if="item.id==info.typeid"
          :key="sIndex"
          :class="sIndex==activeIndex?'common-list-hover':null"
          class="common-list"
          @click="activeIndex=sIndex;handleListClick(info.answer)"
        >
          <p>
            <span>{{(item.name +' : '+ info.answer).substr(0,(item.name +' : '+ info.answer).indexOf(searchVal))}}</span>
            <span style="color:#3B95A8">{{searchVal}}</span>
            <span>{{(item.name +' : '+ info.answer).substr((item.name +' : '+ info.answer).indexOf(searchVal)+searchVal.length)}}</span>
          </p>
        </div>
      </div>
    </div>

    <!-- <transdialog
      :data="transdata"
      :dialogFormVisible="transAlerShow"
      @treedialog="transdialogAction"
    ></transdialog>-->
  </div>
</template>

<script>
import { uploadimg, onlinekefu } from '@/api/im_view';
import { getReplyKeyWords } from '@/api/im_view/commonUse';
import { drawimg } from '../im_container_tool/message_tool';
// import transdialog from './treedialog';
// import { transData } from './mockdata';
import { resolve } from 'q';
import axios from 'axios';
export default {
  components: {
    // transdialog
  },
  props: ['allcontent', 'uploadurl', 'containerID', 'editorTextProps'],
  data() {
    return {
      editorText: '',
      transAlerShow: false,
      imglistShow: false,
      transdata: [],
      fv: process.env.BASE_API,
      kindetor: undefined,
      searchVal: '', //搜索框的值
      searchFlag: false, //自动回复框显隐
      cloneReplyKeyWorkds: {}, //自动回复数据克隆
      replyKeyWorkds: {
        allinfos: [],
        alltypes: []
      }, //自动回复数据
      activeIndex: null, //自动回复index
      shiftChina: false, //搜索框的值
      numActive: 0, //当前选中list值
      allLength: 0, //搜索框的值
      editorElement: null
    };
  },
  created() {
    this.emits(this, 'register', {
      type: 'kuaijiehuifu_kfim',
      uid: this._uid,
      fn: val => {
        if (val.hasOwnProperty('answer')) {
          let qa = val.answer;
          this.onContentChange(qa, 'kjhf');
        } else if (val.hasOwnProperty('smallurl')) {
          let changeIMG =
            '<img ' +
            'onclick="showRaw(\'' +
            val.bigurl +
            '\')" ' +
            'src="' +
            val.smallurl +
            '" />';
          this.onContentChange(changeIMG, 'kjhf');
        } else if (val.hasOwnProperty('link')) {
          let qa =
            val.title +
            '<br>' +
            val.des +
            '<a href="' +
            val.link +
            '">' +
            val.link +
            '</a>';
          this.onContentChange(qa, 'kjhf');
        }
      }
    });
  },
  mounted() {
    // this.afterChange();
    // console.log(this.$refs.kindeditor.editor.edit.doc);
    // this.apiOnlinekefu();
  },
  methods: {
    // initKindeditor() {
    //   var _this = this;
    //   // 默认参数
    //   var options = {
    //     uploadJson: 'upload/image',
    //     width: '100%',
    //     afterChange() {
    //       _this.localValue = this.html();
    //     }
    //   };
    //   // 调用外部参数
    //   if (_this.options) {
    //     for (var i in _this.options) {
    //       options[i] = _this.options[i];
    //     }
    //   }
    //   KindEditor.create(_this.$refs.kindeditor, options);
    // },
    // readyfunction(ke) {
    //   alert(ke);
    // },
    pasteFunction(ev) {
      alert(ev);
    },
    afterChange() {
      let doc = this.$refs.kindeditor.editor.edit.doc;
      let cmd = this.$refs.kindeditor.editor.edit.cmd;
      let _this = this;

      this.$refs.kindeditor.editor.edit.textarea.bind('paste', function(event) {
        binding.value(event);
      });
      // doc.body.bind('onpaste', null, function(event) {
      //   alert('111111111');
      //   binding.value(event);
      // });
      console.log(this.$refs.kindeditor);
      return;
      doc.addEventListener('onpaste', null, function(event) {
        binding.value(event);
      });
      return;
      doc.body['paste'] = function(ev) {
        alert(333333);
        var dataItem = ev.originalEvent.clipboardData.items[0];
        //判断文件类型
        if (dataItem.kind == 'file' && dataItem.type.indexOf('image/') !== -1) {
          let file = dataItem.getAsFile();

          if (!/\.(jpg|png|JPG|PNG|bmp|BMP|gif|GIF)$/.test(file.name)) {
            alert('不支持上传的文件类型');
            return;
          }
          let srcg = file.size;
          if (srcg / 1024 / 1024 > 2) {
            alert('图片超过2M不能上传');
            return;
          }
        }
      };

      return;
      doc.body.bind('paste', function(ev) {
        var dataItem = ev.originalEvent.clipboardData.items[0];
        //判断文件类型
        if (dataItem.kind == 'file' && dataItem.type.indexOf('image/') !== -1) {
          let file = dataItem.getAsFile();

          if (!/\.(jpg|png|JPG|PNG|bmp|BMP|gif|GIF)$/.test(file.name)) {
            alert('不支持上传的文件类型');
            return;
          }
          let srcg = file.size;
          if (srcg / 1024 / 1024 > 2) {
            alert('图片超过2M不能上传');
            return;
          }
        }
      });
    },

    //内容改变时触发
    onContentChange(val, type) {
      // var e = event || window.event;
      // this.editorElement = e.target;
      if (this.$refs.kindeditor.editor.cmd.sel.anchorNode.data == '/') {
        this.numActive = 0;
        this.searchVal = '';
        this.getReplyKeyWords();
        this.removeComposition();
        this.addComposition(e);
      }
      // this.keyDown();
      this.editorText = val;
      this.$emit('clickInputTool', this.editorText);
    },

    // 转接弹窗的方法回调
    transdialogAction(type, value) {
      if (type === 'hiddenAction') {
        //是否隐藏
        this.transAlerShow = value;
      } else if (type === 'clickNode') {
        //点击跳转的节点
        this.$emit('clickInputTool', 'transfera', value);
      }
    },

    cleanInput() {
      this.editorText = '';
    },

    compositionListener(e) {
      this.searchVal += e.data;
      this.searchResult(this.searchVal);
    },

    // 绑定输入事件
    addComposition(e) {
      this.searchFlag = true;
      this.editorElement.addEventListener(
        'compositionend',
        this.compositionListener,
        false
      );
    },
    // 移除输入事件
    removeComposition() {
      this.searchFlag = false;
      this.editorElement.removeEventListener(
        'compositionend',
        this.compositionListener,
        false
      );
    },
    //获取接口数据
    getReplyKeyWords() {
      getReplyKeyWords().then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.cloneReplyKeyWorkds = JSON.parse(JSON.stringify(res.data));
        this.replyKeyWorkds = JSON.parse(JSON.stringify(res.data));
      });
    },
    //传入匹配关键字
    searchResult(keyWorkds) {
      if (!keyWorkds) {
        this.replyKeyWorkds = JSON.parse(
          JSON.stringify(this.cloneReplyKeyWorkds)
        );
        return;
      }
      var arr = [];
      this.replyKeyWorkds.allinfos.splice(
        0,
        this.replyKeyWorkds.allinfos.length
      );
      this.replyKeyWorkds.alltypes.splice(
        0,
        this.replyKeyWorkds.alltypes.length
      );
      this.cloneReplyKeyWorkds.alltypes.forEach(item => {
        if (item.name.indexOf(keyWorkds) >= 0) {
          this.replyKeyWorkds.alltypes.push(item);
          if (arr.indexOf(item.id) < 0) {
            arr.push(item.id);
            this.cloneReplyKeyWorkds.allinfos.forEach(typeItem => {
              if (item.id == typeItem.typeid) {
                this.replyKeyWorkds.allinfos.push(typeItem);
              }
            });
          }
        }
      });
      this.cloneReplyKeyWorkds.allinfos.forEach(item => {
        if (item.answer.indexOf(keyWorkds) >= 0) {
          this.replyKeyWorkds.allinfos.push(item);
          if (arr.indexOf(item.typeid) < 0) {
            arr.push(item.typeid);
            this.cloneReplyKeyWorkds.alltypes.forEach(typeItem => {
              if (item.typeid == typeItem.id) {
                this.replyKeyWorkds.alltypes.push(typeItem);
              }
            });
          }
        }
      });
      // this.replyKeyWorkds.allinfos.forEach(itemList=>{
      //   itemList.answer.
      // })
      console.log(this.replyKeyWorkds);
    },
    //选中后替换
    handleListClick(item) {
      this.$refs.kindeditor.editor.cmd.sel.anchorNode.data = this.$refs.kindeditor.editor.cmd.sel.anchorNode.data.replace(
        '/' + this.searchVal,
        item
      );
      this.removeComposition();
    }
    //绑定编辑器事件
    // keyDown() {
    //   this.editorElement.removeEventListener('keydown', false);
    // }
  },
  watch: {
    numActive: {
      handler(val, oldval) {
        this.allLength = document.querySelectorAll(
          '.autoreply .common-list'
        ).length;
        if (this.allLength > 0) {
          for (var i = 0; i < this.allLength; i++) {
            document.querySelectorAll('.autoreply .common-list')[
              i
            ].style.background = '#fff';
          }
          document.querySelectorAll('.autoreply .common-list')[
            val
          ].style.background = '#dae9ec';
        }
      },
      deep: true
    },

    editorTextProps(val) {
      this.editorText = val;
    }
  }
};
</script>

<style lang='scss'>
.im_kindeditor {
  height: 170px;
}
.im_kindeditor .ke-icon-imageinsert {
  background-image: url('/pc/static/plugins/imageinsert/images/imageinset.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.im_kindeditor .ke-icon-transfer {
  background-image: url('/pc/static/plugins/transfer/image/zhuanyi.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  /* background-color: green; */
  width: 18px;
  height: 18px;
}

.im_kindeditor .ke-icon-history {
  background-image: url('/pc/static/plugins/history/images/history.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.im_kindeditor .ke-icon-assess {
  background-image: url('/pc/static/plugins/assess/images/assess.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.im_kindeditor .ke-icon-emoticonss {
  background-image: url('/pc/static/plugins/emoticonss/images/emotion.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.im_kindeditor .ke-icon-fontsize {
  background-image: url('/pc/static/plugins/fontsize/images/fontsize.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.im_kindeditor .ke-icon-forecolor {
  background-image: url('/pc/static/plugins/fontcolor/images/fontcolor.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.im_kindeditor .ke-toolbar {
  border-bottom: 0px;
  background-color: #fff;
  padding: 2px 5px;
  text-align: left;
  overflow: hidden;
  zoom: 1;
}

.im_kindeditor .ke-statusbar {
  position: relative;
  background-color: #fff;
  border-top: 0px solid #cccccc;
  font-size: 0;
  line-height: 0;
  height: 0px;
  overflow: hidden;
  text-align: center;
  cursor: s-resize;
}

.im_kindeditor .ke-container {
  display: block;
  border: 0px;
  border-top: 1px solid #dcdee3;
  background-color: #fff;
  overflow: hidden;
  margin: 0;
  padding: 0;
}
// 回复悬浮窗样式
.im_kindeditor {
  .autoreply {
    position: absolute;
    left: 20%;
    top: 330px;
    z-index: 1000;
    max-width: 700px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
    border-radius: 2px;
    border: 1px solid rgba(220, 222, 227, 1);
    overflow: auto;
    max-height: 150px;
    .auto-box {
      cursor: pointer;
      .common-list {
        p {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          height: 36px;
          line-height: 36px;
          font-size: 14px;
          color: #60606e;
        }
        &:hover {
          background-color: #dae9ec !important;
        }
      }
    }
  }
  .autoreply::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }
  .autoreply::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #f5f7fa;
  }
  .autoreply::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    background: #fff;
  }
}
</style>

