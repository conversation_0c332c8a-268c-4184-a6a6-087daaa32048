<template>
  <div class="custome-order-component-container">
    <div class="tabs-header">
      <el-radio-group class="sss-radio-box" v-model="activeTab" @change="changeActiveTab">
        <el-radio-button :label="'0'">全部</el-radio-button>
        <el-radio-button :label="'1'" :disabled="unfinished_list_disabled">未完成</el-radio-button>
        <el-radio-button :label="'2'" :disabled="finished_list_disabled">已完成</el-radio-button>
        <el-radio-button :label="'3'" :disabled="closed_list_disabled">已关闭</el-radio-button>
      </el-radio-group>
    </div>
    <div class="tabs-content">
      <!-- 全部 -->
      <div class="tabs-content-item" :class="{ 'is-active': activeTab === '0' }">
        <el-menu :default-active="activeTab0Index" :collapse-transition="false" mode="horizontal"
          active-text-color="#3b95a8" @select="activeTab0IndexChange">
          <el-menu-item :index="'0'">最近订单</el-menu-item>
          <el-menu-item :index="'1'">退款单</el-menu-item>
        </el-menu>
        <div class="orderno-search-box-content">
          <!-- 最近订单 -->
          <div class="orderno-search-box-content-item" :class="{ 'is-active': activeTab0Index === '0' }">
            <div class="orderno-search-box">
              <el-input v-model.trim="orderNoRecentModel" placeholder="请输入订单编号" :maxlength="200" size="medium" clearable
                @keyup.enter.native="orderNoSearch" @clear="orderNoSearch">
                <i slot="suffix" class="el-input__icon el-icon-search" @click="orderNoSearch"></i>
              </el-input>
              <!-- <el-button type="primary" size="medium" @click="orderNoSearch">搜索</el-button> -->
            </div>
            <template v-if="recent_list.length">
              <!-- <recent-order-list class="orderno-search-box-content-item__content" :orderList="recent_list"></recent-order-list> -->
              <order-list-v2 class="orderno-search-box-content-item__content" order-list-name="recent_list"
                :order-list="recent_list" :operable="isOrderOperable" @order-event="handleOrderEvent"></order-list-v2>
              <el-pagination layout="jumper, prev, pager, next" :pager-count="5" :current-page="recent_list_pageNum"
                :total="recent_list_total" @current-change="recentListPageChange"></el-pagination>
            </template>
            <template v-else>
              <div class="content-empty">
                <img src="../../../../assets/common/no_order.png" alt="暂无订单" />
              </div>
            </template>
          </div>
          <!-- 退款单 -->
          <div class="orderno-search-box-content-item" :class="{ 'is-active': activeTab0Index === '1' }">
            <div class="orderno-search-box">
              <el-input v-model.trim="orderNoRefundModel" placeholder="请输入退单编号" :maxlength="200" size="medium" clearable
                @keyup.enter.native="orderNoSearch" @clear="orderNoSearch">
                <i slot="suffix" class="el-input__icon el-icon-search" @click="orderNoSearch"></i>
              </el-input>
              <!-- <el-button type="primary" size="medium" @click="orderNoSearch">搜索</el-button> -->
            </div>
            <template v-if="refund_list.length">
              <!-- <refund-order-list class="orderno-search-box-content-item__content" :refundList="refund_list"></refund-order-list> -->
              <order-list-v2 class="orderno-search-box-content-item__content" order-list-name="refund_list"
                :order-list="refund_list" :operable="isOrderOperable" @order-event="handleOrderEvent"></order-list-v2>
              <el-pagination layout="jumper, prev, pager, next" :pager-count="5" :current-page="refund_list_pageNum"
                :total="refund_list_total" @current-change="refundListPageChang"></el-pagination>
            </template>
            <template v-else>
              <div class="content-empty">
                <img src="../../../../assets/common/no_back_order.png" alt="暂无退款单" />
              </div>
            </template>
          </div>
        </div>
      </div>
      <!-- 未完成 -->
      <div class="tabs-content-item" :class="{ 'is-active': activeTab === '1' }">
        <template v-if="unfinished_list.length">
          <!-- <order-info class="order-info__content" :order-data="unfinished_list"></order-info> -->
          <order-list-v2 class="order-info__content" order-list-name="unfinished_list" :order-list="unfinished_list"
            :operable="isOrderOperable" @order-event="handleOrderEvent"></order-list-v2>
          <el-pagination layout="jumper, prev, pager, next" :pager-count="5" :total="unfinished_list_total"
            @current-change="unfinishedListPageChange"></el-pagination>
        </template>
        <template v-else>
          <div class="content-empty">
            <img src="../../../../assets/common/no_order.png" alt="暂无订单" />
          </div>
        </template>
      </div>
      <!-- 已完成 -->
      <div class="tabs-content-item" :class="{ 'is-active': activeTab === '2' }">
        <template v-if="finished_list.length">
          <!-- <order-info class="order-info__content" :order-data="finished_list"></order-info> -->
          <order-list-v2 class="order-info__content" order-list-name="finished_list" :order-list="finished_list"
            :operable="isOrderOperable" @order-event="handleOrderEvent"></order-list-v2>
          <el-pagination layout="jumper, prev, pager, next" :pager-count="5" :total="finished_list_total"
            @current-change="finishedListPageChange"></el-pagination>
        </template>
        <template v-else>
          <div class="content-empty">
            <img src="../../../../assets/common/no_order.png" alt="暂无订单" />
          </div>
        </template>
      </div>
      <!-- 已关闭 -->
      <div class="tabs-content-item" :class="{ 'is-active': activeTab === '3' }">
        <template v-if="closed_list.length">
          <!-- <order-info class="order-info__content" :order-data="closed_list"></order-info> -->
          <order-list-v2 class="order-info__content" order-list-name="closed_list" :order-list="closed_list"
            :operable="isOrderOperable" @order-event="handleOrderEvent"></order-list-v2>
          <el-pagination layout="jumper, prev, pager, next" :pager-count="5" :total="closed_list_total"
            @current-change="closedListPageChange"></el-pagination>
        </template>
        <template v-else>
          <div class="content-empty">
            <img src="../../../../assets/common/no_order.png" alt="暂无订单" />
          </div>
        </template>
      </div>
    </div>

    <!-- 物流信息 -->
    <dialog-order-logistics ref="refDialogOrderLogistics"></dialog-order-logistics>

    <!-- 订单备注 -->
    <dialog-order-remark ref="refDialogOrderRemark"></dialog-order-remark>

    <!-- 发票信息 -->
    <dialog-order-invoice ref="refDialogOrderInvoice" :operable="isOrderOperable"
      @send="doOrderInvoiceSendSubmit"></dialog-order-invoice>
  </div>
</template>

<script>
import RecentOrderList from './recent-order-list';
import RefundOrderList from './refund-order-list';
import OrderInfo from './order-info';
import OrderListV2 from './order-list-v2.vue';
import DialogOrderLogistics from './dialog-order-logistics.vue';
import DialogOrderRemark from './dialog-order-remark.vue';
import DialogOrderInvoice from './dialog-order-invoice.vue';
import {
  getOrderInfoByNo,
  getRefundOrderInfoByNo,
  getOrderBaseByNo,
  queryOrderInfoPage,
  queryRefundOrderInfoPage,
  queryOrderDetailList,
  queryOrderInvoice,
  sendPdfMessage,
  sendOrderInfoMessage,
  sendProductMessage
} from '@/api/im_view/customeOrder';
export default {
  name: 'CustomeOrder',
  components: {
    RecentOrderList,
    RefundOrderList,
    OrderInfo,
    OrderListV2,
    DialogOrderLogistics,
    DialogOrderRemark,
    DialogOrderInvoice
  },
  filters: {},
  props: {},
  data() {
    return {
      activeTab: '0', //0:全部,1:未完成,2:已完成,3:已关闭
      activeTab0Index: '0', //0:最近订单，1:退款单

      orderNoRecentModel: '', //最近订单搜索
      orderNoRefundModel: '', //退款单搜索

      recent_list: [], //最近订单
      recent_list_pageNum: 1,
      recent_list_pageSize: 10,
      recent_list_total: 0,

      refund_list: [], //退款单
      refund_list_pageNum: 1,
      refund_list_pageSize: 10,
      refund_list_total: 0,

      unfinished_list: [], //未完成
      unfinished_list_pageNum: 1,
      unfinished_list_pageSize: 10,
      unfinished_list_total: 0,
      unfinished_list_disabled: false,

      finished_list: [], //已完成
      finished_list_pageNum: 1,
      finished_list_pageSize: 10,
      finished_list_total: 0,
      finished_list_disabled: false,

      closed_list: [], //已关闭
      closed_list_pageNum: 1,
      closed_list_pageSize: 10,
      closed_list_total: 0,
      closed_list_disabled: false,

      merchantId: '', //药店id

      khid: '', //客户id
      appId: '', //appId

      kfId: (JSON.parse(localStorage.getItem('kfInfoDetail')) || {}).id,

      type: '',
      chat: {},
      kefuInfo: {},
    };
  },
  computed: {
    // 是否有订单操作的相关权限标识，如:发送发票，发送订单，发送商品等
    isOrderOperable: function () {
      return true; // this.chat.kefuid && this.chat.kefuid == this.kfId && this.chat.dealStatus != 9
    }
  },
  watch: {},
  created() { },
  mounted() { },
  methods: {
    /**
     * 查询初始化信息,ref传递参数
     * 弃用watch
     */
    init({ type, chat, userId, kefuInfo }) {
      this.reset(); //切换客户,重置列表

      this.type = type;
      this.chat = chat;
      this.kefuInfo = kefuInfo;

      this.khid = userId;
      this.appId = chat.appId || 1000;
      switch (this.appId) {
        case 1000:
        default:
          //药帮忙
          this.unfinished_list_disabled = false;
          this.finished_list_disabled = false;
          this.closed_list_disabled = false;

          this.merchantId = this.khid;
          this.reqRecentOrderList();
          break;
        case 1001:
          //豆芽
          this.unfinished_list_disabled = true;
          this.finished_list_disabled = true;
          this.closed_list_disabled = true;

          //初始加载不显示订单,只让坐席手动搜索
          this.merchantId = ''; //豆芽销售进线,不传药店id
          this.reqRecentOrderList();
          break;
      }
    },

    /**
     * 重置订单信息组件
     */
    reset() {
      //重置列表页面和数据
      this.recent_list = [];
      this.recent_list_pageNum = 1;
      this.recent_list_total = 0;
      this.refund_list = [];
      this.refund_list_pageNum = 1;
      this.refund_list_total = 0;
      this.unfinished_list = [];
      this.unfinished_list_pageNum = 1;
      this.unfinished_list_total = 0;
      this.finished_list = [];
      this.finished_list_pageNum = 1;
      this.finished_list_total = 0;
      this.closed_list = [];
      this.closed_list_pageNum = 1;
      this.closed_list_total = 0;

      //查询 全部 最近订单
      this.activeTab = '0';
      this.activeTab0Index = '0';

      //清空输入
      this.orderNoRecentModel = '';
      this.orderNoRefundModel = '';

      //药店id
      this.merchantId = '';
    },

    /**
     * 全部 最近订单
     */
    reqRecentOrderList() {
      //药帮忙进线，判断是否有药店id
      if (this.appId === 1000) {
        if (!this.merchantId) {
          return false;
        }
      }
      const loading = this.$loading({
        target: document.querySelector('.im-config'),
        lock: true,
        spinner: 'el-icon-loading',
        text: '加载中...',
        background: 'rgba(255,255,255, 0.8)',
      });
      queryOrderInfoPage({
        merchantId: this.merchantId,
        orderNo: this.orderNoRecentModel,
        status: Number(this.activeTab),
        pageNum: this.recent_list_pageNum,
        pageSize: this.recent_list_pageSize,
      })
        .then((resp) => {
          if (resp.code === 1) {
            if (Array.isArray(resp.data.list)) {
              this.recent_list = resp.data.list.map(item => {
                return {
                  ...item,
                  isExpand: false,
                  productList: []
                }
              });
              this.recent_list_total = Number(resp.data.total) || 0;

              //滚动到顶部
              this.$nextTick(() => {
                let dataContainerDOM = document.querySelector(
                  '.orderno-search-box-content .orderno-search-box-content-item:first-child .orderno-search-box-content-item__content'
                );
                if (dataContainerDOM) {
                  dataContainerDOM.scrollTop = 0;
                }
              });
            }
          } else {
            this.$XyyMessage.error(resp.msg || '');
          }
        })
        .finally(() => {
          loading.close();
          this.setElPaginationJumpLabel();
        });
    },

    /**
     * 全部 最近订单 翻页事件
     */
    recentListPageChange(pageNum) {
      this.recent_list_pageNum = pageNum;
      this.reqRecentOrderList();
    },

    /**
     * 全部 退款单
     */
    reqRefundOrderList() {
      //药帮忙进线，判断是否有药店id
      if (this.appId === 1000) {
        if (!this.merchantId) {
          return false;
        }
      }
      const loading = this.$loading({
        target: document.querySelector('.im-config'),
        lock: true,
        spinner: 'el-icon-loading',
        text: '加载中...',
        background: 'rgba(255,255,255, 0.8)',
      });
      queryRefundOrderInfoPage({
        memberId: this.merchantId,
        refundOrderNo: this.orderNoRefundModel,
        status: Number(this.activeTab),
        pageNum: this.refund_list_pageNum,
        pageSize: this.refund_list_pageSize,
      })
        .then((resp) => {
          if (resp.code === 1) {
            if (resp.data.list) {
              this.refund_list = resp.data.list;
              this.refund_list_total = Number(resp.data.total) || 0;

              //滚动到顶部
              this.$nextTick(() => {
                let dataContainerDOM = document.querySelector(
                  '.orderno-search-box-content .orderno-search-box-content-item:last-child .orderno-search-box-content-item__content'
                );
                if (dataContainerDOM) {
                  dataContainerDOM.scrollTop = 0;
                }
              });
            }
          } else {
            this.$XyyMessage.error(resp.msg || '');
          }
        })
        .finally(() => {
          loading.close();
          this.setElPaginationJumpLabel();
        });
    },

    /**
     * 全部 退款单 翻页事件
     */
    refundListPageChang(pageNum) {
      this.refund_list_pageNum = pageNum;
      this.reqRefundOrderList();
    },

    /**
     * 全部 最近订单 退款单 切换
     */
    activeTab0IndexChange(index, indexPath) {
      this.activeTab0Index = index;

      //豆芽
      // if (this.appId === 1001) {
      //   return false;
      // }

      switch (this.activeTab0Index) {
        case '0':
          //最新订单
          if (!this.recent_list.length) {
            this.recent_list_pageNum = 1;
            this.reqRecentOrderList();
          }
          break;
        case '1':
          //退款单
          if (!this.refund_list.length) {
            this.refund_list_pageNum = 1;
            this.reqRefundOrderList();
          }
          break;
      }
    },

    /**
     * 全部 搜索
     */
    orderNoSearch() {
      switch (this.activeTab0Index) {
        case '0':
          //最新订单

          //豆芽查询,必须输入订单号
          // if (this.appId === 1001) {
          //   if (!this.orderNoRecentModel) {
          //     this.$XyyMessage.error('请输入订单编号');
          //     return false;
          //   }
          // }

          this.recent_list_pageNum = 1;
          this.reqRecentOrderList();
          break;
        case '1':
          //退款单

          //豆芽查询,必须输入订单号
          // if (this.appId === 1001) {
          //   if (!this.orderNoRefundModel) {
          //     this.$XyyMessage.error('请输入退单编号');
          //     return false;
          //   }
          // }

          this.refund_list_pageNum = 1;
          this.reqRefundOrderList();
          break;
      }
    },

    /**
     * tab切换
     * from undefined:手动切换tab,paging:点击分页
     */
    changeActiveTab(tab, from) {
      /** 
      if (!this.merchantId) {
        return false;
      }
      */

      let params = {
        merchantId: this.merchantId,
        status: Number(tab),
      };
      switch (tab) {
        case '0':
          //全部
          if (this.activeTab0Index === '0') {
            if (!this.recent_list.length) {
              this.recent_list_pageNum = 1;
              this.reqRecentOrderList();
            }
          } else if (this.activeTab0Index === '1') {
            if (!this.refund_list.length) {
              this.refund_list_pageNum = 1;
              this.reqRefundOrderList();
            }
          }
          break;
        case '1':
          //未完成
          params = Object.assign({}, params, {
            pageNum: this.unfinished_list_pageNum,
            pageSize: this.unfinished_list_pageSize,
          });
          this.reqOrderList(params, from, 'unfinished_list');
          break;
        case '2':
          //已完成
          params = Object.assign({}, params, {
            pageNum: this.finished_list_pageNum,
            pageSize: this.finished_list_pageSize,
          });
          this.reqOrderList(params, from, 'finished_list');
          break;
        case '3':
          //已关闭
          params = Object.assign({}, params, {
            pageNum: this.closed_list_pageNum,
            pageSize: this.closed_list_pageSize,
          });
          this.reqOrderList(params, from, 'closed_list');
          break;
      }
    },

    /**
     * 请求tab数据
     */
    reqOrderList(params, from, tabTypeName) {
      if (!from) {
        if (this[tabTypeName].length) {
          return false;
        }
      }
      const loading = this.$loading({
        target: document.querySelector('.im-config'),
        lock: true,
        spinner: 'el-icon-loading',
        text: '加载中...',
        background: 'rgba(255,255,255, 0.8)',
      });
      queryOrderInfoPage(params)
        .then((resp) => {
          if (resp.code === 1) {
            if (Array.isArray(resp.data.list)) {
              this[tabTypeName] = resp.data.list.map(item => {
                return {
                  ...item,
                  isExpand: false,
                  productList: []
                }
              });
              this[`${tabTypeName}_total`] = Number(resp.data.total) || 0;

              //滚动到顶部
              this.$nextTick(() => {
                let dataContainerDOM = document.querySelector(
                  '.tabs-content-item.is-active .order-info__content'
                );
                if (dataContainerDOM) {
                  dataContainerDOM.scrollTop = 0;
                }
              });
            }
          } else {
            this.$XyyMessage.error(resp.msg || '');
          }
        })
        .finally(() => {
          loading.close();
          this.setElPaginationJumpLabel();
        });
    },

    /**
     * 未完成 翻页事件
     */
    unfinishedListPageChange(pageNum) {
      this.unfinished_list_pageNum = pageNum;
      this.changeActiveTab('1', 'paging');
    },

    /**
     * 已完成 翻页事件
     */
    finishedListPageChange(pageNum) {
      this.finished_list_pageNum = pageNum;
      this.changeActiveTab('2', 'paging');
    },

    /**
     * 已关闭 翻页事件
     */
    closedListPageChange(pageNum) {
      this.closed_list_pageNum = pageNum;
      this.changeActiveTab('3', 'paging');
    },

    // 设置分页器jumper的文案
    setElPaginationJumpLabel() {
      this.$nextTick(() => {
        let pList = document.querySelectorAll(`.${this.$el.className} .el-pagination .el-pagination__jump`)
        if (pList.length) {
          pList.forEach(p => {
            p.childNodes[0].nodeValue = "跳转"
            p.childNodes[2].nodeValue = ""
          })
        }
      })
    },




    // order-list-v2事件响应总线
    handleOrderEvent({ handle, orderListName, orderItem, orderIndex, productItem, productIndex }) {
      switch (handle) {
        case 'order-preview': // 查看订单
          this.doOrderPreview({ orderListName, orderItem, orderIndex })
          break;
        case 'order-logistics-preview': // 查看物流
          this.doOrderLogisticsPreview({ orderListName, orderItem, orderIndex })
          break;
        case 'order-invoice-preview': // 查看发票
          this.doOrderInvoicePreview({ orderListName, orderItem, orderIndex })
          break;
        case 'order-invoice-send': // 发送发票
          this.doOrderInvoiceSend({ orderListName, orderItem, orderIndex })
          break;
        case 'order-remark-preview': // 查看备注
          this.doOrderRemarkPreview({ orderListName, orderItem, orderIndex })
          break;
        case 'order-remark-add': // 添加备注
          this.doOrderRemarkAdd({ orderListName, orderItem, orderIndex })
          break;
        case 'order-expand': // 展开&收起
          this.doOrderExpand({ orderListName, orderItem, orderIndex })
          break;
        case 'order-send': // 发送订单
          this.doOrderSend({ orderListName, orderItem, orderIndex })
          break;
        case 'order-product-send': // 发送商品
          this.doOrderProductSend({ orderListName, orderItem, orderIndex, productItem, productIndex })
          break;
      }
    },

    // 查看订单
    doOrderPreview({ orderListName, orderItem, orderIndex }) {
      if (['recent_list', 'unfinished_list', 'finished_list', 'closed_list'].includes(orderListName)) {
        window.open(orderItem.orderDetailUrl, '_blank');
      } else if (['refund_list'].includes(orderListName)) {
        window.open(orderItem.refundOrderDetailUrl, '_blank');
      }
    },

    // 查看物流
    doOrderLogisticsPreview({ orderListName, orderItem, orderIndex }) {
      this.$refs.refDialogOrderLogistics.init({ orderNo: orderItem.orderNo })
    },

    // 查看发票
    doOrderInvoicePreview({ orderListName, orderItem, orderIndex }) {
      this.$refs.refDialogOrderInvoice.init({ orderNo: orderItem.orderNo })
    },

    // 发送发票
    async doOrderInvoiceSend({ orderListName, orderItem, orderIndex }) {
      // 发票只有一张,直接发送; 多张发票,展示弹窗
      try {
        const { code, data, msg } = await queryOrderInvoice({
          orderNo: orderItem.orderNo
        })
        if (code == 1) {
          if (Array.isArray(data) && data.length) {
            if (data.length == 1) {
              this.doOrderInvoiceSendSubmit(data[0])
            } else {
              this.$refs.refDialogOrderInvoice.init({ orderNo: orderItem.orderNo })
            }
          } else {
            this.$refs.refDialogOrderInvoice.init({ orderNo: orderItem.orderNo })
          }
        } else {
          throw new Error(msg)
        }
      } catch (e) {
        this.$XyyMessage.error(e.message);
      } finally { }
    },
    async doOrderInvoiceSendSubmit(invoiceUrl) {
      try {
        const { code, data, msg } = await sendPdfMessage({
          dialogId: this.chat.id,
          url: invoiceUrl
        })
        if (code == 1) {
          // 发票消息发送成功
        } else {
          throw new Error(msg)
        }
      } catch (e) {
        this.$XyyMessage.error(e.message);
      }
    },


    // 查看备注
    doOrderRemarkPreview({ orderListName, orderItem, orderIndex }) {
      this.$refs.refDialogOrderRemark.init({ viewType: 'view', orderNo: orderItem.orderNo })
    },

    // 添加备注
    doOrderRemarkAdd({ orderListName, orderItem, orderIndex }) {
      this.$refs.refDialogOrderRemark.init({ viewType: 'add', orderNo: orderItem.orderNo })
    },

    // 展开&收起
    async doOrderExpand({ orderListName, orderItem, orderIndex }) {
      if (orderItem.isExpand) {
        orderItem.isExpand = false
      } else {
        if (orderItem.productList.length) {
          orderItem.isExpand = true
        } else {
          try {
            const { code, data, msg } = await queryOrderDetailList({
              orderNo: orderItem.orderNo
            })
            if (code == 1) {
              if (Array.isArray(data) && data.length) {
                this[orderListName][orderIndex].productList = data
              }
            } else {
              throw new Error(msg)
            }
          } catch (e) {
            console.log(e.message)
          } finally {
            orderItem.isExpand = true
          }
        }
      }
    },

    // 发送订单
    async doOrderSend({ orderListName, orderItem, orderIndex }) {
      try {
        const { code, data, msg } = await sendOrderInfoMessage({
          dialogId: this.chat.id,
          orderNo: orderItem.orderNo
        })
        if (code == 1) {
          // 订单消息发送成功
        } else {
          throw new Error(msg)
        }
      } catch (e) {
        this.$XyyMessage.error(e.message);
      }
    },

    // 发送商品
    async doOrderProductSend({ orderListName, orderItem, orderIndex, productItem, productIndex }) {
      try {
        const { code, data, msg } = await sendProductMessage({
          dialogId: this.chat.id,
          orderNo: orderItem.orderNo,
          skuId: productItem.skuId
        })
        if (code == 1) {
          // 商品消息发送成功
        } else {
          throw new Error(msg)
        }
      } catch (e) {
        this.$XyyMessage.error(e.message);
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.custome-order-component-container {
  width: 100%;
  flex-grow: 1;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: stretch;
  align-items: flex-start;

  .tabs-header {
    flex-shrink: 0;
    width: 100%;

    .sss-radio-box {
      width: 100%;
      background-color: #ffffff;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      /deep/ {
        .el-radio-button {
          flex: 1;
          border-bottom: 1px solid #D9DEE3;

          .el-radio-button__inner {
            width: 100%;
            background-color: #ffffff;
            border: 0;
            padding: 14px 0;
            line-height: 1;
            box-shadow: none;
            color: #666666;
            font-size: 14px;
            font-weight: 400;
          }

          &.is-active {
            position: relative;

            .el-radio-button__inner {
              color: #3B95A8;
              font-weight: 600;
            }

            &::after {
              content: '';
              width: 100%;
              height: 3px;
              background-color: #3B95A8;
              position: absolute;
              left: 0;
              bottom: -1px;
            }
          }
        }
      }
    }
  }

  .tabs-content {
    flex-grow: 1;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: stretch;
    align-items: flex-start;

    .tabs-content-item {
      flex-grow: 1;
      width: 100%;
      overflow: hidden;
      position: relative;
      display: none;
      flex-direction: column;
      justify-content: stretch;
      align-items: flex-start;

      &.is-active {
        display: flex;
      }

      .order-info__content {
        flex-grow: 1;
        width: 100%;
        overflow-y: scroll;

        //分页
        &+.el-pagination {
          flex-shrink: 0;
          width: 100%;
          padding: 12px;
          color: #666666;
          font-size: 14px;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;

          &::before {
            content: '';
            width: 100%;
            height: 6px;
            background: linear-gradient(to bottom, transparent, #F0F2F5);
            position: absolute;
            top: -6px;
            left: 0;
          }

          /deep/ {
            .btn-prev {
              min-width: 28px;
              padding-right: 0;
            }

            .btn-next {
              min-width: 28px;
              padding-left: 0;
            }

            .el-pagination__jump {
              margin-left: 0;
              margin-right: 10px;
              color: #222222;
              font-size: 13px;
              font-weight: 400;
              display: flex;
              justify-content: flex-end;
              align-items: center;

              .el-pagination__editor {
                width: 36px;

                font-size: 12px;
                color: #222222;
                font-weight: 400;

                .el-input__inner {


                  &:focus {
                    border-color: #3B95A8;
                  }
                }
              }
            }

            .el-pager {
              li {
                min-width: 28px;

                &:hover {
                  color: #3B95A8;
                }

                &.active {
                  color: #3B95A8;
                }
              }
            }
          }
        }
      }

      .content-empty {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        img {
          width: 220px;
          height: 220px;
        }
      }



      // 全部 切换tab
      .el-menu {
        flex-shrink: 0;
        border: 0;
        padding: 16px 12px;

        .el-menu-item {
          height: unset;
          line-height: 1;
          border: 0;
          padding: 7px 12px;
          background-color: #FFFFFF;
          border: 1px solid #ECEDEE !important;
          border-radius: 4px;
          margin-right: 12px;

          &.is-active {
            // background-color: #dae9ec !important;
            border-color: #3B95A8 !important;
            color: #3B95A8 !important;
          }

          &:not(.is-disabled):hover {
            // background-color: #dae9ec !important;
            border-color: #3B95A8 !important;
            color: #3B95A8 !important;
          }
        }
      }

      //全部 结果集
      .orderno-search-box-content {
        flex-grow: 1;
        width: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: stretch;
        align-items: flex-start;

        .orderno-search-box-content-item {
          flex-grow: 1;
          width: 100%;
          position: relative;
          overflow: hidden;
          display: none;
          flex-direction: column;
          justify-content: stretch;
          align-items: flex-start;

          &.is-active {
            display: flex;
          }

          // 全部 输入框查询样式
          .orderno-search-box {
            flex-shrink: 0;
            width: 100%;
            padding: 0 12px;
            display: flex;
            justify-content: flex-start;
            align-items: center;

            .el-input {
              font-size: 13px;

              /deep/ {
                .el-input__inner {
                  &:focus {
                    border-color: #3B95A8;
                  }
                }
              }
            }
          }

          .orderno-search-box-content-item__content {
            flex-grow: 1;
            width: 100%;
            overflow-y: scroll;
          }

          //分页
          .el-pagination {
            flex-shrink: 0;
            width: 100%;
            padding: 12px;
            color: #666666;
            font-size: 14px;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;

            &::before {
              content: '';
              width: 100%;
              height: 6px;
              background: linear-gradient(to bottom, transparent, #F0F2F5);
              position: absolute;
              top: -6px;
              left: 0;
            }

            /deep/ {
              .btn-prev {
                min-width: 28px;
                padding-right: 0;
              }

              .btn-next {
                min-width: 28px;
                padding-left: 0;
              }

              .el-pagination__jump {
                margin-left: 0;
                margin-right: 10px;
                color: #222222;
                font-size: 13px;
                font-weight: 400;
                display: flex;
                justify-content: flex-end;
                align-items: center;

                .el-pagination__editor {
                  width: 36px;

                  font-size: 12px;
                  color: #222222;
                  font-weight: 400;

                  .el-input__inner {


                    &:focus {
                      border-color: #3B95A8;
                    }
                  }
                }
              }

              .el-pager {
                li {
                  min-width: 28px;

                  &:hover {
                    color: #3B95A8;
                  }

                  &.active {
                    color: #3B95A8;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>