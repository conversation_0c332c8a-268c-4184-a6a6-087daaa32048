import request from '@/utils/request-im';
import { formData } from '@/utils/index';

export function upload(data) {
  return request({
    header: { 'Content-Type': 'multipart/form-data' },
    url: '/staff/uploadimg',
    method: 'post',
    data: formData(data)
  });
}

export function savePersonalData(params) {
  return request({
    url: '/personnalInfo/updateinfo',
    params
  });
}

export function getPersonalData() {
  return request({
    url: '/personnalInfo/showbaseinfo',
    method: 'get'
  });
}
