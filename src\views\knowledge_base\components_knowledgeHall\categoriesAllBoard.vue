<template>
  <div class="categories-all-board-component-container">
    <div class="cascader-content">
      <template v-for="(item,index) in cascaderList">
        <template v-if="item.options.length">
          <!-- 有下级分类 -->
          <div :class="['content-item',item.isActive?'active':'']" :key="item.id">
            <el-popover
              :key="cascaderKey+'_'+item.level"
              placement="bottom-start"
              trigger="hover"
              v-model="item.isActive"
              popper-class="cascaderPopover"
              :ref="`refPopover${item.level}`"
            >
              <el-cascader-panel
                :props="{
                expandTrigger: 'hover', //次级菜单的展开方式
                checkStrictly: true, //是否严格的遵守父子节点不互相关联
                label:'nameShort',
                value:'id',
                leaf:'isLeaf'
              }"
                :value="item.value"
                :options="item.options"
                @change="cascaderChange($event,item,index)"
              ></el-cascader-panel>
              <div slot="reference" @click="btnCascaderItmeClick(item)">
                <span :title="item.label">{{ item.labelShort }}</span>
                <i :class="['el-icon',item.isActive?'el-icon-arrow-up':'el-icon-arrow-down']"></i>
              </div>
            </el-popover>
            <i class="el-icon-arrow-right"></i>
          </div>
        </template>
        <template v-else>
          <!-- 有下级分类 -->
          <div class="content-item" :key="item.id">
            <span :title="item.label">{{ item.labelShort }}</span>
            <i class="el-icon-arrow-right"></i>
          </div>
        </template>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'categoriesAllBoard',
  components: {},
  filters: {},
  props: {
    category: {
      type: Array,
      require: true,
      default: () => [],
    },
  },
  inject: ['knowledgeHall'],
  data() {
    return {
      //顶部面包屑list
      cascaderList: [],
      cascaderKey: 0, //级联组件的key
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    /**
     * 根据所选类型级别，初始化cascaderList对象
     */
    cascaderListViewAdapter(categoryIdList) {
      //id数组,即value
      let categoryIds = JSON.parse(JSON.stringify(categoryIdList));

      //所有分类组装
      let cascaderListTemp = [];
      cascaderListTemp.push({
        isActive: false,
        level: 0,
        label: '全部分类',
        labelShort: '全部分类',
        value: JSON.parse(JSON.stringify(categoryIds)),
        options: JSON.parse(JSON.stringify(this.category)),
      });

      //任意分类级别
      let sourceCategory = this.category; //源数据，每次遍历均需赋值
      let leveler = 0; //层级深度，即level
      while (categoryIds[0]) {
        //获取当前分类在数据源中的索引,找不到返回-1
        let index = sourceCategory.findIndex((item) => {
          return item.id === categoryIds[0];
        });

        if (index > -1) {
          leveler++;
          let cascaderItem = {
            isActive: false,
            level: leveler,
            label: sourceCategory[index].name,
            labelShort: sourceCategory[index].nameShort,
            parentId: categoryIds[0], //上一父级的id
          };
          categoryIds.shift(); //删除第一项
          cascaderItem = Object.assign({}, cascaderItem, {
            value: JSON.parse(JSON.stringify(categoryIds)), //绑定删除了第一项的所有下级
            options: JSON.parse(
              JSON.stringify(sourceCategory[index].children || [])
            ),
          });
          cascaderListTemp.push(cascaderItem);

          //下级数据源赋值
          sourceCategory = JSON.parse(
            JSON.stringify(sourceCategory[index].children || [])
          );
        }
      }

      this.cascaderList = []; //清空所有值
      ++this.cascaderKey; //给动态生成的级联组件设置动态key，强制组件每次赋值必重渲染
      this.cascaderList = cascaderListTemp;
    },

    /**
     * 面包屑导航分类的点击
     */
    btnCascaderItmeClick(cascaderItme) {
      switch (cascaderItme.level) {
        case 0: //全部分类
          this.knowledgeHall.toQueryCategoryAll();
          break;
        case 1: //一级分类
        case 2: //二级分类
        case 3: //三级分类
          let categoryIdsClicked = [];
          for (let i = 1; i <= cascaderItme.level; i++) {
            if (this.cascaderList[i].hasOwnProperty('parentId')) {
              categoryIdsClicked.push(this.cascaderList[i].parentId);
            }
          }
          this.knowledgeHall.toQueryCategoryLv(categoryIdsClicked);
          break;
      }
    },

    /**
     * 点击任意一个级联
     */
    cascaderChange(e, cascaderItme, cascaderIndex) {
      let categoryIdsChanged = [];
      if (cascaderItme.level === 0) {
        //全部分类的级联
        categoryIdsChanged.push(...e);
      } else {
        //其他动态生成的级联
        for (let i = 1; i <= cascaderItme.level; i++) {
          if (this.cascaderList[i].hasOwnProperty('parentId')) {
            categoryIdsChanged.push(this.cascaderList[i].parentId);
          }
        }
        categoryIdsChanged.push(...e);
      }
      this.knowledgeHall.toQueryCategoryLv(categoryIdsChanged);

      //动态创建时重置为false了，不用再主动设置
      //隐藏选中的级联弹层
      // this.$nextTick(() => {
      //   //this.$refs[`refPopover${cascaderItme.level}`][0].doClose();
      //   //cascaderItme.isActive = false;
      //   this.cascaderList[cascaderIndex].isActive = false;
      // });
    },
  },
};
</script>

<style lang="scss" scoped>
.categories-all-board-component-container {
  width: 100%;
  padding: 20px;
  background-color: #fff;

  .cascader-content {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .content-item {
      box-sizing: border-box;
      position: relative;

      &:last-child {
        span {
          font-size: 14px;
          color: #909399;
        }

        .el-icon-arrow-right {
          display: none;
        }
      }

      .el-icon-arrow-right {
        content: '';
        width: 16px;
        height: 16px;
        color: #aeaebf;
        position: absolute;
        right: 10px;
        top: 10px;
      }

      .el-popover__reference {
        box-sizing: border-box;
        width: 200px;
        height: 36px;
        border-radius: 3px;
        padding: 0 12px;
        border: 1px solid #e4e4eb;
        cursor: pointer;
        margin-right: 36px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        span {
          font-size: 14px;
          color: #575766;
        }

        .el-icon {
          color: #aeaebf;
        }
      }

      &.active .el-popover__reference {
        border-color: #3b95a8;
      }
    }
  }
}
</style>
<style lang="scss">
.el-cascader-node > .el-radio {
  margin-right: 0;
  position: absolute;
  line-height: 34px;
  width: 100%;
  padding: 0 30px 0 20px;
  margin-left: -20px;
  opacity: 0;
}

.el-cascader-node__label {
  padding-left: 0;
}
</style>