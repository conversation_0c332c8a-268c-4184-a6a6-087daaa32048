<template>
  <div id="tags-view-container"
       class="tags-view-container">
    <scroll-pane ref="scrollPane"
                 class="tags-view-wrapper">
      <router-link v-for="tag in visitedViews"
                   ref="tag"
                   :key="tag.path"
                   :class="isActive(tag)?'active':''"
                   :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
                   tag="span"
                   class="tags-view-item"
                   @click.middle.native="closeSelectedTag(tag)">
        <!-- 19.08.26 修改成取用原始的meta.title (tag.title -> 是tagsView.js中将meta.title赋值到外面的title  根据path的判断 相同path的时候不会自动刷新)-->
        {{ tag.meta.title || 'no-name' }}
        <span v-if="!tag.meta.affix"
              class="el-icon-close"
              @click.prevent.stop="closeSelectedTag(tag)" />
      </router-link>
    </scroll-pane>
  </div>
</template>

<script>
import ScrollPane from './ScrollPane';
import path from 'path';

export default {
  components: { ScrollPane },
  data () {
    return {
      affixTags: [],
    };
  },
  computed: {
    visitedViews () {
      return this.$store.state.tagsView.visitedViews;
    },
    routes () {
      return this.$store.state.permission.routes;
    },
  },
  watch: {
    $route () {
      this.addTags();
      this.moveToCurrentTag();
    },
  },
  mounted () {
    this.initTags();
    this.addTags();
  },
  methods: {
    isActive (route) {
      //return route.path === this.$route.path;
      if (route.path === this.$route.path) {
        this.$store.commit('tagsView/SET_ACTIVE_TAGVIEW_ROUTE', route);
        return true;
      } else {
        return false;
      }
    },
    filterAffixTags (routes, basePath = '/') {
      let tags = [];
      routes.forEach((route) => {
        if (route.meta && route.meta.affix) {
          const tagPath = path.resolve(basePath, route.path);
          tags.push({
            fullPath: tagPath,
            path: tagPath,
            name: route.name,
            meta: { ...route.meta },
          });
        }
        if (route.children) {
          const tempTags = this.filterAffixTags(route.children, route.path);
          if (tempTags.length >= 1) {
            tags = [...tags, ...tempTags];
          }
        }
      });
      return tags;
    },
    initTags () {
      const affixTags = (this.affixTags = this.filterAffixTags(this.routes));
      for (const tag of affixTags) {
        // Must have tag name
        if (tag.name) {
          this.$store.dispatch('tagsView/addVisitedView', tag);
        }
      }
    },
    addTags () {
      const { name, path } = this.$route;
      if (name) {
        // 不打开页签的白名单
        const whitePath = ['/empty', '/error/500', '/error/NoNetwork'];
        if (whitePath.indexOf(path) > -1) {
          // 如果是空白页，就清除所有导航标签
          this.$store.dispatch('tagsView/delAllViews', this.$route);
        } else {
          this.$store.dispatch('tagsView/addView', this.$route);
        }
      }
      return false;
    },
    moveToCurrentTag () {
      const tags = this.$refs.tag;
      this.$nextTick(() => {
        if (tags) {
          for (const tag of tags) {
            if (tag.to.path === this.$route.path) {
              this.$refs.scrollPane.moveToTarget(tag);
              // when query is different then update
              if (tag.to.fullPath !== this.$route.fullPath) {
                this.$store.dispatch('tagsView/updateVisitedView', this.$route);
              }
              break;
            }
          }
        }
      });
    },
    closeSelectedTag (view) {
      if (
        (view.name.indexOf('newSheet') != -1 &&
          view.query.editSuccess === 'false') ||
        (view.name.indexOf('newSheet') != -1 && view.query.sheetUnsaved)
      ) {
        this.emits(this, 'send', 'tagclose_newSheet', view.path);
        return;
      }

      if (view.path.indexOf('/imcontainer/list') != -1) {
        this.emits(this, 'send', 'tagclose_imcontainer');
      }

      this.$store
        .dispatch('tagsView/delView', view)
        .then(({ visitedViews }) => {
          if (this.isActive(view)) {
            this.toLastView(visitedViews, view);
          }
        });
    },
    toLastView (visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0];
      if (latestView) {
        this.$router.push(latestView);
      } else {
        // 如果不存在最后一页则跳到空白页
        this.$router.push('/empty');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';
.tags-view-container {
  height: 50px;
  /*width: 100%;*/
  /*padding: 0 20px;*/
  background: $appMainBg;
  overflow: hidden;
  // border-bottom: 1px solid #d8dce5;
  // box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);
  .tags-view-wrapper {
    .tags-view-item {
      display: inline-block;
      position: relative;
      cursor: pointer;
      height: 36px;
      line-height: 36px;
      border: 1px solid #e4e4eb;
      color: #495060;
      border-top-left-radius: 3px;
      border-top-right-radius: 3px;
      background: rgba(245, 247, 250, 1);
      padding: 0 12px;
      font-size: 14px;
      margin-left: 5px;
      margin-top: 6px;
      &:first-of-type {
        margin-left: 0;
      }
      /*&:last-of-type {*/
      /*margin-right: 19px;*/
      /*}*/
      &.active {
        border-color: #e4e4eb;
        border-bottom-color: #fff;
        background-color: #fff;
        color: #292933;
        // &::before {
        //   content: '';
        //   background: $navBarBg;
        //   display: inline-block;
        //   width: 8px;
        //   height: 8px;
        //   border-radius: 50%;
        //   position: relative;
        //   margin-right: 2px;
        // }
      }
    }
  }
}
</style>

<style lang="scss">
//reset element css of el-icon-close
.tags-view-wrapper {
  padding-top: 8px;
  .tags-view-item {
    .el-icon-close {
      width: 20px;
      height: 20px;
      font-size: 20px;
      vertical-align: -1px;
      border-radius: 50%;
      text-align: center;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transform-origin: 100% 50%;
      &:before {
        transform: scale(0.6);
        display: inline-block;
        vertical-align: -1px;
      }
      // &:hover {
      //   background-color: #b4bccc;
      //   color: #fff;
      // }
    }
  }
}
</style>
