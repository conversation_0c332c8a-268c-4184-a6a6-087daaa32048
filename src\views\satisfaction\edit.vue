<template>
  <div class="satisfaction-form">
    <el-form ref="form" :model="form" :rules="rules" label-width="113px">
      <el-form-item label="评价模板名称" prop="tpName">
        <el-input v-model.trim="form.tpName" maxlength="20" placeholder="不超过20个字"></el-input>
      </el-form-item>
      <el-form-item label="说明" prop="tpDescribe">
        <el-input
          v-model="form.tpDescribe"
          :show-word-limit="true"
          type="textarea"
          maxlength="200"
          resize="none"
          placeholder="不超过200个字"
        ></el-input>
      </el-form-item>
      <el-form-item label="适用范围" class="is-required">
        <el-button
          v-show="!scopeDatas.length"
          icon="el-icon-plus"
          @click="dialogVisible=true"
        >关联表单应用</el-button>
        <div v-show="scopeDatas.length" class="scope-box">
          <el-tag
            v-for="data in scopeDatas"
            :key="data.id"
            closable
            type="info"
            @close="delScopeData(data.id)"
          >{{ data.name }}</el-tag>
          <el-button icon="el-icon-plus" @click="dialogVisible=true">关联表单应用</el-button>
        </div>
      </el-form-item>
      <el-form-item label="评价维度1" prop="vdoing1">
        <el-input v-model.trim="form.vdoing1" maxlength="5" placeholder="不超过5个字"></el-input>
      </el-form-item>
      <el-form-item label="星级设置" class="star-box is-required">
        <el-row>
          <el-col :span="12">星级</el-col>
          <el-col :span="12">提示内容</el-col>
        </el-row>
        <el-row v-for="(val,key) in form.vdoing1Star" :key="key">
          <el-col :span="12">
            <el-rate :value="Number(key)" disabled></el-rate>
          </el-col>
          <el-col :span="12">
            <el-input v-model="form.vdoing1Star[key]" maxlength="5" placeholder="不超过5个字"></el-input>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="评价维度2" prop="vdoing2">
        <el-input v-model.trim="form.vdoing2" maxlength="5" placeholder="不超过5个字"></el-input>
      </el-form-item>
      <el-form-item label="星级设置" class="star-box is-required">
        <el-row>
          <el-col :span="12">星级</el-col>
          <el-col :span="12">提示内容</el-col>
        </el-row>
        <el-row v-for="(val,key) in form.vdoing2Star" :key="key">
          <el-col :span="12">
            <el-rate :value="Number(key)" disabled></el-rate>
          </el-col>
          <el-col :span="12">
            <el-input v-model="form.vdoing2Star[key]" maxlength="5" placeholder="不超过5个字"></el-input>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="发送时机" class="height-20">工单完结立即发送</el-form-item>
      <el-form-item label=" ">
        <el-button type="primary" @click="checkTimer(save,'timer')()">保存</el-button>
      </el-form-item>
    </el-form>
    <transfer
      ref="transfer"
      :dialog-visible.sync="dialogVisible"
      :checked="scopeDatas"
      title="选择评价模板适用范围"
      @callback="setScopeDatas"
    ></transfer>
  </div>
</template>

<script>
import transfer from './components/transfer';
import {
  saveTemplateData,
  editTemplateData,
  getTemplateData,
  getFormDatas
} from '@/api/satisfaction';
export default {
  components: {
    transfer
  },
  data() {
    return {
      form: {
        tpName: '', // 名称
        tpDescribe: '', // 说明
        appliedRange: [], // 适用范围
        vdoing1: '', // 评价维度1
        vdoing1Star: {
          '1': '',
          '2': '',
          '3': '',
          '4': '',
          '5': ''
        }, // 评价维度1 星级设置
        vdoing2: '', // 评价维度2
        vdoing2Star: {
          '1': '',
          '2': '',
          '3': '',
          '4': '',
          '5': ''
        }, // 评价维度2 星级设置
        id: ''
      },
      timer: null,
      dialogVisible: false, // 表单弹框
      scopeDatas: [], // 适用范围数据
      rules: {
        tpName: [
          { required: true, message: '请输入评价模板名称', trigger: 'blur' },
          { max: 20, message: '不超过20个字', trigger: 'blur' }
        ],
        tpDescribe: [{ max: 200, message: '不超过200个字', trigger: 'blur' }],
        vdoing1: [
          { required: true, message: '请输入评价维度1', trigger: 'blur' },
          { max: 5, message: '不超过5个字', trigger: 'blur' }
        ],
        vdoing2: [
          { required: true, message: '请输入评价维度2', trigger: 'blur' },
          { max: 5, message: '不超过5个字', trigger: 'blur' }
        ]
      }
    };
  },
  mounted() {
    if (this.$route.query.id) {
      this.$store.dispatch('tagsView/updateVisitedView', {
        ...this.$route,
        meta: {
          title: '编辑评价模板'
        }
      });
      this.form.id = this.$route.query.id;
      this.initDatas(this.$route.query.tpCode);
    }
  },
  methods: {
    save() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (!this.scopeDatas.length) {
            this.$XyyMessage.error('请选择适用范围');
            return false;
          } else if (!this.validateStar(this.form.vdoing1Star)) {
            this.$XyyMessage.error('请核对评价维度1的星级设置');
            return false;
          } else if (!this.validateStar(this.form.vdoing2Star)) {
            this.$XyyMessage.error('请核对评价维度2的星级设置');
            return false;
          }
          this.form.appliedRange = this.scopeDatas.map(el => Number(el.id));
          if (this.form.id) {
            editTemplateData(this.form)
              .then(res => {
                if (res.code === 1) {
                  this.$XyyMessage.success('模板更新成功');
                  this.$store.dispatch('tagsView/delView', this.$route);
                  this.$router.replace({
                    name: 'satisfactionList'
                  });
                } else {
                  this.$XyyMessage.error(res.msg);
                }
              })
              .catch(() => {});
          } else {
            saveTemplateData(this.form)
              .then(res => {
                if (res.code === 1) {
                  this.$XyyMessage.success('模板新建成功');
                  this.$store.dispatch('tagsView/delView', this.$route);
                  this.$router.replace({
                    name: 'satisfactionList'
                  });
                } else {
                  this.$XyyMessage.error(res.msg);
                }
              })
              .catch(() => {});
          }
        }
      });
    },
    /**
     * 设置适用范围数据
     */
    setScopeDatas(datas) {
      this.scopeDatas = JSON.parse(JSON.stringify(datas));
    },
    /**
     * 删除适用范围数据
     */
    delScopeData(id) {
      this.scopeDatas = this.scopeDatas.filter(el => el.id !== id);
    },
    /**
     * 校验星级设置
     */
    validateStar(obj) {
      let pass = true;
      for (const val in obj) {
        if (!obj[val]) {
          pass = false;
          break;
        }
      }
      return pass;
    },
    /**
     * 初始化数据
     */
    initDatas(code) {
      getTemplateData({ tpCode: code })
        .then(res => {
          if (res.code === 1) {
            this.form = {
              tpName: res.data.tpName,
              tpDescribe: res.data.tpDescribe,
              appliedRange: JSON.parse(res.data.appliedRange),
              vdoing1: res.data.vdoing1,
              vdoing1Star: JSON.parse(
                res.data.vdoing1Star.replace(/(&quot;)/g, '"')
              ),
              vdoing2: res.data.vdoing2,
              vdoing2Star: JSON.parse(
                res.data.vdoing2Star.replace(/(&quot;)/g, '"')
              ),
              id: this.form.id
            };
            getFormDatas()
              .then(_res => {
                if (_res.code === 1) {
                  const datas = _res.data.fromInfo;
                  let arr = [];
                  datas.forEach(el => {
                    arr = arr.concat(el.base);
                  });
                  this.scopeDatas = arr.filter(el =>
                    this.form.appliedRange.includes(Number(el.id))
                  );
                } else {
                  this.$XyyMessage.error(_res.msg);
                }
              })
              .catch(() => {});
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    }
  }
};
</script>

<style lang="scss" scoped>
.satisfaction-form {
  .el-form {
    width: 550px;
    margin: 20px auto;
    .el-form-item {
      margin-bottom: 20px;
      &.height-20 {
        /deep/ .el-form-item__label,
        /deep/ .el-form-item__content {
          line-height: 20px;
        }
      }
      /deep/.el-form-item__label {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(41, 41, 51, 1);
      }
      .el-input {
        /deep/.el-input__inner {
          height: 36px;
          line-height: 36px;
          // border-radius: 2px;
          border: 1px solid rgba(228, 228, 235, 1);
        }
      }
      .el-textarea {
        /deep/.el-textarea__inner {
          height: 211px;
          // border-radius: 2px;
          border: 1px solid rgba(228, 228, 235, 1);
        }
      }
      .el-button {
        height: 36px;
        padding: 0 12px;
        line-height: 36px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
        border-radius: 2px;
        border: 1px solid rgba(228, 228, 235, 1);
        &:focus,
        &:hover {
          background: #fff;
          border-color: rgba(228, 228, 235, 1);
        }
        &.el-button--primary {
          color: rgba(255, 255, 255, 1);
          padding: 0 20px;
          border: none;
        }
        &.el-button--primary:focus,
        &.el-button--primary:hover {
          background: #3b95a8;
          border-color: #3b95a8;
        }
      }
      /deep/ .el-form-item__content {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
      }
      &.star-box {
        /deep/.el-form-item__content {
          background: rgba(245, 247, 250, 0.5);
          border-radius: 2px;
          border: 1px solid rgba(228, 228, 235, 1);
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(144, 147, 153, 1);
          text-align: center;
          padding: 8px 20px 12px 12px;
          .el-rate {
            margin-top: 10px;
          }
          .el-input {
            .el-input__inner {
              height: 28px;
              line-height: 28px;
              background: rgba(255, 255, 255, 1);
              // border-radius: 2px;
              border: 1px solid rgba(228, 228, 235, 1);
            }
          }
        }
      }
      .scope-box {
        height: 156px;
        overflow-y: auto;
        border-radius: 2px;
        border: 1px solid rgba(228, 228, 235, 1);
        padding: 12px;
        .el-tag {
          margin: 0 12px 12px 0;
          height: 36px;
          line-height: 36px;
          background: rgba(245, 247, 250, 1);
          border-radius: 2px;
          border: 1px solid rgba(228, 228, 235, 1);
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(87, 87, 102, 1);
          /deep/.el-tag__close.el-icon-close:hover {
            background: rgba(245, 247, 250, 1);
            color: rgba(87, 87, 102, 1);
          }
        }
      }
    }
  }
}
</style>
