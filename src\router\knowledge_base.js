import Layout from '@/views/layout/Layout';
export default [
  {
    path: '/knowledge_base',
    component: Layout,
    name: 'knowledge_base',
    // redirect: '/monitor/list',
    meta: {
      title: '知识库',
      icon: 'knowledge_base',
      affix: true,
      code: 'menu:cs:knowledge_base',
      mark: 'knowledge_base'
    },
    children: [
      {
        path: 'knowledgeManagement',
        name: 'knowledgeManagement',
        meta: {
          title: '知识管理',
          code: 'menu:cs:knowledgeManagement',
          mark: 'knowledge_base',
          componentName: 'knowledgeManagement'
        },
        component: () => import('@/views/knowledge_base/knowledgeManagement')
      },
      {
        path: 'knowledgeManagementEdit/:id',
        name: 'knowledgeManagementEdit',
        meta: {
          title: '新建知识',
          code: 'menu:cs:knowledgeManagement',
          mark: 'knowledge_base'
          // componentName: 'KnowledgeManagementEdit'
        },
        component: () =>
          import('@/views/knowledge_base/knowledgeManagementEdit'),
        hidden: true
      },
      {
        path: 'draftBoxList',
        name: 'draftBoxList',
        meta: {
          title: '草稿箱',
          code: 'menu:cs:knowledgeManagement',
          mark: 'knowledge_base',
          componentName: 'DraftBoxList'
        },
        component: () => import('@/views/knowledge_base/draftBoxList'),
        hidden: true
      },
      {
        path: 'knowledgeDetails/:id',
        name: 'knowledgeDetails',
        meta: {
          title: '知识详情',
          code: 'menu:cs:knowledgeHall',
          mark: 'knowledge_base',
          componentName: 'KnowledgeDetails'
        },
        component: () => import('@/views/knowledge_base/knowledgeDetails'),
        hidden: true
      },
      {
        path: 'knowledgeHistory/:id',
        name: 'knowledgeHistory',
        meta: {
          title: '知识历史',
          code: 'menu:cs:knowledgeManagement',
          mark: 'knowledge_base',
          componentName: 'KnowledgeHistory'
        },
        component: () => import('@/views/knowledge_base/knowledgeHistory'),
        hidden: true
      },
      {
        path: 'knowledgeHall',
        name: 'knowledgeHall',
        meta: {
          title: '知识大厅',
          code: 'menu:cs:knowledgeHall',
          mark: 'knowledge_base',
          componentName: 'knowledgeHall'
        },
        component: () => import('@/views/knowledge_base/knowledgeHall')
      },
      {
        path: 'announcementManagement',
        name: 'announcementManagement',
        meta: {
          title: '公告管理',
          code: 'menu:cs:announcementManagement',
          mark: 'knowledge_base',
          componentName: 'announcementManagement'
        },
        component: () => import('@/views/knowledge_base/announcementManagement')
      },
      {
        path: 'announcementDetails/:id',
        name: 'announcementDetails',
        meta: {
          title: '公告详情',
          code: 'menu:cs:announcementHall',
          mark: 'knowledge_base',
          componentName: 'AnnouncementDetails'
        },
        component: () => import('@/views/knowledge_base/announcementDetails'),
        hidden: true
      },
      {
        path: 'announcementManagementEdit/:id',
        name: 'announcementManagementEdit',
        meta: {
          title: '新建公告',
          code: 'menu:cs:announcementManagement',
          mark: 'knowledge_base'
          // componentName: 'AnnouncementManagementEdit'
        },
        component: () =>
          import('@/views/knowledge_base/announcementManagementEdit'),
        hidden: true
      },
      {
        path: 'announcementHall',
        name: 'announcementHall',
        meta: {
          title: '公告大厅',
          code: 'menu:cs:announcementHall',
          mark: 'knowledge_base',
          componentName: 'announcementHall'
        },
        component: () => import('@/views/knowledge_base/announcementHall')
      },
      {
        path: 'knowledgeCollection',
        name: 'knowledgeCollection',
        meta: {
          title: '我的收藏',
          code: 'menu:cs:knowledgeHall',
          mark: 'knowledge_base',
          componentName: 'KnowledgeCollection'
        },
        component: () => import('@/views/knowledge_base/knowledgeCollection'),
        hidden: true
      }
    ]
  }
];
