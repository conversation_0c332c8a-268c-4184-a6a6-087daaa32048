<template>
  <div class="item-content">
    <el-card class="box-card" shadow="never">
      <header class="box row-between">
        <div class="box row-start column-center left">
          <h3>工单监控</h3>
          <info :info="info"></info>
        </div>
      </header>
      <el-row>
        <el-col :xs="12" :sm="12" :lg="{span: '4-8'}" class="card-panel-col">
          <div class="item">
            <div class="img">
              <svg-icon class="iconsvg" icon-class="icon_day_create_order"></svg-icon>
            </div>
            <div class="right">
              <div class="count">{{ data.createWorkOrderCount }}</div>
              <div class="normal">当日创建工单数</div>
            </div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="12" :lg="{span: '4-8'}" class="card-panel-col">
          <div class="item">
            <div class="img">
              <svg-icon class="iconsvg" icon-class="icon_to_receive_order"></svg-icon>
            </div>
            <div class="right">
              <div class="count">{{ data.unclaimedWorkOrderCount }}</div>
              <div class="normal">待领取工单数</div>
            </div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="12" :lg="{span: '4-8'}" class="card-panel-col">
          <div class="item">
            <div class="img">
              <svg-icon class="iconsvg" icon-class="icon_the_timeout_order"></svg-icon>
            </div>
            <div class="right">
              <div class="count">{{ data.soonTimeoutWorkOrderCount }}</div>
              <div class="normal">即将超时工单数</div>
            </div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="12" :lg="{span: '4-8'}" class="card-panel-col">
          <div class="item">
            <div class="img">
              <svg-icon class="iconsvg" icon-class="icon_overtime_work_order"></svg-icon>
            </div>
            <div class="right">
              <div class="count">{{ data.timeOutWorkOrderCount }}</div>
              <div class="normal">超时工单总数</div>
            </div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="12" :lg="{span: '4-8'}" class="card-panel-col">
          <div class="item">
            <div class="img">
              <svg-icon class="iconsvg" icon-class="icon_overdue_payment_order"></svg-icon>
            </div>
            <div class="right">
              <div class="count">{{ data.timeOutUnclaimedWorkOrderCount }}</div>
              <div class="normal">超时未领取工单总数</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'Statics2Item',
  data() {
    return {
      info: [
        {
          title: '当日创建工单数',
          info: '工单创建时间在当日内，计算各用户组&组内人员创建工单的总量'
        },
        {
          title: '待领取工单数',
          info: '截止到当前时间，工单状态为待领取的总量'
        },
        {
          title: '即将超时工单数',
          info: '截止到当前时间，已经超时的工单总量'
        },
        {
          title: '超时工单总数',
          info: '截止到当前时间，已经超时的工单总量'
        },
        {
          title: '超时未领取工单数',
          info: '截止到当前时间，已经超时且状态为待领取的工单总量'
        }
      ],
      data: {
        createWorkOrderCount: 0, // 当日创建工单数
        unclaimedWorkOrderCount: 0, // 待领取工单数
        soonTimeoutWorkOrderCount: 0, // 即将超时工单数
        timeOutWorkOrderCount: 0, // 超时工单总数
        timeOutUnclaimedWorkOrderCount: 0 // 超时未领取工单总数
      }
    };
  },
  mounted() {},
  methods: {
    initData(arr) {
      this.data['waitCount'] = 0;
      this.data['callCount'] = 0;
      arr[1].forEach(item => {
        this.data.waitCount += parseInt(item);
      });
      arr[2].forEach(item => {
        this.data.callCount += parseInt(item);
      });
      // this.data.visitCount = this.data.waitCount + this.data.callCount;
    },
    setPersonCount(data) {
      this.data.createWorkOrderCount = data.createWorkOrderCount;
      this.data.unclaimedWorkOrderCount = data.unclaimedWorkOrderCount;
      this.data.soonTimeoutWorkOrderCount = data.soonTimeoutWorkOrderCount;
      this.data.timeOutWorkOrderCount = data.timeOutWorkOrderCount;
      this.data.timeOutUnclaimedWorkOrderCount =
        data.timeOutUnclaimedWorkOrderCount;
    }
  }
};
</script>
<style>
.el-card__body {
  padding-right: 0;
}
</style>
<style lang="scss" scoped>
.item-content {
  width: 100%;
  min-width: 1080px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(48, 49, 51, 1);

  .box-card {
    min-width: 1080px !important;
    overflow: auto !important;
  }

  .el-col-lg-4-8 {
    width: 20%;
  }

  .item {
    padding: 24px;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.08);
    border-radius: 2px;
    padding: 14px 8px;
    margin-right: 20px;
    display: flex;
    img {
      vertical-align: super;
      margin-right: 16px;
    }
    .right {
      margin-left: 20px;
      display: inline-block;
      .count {
        font-size: 24px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        line-height: 33px;
        color: rgba(48, 49, 51, 1);
      }
      .normal {
        line-height: 22px;
        color: #909399;
      }
      .des {
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(174, 174, 191, 1);
        line-height: 13px;
      }
    }
  }
  .iconsvg {
    width: 50px;
    height: 50px;
  }

  .clearfix {
    position: relative;

    .titlefont {
      float: left;
      font-weight: bold;
      font-size: 16px;
    }

    .info-container {
      float: left;

      /deep/ {
        .explain {
          line-height: normal !important;
          width: fit-content;
        }
      }
    }
  }
}
</style>>

