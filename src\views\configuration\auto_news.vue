<template>
  <div class="newsConfig">
    <!-- 坐席无应答自动消息 -->
    <el-form ref="ruleForm" status-icon label-width="100px" class="demo-ruleForm">
      <template>
        <el-row class="_title" type="flex" justify="start" align="middle">
          <span class="borline"></span>
          <span class="content">坐席无应答自动消息</span>
        </el-row>
        <el-row class="isConfing" type="flex" justify="start" align="top">
          <span class="_lable">开启此功能</span>
          <div>
            <el-row>
              <el-radio v-model="seatAuto.isOpen" :label="true">开启</el-radio>
              <el-radio v-model="seatAuto.isOpen" :label="false">关闭</el-radio>
            </el-row>
            <el-row class="tip">当坐席超过设置时间没有回复访客，系统自动向访客发送一条系统消息</el-row>
          </div>
        </el-row>
        <el-row class="isConfing" type="flex" justify="start" align="middle">
          <span class="_lable">在线客服超过</span>
          <el-row>
            <el-input
              :disabled="!seatAuto.isOpen"
              v-model="seatAuto.timeLimt"
              :maxlength="3"
              onkeyup="if(this.value<=0) { value = null } this.value=this.value.replace(/[^\d.]/g,'');"
              class="_time"
              placeholder="请输入"
            >
              <svg-icon slot="suffix" icon-class="second" class="second" />
            </el-input>
            <span class="timeTip">秒没有回应访客消息时，系统自动发送以下消息</span>
          </el-row>
        </el-row>
        <el-row class="isConfing" type="flex" justify="start" align="middle">
          <span class="_lable">自动消息内容</span>
          <el-row>
            <el-input
              :disabled="!seatAuto.isOpen"
              v-model="seatAuto.content"
              type="textarea"
              placeholder="请输入内容"
              maxlength="100"
              class="autoInfo"
              show-word-limit
            ></el-input>
          </el-row>
        </el-row>
        <el-row></el-row>
      </template>
    </el-form>
    <!-- 访客无应答自动消息 -->
    <template>
      <el-row class="_title mar_top20" type="flex" justify="start" align="middle">
        <span class="borline"></span>
        <span class="content">访客无应答自动消息</span>
      </el-row>
      <el-row class="isConfing" type="flex" justify="start" align="top">
        <span class="_lable">开启此功能</span>
        <div>
          <el-row>
            <el-radio v-model="guestAuto.isOpen" :label="true">开启</el-radio>
            <el-radio v-model="guestAuto.isOpen" :label="false">关闭</el-radio>
          </el-row>
          <el-row class="tip">访客超过设置时间没有回复坐席，系统自动向访客发送一条消息</el-row>
        </div>
      </el-row>
      <el-row class="isConfing" type="flex" justify="start" align="middle">
        <span class="_lable">访客超过</span>
        <el-row>
          <el-input
            :disabled="!guestAuto.isOpen"
            v-model="guestAuto.timeLimt"
            :maxlength="3"
            onkeyup="if(this.value<=0) { value = null } this.value=this.value.replace(/[^\d.]/g,'');"
            class="_time"
            placeholder="请输入"
          >
            <svg-icon slot="suffix" icon-class="second" class="second" />
          </el-input>
          <span class="timeTip">秒没有回应坐席消息时，系统自动发送以下消息</span>
        </el-row>
      </el-row>
      <el-row class="isConfing" type="flex" justify="start" align="middle">
        <span class="_lable">自动消息内容</span>
        <el-row>
          <el-input
            :disabled="!guestAuto.isOpen"
            v-model="guestAuto.content"
            type="textarea"
            placeholder="请输入内容"
            maxlength="100"
            class="autoInfo"
            show-word-limit
          ></el-input>
        </el-row>
      </el-row>
      <el-row></el-row>
    </template>
    <!-- 访客超时结束会话   -->
    <template>
      <el-row class="_title mar_top20" type="flex" justify="start" align="middle">
        <span class="borline"></span>
        <span class="content">访客超时结束会话</span>
      </el-row>
      <el-row class="isConfing" type="flex" justify="start" align="top">
        <span class="_lable">开启此功能</span>
        <div>
          <el-row>
            <el-radio v-model="guestOverTime.isOpen" :label="true">开启</el-radio>
            <el-radio v-model="guestOverTime.isOpen" :label="false">关闭</el-radio>
          </el-row>
          <el-row class="tip">当会话超过设置时间且没有新消息产生，系统自动向访客发送一条消息，并结束会话</el-row>
        </div>
      </el-row>
      <el-row class="isConfing" type="flex" justify="start" align="middle">
        <span class="_lable">访客超过</span>
        <el-row>
          <el-input
            :disabled="!guestOverTime.isOpen"
            v-model="guestOverTime.timeLimt"
            :maxlength="3"
            onkeyup="if(this.value<=0) { value = null } this.value=this.value.replace(/[^\d.]/g,'');"
            class="_time"
            placeholder="请输入"
          >
            <svg-icon slot="suffix" icon-class="second" class="second" />
          </el-input>
          <span class="timeTip">秒没有回应坐席消息时，系统自动发送以下消息</span>
        </el-row>
      </el-row>
      <el-row class="isConfing" type="flex" justify="start" align="middle">
        <span class="_lable">自动消息内容</span>
        <el-row>
          <el-input
            :disabled="!guestOverTime.isOpen"
            v-model="guestOverTime.content"
            type="textarea"
            placeholder="请输入内容"
            maxlength="100"
            class="autoInfo"
            show-word-limit
          ></el-input>
        </el-row>
      </el-row>
    </template>
    <!-- 访客即将超时提醒坐席 -->
    <template>
      <el-row class="_title mar_top20" type="flex" justify="start" align="middle">
        <span class="borline"></span>
        <span class="content">访客即将超时提醒坐席</span>
      </el-row>
      <el-row class="isConfing" type="flex" justify="start" align="top">
        <span class="_lable">开启此功能</span>
        <div>
          <el-row>
            <el-radio v-model="RemindCustomerServiceTime.isOpen" :label="true">开启</el-radio>
            <el-radio v-model="RemindCustomerServiceTime.isOpen" :label="false">关闭</el-radio>
          </el-row>
          <el-row class="tip">当会话超过设置时间且没有新消息产生，系统自动向客服发送一条消息</el-row>
        </div>
      </el-row>
      <el-row class="isConfing" type="flex" justify="start" align="middle">
        <span class="_lable">访客超过</span>
        <el-row>
          <el-input
            :disabled="!RemindCustomerServiceTime.isOpen"
            v-model="RemindCustomerServiceTime.timeLimt"
            :maxlength="3"
            onkeyup="if(this.value<=0) { value = null } this.value=this.value.replace(/[^\d.]/g,'');"
            class="_time"
            placeholder="请输入"
          >
            <svg-icon slot="suffix" icon-class="second" class="second" />
          </el-input>
          <span class="timeTip">秒没有回应坐席消息时，系统自动发送以下消息</span>
        </el-row>
      </el-row>
      <el-row class="isConfing" type="flex" justify="start" align="middle">
        <span class="_lable">自动消息内容</span>
        <el-row>
          <el-input
            :disabled="!RemindCustomerServiceTime.isOpen"
            v-model="RemindCustomerServiceTime.content"
            type="textarea"
            placeholder="请输入内容"
            maxlength="100"
            class="autoInfo"
            show-word-limit
          ></el-input>
        </el-row>
      </el-row>
    </template>
    <!-- 坐席超时结束会话   -->
    <template>
      <el-row class="_title mar_top20" type="flex" justify="start" align="middle">
        <span class="borline"></span>
        <span class="content">坐席超时结束会话</span>
      </el-row>
      <el-row class="isConfing" type="flex" justify="start" align="top">
        <span class="_lable">开启此功能</span>
        <div>
          <el-row>
            <el-radio v-model="kefuTime.isOpen" :label="true">开启</el-radio>
            <el-radio v-model="kefuTime.isOpen" :label="false">关闭</el-radio>
          </el-row>
          <el-row class="tip">当会话超过设置时间且没有新消息产生，系统自动向访客发送一条消息，并结束会话</el-row>
        </div>
      </el-row>
      <el-row class="isConfing" type="flex" justify="start" align="middle">
        <span class="_lable">坐席超过</span>
        <el-row>
          <el-input
            :disabled="!kefuTime.isOpen"
            v-model="kefuTime.timeLimt"
            :maxlength="3"
            onkeyup="if(this.value<=0) { value = null } this.value=this.value.replace(/[^\d.]/g,'');"
            class="_time"
            placeholder="请输入"
          >
            <svg-icon slot="suffix" icon-class="second" class="second" />
          </el-input>
          <span class="timeTip">秒没有回应访客消息时，系统自动向访客发送以下消息</span>
        </el-row>
      </el-row>
      <el-row class="isConfing" type="flex" justify="start" align="middle">
        <span class="_lable">自动消息内容</span>
        <el-row>
          <el-input
            :disabled="!kefuTime.isOpen"
            v-model="kefuTime.content"
            type="textarea"
            placeholder="请输入内容"
            maxlength="100"
            class="autoInfo"
            show-word-limit
          ></el-input>
        </el-row>
      </el-row>
    </template>
    <!-- 排队超时或溢出 -->
    <template>
      <el-row class="_title mar_top20" type="flex" justify="start" align="middle">
        <span class="borline"></span>
        <span class="content">排队超时或溢出</span>
      </el-row>
      <el-row class="isConfing" type="flex" justify="start" align="top">
        <span class="_lable">开启转留言</span>
        <div>
          <el-row>
            <el-radio v-model="lineUpOverTime.isOpen" :label="true">开启</el-radio>
            <el-radio v-model="lineUpOverTime.isOpen" :label="false">关闭</el-radio>
          </el-row>
          <el-row class="tip">当排队人数达到上限值或排队时间超时后，访客自动进入留言</el-row>
        </div>
      </el-row>
      <el-row class="isConfing" type="flex" justify="start" align="middle">
        <span class="_lable"></span>
        <el-row>
          <div>
            <el-input
              :disabled="!lineUpOverTime.isOpen"
              v-model="lineUpOverTime.peopleNum"
              :maxlength="3"
              onkeyup="if(this.value<=0) { value = null } this.value=this.value.replace(/[^\d.]/g,'');"
              class="_time"
              placeholder="请输入"
            >
              <svg-icon slot="suffix" icon-class="people" class="second" />
            </el-input>
            <span class="timeTip">排队人数上限</span>
          </div>
          <div class="mar_top12">
            <el-input
              :disabled="!lineUpOverTime.isOpen"
              v-model="lineUpOverTime.timeLimt"
              :maxlength="3"
              onkeyup="if(this.value<=0) { value = null } this.value=this.value.replace(/[^\d.]/g,'');"
              class="_time"
              placeholder="请输入"
            >
              <svg-icon slot="suffix" icon-class="minute" class="second" />
            </el-input>
            <span class="timeTip">排队超时上限</span>
          </div>
          <div class="limtTip">当达到接待能力上限后，新访客自动进入排队状态，系统提示访客排队状态和排队人数</div>
        </el-row>
      </el-row>
      <el-row class="isConfing" type="flex" justify="start" align="middle">
        <span class="_lable">排队提示语</span>
        <el-row>
          <el-input
            :disabled="!lineUpOverTime.isOpen"
            v-model="lineUpOverTime.content"
            type="textarea"
            placeholder="请输入内容"
            maxlength="200"
            class="autoInfo"
            show-word-limit
          ></el-input>
        </el-row>
      </el-row>
    </template>
    <!-- 对话结束语设置   -->
    <!-- <template>
      <el-row class="_title mar_top20" type="flex" justify="start" align="middle">
        <span class="borline"></span>
        <span class="content">对话结束语设置</span>
      </el-row>
      <el-row class="isConfing" type="flex" justify="start" align="top">
        <span class="_lable">开启此功能</span>
        <div>
          <el-row>
            <el-radio v-model="closeSession.isOpen" :label="true">开启</el-radio>
            <el-radio v-model="closeSession.isOpen" :label="false">关闭</el-radio>
          </el-row>
          <el-row class="tip">当会话超过设置时间且没有新消息产生，系统自动向访客发送一条消息，并结束会话</el-row>
        </div>
      </el-row>
      <el-row class="isConfing" type="flex" justify="start" align="middle">
        <span class="_lable">结束语内容</span>
        <el-row>
          <el-input :disabled="!closeSession.isOpen" v-model="closeSession.content" type="textarea" placeholder="请输入内容" maxlength="100" class="autoInfo" show-word-limit></el-input>
        </el-row>
      </el-row>
    </template>-->
    <el-button class="submBtn mar_top20" type="primary" @click="checkTimer(saveBtn,'timer')()">保存</el-button>
  </div>
</template>

<script>
import { autoMsgAdd, autoMsgs } from '@/api/configuration/config_im';
export default {
  name: 'compAutoNews',
  data() {
    return {
      seatAuto: {
        isOpen: false,
        timeLimt: '',
        content: ''
      },
      guestAuto: {
        isOpen: false,
        timeLimt: '',
        content: ''
      },
      guestOverTime: {
        isOpen: false,
        timeLimt: '',
        content: ''
      },
      lineUpOverTime: {
        isOpen: false,
        peopleNum: '',
        timeLimt: '',
        content: ''
      },
      closeSession: {
        isOpen: false,
        content: ''
      },
      kefuTime: {
        isOpen: false,
        timeLimt: '',
        content: ''
      },
      RemindCustomerServiceTime: {
        isOpen: false,
        timeLimt: '',
        content: ''
      },
      timer: null,
      isCheck: true
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      autoMsgs().then(res => {
        if (res.code === 1) {
          const {
            customNomsgOpen,
            customNomsgOuttime,
            customNomsgtip,
            customTimeout,
            customTimeoutOpen,
            customTimeoutTip,
            dialogFinishOpen,
            dialogFinishTip,
            kefuNomsgOpen,
            kefuNomsgOuttime,
            kefuNomsgTip,
            queuingLimit,
            queuingTimeout,
            queuingTimeoutOpen,
            queuingTimeoutTip,
            kefuNoMsgTimeOutOpen,
            kefuNoMsgTimeOut,
            kefuNoMsgTimeOutTip,
            customTimeoutRemindKefuOpen,
            customTimeoutRemindKefuTime,
            customTimeoutRemindKefuTip
          } = res.data;
          customNomsgOpen === '1'
            ? (this.guestAuto.isOpen = true)
            : (this.guestAuto.isOpen = false);
          this.guestAuto.timeLimt = customNomsgOuttime;
          this.guestAuto.content = customNomsgtip;
          this.guestOverTime.timeLimt = customTimeout;
          customTimeoutOpen === '1'
            ? (this.guestOverTime.isOpen = true)
            : (this.guestOverTime.isOpen = false);
          this.guestOverTime.content = customTimeoutTip;
          dialogFinishOpen === '1'
            ? (this.closeSession.isOpen = true)
            : (this.closeSession.isOpen = false);
          this.closeSession.content = dialogFinishTip;
          kefuNomsgOpen === '1'
            ? (this.seatAuto.isOpen = true)
            : (this.seatAuto.isOpen = false);
          this.seatAuto.timeLimt = kefuNomsgOuttime;
          this.seatAuto.content = kefuNomsgTip;
          this.lineUpOverTime.peopleNum = queuingLimit;
          this.lineUpOverTime.timeLimt = queuingTimeout;
          queuingTimeoutOpen === '1'
            ? (this.lineUpOverTime.isOpen = true)
            : (this.lineUpOverTime.isOpen = false);
          this.lineUpOverTime.content = queuingTimeoutTip;
          kefuNoMsgTimeOutOpen === '1'
            ? (this.kefuTime.isOpen = true)
            : (this.kefuTime.isOpen = false);
          this.kefuTime.timeLimt = kefuNoMsgTimeOut;
          this.kefuTime.content = kefuNoMsgTimeOutTip;
          this.RemindCustomerServiceTime.timeLimt = customTimeoutRemindKefuTime;
          customTimeoutRemindKefuOpen === '1'
            ? (this.RemindCustomerServiceTime.isOpen = true)
            : (this.RemindCustomerServiceTime.isOpen = false);
          this.RemindCustomerServiceTime.content = customTimeoutRemindKefuTip;
        }
      });
    },
    saveBtn() {
      if (this.seatAuto.isOpen) {
        // 坐席无应答是否开启
        if (this.seatAuto.timeLimt == '' || this.seatAuto.timeLimt == 0) {
          this.$XyyMessage.error('请填写坐席超时时间！');
          return;
        }
        if (this.seatAuto.content == '') {
          this.$XyyMessage.error('请填写坐席消息内容！');
          return;
        }
      }
      //	访客无应答是否开启
      if (this.guestAuto.isOpen) {
        if (this.guestAuto.timeLimt == '' || this.guestAuto.timeLimt == 0) {
          this.$XyyMessage.error('请填写访客无应答超时时间！');
          return;
        }
        if (this.guestAuto.content == '') {
          this.$XyyMessage.error('请填写访客无应答消息内容！');
          return;
        }
      }
      //	访客无应答是否开启
      if (this.guestOverTime.isOpen) {
        if (
          this.guestOverTime.timeLimt == '' ||
          this.guestOverTime.timeLimt == 0
        ) {
          this.$XyyMessage.error('请填写访客超时结束会话时间！');
          return;
        }
        if (this.guestOverTime.content == '') {
          this.$XyyMessage.error('请填写访客超时结束会话消息内容！');
          return;
        }
      }
      //	访客即将超时提醒坐席
      if (this.RemindCustomerServiceTime.isOpen) {
        if (
          this.RemindCustomerServiceTime.timeLimt == '' ||
          this.RemindCustomerServiceTime.timeLimt == 0
        ) {
          this.$XyyMessage.error('请填写访客即将超时提醒坐席时间！');
          return;
        }
        if (this.RemindCustomerServiceTime.content == '') {
          this.$XyyMessage.error('请填写访客即将超时提醒坐席消息内容！');
          return;
        }
      }
      //	访客无应答是否开启
      if (this.lineUpOverTime.isOpen) {
        if (
          this.lineUpOverTime.peopleNum == '' ||
          this.lineUpOverTime.peopleNum == 0
        ) {
          this.$XyyMessage.error('请填写排队溢出人数上限！');
          return;
        }
        if (
          this.lineUpOverTime.timeLimt == '' ||
          this.lineUpOverTime.timeLimt == 0
        ) {
          this.$XyyMessage.error('请填写排队超时时间！');
          return;
        }
        if (this.lineUpOverTime.content == '') {
          this.$XyyMessage.error('请填写排队超时提示语！');
          return;
        }
      }
      //坐席超时结束会话开启
      if (this.kefuTime.isOpen) {
        if (this.kefuTime.timeLimt == '' || this.kefuTime.timeLimt == 0) {
          this.$XyyMessage.error('请填写坐席超时结束会话时间！');
          return;
        }
        if (this.kefuTime.content == '') {
          this.$XyyMessage.error('请填写坐席超时结束会话消息内容！');
          return;
        }
      }
      //	对话结束语是否开启
      // if (this.closeSession.isOpen) {
      //   if (this.closeSession.content == '') {
      //     this.$XyyMessage.error('请填写对话结束语内容！');
      //     return;
      //   }
      // }
      const param = {
        customNomsgOpen: this.guestAuto.isOpen ? '1' : '0', //	访客无应答是否开启	string	@mock=1
        customNomsgOuttime: parseInt(this.guestAuto.timeLimt), //	访客无应答超时时间	number
        customNomsgtip: this.guestAuto.content, //	访客无应答自动回复消息	string
        customTimeout: parseInt(this.guestOverTime.timeLimt), // 访客结束会话超时时间	number
        customTimeoutOpen: this.guestOverTime.isOpen ? '1' : '0', // 访客超时会话结束是否开启	string
        customTimeoutTip: this.guestOverTime.content, // 访客超时结束自动回复内容	string
        dialogFinishOpen: this.closeSession.isOpen ? '1' : '0', // 对话结束语是否开启	number
        dialogFinishTip: this.closeSession.content, // 对话结束语自动回复内容	string
        kefuNomsgOpen: this.seatAuto.isOpen ? '1' : '0', // 坐席无应答是否开启	string
        kefuNomsgOuttime: parseInt(this.seatAuto.timeLimt), // 坐席无应答超时时间	number
        kefuNomsgTip: this.seatAuto.content, //	坐席无应答提示内容	string
        queuingLimit: parseInt(this.lineUpOverTime.peopleNum), // 排队人数上限	number
        queuingTimeout: parseInt(this.lineUpOverTime.timeLimt), // 排队超时结束会话时间	number
        queuingTimeoutOpen: this.lineUpOverTime.isOpen ? '1' : '0', //	排队超时结束会话是否开启	string
        queuingTimeoutTip: this.lineUpOverTime.content, //	排队超时结束会话自动提示内容	string
        kefuNoMsgTimeOutOpen: this.kefuTime.isOpen ? '1' : '0', // 坐席超时是否开启	string
        kefuNoMsgTimeOut: parseInt(this.kefuTime.timeLimt), // 坐席超时超时时间	number
        kefuNoMsgTimeOutTip: this.kefuTime.content, //	坐席超时提示内容	string
        customTimeoutRemindKefuOpen: this.RemindCustomerServiceTime.isOpen
          ? '1'
          : '0', // 访客会话即将超时，提醒坐席是否开启
        customTimeoutRemindKefuTime: parseInt(
          this.RemindCustomerServiceTime.timeLimt
        ), // 访客会话即将超时，时间设置
        customTimeoutRemindKefuTip: this.RemindCustomerServiceTime.content // 访客会话即将超时，提醒坐席消息内容
      };
      if (!this.isCheck) {
        return;
      }
      this.isCheck = false;
      setTimeout(() => {
        this.isCheck = true; //点击一次时隔两秒后才能再次点击
      }, 2000);
      autoMsgAdd(param).then(res => {
        if (res.code === 1) {
          this.$XyyMessage.success('设置成功！');
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.mar_top20 {
  margin-top: 20px;
}
.mar_top12 {
  margin-top: 12px;
}
.newsConfig {
  padding-top: 20px;
  padding-left: 173px;
  padding-bottom: 50px;
  ._title {
    .borline {
      width: 2px;
      height: 13px;
      background: rgba(59, 149, 168, 1);
      margin-right: 6px;
      margin-top: -1px;
    }
    .content {
      font-size: 16px;
      font-weight: bold;
      color: rgba(41, 41, 51, 1);
    }
  }
  .isConfing {
    padding-top: 19px;
    ._lable {
      display: inline-block;
      font-size: 14px;
      font-weight: none;
      color: rgba(41, 41, 51, 1);
      width: 86px;
      text-align: right;
      padding-right: 12px;
      box-sizing: content-box;
    }
    .tip {
      padding-top: 7px;
      font-size: 12px;
      color: rgba(144, 147, 153, 1);
    }
    ._time {
      width: 74px;
      height: 36px;
    }
    .timeTip {
      font-size: 14px;
      padding-right: 12px;
      color: rgba(41, 41, 51, 1);
    }
    .limtTip {
      padding-top: 10px;
      font-size: 12px;
      color: rgba(144, 147, 153, 1);
    }
    .autoInfo {
      /deep/.el-textarea__inner {
        width: 724px;
        height: 80px;
      }
    }
    .second {
      margin-top: 13px;
      margin-right: 5px;
    }
  }
  .submBtn {
    margin-left: 100px;
  }
}
</style>
