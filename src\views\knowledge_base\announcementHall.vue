<template >
  <xyy-list-page class="announcement-hall">
    <template slot="header">
      <div>
        <el-form ref="queryList" :model="queryList" class="announcement-hall-box">
          <el-row class="innerEl" type="flex" justify="start" align="middle">
            <el-form-item class="serverNum" label="公告查询" label-width="80px" prop="serveNum">
              <el-input
                v-model="queryList.title"
                class="serverName"
                size="small"
                clearable
                placeholder="请输入标题或内容"
              />
            </el-form-item>

            <el-form-item class="serverNum" label="发布时间" label-width="80px" prop="dateRange">
              <el-date-picker
                :picker-options="pickerOptions"
                v-model="queryList.dateRange"
                type="daterange"
                range-separator="-"
                size="small"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                text="erdf"
                prefix-icon="el-icon-date"
                class="timeSel"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                plain
                type="primary"
                size="medium"
                class="searchCondition"
                @click="checkTimer(handerSearch('queryList'))"
              >查询</el-button>
              <el-button plain size="medium" @click="resetForm">重置</el-button>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
    </template>

    <template slot="body">
      <div>
        <el-row type="flex" justify="space-between" align="top">
          <xyy-table
            style="width: 100%"
            :data="list"
            :list-query="listQuery"
            :col="col"
            :offset-top="240"
            @get-data="getList"
            @row-dblclick="handleRowdblClick"
            class="customer-table"
          ></xyy-table>
          <!-- <el-table :data="list" :show-header="true" class="customer-table" stripe>
            <el-table-column prop="title" label="标题"></el-table-column>
            <el-table-column
              prop="modifyName"
              label="阅读量、发布人、发布时间"
              :width="280"
              :show-overflow-tooltip="true"
            ></el-table-column>
          </el-table>-->
        </el-row>
      </div>
      <!-- <div>
        <el-row type="flex" justify="space-between" align="top">
          <div v-if="isPagination" class="pagination-container">
            <el-pagination
              :pager-count="7"
              :background="true"
              isPagination
              &#x26;&#x26;
              listQuery.total
              @size-change="updateSize"
              layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[10, 20, 30, 40, 50, 100]"
              :small="false"
              :current-page.sync="listQuery.page"
              :page-size.sync="listQuery.pageSize"
              :total="listQuery.total"
              prev-text="上一页"
              next-text="下一页"
              @current-change="getList"
            />
          </div>
        </el-row>
      </div>-->
    </template>
  </xyy-list-page>
</template>

<script>
import { findKnowledgeNoticeList } from '@/api/knowledge_base';
export default {
  name: 'announcementHall',
  data() {
    return {
      created: false,
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      queryList: {
        dateRange: [],
        title: ''
      },
      list: [],
      datas: {},
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.minDate = minDate;
          this.maxDate = maxDate;
        },
        disabledDate: time => {
          const curDate = new Date().getTime();
          const two = 365 * 2 * 24 * 3600 * 1000;
          const twoyear = curDate - two;
          const three = 92 * 24 * 3600 * 1000;
          if (this.minDate) {
            return (
              time.getTime() > Date.now() ||
              time.getTime() < twoyear ||
              time > new Date(this.minDate.getTime() + three) ||
              time < new Date(this.minDate.getTime() - three)
            );
          }
          return time.getTime() > Date.now() || time.getTime() < twoyear;
        }
      },
      col: [
        { index: 'title', name: '公告标题' },
        { index: 'viewNum', name: '阅读量', width: 120 },
        { index: 'modifyName', name: '发布人', width: 180 },
        { index: 'modifyTime', name: '更新时间', width: 200 }
      ],
      isPagination: true
    };
  },
  created() {
    this.getNowTimeDate();
    this.created = true;
  },
  activated() {
    if (!this.created) this.getList(this.listQuery);
    this.created = false;
  },
  mounted() {},
  methods: {
    resetForm() {
      (this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      }),
        (this.queryList = {
          dateRange: [], // 日期
          title: ''
        });
      this.getNowTimeDate();
      this.getList(this.listQuery);
    },
    handerSearch(formName) {
      (this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      }),
        this.getList(this.listQuery);
    },
    getList: function(listQuery) {
      const { page, pageSize } = listQuery;
      let params = {
        pageNum: page,
        pageSize,
        title: this.queryList.title
      };

      if (this.queryList.dateRange) {
        params.endTime = this.queryList.dateRange[1];
        params.startTime = this.queryList.dateRange[0];
      }

      findKnowledgeNoticeList(params)
        .then(res => {
          const { total } = res.data;
          this.list = res.data.list;

          this.listQuery = {
            ...this.listQuery,
            page: Number(res.data.pageNum),
            pageSize: Number(res.data.pageSize),
            total: Number(res.data.total)
          };
        })
        .catch(() => {});
    },
    /**
     * 跳转到详情
     */
    handleRowdblClick(row, column, cell, event) {
      this.$router.push({
        path: `/knowledge_base/announcementDetails/${row.id}`,
        query: { templateId: row.id }
      });
    },
    // 当前时间
    getNowTimeDate() {
      const time = new Date(8 * 3600 * 1000 + +new Date())
        .toJSON()
        .substr(0, 10);

      const cc = new Date().getTime();
      var halfYear = (365 / 2) * 24 * 3600 * 1000;
      var pastResult = cc - halfYear;
      var pastDate = new Date(pastResult),
        pastYear = pastDate.getFullYear(),
        pastMonth =
          pastDate.getMonth() + 1 < 10
            ? '0' + (pastDate.getMonth() + 1)
            : pastDate.getMonth() + 1,
        pastDay =
          pastDate.getDate() < 10
            ? '0' + pastDate.getDate()
            : pastDate.getDate();

      const oldTime = pastYear + '-' + pastMonth + '-' + pastDay;
      this.queryList.dateRange = [oldTime, time];
    }
  }
};
</script>

<style lang="scss">
.announcement-hall {
  /deep/.page-body {
    width: 100%;
  }
  /deep/.table-containter {
    width: 100%;
    min-height: 400px;
  }
  /deep/.el-table__empty-block {
    width: 100 !important;
  }
  // 去掉表格单元格边框
  .customer-table th {
    border: none;
    // text-align: right;
  }
  .customer-table td,
  .customer-table th.is-leaf {
    border: none;
    // text-align: right;
  }
  // 表格最外边框
  // .el-table--border,
  // .el-table--group {
  //   border: none;
  // }
  // 头部边框
  .customer-table thead tr th.is-leaf {
    border: 1px solid #ebeef5;
    border-right: none;
  }
  .customer-table thead tr th:nth-last-of-type(2) {
    border-right: 1px solid #ebeef5;
  }
  // 表格最外层边框-底部边框
  .el-table--border::after,
  .el-table--group::after {
    width: 0;
  }
  .customer-table::before {
    width: 0;
  }
  .customer-table .el-table__fixed-right::before,
  .el-table__fixed::before {
    width: 0;
  }
  // 表格有滚动时表格头边框
  .el-table--border th.gutter:last-of-type {
    border: 1px solid #ebeef5;
    border-left: none;
  }
  .im_container_leftview_table_row {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100px;
  }

  .im_container_leftview_table_row_1 {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 60px;
  }
  /* 分页 */
  .pagination-container {
    margin-top: 20px;
    text-align: right;
    .el-pagination {
      position: relative;
      padding: 0;
      .el-pagination__total {
        position: absolute;
        top: 2px;
        left: 5px;
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        color: #575766;
      }
      @mixin reset($h: 32px, $fs: 14px) {
        height: $h;
        line-height: $h;
        font-size: $fs;
        font-family: Helvetica;
        font-weight: 400;
      }
      span,
      button {
        @include reset;
      }
      .el-pager {
        li {
          @include reset;
        }
      }
      .el-input--mini .el-input__inner {
        @include reset;
      }
      .el-pagination__editor.el-input .el-input__inner {
        @include reset;
      }
      .btn-prev,
      .btn-next,
      .el-pager li {
        background: #fff;
        width: 32px;
        height: 32px;
        border-radius: 2px;
        border: 1px solid #e4e4eb;
      }
      .el-pagination__editor {
        margin: 0 9px;
      }
    }
  }
  .announcement-hall-box {
    padding-bottom: 20px;
    margin-bottom: 10px;
    border-bottom: 1px dotted #e4e4eb;
    .innerEl {
      height: 38px;
      /deep/.el-input__inner {
        width: 100%;
        height: 36px;
        line-height: 36px;
      }
      /deep/.el-form-item {
        margin: 0;
      }
      .timeSel {
        width: 300px;
      }
      /deep/input[type='number']::-webkit-inner-spin-button,
      /deep/input[type='number']::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      .btnNew {
        color: #fff;
        background-color: rgba(59, 149, 168, 1);
      }
      .serverName {
        width: 200px;
      }
      .serverNum {
        margin-right: 20px;
      }
      .searchCondition.is-plain {
        background: rgba(59, 149, 168, 1);
        color: #fff;
      }
      &.top_20 {
        margin-top: 20px;
      }
    }
  }
}
</style>
