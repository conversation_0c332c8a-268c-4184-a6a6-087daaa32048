<template>
  <div class="preview-box">
    <div class="file-preview">
      <img :src="file.smallurl" />
      <div class="cover">
        <div class="tools">
          <!-- <span v-show="percent<100">
            <el-progress :percentage="percent"></el-progress>
            <i class="percent-size">{{ percentSize }}</i>
            <el-button type="text" class="file-abort" @click="$emit('abortCallback')">取消</el-button>
          </span>-->
          <span class="operate-tools">
            <i class="el-icon-search" title="预览" @click="preview"></i>
            <i v-if="!editable" class="el-icon-download" title="下载" @click="download"></i>
            <i v-if="editable" class="el-icon-delete" title="删除" @click="$emit('delCallback')"></i>
          </span>
        </div>
      </div>
    </div>
    <div :title="file.name" class="file-name">{{ file.name }}</div>
    <el-dialog
      ref="previewDialog"
      :visible.sync="isOpen"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="800px"
      top="0"
      custom-class="preview-dialog"
      @close="close"
    >
      <div slot="title" class="preview-title">
        {{ file.name }}
        <span class="preview-tools">
          <i class="el-icon-zoom-in" title="放大" @click="zoomIn" />
          <i class="el-icon-zoom-out" title="缩小" @click="zoomOut" />
          <svg-icon icon-class="rotate" title="旋转" @click="rotate" />
        </span>
      </div>
      <img :style="imgStyle" :src="filePath" />
    </el-dialog>
  </div>
</template>

<script>
import { FILE_PATH } from '@/api/fields';
export default {
  props: {
    file: {
      type: Object,
      default: () => {
        return {
          uid: '', // 唯一标识
          name: '', // 文件名
          url: '', // 文件缩略图地址 或 预览地址
          path: '', // 文件真实地址
          raw: {
            type: '', // 文件mime-type
            size: 0 // 文件大小
          },
          data: '' // 后台所需数据
        };
      }
    },
    // 上传进度
    percent: {
      type: Number,
      default: 0
    },
    // 编辑状态
    editable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      basePath: FILE_PATH,
      isOpen: false, // 预览状态
      imgTop: 0, // 图片距离顶部位置
      imgSelfTop: 0, // 图片自身Y轴偏移
      imgRotate: 0, // 图片翻转角度
      imgScale: 1, // 图片放大倍数
      imgWidth: 0, // 图片显示宽度
      imgHeight: 0, // 图片显示高度
      // pdfPath: 'https://view.officeapps.live.com/op/view.aspx?src=',
      ignore: ['zip', 'word', 'excel', 'ppt', 'avi', 'txt']
    };
  },
  computed: {
    imgStyle() {
      return {
        width: this.imgWidth + 'px',
        height: this.imgHeight + 'px',
        top: this.imgTop,
        transform: `translateY(${this.imgSelfTop}) scale(${this.imgScale}) rotate(${this.imgRotate}deg)`
      };
    },
    percentSize() {
      return (
        ((this.file.raw.size * this.percent) / 102400000).toFixed(2) + 'MB'
      );
    },
    previewSize() {
      return this.file.raw.size < 1000
        ? this.file.raw.size + 'B'
        : this.file.raw.size < 1024000
        ? (this.file.raw.size / 1000).toFixed(2) + 'KB'
        : (this.file.raw.size / 1024000).toFixed(2) + 'MB';
    },
    filePath() {
      return this.file.bigurl ? this.file.bigurl : this.file.smallurl;
    }
  },
  methods: {
    /**
     * 文件下载
     */
    download() {
      const url = `${
        process.env.BASE_API
      }/fileUpload/downloadFile?originalFilename=${this.file.name}&path=${
        this.file.data
          ? this.file.data.path
          : this.file.response.data.successFiles[0].path
      }&group=${
        this.file.data
          ? this.file.data.group
          : this.file.response.data.successFiles[0].group
      }`;
      const a = document.createElement('a');
      a.href = url;
      a.click();
    },
    /**
     * 文件预览
     */
    preview() {
      this.isOpen = true;
      this.initImage();
    },
    /**
     * 初始化图片宽高
     */
    initImage() {
      const boxWidth = 800;
      const boxHeight = 440;
      this.imgRotate = 0;
      this.imgScale = 1;
      const img = new Image();
      img.src = this.filePath;
      img.onload = () => {
        const _width = img.width;
        const _height = img.height;
        if (_width > boxWidth) {
          let h = ((_height * boxWidth) / _width).toFixed(0);
          let w = boxWidth;
          if (h > boxHeight) {
            w = ((w * boxHeight) / h).toFixed(0);
            h = boxHeight;
          }
          this.imgWidth = w;
          this.imgHeight = h;
        } else {
          if (_height > boxHeight) {
            this.imgWidth = ((_width * boxHeight) / _height).toFixed(0);
            this.imgHeight = boxHeight;
          } else {
            this.imgWidth = _width;
            this.imgHeight = _height;
          }
        }

        if (this.imgHeight < boxHeight) {
          this.imgTop = '50%';
          this.imgSelfTop = '-50%';
        } else {
          this.imgTop = 0;
          this.imgSelfTop = 0;
        }
      };
    },
    /**
     * 图片旋转
     */
    rotate() {
      this.imgRotate = this.imgRotate === 360 ? 90 : this.imgRotate + 90;
    },
    /**
     * 图片放大
     */
    zoomIn() {
      this.imgScale = (Number(this.imgScale) + 0.1).toFixed(1);
    },
    /**
     * 图片缩小
     */
    zoomOut() {
      if (this.imgScale > 0.1) {
        this.imgScale = (this.imgScale - 0.1).toFixed(1);
      } else {
        this.$XyyMessage.warning(`不能再小了`);
      }
    },
    /**
     * 关闭回调
     */
    close() {
      if (this.$refs['myVideo']) {
        this.$refs['myVideo'].load();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.preview-box {
  line-height: 0;
  float: left;
  width: 80px;
  margin: 0 10px 10px;
  .file-preview {
    width: 80px;
    height: 80px;
    overflow: hidden;
    position: relative;
    margin: 0;
    left: 50%;
    transform: translateX(-50%);
    img,
    i.file-icon {
      width: 100%;
      position: relative;
      top: 50%;
      transform: translateY(-50%);
      float: left;
    }

    i.file-icon {
      display: inline-block;
      height: 100%;
      box-sizing: border-box;
    }

    // i.pdf {
    //   background: url(../../assets/fields/pdf.png) 0 0 no-repeat;
    // }

    // i.video,
    // i.avi {
    //   background: url(../../assets/fields/video.png) 0 0 no-repeat;
    // }

    // i.zip {
    //   background: url(../../assets/fields/zip.png) 0 0 no-repeat;
    // }

    // i.word {
    //   background: url(../../assets/fields/word.png) 0 0 no-repeat;
    // }

    // i.ppt {
    //   background: url(../../assets/fields/ppt.png) 0 0 no-repeat;
    // }

    // i.excel {
    //   background: url(../../assets/fields/excel.png) 0 0 no-repeat;
    // }

    // i.txt {
    //   background: url(../../assets/fields/txt.png) 0 0 no-repeat;
    // }

    .cover {
      display: none;
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: 1;
      .el-progress {
        width: 100%;
        /deep/.el-progress-bar__outer {
          background-color: #909399;
          .el-progress-bar__inner {
            background-color: #fff;
          }
        }
        /deep/.el-progress__text {
          color: #fff;
          transform: translateY(50%);
          font-size: 8px !important;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
        }
      }
      i.percent-size {
        display: inline-block;
        height: 12.3px;
        font-size: 8px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        font-style: normal;
        position: absolute;
        top: 50%;
        transform: translateY(-100%);
      }
      .el-button.file-abort {
        display: none;
        font-size: 10px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(77, 223, 254, 1);
        position: absolute;
        bottom: 7px;
        left: 50%;
        transform: translateX(-50%);
        padding: 0;
      }
      .operate-tools {
        width: 100%;
        text-align: center;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        i {
          color: #fff;
          font-size: 20px;
          cursor: pointer;
          margin: 0 5px;
        }
      }
      &.uploading {
        display: block;
      }
    }

    &:hover {
      .cover {
        display: block;
        .el-button.file-abort {
          display: block;
        }
      }
    }
  }
  .file-name {
    width: 100%;
    display: inline-block;
    text-align: center;
    height: 17px;
    line-height: 17px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(87, 87, 102, 1);
    margin: 4px 0 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.preview-box:first-child {
  margin-left: 0;
}

.preview-box:last-child::after {
  content: '';
  clear: both;
}
</style>

<style lang="scss">
.el-dialog.preview-dialog {
  height: 500px;
  top: 50%;
  transform: translateY(-50%);
  overflow: hidden;
  /deep/.el-dialog__header {
    height: 60px;
    padding: 15px 16px 20px 20px;
    border-bottom: 1px solid #dcdfe6;
    .preview-title {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(41, 41, 51, 1);
      line-height: 25px;
      height: 25px;
      span.preview-size {
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(96, 98, 102, 1);
        margin-left: 14px;
      }
      span.preview-tools {
        float: right;
        margin-right: 40px;
        border-right: 1px solid #dcdfe6;
        user-select: none;
        padding-right: 10px;
        i {
          color: #c0c4cc;
          margin: 0 10px;
          cursor: pointer;
        }
        .svg-icon {
          margin: 0 10px;
          cursor: pointer;
        }
      }
    }
  }
  /deep/ .el-dialog__body {
    height: calc(100% - 57px);
    overflow: auto;
    padding: 0;
    text-align: center;
    position: relative;
    img {
      position: relative;
    }
    video {
      width: 100%;
      height: calc(100% - 5px);
    }
    object {
      width: 100%;
      height: calc(100% - 5px);
    }
  }
}
</style>

