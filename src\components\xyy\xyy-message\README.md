# xyy-message

| 参数          | 说明                               | 类型    | 可选值 | 默认值 |
| ------------- | -----------------------------------| ------- | ------ | ------ |
| message       | 弹框显示的数据文本                 | Array    | ---    | '提示'    |
| htmlString    | message为html时 该值需为true       | Boolean  | ---    | false    |
| type          | 弹框的类型                         | String   | 'success', 'warning', 'info', 'error'    | 'info' |
| showClose     | 是否可以手动关闭按钮               | Boolean   | ---    | false    |
| duration      | 弹窗的显示时间 <=0时默认不自动关闭弹窗 | Number | ---    | 3000   |
| offset-top    | 表格距视口高度,用于动态计算表格高度 | Number  | ---    | 189    |
> message 文字最多显示32个 超过显示...
#### 使用
```
this.$XyyMessage('这是info提示')
this.$XyyMessage.error('这是error提示')
this.$XyyMessage.success('这是success提示')
this.$XyyMessage.warning('这是warning提示')
this.$XyyMessage.error({
  message: '这是error手动关闭提示',
  showClose: true
})
```