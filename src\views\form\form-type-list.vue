<template>
  <xyy-list-page>
    <template slot="header">
      <xyy-button icon-class="btn-add"
                  class-name="btn-add-icon"
                  @click="openDialog('add')">新建表单类型</xyy-button>
    </template>
    <template slot="body">
      <xyy-table :data="list"
                 :list-query="listQuery"
                 :col="col"
                 :operation="operation"
                 @get-data="getList"
                 @operation-click="operationClick">
        <template slot="gmtCreate"
                  slot-scope="{col}">
          <el-table-column :key="col.index"
                           :prop="col.index"
                           :label="col.name"
                           :width="col.width"
                           :formatter="getFormatDate"
                           show-overflow-tooltip />
        </template>
        <template slot="status"
                  slot-scope="{col}">
          <el-table-column :key="col.index"
                           :label="col.name"
                           :width="col.width">
            <template slot-scope="{row}">
              <span :class="'status-tip '+(row.status === 1?'open':'close')"></span>
              {{ row.status === 1?'启用':'禁用' }}
            </template>
          </el-table-column>
        </template>
      </xyy-table>
      <!-- 表单弹窗 start -->
      <el-dialog :visible.sync="dialogFormVisible"
                 :close-on-click-modal="false"
                 :title="dialogTitle"
                 width="850px">
        <div style="padding: 0 150px">
          <el-form ref="form"
                   :model="form"
                   :rules="rules">
            <el-form-item :label-width="formLabelWidth"
                          label="类型名称"
                          prop="name">
              <el-input v-model.trim="form.name"
                        maxlength="20"
                        placeholder="请输入" />
            </el-form-item>
            <el-form-item :label-width="formLabelWidth"
                          label="工单编号前缀"
                          prop="codePrefix">
              <el-input v-model.trim="form.codePrefix"
                        :disabled="isEdit"
                        maxlength="4"
                        placeholder="4个英文字母以内，注：新建后工单编号前缀不能二次修改" />
            </el-form-item>
            <el-form-item :label-width="formLabelWidth"
                          label="备注说明"
                          prop="description">
              <el-input v-model="form.description"
                        type="textarea"
                        maxlength="200"
                        rows="5"
                        placeholder="请输入" />
            </el-form-item>
          </el-form>
        </div>

        <div slot="footer"
             class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary"
                     @click="checkTimer(addFormType,'timer')()">确 定</el-button>
        </div>
      </el-dialog>
      <!--  表单弹窗 end -->
    </template>
  </xyy-list-page>
</template>

<script>
import {
  getFormTypeList,
  addFormType,
  delFormType,
  copyFormType,
  updateFormType,
  changeTypeStatus
} from '@/api/formManage';
/* 查询关联 */
// import { listTemplate } from '../../api/fields/fields-comment';

export default {
  name: 'FormTypeList',
  data () {
    return {
      timer: null,
      created: false,
      list: [],
      dialogTitle: '新建表单类型',
      dialogFormVisible: false,
      isEdit: false, // 编辑状态
      form: {
        name: '',
        codePrefix: '',
        description: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入类型名称', trigger: 'blur' },
          { max: 20, message: ' 20字以内', trigger: 'blur' }
        ],
        codePrefix: [
          { required: true, message: '请输入工单编号前缀', trigger: 'blur' },
          { max: 4, message: ' 4个英文字符以内', trigger: 'blur' },
          { validator: this.validatePrefix, trigger: 'blur' }
        ],
        description: [{ max: 200, message: ' 200字以内', trigger: 'blur' }]
      },
      formLabelWidth: '120px',
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      col: [
        {
          index: 'name',
          name: '表单名称',
          width: 300,
          ellipsis: true,
          resizable: true
        },
        { index: 'description', name: '说明', ellipsis: true, resizable: true },
        {
          index: 'gmtCreate',
          name: '创建时间',
          ellipsis: true,
          width: 180,
          slot: true,
          resizable: true
        },
        { index: 'editorName', name: '编辑人', width: 130, resizable: true },
        {
          index: 'status',
          name: '状态',
          width: 90,
          slot: true,
          resizable: true
        },
        {
          index: 'operation',
          name: '操作',
          width: 220,
          operation: true,
          resizable: true
        }
      ],
      operation: [
        {
          name: '启用',
          type: 0,
          format: function (row) {
            return ['启用', '禁用'][row.status];
          }
        },
        {
          name: '编辑',
          type: 1
        },
        // {
        //   name: '复制',
        //   type: 2
        // },
        {
          name: '删除',
          type: 2,
          disabled: function (row) {
            return !!row.status;
          }
        }
      ]
    };
  },
  created () {
    this.created = true;
  },
  activated () {
    if (!this.created) this.getList(this.listQuery);
    this.created = false;
  },
  methods: {
    validatePrefix (rule, value, callback) {
      const reg = /^[a-zA-Z]{1,4}$/;
      if (!reg.test(value)) {
        return callback(new Error('请输入4个以内的英文字母'));
      } else {
        this.form.codePrefix = value.toUpperCase();
        callback();
      }
    },
    // 获取表格数据
    getList: function (listQuery) {
      // pageNum, pageSize
      const { page, pageSize } = listQuery;
      getFormTypeList({ pageNum: page, pageSize }).then(res => {
        if (res.code === 1) {
          const { list, total } = res.data;
          this.list = list;
          this.listQuery = {
            ...this.listQuery,
            total
          };
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 操作回调
    operationClick: function (type, row) {
      const { id, status } = row;
      const options = [
        row => {
          this.setStates(id, status);
        },
        row => {
          this.openDialog('edit', row);
        },
        // row => {
        //   this.copyForm(id);
        // },
        row => {
          this.delForm(id);
        }
      ];
      options[type](row);
    },
    // 启用、禁用
    setStates (id, status) {
      status = status === 0 ? 1 : 0;
      const msgs = ['禁用', '启用'];
      this.$XyyMsg({
        title: '提示',
        content: '确定' + msgs[status] + '此表单类型吗？', // html代码串
        onSuccess: () => {
          changeTypeStatus({ id, status }).then(res => {
            if (res.code === 1) {
              this.$store.dispatch('workType/getWorkType');
              this.$XyyMessage.success('表单类型已' + msgs[status]);
              this.getList(this.listQuery);
            } else {
              this.$XyyMessage.error(res.msg);
            }
          });
        }
      });
    },
    // 删除
    delForm (id) {
      this.$XyyMsg({
        title: '提示',
        content: '确定删除此表单类型吗？',
        onSuccess: () => {
          delFormType(id).then(res => {
            if (res.code === 1) {
              this.$store.dispatch('workType/getWorkType');
              this.$XyyMessage.success('删除成功');
              this.getList(this.listQuery);
            } else if (res.code === 9) {
              // this.$XyyMessage.error(res.msg);
              // 查询字段是否被关联
              let html = '当前正有表单应用使用此表单类型，请先修改表单应用( ';
              html += `<span style="color: red"> ${res.msg}</span>`;
              html += ' )';
              this.$XyyMsg({
                title: '提示',
                content: html,
                closeBtn: false,
                onSuccess: function () { }
              });
            } else {
              this.$XyyMsg({
                title: '提示',
                closeBtn: false,
                content: res.msg, // html代码串
                onSuccess: () => { }
              });
            }
          });
        }
      });
    },
    // 新增
    addFormType () {
      this.$refs.form.validate(valid => {
        if (valid) {
          const { id, name, codePrefix, description } = this.form;
          if (this.dialogTitle.startsWith('新建')) {
            addFormType({ name, codePrefix, description }).then(res => {
              if (res.code === 1) {
                this.$XyyMessage.success('新建成功');
                this.getList(this.listQuery);
              } else {
                this.$XyyMessage.error(res.msg);
              }
            });
          } else {
            updateFormType({ id, name, description, sure: false }).then(res => {
              if (res.code === 1) {
                this.$store.dispatch('workType/getWorkType');
                this.$XyyMessage.success('编辑成功');
                this.getList(this.listQuery);
              } else if (res.code === 2) {
                this.$XyyMsg({
                  title: '提示',
                  content: res.msg,
                  okValue: '确定',
                  onSuccess: () => {
                    updateFormType({ id, name, description, sure: true }).then(
                      res => {
                        if (res.code === 1) {
                          this.$store.dispatch('workType/getWorkType');
                          this.$XyyMessage.success('编辑成功');
                          this.getList(this.listQuery);
                        } else {
                          this.$XyyMessage.error(res.msg);
                        }
                      }
                    );
                  }
                });
              } else {
                this.$XyyMessage.error(res.msg);
              }
            });
          }
          this.dialogFormVisible = false;
        } else {
          return false;
        }
      });
    },
    // 复制
    copyForm (id) {
      copyFormType(id).then(res => {
        if (res.code === 1) {
          this.$XyyMessage.success('复制成功');
          this.getList(this.listQuery);
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 新增 && 编辑
    openDialog (flag, info) {
      if (flag === 'add') {
        this.dialogTitle = '新建表单类型';
        this.form.name = '';
        this.form.codePrefix = '';
        this.form.description = '';
        this.isEdit = false;
      } else {
        this.dialogTitle = '编辑表单类型';
        this.form = Object.assign({}, info);
        this.isEdit = true;
      }
      this.dialogFormVisible = true;
    },
    // operationStatus: function(row) {},
    getFormatDate: function (row, column, cellValue, index) {
      return new Date(cellValue + 8 * 3600 * 1000)
        .toJSON()
        .substr(0, 19)
        .replace('T', ' ');
    }
  }
};
</script>

<style scoped lang="scss">
.status-tip {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 2px 2px 0;
  &.open {
    background: #67c23a;
  }
  &.close {
    background: #ff3024;
  }
}
</style>
<style>
/*dialog垂直居中*/
.el-dialog {
  margin-top: 0 !important;
  top: 50%;
  transform: translateY(-50%);
}
.el-form-item__label {
  font-weight: 500;
}
.el-dialog__body {
  padding: 20px;
}
</style>

