{
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // vscode默认启用了根据文件类型自动设置tabsize的选项
  "editor.detectIndentation": false,
  // 重新设定tabsize
  "editor.tabSize": 2,
  // #每次保存的时候自动格式化
  "editor.formatOnSave": false,
  // #每次保存的时候将代码按eslint格式进行修复
  // "eslint.autoFixOnSave": true,
  // 添加 vue 支持
  "eslint.validate": [
    "javascript",
    {
      "language": "html",
      "autoFix": true
    },
    {
      "language": "vue",
      "autoFix": true
    }
  ],
  //使用带引号替代双引号
  "prettier.singleQuote": true,
  //让函数(名)和后面的括号之间加个空格
  // "javascript.format.insertSpaceBeforeFunctionParenthesis": true,
  //.vue文件的格式化方式
  "vetur.format.defaultFormatter.html": "prettyhtml",
  "vetur.format.defaultFormatter.js": "prettier",
  "vetur.format.defaultFormatterOptions": {
    "prettier": {
      "singleQuote": true
    }
  },
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
}