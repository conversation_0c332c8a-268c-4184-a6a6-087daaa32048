<template>
  <div class="gdnumclass">
    <xyy-list-page>
      <template slot="header">
        <el-row class="herader-title" type="flex" justify="space-between" align="middle">
          <span style="font-size:16px">对话明细报表</span>
        </el-row>
        <el-form
          ref="listQuery"
          :rules="rules"
          :inline="true"
          :model="listQuery"
          label-position="right"
          class="search-form"
        >
          <el-row type="flex" class="row-bg" justify="space-between">
            <!-- 日期 -->
            <el-form-item label="日期" prop="dataRange">
              <el-date-picker
                :picker-options="pickerOptions"
                v-model="listQuery.dataRange"
                type="daterange"
                range-separator="-"
                size="small"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                text="erdf"
                prefix-icon="el-icon-date"
                class="innerSelTime"
                @change="queryTime()"
                @focus="dateTimeFocus()"
              />
            </el-form-item>
            <!--员工类型-->
            <el-form-item label="员工组" prop="sheetType">
              <el-select v-model="listQuery.sheetType" placeholder="全部" @change="changeList">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in oderCityData"
                  :key="item.id"
                  :label="item.groupName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <!--坐席号-->
            <el-form-item label="坐席" prop="CustLocation">
              <!-- <el-select v-model="listQuery.CustLocation" placeholder="全部">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in ortoCityData"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>-->
              <el-select
                v-model="listQuery.CustLocation"
                :remote-method="remoteMethod"
                filterable
                remote
                clearable
                placeholder="请输入坐席"
              >
                <el-option
                  v-for="item in options2"
                  :key="item.id"
                  :label="item.code===''?item.name:item.name+'('+item.code+')'"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row>
            <!--会话结果-->
            <el-form-item label="会话结果" prop="timeType" class="biangeng">
              <el-select v-model="listQuery.timeType" placeholder="全部">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in orderTypeData"
                  :key="item.typeResult"
                  :label="item.typeResultText"
                  :value="item.typeResult"
                ></el-option>
              </el-select>
            </el-form-item>
            <!--建立方式-->
            <el-form-item label="建立方式" prop="getType" class="biangeng">
              <el-select v-model="listQuery.getType" placeholder="全部">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in houTypeDta"
                  :key="item.status"
                  :label="item.remark"
                  :value="item.status"
                ></el-option>
              </el-select>
            </el-form-item>
            <!-- 客户名称 -->
            <el-form-item label="客户名称" prop="customName">
              <el-input
                v-model.trim="listQuery.customName"
                clearable
                maxlength="20"
                placeholder="请输入客户名称"
              ></el-input>
              <!-- <el-select
                v-model="listQuery.customName"
                :remote-method="remoteMethod"
                filterable
                remote
                clearable
                placeholder="请输入客户名称"
              >
                <el-option
                  v-for="item in options2"
                  :key="item.id"
                  :label="item.name+'('+item.code+')'"
                  :value="item.id"
                ></el-option>
              </el-select>-->
            </el-form-item>
          </el-row>
          <el-row>
            <!--会话结果-->
            <el-form-item label="来源渠道" prop="channelId" class="biangeng">
              <el-select v-model="listQuery.channelId" placeholder="全部">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in sheetTypeList"
                  :key="item.id"
                  :label="item.groupName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <!--建立方式-->
            <el-form-item label="来源应用" prop="applicationType" class="biangeng">
              <el-select v-model="listQuery.applicationType" placeholder="全部">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in applicationChannelsData"
                  :key="item.appId"
                  :label="item.appName"
                  :value="item.appId"
                ></el-option>
              </el-select>
            </el-form-item>
            <!-- 是否评价 -->
            <el-form-item label="是否评价" prop="isEstimate" class="biangeng">
              <el-select v-model="listQuery.isEstimate" placeholder="全部">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in evaluationData"
                  :key="item.id"
                  :label="item.groupName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row style="text-align:right;float:right;margin-top: -65px;">
            <el-form-item>
              <div class="searchTwoButton">
                <el-button
                  type="primary"
                  size="small"
                  @click="checkTimer(handerSearch('listQuery'))"
                >查询</el-button>
                <!-- <el-button size="small" @click="checkTimer(oldSearch('listQuery'))">重置</el-button> -->
                <el-button class="export-excel" @click="exportGet" style="padding-top: 9px;">导出</el-button>
              </div>
            </el-form-item>
          </el-row>
        </el-form>
      </template>
      <template slot="body">
        <div class="info-container">
          <info :info="infodata()"></info>
        </div>
        <xyy-table
          v-loading="tabLoading"
          :is-stripe="false"
          :data="list"
          :col="col"
          :list-query="queryList"
          :offset-top="240"
          :operation="operation"
          @get-data="getList"
          @operation-click="operationClick"
        ></xyy-table>
        <!-- 查看弹窗 -->
        <el-dialog :visible.sync="dialogTableVisible">
          <div v-for="(lll,index) in good" :key="index">
            <span>{{lll.kefuName}} {{lll.kefuCode}} {{lll.createTime}}</span>
            <p>{{lll.content}}</p>
          </div>
        </el-dialog>
      </template>
    </xyy-list-page>
    <expor-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    ></expor-tip>
  </div>
</template>

<script>
import exporTip from '@/views/work-sheet/components/exportTip';
import { mockGDType, mockGDUser } from './components/mock';
import {
  dialogueTrafficList,
  getTrafficList,
  getOrderTypeList,
  getTraffic,
  getRoleTra,
  exportTrafficList,
  getSources
} from '@/api/nodemanager';
import chosseTypeAlert from './components/commonDialog';
import formTypeListVue from '../form/form-type-list.vue';
import axios from 'axios';
export default {
  name: 'dialogue',
  components: {
    chosseTypeAlert,
    exporTip
  },
  data() {
    return {
      options2: [],
      tabLoading: false,
      good: [
        {
          content: '聊天内容1',
          createTime: '创建时间1',
          kefuCode: '客服工号',
          kefuName: '客服名称',
          type: '消息类型',
          uid: 1,
          userNickName: '用户昵称'
        },
        {
          content: '聊天内容2',
          createTime: '创建时间2',
          kefuCode: '客服工号2',
          kefuName: '客服名称2',
          type: '消息类型2',
          uid: 1,
          userNickName: '用户昵称2'
        }
      ],
      dialogTableVisible: false, // 控制弹出框
      ortoCityData: [],
      queryList: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      wenhao: false,
      changeExport: false,
      loading: false,
      mocktype: this.APIMockGDType(),
      mockCity: this.APICityGDType(),
      houTypeDta: [
        { status: 1, remark: '接入会话' },
        { status: 2, remark: '转入会话' },
        { status: 3, remark: '留言发起' },
        { status: 4, remark: '坐席主动' }
      ], //建立方式
      yuanTypeDta: [
        { status: 0, remark: '坐席手动' },
        { status: 1, remark: '系统自动变更' }
      ],
      sheetTypeList: [
        { id: 1000, groupName: 'PC' },
        // { id: 1001, groupName: '微信服务号' },
        { id: 1002, groupName: '微信H5' },
        { id: 1003, groupName: 'APP' },
        { id: 1004, groupName: '客服工作台' }
      ],
      applicationChannelsData: [],
      evaluationData: [
        { id: 1, groupName: '是' },
        { id: 0, groupName: '否' }
      ],
      orderTypeData: [], //会话结果
      oderCityData: [],
      dataRange: '',
      formAdressId: '',
      formTypeId: '',
      created: false,
      list: [],
      listQuery: {
        dataRange: [], // 日期
        sheetType: '', // 工单类型
        CustLocation: '', // 客户所在地
        timeType: '', // 客户所在地
        getType: '',
        channelId: '', //来源渠道
        applicationType: '', //来源应用
        isEstimate: '', // 是否评价
        customName: '' // 客户名称
      },
      radio: 3,
      rules: {
        dataRange: [
          { required: false, message: '日期为必填项', trigger: 'blur' }
        ]
      },
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.minDate = minDate;
          this.maxDate = maxDate;
        },
        disabledDate: time => {
          const curDate = new Date().getTime();
          const two = 365 * 2 * 24 * 3600 * 1000;
          const twoyear = curDate - two;
          const three = 92 * 24 * 3600 * 1000;
          if (this.minDate) {
            return (
              time.getTime() > Date.now() ||
              time.getTime() < twoyear ||
              time > new Date(this.minDate.getTime() + three) ||
              time < new Date(this.minDate.getTime() - three)
            );
          }
          return time.getTime() > Date.now() || time.getTime() < twoyear;
        }
      },
      col: [
        {
          index: 'reportDate',
          name: '序号',
          width: 50,
          fixed: true,
          resizable: true
        },
        {
          index: 'customName',
          name: '访客姓名',
          width: 180,
          fixed: true,
          resizable: true
        },
        {
          index: 'kefuCode',
          name: '坐席工号',
          width: 95,
          fixed: true,
          resizable: true
        },
        {
          index: 'userName',
          name: '坐席名称',
          width: 95,
          fixed: true
        },
        {
          index: 'groupName',
          name: '员工组',
          width: 120,
          fixed: true,
          resizable: true
        },
        {
          index: 'regionName',
          name: '地理组合',
          width: 120,
          resizable: true
        },
        {
          index: 'appName',
          name: '来源应用',
          width: 130,
          resizable: true
        },
        {
          index: 'channelName',
          name: '来源渠道',
          width: 120,
          resizable: true
        },
        {
          index: 'cmsgCount',
          name: '访客消息数',
          width: 100,
          resizable: true
        },
        {
          index: 'kmsgCount',
          name: '坐席消息数',
          width: 120,
          resizable: true
        },
        {
          index: 'smsgCount',
          name: '自动回复消息数',
          width: 130,
          resizable: true
        },
        {
          index: 'startTime',
          name: '开始时间',
          width: 160,
          resizable: true
        },
        {
          index: 'endTime',
          name: '结束时间',
          width: 160,
          resizable: true
        },
        {
          index: 'firstResTime',
          name: '首次响应时间',
          width: 160,
          resizable: true
        },
        {
          index: 'firstResLastTime',
          name: '首次响应时长(s)',
          width: 130,
          resizable: true
        },
        {
          index: 'avgResLastTime',
          name: '平均响应时长(s)',
          width: 130,
          resizable: true
        },
        {
          index: 'queueUpLastTime',
          name: '排队时长(s)',
          width: 110,
          resizable: true
        },
        {
          index: 'timeused',
          name: '会话持续时长(s)',
          width: 130,
          resizable: true
        },
        {
          index: 'buildType',
          name: '建立方式',
          width: 100,
          resizable: true
        },
        {
          index: 'dialogResultText',
          name: '会话结果',
          width: 150,
          resizable: true
        },
        {
          index: 'isEstimate',
          name: '是否评价',
          width: 80,
          resizable: true
        },
        {
          index: 'adviceTime',
          name: '客户评价时间',
          width: 130,
          resizable: true
        },
        {
          index: 'estimate',
          name: '评价结果',
          width: 100,
          resizable: true
        },
        {
          index: 'advice',
          name: '评价备注',
          width: 120,
          resizable: true
        },
        {
          index: 'operation',
          name: '操作(会话内容)',
          width: 120,
          operation: true
        }
      ],
      operation: [
        {
          name: '查看',
          type: 1
        }
      ],
      dialogInfoUrl: '',
      url: ``,
      timelist: {
        1: '接入会话',
        2: '转入会话',
        3: '留言发起',
        4: '坐席主动'
      },
      isPing: {
        1: '是',
        0: '否'
      },
      manList: {
        0: '',
        1: '非常满意',
        2: '满意',
        3: '一般',
        4: '不满意',
        5: '非常不满意'
      }
    };
  },
  created() {
    this.getNowTimeDate();
    this.getSourcesList();
    this.APIGetTrafficList();
    this.APIGetOrderTypeList();
    this.created = true;
  },
  activated() {
    // if (!this.created) {
    //   this.APISelectWorkorderList();
    // }
    this.created = false;
    this.getList(this.queryList);
  },
  methods: {
    // 坐席搜索
    async remoteMethod(query) {
      if (query !== '') {
        var params = {
          page: 1,
          pagesize: 1000,
          searchtype: 'name',
          searchval: query
        };
        await getRoleTra(params).then(res => {
          this.options2 = res.data.page.datas;
          console.log(this.options2);
          return res.data;
        });
      } else {
        this.options2 = [];
      }
    },
    getList: function(queryList) {
      const { page, pageSize } = queryList;
      const name = this.queryList.creatorName;
      const {
        dataRange,
        sheetType,
        CustLocation,
        timeType,
        getType,
        channelId,
        applicationType,
        isEstimate,
        customName
      } = this.listQuery;
      const param = {
        pageNum: page,
        pageSize,
        endTime: dataRange[1],
        startTime: dataRange[0],
        dialogResult: timeType,
        buildType: getType,
        kefuGroupId: sheetType,
        kefuid: CustLocation || null,
        channelId: channelId,
        appId: applicationType,
        isEstimate: isEstimate,
        customName: customName || null
      };
      this.tabLoading = true;
      dialogueTrafficList(param)
        .then(res => {
          // console.log(res);
          this.tabLoading = false;
          const { total } = res.data;
          this.list = res.data.records;
          let reportDate = {};
          let firstResTime = {};
          let adviceTime = {};
          this.list.forEach((item, index) => {
            reportDate = {};
            firstResTime = {};
            adviceTime = {};
            item.buildType = this.timelist[item.buildType];
            item.isEstimate = this.isPing[item.isEstimate];
            if (item.firstResTime == null) {
              firstResTime = '-';
            } else {
              firstResTime = item.firstResTime;
            }
            if (item.adviceTime == null) {
              adviceTime = '-';
            } else {
              adviceTime = item.adviceTime;
            }
            item.estimate = this.manList[item.estimate];
            this.list[index] = Object.assign({}, this.list[index], {
              reportDate: index + 1
            });
            this.list[index] = Object.assign({}, this.list[index], {
              firstResTime: firstResTime,
              adviceTime: adviceTime
            });
          });
          this.queryList = {
            ...this.queryList,
            total: Number(total)
          };
        })
        .catch(() => {
          this.tabLoading = false;
        });
    },
    operationClick: function(type, row) {
      switch (type) {
        case 1:
          this.dialogInfoUrl = row.dialogInfoUrl;
          const a = document.createElement('a');
          a.href = window.open(this.dialogInfoUrl);
          // a.click();
          break;
      }
    },
    handleChangeExport(info) {
      const that = this;
      this.changeExport = false;
      if (info === 'go') {
        that.$router.push({
          path: '/other/index/'
        });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    exportGet() {
      const that = this;
      if (!that.list || !that.list.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }

      const param = {
        endTime: that.listQuery.dataRange[1],
        startTime: that.listQuery.dataRange[0],
        buildType: this.listQuery.getType,
        kefuGroupId: this.listQuery.sheetType,
        kefuid: this.listQuery.CustLocation,
        dialogResult: this.listQuery.timeType,
        channelId: this.listQuery.channelId,
        appId: this.listQuery.applicationType,
        isEstimate: this.listQuery.isEstimate,
        customName: this.listQuery.customName
      };
      exportTrafficList(param).then(res => {
        if (res.hasOwnProperty('code')) {
          that.$XyyMessage.error(res.msg);
        } else {
          this.url = `${process.env.BASE_API_IM}/report/dialogInfoReport/export?endTime=${that.listQuery.dataRange[1]}&startTime=${that.listQuery.dataRange[0]}&buildType=${this.listQuery.getType}&dialogResult=${this.listQuery.timeType}&kefuGroupId=${this.listQuery.sheetType}&kefuid=${this.listQuery.CustLocation}&channelId=${this.listQuery.channelId}&appId=${this.listQuery.applicationType}&isEstimate=${this.listQuery.isEstimate}&customName=${this.listQuery.customName}`;
          if (
            this.$store.getters.channel &&
            this.$store.getters.channel.businessPartCode
          ) {
            this.url =
              this.url +
              `&businessPartCode=${this.$store.getters.channel.businessPartCode}`;
          }
          const apill = document.createElement('a');
          apill.href = this.url;
          apill.click();
        }
      });
    },
    // oldSearch() {
    //   this.getNowTimeDate();
    //   this.listQuery.sheetType = '';
    //   this.listQuery.CustLocation = '';
    //   this.APISelectWorkorderList();
    // },
    handerSearch() {
      this.getList(this.queryList);
    },
    // 时间格式化
    getFormatDate: function(row, column, cellValue, index) {
      return new Date(cellValue + 8 * 3600 * 1000)
        .toJSON()
        .substr(0, 19)
        .replace('T', ' ');
    },
    // 当前时间
    getNowTimeDate() {
      const time = new Date(8 * 3600 * 1000 + +new Date())
        .toJSON()
        .substr(0, 10);

      this.listQuery.dataRange = [time, time];
    },
    dateTimeFocus() {
      this.minDate = undefined;
      // 条件时间查询
      const that = this;
      that.$nextTick(function() {
        document
          .getElementsByClassName('el-picker-panel')[0]
          .addEventListener('click', function() {
            // that.$refs.messageDrop.show();
          });
      });
    },
    queryTime() {
      // this.$refs.messageDrop.show();
    },
    getSummaries() {
      const sums = [];
      this.col.forEach((column, index) => {
        if (index === 0) {
          sums[column.index] = '合计';
          return;
        }
        const values = this.list.map(item => Number(item[column.index]));
        if (!values.every(value => isNaN(value))) {
          sums[column.index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          // sums[index] += ' 元';
        } else {
          sums[column.index] = '';
        }
      });

      this.list.push(sums);
    },

    changeToNumber(data) {
      const newdata = {};
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        for (const key in element) {
          if (element.hasOwnProperty(key)) {
            const objelement = element[key];
            newdata;
          }
        }
        newdata[element];
      }
    },
    infodata() {
      return [
        {
          title: '地理组合',
          info: '客户进入在线客服分流后的地区组'
        },
        {
          title: '开始时间',
          info: '会话开始时间'
        },
        {
          title: '结束时间',
          info: '会话结束时间'
        },
        {
          title: '首次响应时间',
          info: '客服首次回复时间（不包含自动回复）'
        },
        {
          title: '首次响应时长',
          info: '首次响应时间-开始时间'
        },
        {
          title: '平均响应时长',
          info: '（坐席回复时间-访客消息时间）之和/消息对数'
        },
        {
          title: '排队时长',
          info: '客户排队时间'
        },
        {
          title: '会话持续时长',
          info: '结束时间-开始时间'
        },
        {
          title: '建立方式',
          info: '接入会话、转入会话、留言发起、坐席手动'
        },
        {
          title: '会话结果',
          info: '坐席超时、访客超时、手动结束、访客离线、转出到坐席（工号）'
        }
      ];
    },
    // 获取来源渠道数据
    getSourcesList() {
      getSources()
        .then(response => {
          this.applicationChannelsData = response.data;
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    /**
     * 工单量报表API
     */

    APISelectWorkorderList() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      dialogueTrafficList({
        customerSource: this.listQuery.CustLocation,
        endTime: this.listQuery.dataRange[1],
        formTypeId: this.listQuery.sheetType,
        startTime: this.listQuery.dataRange[0]
      }).then(res => {
        this.loading.close();
        let dataListList = res.data;
        if (
          !dataListList &&
          typeof dataListList !== 'undefined' &&
          dataListList != 0
        ) {
          this.$XyyMessage.success('暂时无数据');
          this.list = [];
          return;
        }
        if (res.code === 1) {
          if (res.data.length === 0) {
            this.$XyyMessage.success('暂时无数据');
          }
          this.list = res.data;
        } else {
          this.$XyyMessage.error(res.msg);
        }
        // this.getSummaries();
      });
    },
    APIGetTrafficList() {
      getTrafficList().then(res => {
        if (res.code === 1) {
          this.oderCityData = res.data;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    APIGetOrderTypeList() {
      getOrderTypeList().then(res => {
        if (res.code === 1) {
          this.orderTypeData = res.data;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    changeList() {
      var parm = this.listQuery.sheetType;
      if (parm) {
        getTraffic(parm).then(res => {
          if (res.code == 0) {
            this.$XyyMessage.error(res.msg);
          } else if (res.code == 1) {
            this.ortoCityData = res.data.userKefuList;
          }
        });
      }
    },
    APIMockGDType() {
      return mockGDType();
    },
    APICityGDType() {
      return mockGDUser();
    }
  }
};
</script>
<style scoped lang="scss">
/deep/.el-date-editor {
  /*width: 490px;*/
}
/deep/.page-header {
  padding-bottom: 0 !important;
}
</style>
<style>
.el-form--inline .biangeng {
  width: 25%;
}
.el-form--inline .biangeng .el-form-item__content {
  width: 65%;
}
.kuaijie {
  font-size: 14px;
  color: #606266;
  padding-right: 15px;
}
.danxaun {
  margin-top: 12px;
}
.search-form {
  border-bottom: 1px dashed #e4e4eb;
  overflow: hidden;
}
.herader-title {
  font-size: 14px;
  color: #393943;
  padding: 10px 0;
  background: #e4e4e4;
  margin-bottom: 20px;
}

.gdnumclass .el-table th > .cell {
  position: relative;
  word-wrap: normal;
  text-overflow: ellipsis;
  vertical-align: middle;
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  /* display: flex;
    flex-direction: column;
    align-items: flex-start; */
  padding-top: 10px;
  padding-bottom: 10px;
}

.gdnumclass .el-table th > .cell p {
  display: block;
  margin-block-start: 0px;
  margin-block-end: 0px;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  text-align: right;
}

.gdnumclass .el-table th > .cell small {
  font-size: 12px;
  color: #909399;
  display: block;
  margin-block-start: 0px;
  margin-block-end: 0px;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  text-align: right;
}

.gdnumclass .el-table tr td .cell {
  height: 100%;
  line-height: 25px;
  overflow-x: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  word-break: normal;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  padding-left: 10px;
  padding-right: 10px;
  white-space: normal;
  display: block;
  text-align: center;
}
.gdnumclass .el-table tr td:nth-child(2) .cell {
  text-align: left;
}
.gdnumclass .el-table tr td:nth-child(21) .cell {
  text-align: left;
}
.gdnum_input_group .el-form-item__error {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  margin-left: 15px;
}
.el-dialog__body {
  height: 400px;
  overflow-x: auto;
}
</style>

<style scoped lang="scss">
/deep/.el-table {
  /deep/.el-table__fixed {
    height: auto !important;
    bottom: 16px;
    margin-bottom: 0 !important;
  }
  /deep/.el-table__fixed-body-wrapper {
    top: 44px !important;
  }
}
/deep/.el-table__body tr td {
  border-bottom: 1px solid #ebeef5;
  background: #fff;
}
.el-menu-item.is-active {
  color: #3b95a8 !important;
}
/deep/.el-input__inner {
  height: 36px !important;
  line-height: 36px !important;
}
/deep/.el-button {
  height: 36px;
}
.el-menu.el-menu--horizontal {
  border-bottom: 1px dashed #e4e4eb;
  padding-bottom: 20px;
}
.search-box {
  width: 344px;
  float: right;
  .el-input {
    width: calc(100% - 76px);
    /deep/ .el-input__inner {
      height: 36px;
      line-height: 36px;
    }
  }
  button {
    width: 68px;
    height: 36px;
    float: right;
  }
}
.status-tip {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 2px 2px 0;
  &.open {
    background: #67c23a;
  }
  &.close {
    background: #ff3024;
  }
}

.toprow {
  display: flex;
  flex-direction: row;
}

.timediv {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 14px;
  margin-right: 30px;
}

/*.innerSelTime {*/
/*margin-left: 15px;*/
/*}*/

.gdnumheader {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.gdnum_input_mainheader {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.gdnum_input_group {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.gdnum_button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}

.searchTwoButton {
  width: 100%;
}
.el-form--inline .biangeng {
  width: 25%;
}
</style>

