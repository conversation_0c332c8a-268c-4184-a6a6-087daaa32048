import router from './router';
import store from './store';
import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css'; // progress bar style
import { Message } from 'element-ui'; //MessageBox
// import { getToken, setToken } from "@/utils/auth"; // getToken from cookie

const whiteMenu = [
  '/empty',
  '/error/500',
  '/error/NoNetwork',
  '/customerService/index',
  '/message/list',
  '/preview/list',
  '/403'
];
NProgress.configure({ showSpinner: false }); // NProgress configuration
router.beforeEach((to, from, next) => {
  // 顶部加载条(开启/关闭)
  // NProgress.start();
  // NProgress.done();
  // const menu = store.getters.menu;
  // if (whiteMenu.indexOf(to.path) > -1) {
  //   // 白名单内直接放行
  //   if (to.path === '/customerService/index') {
  //     // 在线客服暂时未加权限，先放入白名单，直接跳转外链
  //     window.open('http://admin-im.test.ybm100.com/loginsuccess', '_blank')
  //       .location;
  //   } else {
  //     next();
  //   }
  // }
  // else
  // console.log('路由：：：：', store.getters.name);
  // console.log('路由：：：：', store.getters.channel);
  if (store.getters.name && store.getters.channel) {
    // 白名单加入验证
    const menu = store.getters.menu;
    if (whiteMenu.indexOf(to.path) > -1) {
      // 白名单内直接放行
      if (to.path === '/customerService/index') {
        // 在线客服暂时未加权限，先放入白名单，直接跳转外链
        window.open('http://cc-admin-im.ybm100.com/index', '_blank').location;
      } else {
        next();
      }
    }
    // 通过用户名判断登录
    if (menu.some(item => to.meta.code === item.code)) {
      // 已登录用户判断是否有当前模块权限
      // 重新打开已关闭标签页
      const item = menu.filter(item => {
        return item.code === to.meta.code;
      });
      // sessionStorage.setItem('lastUrl', to.path);

      window.localStorage.removeItem('currentId');
      window.localStorage.setItem('currentId', item[0].id);
      if (
        store.getters.visitedViews.every(route => route.path !== to.path) &&
        !to.query.t
      ) {
        next({
          path: to.path,
          query: {
            ...to.query,
            t: new Date().getTime()
          }
        });
      } else if (!to.query.t) {
        next({
          path: to.path,
          query: {
            ...store.getters.getSelectView(to).query,
            t: store.getters.getSelectView(to).query.t || new Date().getTime()
          }
        });
      } else {
        next();
      }
    } else {
      Message({
        type: 'warning',
        message: '对不起，您没有当前模块权限'
      });
    }
  } else if (to.path == '/preview/list') {
    next();
  } else if (to.path == '/403') {
    next();
  } else {
    store
      .dispatch('GetInfo')
      .then(res => {
        // window.zhuge.identify(res.data.id, {
        //   name: res.data.attributes.nickname
        // });
        try {
          webSdk.track(res.data.id, {
            name: res.data.attributes.nickname
          });
        } catch (error) {
          console.error(error);
        }

        // 拉取用户信息
        store
          .dispatch('workType/getWorkType')
          .then(res => {
            next({ ...to, replace: true });
          })
          .catch(err => {
            next({ ...to, replace: true });
          });
      })
      .catch(err => {
        //无权限
        if (err.type === '403') {
          next({
            path: '/403',
            query: err
          });
        }
      });
    /* if (getToken()) {
      console.log('cookie', getToken());
      store
        .dispatch('GetInfo')
        .then(res => {
          // 拉取用户信息
          next();
        })
        .catch(err => {
          console.log('err', err);
        });
    } else {
      window.location =
        'https://messo.test.ybm100.com/login?service=http://zhou.dev.ybm100.com:9528';
    } */
    /* console.log('cookie before', getToken());
    MessageBox({
      title: '消息',
      message: '是否去登录',
      showCancelButton: true,
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })
      .then(action => {
        console.log('登录');
        window.location =
          'https://messo.test.ybm100.com/login?service=http://ec-service.test.ybm100.com/pc';
      })
      .catch(() => {
        console.log('cookie after', getToken());

        store
          .dispatch('GetInfo')
          .then(res => {
            // 拉取用户信息
            next();
          })
          .catch(err => {
            console.log('err', err);
          });
      }); */
  }
});

router.afterEach((to, from) => {
  NProgress.done(); // 结束Progress
});
