<template>
  <div class="wrapper">
    <el-form ref="form" :rules="rules" :model="form" inline label-width="90px">
      <el-form-item label="日期" prop="date">
        <el-date-picker
          v-model="date"
          type="daterange"
          placeholder="开始日期"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="工单类型">
        <el-select v-model="form.orderType">
          <el-option
            v-for="(item, index) in orderTypeList"
            :key="index"
            :value="item.id"
            :label="item.typeName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="客户所在地">
        <el-select v-model="form.customerSource">
          <el-option value label="全部"></el-option>
          <el-option
            v-for="(item, index) in customerSourceList"
            :key="index"
            :value="item.id"
            :label="item.sourceName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="问题分类">
        <el-cascader
          v-model="form.questionType"
          :options="questionTypeList"
          :props="{
            value: 'pathCodes',
            label: 'typeName',
            checkStrictly: true
          }"
        ></el-cascader>
      </el-form-item>
      <el-form-item label=" ">
        <el-button :loading="loading" type="primary" @click="search">查询</el-button>
        <el-button type="primary" @click="reset">重置</el-button>
        <el-button :loading="loadingExport" @click="exportExcel">导出Excel</el-button>
      </el-form-item>
    </el-form>

    <QuestionCatagoryTable ref="table" :data="tableData" @clickCell="onClickCell"></QuestionCatagoryTable>

    <expor-tip
      :change-export="visible"
      @handleExoprClose="visible = false"
      @handleChangeExport="toExportList"
    ></expor-tip>
  </div>
</template>

<script>
import {
  apiGetOrderType,
  apiGetCustomerSource,
  apiGetQuestionReport,
  apigetProblemTree,
  apiExportExcel
} from '@/api/report/index';
import QuestionCatagoryTable from './table.vue';
import dayjs from 'dayjs';
import exporTip from '@/views/work-sheet/components/exportTip';

function initForm() {
  return {
    startDate: dayjs().format('YYYY-MM-DD'),
    endDate: dayjs().format('YYYY-MM-DD'),
    orderType: '',
    customerSource: '',
    questionType: ''
  };
}

export default {
  name: 'QuestionCatagory',
  components: { QuestionCatagoryTable, exporTip },
  data() {
    return {
      form: initForm(),
      rules: {
        date: {
          required: true,
          validator: (_, __, callback) => {
            if (this.form.startDate && this.form.endDate) {
              callback();
            } else {
              return callback('请选择日期');
            }
          }
        }
      },
      orderTypeList: [],
      customerSourceList: [],
      questionTypeList: [],
      tableData: [],
      loading: false,
      loadingExport: false,
      visible: false
    };
  },
  computed: {
    date: {
      get() {
        return [this.form.startDate, this.form.endDate];
      },
      set(val) {
        if (val) {
          [this.form.startDate, this.form.endDate] = [val[0], val[1]];
        } else {
          [this.form.startDate, this.form.endDate] = ['', ''];
        }
      }
    }
  },
  watch: {
    'form.orderType'(val) {
      this.form.questionType = '';
      apigetProblemTree(val).then(res => {
        if (res.code === 1) {
          this.questionTypeList = res.data;
        }
      });
    }
  },
  created() {
    apiGetOrderType().then(res => {
      if (res.code === 1) {
        this.orderTypeList = res.data;
        this.form.orderType = res.data[0] && res.data[0].id;
      }
    });
    apiGetCustomerSource().then(res => {
      if (res.code === 1) {
        this.customerSourceList = res.data;
      }
    });
  },
  methods: {
    async getReport() {
      if (await this.$refs.form.validate()) {
        this.loading = true;
        let problemClassification = '';
        const len = this.form.questionType.length;
        if (len) {
          problemClassification = this.form.questionType[len - 1];
        }
        apiGetQuestionReport({
          startTime: this.form.startDate,
          endTime: this.form.endDate,
          customerSource: this.form.customerSource,
          formTypeId: this.form.orderType,
          problemClassification
        })
          .then(res => {
            if (res.code === 1) {
              this.tableData = res.data;
            } else if (res.msg) {
              this.$XyyMessage.warning(res.msg);
            }
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
    search() {
      this.getReport();
    },
    reset() {
      this.form = initForm();
    },
    async exportExcel() {
      if (await this.$refs.form.validate()) {
        this.loadingExport = true;
        apiExportExcel({
          startTime: this.form.startDate,
          endTime: this.form.endDate,
          customerSource: this.form.customerSource,
          formTypeId: this.form.orderType
        })
          .then(res => {
            if (res.code === 1) {
              this.visible = true;
            } else if (res.msg) {
              this.$XyyMessage.warning(res.msg);
            }
          })
          .finally(() => {
            this.loadingExport = false;
          });
      }
    },
    toExportList(info) {
      this.visible = false;
      if (info === 'go') {
        this.$router.push({
          path: '/other/index/'
        });
      }
    },
    onClickCell({ name, questionCode }) {
      const query = {
        formTypeId: this.form.orderType,
        problemClassification: questionCode,
        dataRange: `${this.form.startDate},${this.form.endDate}`,
        customerSource: this.form.customerSource
      };
      switch (name) {
        case 'timeoutNum':
          query.timeout = 1;
          break;
        case 'unClosedNum':
          query.currentState = 1;
          break;
      }
      switch (name) {
        case 'questionName':
        case 'handle':
        case 'timeoutNum':
        case 'unClosedNum':
          this.$store
            .dispatch('tagsView/delView', {
              name: 'workOrderInformation',
              path: '/report/workOrderInformation',
              meta: { componentName: 'workOrderInformation' }
            })
            .then(res => {
              this.$router.push({ name: 'workOrderInformation', query });
            });
          break;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  padding: 20px;
}
</style>
