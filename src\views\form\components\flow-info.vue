<template>
  <div class="container clearfix">
    <!-- 左侧 节点列表 start -->
    <div class="left">
      <div class="title">
        节点
        <description :info="info" :key="0"></description>
        <span v-if="!readOnly" class="btn-box">
          <el-tooltip
            effect="light"
            content="引用节点"
            placement="bottom"
            popper-class="icon-title"
          >
            <svg-icon
              icon-class="import-node"
              @click="nodeOpen = true"
            ></svg-icon>
          </el-tooltip>
          <!-- <svg-icon icon-class="import-node" title="引用节点" @click="nodeOpen = true"></svg-icon> -->
          <el-dropdown
            trigger="click"
            placement="bottom-start"
            @command="addNode"
          >
            <span class="el-dropdown-link">
              <el-tooltip
                effect="light"
                content="创建节点"
                placement="bottom"
                popper-class="icon-title"
              >
                <i class="el-icon-plus"></i>
              </el-tooltip>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="0">
                <el-popover placement="right" trigger="hover">
                  <div>即工单发起的节点，对应新建工单</div>
                  <div slot="reference">发起节点</div>
                </el-popover>
              </el-dropdown-item>
              <el-dropdown-item command="1">
                <el-popover placement="right" trigger="hover">
                  <div>
                    即工单处理的节点，对应流转中工单受理人，处理回复工单
                  </div>
                  <div slot="reference">处理节点</div>
                </el-popover>
              </el-dropdown-item>
              <el-dropdown-item command="2">
                <el-popover placement="right" trigger="hover">
                  <div>
                    流程中非处理职责，对应流转中，被抄送的用户可查看工单
                  </div>
                  <div slot="reference">抄送节点</div>
                </el-popover>
              </el-dropdown-item>
              <el-dropdown-item command="3">
                <el-popover placement="right" trigger="hover">
                  <div>
                    即工单关闭的节点，对应发起人或流转中工单受理人 ，关闭工单
                  </div>
                  <div slot="reference">关闭节点</div>
                </el-popover>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </span>
      </div>
      <draggable
        ref="nodeList"
        :list="form"
        :sort="draggable"
        :draggable="draggable ? '.item' : ''"
        class="node-list"
        @start="onDragStart"
        @end="onDragEnd"
      >
        <div
          v-for="(item, index) in form"
          :key="item.id"
          :class="{
            active: item.active,
            require: item.require,
            hover: dragHoverIndex === index,
            dragging: dragIndex === index,
          }"
          class="item"
          @click="changeNode(index, item)"
        >
          <span
            class="draggable"
            @mouseenter="onDragItemEnter(index)"
            @mouseleave="onDragItemLeave(index)"
          >
            <img src="~@/assets/icons/draggable.png" alt="" />
          </span>
          <span :title="item.nodeName" class="node-name">{{
            item.nodeName
          }}</span>
          <el-dropdown
            v-if="!readOnly && item.active"
            trigger="click"
            @command="handleCommand($event, item, index, form)"
            @visible-change="handleChange"
          >
            <span class="el-dropdown-link">
              <i :class="{ active: menuStatus }" class="el-icon-more"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                :disabled="
                  item.nodeClass === 1 || [0, 3].includes(item.nodeType)
                "
                command="copy"
              >复制</el-dropdown-item
              >
              <el-dropdown-item command="del">删除</el-dropdown-item>
              <el-dropdown-item
                :disabled="index === 0"
                command="up"
              >上移</el-dropdown-item
              >
              <el-dropdown-item
                :disabled="index === form.length - 1"
                command="down"
              >下移</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </draggable>
      <node-transfer
        :status.sync="nodeOpen"
        :startable="startable"
        :endable="endable"
        :checked="checked"
        @callback="setNodes"
      ></node-transfer>
    </div>
    <!-- 左侧 节点列表 end-->

    <!-- 右侧 节点信息 start -->
    <div class="right">
      <div v-if="!form.length" class="null-data">
        <img :src="nullDatas" />
      </div>
      <div v-else>
        <div class="title">节点信息</div>
        <node-info
          ref="nodeInfo"
          :key="nodeKey"
          :form="nodeInfo"
          :form-id="formId"
          @saveDone="saveDone"
        />
      </div>
    </div>
  </div>
  <!-- 右侧 节点信息 end -->
</template>

<script>
import {
  getNodeInfoById,
  delFlowNode,
  copyFlowNode,
  moveFlowNode,
  dragFlowNode,
  addCustomNode,
  saveRefNodes
} from '@/api/formManage';
import nodeInfo from './node-info';
import nodeTransfer from './node-transfer';
import nullDatas from '@/assets/work_sheet/notSheet.png';
import description from '@/components/tools/description.vue';
import draggable from 'vuedraggable';
export default {
  name: 'FlowInfo',
  components: { nodeInfo, nodeTransfer, description, draggable },
  props: {
    form: {
      type: Array,
      default: () => []
    },
    formId: {
      type: [String, Number, Boolean],
      required: true
    }
  },
  data() {
    return {
      nodeInfo: {},
      nodeKey: 0,
      menuStatus: false, // 节点菜单展开状态
      nodeOpen: false, // 引用节点弹框
      nullDatas: nullDatas,
      info: [
        {
          title: '可创建两种类型节点，引用和新建',
          info: ''
        },
        {
          title: '引用节点',
          info:
            '可从节点池中引用节点加入流程，引用后的节点不能修改节点设置项，仅能沿用'
        },
        {
          title: '新建节点',
          info: '新建的节点可自由配置，不受限制'
        }
      ],
      readOnly: false, // 只读状态
      draggable: false,
      // 拖拽中的下标
      dragIndex: -1,
      // 拖拽项hover下标
      dragHoverIndex: -1
    };
  },
  computed: {
    startable() {
      return this.form.some(el => el.nodeType === 0);
    },
    endable() {
      return this.form.some(el => el.nodeType === 3);
    },
    checked() {
      return this.form.filter(el => el.nodeClass === 1);
    }
  },
  watch: {
    form(list) {
      list.length &&
        list.forEach((el, index) => {
          if (el.active) {
            this.getNodeInfoById(el.id, el.nodeType);
          }
        });
    }
  },
  created() {
    if (this.form.length) {
      const activeNode = this.form.filter(el => el.active);
      if (activeNode.length) {
        this.getNodeInfoById(activeNode[0].id, 0);
      } else {
        this.form[0].active = true;
        this.getNodeInfoById(this.form[0].id, 0);
      }
    }
    this.readOnly = this.$route.query.readOnly
      ? Boolean(this.$route.query.readOnly)
      : false;
  },
  methods: {
    setNodes(datas) {
      const _datas = datas.map(el => {
        return {
          nodeName: el.nodeName,
          nodeType: el.nodeType,
          nodeCode: el.versionCode,
          templateCode: el.templateCode,
          responsibly: el.responsibly,
          closeWay: el.closeWay,
          closeBy: el.closeBy,
          processTimeout: el.processTimeout
        };
      });
      saveRefNodes(_datas, this.formId).then(res => {
        if (res.code === 1) {
          this.$parent.getListNodesByFormId(this.formId);
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    handleCommand(command, item, index, form) {
      switch (command) {
        case 'copy':
          this.copyNode(item.id);
          break;
        case 'del':
          this.delNode(item.id);
          break;
        case 'up':
          this.moveUp(item, index, form);
          break;
        case 'down':
          this.moveDown(item, index, form);
          break;
      }
    },
    handleChange(status) {
      this.menuStatus = status;
    },
    // 获取节点信息
    getNodeInfoById(id, nodeType) {
      // nodeType 节点类型
      if (!id) return false;
      getNodeInfoById(id).then(res => {
        if (res.code === 1) {
          const activeNode = this.form.filter(el => el.active)[0];
          this.nodeInfo = res.data;
          this.nodeInfo.editable = activeNode && activeNode.editable;
          this.nodeKey++;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 新增节点
    addNode(type) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      addCustomNode({ formId: this.formId, nodeType: type })
        .then(res => {
          loading.close();
          if (res.code === 1) {
            this.$XyyMessage.success('添加节点成功');
            this.$emit('reloadNodeList', { id: res.data.id });
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },
    // 切换节点
    changeNode(index, item) {
      const _this = this;
      if (item.active) return false;
      // 校验节点信息是否保存
      if (!this.readOnly && !this.$refs.nodeInfo.editable) {
        this.$XyyMsg({
          title: '提示',
          content: '是否保存当前节点的设置？', // html代码串
          closeBtnClass: 'left',
          okValue: '保存',
          onSuccess: function() {
            _this.$refs.nodeInfo.saveNode(index, item.id);
          },
          button: [
            {
              value: '不保存',
              callback: function() {
                _this.changeNodeStatus(index);
              }
            }
          ]
        });
      } else {
        this.changeNodeStatus(index);
      }
    },
    // 改变active状态
    changeNodeStatus(index) {
      this.form.forEach(element => {
        element.active = false;
      });
      const newValue = Object.assign(this.form[index], { active: true });
      this.form.splice(index, 1, newValue);
    },
    // 删除节点
    delNode(id) {
      const _this = this;
      this.$XyyMsg({
        title: '提示',
        content: '确认删除节点吗？（删除后，数据无法恢复）',
        onSuccess: function() {
          delFlowNode(id).then(res => {
            if (res.code === 1) {
              _this.$XyyMessage.success('删除节点成功');
              _this.$emit('reloadNodeList', { id });
            } else {
              _this.$XyyMessage.error(res.msg);
            }
          });
        }
      });
    },
    /**
     * 复制节点
     */
    copyNode(id) {
      copyFlowNode(id).then(res => {
        if (res.code === 1) {
          this.$emit('reloadNodeList', { id });
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    /**
     * 向上移动
     */
    moveUp(item, index, form) {
      let prevNode = form[index - 1];
      const _sort = item.sort;
      const curNode = { id: item.id, sort: prevNode.sort };
      prevNode = { id: prevNode.id, sort: _sort };
      const datas = [prevNode, curNode];
      moveFlowNode(datas).then(res => {
        if (res.code === 1) {
          this.$emit('reloadNodeList', { id: item.id });
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    /**
     * 向下移动
     */
    moveDown(item, index, form) {
      let nextNode = form[index + 1];
      nextNode = { id: nextNode.id, sort: nextNode.sort };
      const curNode = { id: item.id, sort: item.sort };
      curNode.sort = curNode.sort ^ nextNode.sort;
      nextNode.sort = curNode.sort ^ nextNode.sort;
      curNode.sort = curNode.sort ^ nextNode.sort;
      const datas = [curNode, nextNode];
      moveFlowNode(datas).then(res => {
        if (res.code === 1) {
          this.$emit('reloadNodeList', { id: item.id });
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 节点信息更新后的操作
    saveDone(formData, validator, index, resData, step) {
      // formData--提交表单， validator--数据完整性校验结果 index -- 节点索引 ，resData-- 保存结果信息
      if (resData.status !== 0) {
        // 刷新节点列表
        this.$parent.getListNodesByFormId(
          resData.formId,
          resData.currentNodeId
        );
        return false;
      }

      this.form.forEach((el, ind) => {
        if (el.active) {
          // 更新nodeName
          el.nodeName = formData.nodeName;
          el.nodeType = formData.nodeType;
          // 更新节点完整性状态
          el.require = !validator.valid;
          el.editable = true;
          if (typeof index === 'number') el.active = false;
          this.$set(this.form, ind, el);
        }
      });
      if (typeof index === 'number') this.form[index].active = true;

      if (step) {
        this.$parent.active += step;
      }
    },
    onDragItemEnter(index) {
      if (this.dragIndex >= 0) {
        return;
      }
      this.draggable = true;
      this.dragHoverIndex = index;
    },
    onDragItemLeave(index) {
      if (this.dragIndex >= 0) {
        return;
      }
      this.draggable = false;
      this.dragHoverIndex = -1;
    },
    onDragStart(e) {
      console.log('onDragStart');
      this.dragIndex = e.oldDraggableIndex;
    },
    onDragEnd() {
      console.log('onDragEnd');
      dragFlowNode(this.form.map((val) => ({ id: val.id })));
      this.dragIndex = -1;
    }
  }
};
</script>
<style lang="scss" scoped>
.s-message-footer .closeBtn {
  float: left !important;
}
.container {
  width: 100%;
  margin: 0 auto;
  border-radius: 2px;
  border: 1px solid rgba(228, 228, 235, 1);
  height: calc(100% - 77px);
  overflow-y: auto;
  .left {
    float: left;
    width: 180px;
    padding: 20px;
    .title {
      height: 20px;
      font-size: 14px;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      color: rgba(48, 49, 51, 1);
      line-height: 20px;
      overflow: hidden;
      .btn-box {
        float: right;
        .svg-icon {
          margin-right: 8px;
          cursor: pointer;
        }
        .el-dropdown {
          > span {
            &:focus {
              outline: none;
            }
          }
          .el-icon-plus {
            cursor: pointer;
            color: #3b95a8;
            border: 1px solid #3b95a8;
            font-size: 12px;
            position: relative;
            top: -1px;
          }
        }
      }
    }
    .node-list {
      margin: 16px 0 0;
      padding: 0;
      .item {
        font-size: 14px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
        height: 36px;
        position: relative;
        display: flex;
        align-items: center;
        cursor: pointer;
        .draggable {
          height: 100%;
          flex-shrink: 0;
          cursor: move;
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            width: 16px;
            height: 16px;
          }
        }
        .node-name {
          display: inline-block;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          flex-grow: 1;
          position: relative;
          padding-left: 18px;
        }
        &.hover {
          box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
          margin-left: -8px;
          margin-right: -8px;
          padding-left: 8px;
          padding-right: 8px;
        }
        &.dragging {
          margin-left: -10px;
          margin-right: -10px;
          padding-left: 10px;
          padding-right: 10px;
          box-shadow: 0px -5px 6px 0px #e4e4eb, 0px 2px 10px 0px #e4e4eb;
        }
        &.active {
          color: #40a3b8;
        }
        &.require {
          .node-name::before {
            content: '*';
            display: inline-block;
            color: red;
            position: absolute;
            top: 2px;
            left: 6px;
          }
        }
        .el-dropdown {
          flex-shrink: 0;
          > span {
            &:focus {
              outline: none;
            }
          }
          i {
            margin-top: 2px;
            cursor: pointer;
            position: relative;
            left: 6px;
            transform: rotate(90deg);
            color: #c0c4cc;
          }
          i.active {
            color: #3b95a8;
          }
        }
      }
    }
  }
  .right {
    float: right;
    width: calc(100% - 180px);
    padding: 20px;
    border-left: 1px solid rgba(228, 228, 235, 1);
    min-height: 100%;
    position: relative;
    .null-data {
      text-align: center;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateX(-50%) translateY(-50%);
    }
    .title {
      /*width: 64px;*/
      height: 22px;
      font-size: 16px;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      color: rgba(48, 49, 51, 1);
      line-height: 22px;
    }
  }
}

.clearfix {
  zoom: 1;
  clear: both;
}
</style>
<style lang="scss">
.el-popover.form-tips {
  padding: 0 10px;
  height: 25px;
  line-height: 25px;
}
.el-tooltip__popper.icon-title {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(238, 238, 238, 1);
  height: 25px;
  line-height: 25px;
  padding: 0 12px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(87, 87, 102, 1);
  /deep/.popper__arrow {
    background: #fff;
    border-bottom-color: rgba(238, 238, 238, 1) !important;
  }
}
</style>
