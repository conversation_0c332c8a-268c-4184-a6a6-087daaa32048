<template>
  <div>
    <el-dialog ref="previewDialog"
               :visible="isOpen"
               :close-on-click-modal="false"
               :append-to-body="true"
               title="新增常用图片"
               width="850px"
               top="0"
               custom-class="dialogUpload"
               @close="close"
               @open="openDialog">
      <el-upload ref="dialogUploader"
                 :before-upload="beforeUpload"
                 :action="url"
                 :http-request="uploadFile"
                 :limit="fileNums"
                 :on-exceed="validateNums"
                 :on-error="handleError"
                 :on-progress="handleProgress"
                 :on-change="handleChange"
                 :before-remove="handleReove"
                 :class="[_ploadFile,{completed:uploadList.length===fileNums}]"
                 :auto-upload="false"
                 :with-credentials="true"
                 :data="imgfilenamesArr"
                 name="files"
                 multiple
                 list-type="picture-card"
                 accept=".jpg, .jpeg, .png">
        <i v-if="uploadList.length"
           slot="default"
           class="el-icon-plus"
           @click="uploadStart"></i>
        <svg-icon v-else
                  slot="default"
                  icon-class="emptUpload"
                  class="emptUpload" />
        <div slot="file"
             slot-scope="{file}">
          <file-preview ref="preview-box"
                        :file="file"
                        :percent="file.percent?file.percent:0"
                        :editable="true"
                        :isallcheck="checkAll"
                        @abortCallback="abortFile(file)"
                        @delCallback="delFile(file)" />
        </div>
      </el-upload>
      <div v-if="uploadList.length"
           slot="footer"
           class="preview-title">
        <el-row type="flex"
                justify="space-between"
                align="middle">
          <div>
            <el-checkbox v-model="checkAll"
                         class="checkAll">{{ checkAll?'全不选':'全选' }}</el-checkbox>
            <el-button v-if="checkAll"
                       type="text"
                       @click="isAllDel">删除</el-button>
          </div>
          <div>
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary"
                       @click="pictureAdds">确 定</el-button>
          </div>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import { pictureAdds } from '@/api/configuration/use_image';
import filePreview from './dialogFilePre';
import axios from 'axios';

export default {
  name: '',
  components: {
    filePreview
  },
  props: {
    isOpen: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      MIME_TYPES: ['image/gif', 'image/bmp', 'image/jpeg', 'image/png'],
      checkAll: false,
      url: process.env.BASE_API_IM + '/picture/adds',
      fileNums: 10, // 文件最大上传个数
      imgfilenamesArr: { imgfilenames: '' },
      uploadList: [], // 上传队列
      failureList: [], // 上传失败文件列表
      uploading: false, // 文件上传中
      uploadNum: null,
      uploadingNum: 0,
      uploadSuccess: false,
      loading: false,
      multiple: true,
      formDate: '',
      fileArr: []
    };
  },
  computed: {
    _ploadFile () {
      if (this.uploadList.length) {
        return 'uploadFile-box';
      } else {
        return 'uploadFileEmpt';
      }
    }
  },
  watch: {},
  mounted () { },
  methods: {
    uploadFile (file) {
      this.formDate.append('files', file.file);
    },
    isAllDel () {
      this.$refs['dialogUploader'].clearFiles();
      setTimeout(() => {
        this.uploadList = this.$refs['dialogUploader'].uploadFiles;
      }, 1000);
      this.checkAll = false;
    },
    close () {
      const that = this;
      if (
        that.$refs['dialogUploader'].uploadFiles.length &&
        !that.uploadSuccess
      ) {
        that.$XyyMsg({
          title: '提示',
          content: '上传图片未保存,确定关闭吗？',
          onSuccess: function () {
            that.$emit('dialogClose');
            setTimeout(() => {
              that.checkAll = false;
              that.$refs['dialogUploader'].uploadFiles = [];
              that.uploadList = [];
            }, 400);
          }
        });
      } else {
        that.$emit('dialogClose');
        if (that.uploadSuccess) {
          that.$refs['dialogUploader'].uploadFiles = [];
          that.uploadList = [];
          this.$parent.pictureList();
        }
      }
    },
    openDialog () {
      this.$nextTick(_ => {
        console.log(
          "this.$refs['dialogUploader']:",
          this.$refs['dialogUploader']
        );
      });
    },
    handleReove (file, fileList) {
      let ismodfiy = false;
      this.$nextTick(_ => {
        console.log(
          "this.$refs['preview-box'].modefiName11:",
          this.$refs['preview-box']
        );
        if (this.$refs['preview-box'].modefiName) {
          ismodfiy = false;
        } else {
          ismodfiy = true;
        }
      });
      return ismodfiy;
    },
    handleChange (file, fileList) {
      if (file.size / 1024 / 1000 > 500) {
        // 附件上传大小的限制
        this.$XyyMessage.error(`附件上传大小不能超过500M`);
        return false;
      }
      console.log('file.type:', file.raw.type);

      if (file.raw.type) {
        if (!this.MIME_TYPES.includes(file.raw.type)) {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: `上传文件格式仅限：<span style="color:red;">gif bmp jpg jpeg png</span>`, // html代码串
            onSuccess: () => { }
          });
          fileList.splice(fileList.length - 1, 1);
          return false;
        }
      } else {
        // element ui 插件bug 无法检测rar rar文件以后缀方式判断
        const _type = file.name
          .slice(file.name.lastIndexOf('.') + 1)
          .toLowerCase();
        if (_type !== 'rar') {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: `上传文件格式仅限：<span style="color:red;">gif bmp jpg jpeg png</span>`, // html代码串
            onSuccess: () => { }
          });
          fileList.splice(fileList.length - 1, 1);
          return false;
        }
      }
      console.log('file, fileList:', file, fileList);
      this.uploadList = fileList;
    },
    pictureAdds () {
      console.log('上传this.uploadList:', this.uploadList);
      this.loading = this.$loading({
        lock: true,
        text: '正在上传……',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      const imgfilenames = this.uploadList.map(item => {
        return item.name;
      });
      this.formDate = new FormData();
      this.$refs['dialogUploader'].submit();
      this.formDate.append('imgfilenames', imgfilenames);
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        withCredentials: true
      };

      if (this.$store.getters.channel && this.$store.getters.channel.businessPartCode) {
        config.headers.businessPartCode = this.$store.getters.channel.businessPartCode;
      }
      axios
        .post(this.url, this.formDate, config)
        .then(res => {
          console.log('resresress:', res);
          if (res.data.code === 1) {
            if (!res.data.data.failFiles.length) {
            } else {
              let msg = '';
              if (res.data.data.failFiles.length > 1) {
                msg = `${res.data.data.failFiles[0].originalFilename}等${res.data.data.failFiles.length}个文件上传失败`;
              } else {
                msg = `${res.data.data.failFiles[0].originalFilename}上传失败`;
              }
              this.$XyyMessage.error(msg);
            }
            this.$XyyMessage.success('上传完成！');
            this.uploading = false;
            this.uploadSuccess = true;
            this.loading.close();
            this.close();
          }
        })
        .catch(res => {
          this.loading.close();
          this.uploadSuccess = true;
          this.close();
          this.$XyyMessage.error(res);
        });
    },
    cancel () {
      const that = this;
      if (that.$refs['dialogUploader'].uploadFiles.length) {
        that.$XyyMsg({
          title: '提示',
          content: '上传图片未保存,确定取消吗？',
          onSuccess: function () {
            that.checkAll = false;
            that.$refs['dialogUploader'].clearFiles();
            setTimeout(() => {
              that.uploadList = that.$refs['dialogUploader'].uploadFiles;
            }, 1000);
            // setTimeout(() => {
            //   that.checkAll = false;
            //   that.$refs['dialogUploader'].uploadFiles = [];
            //   that.uploadList = [];
            // }, 400);
          }
        });
      }
    },
    handleProgress (event, file) {
      if (file) {
        this.uploading = true;
        file.percent = Number(event.percent.toFixed(0));
        if (file.percent > 99) file.percent = 99;
      }
    },
    // 附件上传失败返回
    handleError (res) {
      this.uploading = false;
      this.$XyyMessage.error(`文件上传失败`);
    },
    /**
     * 文件个数超出回调
     */
    validateNums (file, fileList) {
      this.$XyyMessage.error(`最多上传${this.fileNums}个`);
    },
    /**
     * 上传点击事件 初始化上传失败数组
     */
    uploadStart () {
      this.uploadIsOpen = true;
      // this.failureList = [];
    },
    // 附件上传之前拦截
    beforeUpload (file) {
      this.uploadList = [].concat(file);
      if (file.size / 1024 / 1000 > 500) {
        // 附件上传大小的限制
        this.$XyyMessage.error(`附件上传大小不能超过500M`);
        return false;
      }

      if (file.type) {
        if (!this.MIME_TYPES.includes(file.type)) {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: `上传文件格式仅限：<span style="color:red;">gif bmp jpg jpeg png</span>`, // html代码串
            onSuccess: () => { }
          });
          return false;
        }
      } else {
        // element ui 插件bug 无法检测rar rar文件以后缀方式判断
        const _type = file.name
          .slice(file.name.lastIndexOf('.') + 1)
          .toLowerCase();
        if (_type !== 'rar') {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: `上传文件格式仅限：<span style="color:red;">gif bmp jpg jpeg png</span>`, // html代码串
            onSuccess: () => { }
          });
          return false;
        }
      }
    },
    /**
     * 取消上传
     */
    abortFile (file) {
      this.$refs['dialogUploader'].abort(file);
      this.$refs['dialogUploader'].uploadFiles.forEach((el, i) => {
        if (el.uid === file.uid) {
          this.$refs['dialogUploader'].uploadFiles.splice(i, 1);
        }
      });
      this.uploadList = [].concat(this.$refs['dialogUploader'].uploadFiles);
      this.uploading = false;
    },
    delFile (file) {
      // 删除上传队列中的数据
      this.$refs['dialogUploader'].uploadFiles.forEach((el, i) => {
        if (el.uid === file.uid) {
          console.log(55555);
          this.$refs['dialogUploader'].uploadFiles.splice(i, 1);
          // setTimeout(_ => {
          //   this.uploadList = [...this.$refs['dialogUploader'].uploadFiles];
          //   console.log(6666);
          // }, 3000);
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
/deep/.el-dialog.dialogUpload {
  height: 600px;
  top: 50%;
  transform: translateY(-50%);
  overflow: hidden;
  /deep/.el-dialog__header {
    height: 60px;
    padding: 15px 16px 20px 20px;
    border-bottom: 1px solid #dcdfe6;
  }
  /deep/ .el-dialog__body {
    height: calc(100% - 130px);
    overflow: auto;
    padding: 0;
  }
  /deep/.el-dialog__footer {
    height: 70px;
  }
  .checkAll {
    margin-right: 30px;
  }
}
.uploadFile-box {
  float: left;
  margin-left: 22px;
  margin-top: 20px;
  /deep/.el-upload--picture-card {
    width: 89px;
    height: 89px;
    line-height: 89px;
    // background: rgba(245, 247, 250, 1);
    border-radius: 2px;
    border: 1px solid #dcdfe6;
    margin-left: 10px;
    .el-icon-plus {
      color: rgba(216, 216, 216, 1);
    }
  }
  /deep/.el-upload-list--picture-card {
    .el-upload-list__item {
      width: 100px;
      height: auto;
      background: none;
      border: none;
      margin-right: 10px;
      &:focus {
        border: none;
        outline: none;
      }
    }
  }
  /deep/.el-upload__tip {
    line-height: 20px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(144, 147, 153, 1);
  }
}
.uploadFile-box.completed {
  /deep/.el-upload--picture-card {
    display: none;
  }
}
.uploadFileEmpt {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /deep/.el-upload--picture-card {
    border: none;
    background: #fff;
  }
  .emptUpload {
    width: 143px;
    height: 36px;
  }
  /deep/.el-upload-list--picture-card {
    display: none;
  }
}
</style>
