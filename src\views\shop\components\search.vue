<template>
  <div class="shop-area">
    <el-form ref="form" :inline="true" :model="form"   label-width="90px" label-position="left" class="input_serch">
      <el-form-item label="商品编码">
        <el-input v-model="form.barcode"></el-input>
      </el-form-item>
      <el-form-item label="通用名称">
        <el-input v-model="form.commonName"></el-input>
      </el-form-item>
      <el-form-item label="商品名称">
        <el-input v-model="form.productName"></el-input>
      </el-form-item>
      <el-form-item label="展示名称">
        <el-input v-model="form.showName"></el-input>
      </el-form-item>
      <el-form-item label="条码">
        <el-input v-model="form.code"></el-input>
      </el-form-item>
      <el-form-item label="是否易碎品">
        <el-select v-model="form.isFragileGoods" placeholder="">
          <el-option label="全部" value="" ></el-option>
          <el-option label="是" value="1" ></el-option>
          <el-option label="否" value="0" ></el-option>
        </el-select>
<!--        <el-radio-group v-model="form.isFragileGoods">-->
<!--          <el-radio label="1">是</el-radio>-->
<!--          <el-radio label="0">否</el-radio>-->
<!--        </el-radio-group>-->
      </el-form-item>
      <el-form-item label="药品类型">
        <el-select v-model="form.drugClassification" placeholder="">
          <el-option v-for="(item) in drugClassificationList" :label="item.label" :value="item.id" :key="item.id" ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="生产厂家">
        <el-input v-model="form.manufacturer"></el-input>
      </el-form-item>
      <el-form-item label="是否限购">
        <el-select v-model="form.isLimited" placeholder="">
          <el-option label="全部" value="" ></el-option>
          <el-option label="是" value="1" ></el-option>
          <el-option label="否" value="0" ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="商品状态">
        <el-select v-model="form.status" placeholder="">
          <el-option v-for="(item) in statusList" :label="item.label" :value="item.id" :key="item.id" ></el-option>
        </el-select>
      </el-form-item>
<!--      <el-form-item label="商品分类">-->
<!--        <el-cascader v-model="form.categoryFirstId" :props="props">-->
<!--        </el-cascader>-->
<!--      </el-form-item>-->
      <el-form-item label="商品分类">
        <el-select v-model="form.categoryFirstId" placeholder="" @change="handleFirstChange">
          <el-option label="全部" value="" ></el-option>
          <el-option v-for="item in categoryFirstIdList" :label="item.name" :value="item.id" :key="item.id" ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="二级分类">
        <el-select v-model="form.categorySecondId" placeholder="" @change="handleSecondChange">
          <el-option label="全部" value="" ></el-option>
          <el-option v-for="item in categorySecondIdList" :label="item.name" :value="item.id" :key="item.id" ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="三级分类">
        <el-select v-model="form.categoryThirdId" placeholder="">
          <el-option label="全部" value="" ></el-option>
          <el-option v-for="item in categoryThirdIdList" :label="item.name" :value="item.id" :key="item.id" ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否独家">
        <el-select v-model="form.agent" placeholder="">
          <el-option label="全部" value="" ></el-option>
          <el-option label="是" value="1" ></el-option>
          <el-option label="否" value="0" ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否医保">
        <el-select v-model="form.isUsableMedical" placeholder="">
          <el-option label="全部" value="" ></el-option>
          <el-option label="是" value="1" ></el-option>
          <el-option label="否" value="0" ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否高毛">
        <el-select v-model="form.highGross" placeholder="">
          <el-option label="全部" value="" ></el-option>
          <el-option label="是" value="2" ></el-option>
          <el-option label="否" value="1" ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="地域">
        <el-select v-model="form.branchCode" placeholder="" @change="getShopList">

          <!-- 2020.4.1,rl,v1.7.4 -->
          <!-- v-if="item.branchCode!='XS000000'" -->
          <el-option v-for="item in branchCodeList" :label="item.branchName" :value="item.branchCode" :key="item.branchCode" ></el-option>

        </el-select>
      </el-form-item>
      <el-form-item label="店铺ID">
        <!-- allow-create -->
        <el-select v-model="form.shopCode" placeholder=""
           filterable
           clearable
           :remote="form.branchCode == 'XS000000'"
           :remote-method="searchShopList"
           :loading="searchShopListLoading"
        >
          <el-option v-for="item in shopList" :label="item.name" :value="item.shopCode" :key="item.id" ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否促销">
        <el-select v-model="form.isPromotion" placeholder="">
          <el-option label="全部" value="" ></el-option>
          <el-option label="是" value="1" ></el-option>
          <el-option label="否" value="0" ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否控销">
        <el-select v-model="form.isControl" placeholder="">
          <el-option label="全部" value="" ></el-option>
          <el-option label="是" value="1" ></el-option>
          <el-option label="否" value="0" ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否赠品" style="margin-bottom:0;">
        <el-select v-model="form.isGive" placeholder="">
          <el-option label="全部" value="" ></el-option>
          <el-option label="是" value="1" ></el-option>
          <el-option label="否" value="0" ></el-option>
        </el-select>
      </el-form-item>
      <p style="margin-top:0;margin-bottom:20px;text-align:right;">
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </p>

    </el-form>
    <p>

    </p>
  </div>
</template>

<script>
import { getAllBranchs, findCategoryListByLevel, getListCategoryByParentId ,getShopList} from '@/api/shop';
export default {
  name: 'Search',
  data() {
    return {
      form: {
        barcode: '', // 商品编码
        commonName: '', // 通用名称
        productName: '', // 商品名称
        showName: '', // 展示名称
        code: '', // 条码
        isFragileGoods: '', // 是否易碎品
        drugClassification: '', // 药品类别
        manufacturer: '', // 生产厂家
        isLimited: '', // 是否限购
        status: '', // 商品状态
        categoryFirstId: '', // 商品分类
        categorySecondId: '', // 二级分类
        categoryThirdId: '', // 三级分类
        agent: '', // 是否独家/////////
        isUsableMedical: '', // 是否医保
        highGross: '', // 是否高毛
        branchCode: 'XS000000', // 地域
        isPromotion: '', // 是否促销
        isControl: '', // 是否控销
        isGive: '', // 是否赠品
        shopCode: '' // 店铺id
      },
      categoryFirstIdList:[],
      categorySecondIdList:[],
      categoryThirdIdList:[],
      drugClassificationList: [
        { id: '', label: '全部' },
        { id: 1, label: '甲类 OTC' },
        { id: 2, label: '乙类 OTC' },
        { id: 3, label: '处方药' }
      ],
      statusList: [
        { id: '', label: '全部' },
        { id: 1, label: '销售中' },
        { id: 2, label: '已售罄' },
        { id: 4, label: '下架' },
        { id: 6, label: '待上架' },
        { id: 7, label: '已录入' },
        { id: 8, label: '待审核' }
      ],
      categoryList: [],
      branchCodeList: [],
      cascaderConfig: {
        label: 'name',
        value: 'id',
        children: 'children'
      },
      giveList: [
        { id: '', label: '全部' },
        { id: 1, label: '是' },
        { id: 0, label: '否' }
      ],
      shopList:[],
      searchShopListLoading:false,
      currentIndex: '',
    };
  },
  mounted() {
    this.getAllBranchs();
    this.findCategoryListByLevel(1);
    // this.findCategoryListByLevel(2);
    // // this.findCategoryListByLevel(3);
    this.getShopList(this.form.branchCode)
  },
  methods: {
    getAllBranchs() {
      getAllBranchs().then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.branchCodeList = res.data;
      });
    },
    handleSearch() {
      this.$emit('handleSearch', this.form);
    },
    findCategoryListByLevel(level){
      findCategoryListByLevel({level}).then(res=>{
          if(res.code!==1){
            this.$XyyMessage.warning(res.msg);
            return
          }
          switch(level){
            case 1:
              this.categoryFirstIdList=res.data
              break;
            case 2:
              this.categorySecondIdList=res.data;
              break;
            case 3:
              this.categoryThirdIdList=res.data;
              break;
          }

      })
    },
    handleFirstChange(val){
      this.form.categorySecondId="";
      this.form.categoryThirdId="";
      this.categoryThirdIdList.splice(0,this.categoryThirdIdList.length);
      if(!val){
        this.categorySecondIdList.splice(0,this.categorySecondIdList.length);
        this.categoryThirdIdList.splice(0,this.categoryThirdIdList.length);
        return ;
      }
      getListCategoryByParentId({id:val}).then(res=>{
        if(res.code!==1){
          this.$XyyMessage.warning(res.msg);
          return
        }
        this.categorySecondIdList=res.data
      })
    },
    handleSecondChange(val){
      this.form.categoryThirdId="";
      getListCategoryByParentId({id:val}).then(res=>{
        if(res.code!==1){
          this.$XyyMessage.warning(res.msg);
          return
        }
        this.categoryThirdIdList=res.data
      })
    },
    getListCategoryByParentId(id){
      return new Promise((resolve,reject)=>{
        getListCategoryByParentId({id}).then(res=>{
          if(res.code!==1){
            reject(res.msg)
          }
          resolve(res.data)
        })
      })
    },

    getShopList(val){
      this.form.shopCode='';
      this.shopList=[];

      //2020.4.1,rl,v1.7.4
      //选择全国,店铺ID必填
      if(val == 'XS000000'){

      }else{

        getShopList({branchCode:val}).then(res=>{
          if(res.code!==1){
            this.$XyyMessage.warning(res.msg);
            return
          }
          this.shopList=res.data.data
        })

      }
    },

    //店铺ID远程搜索
    searchShopList(val){
      this.shopList=[];
      if(!val){
        return false;
      }
      this.searchShopListLoading=true;
      getShopList({branchCode:this.form.branchCode,shopCode:val}).then(res=>{
        if(res.code!==1){
          this.$XyyMessage.warning(res.msg);
          return
        }
        this.shopList=res.data.data
      }).finally(()=>{
        this.searchShopListLoading=false;
      })
    },

    reset() {
      this.form = {
        barcode: '', // 商品编码
        commonName: '', // 通用名称
        productName: '', // 商品名称
        showName: '', // 展示名称
        code: '', // 条码
        isFragileGoods: '', // 是否易碎品
        drugClassification: '', // 药品类别
        manufacturer: '', // 生产厂家
        isLimited: '', // 是否限购
        status: '', // 商品状态
        categoryFirstId: '', // 商品分类
        categorySecondId: '', // 二级分类
        categoryThirdId: '', // 三级分类
        agent: '', // 是否独家/////////
        isUsableMedical: '', // 是否医保
        highGross: '', // 是否高毛
        branchCode: 'XS000000', // 地域
        isPromotion: '', // 是否促销
        isControl: '', // 是否控销
        isGive: '', // 是否赠品
        shopCode: '' // 店铺id
      };
      this.shopList=[];
      this.searchShopListLoading=false;
      this.$refs.form.resetFields();
    }
  }
};
</script>

<style scoped lang="scss">
  .shop-area{
    padding:10px;
    padding-top:20px;

  }
  /deep/.el-button.el-button--primary.is-plain {
    background-color: rgba(59, 149, 168, 1);
    color: #fff;
  }
  .userGroup {
    margin-bottom: 20px;
  }
  .label {
    display: inline-block;
    width: 56px;
    text-align: right;
    color: #292933;
    margin-right: 8px;
  }
  .sheetNumber {
    font-size: 14px;
    color: rgba(144, 147, 153, 1);
    padding-left: 20px;
    padding-bottom: 20px;
  }
  .assSubmit {
    padding: 0 20px 20px 0;
  }
  /deep/.el-dialog__header {
    padding: 0 20px;
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    font-weight: 600;
    color: rgba(48, 49, 51, 1);
    border-bottom: 1px solid rgba(238, 238, 238, 1);
  }
  /deep/.el-dialog__body {
    padding: 14px 0 0 0px;
  }
  /deep/.el-input {
    width: 230px;
  }
  /deep/.el-button {
    height: 36px;
  }

  /deep/label {
    font-weight: 400 !important;
  }
  .sheeTitle {
    color: #303133;
    font-size: 18px;
    font-weight: 600;
  }
  .searchCondition.is-plain {
    background: rgba(59, 149, 168, 1);
    color: #fff;
  }
  /deep/.searchCondition.is-plain:hover {
    background: rgba(40, 126, 144, 1);
  }
  .el-range-editor--small.el-input__inner {
    height: 36px;
  }
  .el-button {
    height: 36px;
  }
  .el-button--small {
    font-size: 14px;
  }
  .el-icon--right {
    margin-left: 0;
  }
  .input_serch {
    font-size: 14px;
    border-bottom: 1px dashed #e4e4eb;
  }

</style>
