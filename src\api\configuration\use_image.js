import request from '@/utils/request-im';

/**
 *常用图片列表
 * @param {*}
 */
export function pictureList (params) {
  return request({
    url: '/picture/list',
    method: 'get',
    params: params
  });
}

/**
 *添加图片(批量)
 * @param
 */
export function pictureAdds (param) {
  return request({
    url: '/picture/adds',
    method: 'post',
    data: param
  });
}

/**
 *删除图片
 * @param
 */
export function pictureDel (param) {
  return request({
    url: '/picture/delete/' + param.imgid,
    method: 'delete',
    data: param
  });
}
