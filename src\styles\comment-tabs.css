/* tabs页的样式 */
.tabs-box {
  padding: 20px;
}
.tabs-box-header {
  padding-bottom: 20px;
}
.tabs-box .tabs-box-header .el-button.is-plain {
  background: #fff;
}
.tabs-box .tabs-box-header .el-button.is-plain:hover {
  background: #3B95A8;
  border-color: #3B95A8;
  color: #FFFFFF;
}
/* 表格 title样式 */
.tabs-box-tables .el-table__header-wrapper .has-gutter th {
  background: #ddd;
}
.tabs-box-tables .tabs-box-tag-enable::before,
.tabs-box-tables .tabs-box-tag-disable:before {
  content: '';
  display: inline-block;
  width: 5px;
  height: 5px;
  margin-right: 10px;
  text-align: center;
  border-radius: 50%;
  background: green;
  vertical-align: middle;
}
.tabs-box-tables .tabs-box-tag-disable:before {
  background: red;
}
/* 列表中的启用禁用状态*/
.status-tip {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 2px 2px 0;
}
.status-tip.open {
   background: #67c23a;
 }
.status-tip.close {
   background: #ff3024;
 }
/* 列表中的优先级状态*/
.levelBlock {
  line-height: 24px;
  text-align: center;
  font-size: 14px;
  padding: 3px 8px;
border-radius:2px;
  box-sizing: border-box;
}
.levelBlock.urgent{
  background:rgba(255,222,220,1);
  color:#FF3024;
}
.levelBlock.pressing{
  background:rgba(252,229,212,1);
  color:#FB720C;
}
.levelBlock.plain{
  background:rgba(237,237,240,1);
  color:#909399;
}
