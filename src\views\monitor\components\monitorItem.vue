<template>
  <div class="list-content">
    <div
      v-for="(item, index) in list"
      v-show="showDo(item)"
      :key=" 'kefuId_' + index"
      class="monitor-content"
      @contextmenu.prevent="openMenu($event, item)"
      @click="sensitive(item)"
    >
      <div
        :class="(item.timeout || item.forbidden) ? 'busy' : status[item.state]"
        class="title"
      >{{ item.username }}</div>
      <div class="content">
        <div>
          <i class="count"></i>
          <svg-icon class="iconsvg" icon-class="monitor_count"></svg-icon>
          {{ item.currentdialog }}次
        </div>
        <div>
          <svg-icon class="iconsvg" icon-class="monitor_time"></svg-icon>
          {{ item.lasttime }}
        </div>
        <div>
          <svg-icon class="iconsvg" icon-class="monitor_online"></svg-icon>
          {{ item.state }}
        </div>
        <div v-if="item.forbidden" class="warning-content">
          <svg-icon class="iconsvg" icon-class="monitor_warning"></svg-icon>敏感词
        </div>
        <div v-if="item.timeout" class="warning-content">
          <svg-icon class="iconsvg" icon-class="monitor_warning"></svg-icon>会话超时
        </div>
      </div>
    </div>
    <ul
      v-if="right"
      v-show="visible"
      :style="{ left: left + 'px', top: top + 'px' }"
      class="contextmenu"
      @mouseleave="closeMenu"
    >
      <li @click="goPage">对话监控</li>
      <li @click="changeState($event, 3)">强制置忙</li>
      <li @click="changeState($event, 1)">强制置闲</li>
      <li @click="changeState($event, 5)">强制离线</li>
    </ul>
  </div>
</template>

<script>
import { changeState } from '@/api/monitor/index.js';
export default {
  name: 'MonitorItem',
  props: {
    id: {
      type: Number,
      default: null
    },
    right: {
      type: Boolean,
      default: true
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      status: {
        在线: 'online',
        忙碌: 'busy',
        培训: 'train',
        会议: 'meeting',
        就餐: 'eating',
        小休: 'brack',
        离线: 'offline'
      },
      top: 0,
      left: 0,
      currentItem: {}
    };
  },
  watch: {
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu);
      } else {
        document.body.removeEventListener('click', this.closeMenu);
      }
    }
  },
  mounted() {},
  methods: {
    // 显示的条件
    showDo(obj) {
      if (obj.state !== '退出') {
        if (obj.forbidden === undefined && obj.timeout === undefined) {
          return true;
        } else {
          if (obj.forbidden || obj.timeout) {
            return true;
          } else {
            return false;
          }
        }
      } else {
        return false;
      }
    },
    openMenu(e, item) {
      this.left = e.clientX - 240;
      this.top = e.offsetY;
      this.visible = true;
      this.currentItem = item;
    },
    goPage() {
      // console.log(this.id, this.currentItem.kefuId);
      // return false;
      const { kefuId } = this.currentItem;
      this.$router.push({
        path: '/whole/errorchat',
        query: {
          id: this.id,
          kefuId
        }
      });
    },
    closeMenu() {
      this.visible = false;
    },
    changeState(e, state) {
      const { kefuId } = this.currentItem;
      changeState({ state, kefuId, changeReason: 2 }).then(res => {
        if (res.code !== 0) {
          this.$emit('eat');
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    sensitive(v) {
      if (v.dialogid) {
        this.$router.push({
          path: '/whole/errorchat',
          query: v
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.list-content {
  position: relative;
  .monitor-content {
    width: 155px;
    font-weight: 500;
    margin-right: 15px;
    margin-bottom: 15px;
    display: inline-block;
    .title {
      height: 44px;
      line-height: 44px;
      color: #ffffff;
      padding-left: 10px;
      background: rgba(59, 149, 168, 1);
      border-radius: 2px 2px 0px 0px;
    }

    .online {
      background: #49ba6b !important;
    }
    .busy {
      background: #ff5724 !important;
    }
    .train {
      background: #00a1ff !important;
    }
    .meeting {
      background: #6098ff !important;
    }
    .eating {
      background: #ffa631 !important;
    }
    .brack {
      background: #9762eb !important;
    }
    .offline {
      background: #afafbf !important;
    }
    .content {
      border: 1px solid rgba(228, 228, 235, 1);
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(87, 87, 102, 1);
      line-height: 20px;
      padding: 8px;
      padding-bottom: 2px;
      border-radius: 0 0 2px 2px;
      & > div {
        margin-bottom: 10px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .warning-content {
        color: #fa5555;
      }
      .iconsvg {
        width: 18px;
        height: 18px;
        display: inline-block;
        vertical-align: bottom;
        margin-right: 8px;
      }
      .time {
        width: 18px;
        height: 18px;
        display: inline-block;
        vertical-align: bottom;
        margin-right: 8px;
        background: url('../../../assets/monitor/duration.png') 0 0 no-repeat;
      }
      .status {
        width: 18px;
        height: 18px;
        display: inline-block;
        vertical-align: bottom;
        margin-right: 8px;
        background: url('../../../assets/monitor/status.png') 0 0 no-repeat;
      }
      .warning {
        width: 18px;
        height: 18px;
        display: inline-block;
        vertical-align: bottom;
        margin-right: 8px;
        background: url('../../../assets/monitor/warning.png') 0 0 no-repeat;
      }
    }
  }
  .contextmenu {
    position: absolute;
    width: 110px;
    margin: 0;
    padding: 15px 0;
    list-style-type: none;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(240, 242, 245, 1);
    border-radius: 2px;
    li {
      margin: 0;
      padding: 0;
      list-style-type: none;
      text-align: center;
      cursor: pointer;
      margin-bottom: 10px;
      color: #292933;
    }
    li:last-child {
      margin-bottom: 0;
    }
    li:hover {
      color: #3b95a8;
    }
  }
}
</style>
