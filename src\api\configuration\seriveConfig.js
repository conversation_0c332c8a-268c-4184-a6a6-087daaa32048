import request from '@/utils/request-im';

/**
 *查询模板数据
 * @param {Object}查询参数
 */
export function getTemplate() {
  return request({
    url: '/serviceSummary/selectFieldByFromId?fromId=1',
    method: 'get'
  });
}

/**
 *保存模板
 * @param {Object}查询参数
 */
export function saveTemplate(params) {
  return request({
    url: '/serviceSummary/imFormFileSaveOrUpdate',
    method: 'post',
    data: params
  });
}

