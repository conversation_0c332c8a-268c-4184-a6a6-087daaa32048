<template>
  <div class="template-config">
    <steps :active="2"></steps>
    <div class="field-auth">
      <el-row class="title">
        <el-col :span="8">字段名称</el-col>
        <el-col :span="4">隐藏</el-col>
        <el-col :span="4">只读</el-col>
        <el-col :span="4">编辑</el-col>
        <el-col :span="4">
          <description :info="info"></description>
        </el-col>
      </el-row>
      <el-row v-for="(item, index) in fields" :key="item.fieldCode">
        <el-col :span="8">
          <span>{{ item. fieldName }}</span>
          <span class="field-type">{{ item.fieldType | fieldTypeFormat }}</span>
        </el-col>
        <el-col :span="4">
          <el-radio
            :disabled="!item.systemFieldFlag"
            v-model="item.authType"
            :label="0"
            @change="handleChange($event,item.fieldCode)"
          >&nbsp;</el-radio>
        </el-col>
        <el-col :span="4">
          <el-radio
            v-model="item.authType"
            :label="1"
            @change="handleChange($event,item.fieldCode)"
          >&nbsp;</el-radio>
        </el-col>
        <el-col :span="4">
          <el-radio
            v-model="item.authType"
            :label="2"
            @change="handleChange($event,item.fieldCode)"
          >&nbsp;</el-radio>
          <el-checkbox
            :disabled="item.authType !== 2"
            v-model="item.required"
            :true-label="1"
            :false-label="0"
            @change="handleChange($event,item.fieldCode)"
          >必填项</el-checkbox>
        </el-col>
        <el-col :span="4">
          <el-button type="text" class="right" @click="openConditionDialog(item)">设置条件</el-button>
        </el-col>
        <el-col
          v-if="item.conditionList && item.conditionList.length"
          :span="24"
          class="condition-display"
        >
          <span>条件：{{ formatCondition(item.conditionList,item.authTypeCondition,item.requiredCondition).condition }}</span>
          <span>触发条件后状态：{{ formatCondition(item.conditionList,item.authTypeCondition,item.requiredCondition).status }}</span>
          <el-button type="text" @click="delNodeCondition(index)">删除联动</el-button>
        </el-col>
      </el-row>
      <condition
        :status.sync="conditionOpen"
        :fields="conditionFields"
        :data="curField"
        @callback="setCondition"
      ></condition>
    </div>

    <div class="btn-container">
      <el-button @click="checkTimer(prevStep,'timer1')()">上一步</el-button>
      <el-button type="primary" @click="checkTimer(nextStep,'timer2')()">保存</el-button>
    </div>
  </div>
</template>

<script>
import {
  FIELD_TYPES,
  getTemplateData,
  getAuthFieldDatas,
  saveAuthFieldDatas,
  updateAuthFieldDatas
} from '@/api/templates';
import condition from './components/condition';
import utils from '@/utils/form';
import steps from './components/steps'; // 导航
import description from '@/components/tools/description.vue';
export default {
  components: {
    condition,
    steps,
    description
  },
  filters: {
    fieldTypeFormat: val =>
      FIELD_TYPES.filter(el => el.value === val).length
        ? FIELD_TYPES.filter(el => el.value === val)[0].label
        : ''
  },
  data() {
    return {
      info: [
        {
          title: '字段',
          info:
            '如是发起类型的模板，则显示系统字段和引入的自定义字段；如是处理或关闭类型的模板，则只显示引入的自定义字段'
        },
        {
          title: '状态',
          info:
            '即字段的初始状态，分为隐藏、只读、编辑（必填/非必填），系统字段不可隐藏'
        },
        {
          title: '设置条件',
          info: '可设置联动效果，即触发条件后的字段状态，以及触发条件'
        }
      ],
      templateId: '',
      templateType: 0,
      isUpdate: false,
      fields: [],
      curField: null,
      conditionOpen: false, // 条件弹框
      timer1: null,
      timer2: null,
      symbolList: utils.conditionSymbolList,
      editing: [],
      delFields: [],
      oldDatas: [] // 更新前数据
    };
  },
  computed: {
    conditionFields() {
      return this.fields.filter(
        el =>
          [0, 1, 2, 3, 4, 6].includes(el.fieldType) &&
          el.fieldCode !== 's1164724692221825024'
      );
    }
  },
  activated() {
    this.templateId = this.$route.query.templateId;
    this.templateType = Number(this.$route.query.templateType);
    this.isUpdate = this.$route.query.isUpdate
      ? this.$route.query.isUpdate
      : false;
    this.getFields();
    if (this.isUpdate) {
      this.$store.dispatch('tagsView/updateVisitedView', {
        ...this.$route,
        meta: {
          title: '编辑模板'
        }
      });
    }
    this.editing = [];
    this.delFields = [];
  },
  methods: {
    getFields() {
      if (!this.isUpdate) {
        getTemplateData({
          templateId: this.templateId,
          needSystemField: !this.templateType
        }).then(res => {
          if (res.code === 1) {
            this.fields = res.data.list.map(el => {
              el.authType = 2;
              el.required = 0;
              el.authTypeCondition = 2;
              el.requiredCondition = 0;
              el.ganged = 0;
              return el;
            });
          } else {
            this.$XyyMessage.error(res.msg);
          }
        });
      } else {
        getAuthFieldDatas({
          templateId: this.templateId
        }).then(res => {
          if (res.code === 1) {
            this.fields = res.data;
            if (!res.data.length) {
              this.isUpdate = false;
              getTemplateData({
                templateId: this.templateId,
                needSystemField: !this.templateType
              }).then(res => {
                if (res.code === 1) {
                  this.fields = res.data.list.map(el => {
                    el.authType = 2;
                    el.required = 0;
                    el.authTypeCondition = 2;
                    el.requiredCondition = 0;
                    el.ganged = 0;
                    return el;
                  });
                } else {
                  this.$XyyMessage.error(res.msg);
                }
              });
            } else {
              this.oldDatas = JSON.parse(JSON.stringify(res.data));
            }
          } else {
            this.$XyyMessage.error(res.msg);
          }
        });
      }
    },

    /**
     * 格式化 条件数据
     */
    formatCondition(_datas, authTypeCondition, requiredCondition) {
      const datas = JSON.parse(JSON.stringify(_datas));
      const el = datas[0];
      const name = this.getConditionName(el.conditionFieldCode);
      const symbol = this.getSymbolName(el.conditionSymbol);
      const value = this.getValueName(
        [0, 1].includes(el.fieldType) ? '' : el.optionSettings,
        el.fieldType,
        Number(el.conditionSymbol),
        el.valueFirst instanceof Array ? el.valueFirst.join() : el.valueFirst
      );
      const condition = `${name} ${symbol} ${value}`;
      const status = this.getStatusName(authTypeCondition);
      const required = requiredCondition ? '必填项' : '';
      return {
        condition,
        status: required ? `${status} ${required}` : status
      };
    },
    /**
     * 获取状态名称
     */
    getStatusName(val) {
      return val === 0 ? '隐藏' : val === 1 ? '只读' : val === 2 ? '编辑' : '';
    },
    /**
     * 获取字段名称
     */
    getConditionName(code) {
      return this.fields.length &&
        this.fields.filter(el => el.fieldCode === code).length
        ? this.fields.filter(el => el.fieldCode === code)[0].fieldText
        : '';
    },
    /**
     * 获取条件判断方式名称
     */
    getSymbolName(value) {
      return value || value === 0
        ? this.symbolList.filter(el => el.value === value)[0].label
        : '';
    },
    /**
     * 获取条件值名称
     */
    getValueName(options, type, symbol, vals) {
      let name = '';
      if ([2, 3, 4, 6].includes(type)) {
        if (options && typeof options === 'string') {
          options = JSON.parse(options.replace(/&quot;/g, '"'));
        }
      }
      if (type === 2 || type === 3) {
        const key = type === 2 ? 'selectOptions' : 'radioOptions';
        if (symbol === 3) {
          const arr = [];
          vals =
            vals || vals === 0 ? vals.split(',').map(el => Number(el)) : [];
          options[key].optionsArray.forEach((el, i) => {
            if (el.val && vals.indexOf(el.val) > -1) {
              arr.push(el.optionsDesc);
            } else if (vals.indexOf(i) > -1) {
              arr.push(el.optionsDesc);
            }
          });
          name = arr.join();
        } else {
          vals = vals || vals === 0 ? Number(vals) : '';
          const arr = options[key].optionsArray.filter((el, i) =>
            el.val ? el.val === vals : i === vals
          );
          name = arr.length ? arr[0].optionsDesc : '';
        }
      } else if (type === 4) {
        const arr = [];
        vals = vals || vals === 0 ? vals.split(',').map(el => Number(el)) : [];
        options.checkedOptions.optionsArray.forEach((el, i) => {
          if (el.val && vals.indexOf(el.val) > -1) {
            arr.push(el.optionsDesc);
          } else if (vals.indexOf(i) > -1) {
            arr.push(el.optionsDesc);
          }
        });
        name = arr.join();
      } else if (type === 6) {
        if (options.treeOptions.optionsLabel) {
          name = options.treeOptions.optionsLabel.join('>');
        } else {
          name = this.getTreeLabel(
            options.treeOptions.optionsArray,
            vals || vals === 0 ? vals.split(',') : [],
            []
          ).join('>');
        }
      } else {
        name = vals;
      }
      return name;
    },
    /**
     * 获取 级联label
     */
    getTreeLabel(tree, vals, labels) {
      if (!vals.length) {
        return labels;
      }
      const _vals = [].concat(vals);
      const len = labels.length;
      for (let i = 0; i < _vals.length; i++) {
        const val = _vals[i];
        for (const el of tree) {
          if (el.value === Number(val)) {
            labels.push(el.label);
            const arr = [].concat(_vals);
            arr.splice(i, 1);
            labels = this.getTreeLabel(
              el.children && el.children.length ? el.children : [],
              arr,
              labels
            );
            break;
          }
        }
        if (labels.length > len) {
          break;
        }
      }
      return labels;
    },
    /**
     * 删除当前触发条件
     */
    delNodeCondition(index) {
      this.$XyyMsg({
        title: '提示',
        content: '确定删除联动吗?',
        onSuccess: () => {
          const id = this.fields[index].conditionList[0].id;
          this.fields[index].conditionList = null;
          this.fields[index].ganged = 0;
          this.fields[index].authTypeCondition = this.fields[index].authType;
          this.fields[index].requiredCondition = this.fields[index].required;
          const field = JSON.parse(JSON.stringify(this.fields[index]));
          if (id) {
            field.conditionList = [{ id }];
            this.delFields.push(field);
          }
        }
      });
    },
    // 打开设置字段条件弹窗
    openConditionDialog(_data) {
      this.conditionOpen = true;
      const data = JSON.parse(JSON.stringify(_data));
      if (data.conditionList && data.conditionList.length) {
        data.conditionList[0].requiredCondition = data.requiredCondition;
        data.conditionList[0].authTypeCondition = data.authTypeCondition;
        if (
          data.conditionList[0].optionSettings &&
          typeof data.conditionList[0].optionSettings === 'string'
        ) {
          data.conditionList[0].optionSettings = JSON.parse(
            data.conditionList[0].optionSettings.replace(/&quot;/g, '"')
          );
        }
      } else {
        data.conditionList = [
          {
            fieldType: '',
            optionSettings: null,
            conditionFieldCode: '',
            conditionSymbol: '',
            valueFirst: '',
            requiredCondition: 0,
            authTypeCondition: 2
          }
        ];
      }
      this.curField = data;
    },
    setCondition(data) {
      const field = this.fields.filter(
        el => el.fieldCode === this.curField.fieldCode,
        this
      )[0];
      field.conditionList = [
        {
          id:
            this.isUpdate && field.conditionList && field.conditionList.length
              ? field.conditionList[0].id
              : null,
          fieldType: data.fieldType,
          optionSettings: JSON.stringify(data.optionSettings),
          conditionFieldCode: data.conditionFieldCode,
          conditionSymbol: data.conditionSymbol,
          valueFirst: data.valueFirst
        }
      ];
      field.authTypeCondition = data.authTypeCondition;
      field.requiredCondition = data.requiredCondition;
      field.ganged = 1;
      this.editing.push(this.curField.fieldCode);
    },
    /**
     * 保存数据 并跳转到编辑模板
     */
    prevStep() {
      const old = this.$store.state.tagsView.visitedViews.filter(
        el => el.path === this.$route.path
      )[0];
      const path = `/worksheet/templateEditor/${this.templateId}`;
      const view = this.$router.options.routes
        .filter(el => el.name === 'worksheet')[0]
        .children.filter(el => el.name === 'templateEditor')[0];
      const query = {
        templateId: this.templateId,
        templateType: this.templateType,
        isUpdate: true
      };
      if (this.$route.query.copy) {
        query.copy = true;
      }
      this.saveData(() => {
        this.updateTab(
          {
            ...view,
            path,
            query,
            fullPath: this.getFullPath(path, query),
            meta: {
              title: '编辑模板'
            }
          },
          old
        );
      });
    },
    updateTab(view, old) {
      this.$store
        .dispatch('tagsView/updateCurrentView', {
          old: old,
          view
        })
        .then(() => {
          const { fullPath } = view;
          this.$nextTick(() => {
            this.$router.replace({
              path: fullPath
            });
          });
        });
    },
    getFullPath(path, query) {
      const params = [];
      for (const key in query) {
        params.push(key + '=' + query[key]);
      }
      return path + '?' + params.join('&');
    },
    saveData(callback) {
      // 保存 重新排序
      if (!this.isUpdate) {
        this.fields.forEach((el, i) => {
          el.sort = i + 1;
        });
      }
      let list = this.isUpdate ? this.getFieldDatas() : this.fields;
      list = list.map((el, i) => {
        const obj = {
          id: el.id,
          templateId: this.templateId,
          fieldCode: el.fieldCode,
          authType: el.authType,
          authTypeCondition: el.authTypeCondition,
          required: el.required,
          requiredCondition: el.requiredCondition,
          ganged: el.ganged,
          sort: el.sort
        };
        if (el.conditionList && el.conditionList.length) {
          obj.conditionList = el.conditionList.map(item => {
            return {
              id: item.id ? item.id : null,
              authId: el.id,
              conditionFieldCode: item.conditionFieldCode,
              conditionSymbol: item.conditionSymbol,
              valueFirst: item.valueFirst,
              fieldType: item.fieldType,
              optionSettings: item.optionSettings
            };
          });
        }
        return obj;
      }, this);
      // 追加删除条件的数据
      if (this.isUpdate && this.delFields.length) {
        const arr = this.delFields.map((el, i) => {
          return {
            id: el.id,
            templateId: this.templateId,
            fieldCode: el.fieldCode,
            authType: el.authType,
            authTypeCondition: el.authTypeCondition,
            required: el.required,
            requiredCondition: el.requiredCondition,
            ganged: el.ganged,
            sort: el.sort,
            conditionList: el.conditionList
          };
        });
        list = list.concat(arr);
      }
      let loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      if (!list.length) {
        if (callback) {
          loading.close();
          callback();
        }
      } else {
        if (this.isUpdate) {
          updateAuthFieldDatas(list, false, !!this.$route.query.copy).then(
            res => {
              loading.close();
              if (res.code === 1) {
                if (callback) {
                  this.$XyyMessage.success('模板修改成功');
                  callback();
                }
              } else if (res.code === 2) {
                this.$XyyMsg({
                  title: '提示',
                  content: res.msg,
                  onSuccess: () => {
                    loading = this.$loading({
                      lock: true,
                      text: 'Loading',
                      spinner: 'el-icon-loading',
                      background: 'rgba(255,255,255, 0.8)'
                    });
                    updateAuthFieldDatas(
                      list,
                      true,
                      !!this.$route.query.copy
                    ).then(res => {
                      loading.close();
                      if (res.code === 1) {
                        if (callback) {
                          this.$XyyMessage.success('模板修改成功');
                          callback();
                        }
                      } else {
                        this.$XyyMessage.error(res.msg);
                      }
                    });
                  }
                });
              } else {
                this.$XyyMessage.error(res.msg);
              }
            }
          );
        } else {
          saveAuthFieldDatas(list).then(res => {
            loading.close();
            if (res.code === 1) {
              if (callback) {
                this.$XyyMessage.success('模板保存成功');
                callback();
              }
            } else {
              this.$XyyMessage.error(res.msg);
            }
          });
        }
      }
    },
    /**
     * 获取已更新字段
     */
    getFieldDatas() {
      const _datas = this.oldDatas.filter(
        el => this.editing.includes(el.fieldCode),
        this
      );
      let datas = this.fields.filter(
        el => this.editing.includes(el.fieldCode),
        this
      );

      datas = datas.filter(el => {
        return _datas.some(_el => {
          if (_el.id === el.id) {
            if (
              _el.authType !== el.authType ||
              _el.required !== el.required ||
              _el.authTypeCondition !== el.authTypeCondition ||
              _el.requiredCondition !== el.requiredCondition ||
              this.compareCondition(_el.conditionList, el.conditionList)
            ) {
              return true;
            }
          } else {
            return false;
          }
        }, this);
      }, this);
      return datas;
    },
    compareCondition(oldCondition, newCondition) {
      if (
        newCondition &&
        newCondition.length &&
        (!oldCondition || !oldCondition.length)
      ) {
        return true;
      } else if (
        oldCondition &&
        oldCondition.length &&
        (!newCondition || !newCondition.length)
      ) {
        return true;
      } else if (
        newCondition &&
        newCondition.length &&
        oldCondition &&
        oldCondition.length
      ) {
        if (!newCondition[0].id) {
          return true;
        } else if (newCondition[0].id === oldCondition[0].id) {
          return (
            newCondition[0].conditionFieldCode !==
              oldCondition[0].conditionFieldCode ||
            newCondition[0].conditionSymbol !==
              oldCondition[0].conditionSymbol ||
            newCondition[0].valueFirst !== oldCondition[0].valueFirst
          );
        }
      } else {
        return false;
      }
    },
    /**
     * 保存数据 并跳转到列表
     */
    nextStep() {
      this.saveData(() => {
        this.$store.dispatch('tagsView/delView', this.$route);
        this.$router.replace({
          name: 'templateList'
        });
      });
    },
    /**
     * 权限改变触发
     */
    handleChange(val, fieldCode) {
      this.editing.push(fieldCode);
    }
  }
};
</script>

<style lang="scss" scoped>
.template-config {
  box-sizing: border-box;
  padding: 15px 20px 56px;
  .field-auth {
    width: 850px;
    margin: 18px auto 0;
    background: rgba(245, 247, 250, 1);
    border-radius: 2px;
    border: 1px solid rgba(228, 228, 235, 1);
    border-bottom: none;
    .title {
      .el-col:last-child {
        text-align: right;
      }
    }
    .el-row {
      font-family: PingFangSC-Medium;
      font-weight: normal;
      padding: 0 20px;
      line-height: 40px;
      border-bottom: 1px solid #e4e4eb;
      font-size: 14px;
      &:first-child {
        color: rgba(41, 41, 51, 1);
        background: rgba(240, 242, 245, 1);
        line-height: 45px;
      }
      .condition-display {
        height: 30px;
        line-height: 30px;
        background: rgba(235, 235, 239, 1);
        margin-bottom: 12px;
        box-sizing: border-box;
        padding: 0 12px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFangSC;
        font-weight: 400;
        color: rgba(144, 147, 153, 1);
        span {
          float: left;
          width: 30%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        span:first-child {
          margin-right: 50px;
        }
        .el-button {
          font-size: 12px;
          height: 100%;
          font-family: PingFangSC-Regular, PingFangSC;
          font-weight: 400;
          color: rgba(255, 156, 8, 1);
          float: right;
          cursor: pointer;
          padding: 0;
          &.is-disabled {
            color: #c0c4cc;
          }
        }
      }
      span.field-type {
        font-size: 12px;
        color: #909399;
      }
      .el-button.right {
        float: right;
      }
    }
  }
  div.btn-container {
    position: fixed;
    bottom: 0;
    width: calc(100% - 211px);
    box-sizing: border-box;
    left: 211px;
    height: 56px;
    background: #fff;
    box-shadow: 0px -2px 2px 0px rgba(145, 144, 144, 0.11);
    margin: 0;
    text-align: right;
    button {
      height: 36px;
      margin: 10px 20px 10px 0;
    }
    button:first-child {
      margin: 10px 10px 10px 0;
    }
  }
}
</style>
