<template>
  <xyy-list-page class="seachim">
    <template slot="header">
      <div class="im-seach">
        <el-form ref="searchForm" :model="search" class="search-box">
          <el-row type="flex" class="innerEl" justify="space-between" align="middle">
            <el-form-item class label="时间" label-width="80px" prop="dateRange">
              <el-date-picker
                v-model="search.dateRange"
                :start-placeholder="searchstartTime"
                :end-placeholder="searchendTime"
                :default-time="['00:00:00', '23:59:59']"
                type="datetimerange"
                class="timeSel"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="关闭方式" label-width="80px" prop="state">
              <el-select v-model="search.state" clearable placeholder="请选择">
                <el-option label="系统24小时自动关闭" value="1"></el-option>
                <el-option label="访客超时" value="2"></el-option>
                <el-option label="访客关闭" value="3"></el-option>
                <el-option label="客服关闭" value="4"></el-option>
                <el-option label="转入待跟进" value="5"></el-option>
                <el-option label="强制转接" value="6"></el-option>
                <el-option label="被接受转接" value="7"></el-option>
                <el-option label="客服超时" value="8"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="满意度" label-width="80px" prop="degree">
              <el-select v-model="search.degree" clearable placeholder="请选择活动区域">
                <el-option label="非常满意" value="1"></el-option>
                <el-option label="满意" value="2"></el-option>
                <el-option label="一般" value="3"></el-option>
                <el-option label="不满意" value="4"></el-option>
                <el-option label="非常不满意" value="5"></el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row type="flex" class="innerEl top_20" justify="space-between" align="middle">
            <el-row type="flex" justify="start" align="middle" class="seachItme">
              <el-form-item class="timeLoneIte" label="时长查询" label-width="80px" prop="startTime">
                <el-input
                  v-model.number="search.startTime"
                  class="timeLone"
                  size="small"
                  placeholder="请输入"
                  type="number"
                />
                <span class="line">-</span>
                <el-input
                  v-model.number="search.endTime"
                  class="timeLone"
                  size="small"
                  placeholder="请输入"
                  type="number"
                />
              </el-form-item>
              <el-form-item label="客服姓名" label-width="80px" prop="serveName">
                <el-input
                  v-model="search.serveName"
                  class="serverName"
                  size="small"
                  placeholder="请输入内容"
                />
              </el-form-item>
            </el-row>
            <el-form-item label="客户名称" label-width="80px" prop="customerName">
              <el-input v-model="search.customerName" size="small" placeholder="请输入内容" />
            </el-form-item>
            <el-form-item label="药店编号" label-width="80px" prop="serveId">
              <el-input v-model="search.serveId" size="small" placeholder="请输入内容" />
            </el-form-item>
          </el-row>
          <el-row type="flex" class="innerEl top_20" justify="space-between" align="middle">
            <el-row type="flex" justify="start" align="middle">
              <el-form-item class="serverNum" label="客服账号" label-width="80px" prop="serveNum">
                <el-input
                  v-model="search.serveNum"
                  class="serverName"
                  size="small"
                  placeholder="请输入内容"
                />
              </el-form-item>
              <el-form-item label="客服昵称" label-width="80px" prop="serveNick">
                <el-input
                  v-model="search.serveNick"
                  class="serverName"
                  size="small"
                  placeholder="请输入内容"
                />
              </el-form-item>
            </el-row>
            <el-row type="flex" justify="end" align="middle">
              <el-checkbox v-model="isSelf" class="isSelf">自己的对话记录</el-checkbox>
              <el-form-item>
                <el-button
                  plain
                  type="primary"
                  size="medium"
                  class="searchCondition"
                  @click="handleCondition(listQuery,true)"
                >查询</el-button>
                <el-button plain size="medium" @click="resetForm('searchForm')">重置</el-button>
              </el-form-item>
            </el-row>
          </el-row>

          <!--聊天记录 -->
          <el-row type="flex" class="innerEl top_20" justify="space-between" align="middle">
            <el-row type="flex" justify="start" align="middle">
              <el-form-item
                class="chattingRecordsIte"
                label="聊天记录"
                label-width="80px"
                prop="chattingRecords"
              >
                <el-input
                  v-model="search.chattingRecords"
                  class="chattingRecords"
                  size="small"
                  placeholder="请输入内容"
                />
              </el-form-item>
            </el-row>
          </el-row>

          <el-row class="line-border"></el-row>
        </el-form>
        <el-button icon="el-icon-upload2" class="excelBtn" @click="exportExcel">导出Excel</el-button>
      </div>
    </template>

    <template slot="body">
      <el-row type="flex" justify="space-between" align="top">
        <xyy-table
          :data="list"
          :list-query="listQuery"
          :col="col"
          :highlight_current_row="getDataList.length?true:false"
          @get-data="getList"
          @handleCellClick="handleCellClick"
        >
          <template slot="createtime" slot-scope="{col}">
            <el-table-column
              :key="col.index"
              :prop="col.index"
              :label="col.name"
              :width="col.width"
            >
              <template slot-scope="item">
                <span>{{ item.row['createtime'] }}~{{ item.row['endtime'] }}</span>
              </template>
            </el-table-column>
          </template>
        </xyy-table>
        <div class="dialogue">
          <el-row class="header" type="flex" justify="end" align="middle">
            <!--  -->
            <el-button
              type="text"
              icon="el-icon-upload2"
              class="AloneExport"
              @click="AloneExport"
            >单独导出</el-button>
          </el-row>
          <div class="content">
            <chatRecordChild :get-data-list="getDataList"></chatRecordChild>
          </div>
        </div>
      </el-row>
    </template>
  </xyy-list-page>
</template>

<script>
import utils from '@/utils/filter';
import { dialogsSearch } from '@/api/im_view/serviceConfigForm';
import { getConversation } from '@/api/configuration/RegionalMenu';
import { getConversationMessages } from '@/api/configuration/RegionalMenu';
import chatRecordChild from '@/components/im/chatRecord';
export default {
  name: 'compIMSearchList',
  components: {
    chatRecordChild,
  },
  filters: {
    dateFormat(val) {
      return utils.dataTime(val, 'yy-mm-dd HH:ss:nn');
    },
  },
  data() {
    return {
      search: {
        dateRange: '',
        state: '',
        degree: '',
        startTime: '',
        endTime: '',
        serveName: '',
        customerName: '',
        serveId: '',
        serveNum: '',
        serveNick: '',
        chattingRecords: '',
      },
      getDataList: [],
      isSelf: false,
      list: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 10,
      },
      col: [
        { index: 'createtime', name: '时间', width: 368, slot: true },
        { index: 'timeused', name: '持续时长', width: 150 },
        { index: 'userName', name: '客服账号', width: 150 },
        { index: 'name', name: '客服名称', width: 200 },
      ],
      rowId: '',
      kefuName: '',
      searchstartTime: '',
      searchendTime: '',
      // operation: [
      //   {
      //     name: '编辑',
      //     type: 1
      //   },
      //   {
      //     name: '删除',
      //     type: 2
      //   }
      // ]
    };
  },
  mounted() {
    this.initializeTime();
    this.$nextTick(() => {
      this.getList();
    });
  },
  methods: {
    handleCellClick(row, column, cell, event) {
      // row.id
      const that = this;
      this.rowId = row.id; // row.id
      console.log(row);

      getConversationMessages(row.id)
        .then((response) => {
          console.log('response.data:', response.data);
          this.getDataList = response.data;
          that.kefuname = response.data[0].kefuName;
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    handleCondition(listQuery, reset) {
      this.getList(listQuery, reset);
    },
    resetForm(formName) {
      this.search.endTime = '';
      this.$refs[formName].resetFields();
      this.initializeTime();
      this.getDataList = [];
    },
    getList: function (listQuery, reset) {
      const that = this;
      const { page, pageSize } = listQuery || this.listQuery;

      const dateRange = that.search.dateRange;
      if (!dateRange) {
        that.$XyyMessage.warning('请选择时间');
      }
      const createTimeStart =
        dateRange && dateRange.length
          ? this.getFormatDate('', '', dateRange[0].getTime())
          : '';
      const createTimeEnd =
        dateRange && dateRange.length
          ? this.getFormatDate('', '', dateRange[1].getTime())
          : '';

      const param = {
        cid: that.search.serveId,
        closer: that.search.state === '' ? 0 : that.search.state,
        endTime: createTimeEnd,
        estimate: that.search.degree === '' ? 0 : that.search.degree,
        customName: that.search.customerName,
        keyword: '',
        maxtime: that.search.endTime,
        mintime: that.search.startTime,
        myBox: that.isSelf ? 1 : 0,
        name: that.search.serveName,
        nickName: that.search.serveNick,
        // page: page,
        page: reset ? 1 : page,
        pageSize: pageSize,
        startTime: createTimeStart,
        userName: that.search.serveNum,
        contentWord: that.search.chattingRecords,
      };
      if (reset) {
        this.getDataList = [];
      }

      dialogsSearch(param)
        .then((res) => {
          console.log('res555:', res);
          that.list = res.data.datas;
          const { curPage, pageSize, total } = res.data;
          this.listQuery = {
            page: Number(curPage),
            pageSize: Number(pageSize),
            total: Number(total),
          };
        })
        .catch(() => {});
    },
    operationClick: function (type, row) {
      // switch (type) {
      //   case 0:
      //     this.toEdit(row);
      //     break;
      //   case 1:
      //     this.$XyyMsg({
      //       title: '提示',
      //       content: '确定要删除模板吗?',
      //       onSuccess: () => {
      //         this.delData(row);
      //       }
      //     });
      //     break;
      // }
    },
    AloneExport() {
      // 导出列表
      const that = this;
      if (!that.getDataList || !that.getDataList.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！',
        });
        return false;
      }
      let preParam = {
        did: this.rowId, // 对话id
        kefuname: this.kefuName,
      };

      //添加业务线code参数
      if (
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
      ) {
        preParam = Object.assign({}, preParam, {
          businessPartCode: this.$store.getters.channel.businessPartCode,
        });
      }

      // console.log('preParam:', preParam);
      function httpPost(URL, PARAMS) {
        var temp = document.createElement('form');
        temp.action = URL;
        temp.method = 'get';
        temp.style.display = 'none';

        for (var x in PARAMS) {
          var opt = document.createElement('textarea');
          opt.name = x;
          opt.value = PARAMS[x];
          temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();
      }
      httpPost(`${process.env.BASE_API_IM}/dialogs/search/expmsg`, preParam);
    },
    exportExcel() {
      // 导出列表
      const that = this;
      if (!that.list || !that.list.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！',
        });
        return false;
      }
      const dateRange = that.search.dateRange;
      const createTimeStart =
        dateRange && dateRange.length
          ? this.getFormatDate('', '', dateRange[0].getTime())
          : '';
      const createTimeEnd =
        dateRange && dateRange.length
          ? this.getFormatDate('', '', dateRange[1].getTime())
          : '';
      const businessPart =
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
          ? this.$store.getters.channel.businessPartCode
          : '';

      const preParam = {
        cid: that.search.serveId,
        closer: that.search.state === '' ? 0 : that.search.state,
        endTime: createTimeEnd,
        estimate: that.search.degree === '' ? 0 : that.search.degree,
        customName: that.search.customerName,
        keyword: '',
        maxtime: that.search.endTime,
        mintime: that.search.startTime,
        myBox: that.isSelf ? 1 : 0,
        name: that.search.serveName,
        nickName: that.search.serveNick,
        startTime: createTimeStart,
        userName: that.search.serveNum,
        contentWord: that.search.chattingRecords,
        businessPartCode: businessPart,
      };
      // console.log('preParam:', preParam);
      function httpPost(URL, PARAMS) {
        var temp = document.createElement('form');
        temp.action = URL;
        temp.method = 'get';
        temp.style.display = 'none';

        for (var x in PARAMS) {
          var opt = document.createElement('textarea');
          opt.name = x;
          opt.value = PARAMS[x];
          temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();
      }
      httpPost(`${process.env.BASE_API_IM}/dialogs/search/export`, preParam);
    },
    initializeTime() {
      const start = new Date(
        new Date(new Date().toLocaleDateString()).getTime()
      );
      const end = new Date(
        new Date(new Date().toLocaleDateString()).getTime() +
          24 * 60 * 60 * 1000 -
          1
      );

      this.search.dateRange = [start, end];

      // const createTimeStart = this.getFormatDate('', '', start.getTime());
      // const createTimeEnd = this.getFormatDate('', '', end.getTime());
      // this.searchstartTime = createTimeStart;
      // this.searchendTime = createTimeEnd;

      // const dateRange = this.search.dateRange;

      // const createTimeStart =
      //   dateRange && dateRange.length
      //     ? this.getFormatDate('', '', dateRange[0].getTime())
      //     : '';
      // const createTimeEnd =
      //   dateRange && dateRange.length
      //     ? this.getFormatDate('', '', dateRange[1].getTime())
      //     : '';

      // this.searchstartTime = this.search.dateRange[0];
      // this.searchendTime = this.search.dateRange[1];
    },
    getFormatDate: function (row, column, cellValue, index) {
      return new Date(cellValue + 8 * 3600 * 1000)
        .toJSON()
        .substr(0, 19)
        .replace('T', ' ');
    },
  },
};
</script>

<style lang="scss" scoped>
.excelBtn {
  margin-top: 20px;
  height: 36px;
  /deep/span {
    font-size: 14px;
    color: rgba(64, 163, 184, 1);
  }
}
.search-box {
  padding-bottom: 20px;
  border-bottom: 1px dotted #e4e4eb;
  .innerEl {
    height: 38px;
    /deep/.el-input__inner {
      width: 100%;
      height: 36px;
      line-height: 36px;
    }
    /deep/.el-form-item {
      margin: 0;
    }
    .timeSel {
      width: 500px;
    }
    .chattingRecords {
      width: 500px;
    }
    .seachItme {
      min-width: 580px;
    }
    .timeLoneIte {
      margin-right: 20px;
    }
    .timeLone {
      display: inline-block;
      width: 90px;
    }
    /deep/input[type='number']::-webkit-inner-spin-button,
    /deep/input[type='number']::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
    .line {
      display: inline-block;
      width: 12px;
      text-align: center;
    }
    .serverName {
      width: 200px;
    }
    .serverNum {
      margin-right: 20px;
    }
    .isSelf {
      margin-right: 200px;
    }
    .searchCondition.is-plain {
      background: rgba(59, 149, 168, 1);
      color: #fff;
    }
    &.top_20 {
      margin-top: 20px;
    }
  }
}
.seachim {
  /deep/.page-body {
    width: 100%;
  }
  /deep/.table-containter {
    width: 54%;
    min-height: 400px;
  }
  /deep/.el-table__empty-block {
    width: 100 !important;
  }
  .dialogue {
    display: inline-block;
    width: 40%;
    background: rgba(255, 255, 255, 1);
    border-radius: 2px;
    position: relative;
    .header {
      position: absolute;
      top: 0;
      left: 0;
      height: 40px;
      width: 100%;
      background: rgba(245, 247, 250, 1);
      border-radius: 1px 1px 0px 0px;
      border: 1px solid rgba(220, 222, 227, 1);
      border-bottom: none;
      .exportExcel {
        margin-right: 20px;
      }
    }
    .content {
      position: absolute;
      width: 100%;
      max-height: 400px;
      overflow-y: scroll;
      top: 40px;
      left: 0;
      padding: 20px;
      border-radius: 1px 1px 0px 0px;
      border: 1px solid rgba(220, 222, 227, 1);
      border-top: none;
    }
  }
}
/deep/.current-row td {
  background-color: #bad6dc !important;
}
</style>
