<template>
  <div
    class="page"
    :class="erectMode ?'erectMode':''"
    v-loading.lock="fullscreenLoading"
    element-loading-text="loading"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(255,255,255, 0.8)"
  >
    <el-row type="flex" class="sheeTitle" align="middle" v-if="!erectMode">
      <span>新建工单</span>
    </el-row>
    <el-row class="select_type row-start" :class="erectMode ?'':'box'">
      <div>
        <label>选择工单类型</label>
        <el-select
          v-model="sheetTypeVal"
          placeholder="请选择"
          class="input_sheetType"
          @change="handleChangeType(sheetTypeVal)"
        >
          <el-option v-for="item in sheetTpye" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </div>
      <div class="select_Template">
        <label>选择工单模板</label>
        <el-select
          v-model="sheetNameVal"
          placeholder="请选择"
          class="input_sheetType"
          filterable
          @visible-change="checkSheetType"
          @change="sheetNameBtn"
        >
          <el-option v-for="item in sheetName" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </div>
    </el-row>
    <div v-if="sheetPre.length !==0&&layDatas" class="formType-layout">
      <el-row>
        <!-- 系统字段 -->
        <el-col :span="erectMode ? 24 : 9">
          <div class="systemField-form">
            <header>系统字段</header>
            <div v-for="(item,index) in systemArr" :key="index">
              <!-- 有关联关系 -->
              <div v-if="item.conditionList && item.conditionList.length > 0">
                <preview
                  v-show="getAuthTypeStatus(item)"
                  :ref="'ref'+item.fieldCode"
                  :keys="item.fieldType"
                  :preview="item"
                  :read="conditionAA(item) === 1"
                  :requirs="getAuthTypeStatus(item)?requirsConditionAA(item)===1:false"
                  refs="preSheet"
                  :sheetTypeVal="sheetTypeVal"
                  :sheetPreviewAll="sheetPre"
                  :searchResult="searchResult"
                  @keywordSearch="keywordToSearch"
                  @keywordSelected="keywordToSelected"
                  @handleProblemClass="handleSelVal"
                  @queryOrderInfo="queryOrderInfo"
                />
              </div>
              <preview
                v-else
                v-show="item.authType"
                :ref="'ref'+item.fieldCode"
                :keys="item.fieldType"
                :preview="item"
                :read="item.authType===1"
                :requirs="getAuthTypeStatus(item)?requirsConditionAA(item)===1:false"
                refs="preSheet"
                :sheetTypeVal="sheetTypeVal"
                :sheetPreviewAll="sheetPre"
                :searchResult="searchResult"
                @keywordSearch="keywordToSearch"
                @keywordSelected="keywordToSelected"
                @handleProblemClass="handleSelVal"
                @queryOrderInfo="queryOrderInfo"
              />
            </div>
          </div>
        </el-col>
        <!-- 模板字段 -->
        <el-col :span="erectMode ? 24 : 14" :style="erectMode ?'margin-top:8px;':''">
          <div class="custome-form">
            <header>{{ sheetNameTitle }}</header>
            <layout :lay-datas="getLayDatas()" :col-datas="customeArr">
              <template slot="col" slot-scope="{ arr,row,col }">
                <div class="template-col">
                  <!-- 有关联关系 -->
                  <div
                    v-if="arr[row][col]&&arr[row][col].conditionList && arr[row][col].conditionList.length > 0"
                  >
                    <preview
                      v-show="getAuthTypeStatus(arr[row][col])"
                      :ref="'ref'+arr[row][col].fieldCode"
                      :keys="arr[row][col].fieldType"
                      :preview="arr[row][col]"
                      :read="conditionAA(arr[row][col]) === 1"
                      :requirs="getAuthTypeStatus(arr[row][col])?requirsConditionAA(arr[row][col])===1:false"
                      refs="preSheet"
                      :sheetTypeVal="sheetTypeVal"
                      :sheetPreviewAll="sheetPre"
                      :searchResult="searchResult"
                      @keywordSearch="keywordToSearch"
                      @keywordSelected="keywordToSelected"
                      @handleProblemClass="handleSelVal"
                      @queryOrderInfo="queryOrderInfo"
                    />
                  </div>
                  <preview
                    v-else-if="arr[row][col]"
                    v-show="arr[row][col].authType"
                    :ref="'ref'+arr[row][col].fieldCode"
                    :keys="arr[row][col].fieldType"
                    :preview="arr[row][col]"
                    :read="arr[row][col].authType===1"
                    :requirs="getAuthTypeStatus(arr[row][col])?requirsConditionAA(arr[row][col])===1:false"
                    :show-tips="true"
                    refs="preSheet"
                    :sheetTypeVal="sheetTypeVal"
                    :sheetPreviewAll="sheetPre"
                    :searchResult="searchResult"
                    @keywordSearch="keywordToSearch"
                    @keywordSelected="keywordToSelected"
                    @handleProblemClass="handleSelVal"
                    @queryOrderInfo="queryOrderInfo"
                  />
                </div>
              </template>
            </layout>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <div class="footer-btn">
          <el-button
            :disabled="addSheetState"
            class="submBtn"
            type="primary"
            @click="checkTimer(workorderLocationCheck,'time1')()"
          >发起</el-button>
          <el-button class="addDraft" @click="checkTimer(addDraft,'time2')()">保存草稿</el-button>
        </div>
      </el-row>
    </div>

    <el-col v-if="sheetPre.length ===0 && !erectMode" class="notSheet">
      <img class="noSelImg" src="../../assets/work_sheet/notSheet.png" />
      <span class="noSelTxt">请先选择工单类型和工单</span>
    </el-col>

    <!-- 相似工单提醒dialog -->
    <el-dialog
      :visible.sync="addSheetCheckHid"
      custom-class="addSheetCheckStyle"
      title="提示"
      width="400"
    >
      <span>
        近期有内容相似的工单已被创建，工单编号：
        <span
          class="worderId"
          style="cursor: pointer"
          @click="goDetail(workorderId)"
        >{{ workorderNum }}</span> 是否仍要发起
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addSheetCheckHid = false">取 消</el-button>
        <el-button type="primary" @click="NewAddSheet">继续发起</el-button>
      </span>
    </el-dialog>

    <!-- 客户所在地校验弹框dialog -->
    <el-dialog
      :visible.sync="locationStatus"
      custom-class="addSheetCheckStyle"
      title="提示"
      width="400"
    >
      <span>{{ locationMsg }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="checkTimer(workorderRepeateCheck,'time3')()">继续发起</el-button>
        <el-button type="primary" @click="locationStatus = false">去核对</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSheetTypeList,
  seleSheetList,
  getNodeList,
  saveWorkorder,
  addDraftItem,
  updateDraft,
  delDraft,
  getWorkorderRepeateCheck,
  getWorkorderLocationCheck,
  listTemplateFieldAuthByCode,
  getMerchantByAccountMobile
} from '@/api/mySheetManage';
import { listFieldAndAuthByFormId } from '@/api/im_view';
import Preview from '~/Fields/preview';
import dataTime from '@/utils/filter.js';
import { typeCopyVal } from '@/utils/tools.js';
import pubUrl from './mixin/pubUrl';
import layout from '@/components/templates/layout';
export default {
  components: {
    Preview,
    layout
  },
  mixins: [pubUrl],
  props: {
    erectMode: {
      // 是否竖向排列模式
      type: Boolean,
      default: false
    }
  },
  mixins: [pubUrl],
  data() {
    return {
      time1: null,
      time2: null,
      time3: null,
      locationStatus: false, // 客户所在地校验弹框
      locationMsg: '', // 客户所在地校验信息
      addSheetCheckHid: false,
      addSheetState: false,
      edit: false,
      workorderDraftId: null,
      sheetNameTitle: '',
      sheetPre: [
        // {
        //   id: 30, // 主键
        //   formId: 1, // 表单ID
        //   nodeId: 71, // 节点ID
        //   fieldId: 23, // 字段ID
        //   authType: 2, // 权限类型 0-隐藏 1-只读 2-编辑
        //   authTypeCondition: 2, // 满足条件的权限类型 0-隐藏 1-只读 2-编辑
        //   required: 1, // 是否必填 0-否 1-是
        //   requiredCondition: 0, // 满足条件后是否必填 0-否 1-是
        //   ganged: 0, // 是否是联动字段：0-否 1-是
        //   deleted: null, // 是否删除 0-否 1-是
        //   duplicate: null, // 是否是副本：0-否 1-是
        //   creatorId: null, // 创建人ID
        //   orgId: null, // 组织ID
        //   gmtCreate: null, // 创建时间
        //   gmtModified: null, // 更新时间
        //   fieldText: '字段名111', // 字段文本
        //   fieldName: '字段名12', // 字段名称
        //   fieldCode: 'a120170553763339689', // 字段key
        //   fieldType: 1, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
        //   defaultValue: '', // 默认值
        //   optionSettings: '', // 选项设置
        //   tips: '',
        //   systemFieldFlag: 1, // 是否是系统字段 0是，1 不是 // 提示信息
        //   conditionList: [] // 节点权限字段关联条件list
        // },
        // {
        //   id: 33, // 主键
        //   formId: 2, // 表单ID
        //   nodeId: 71, // 节点ID
        //   fieldId: 33, // 字段ID
        //   authType: 2, // 权限类型 0-隐藏 1-只读 2-编辑
        //   authTypeCondition: 2, // 满足条件的权限类型 0-隐藏 1-只读 2-编辑
        //   required: 1, // 是否必填 0-否 1-是
        //   requiredCondition: 0, // 满足条件后是否必填 0-否 1-是
        //   ganged: 0, // 是否是联动字段：0-否 1-是
        //   deleted: null, // 是否删除 0-否 1-是
        //   duplicate: null, // 是否是副本：0-否 1-是
        //   creatorId: null, // 创建人ID
        //   orgId: null, // 组织ID
        //   gmtCreate: null, // 创建时间
        //   gmtModified: null, // 更新时间
        //   fieldText: '字段名22', // 字段文本
        //   fieldName: '字段名12', // 字段名称
        //   fieldCode: 's1164724692221825025', // 字段key
        //   fieldType: 0, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
        //   defaultValue: '00000', // 默认值
        //   optionSettings: '', // 选项设置
        //   tips: '',
        //   systemFieldFlag: 1, // 是否是系统字段 0是，1 不是 // 提示信息
        //   conditionList: [
        //     {
        //       conditionFieldCode: 30, // 条件字段ID
        //       conditionSymbol: 0, // 条件符号 0-等于 1-大于 2-小于 3-大于等于 4-小于等于 5-介于 6-无
        //       valueFirst: '888' // 条件值1
        //     }
        //   ] // 节点权限字段关联条件list
        // },
        // {
        //   id: 34,
        //   formId: 3,
        //   nodeId: 71,
        //   fieldId: 230,
        //   authType: 2,
        //   authTypeCondition: 2,
        //   required: 1,
        //   requiredCondition: 0,
        //   ganged: 0,
        //   deleted: null,
        //   duplicate: null,
        //   creatorId: null,
        //   orgId: null,
        //   gmtCreate: null,
        //   gmtModified: null,
        //   fieldText: '字段名2',
        //   fieldName: '字段名2',
        //   fieldCode: 'a120170553763339683',
        //   fieldType: 2, // <!-- 下拉列表 -->
        //   defaultValue: '',
        //   optionSettings: {
        //     // 选项设置卡
        //     selectOptions: {
        //       // 下拉列表
        //       optionsValue: 0, // 默认值
        //       optionsArray: [
        //         {
        //           // 选项数组
        //           optionsDesc: '选项数组1'
        //         },
        //         {
        //           // 选项数组
        //           optionsDesc: '选项数组2'
        //         }
        //       ]
        //     }
        //   },
        //   tips: '',
        //   systemFieldFlag: 1, // 是否是系统字段 0是，1 不是
        //   conditionList: []
        // },
        // {
        //   id: 33,
        //   formId: 4,
        //   nodeId: 71,
        //   fieldId: 239,
        //   authType: 2,
        //   authTypeCondition: 2,
        //   required: 1,
        //   requiredCondition: 0,
        //   ganged: 0,
        //   deleted: null,
        //   duplicate: null,
        //   creatorId: null,
        //   orgId: null,
        //   gmtCreate: null,
        //   gmtModified: null,
        //   fieldText: '字段名3',
        //   fieldName: '字段名3',
        //   fieldCode: '1161472515860729856',
        //   fieldType: 3,
        //   defaultValue: '0',
        //   optionSettings: {
        //     // 选项设置卡
        //     radioOptions: {
        //       // 单选 选项设置
        //       optionsValue: 0, // 默认值
        //       optionsArray: [
        //         {
        //           // 选项数组
        //           optionsDesc: '男'
        //         },
        //         {
        //           // 选项数组
        //           optionsDesc: '女'
        //         }
        //       ]
        //     }
        //   },
        //   tips: '',
        //   systemFieldFlag: 0, // 是否是系统字段 0是，1 不是
        //   conditionList: []
        // },
        // {
        //   id: 33,
        //   formId: 1,
        //   nodeId: 71,
        //   fieldId: 238,
        //   authType: 2,
        //   authTypeCondition: 2,
        //   required: 1,
        //   requiredCondition: 0,
        //   ganged: 0,
        //   deleted: null,
        //   duplicate: null,
        //   creatorId: null,
        //   orgId: null,
        //   gmtCreate: null,
        //   gmtModified: null,
        //   fieldText: '字段名4',
        //   fieldName: '字段名4',
        //   fieldCode: '1161472515860729856',
        //   fieldType: 4,
        //   defaultValue: '0',
        //   optionSettings: {
        //     // 选项设置卡
        //     checkedOptions: {
        //       // 多选 选项设置
        //       optionsValue: [0], // 默认值
        //       optionsArray: [
        //         {
        //           // 选项数组
        //           optionsDesc: '苹果',
        //           val: ''
        //         },
        //         {
        //           // 选项数组
        //           optionsDesc: '红色'
        //         }
        //       ]
        //     }
        //   },
        //   tips: '',
        //   systemFieldFlag: 0, // 是否是系统字段 0是，1 不是
        //   conditionList: []
        // },
        // {
        //   id: 33,
        //   formId: 1,
        //   nodeId: 71,
        //   fieldId: 237,
        //   authType: 2,
        //   authTypeCondition: 2,
        //   required: 1,
        //   requiredCondition: 0,
        //   ganged: 0,
        //   deleted: null,
        //   duplicate: null,
        //   creatorId: null,
        //   orgId: null,
        //   gmtCreate: null,
        //   gmtModified: null,
        //   fieldText: '字段名5',
        //   fieldName: '字段名5',
        //   fieldCode: '1161472515860729856',
        //   fieldType: 5,
        //   defaultValue: '0',
        //   optionSettings: {
        //     // 选项设置卡
        //     dateOptions: {
        //       // 日期选项
        //       dateSelect: 'yy-mm-dd 周ww', // 日期默认
        //       dateArray: [
        //         {
        //           value: 'YYYY年MMMM月DDDD日',
        //           label: '二零一九年/四月/十五日'
        //         },
        //         {
        //           value: 'yy-mm-dd',
        //           label: '2019-04-15'
        //         },
        //         {
        //           value: 'yy-mm-dd HH:ss:nn',
        //           label: '2019-04-15 17:33:45'
        //         },
        //         {
        //           value: 'yy年mm月dd日 周ww',
        //           label: '2019年4月15日 周一'
        //         },
        //         {
        //           value: 'yy-mm-dd 周ww',
        //           label: '2019-04-15 周一'
        //         }
        //       ],
        //       dateValue: new Date().getTime(),
        //       defaultDate: 0, // 默认值选择
        //       defaultDateArray: [
        //         {
        //           label: '当前时间',
        //           value: 0
        //         },
        //         {
        //           label: '5天前',
        //           value: 1
        //         },
        //         {
        //           label: '1天前',
        //           value: 2
        //         },
        //         {
        //           label: '7天前',
        //           value: 3
        //         },
        //         {
        //           label: '无',
        //           value: 4
        //         }
        //       ]
        //     }
        //   },
        //   tips: '',
        //   systemFieldFlag: 0, // 是否是系统字段 0是，1 不是
        //   conditionList: []
        // },
        // {
        //   id: 33,
        //   formId: 1,
        //   nodeId: 71,
        //   fieldId: 236,
        //   authType: 2,
        //   authTypeCondition: 2,
        //   required: 0,
        //   requiredCondition: 0,
        //   ganged: 0,
        //   deleted: null,
        //   duplicate: null,
        //   creatorId: null,
        //   orgId: null,
        //   gmtCreate: null,
        //   gmtModified: null,
        //   fieldText: '字段名6',
        //   fieldName: '字段名6',
        //   fieldCode: 's1164724692221825024',
        //   fieldType: 6,
        //   defaultValue: '0',
        //   optionSettings: {
        //     // 选项设置卡
        //     treeOptions: {
        //       optionsValue: [],
        //       optionsArray: [
        //         {
        //           children: [
        //             {
        //               label: '证件过期2',
        //               parentCode: 'c1211543570062905344',
        //               templateCode: '',
        //               typeFullName: '客户2/证件过期2',
        //               value: 'c1211543570096459776'
        //             }
        //           ],
        //           label: '修改客户2',
        //           parentCode: '0',
        //           templateCode: '',
        //           typeFullName: '客户2',
        //           value: 'c1211543570062905344'
        //         },
        //         {
        //           children: [
        //             {
        //               children: [
        //                 {
        //                   label: '哈哈123',
        //                   parentCode: 'c1211621923419721728',
        //                   templateCode: '',
        //                   typeFullName: '/哈哈/哈哈123',
        //                   value: 'c1211621923423916032'
        //                 }
        //               ],
        //               label: '哈哈',
        //               parentCode: 'c1211621923415527424',
        //               templateCode: '',
        //               typeFullName: '/哈哈',
        //               value: 'c1211621923419721728'
        //             }
        //           ],
        //           label: '哈',
        //           parentCode: '0',
        //           templateCode: '',
        //           typeFullName: '',
        //           value: 'c1211621923415527424'
        //         },
        //         {
        //           label: '东东',
        //           parentCode: '0',
        //           templateCode: '',
        //           typeFullName: '东东',
        //           value: 'c1211621923423916033'
        //         }
        //       ]
        //     }
        //   },
        //   tips: '',
        //   systemFieldFlag: 0, // 是否是系统字段 0是，1 不是
        //   conditionList: []
        // },
        // {
        //   id: 34,
        //   formId: 1,
        //   nodeId: 71,
        //   fieldId: 235,
        //   authType: 2,
        //   authTypeCondition: 2,
        //   required: 1,
        //   requiredCondition: 0,
        //   ganged: 0,
        //   deleted: null,
        //   duplicate: null,
        //   creatorId: null,
        //   orgId: null,
        //   gmtCreate: null,
        //   gmtModified: null,
        //   fieldText: '字段名7',
        //   fieldName: '字段名7',
        //   fieldCode: '1161472515860729856',
        //   fieldType: 7,
        //   defaultValue: '0',
        //   optionSettings: {
        //     cityOptions: {
        //       optionsValue: ['省', '市', '区', '街道'],
        //       optionsArray: ''
        //     }
        //   },
        //   tips: '',
        //   systemFieldFlag: 0, // 是否是系统字段 0是，1 不是
        //   conditionList: []
        // },
        // {
        //   id: 39,
        //   formId: 1,
        //   nodeId: 71,
        //   fieldId: 23,
        //   authType: 2,
        //   authTypeCondition: 2,
        //   required: 1,
        //   requiredCondition: 0,
        //   ganged: 0,
        //   deleted: null,
        //   duplicate: null,
        //   creatorId: null,
        //   orgId: null,
        //   gmtCreate: null,
        //   gmtModified: null,
        //   fieldText: '字段名8',
        //   fieldName: '字段名8',
        //   fieldCode: '1161472515860729856',
        //   fieldType: 8,
        //   defaultValue: '13031996666',
        //   optionSettings: '',
        //   tips: '',
        //   systemFieldFlag: 0, // 是否是系统字段 0是，1 不是
        //   conditionList: []
        // }
        // {
        //   id: 300,
        //   formId: 1,
        //   nodeId: 71,
        //   fieldId: 234,
        //   authType: 2,
        //   authTypeCondition: 2,
        //   required: 1,
        //   requiredCondition: 0,
        //   ganged: 0,
        //   deleted: null,
        //   duplicate: null,
        //   creatorId: null,
        //   orgId: null,
        //   gmtCreate: null,
        //   gmtModified: null,
        //   fieldText: '字段名10',
        //   fieldName: '字段名10',
        //   fieldCode: '1161472515860729856',
        //   fieldType: 10,
        //   defaultValue: '0',
        //   optionSettings: {
        //     fileObj: {
        //       optionsValue: [], // 文件名
        //       optionsArray: '' // 文件路径
        //     }
        //   },
        //   tips: '',
        // systemFieldFlag:0,//是否是系统字段 0是，1 不是
        //   conditionList: []
        // }
      ],
      sheetTypeVal: '',
      sheetNameVal: '',
      sheetTpye: [],
      sheetName: [
        // 工单列表(工单模板)
        // {
        //   value: '选项1',
        //   label: '黄金糕'
        // }
      ],
      extendWorkorderField: [],
      routePath: '',
      delView: false,
      refresh: false,
      workorderNum: '',
      workorderId: null,
      time: null,
      loading: null,
      fullscreenLoading: false,
      systemArr: [],
      customeArr: [],
      layDatas: '',
      im_field: '', // 编辑草稿时传递im信息
      areaName: undefined,
      searchResult: [], // 根据手机号搜索出来的店铺信息
      tempMerchantInfo: {}, // 根据客户电话匹配出来的客户信息
    };
  },
  computed: {
    formTypeName() {
      let formTypeName = null;
      this.sheetTpye.forEach(el => {
        if (el.id === this.sheetTypeVal) {
          formTypeName = el.name;
        }
      });
      return formTypeName;
    },
    formName() {
      let formName = null;
      this.sheetName.forEach(el => {
        if (el.id === this.sheetNameVal) {
          formName = el.name;
        }
      });
      return formName;
    },
    templateId() {
      let templateId = null;
      if (this.sheetPre.length > 0) {
        templateId = this.sheetPre[0].templateId;
      }
      return templateId;
    }
  },
  watch: {
    sheetPre: function(val) {
      this.systemArr.splice(0, this.systemArr.length);
      this.customeArr.splice(0, this.customeArr.length);
      if (val && val.length > 0) {
        val.forEach(item => {
          if (item.systemFieldFlag == 0) {
            this.systemArr.push(item);
          } else {
            this.customeArr.push(item);
          }
        });
      }
    },

    /**
     * 监听工单类型列表，查找工单类型中默认值并显示
     */
    sheetTpye: function(newval) {
      if (this.erectMode) {
        const formTypeArray = newval.filter(item => {
          if (item.defaultValue === true) return item;
        });
        if (formTypeArray.length) {
          this.sheetTypeVal = formTypeArray[0].id || '';
          this.handleChangeType(this.sheetTypeVal); // 获取模板
        }
      }
    }
  },
  created() {
    this.sheetPre = this.$route.query.edit
      ? JSON.parse(this.$route.query.draft)
      : [];

    this.im_field = this.$route.query.im_field
      ? this.$route.query.im_field
      : '';

    if (this.$route.query.edit) {
      localStorage.removeItem('oldPathArr');
    }

    const that = this;
    localStorage.setItem('currtView', this.$route.path);

    this.emits(this, 'register', {
      type: 'tagclose_newSheet',
      uid: this._uid,
      fn: currentPath => {
        const that = this;
        this.$XyyMsg({
          title: '提示',
          content: '是否保存为草稿？',
          closeBtn: false,
          okValue: '保存',
          onSuccess: function() {
            that.addDraft(currentPath, true);
          },
          button: [
            {
              value: '不保存',
              callback: function() {
                // const currtView = localStorage.getItem('currtView');
                const currtPage = {
                  path: currentPath
                };
                that.$store.dispatch('tagsView/delView', currtPage);
                that.$router.push({
                  path: that.$route.query.currentUrl,
                  query: { t: new Date().getTime() }
                });
              }
            }
          ]
        });
      }
    });

    this.emits(this, 'register', {
      type: 'save_newSheet_data',
      uid: this._uid,
      fn: currtVal => {
        // 20201110DT确认此断代码永远不会触发后废弃
        try {
          localStorage.setItem(currtVal + 'sheetNameVal', that.sheetNameVal);
          localStorage.setItem(
            currtVal + 'sheetName',
            JSON.stringify(that.sheetName)
          );
          localStorage.setItem(currtVal + 'sheetTypeVal', that.sheetTypeVal);
          localStorage.setItem(
            currtVal + 'sheetTpye',
            JSON.stringify(that.sheetTpye)
          );
          localStorage.setItem(
            currtVal + 'layDatas',
            JSON.stringify(that.layDatas)
          );
          localStorage.setItem(
            currtVal + 'sheetPre',
            JSON.stringify(that.sheetPre)
          );
        } catch (error) {
          // 待接入雪地
          console.error(error);
        }
      }
    });

    // 关闭页签为在线会话时，清除im中选中的会话id
    this.emits(this, 'register', {
      type: 'tagclose_imcontainer',
      uid: this._uid,
      fn: () => {
        if (this.erectMode) {
          this.sheetTypeVal = '';
          this.sheetNameVal = '';
        }
      }
    });
  },
  activated() {
    this.$store.commit('tagsView/update_NewList_status', false);
    this.routePath = this.$route.path;
    this.delView = false;
    if (this.$route.query.edit) {
      this.$route.query['editSuccess'] =
        this.$route.query.edit === true ? 'false' : 'true';
    }

    if (this.sheetName && this.sheetName.length > 0 && this.sheetNameVal) {
      this.sheetNameTitle = this.sheetName.filter(
        el => el.id === this.sheetNameVal
      )[0].name;
    }
  },
  beforeRouteUpdate(to, from, next) {
    // this.emits(this, 'send', 'save_newSheet_data', this.$route.path);// 20201110DT确认此断代码永远不会触发后废弃
    next();
  },
  beforeRouteLeave(to, from, next) {
    // this.emits(this, 'send', 'save_newSheet_data', this.$route.path);// 20201110DT确认此断代码永远不会触发后废弃
    next();
  },
  mounted: function() {
    this.init();
  },
  methods: {
    init() {
      const that = this;
      // 处理切换当前页面后数据丢失的问题；存储路由 路由和tag做绑定 存储数据并赋值
      const oldPathArr = JSON.parse(localStorage.getItem('oldPathArr'));
      // #region
      // 20201110DT确认此断代码永远不会触发后废弃
      // debugger
      // if (oldPathArr && oldPathArr.indexOf(this.$route.path) != -1) {
      //   // localStorage.getItem(
      //   //   that.$route.path + 'sheetNameVal'
      //   // );
      //   const sheetNameVal = localStorage.getItem(
      //     that.$route.path + 'sheetNameVal'
      //   );
      //   const sheetName = JSON.parse(
      //     localStorage.getItem(that.$route.path + 'sheetName')
      //   );

      //   const sheetTypeVal = localStorage.getItem(
      //     that.$route.path + 'sheetTypeVal'
      //   );
      //   const sheetTpye = JSON.parse(
      //     localStorage.getItem(that.$route.path + 'sheetTpye')
      //   );
      //   const sheetPre = JSON.parse(
      //     localStorage.getItem(that.$route.path + 'sheetPre')
      //   );
      //   let layDatas = '';
      //   if (
      //     localStorage.getItem(that.$route.path + 'layDatas') &&
      //     localStorage.getItem(that.$route.path + 'layDatas') != 'undefined'
      //   ) {
      //     layDatas = JSON.parse(
      //       localStorage.getItem(that.$route.path + 'layDatas')
      //     );
      //   }
      //   this.layDatas = this.$route.query.layout || layDatas;

      //   // 草稿编辑回显处理
      //   const { edit, id } = this.$route.query;
      //   edit ? (this.edit = true) : (this.edit = false);
      //   id ? (this.workorderDraftId = id) : (this.workorderDraftId = null);
      //   this.sheetNameVal = sheetNameVal;
      //   this.sheetName = sheetName;
      //   this.sheetTypeVal = sheetTypeVal;
      //   if (sheetTpye) {
      //     this.sheetTpye = sheetTpye;
      //   } else {
      //     const preParam = { status: 1 }; // // 获取表单类型入参
      //     this.getSheetTypeList(preParam); // 获取表单类型
      //   }

      //   this.sheetPre = sheetPre || [];
      //   // this.layDatas = layDatas;
      //   if (this.sheetPre && this.sheetPre.length > 0) {
      //     this.sheetPre.forEach(el => {
      //       if (el.fieldType === 10) {
      //         this.$nextTick(() => {
      //           if (this.$refs['ref' + el.fieldCode]) {
      //             this.$refs['ref' + el.fieldCode][0]
      //               ? this.$refs['ref' + el.fieldCode][0].initFiles()
      //               : this.$refs['ref' + el.fieldCode].initFiles();
      //           }
      //         });
      //       }
      //     }, this);
      //   }
      //   return;
      // } else
      // #endregion
      if (oldPathArr) {
        oldPathArr.push(this.$route.path);
        localStorage.setItem('oldPathArr', JSON.stringify(oldPathArr));
      } else {
        const routeArr = [];
        routeArr.push(this.$route.path);
        localStorage.setItem('oldPathArr', JSON.stringify(routeArr));
      }
      // --结束--
      this.sheetTypeVal = this.$route.query.id;
      this.layDatas = this.$route.query.layout;

      if (this.$route.query.edit) {
        this.$route.query['editSuccess'] =
          this.$route.query.edit === true ? 'false' : 'true';
      }
      const { edit, id } = this.$route.query;
      edit ? (this.edit = true) : (this.edit = false);
      id ? (this.workorderDraftId = id) : (this.workorderDraftId = null);
      const preParam = { status: 1 }; // // 获取表单类型入参
      this.getSheetTypeList(preParam); // 获取表单类型
    },
    keywordToSearch(fieldCode, keyword) {
      // 客户电话点击搜索
      if (fieldCode === 's1164723822566445056') {
        getMerchantByAccountMobile({
          mobile: keyword,
        }).then((res) => {
          this.searchResult = res.data || [];
          if (this.searchResult.length === 0) {
            this.$message.info('搜索结果为空');
            this.sheetPre.forEach(function(item, index) {
              // 客户名称
              if (item.fieldCode === 's1164456058157142016') {
                item.defaultValue = '';
              }
              // 客户ID
              if (item.fieldCode === 's1863875885430607872') {
                item.defaultValue = '';
              }
            })
          }
        });
      }
    },
    keywordToSelected(info) {
      this.tempMerchantInfo = info;
      this.sheetPre.forEach(function(item, index) {
        // 客户名称
        if (item.fieldCode === 's1164456058157142016') {
          item.defaultValue = info.realName;
        }
        // 客户ID
        if (item.fieldCode === 's1863875885430607872') {
          item.defaultValue = info.id.toString();
        }
      })
    },

    // 订单编号搜索回调,定向更新某些字段
    queryOrderInfo(orderInfo) {
      let templateProductNameFieldCodes = []
      this.sheetPre.forEach((item, index) => {
        // 客户名称
        if (item.fieldCode === 's1164456058157142016') {
          item.defaultValue = orderInfo.merchantName;
        }
        // 客户电话
        if(item.fieldCode === 's1164723822566445056') {
          item.defaultValue = orderInfo.mobile;
        }
        // 客户ID
        if (item.fieldCode === 's1863875885430607872') {
          item.defaultValue = orderInfo.merchantId.toString();
        }
        // 商家ID
        if(item.fieldCode === 's1870039678284075008') {
          item.defaultValue = orderInfo.orgId.toString();
        }
        // 商品
        if([
          'c1216381125258252288', // 商品名称A
          'c1216381794576896000', // 商品名称B
          'c1216381703413698560', // 商品名称C
          'c1864595898831605760', // 商品名称D
          'c1866073198404177920', // 商品名称E
        ].includes(item.fieldCode)) {
          templateProductNameFieldCodes.push(item.fieldCode)
        }
      })
      // 依次填充商品名称字段
      // 模板有商品名称字段 && 有选择商品
      if(templateProductNameFieldCodes.length && orderInfo.productList.length) {
        templateProductNameFieldCodes.forEach((codeItem, codeIndex) => {
          const index = this.sheetPre.findIndex(p=> p.fieldCode == codeItem)
          if(orderInfo.productList[codeIndex]) {
            const product = orderInfo.productList[codeIndex]
            this.sheetPre[index].defaultValue = `商品编号：${product.barcode || ''}\n商品名称：${product.productName || ''}\n套餐编号：${product.packageId || ''}\n套餐数量：${product.packageCount || ''}\n生产厂家：${product.manufacturer || ''}\n规格：${product.spec || ''}\n商品原价：${product.productPrice || ''}\n商品购买单价：${product.purchasePrice || ''}\n近效期至：${product.nearEffect || ''}\n远效期至：${product.farEffect || ''}\n商品数量：${product.productAmount || ''}\n实际出库数量：${product.realSendNum || ''}`
          } else {
            this.sheetPre[index].defaultValue = ''
          }
        })
      }
    },


    // 处理级联（问题分类）改变
    handleSelVal(Array, callback) {
      const that = this;
      // console.log('Array', Array);
      const Modal = Array[0];
      // 判断是否是系统字段问题分类
      if (Modal.fieldCode === 's1164724692221825024') {
        // 如果为系统字段问题分类改变，则开启loading
        that.fullscreenLoading = true;
        const TmpCode = Array[1];
        localStorage.setItem('oldCostomArr', JSON.stringify(that.sheetPre));
        if (TmpCode == '') {
          getNodeList({ formId: that.sheetNameVal })
            .then(response => {
              if (response.msg === 'success') {
                this.layDatas = response.data.layout;
                const newCostomArr = response.data.list;
                const oldCostomArr = JSON.parse(
                  localStorage.getItem('oldCostomArr')
                ); // 获取oldCostomArr
                newCostomArr.forEach(el => {
                  oldCostomArr.forEach(item => {
                    if (el.fieldCode === item.fieldCode) {
                      const it = typeCopyVal(item, el);
                      el = it;
                      if (el.fieldType === 10) {
                        this.$nextTick(() => {
                          if (this.$refs['ref' + item.fieldCode]) {
                            this.$refs['ref' + item.fieldCode].initFiles();
                          }
                        });
                      }
                    }
                  }, this);
                }, this);
                that.sheetPre = [...newCostomArr];
                if (!that.layDatas && that.sheetPre.length) {
                  const arr = [];
                  for (let i = 0; i < that.sheetPre.length; i++) {
                    // arr.push([1]);
                    if (that.sheetPre[i].systemFieldFlag == 1) {
                      arr.push([1]);
                    }
                  }
                  that.layDatas = arr.map(cols => cols.join('-')).join();
                }

                callback && callback()
              }
            })
            .finally(() => {
              that.fullscreenLoading = false;
            });
        } else {
          listTemplateFieldAuthByCode(TmpCode, '', this.sheetTypeVal)
            .then(res => {
              if (res.code === 1) {
                this.layDatas = res.data.layout;
                const newCostomArr = res.data.list;
                const oldCostomArr = JSON.parse(
                  localStorage.getItem('oldCostomArr')
                ); // 获取oldCostomArr
                console.log(newCostomArr);
                console.log(oldCostomArr);
                newCostomArr.forEach(el => {
                  oldCostomArr.forEach(item => {
                    if (el.fieldCode === item.fieldCode) {
                      const it = typeCopyVal(item, el);
                      el = it;
                      console.log(it);
                      if (el.fieldType === 10) {
                        this.$nextTick(() => {
                          if (this.$refs['ref' + item.fieldCode]) {
                            this.$refs['ref' + item.fieldCode].initFiles();
                          }
                        });
                      }
                    }
                  }, this);
                }, this);
                that.sheetPre = [...newCostomArr];
                // console.log('that.sheetPre:', that.sheetPre);
                if (!that.layDatas && that.sheetPre.length) {
                  const arr = [];
                  for (let i = 0; i < that.sheetPre.length; i++) {
                    if (that.sheetPre[i].systemFieldFlag == 1) {
                      arr.push([1]);
                    }
                    // arr.push([1]);
                  }
                  that.layDatas = arr.map(cols => cols.join('-')).join();
                }

                callback && callback()
              }
            })
            .finally(() => {
              that.fullscreenLoading = false;
            });
        }
      }
    },
    goDetail(id) {
      // console.log('继续发起：', id);
      this.addSheetCheckHid = false;
      this.$router.push({
        // path: '/' + this.pubUrl + '/sheetDetail/' + id, // 2020/10/23讲mixin混入puburl改为sheetTypeVal拼接
        path: '/workStatus' + this.sheetTypeVal + '/sheetDetail/' + id,
        query: { id: id, type: 'newSheet' }
      });
    },
    addDraft(currentPath, isClose) {
      this.delView = true; // 是否需要提示保存草稿提示 true不需要 false需要
      let uploading = false;
      this.sheetPre.forEach(el => {
        if (el.fieldType === 10) {
          if (
            this.$refs['ref' + el.fieldCode] &&
            this.$refs['ref' + el.fieldCode].uploading
          ) {
            uploading = true;
          }
        }
      }, this);
      if (uploading) {
        this.$XyyMessage.warning('尚有未上传完成的附件，请稍后');
        return;
      }
      const that = this;
      // if (that.$route.query.edit && !that.edit) {
      if (that.workorderDraftId) {
        const param = {
          formId: this.sheetNameVal,
          templateId: this.templateId,
          json: JSON.stringify(this.sheetPre),
          workorderDraftId: that.workorderDraftId
        };
        if (this.im_field) {
          param.im_field = this.im_field;
        }
        // console.log('更新草稿：', param);
        return updateDraft(param).then(response => {
          // console.log('updateDraft:', response);
          if (response.msg === 'success') {
            // console.log('保存草稿成功');
            this.$route.query.editSuccess = 'true';
            that.$message({
              type: 'success',
              message: '更新草稿成功!'
            });
            this.$store.commit('draft/update_Draft_status', true);
            if (isClose) {
              const currtPage = {
                path: currentPath || currtView
              };
              that.$store.dispatch('tagsView/delView', currtPage);
              // that.$store.dispatch('tagsView/delView', that.$route);
              that.$router.push({
                path: that.$route.query.currentUrl,
                query: { t: new Date().getTime() }
              });
            }
          } else {
            that.$XyyMessage.error(response.msg);
          }
        });
      } else {
        const param = {
          formId: this.sheetNameVal,
          formName: this.formName,
          formTypeId: this.sheetTypeVal,
          formTypeName: this.formTypeName,
          templateId: this.templateId,
          draft: JSON.stringify(this.sheetPre)
        };
        if (
          that.erectMode &&
          that.$store.getters.khid &&
          that.$store.getters.containerid
        ) {
          // im 竖立模式需要关联khid，containerid，appId
          const im_field = {
            im_client_id: Number(that.$store.getters.khid),
            im_dialog_id: Number(that.$store.getters.containerid),
            im_app_id: Number(that.$store.getters.chat.appId)
          };
          param.im_field = JSON.stringify(im_field);
        }

        that.$nextTick(function() {
          return addDraftItem(param).then(response => {
            if (response.msg === 'success') {
              that.workorderDraftId = response.data;
              that.$message({
                type: 'success',
                message: '草稿保存成功!'
              });
              if (!that.erectMode) {
                // 非竖立模式跳转
                this.$store.commit('draft/update_Draft_status', true);
                const currtView = localStorage.getItem('currtView');
                const currtPage = {
                  path: currentPath || currtView
                };
                that.$store.dispatch('tagsView/delView', currtPage);
                // that.$store.dispatch('tagsView/delView', that.$route);
                that.$router.push({
                  path: that.$route.query.currentUrl,
                  query: { t: new Date().getTime() }
                });
              } else {
                // 竖立模式 不跳转，重置表单，清空草稿ID
                that.workorderDraftId = null;
                that.sheetPre = [];
                this.sheetNameBtn(this.sheetNameVal); // 获取模板字段
                const cid = that.$store.getters.containerid;
                this.$store.commit('imProperty/DEL_IMSheetItem', {
                  name: cid
                }); // 删除本表单缓存
              }
            }
          });
        });
      }
    },
    // 获取显隐状态
    getAuthTypeStatus(item) {
      const condition = this.conditionAA(item);
      return condition !== 0;
    },
    conditionAA(obj) {
      // authType: 0, // 权限类型 0-隐藏 1-只读 2-编辑
      // authTypeCondition: 2, // 满足条件的权限类型 0-隐藏 1-只读 2-编辑
      const that = this;
      let authTypeCondition = obj.authType;
      if (obj.conditionList[0]) {
        this.sheetPre.forEach(function(item, index) {
          if (item.fieldCode === obj.conditionList[0].conditionFieldCode) {
            let optionsValue;

            if (
              item.optionSettings &&
              typeof item.optionSettings === 'string'
            ) {
              const rest = item.optionSettings.replace(/(&quot;)/g, '"');
              optionsValue = JSON.parse(rest);
            } else {
              optionsValue = item.optionSettings;
            }
            // fieldType: 1, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
            let symbolSign = false;
            let value = null;
            switch (item.fieldType) {
              case 0:
              case 1:
              case 5:
              case 8:
              case 9:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  item.defaultValue
                );
                break;
              case 2:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  optionsValue.selectOptions.optionsValue
                );
                break;
              case 3:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  optionsValue.radioOptions.optionsValue
                );
                break;
              // eslint-disable-next-line no-case-declarations
              case 4:
                value = [...optionsValue.checkedOptions.optionsValue];
                value = value.sort();
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  value.join()
                );
                break;
              // eslint-disable-next-line no-case-declarations
              case 6:
                // console.log('级联：', optionsValue);
                // console.log('级联：', obj);
                value = [...optionsValue.treeOptions.optionsValue];
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  value.join()
                );
                break;
            }
            if (symbolSign) {
              // 满足条件的权限类型 0-隐藏 1-只读 2-编辑
              authTypeCondition = obj.authTypeCondition;
            }
          }
        });
      }

      return authTypeCondition;
    },
    // 是否必填
    requirsConditionAA(obj) {
      //   required: 1, // 是否必填 0-否 1-是
      //   requiredCondition: 0, // 满足条件后是否必填 0-否 1-是
      const that = this;
      let requiredCondition = obj.required;
      if (obj.conditionList[0]) {
        this.sheetPre.forEach(function(item, index) {
          if (item.fieldCode === obj.conditionList[0].conditionFieldCode) {
            let optionsValue;

            if (
              item.optionSettings &&
              typeof item.optionSettings === 'string'
            ) {
              const rest = item.optionSettings.replace(/(&quot;)/g, '"');
              optionsValue = JSON.parse(rest);
            } else {
              optionsValue = item.optionSettings;
            }
            // fieldType: 1, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
            let symbolSign = false;
            switch (item.fieldType) {
              case 0:
              case 1:
              case 5:
              case 8:
              case 9:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  item.defaultValue
                );
                break;
              case 2:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  optionsValue.selectOptions.optionsValue
                );
                break;
              case 3:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  optionsValue.radioOptions.optionsValue
                );
                break;
              // eslint-disable-next-line no-case-declarations
              case 4:
                let value = [...optionsValue.checkedOptions.optionsValue];
                value = value.sort((a, b) => a - b);
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  value.join()
                );
                break;
              case 6:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  optionsValue.treeOptions.optionsValue
                );
                break;
            }
            if (symbolSign) {
              //   requiredCondition: 0, // 满足条件后是否必填 0-否 1-是
              requiredCondition = obj.requiredCondition;
            }
          }
        });
      }
      return requiredCondition;
    },
    getConditionSymbol(obj, entryValue) {
      // entryValue:输入值 valueFirst:条件值
      // 条件符号 0-等于 1-大于 2-小于 3-大于等于 4-小于等于 5-介于 6-无
      const value = obj.valueFirst
        .split(',')
        .sort((a, b) => a - b)
        .join(',');
      if (Object.prototype.toString.call(entryValue) === '[object Array]') {
        entryValue = entryValue.join(',');
      }
      switch (obj.conditionSymbol) {
        case 0:
          return entryValue == value;
        case 1:
          return entryValue != value;
        case 2:
          return entryValue.indexOf(value) !== -1;
        // eslint-disable-next-line no-case-declarations
        case 3:
          // console.log('entryValue:', entryValue);
          // console.log('value:', value);
          const valArr = value.split(',');
          for (let i = 0; i <= valArr.length; i++) {
            if (String(entryValue).indexOf(valArr[i]) != -1) {
              return true;
            }
          }
          return false;
        case 4:
          return entryValue === value;
        case 5:
          return entryValue !== value;
      }
    },



    // 获取表单类型
    getSheetTypeList(preParam) {
      const that = this;
      return getSheetTypeList(preParam)
        .then(response => {
          if (response.code) {
            const data = response.data;
            that.sheetTpye = data;
            // 点击草稿编辑跳转
            if (that.edit) {
              const { formTypeId } = that.$route.query;
              that.sheetTypeVal = formTypeId;
              that.handleChangeType(formTypeId);
            } else if (that.$route.query && that.$route.query.formTypeId) {
              // 选择表单跳转
              const { formTypeId } = that.$route.query;
              that.sheetTypeVal = formTypeId;
              that.handleChangeType(formTypeId);
            }
          } else {
            that.$XyyMessage.error(response.msg);
          }
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    // 表单类型切换
    handleChangeType(id) {
      const that = this;
      if (that.$route.query.edit && !that.edit) {
        that
          .$confirm('放弃编辑当前草稿?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(() => {
            const param = { formTypeId: id, status: 1 };
            that.seleSheetList(param);
            that.workorderDraftId = null;
            that.$route.query.edit = false;
          })
          .catch(id => {
            const { formTypeId } = that.$route.query;

            that.sheetTypeVal = formTypeId;
            return;
          });
      } else {
        // 请求工单数据
        const param = { formTypeId: id, status: 1 };
        if (this.areaName) param.areaName = this.areaName; // im地域名称
        that.seleSheetList(param);
      }
    },
    // 获取表单模板
    seleSheetList(param) {
      const that = this;
      that.fullscreenLoading = true;

      return seleSheetList(param)
        .then(response => {
          // console.log('工单列表:', response);
          const data = response.data;
          this.sheetName = data;
          this.sheetNameVal = '';
          this.sheetPre = [];
          if (that.edit) {
            const { formId } = that.$route.query;
            that.sheetNameVal = formId;
            that.sheetNameBtn(formId);
          }

          if (this.erectMode) {
            // 如果是im竖立模式，则查找im会话用户对用的工单模板并默认选择
            const sheetNameArray = this.sheetName.filter(item => {
              if (item.defaultValue === true) return item;
            });
            if (sheetNameArray.length) {
              this.sheetNameVal = sheetNameArray[0].id || '';
              this.sheetNameBtn(this.sheetNameVal); // 获取模板字段
            }
          }
        })
        .catch(function(error) {
          console.log(error);
        })
        .finally(() => {
          that.fullscreenLoading = false;
        });
    },
    // 表单切换
    sheetNameBtn(id) {
      const that = this;
      if (that.$route.query.edit && !that.edit) {
        that
          .$confirm('放弃编辑当前草稿?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(() => {
            this.sheetNameTitle = this.sheetName.filter(
              el => el.id === id
            )[0].name;
            const formId = { formId: id };
            this.getNodeList(formId);
            that.workorderDraftId = null;
            that.$route.query.edit = false;
          })
          .catch(() => {
            const { formId } = that.$route.query;
            that.sheetNameVal = formId;
            return;
          });
      } else {
        // 查询节点字段
        this.sheetNameTitle = this.sheetName.filter(el => el.id === id)[0].name;
        const formId = { formId: id };
        this.getNodeList(formId);
      }
    },
    // 根据表单ID获取列表
    getNodeList(formId) {
      const that = this;
      if (that.$route.query.edit && that.edit) {
        that.sheetPre = JSON.parse(that.$route.query.draft);
        that.sheetPre.forEach(el => {
          if (el.fieldType === 10) {
            that.$nextTick(() => {
              if (that.$refs['ref' + el.fieldCode]) {
                that.$refs['ref' + el.fieldCode][0]
                  ? that.$refs['ref' + el.fieldCode][0].initFiles()
                  : that.$refs['ref' + el.fieldCode].initFiles();
              }
            });
          }
        });
        that.edit = false;
      } else {
        let getNodeListhandle = getNodeList;
        if (this.erectMode && that.$store.getters.containerid) {
          getNodeListhandle = listFieldAndAuthByFormId;
          formId.dialogId = Number(that.$store.getters.containerid);
        }
        setTimeout(() => {
          that.fullscreenLoading = true;
        }, 0);
        return getNodeListhandle(formId)
          .then(response => {
            // console.log('查询节点字段:', response);
            if (response.msg === 'success') {
              this.layDatas = response.data.layout;
              const data = response.data.list;
              if (response.data.list.length) {
                that.sheetPre = data;
                if (this.$route.query.edit) {
                  this.$route.query['sheetUnsaved'] = true;
                }
              }
              if (!that.layDatas && that.sheetPre.length) {
                const arr = [];
                for (let i = 0; i < that.sheetPre.length; i++) {
                  if (that.sheetPre[i].systemFieldFlag == 1) {
                    arr.push([1]);
                  }
                }
                that.layDatas = arr.map(cols => cols.join('-')).join();
              }
              that.sheetPre.forEach(el => {
                if (el.fieldType === 10) { // 附件
                  that.$nextTick(() => {
                    if (that.$refs['ref' + el.fieldCode]) {
                      that.$refs['ref' + el.fieldCode][0]
                        ? that.$refs['ref' + el.fieldCode][0].initFiles()
                        : that.$refs['ref' + el.fieldCode].initFiles();
                    }
                  });
                } else if (el.fieldType === 0) { // 单行预览

                } else if (el.fieldType === 8) { // 电话
                  // 隐藏搜索结果
                  that.$nextTick(() => {
                    const refFieldCode = that.$refs['ref' + el.fieldCode]
                    if (refFieldCode && refFieldCode[0]) {
                      refFieldCode[0].showSearchResult = false
                      this.searchResult = []
                    }
                  })
                }
              });
            } else {
              if (!response.hasOwnProperty('message')) {
                that.$message({
                  type: 'info',
                  message: response.msg
                });
              }
            }
          })
          .finally(function(error) {
            that.fullscreenLoading = false; // 取消loading
            console.log(error);
          });
      }
    },
    // 为选择工单类型-不允许选择工单
    checkSheetType(callback) {
      if (callback === true) {
        // 选择工单下拉状态
        if (!this.sheetTypeVal) {
          // this.$notify({
          //   title: '警告',
          //   message: '请先选择工单类型',
          //   duration: 3000,
          //   type: 'warning'
          // });
          return false;
        }
      }
    },



    // 校验客户所在地是否相同
    workorderLocationCheck() {
      let customerLocation = '';
      this.sheetPre.forEach(el => {
        // 客户所在地
        if (
          el.fieldCode === 's1176037136521105408' &&
          el.optionSettings.selectOptions.optionsValue
        ) {
          customerLocation = el.optionSettings.selectOptions.optionsArray.filter(
            opt => opt.val === el.optionSettings.selectOptions.optionsValue
          )[0].optionsDesc;
        }
      });
      if (customerLocation) {
        getWorkorderLocationCheck({
          customerLocation,
          formBaseId: this.sheetNameVal
        }).then(res => {
          if (res.code === 1) {
            this.workorderRepeateCheck();
          } else {
            this.locationStatus = true;
            this.locationMsg = res.msg;
          }
        });
      } else {
        this.workorderRepeateCheck();
      }
    },
    // 校验新增工单是否有相同问题分类和订单
    workorderRepeateCheck() {
      this.locationStatus = false;
      const that = this;
      const orderAndProb = [];
      that.sheetPre.forEach(el => {
        if (
          el.fieldCode === 's1164724692221825024' ||
          el.fieldCode === 's1176037234466492416'
        ) {
          orderAndProb.unshift(el);
        }
      });
      // console.log('问题分类or订单编号', orderAndProb);
      if (orderAndProb.length > 0) {
        let problemClassification, orderNumber;
        orderAndProb.forEach(el => {
          if (el.fieldCode === 's1164724692221825024') {
            // console.log('问题分类');
            const obj = {};
            if (
              !el.optionSettings.treeOptions ||
              el.optionSettings.treeOptions.optionsLabel
            ) {
              const arr2 = el.optionSettings.treeOptions.optionsLabel;
              el.optionSettings.treeOptions.optionsValue.forEach(function(
                item,
                index
              ) {
                obj[item] = arr2[index];
              });
            }
            problemClassification = Object.keys(obj).join(',');
          } else if (el.fieldCode === 's1176037234466492416') {
            // console.log('订单编号');
            orderNumber = el.defaultValue;
          }
        });
        // console.log('问题分类or订单编号', problemClassification, orderNumber);
        getWorkorderRepeateCheck(
          orderNumber,
          problemClassification,
          that.sheetTypeVal,
          '', // 新建时，工单号传空
        ).then(res => {
          // console.log('获取成功：', res);
          if (res.code) {
            that.NewAddSheet();
          } else {
            that.workorderId = res.data.workorder_id;
            that.workorderNum = res.data.workorder_num;
            that.addSheetCheckHid = true;
            return false;
          }
        });
      } else {
        that.NewAddSheet(); // 没有问题分类or订单编号
      }
    },
    // 新增工单开始
    NewAddSheet: function() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      const that = this;
      that.addSheetCheckHid = false;

      var result = [];
      const refList = this.$refs;
      for (var i in refList) {
        if (refList[i]) {
          var rest = new Promise((resolve, reject) => {
            if (refList[i][0]) {
              refList[i][0].submitForm().then(
                valid => {
                  resolve();
                },
                () => {
                  reject('请检查输入！');
                }
              );
            } else {
              refList[i].submitForm().then(
                valid => {
                  resolve();
                },
                () => {
                  reject('请检查输入！');
                }
              );
            }
          });
        }
        result.push(rest);
      }
      Promise.all(result)
        .then(res => {
          // console.log('数据列表', that.sheetPre);
          that.combSelVal();
        })
        .catch(res => {
          that.loading.close();
          console.log('校验失败' + res);
          // that.$XyyMessage.warning(res);
        });
    },
    combSelVal() {
      const extendWorkorderField = [];
      const that = this;
      let uploading = false;
      this.sheetPre.forEach(el => {
        if (el.fieldType === 10) {
          if (
            this.$refs['ref' + el.fieldCode] &&
            this.$refs['ref' + el.fieldCode].uploading
          ) {
            uploading = true;
          }
        }
      }, this);
      if (uploading) {
        this.$XyyMessage.warning('尚有未上传完成的附件，请稍后');
        that.loading.close();
        return;
      }
      that.addSheetState = true;
      that.sheetPre.forEach(function(item, index) {
        // fieldType: 1, // 字段类型
        // 0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期
        // 6:级联,7:省市区,8:电话,9:邮箱,10:附件
        const isShow = that.getAuthTypeStatus(item);
        if ((item.authType !== 0 || item.authTypeCondition == 2) && isShow) {
          
          // 此处都取页面上的字段
          /** 
          // 客户名称优先取通过手机号匹配出来的名称
          if (item.fieldCode === 's1164456058157142016') {
            extendWorkorderField.push({
              field_code: item.fieldCode,
              field_type: item.fieldType,
              field_value: that.tempMerchantInfo.realName || item.defaultValue.trim() || '',
            });
          } else 
          */
          if (
            item.fieldType === 0 ||
            item.fieldType === 1 ||
            item.fieldType === 8 ||
            item.fieldType === 9
          ) {
            extendWorkorderField.push({
              field_code: item.fieldCode,
              field_type: item.fieldType,
              field_value:
                item.fieldType === 0
                  ? item.defaultValue.trim()
                  : item.defaultValue
            });
            console.log(extendWorkorderField);
            // console.log('单行输入,多行输入', item.defaultValue);
          } else if (item.fieldType === 2) {
            if (
              !item.optionSettings.selectOptions ||
              item.optionSettings.selectOptions.optionsValue === ''
            ) {
              extendWorkorderField.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: {
                  option_name: '',
                  option_value: ''
                }
              });
            } else {
              extendWorkorderField.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: {
                  option_name: item.optionSettings.selectOptions.optionsArray.filter(
                    (el, i) =>
                      el.val
                        ? el.val ===
                        item.optionSettings.selectOptions.optionsValue
                        : i === item.optionSettings.selectOptions.optionsValue
                  )[0].optionsDesc,
                  option_value: item.optionSettings.selectOptions.optionsValue
                }
              });
            }
          } else if (item.fieldType === 3) {
            if (
              !item.optionSettings.radioOptions ||
              item.optionSettings.radioOptions.optionsValue === ''
            ) {
              extendWorkorderField.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: {
                  option_name: '',
                  option_value: ''
                }
              });
            } else {
              if (
                // item.optionSettings.radioOptions.optionsArray[
                //   item.optionSettings.radioOptions.optionsValue
                // ]
                item.optionSettings.radioOptions.optionsArray.some((el, i) =>
                  el.val
                    ? el.val === item.optionSettings.radioOptions.optionsValue
                    : i === item.optionSettings.radioOptions.optionsValue
                )
              ) {
                extendWorkorderField.push({
                  field_code: item.fieldCode,
                  field_type: item.fieldType,
                  field_value: {
                    option_name: item.optionSettings.radioOptions.optionsArray.filter(
                      (el, i) =>
                        el.val
                          ? el.val ===
                          item.optionSettings.radioOptions.optionsValue
                          : i === item.optionSettings.radioOptions.optionsValue
                    )[0].optionsDesc,
                    option_value: item.optionSettings.radioOptions.optionsValue
                  }
                });
              }
            }
          } else if (item.fieldType === 4) {
            const obj = {};
            for (const i of item.optionSettings.checkedOptions.optionsValue) {
              if (item.optionSettings.checkedOptions.optionsArray[i]) {
                obj[i] =
                  item.optionSettings.checkedOptions.optionsArray[
                    i
                  ].optionsDesc;
              } else if (
                item.optionSettings.checkedOptions.optionsArray.some(
                  el => el.val === i
                )
              ) {
                obj[i] = item.optionSettings.checkedOptions.optionsArray.filter(
                  el => el.val === i
                )[0].optionsDesc;
              }
            }
            extendWorkorderField.push({
              field_code: item.fieldCode,
              field_type: item.fieldType,
              field_value: {
                option_name: Object.values(obj).join('/'),
                option_value: Object.keys(obj).join(',')
              }
            });
            // console.log('多选', obj);
          } else if (item.fieldType === 5) {
            if (
              !item.optionSettings.dateOptions ||
              item.optionSettings.dateOptions === ''
            ) {
              extendWorkorderField.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: ''
              });
            } else {
              const dateOptions = item.optionSettings.dateOptions;
              const dateValue = dataTime.dataTime(
                dateOptions.dateValue,
                dateOptions.dateSelect
              );
              extendWorkorderField.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: dateValue
              });
              // console.log('日期', dateValue);
            }
          } else if (item.fieldType === 6) {
            const obj = {};

            // console.log(
            //   'item.optionSettings.treeOptions.optionsValue:',
            //   item.optionSettings.treeOptions.optionsValue
            // );
            // console.log(
            //   'item.optionSettings.treeOptions.optionsLabel:',
            //   item.optionSettings.treeOptions.optionsLabel
            // );
            if (item.optionSettings.treeOptions.optionsLabel) {
              const optionsLabelArr =
                item.optionSettings.treeOptions.optionsLabel;
              item.optionSettings.treeOptions.optionsValue.forEach(function(
                item,
                index
              ) {
                obj[item] = optionsLabelArr[index];
              });
            }

            // console.log('级联对象', obj);
            extendWorkorderField.push({
              field_code: item.fieldCode,
              field_type: item.fieldType,
              field_value: {
                option_name: Object.values(obj).join('/'),
                option_value: Object.keys(obj).join(',')
              }
            });
            console.log(extendWorkorderField);
          } else if (item.fieldType === 7) {
            extendWorkorderField.push({
              field_code: item.fieldCode,
              field_type: item.fieldType,
              field_value:
                item.optionSettings.cityOptions.optionsValue +
                ' ' +
                item.optionSettings.cityOptions.optionsArray
            });
            // console.log(
            //   '省市区',
            //   item.optionSettings.cityOptions.optionsValue +
            //     ' ' +
            //     item.optionSettings.cityOptions.optionsArray
            // );
          } else if (item.fieldType === 10) {
            // console.log('附件合并', item);
            let obj = [];
            const optionsArray = item.optionSettings.fileObj.optionsArray;
            // if (optionsArray.length > 0) {
            //   obj = [...optionsArray];
            // }
            if (optionsArray.length > 0) {
              obj = JSON.stringify(optionsArray);
              obj = JSON.parse(obj);
            }
            const objCopy = [];
            for (let i = 0; i < obj.length; i++) {
              objCopy.push(obj[i].data);
            }
            if (objCopy.length > 0) {
              extendWorkorderField.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: JSON.stringify(objCopy)
              });
            } else {
              extendWorkorderField.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: []
              });
            }
          }
        }
      });
      this.extendWorkorderField = extendWorkorderField;
      this.saveWorkorder();
    },
    saveWorkorder() {
      this.$route.query['sheetUnsaved'] = false; // 是否需要提示保存草稿提示 true需要 false不需要
      const that = this;
      // 发起工单
      const param = {
        base_field: {
          form_id: that.sheetNameVal, // that.sheetNameVal
          form_type_id: that.sheetTypeVal, // that.sheetTypeVal
          template_id: that.sheetPre[0] ? that.sheetPre[0].templateId : '',
          im_client_id: that.tempMerchantInfo.id || '',
        },
        custom_field: that.extendWorkorderField
      };
      if (
        that.erectMode &&
        that.$store.getters.khid &&
        that.$store.getters.containerid
      ) {
        // im 竖立模式需要关联khid，containerid，appId
        param.im_field = {
          im_client_id: Number(that.$store.getters.khid),
          im_dialog_id: Number(that.$store.getters.containerid),
          im_app_id: Number(that.$store.getters.chat.appId)
        };
      } else if (this.im_field) {
        param.im_field = JSON.parse(this.im_field);
      }
      // console.log('发起对象：', JSON.stringify(param));
      return saveWorkorder(param)
        .then(response => {
          if (response.msg === 'success') {
            this.loading.close();
            if (that.workorderDraftId) {
              // 如果为草稿新建，需要删除草稿
              delDraft(that.workorderDraftId).then(res => {
                if (res.code !== 1) {
                  console.log(`草稿${that.workorderDraftId}删除异常`);
                }
              });
            }
            that.$message({
              type: 'success',
              message: '保存成功!'
            });
            if (!that.erectMode) {
              // 非竖立模式跳转
              that.$store.dispatch('tagsView/delView', that.$route);
              that.$router.push({
                path: that.$route.query.currentUrl,
                query: { t: new Date().getTime() }
              });
            } else {
              // 竖立模式 不跳转，重置表单，并且删除store中的表单内容
              that.sheetPre = [];
              that.tempMerchantInfo = {};
              this.sheetNameBtn(this.sheetNameVal); // 获取模板字段
              const cid = that.$store.getters.containerid;
              this.$store.commit('imProperty/DEL_IMSheetItem', {
                name: cid
              }); // 删除本表单缓存
            }
          } else {
            that.$message({
              type: 'info',
              message: response.msg
            });
            that.loading.close();
          }
        })
        .finally(() => {
          that.addSheetState = false;
        });
    },

    /**
     * 根据erectMode 来返回不同的layDatas
     */
    getLayDatas() {
      if (this.erectMode) {
        return this.layDatas.replace(/-/g, ',');
      } else {
        return this.layDatas;
      }
    },

    /**
     * 重置 关联工单地域模板
     */
    resetTypeVal(data) {
      this.tempMerchantInfo = {};
      this.areaName = data.areaName; // im客户地域名称
      if (this.sheetTypeVal) {
        this.handleChangeType(this.sheetTypeVal);
      }
    },

    /**
     * IM端设定工单类型，模板和字段
     */
    setSheetItem(sheetItem) {
      let that = this;
      this.tempMerchantInfo = {};
      this.sheetTypeVal = sheetItem.sheetTypeVal;
      this.sheetName = sheetItem.sheetName;
      this.sheetNameVal = sheetItem.sheetNameVal;
      if (this.sheetNameVal) {
        that.sheetPre = sheetItem.sheetPre;
        that.layDatas = sheetItem.layDatas;
        that.$nextTick(() => {
          that.sheetPre.forEach(el => {
            if (el.fieldType === 10) {
              that.$nextTick(() => {
                // 初始化附件组件
                if (that.$refs['ref' + el.fieldCode]) {
                  that.$refs['ref' + el.fieldCode][0]
                    ? that.$refs['ref' + el.fieldCode][0].initFiles()
                    : that.$refs['ref' + el.fieldCode].initFiles();
                }
              });
              if (
                el.optionSettings.fileObj &&
                el.optionSettings.fileObj.optionsArray &&
                el.optionSettings.fileObj.optionsArray.length <= 0
              ) {
                el.uploading = false;
              }
            }
          });
        });
      }
    },

    /**
     * IM端获取工单类型，模板和字段
     */
    getSheetItem() {
      let sheetItem = {
        sheetTypeVal: this.sheetTypeVal,
        sheetNameVal: this.sheetNameVal,
        sheetPre: this.sheetPre,
        layDatas: this.layDatas,
        sheetName: this.sheetName
      };
      return sheetItem;
    },
  }
};
</script>
<style lang="scss">
/**
.explain {
  position: absolute;
  left: 104px;
  top: 4px;
}
*/
.el-dialog.addSheetCheckStyle {
  width: 400px;
  height: 200px;
  .el-dialog__header {
    border: 1px solid rgba(238, 238, 238, 1);
  }
  .el-dialog__body {
    padding: 20px 20px 30px 20px;
    font-size: 14px;
    color: #292933;
    .worderId {
      color: #3b95a8;
    }
  }
}
</style>
<style lang="scss" scoped>
/deep/.date-box {
  height: 36px;
}
.systemField-form,
.custome-form {
  /deep/.preview .single {
    margin: 16px;
    margin-bottom: 0 !important;
  }
  border: 1px solid #e4e4eb;
  padding-bottom: 20px;
  header {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: rgba(41, 41, 51, 1);
    padding-left: 16px;
    background: rgba(240, 242, 245, 1);
  }
}
.systemField-form {
  width: 399px;
}
.custome-form {
  width: 744px;
  overflow-y: auto;
  overflow-x: hidden;
  .custome-form-item {
    width: 50%;
    float: left;
  }
  /deep/.single {
    margin-right: 0;
  }
}
.footer-btn {
  height: 56px;
  line-height: 56px;
  float: right;
}
.page {
  position: relative;
  height: 758px;
  overflow: auto;
  background: #fff;
  padding-bottom: 20px;
}

.select_Template {
  margin-left: 32px;
}

.sheeTitle {
  padding: 16px 20px;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}
.select_type {
  color: #292933;
  font-size: 14px;
  padding-bottom: 20px;
  margin: 4px 61px 15px 20px;
  border-bottom: 1px dashed #e4e4eb;
}
.input_sheetType {
  /*width: 50%;*/
  width: 288px;
  height: 36px;
  margin-left: 12px;
}
.notSheet {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  color: #aeaebf;
  font-size: 14px;
  text-align: center;
  .noSelTxt {
    position: relative;
  }
  .noSelImg {
    width: 100%;
    height: 100%;
  }
}
.formType {
  width: 620px;
  margin: 0 auto;
}
.formType-layout {
  /*width: 620px;*/
  /*max-width: 1140px;*/
  width: 1140px;
  padding: 0 20px;
  /*margin: 0 auto;*/
}
.sheetName {
  display: block;
  text-align: center;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}
.el-form-item {
  margin-bottom: 0px !important;
}
.submBtn {
  margin-left: 152px;
  border-radius: 2px;
}
.addDraft {
  margin-left: 10px;
  border-radius: 2px;
}
.template-col {
  min-height: 1px;
}

// 竖立模式css样式
.erectMode {
  height: auto;
  .row-start {
    margin: 0px 5px 15px 0px;
  }
  .select_Template {
    margin-top: 12px;
    margin-left: 0px;
  }
  .formType-layout {
    width: auto;
    padding: 0;
    .systemField-form {
      width: auto;
    }
  }
  .custome-form {
    width: auto;
  }
}
</style>
