const indexedDB = {
  DATABASE_NAME: 'IM_DATABASE',
  OBJECTSTORE_NAME: 'workbench',
  EXAMPLE_DB: null,
  install(Vue, options) {
    // // 1. 添加全局方法或属性
    // Vue.myGlobalMethod = function () {
    //   // 逻辑...
    // }

    // // 2. 添加全局资源
    // Vue.directive('my-directive', {
    //   bind(el, binding, vnode, oldVnode) {
    //     // 逻辑...
    //   }
    // })

    // // 3. 注入组件
    // Vue.mixin({
    //   created: function () {
    //     // 逻辑...
    //   }
    // })
    const that = this;
    // 4. 添加实例方法
    Vue.prototype.$initIndexedDB = function(methodOptions) {
      let request = window.indexedDB.open(that.DATABASE_NAME, 7); // 打开indexedDB
      request.onerror = function(event) {
        // open异常
        console.log(`打开${that.DATABASE_NAME} 失败 ${event.target.error}`);
      };

      request.onsuccess = function(event) {
        // open成功，把数据库对象保存下来，以后增删改查都需要用到。
        console.log(`打开${that.DATABASE_NAME} 成功！`);
        that.EXAMPLE_DB = event.target.result;
      };

      // 第一次新建版本库时回调
      request.onupgradeneeded = function(event) {
        let db = event.target.result;
        if (!db.objectStoreNames.contains(that.OBJECTSTORE_NAME)) {
          // 判断Store是否存在，否则会报错
          let objectStore = db.createObjectStore(that.OBJECTSTORE_NAME, {
            autoIncrement: true,
            keyPath: 'id'
          });
          // objectStore.createIndex("name", "name", { unique: false });
          // objectStore.createIndex("email", "email", { unique: true });
        }
      };
    };

    Vue.prototype.$indexedDB = {
      // 新增数据  点标识（页面_函数_动作），时间戳，data
      add(data) {
        if (!data) return;
        data.timestamp = new Date().getTime();
        var request = that.EXAMPLE_DB.transaction(
          that.OBJECTSTORE_NAME,
          'readwrite'
        )
          .objectStore(that.OBJECTSTORE_NAME)
          .add(data);

        request.onsuccess = function(event) {
          console.log('数据写入成功');
        };

        request.onerror = function(event) {
          debugger;
          console.log('数据写入失败');
        };
      },
      // 获取全部
      getAll(foo) {
        if (!foo) return;
        let request = that.EXAMPLE_DB.transaction(that.OBJECTSTORE_NAME)
          .objectStore(that.OBJECTSTORE_NAME)
          .getAll();
        request.onsuccess = foo;
      },
      // 删除
      del() {
        let request = that.EXAMPLE_DB.transaction(
          that.OBJECTSTORE_NAME,
          'readwrite'
        )
          .objectStore(that.OBJECTSTORE_NAME)
          .delete(5);

        request.onsuccess = function(event) {
          debugger;
          console.log('数据删除成功');
        };
      },
      // 清空
      clear(foo) {
        if (!foo) return;
        let request = that.EXAMPLE_DB.transaction(
          that.OBJECTSTORE_NAME,
          'readwrite'
        )
          .objectStore(that.OBJECTSTORE_NAME)
          .clear();
        request.onsuccess = foo;
      },
      readAll() {
        let request = that.EXAMPLE_DB.transaction(
          that.OBJECTSTORE_NAME
        ).objectStore(that.OBJECTSTORE_NAME);
        request.openCursor().onsuccess = function(event) {
          var cursor = event.target.result;
          if (cursor) {
            console.log('Id: ' + cursor.key);
            console.log('Name: ' + cursor.value.name);
            console.log('Age: ' + cursor.value.age);
            console.log('Email: ' + cursor.value.email);
            cursor.continue();
          } else {
            console.log('没有更多数据了！');
          }
        };
      }
    };
  }
};

export default indexedDB;
