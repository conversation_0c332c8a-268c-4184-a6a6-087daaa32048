<template>
  <div class="page">
    <xyy-list-page>
      <template slot="header">
        <el-row class="herader-title" type="flex" justify="space-between" align="middle">
          <span style="font-size:16px">等待会话</span>
          <el-button icon="el-icon-upload2" class="export-excel" @click="exportData">导出Excel</el-button>
        </el-row>
        <el-form
          ref="listQuery"
          :rules="rules"
          :model="listQuery"
          :inline="true"
          label-position="right"
          class="search-form"
        >
          <el-row :gutter="10" type="flex" class="row-bg" justify="space-between">
            <!-- 发起时间-->
            <el-form-item label="访客名称" prop="pool" class="nodeClass">
              <el-input v-model="listQuery.pool" maxlength="20"></el-input>
            </el-form-item>
            <!--来源渠道-->
            <el-form-item class="form-item" label="来源渠道" prop="channelId">
              <el-select v-model="listQuery.channelId" placeholder="请选择">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in sheetTypeList"
                  :key="item.id"
                  :label="item.groupName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <!-- 应用 -->
            <el-form-item class="form-item" label="来源应用" prop="applicationType">
              <el-select v-model="listQuery.applicationType" placeholder="全部">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in applicationChannelsData"
                  :key="item.appId"
                  :label="item.appName"
                  :value="item.appId"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item style="text-align:right;float:right">
              <el-button
                plain
                type="primary"
                size="small"
                class="searchCondition"
                @click="handerSearch('listQuery')"
              >查询</el-button>
              <el-button plain size="small" @click="resetForm">重置</el-button>
            </el-form-item>
          </el-row>
        </el-form>
      </template>
      <template slot="body">
        <xyy-table
          :data="list"
          :col="col"
          :list-query="listQuery"
          :is-pagination="true"
          :is-stripe="false"
          :operation="operation"
          @operation-click="operationClick"
          @get-data="handerSearch('listQuery')"
        ></xyy-table>
      </template>
    </xyy-list-page>
    <expor-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    ></expor-tip>
  </div>
</template>

<script>
import exporTip from '@/views/work-sheet/components/exportTip';
import {
  findDialogQueueList,
  dialogQueueStart,
  getSources
} from '@/api/monitor/index.js';
export default {
  name: 'Waitingsession',
  components: {
    exporTip
  },
  data() {
    return {
      changeExport: false,
      list: [], // 表单数据
      listQuery: {
        current: 1,
        size: 10,
        total: 0,
        channelId: '',
        pool: '',
        applicationType: ''
      },
      rules: {},
      col: [
        {
          index: 'userName',
          name: '访客名称',
          resizable: true
        },
        {
          index: 'userProvince',
          name: '访客所在省区',
          resizable: true
        },
        {
          index: 'appName',
          name: '来源应用',
          resizable: true
        },
        {
          index: 'channelName',
          name: '来源渠道',
          resizable: true
        },
        {
          index: 'queueTime',
          name: '等待时长',
          resizable: true
        },
        {
          index: 'operation',
          name: '操作',
          width: 250,
          operation: true
        }
      ],
      operation: [
        {
          name: '发起会话',
          type: 1
        }
      ],
      sheetTypeList: [
        { id: 1000, groupName: 'PC' },
        // { id: 1001, groupName: '微信服务号' },
        { id: 1002, groupName: '微信H5' },
        { id: 1003, groupName: 'APP' }
      ],
      applicationChannelsData: []
    };
  },
  mounted() {
    // this.handerSearch('listQuery');
    // this.getNowTimeDate();
    this.getSourcesList();
  },
  methods: {
    handleChangeExport(info) {
      const that = this;
      this.changeExport = false;
      if (info === 'go') {
        that.$router.push({
          path: '/other/index/'
        });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    // exportData() {
    //   const that = this;
    //   if (!that.list || !that.list.length) {
    //     that.$message({
    //       type: 'info',
    //       message: '无导出数据！'
    //     });
    //     return false;
    //   }
    //   // const { dataRange } = this.listQuery;
    //   const params = JSON.parse(JSON.stringify(this.listQuery));
    //   nodeExportData(params).then(res => {
    //     if (res.code !== 1) {
    //       this.$XyyMessage.warning(res.msg);
    //       return;
    //     }
    //     this.changeExport = true;
    //   });
    // },
    exportData() {
      const that = this;
      if (!that.list || !that.list.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      this.url = `${process.env.BASE_API_IM}/dialogQueue/export?channelId=${that.listQuery.channelId}&userName=${that.listQuery.pool}&appId=${that.listQuery.applicationType}`;
      if (
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
      ) {
        this.url =
          this.url +
          `&businessPartCode=${this.$store.getters.channel.businessPartCode}`;
      }
      const a = document.createElement('a');
      a.href = this.url;
      a.click();
    },
    // 点击搜索事件
    handerSearch(name) {
      const that = this;
      this.$refs[name].validate(valid => {
        if (valid) {
          const params = {
            current: this.listQuery.current,
            size: this.listQuery.size,
            channelId: this.listQuery.channelId,
            appId: this.listQuery.applicationType,
            userName: this.listQuery.pool
          };

          findDialogQueueList(params).then(res => {
            const dataListList = res.data;
            if (
              !dataListList &&
              typeof dataListList !== 'undefined' &&
              dataListList !== 0
            ) {
              this.$XyyMessage.success('暂时无数据');
              this.list = [];
              return;
            }
            if (res.code !== 1) {
              this.$XyyMessage.warning(res.msg);
              return;
            }
            if (res.data.list && res.data.list.length === 0) {
              this.$XyyMessage.success('暂时无数据');
            }
            this.list = res.data.records;
            const { total, size, current } = res.data;
            that.listQuery = {
              ...that.listQuery,
              current: current,
              size: size,
              total: Number(total)
            };
          });
        }
      });
    },
    resetForm() {
      this.listQuery = {
        current: 1,
        size: 10,
        total: 0,
        channelId: '',
        applicationType: '',
        pool: ''
      };
    },
    operationClick: function(type, row) {
      switch (type) {
        case 1:
          this.dialogQueueStart(row);
          break;
      }
    },
    /**
     * 跳转到在线会话
     */
    toImViewMain(data) {
      if (data) {
        this.$router.replace({
          path: `/imcontainer/list`
        });
      }
    },
    /**
     * 客服在等待中列表点击发起会话
     */
    dialogQueueStart(data) {
      dialogQueueStart({ dialogId: data.dialogId }).then(res => {
        if (res.code === 1) {
          this.$XyyMessage.success('发起会话成功');
          this.$XyyMsg({
            title: '提示',
            content: '发起成功！是否打开在线会话窗口？',
            onSuccess: () => {
              this.toImViewMain(data);
            }
          });
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 获取来源渠道数据
    getSourcesList() {
      getSources()
        .then(response => {
          this.applicationChannelsData = response.data;
        })
        .catch(function(error) {
          console.log(error);
        });
    }
  }
};
</script>

<style scoped lang="scss">
/deep/.nodeClass {
  /deep/.el-form-item__label {
    padding-left: 10px;
  }
}
/deep/.el-table {
  td.is-hidden {
    .cell {
      visibility: inherit !important;
    }
  }
}
/deep/.el-form-item__label {
  font-weight: 500;
}
/deep/.el-table {
  /deep/.el-table__fixed {
    height: auto !important;
    bottom: 16px;
    margin-bottom: 0 !important;
  }
  /deep/.el-table__fixed-body-wrapper {
    top: 44px !important;
  }
}

/deep/.page-header {
  padding-bottom: 0 !important;
}
.search-form {
  border-bottom: 1px dashed #e4e4eb;
}
.herader-title {
  font-size: 14px;
  color: #393943;
  padding-bottom: 20px;
  border-bottom: 1px dashed #e4e4eb;
  margin-bottom: 20px;
}
/deep/.el-table__body tr td {
  border-bottom: 1px solid #ebeef5;
  background: #fff;
}
/deep/.el-date-editor {
  /*width: 450px;*/
}
/deep/.el-input__inner {
  height: 36px !important;
  line-height: 36px !important;
}
/deep/.el-button {
  height: 36px;
}
.searchCondition.is-plain {
  background: rgba(59, 149, 168, 1);
  color: #fff;
}
/deep/.searchCondition.is-plain:hover {
  background: rgba(40, 126, 144, 1);
}
/deep/.el-table th {
  padding-top: 10px;
  padding-bottom: 10px;
  text-align: left;
  padding-right: 19px;
  &:first-child {
    text-align: left;
  }
  p {
    margin: 0;
    height: 20px;
  }
  small {
    color: #909399;
    font-size: 12px;
    height: 17px;
  }
}
</style>
