<template>
  <div class="app-wrapper">
    <sidebar class="sidebar-container" />
    <div
      :style="{'margin-left':$store.getters.isChild?'200px':'50px'}"
      class="main-container hasTagsView"
    >
      <div class="fixed-header">
        <navbar />
        <!-- <tags-view /> -->
      </div>
      <app-main />
    </div>

    <!-- 新消息音频 -->
    <audio id="audioPlay" preload="true" muted :src="noticeAudioSrc"></audio>

    <div
      class="superSearchBtn"
      v-if="businessPartCode!=='S00009994'"
      :class="{superSearchBtn_:drawer}"
      @click="openDrawer"
      :style="{ 'right':drawer ? drawerSize:'0px' }"
    >
      <span v-show="!drawer">
        <img src="@/assets/icons/knowledge.png" alt />知识库
      </span>
      <i v-show="drawer" class="el-icon-arrow-right"></i>
    </div>

    <el-drawer
      :size="drawerSize"
      :show-close="false"
      :with-header="false"
      :modal="false"
      :visible.sync="drawer"
      :wrapperClosable="false"
      :style="{'width':'485px','left':'auto'}"
    >
      <el-tabs class="knowledgeTabs" @tab-click="getCollection" v-model="activeName">
        <el-tab-pane label="知识库" name="first">
          <div class="knowledgeBox">
            <div class="searchClass">
              <el-input
                clearable
                placeholder="请输入内容"
                v-model="searchKey"
                @input="hittypeShow = true;treeShow=false"
                @focus="searchFocus"
                @blur="hittypeShow = false"
                @keyup.enter.native="hitTypeHandle(0)"
                class="input-with-select"
              >
                <template slot="prepend">
                  <div @click="treeShow = !treeShow;hittypeShow=false">
                    <span class="hittypeName">{{treeName}}</span>
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </div>
                </template>
              </el-input>

              <!-- 命中类型 选择查询框 -->
              <transition name="fade">
                <div class="hitTypeBox" v-show="hittypeShow">
                  <div @click="hitTypeHandle(0)" class="sitem">
                    查询所有包含「
                    <span>{{searchKey}}</span>」的知识
                  </div>
                  <div @click="hitTypeHandle(1)" class="sitem">
                    查询标题包含「
                    <span>{{searchKey}}</span>」的知识
                  </div>
                  <div @click="hitTypeHandle(2)" class="sitem">
                    查询内容包含「
                    <span>{{searchKey}}</span>」的知识
                  </div>
                  <div @click="hitTypeHandle(3)" class="sitem">
                    查询标签包含「
                    <span>{{searchKey}}</span>」的知识
                  </div>
                </div>
              </transition>

              <!-- 树形搜索 -->
              <transition name="fade">
                <div v-show="treeShow" class="treeBox">
                  <div
                    class="allType"
                    :class="{checked:treeName=='全部分类'}"
                    @click="treeClickHandle()"
                  >全部分类</div>
                  <el-tree
                    :data="treeData"
                    node-key="id"
                    :props="defaultProps"
                    :check-on-click-node="true"
                    :highlight-current="true"
                    @node-click="treeClickHandle"
                    ref="tree"
                    slot="prepend"
                  ></el-tree>
                </div>
              </transition>

              <!-- 数据总数&排序类型 -->
              <div class="resultTypeBox">
                <div>共 {{ knowledge.total }} 条数据</div>
                <div class="descType">
                  <el-dropdown trigger="click" @command="sortTypeHandle">
                    <span class="el-dropdown-link">
                      按{{sortTypeName}}
                      <i class="el-icon-arrow-down el-icon--right"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="publishTime|发布时间排序">发布时间排序</el-dropdown-item>
                      <el-dropdown-item command="modifyTime|更新时间排序">更新时间排序</el-dropdown-item>
                      <el-dropdown-item command="hot|热度排序">热度排序</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
            </div>
            <!-- 查询结果 -->
            <div
              class="resultBox"
              v-infinite-scroll="loadmoreKnowledge"
              :infinite-scroll-disabled="knowledge.knowledgeScrollDisabled"
            >
              <div
                v-for="(item,index) in knowledge.list"
                :key="item.knowledgeManagementId"
                :kid="item.knowledgeManagementId"
                class="resultItem"
              >
                <div class="header">
                  <div
                    class="title"
                    v-html="item.highlight.knowledgeTitle ||  item.knowledgeTitle"
                    @click="stretchOn({type:1,index})"
                  ></div>
                  <div class="icons">
                    <img
                      @click="openOn(item.knowledgeManagementId)"
                      src="@/assets/icons/fullScreen.png"
                    />
                    <img
                      :class="{arrow:item.open}"
                      @click="stretchOn({type:1,index})"
                      src="@/assets/icons/downArrow.png"
                    />
                  </div>
                </div>
                <transition
                  :duration="200"
                  name="fade"
                  @after-enter="knowledgeStretch(item.knowledgeManagementId)"
                >
                  <div
                    class="content"
                    v-show="item.open"
                    v-html="item.highlight.knowledgeContent || item.knowledgeContent"
                  ></div>
                </transition>
                <div class="tagBox" v-if="item.highlight.knowledgeSynonyms">
                  <div class="tags" v-html="findtagArray(item.highlight.knowledgeSynonyms)"></div>
                </div>
              </div>
              <div class="empty-data-container" v-if="knowledge.list.length == 0">
                <img :src="emptyDataImgUrl" />
              </div>
              <span class="loads" v-if="knowledge.list.length > 0 && knowledge.loading">加载中...</span>
              <span
                class="loads"
                v-if="knowledge.list.length > 0 && knowledge.list.length >= knowledge.total"
              >- 没有更多了 -</span>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane :lazy="true" label="我的收藏" name="second">
          <div class="collectionBox">
            <div class="searchClass">
              <!-- 数据总数&排序类型 -->
              <div class="resultTypeBox">
                <div>共 {{ collection.total }} 条数据</div>
                <div class="descType">
                  <el-dropdown trigger="click" @command="sortRuleHandle">
                    <span class="el-dropdown-link">
                      按{{sortRuleName}}
                      <i class="el-icon-arrow-down el-icon--right"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="createTime|发布时间排序">发布时间排序</el-dropdown-item>
                      <el-dropdown-item command="modifyTime|更新时间排序">更新时间排序</el-dropdown-item>
                      <el-dropdown-item command="hot|热度排序">热度排序</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
            </div>

            <div
              class="resultBox"
              v-infinite-scroll="getCollection"
              :infinite-scroll-disabled="collection.collectionScrollDisabled"
            >
              <div
                v-for="(item,index) in collection.list"
                :key="item.id"
                :kid="item.id"
                class="resultItem"
              >
                <div class="header">
                  <div class="title" @click="stretchOn({type:2,index,id:item.id})">
                    <img
                      @click.stop="collectionCancel(item.id,index)"
                      src="@/assets/icons/collection.png"
                    />
                    {{item.title}}
                  </div>
                  <div class="icons">
                    <img @click="openOn(item.id)" src="@/assets/icons/fullScreen.png" />
                    <img
                      :class="{arrow:item.open}"
                      @click="stretchOn({type:2,index,id:item.id})"
                      src="@/assets/icons/downArrow.png"
                    />
                  </div>
                </div>
                <transition :duration="200" name="fade" @after-enter="knowledgeStretch(item.id)">
                  <div v-show="item.open">
                    <div class="content" v-html="item.knowledgeContent"></div>
                    <div
                      v-if="!item.knowledgeContent"
                      style="text-align:center;width:100%;line-height:40px;"
                    >
                      <i class="el-icon-loading"></i>
                    </div>
                  </div>
                </transition>
              </div>
              <div class="empty-data-container" v-if="collection.list.length == 0">
                <img :src="emptyDataImgUrl" />
              </div>
              <span class="loads" v-if="collection.list.length > 0 && collection.loading">加载中...</span>
              <span
                class="loads"
                v-if="collection.list.length > 0 && collection.list.length >= collection.total"
              >- 没有更多了 -</span>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
  </div>
</template>

<script>
import { Navbar, Sidebar, AppMain, TagsView } from './components';
import {
  listAllClassification,
  searchKnowledgeList,
  getKnowledgeManagementDetail,
  favoriteCancel,
  favoriteSelectList
} from '@/api/knowledge_base';
import emptyDataImgUrl from '@/assets/common/empty-data-table.png';
export default {
  name: 'Layout',
  provide() {
    return {
      compLayout: this
    };
  },
  components: {
    Navbar,
    Sidebar,
    AppMain
    // TagsView
  },
  data() {
    return {
      noticeAudioSrc: '', //audio的src
      wsKFOnlineStatusRefrest: false, //是否刷新客服在线状态

      emptyDataImgUrl, // 无数据占位图
      drawer: false, // 搜索栏是否展示
      drawerSize: '480px', // 展示宽度
      activeName: 'first', // 知识、收藏切换
      searchKey: '', // 搜索关键字
      searchType: 0, // 搜索类型  0:查所有; 1:查标题; 2:查内容; 3:查标签
      sortType: 'publishTime', // 排序类型
      sortTypeName: '发布时间排序', // 排序类型Name
      sortRule: 'createTime',
      sortRuleName: '发布时间排序',
      hittypeShow: false, // 搜索项展示
      treeData: [], // 全部分类树
      treeName: '全部分类', // 选中树name
      treeId: '', // 选中分类树id
      treeCategory: [], // 分类选中ID目录组
      treeShow: false, // 全部分类树展示
      defaultProps: {
        children: 'children',
        label: 'name'
      },

      // 知识库数据集
      knowledge: {
        total: 0,
        pageNum: 1,
        pageSize: 20,
        loading: false,
        knowledgeScrollDisabled: true, // 加载更多知识 是否禁用
        list: [] // 知识搜索结果  <span class="highlight">这是标记</span>
      },

      // 我的收藏数据集
      collection: {
        total: 0,
        pageSize: 20,
        loading: false,
        collectionScrollDisabled: true, // 加载更多收藏 是否禁用
        list: [] // 我的收藏列表
      },
      businessPartCode: ''
    };
  },
  created() {
    this.createComponentsMediator();
    this.getBusinessPartCode();
    //设置audio的src
    console.log(process.env.NODE_ENV);
    switch (process.env.NODE_ENV) {
      case 'development':
        this.noticeAudioSrc = '../../static/notice.mp3';
        break;
      default:
        this.noticeAudioSrc = '../../pc/static/notice.mp3';
        break;
    }

    this.search(); // 加载全部知识
    this.getCollection(); // 加载收藏列表
  },
  methods: {
    /**
     * 展开抽屉
     */
    openDrawer() {
      this.drawer = !this.drawer;
      if (!this.treeData || this.treeData.length == 0) {
        this.getTreeData(); // 加载树形分类
      }
    },

    /**
     * 加载树形分类
     */
    getTreeData() {
      listAllClassification().then(res => {
        if (res && res.code === 1) {
          this.treeData = res.data;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    /**
     * 搜索知识
     */
    search() {
      const that = this;
      this.treeShow = false;
      this.hittypeShow = false;
      let params = {
        desc: true,
        category: this.treeId,
        pageNum: this.knowledge.pageNum,
        pageSize: this.knowledge.pageSize,
        keyword: this.searchKey,
        searchType: this.searchType,
        sortFiled: this.sortType
      };
      that.knowledge.loading = true;
      that.knowledge.knowledgeScrollDisabled = true;
      // 搜索知识
      searchKnowledgeList(params).then(res => {
        that.knowledge.loading = false;
        if (res.code === 1) {
          that.knowledge.list = that.knowledge.list.concat(res.data.list);
          that.knowledge.total = res.data.total;
          that.knowledge.knowledgeScrollDisabled =
            res.data.total && that.knowledge.list.length < res.data.total
              ? false
              : true;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    /**
     * 加载更多知识
     */
    loadmoreKnowledge() {
      this.knowledge.pageNum++;
      this.search();
    },

    /**
     * 加载收藏列表
     */
    getCollection(e) {
      const that = this;
      if (e && e.name !== 'second') {
        return;
      } else if (e && e.name === 'second') {
        that.collection.list = [];
      }
      let params = {
        sortFiled: that.sortRule,
        sortRule: 1,
        from: that.collection.list.length,
        pageSize: 20,
        loadContent: true
      };

      this.collection.loading = true;
      this.collection.collectionScrollDisabled = true;
      favoriteSelectList(params).then(res => {
        this.collection.loading = false;
        if (res.code === 1) {
          that.collection.list = that.collection.list.concat(res.data.list);
          that.collection.total = res.data.total;
          that.collection.collectionScrollDisabled =
            res.data.total &&
            res.data.list.length > 0 &&
            that.collection.list.length < res.data.total
              ? false
              : true;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    /**
     * 匹配命中tag
     */
    findtagArray(tags) {
      const ary = tags.match(/<span class='highlight'>(.*?)<\/span>/g);
      const tag = ary.length ? ary.join() : '';
      return tag;
    },

    /**
     * 聚焦搜索框处理逻辑
     */
    searchFocus() {
      if (this.searchKey) {
        this.hittypeShow = true;
      }
    },

    /**
     * 树形分类搜索
     */
    treeClickHandle(data, e) {
      this.treeCategory = [];
      if (data) {
        this.findParent(e);
        this.treeId = this.treeCategory.reverse().join(',');
        this.treeName = data.name;
      } else {
        this.treeId = '';
        this.treeName = '全部分类';
        this.$refs.tree.setCurrentKey();
      }
      this.knowledge.list = [];
      this.knowledge.pageNum = 1;
      this.search();
    },

    /**
     * 树形分类-递归查找父级
     */
    findParent(node) {
      if (node.data && node.data.id) {
        this.treeCategory.push(node.data.id);
      }

      if (node.parent) {
        this.findParent(node.parent);
      }
    },

    /**
     * 命中类型选中处理
     */
    hitTypeHandle(searchType) {
      this.searchType = searchType || 0;
      this.knowledge.list = [];
      this.knowledge.pageNum = 1;
      this.search();
    },

    /**
     * 排序类型选中处理
     */
    sortTypeHandle(command) {
      if (command) {
        const type = command.split('|');
        this.sortType = type[0];
        this.sortTypeName = type[1];
        this.knowledge.list = [];
        this.knowledge.pageNum = 1;
        this.search();
      }
    },

    /**
     * 收藏排序类型选中处理
     */
    sortRuleHandle(command) {
      if (command) {
        const type = command.split('|');
        this.sortRule = type[0];
        this.sortRuleName = type[1];
        this.collection.list = [];
        this.getCollection();
      }
    },

    /**
     * 展开知识||收藏，并定位到高亮位置
     * type:1 知识列表，2 收藏列表
     * index:数据下标
     */
    stretchOn({ type, index, id }) {
      if (type == 1) {
        // 展开文章详情
        if (this.knowledge.list[index]) {
          if (this.knowledge.list[index].open) {
            this.knowledge.list[index].open = false;
          } else {
            this.knowledge.list[index].open = true;
          }
        }
      } else {
        // 展开收藏，如果没有内容就单次查询
        if (this.collection.list[index].open) {
          this.collection.list[index].open = false;
        } else {
          if (!this.collection.list[index].knowledgeContent) {
            getKnowledgeManagementDetail({ id }).then(res => {
              if (res.code === 1) {
                this.collection.list[index].knowledgeContent = res.data.content;
              }
            });
          }
          this.collection.list[index].open = true;
        }
      }
      this.$forceUpdate();
    },

    /**
     * 展开知识详情，transtion 进入后-钩子
     */
    knowledgeStretch(id) {
      const dom = document
        .querySelector('.knowledgeBox')
        .querySelector(`div[kid="${id}"] .content span.highlight`);
      if (id && dom) {
        dom.scrollIntoView({
          behavior: 'smooth', // 平滑过渡
          block: 'center' // 上边框与视窗顶部平齐。默认值
        });
      }
    },

    /**
     * 取消收藏
     */
    collectionCancel(id, index) {
      favoriteCancel({
        knowledgeId: id
      }).then(res => {
        if (res.code === 1) {
          this.collection.list.splice(index, 1);
          this.collection.total--;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    /**
     * 打开知识详情
     */
    openOn(id) {
      this.$router.push({
        path: `/knowledge_base/knowledgeDetails/${id}`,
        query: { templateId: id }
      });
    },
    /**
     * 小九云根据业务线判断不显示右侧知识库按钮
     */
    getBusinessPartCode() {
      if (
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
      ) {
        this.businessPartCode = this.$store.getters.channel.businessPartCode;
      }
    }
  }
};
</script>
<style  lang="scss">
.el-drawer {
  box-shadow: 0 0px 6px 0px rgba(0, 0, 0, 0.2);
}
.knowledgeTabs {
  padding-top: 8px;
  height: 100%;
  overflow: hidden;
  margin-top: 17px;

  .el-tabs__nav {
    margin-left: 20px;
  }

  .knowledgeBox,
  .collectionBox {
    width: 100%;
    box-sizing: border-box;
    .searchClass {
      padding: 0 20px;
    }
    .input-with-select .el-input-group__prepend {
      background-color: #fff;
      width: 150px;
      overflow: hidden;
      padding: 0 12px;
      .hittypeName {
        width: 110px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: inline-block;
        height: 20px;
        line-height: 26px;
        &:hover {
          cursor: pointer;
        }
      }
      i {
        margin-left: 0;
        position: relative;
        top: -2px;
        &:hover {
          cursor: pointer;
        }
      }
    }

    .hitTypeBox {
      position: fixed;
      width: 291px; // 291px
      z-index: 9;
      margin: 2px 0 0 150px;
      background: #ffffff;
      border: 1px solid #e4e4eb;
      border-radius: 3px;
      .sitem {
        width: 100%;
        height: 36px;
        line-height: 36px;
        padding-left: 11px;
        background: #ffffff;
        box-sizing: border-box;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        color: #575766;
        &:hover {
          cursor: pointer;
          background: #f4f7fa;
        }
        span {
        }
      }
    }

    .treeBox {
      position: fixed;
      width: 438px;
      z-index: 9;
      margin-top: 2px;
      padding-bottom: 5px;
      min-height: 176px;
      background: #ffffff;
      border: 1px solid #e4e4eb;
      border-radius: 3px;
      .allType {
        margin: 4px 0 0;
        width: 100%;
        line-height: 36px;
        opacity: 1;
        box-shadow: 0px 4px 8px 0px rgba(175, 175, 175, 0.2);
        padding: 0 19px;
        font-size: 14px;
        font-family: PingFangSC, PingFrangSC-Regular;
        color: #606266;
        &:hover {
          cursor: pointer;
          background: #f4f7fa;
        }
      }
      .checked {
        background: #e1eef1;
        color: #3b95a8;
      }
      .el-tree-node__content {
        height: 36px;
      }
      .is-current {
        background: #e1eef1;
        color: #3b95a8 !important;
      }
    }

    .resultTypeBox {
      width: 100%;
      height: 40px;
      line-height: 40px;
      display: flex;
      justify-content: space-between;
      color: #909399;
      font-size: 12px;
    }

    .el-dropdown-link {
      cursor: pointer;
    }

    .resultItem {
      background: #f4f7fa;
      padding: 0 12px;
      margin-bottom: 12px;
      font-size: 14px;
      width: 440px;
      .header {
        font-family: PingFangSC, PingFangSC-Medium;
        display: flex;
        justify-content: space-between;
        &:hover {
          cursor: pointer;
        }
        .title {
          width: 80%;
          height: 40px;
          line-height: 42px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          color: #575766;
          img {
            width: 16px;
            height: 16px;
            position: relative;
            top: 2px;
            margin-right: 7px;
            &:hover {
              cursor: pointer;
            }
          }
        }
        .icons {
          width: 70px;
          align-self: center;
          text-align: right;
          img {
            width: 16px;
            height: 16px;
            margin-left: 10px;
            transition: all 0.6s ease;
            &:hover {
              cursor: pointer;
            }
          }

          .arrow {
            transform: rotate(180deg);
          }
        }
      }
      .content {
        padding-bottom: 10px;
        border-top: 1px solid #e4e4eb;
        padding-top: 10px;
        * {
          max-width: 100%;
        }
        table,
        table tr th,
        table tr td {
          border: 1px solid #ccc;
        }
        table {
          border-collapse: collapse;
        }
      }
      .tagBox {
        width: 100%;
        padding: 10px 0;
        border-top: 1px solid #e4e4eb;
        display: flex;
        .tags {
          min-width: 30px;
          height: 26px;
          padding: 0 8px;
          line-height: 24px;
          text-align: center;
          border: 1px solid #3b95a8;
          border-radius: 3px;
        }
      }
    }

    .loads {
      width: 100%;
      text-align: center;
      line-height: 28px;
      font-size: 12px;
      color: #909399;
      display: block;
    }

    .highlight {
      color: #fa6400 !important;
    }
  }

  .knowledgeBox {
    .resultBox {
      overflow-y: auto;
      height: calc(98vh - 160px);
      padding: 0 20px;
    }
  }

  .collectionBox {
    .resultBox {
      overflow-y: auto;
      height: calc(98vh - 110px);
      padding: 0 20px;
    }
    .resultTypeBox {
      height: 14px !important;
      line-height: 14px !important;
      margin-bottom: 12px !important;
    }
  }

  //无数据样式
  .empty-data-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 305px;
      height: 219px;
    }
  }
}

.s-message-wrap .s-message-content .s-message-footer .i {
  font-size: 14px;
}
</style>

<style rel="stylesheet/scss" lang="scss" scoped>
@import '@/styles/mixin.scss';
@import '@/styles/variables.scss';

.main-container {
  transition: margin-left 1s;
  -moz-transition: margin-left 1s; /* Firefox 4 */
  -webkit-transition: margin-left 1s; /* Safari 和 Chrome */
  -o-transition: margin-left 1s;
}
.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;
}
.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 2000;
  width: 100%;
  transition: width 0.28s;
  height: 64px;
}
.superSearchBtn {
  position: absolute;
  right: 0px;
  top: 75px;
  z-index: 9001;
  transition: all 0.3s ease;

  right: 480px;
  width: 86px;
  height: 40px;
  line-height: 40px;
  opacity: 1;
  background: #ffffff;
  border-radius: 20px 0 0 20px;
  box-shadow: -5px 0px 5px 0px rgba(0, 0, 0, 0.2);
  text-align: center;
  font-size: 14px;
  font-family: PingFangSC, PingFangSC-Regular;
  color: #575766;
  &:hover {
    cursor: pointer;
  }

  span {
    display: flex;
    align-items: center;
    height: 100%;
    overflow: hidden;
    img {
      width: 16px;
      height: 16px;
      margin: 0 4px 0 12px;
    }
  }
}

.superSearchBtn_ {
  width: 40px;
}
</style>
