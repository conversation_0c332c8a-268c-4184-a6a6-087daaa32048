import Layout from '@/views/layout/Layout';
export default [
  {
    path: '/worksheet',
    component: Layout,
    name: 'worksheet',
    meta: {
      title: '工单',
      affix: true,
      code: 'menu:cs:workorderconfig',
      mark: 'config'
    },
    children: [
      {
        path: 'systemFields',
        name: 'systemFields',
        meta: {
          title: '系统字段',
          code: 'menu:cs:systemfield',
          mark: 'config',
          componentName: 'SystemFields'
        },
        component: () => import('@/views/fields/system-fields')
      },
      {
        path: 'customFields',
        name: 'customFields',
        meta: {
          title: '自定义字段',
          code: 'menu:cs:customfield',
          mark: 'config',
          componentName: 'CustomFields'
        },
        component: () => import('@/views/fields/custom-fields')
      },
      {
        path: 'newAddFields/:times',
        name: 'newAddFields',
        affix: true,
        meta: {
          title: '新建字段',
          // code: 'menu:cs:customfield',
          mark: 'config'
        },
        component: () => import('@/views/fields/new-add-fields'),
        hidden: true
      },
      {
        path: 'editFields/:time',
        name: 'editFields',
        affix: true,
        meta: {
          title: '编辑字段',
          // code: 'menu:cs:customfield',
          mark: 'config'
        },
        component: () => import('@/views/fields/edit-fields'),
        hidden: true
      },
      {
        path: 'templateList',
        name: 'templateList',
        meta: {
          title: '模板',
          code: 'menu:cs:template',
          mark: 'config',
          componentName: 'TemplateList'
        },
        component: () => import('@/views/templates/list')
      },
      {
        path: 'templateBase/:id',
        name: 'templateBase',
        meta: {
          title: '新建模板',
          code: 'menu:cs:template',
          mark: 'config'
        },
        component: () => import('@/views/templates/base'),
        hidden: true
      },
      {
        path: 'templateEditor/:id',
        name: 'templateEditor',
        meta: {
          title: '新建模板',
          code: 'menu:cs:template',
          mark: 'config'
        },
        component: () => import('@/views/templates/editor'),
        hidden: true
      },
      {
        path: 'templateConfig/:id',
        name: 'templateConfig',
        meta: {
          title: '新建模板',
          code: 'menu:cs:template',
          mark: 'config'
        },
        component: () => import('@/views/templates/config'),
        hidden: true
      },
      {
        path: 'userGroupList',
        name: 'userGroupList',
        meta: {
          title: '用户组',
          code: 'menu:cs:usergroup',
          mark: 'config',
          componentName: 'UserGroupList'
        },
        component: () => import('@/views/user-group/list')
      },
      {
        path: 'userGroupAdd/:t',
        name: 'userGroupAdd',
        hidden: true,
        meta: {
          title: '新增用户组',
          code: 'menu:cs:usergroup',
          mark: 'config'
        },
        component: () => import('@/views/user-group/add')
      },
      {
        path: 'userGroupEdit/:id',
        name: 'userGroupEdit',
        hidden: true,
        meta: {
          title: '编辑用户组',
          code: 'menu:cs:usergroup',
          mark: 'config'
        },
        component: () => import('@/views/user-group/add')
      },
      {
        path: 'dotmanagerindex',
        name: 'dotManagerIndex',
        meta: {
          title: '节点',
          code: 'menu:cs:nodepool',
          mark: 'config',
          componentName: 'Dotmanagerindex'
        },
        component: () => import('@/views/dotmanager/index')
      },
      {
        path: 'dotmanagernewdot/:id',
        name: 'dotManagerNewDot',
        hidden: true,
        meta: {
          keepAlive: true,
          title: '新建节点',
          code: 'menu:cs:nodepool',
          mark: 'config'
        },
        component: () => import('@/views/dotmanager/newdot')
      },
      {
        path: 'dotmanagerupdate/:id',
        name: 'dotManagerUpdate',
        hidden: true,
        meta: {
          title: '编辑节点',
          code: 'menu:cs:nodepool',
          mark: 'config'
        },
        component: () => import('@/views/dotmanager/newdot')
      },

      {
        path: 'formList',
        name: 'formList',
        meta: {
          title: '表单应用',
          code: 'menu:cs:formapplication',
          mark: 'config',
          componentName: 'FormList'
        },
        component: () => import('@/views/form/form-list')
      },
      {
        path: 'newForm/:id',
        name: 'newForm',
        hidden: true,
        meta: {
          title: '新建表单应用',
          code: 'menu:cs:formapplication',
          mark: 'config'
        },
        component: () => import('@/views/form/form-add')
      },
      {
        path: 'editForm/:id',
        name: 'editForm',
        hidden: true,
        meta: {
          title: '编辑表单应用',
          code: 'menu:cs:formapplication',
          noCache: true,
          mark: 'config'
        },
        component: () => import('@/views/form/form-edit')
      },
      {
        path: 'typeList',
        name: 'typeList',
        meta: {
          title: '表单类型',
          code: 'menu:cs:formtype',
          mark: 'config',
          componentName: 'FormTypeList'
        },
        component: () => import('@/views/form/form-type-list')
      },
      {
        path: 'satisfactionList',
        name: 'satisfactionList',
        meta: {
          title: '满意度评价',
          code: 'menu:cs:satisfaction',
          mark: 'config',
          componentName: 'SatisfactionList'
        },
        component: () => import('@/views/satisfaction/list')
      },
      {
        path: 'satisfactionEdit/:id',
        name: 'satisfactionEdit',
        meta: {
          title: '新建评价模板',
          code: 'menu:cs:satisfaction',
          mark: 'config'
        },
        component: () => import('@/views/satisfaction/edit'),
        hidden: true
      },
      {
        path: 'configIndex',
        name: 'ConfigIndx',
        meta: {
          title: '参数配置',
          code: 'menu:cs:paramconfig',
          mark: 'config',
          componentName: 'ConfigIndx'
        },
        component: () => import('@/views/config/index')
      },
      {
        path: 'configuration',
        name: 'configuration',
        meta: {
          title: '问题分类-模板配置',
          code: 'menu:cs:paramconfig',
          mark: 'config',
          componentName: 'configuration'
        },
        hidden: true,
        component: () => import('@/views/config/configuration')
      },
      {
        path: 'configOrderNum',
        name: 'configOrderNum',
        meta: {
          title: '领取工单数配置',
          code: 'menu:cs:paramconfig',
          mark: 'config',
          componentName: 'configOrderNum'
        },
        hidden: true,
        component: () => import('@/views/config/configOrderNum')
      },
      {
        path: 'configOrderTag',
        name: 'configOrderTag',
        meta: {
          title: '工单标签配置',
          code: 'menu:cs:paramconfig',
          mark: 'config',
          componentName: 'configOrderTag'
        },
        hidden: true,
        component: () => import('@/views/config/configOrderTag')
      },
      {
        path: 'config-start',
        name: 'ConfigStart',
        meta: {
          title: '重复工单校验',
          // code: 'menu:cs:repeatworkordercheck',
          code: 'menu:cs:paramconfig',
          mark: 'config',
          componentName: 'ConfigStart'
        },
        component: () => import('@/views/config/config-start'),
        hidden: true
      }
    ]
  },

  {
    path: '/other',
    component: Layout,
    name: 'other',
    meta: {
      title: '其他',
      icon: 'other',
      affix: true,
      code: 'menu:cs:download',
      mark: 'config'
    },
    children: [
      {
        path: 'index',
        name: 'otherIndx',
        meta: {
          title: '下载管理',
          code: 'menu:cs:download',
          mark: 'config',
          componentName: 'OtherIndex'
        },
        component: () => import('@/views/other/index')
      }
    ]
  },

  /**
   * 药品召回单路由
   */
  {
    path: '/recall',
    component: Layout,
    name: 'recall',
    redirect: '/recall/drugRecall',
    meta: {
      title: '药品召回单',
      affix: true,
      code: 'menu:cs:shopdrugrecall',
      mark: 'config'
    },
    children: [
      {
        path: 'drugRecall',
        name: 'shopDrugRecall',
        meta: {
          title: '药品召回单',
          code: 'menu:cs:shopdrugrecall',
          mark: 'config'
        },
        component: () => import('@/views/shop/drugRecall/list')
      },
      {
        path: 'newdrugRecall',
        name: 'newshopDrugRecall',
        meta: {
          title: '新建药品召回单',
          //code: 'menu:cs:newshopDrugRecall',
          mark: 'config'
        },
        component: () => import('@/views/shop/drugRecall/newdruglist')
      }
    ]
  },

  {
    path: '/message',
    component: Layout,
    name: 'message',
    redirect: '/message/list',
    meta: {
      title: '全部通知',
      icon: '',
      affix: true
    },
    children: [
      {
        path: 'list',
        name: 'messageList',
        meta: {
          title: '全部通知'
        },
        component: () => import('@/views/message/list')
      }
    ]
  }
];
