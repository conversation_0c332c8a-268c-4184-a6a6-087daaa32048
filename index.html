<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <title>客服系统</title>
  <!-- <script type = "text/javascript"> (function() { if (window.zhuge) return; window.zhuge = []; window.zhuge.methods = "_init identify track trackRevenue getDid getSid getKey setSuperProperty setUserProperties setWxProperties setPlatform".split(" "); window.zhuge.factory = function(b) { return function() { var a = Array.prototype.slice.call(arguments); a.unshift(b); window.zhuge.push(a); return window.zhuge; } }; for (var i = 0; i < window.zhuge.methods.length; i++) { var key = window.zhuge.methods[i]; window.zhuge[key] = window.zhuge.factory(key); } window.zhuge.load = function(b, x) { if (!document.getElementById("zhuge-js")) { var a = document.createElement("script"); var verDate = new Date(); var verStr = verDate.getFullYear().toString() + verDate.getMonth().toString() + verDate.getDate().toString(); a.type = "text/javascript"; a.id = "zhuge-js"; a.async = !0; a.src = 'https://zg.ybm100.com/zhuge.js?v=' + verStr; a.onerror = function() { window.zhuge.identify = window.zhuge.track = function(ename, props, callback) { if(callback && Object.prototype.toString.call(callback) === '[object Function]') { callback(); } else if (Object.prototype.toString.call(props) === '[object Function]') { props(); } }; }; var c = document.getElementsByTagName("script")[0]; c.parentNode.insertBefore(a, c); window.zhuge._init(b, x) } };
      window.zhuge.load('e52bfbd724264e45bfa39765dbc1f66e', { //配置应用的AppKey
          superProperty: { //全局的事件属性(选填)
              '应用名称': '诸葛io'
          },
          adTrack:true,//广告监测开关,默认为false
          zgsee:true,//视屏采集开关, 默认为false
          autoTrack: true,//启用全埋点采集（选填，默认false）
          singlePage: true //是否是单页面应用（SPA），启用autoTrack后生效（选填，默认false）
      });
  })(); </script> -->

  <script type="text/javascript">
    (function () {

      // AppKey
      // 生产：ec48e14052624b3294d74921b8c257ca
      // 测试：cdaeb9bc6854423b9ab74b1827bc21a0

      let AppKey = 'cdaeb9bc6854423b9ab74b1827bc21a0'
      let Config = { //配置应用的AppKey
        superProperty: { //全局的事件属性(选填)
          '应用名称': '小药药IM工单系统'
        },
        debug: true,//实时调试（默认false）
        autoTrack: true,//启用全埋点采集（选填，默认false）
        autoScroll: false,//是否监测页面滚动事件，启动autoTrack后生效（选填，默认false）    
        singlePage: true, //是否是单页面应用（SPA），启用autoTrack后生效（选填，默认false）  
        platform: 'js',//平台设置，js、h5（选填，默认js）
      }

      let ConfigHost = {
        // 以下三个字段可不配置，默认上报到正式环境
        api_host: "https://sg.api.test.ybm100.com/sg/collection.gif?method=web_event_srv.uploadmethod", //上报url             
        api_host_bac: "https://sg.api.test.ybm100.com/sg/collection.gif?method=web_event_srv.uploadmethod", //上报url失效-备用url
        api_device_host: "https://sg.api.test.ybm100.com/devinfo", //设备信息上报url
      }

      if (location.hostname === "cc-order.ybm100.com") {
        AppKey = 'ec48e14052624b3294d74921b8c257ca'
      } else {
        Object.assign(Config, ConfigHost);
        AppKey = 'cdaeb9bc6854423b9ab74b1827bc21a0'
      }


      if (window.webSdk) return;
      window.webSdk = [];
      window.webSdk.methods = "init identify track setSuperProperty".split(" ");
      window.webSdk.factory = function (b) {
        return function () {
          var a = Array.prototype.slice.call(arguments);
          a.unshift(b);
          window.webSdk.push(a);
          return window.webSdk;
        }
      };
      for (var i = 0; i < window.webSdk.methods.length; i++) {
        var key = window.webSdk.methods[i];
        window.webSdk[key] = window.webSdk.factory(key);
      }
      window.webSdk.load = function (b, x) {
        if (!document.getElementById("sdk-js")) {
          var a = document.createElement("script");
          var verDate = new Date();
          var verStr = verDate.getFullYear().toString() + verDate.getMonth().toString() + verDate.getDate().toString();
          a.type = "text/javascript";
          a.id = "sdk-js";
          a.async = !0;
          a.src = 'https://zg.ybm100.com/pc-sdk.js?v=' + verStr;
          // a.src = 'static/pc-sdk.js'
          a.onerror = function () {
            window.webSdk.identify = window.webSdk.track = function (ename, props, callback) {
              if (callback && Object.prototype.toString.call(callback) === '[object Function]') {
                callback();
              } else if (Object.prototype.toString.call(props) === '[object Function]') {
                props();
              }
            };
          };
          var c = document.getElementsByTagName("script")[0];
          c.parentNode.insertBefore(a, c);
          window.webSdk.init(b, x)
        }
      };
      window.webSdk.load(AppKey, Config);
    })(); 
  </script>

</head>

<body>
  <div id="app"></div>
</body>

</html>