<!--  -->
<template>
  <div class="config-order-tag-component-container">
    <div class="title">
      <span>领取工单数配置</span>
      <el-popover placement="bottom-start"
                  width="440"
                  popper-class="popoverAll"
                  trigger="hover">
        <img src="@/assets/work_sheet/orderNumberExample.png"
             width="430px" />
        <p>
          此处用于配置 工单及处理页面 每次点击领取工单按钮，可领取到的工单数量。
          <br />
        </p>

        <div slot="reference"
             style="cursor:pointer;">
          <svg-icon class="icon-info"
                    icon-class="info"></svg-icon>
        </div>
      </el-popover>
    </div>
    <div class="boxer">
      <el-form ref="form">
        <el-form-item label="请选择点击领取工单，可领取的工单数">
          <el-select v-model="number"
                     placeholder="请选择">
            <el-option v-for="item in receiveNumber"
                       :key="item.id"
                       :label="item.drawNum"
                       :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <div class="bottom">
      <el-button size="medium"
                 type="primary"
                 @click="onSubmit">生效</el-button>
      <el-button size="medium"
                 @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script>
// 例如：import 《组件名称》 from '《组件路径》';
import { getFormDrawNumList, updateDrawStatus } from '@/api/mySheetManage';
export default {
  name: 'configOrderNum',
  components: {},
  data () {
    return {
      number: 1,
      // receiveNumber: [1, 3, 5, 7, 9, 12, 15, 20, 25, 30, 40, 50],
      receiveNumber: []
    };
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    onSubmit () {
      const that = this;
      that.$XyyMsg({
        title: '提示',
        content: '确认生效 领取工单配置？',
        onSuccess: function () {
          that.handleSetSheetNum();
        }
      });
    },
    handleSetSheetNum () {
      const that = this;
      updateDrawStatus({
        id: that.number,
        status: 1
      }).then(res => {
        if (res.code && res.code === 1) {
          that.$XyyMessage.success('设置成功！');
        } else {
          that.$XyyMessage.error(res.msg);
        }
      });
    },
    /**
     * 取消
     */
    cancel () {
      this.$store.dispatch('tagsView/delView', this.$route);
      this.$router.push({
        path: '/worksheet/configIndex'
      });
    }
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created () {
    const that = this;
    getFormDrawNumList().then(res => {
      if (res.code && res.code === 1) {
        that.receiveNumber = res.data;
        for (let i = 0; i < that.receiveNumber.length; i++) {
          if (that.receiveNumber[i].status === 1)
            that.number = that.receiveNumber[i].id;
        }
      } else {
        that.$XyyMessage.error(res.msg);
      }
    });
  },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { }
};
</script>
<style scoped lang="scss">
.config-order-tag-component-container {
  width: 100%;
  padding: 20px;

  .title {
    width: 100%;
    padding: 10px 0;
    border-bottom: 1px dashed #e4e4eb;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;

    & > span {
      margin-right: 10px;
    }
  }

  .boxer {
    width: 100%;
    height: calc(100% - 75px);
    padding: 20px;
    overflow-y: auto;

    .tag-list {
      .el-tag {
        margin: 0 10px 10px 0;
      }
    }

    .tag-add {
      .tag-add-form {
        margin-top: 20px;

        .el-input {
          width: 250px;
        }
      }

      .tag-add-btn {
        .el-button /deep/ span {
          display: flex;
          align-items: center;

          .add-icon {
            font-size: 20px;
          }
        }
      }
    }
  }

  .bottom {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}
</style>

