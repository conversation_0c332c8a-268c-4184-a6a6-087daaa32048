<template>
  <div class="drug-recall-list-container">
    <!-- 查询条件 -->
    <div class="search-form-box">
      <el-form
        ref="form"
        :inline="true"
        :model="form"
        label-width="90px"
        label-position="left"
        class="input_serch"
        size="medium"
      >
        <el-form-item label="召回单标题" prop="recallTitle">
          <el-input v-model="form.recallTitle" placeholder="请输入标题"></el-input>
        </el-form-item>
        <el-form-item style="float:right">
          <el-button type="primary" @click="search">查询</el-button>
          <el-button @click="reset">重置</el-button>
          <el-button @click="gotoNewDrugRecall">
            <i class="el-icon-plus"></i>
            新建
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- table表单 -->
    <div class="table-box">
      <xyy-table
        :data="list"
        :list-query="listQuery"
        :col="col"
        :operation="operation"
        @get-data="getList"
        @operation-click="operationClick"
      >
        <template slot="gmtModified" slot-scope="{col}">
          <el-table-column
            :key="col.index"
            :prop="col.index"
            :label="col.name"
            :width="col.width"
            :formatter="getFormatDate"
          ></el-table-column>
        </template>
        <template slot="status" slot-scope="{col}">
          <el-table-column :key="col.index" :label="col.name" :width="col.width">
            <template slot-scope="item">
              <span :class="'status-tip '+(item.row.status === 1?'open':'close')"></span>
              {{ item.row.status === 1?'启用':'禁用' }}
            </template>
          </el-table-column>
        </template>
      </xyy-table>
    </div>

    <!-- 二维码弹窗 -->
    <el-dialog
      :visible="qrCodeDialogVisible"
      custom-class="qrCodeDialog"
      :show-close="false"
      width="30%"
      @opened="qrCodeDialogOpened"
    >
      <div class="qrtips">药品召回单二维码已生成，可截图分享或右键保存到本地</div>
      <div id="qrcode" ref="qrCodeUrl"></div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="qrCodeDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>

    <!-- 药品召回信息登记 -->
    <el-dialog :visible.sync="previewDialogVisible" title="药品召回信息登记" custom-class="previewDialog">
      <previewinfor :goods-data="drugParam"></previewinfor>
    </el-dialog>
  </div>
</template>

<script>
import {
  workOrderRecallGetList,
  workOrderRecallDel,
  workOrderRecallEnableAndDisable,
} from '@/api/shop';
import QRCode from 'qrcodejs2';
import previewinfor from './previewinfor.vue';
import { getFormatDate } from '@/assets/filter/commpent';
export default {
  name: '',
  components: {
    previewinfor,
  },
  filters: {},
  props: {},
  data() {
    return {
      form: {
        recallTitle: '',
      },
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      list: [],
      col: [
        {
          index: 'id',
          name: '召回单编号',
          ellipsis: true,
          resizable: true,
          width: 160,
        },
        {
          index: 'drugName',
          name: '召回单标题',
          ellipsis: true,
        },
        {
          index: 'gmtModified',
          name: '更新时间',
          ellipsis: true,
          slot: true,
          width: 200,
        },
        {
          index: 'status',
          name: '状态',
          ellipsis: true,
          slot: true,
          width: 120,
        },
        {
          index: 'operation',
          name: '操作',
          operation: true,
          width: 270,
        },
      ],
      operation: [
        {
          name: '预览',
          type: 1,
        },
        {
          name: '删除',
          type: 2,
          disabled: function (row) {
            return !!row.status;
          },
        },
        {
          name: '查看二维码',
          type: 3,
        },
        {
          name: '',
          type: 4,
          format: function (row) {
            return ['启用', '禁用'][row.status]; // 0-禁用，显示启用 1-启用，显示禁用
          },
        },
      ],
      qrCodeDialogVisible: false,
      qrCodeText: '', // 绘制二维码文本
      drugParam: {},
      previewDialogVisible: false,
      originPath: '', //源路径
    };
  },
  computed: {},
  watch: {},
  activated() {
    this.getList(this.listQuery);
  },
  created() {
    //development,build-dev,build-test,build-stage,production
    console.log(process.env.NODE_ENV);
    switch (process.env.NODE_ENV) {
      case 'production':
        this.originPath = 'https://saoma.ybm100.com/pc/';
        break;
      default:
        this.originPath = `${window.location.origin}${window.location.pathname}`;
        break;
    }
  },
  mounted() {},
  methods: {
    /**
     * 查询
     */
    getList(listQuery) {
      const { page, pageSize } = listQuery;
      workOrderRecallGetList({
        drugName: this.form.recallTitle.trim(),
        pageNum: page,
        pageSize: pageSize,
      }).then((resp) => {
        if (resp.code === 1) {
          this.list = resp.data.records;
          this.listQuery = {
            ...this.listQuery,
            total: Number(resp.data.total),
          };
        } else {
          this.$XyyMessage.error(resp.msg);
        }
      });
    },

    /**
     * 操作栏
     */
    operationClick: function (type, row) {
      switch (type) {
        case 1: {
          // 预览
          const tmpArr = [];
          row.batchNumber.split(',').forEach((item) => {
            tmpArr.push({
              batchNumber: item,
            });
          });
          this.drugParam = {
            drugName: row.drugName,
            description: row.description,
            batchInfos: tmpArr,
          };
          this.previewDialogVisible = true;
          break;
        }
        case 2:
          // 删除
          this.workOrderRecallDel(row);
          break;
        case 3:
          // 查看二维码
          document.getElementById('qrcode') &&
            (document.getElementById('qrcode').innerHTML = '');

          //业务线code
          let businessPartCode = '';
          if (
            this.$store.getters.channel &&
            this.$store.getters.channel.businessPartCode
          ) {
            businessPartCode = this.$store.getters.channel.businessPartCode;
          }
          if (businessPartCode) {
            this.qrCodeText = `${this.originPath}#/preview/list?id=${row.id}&businessPartCode=${businessPartCode}`;
          } else {
            this.qrCodeText = `${this.originPath}#/preview/list?id=${row.id}`;
          }

          console.log(this.qrCodeText);
          this.qrCodeDialogVisible = true;
          break;
        case 4:
          // 启用禁用
          this.workOrderRecallEnableAndDisable(row);
          break;
      }
    },

    /**
     * 启用禁用
     */
    workOrderRecallEnableAndDisable(row) {
      // 状态 0-禁用 1-启用
      if (row.status == 0) {
        // 启用操作
        workOrderRecallEnableAndDisable({
          id: row.id,
          status: 1,
        }).then((resp) => {
          if (resp.code === 1) {
            document.getElementById('qrcode') &&
              (document.getElementById('qrcode').innerHTML = '');

            //业务线code
            let businessPartCode = '';
            if (
              this.$store.getters.channel &&
              this.$store.getters.channel.businessPartCode
            ) {
              businessPartCode = this.$store.getters.channel.businessPartCode;
            }
            if (businessPartCode) {
              this.qrCodeText = `${this.originPath}#/preview/list?id=${row.id}&businessPartCode=${businessPartCode}`;
            } else {
              this.qrCodeText = `${this.originPath}#/preview/list?id=${row.id}`;
            }

            console.log(this.qrCodeText);
            this.qrCodeDialogVisible = true;
            this.getList(this.listQuery);
          } else {
            this.$XyyMessage.error(resp.msg);
          }
        });
      } else if (row.status === 1) {
        // 禁用操作
        this.$XyyMsg({
          title: '提示',
          htmlString: true,
          content: `<span>是否禁用 ${row.drugName} 药品召回单？</span>`,
          onSuccess: () => {
            workOrderRecallEnableAndDisable({
              id: row.id,
              status: 0,
            }).then((resp) => {
              if (resp.code === 1) {
                this.$XyyMessage.success('操作成功！');
                this.getList(this.listQuery);
              } else {
                this.$XyyMessage.error(resp.msg);
              }
            });
          },
        });
      }
    },

    /**
     * 删除
     */
    workOrderRecallDel(row) {
      this.$XyyMsg({
        title: '提示',
        htmlString: true,
        content: `<span>是否删除 ${row.drugName} 药品召回单？</span>`,
        onSuccess: () => {
          workOrderRecallDel({
            id: row.id,
          }).then((resp) => {
            if (resp.code === 1) {
              this.$XyyMessage.success('操作成功！');
              this.getList(this.listQuery);
            } else {
              this.$XyyMessage.error(resp.msg);
            }
          });
        },
      });
    },

    /**
     * 二维码dialog打开
     */
    qrCodeDialogOpened() {
      // 查看二维码
      this.$nextTick(() => {
        new QRCode(this.$refs.qrCodeUrl, {
          text: this.qrCodeText, // 需要转换为二维码的内容
          width: 160,
          height: 160,
          colorDark: '#000000',
          colorLight: '#ffffff',
          correctLevel: QRCode.CorrectLevel.H,
        });
      });
    },

    /**
     * 搜索
     */
    search() {
      this.listQuery.page = 1;
      this.getList(
        Object.assign({}, this.listQuery, {
          title: this.form.recallTitle.trim(),
        })
      );
    },

    /**
     * 重置
     */
    reset() {
      this.$refs['form'].resetFields();
      this.search();
    },

    /**
     * 新建召回单
     */
    gotoNewDrugRecall() {
      this.$router.push({
        path: '/recall/newdrugRecall',
      });
    },

    /**
     * 时间格式化
     */
    getFormatDate: function (row) {
      return getFormatDate(row.gmtModified);
    },
  },
};
</script>

<style lang="scss" scoped>
.drug-recall-list-container {
  width: 100%;

  /deep/label {
    font-weight: 400 !important;
  }

  .search-form-box {
    width: 100%;
    padding: 40px 20px 18px;
  }

  .table-box {
    width: 100%;
    padding: 0 20px;
  }

  .qrCodeDialog {
    .qrtips {
      padding: 0 20px 30px;
      font-size: 16px;
    }

    #qrcode {
      height: 160px;

      /deep/ img {
        margin: 0 auto;
      }
    }
  }

  /deep/ {
    .previewDialog.el-dialog {
      margin-top: 15vh;
      width: 350px;
      max-height: 700px;
      overflow: auto;
      .el-dialog__body {
        padding-top: 0;
      }
    }
  }
}
</style>
