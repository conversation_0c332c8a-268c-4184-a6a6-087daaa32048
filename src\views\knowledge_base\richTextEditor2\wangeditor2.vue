<template lang="html">
  <div class="editor">
    <div ref="toolbar" class="toolbar"></div>
    <div ref="editor" class="text"></div>
  </div>
</template>

<script>
import E from 'wangeditor';
import axios from 'axios';
import emojiJSON from './emoji.json';
export default {
  name: 'editoritem2',
  data() {
    return {
      editor: '',
      info_: '',
      spanInfo: 0,
      isChange: false,
      isShow: false,
      emojiList: emojiJSON
    };
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    index: {
      type: Number,
      required: true
    },
    value: {
      type: String,
      default: ''
    },
    isClear: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    isClear(val) {
      // 触发清除文本域内容
      if (val) {
        this.editor.txt.clear();
        this.info_ = null;
      }
    },
    value: function(value, e) {
      if (!this.isChange) {
        if (value !== this.editor.txt.html()) {
          this.editor.txt.html(this.value);
        }
      }
      this.isChange = false;
    }
    // value为编辑框输入的内容，这里我监听了一下值，当父组件调用得时候，如果给value赋值了，子组件将会显示父组件赋给的值
  },
  mounted() {
    this.seteditor();
    this.editor.txt.html(this.value);
  },
  methods: {
    seteditor() {
      this.editor = new E(this.$refs.toolbar, this.$refs.editor);
      this.editor.config.emotions = [
        {
          title: '默认',
          type: 'image',
          content: this.emojiList
        }
      ];
      this.editor.config.uploadImgShowBase64 = false; // base 64 存储图片
      this.editor.config.uploadFileName = 'file'; // 后端接受上传文件的参数名
      this.editor.config.uploadImgMaxSize = 2 * 1024 * 1024; // 将图片大小限制为 2M
      this.editor.config.uploadImgMaxLength = 6; // 限制一次最多上传 3 张图片
      this.editor.config.uploadImgTimeout = 3 * 60 * 1000; // 设置超时时间

      // 设置上传本地图片
      this._setUploadLocalImg();

      // 配置菜单
      // this.editor.config.menus = [
      //   'emoticon', // 表情
      // ];

      // 配置颜色（文字颜色、背景色）
      this.editor.config.colors = [
        '#606266',
        '#FFFFFF',
        '#000000',
        '#CC0100',
        '#FF0000',
        '#FFB700',
        '#FFFF00',
        '#0000FF',
        '#5676FF',
        '#00B050',
        '#92D050',
        '#292933',
        '#D4D4E3'
      ];

      var change = this.isChange;
      setInterval(() => {
        change = true;
      }, 500);
      this.editor.config.onchange = html => {
        this.isChange = change;
        this.info_ = html; // 绑定当前逐渐地值
        this.$emit('change', this.info_); // 将内容同步到父组件中
      };
      // 创建富文本编辑器
      this.editor.create();
    },
    deleteStudent() {
      this.$emit('deleteIndex', this.index);
    },

    _setUploadLocalImg() {
      this.editor.config.customUploadImg = async (resultFiles, insertImgFn) => {
        for (let i = 0; i < resultFiles.length; i++) {
          let file = resultFiles[i];
          const url = process.env.BASE_API_IM + '/im-file/uploadimg';
          const formDate = new FormData();
          formDate.append('imgFile', file);
          // formDate.append('imgfilenames', file.name);
          const config = {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            withCredentials: true
          };
          if (
            this.$store.getters.channel &&
            this.$store.getters.channel.businessPartCode
          ) {
            config.headers.businessPartCode = this.$store.getters.channel.businessPartCode;
          }
          axios.defaults.withCredentials = true;
          await axios
            .post(url, formDate, config)
            .then(res => {
              if (res.data.code === 1) {
                if (res.data.data.originalPath) {
                  let imgUrl = res.data.data.originalPath;
                  insertImgFn(imgUrl);
                } else {
                  this.$XyyMessage.error(res.data.msg);
                }
              }
            })
            .catch(res => {
              this.$XyyMessage.error(res.data.msg);
            });
        }
      };

      //   // resultFiles 是 input 中选中的文件列表
      //   // insertImgFn 是获取图片 url 后，插入到编辑器的方法
      //   // 上传图片，返回结果，将图片插入到编辑器中
      //   // resultFiles 是 input 中选中的文件列表，遍历上传
      //   for (let i = 0; i < resultFiles.length; i++) {
      //     let file = resultFiles[i];
      //     // 上传服务器拿到图片链接,这里_uploadSingleFile就是上传图片接口，
      //     let imgUrl = this._uploadSingleFile(file);
      //     // 获取图片 url 后，插入到编辑器
      //     insertImgFn(imgUrl);
      //   }
      // };

      // this.editor.config.customUploadImg = function(resultFiles, insertImgFn) {
      //   for (let i = 0; i < resultFiles.length; i++) {
      //     let file = resultFiles[i];
      //     // 上传服务器拿到图片链接,这里_uploadSingleFile就是上传图片接口，
      //     let imgUrl = this._uploadSingleFile(file);
      //     // 获取图片 url 后，插入到编辑器
      //     insertImgFn(imgUrl);
      //   }
      // };
    },

    async _uploadSingleFile(file) {
      const url = process.env.BASE_API_IM + '/im-file/uploadimg';
      const formDate = new FormData();
      formDate.append('imgFile', file);
      // formDate.append('imgfilenames', file.name);
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        withCredentials: true
      };
      if (
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
      ) {
        config.headers.businessPartCode = this.$store.getters.channel.businessPartCode;
      }
      axios.defaults.withCredentials = true;
      await axios
        .post(url, formDate, config)
        .then(res => {
          if (res.data.code === 1) {
            if (res.data.data.originalPath) {
              let imgUrl = res.data.data.originalPath;
              return imgUrl;
            } else {
              this.$XyyMessage.error(res.data.msg);
            }
          }
        })
        .catch(res => {
          this.$XyyMessage.error(res.data.msg);
        });
      return '';
    }
  }
};
</script>

<style lang="scss">
.editor {
  width: 100%;
  margin: 0 auto;
  position: relative;
  // height: 299px !important;
  border: 1px solid #ccc;
  line-height: 25px;
  .limitNum {
    position: absolute;
    right: 6px;
    bottom: 0px;
    color: #909399;
  }
  .elIconDelete {
    position: absolute;
    right: -5%;
    top: 15%;
    cursor: pointer;
  }
  .toolbar {
    position: static;
    bottom: 0;
    z-index: 1000;
    border-bottom: 1px solid #eee;
  }
  .w-e-toolbar {
    z-index: 1001 !important;
  }
  .w-e-text-container {
    z-index: 1000 !important;
  }
  .w-e-toolbar .w-e-menu {
    padding: 0 10px;
  }
  .text {
    height: 400px;
    z-index: auto !important;
    overflow-y: scroll;
    .w-e-panel-container {
      z-index: 99999999;
      left: -1px;
      top: 100%;
      width: 70% !important;
      margin-left: 0 !important;
    }
    // .w-e-text-container .placeholder {
    // color: #d4d4d4;
    // position: absolute;
    // font-size: 11pt;
    // line-height: 22px;
    // left: 10px;
    // top: 10px;
    // -webkit-user-select: none;
    // -moz-user-select: none;
    // -ms-user-select: none;
    // user-select: none;
    // z-index: 99999999;
    // }
    .w-e-text {
      overflow-y: auto;

      /* ul ol 样式 */
      ul,
      dl {
        list-style-type: disc;
      }
      a {
        color: #428bca;
        text-decoration: none;
      }
      a:hover {
        text-decoration: underline;
      }
    }
  }
}
</style>
