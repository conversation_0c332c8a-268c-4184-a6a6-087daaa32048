<template>
  <div class="preview-box">
    <div class="file-preview">
      <img v-if="previewType==='image'" :src="file.url" />
      <i v-else :class="previewType" class="file-icon" />
      <div :class="{uploading:percent<=100}" class="cover">
        <div class="tools">
          <span v-show="percent<100">
            <el-progress :percentage="percent"></el-progress>
            <!--            <i class="percent-size">{{ percentSize }}</i>-->
            <!--            <el-button type="text" class="file-abort" @click="$emit('abortCallback')">取消</el-button>-->
          </span>
          <span v-show="percent<100" class="operate-tools">
            <i class="el-icon-paperclip noShow" title="预览"></i>
            <i v-if="!editable" class="el-icon-download" title="下载"></i>
            <i v-if="editable" class="el-icon-close" title="删除" @click="$emit('delCallback')"></i>
          </span>
          <span v-show="percent==100" class="operate-tools">
            <i
              v-if="!ignore.includes(previewType)"
              class="el-icon-paperclip"
              title="预览"
              @click="preview"
            ></i>
            <i v-if="ignore.includes(previewType)" class="el-icon-paperclip noShow" title="预览"></i>
            <i v-if="!editable" class="el-icon-download" title="下载" @click="download"></i>
            <i v-if="editable" class="el-icon-close" title="删除" @click="$emit('delCallback')"></i>
          </span>
        </div>
      </div>
    </div>
    <div
      :title="file.name"
      class="file-name file-name1"
      @click="preview"
      v-if="!ignore.includes(previewType)"
    >{{ file.name }}</div>
    <div
      :title="file.name"
      class="file-name"
      v-if="ignore.includes(previewType)"
      style="color:#292933"
    >{{ file.name }}</div>
    <el-dialog
      ref="previewDialog"
      :visible.sync="isOpen"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="800px"
      top="0"
      custom-class="preview-dialog"
      @close="close"
    >
      <div slot="title" class="preview-title">
        {{ file.name }}
        <span class="preview-size">{{ previewSize }}</span>
        <span class="preview-tools">
          <i class="el-icon-download" title="下载" @click="download" />
          <i v-if="previewType==='image'" class="el-icon-zoom-in" title="放大" @click="zoomIn" />
          <i v-if="previewType==='image'" class="el-icon-zoom-out" title="缩小" @click="zoomOut" />
          <svg-icon v-if="previewType==='image'" icon-class="rotate" title="旋转" @click="rotate" />
        </span>
      </div>
      <div ref="imgTopList" class="imgTopList">
        <img
          v-if="previewType==='image'"
          :style="imgStyle"
          :src="filePath"
          @mousedown="move"
          ref="imgInner"
        />
        <video v-if="previewType==='video'" ref="myVideo" :src="filePath" controls="controls"></video>
        <object v-if="previewType==='pdf'" :data="filePath" :type="file.raw.type"></object>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { FILE_PATH } from '@/api/fields';
export default {
  props: {
    file: {
      type: Object,
      default: () => {
        return {
          uid: '', // 唯一标识
          name: '', // 文件名
          url: '', // 文件缩略图地址 或 预览地址
          path: '', // 文件真实地址
          raw: {
            type: '', // 文件mime-type
            size: 0 // 文件大小
          },
          data: '' // 后台所需数据
        };
      }
    },
    // 上传进度
    percent: {
      type: Number,
      default: 0
    },
    // 编辑状态
    editable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      basePath: FILE_PATH,
      isOpen: false, // 预览状态
      imgTop: 0, // 图片距离顶部位置
      imgLeft: 0, // 图片距离左边的位置
      imgSelfTop: 0, // 图片自身Y轴偏移
      imgSelfLeft: 0, // 图片滋生X轴偏移
      imgRotate: 0, // 图片翻转角度
      imgScale: 1, // 图片放大倍数
      imgWidth: 0, // 图片显示宽度
      imgHeight: 0, // 图片显示高度
      // pdfPath: 'https://view.officeapps.live.com/op/view.aspx?src=',
      ignore: ['zip', 'word', 'excel', 'ppt', 'avi', 'txt']
    };
  },
  computed: {
    imgStyle() {
      return {
        width: this.imgWidth * this.imgScale + 'px',
        height: this.imgHeight * this.imgScale + 'px',
        top: this.imgTop,
        left: this.imgLeft,
        transform: `translateY(${this.imgSelfTop}) translateX(${this.imgSelfLeft}) scale(${this.imgScale}) rotate(${this.imgRotate}deg)`
      };
    },
    previewType() {
      let _type = '';
      if (this.file.raw.type.search(/image/) >= 0) {
        _type = 'image';
      } else if (this.file.raw.type.search(/video/) >= 0) {
        _type = 'video';
        if (this.file.raw.type.search(/avi/) >= 0) _type = 'avi';
      } else if (this.file.raw.type === 'application/pdf') {
        _type = 'pdf';
      } else if (
        this.file.raw.type === 'application/zip' ||
        this.file.raw.type === 'application/x-zip-compressed' ||
        this.file.raw.type === 'application/x-rar-compressed' ||
        this.file.name.slice(this.file.name.lastIndexOf('.') + 1) === 'rar'
      ) {
        _type = 'zip';
      } else if (
        this.file.raw.type === 'application/msword' ||
        this.file.raw.type ===
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ) {
        _type = 'word';
      } else if (
        this.file.raw.type === 'application/vnd.ms-powerpoint' ||
        this.file.raw.type ===
          'application/vnd.openxmlformats-officedocument.presentationml.presentation'
      ) {
        _type = 'ppt';
      } else if (
        this.file.raw.type === 'application/vnd.ms-excel' ||
        this.file.raw.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ) {
        _type = 'excel';
      } else if (this.file.raw.type === 'text/plain') {
        _type = 'txt';
      }

      return _type;
    },
    percentSize() {
      return (
        ((this.file.raw.size * this.percent) / 102400000).toFixed(2) + 'MB'
      );
    },
    previewSize() {
      return this.file.raw.size < 1000
        ? this.file.raw.size + 'B'
        : this.file.raw.size < 1024000
        ? (this.file.raw.size / 1000).toFixed(2) + 'KB'
        : (this.file.raw.size / 1024000).toFixed(2) + 'MB';
    },
    filePath() {
      return this.file.path ? this.file.path : this.file.url;
    }
  },
  methods: {
    /**
     * 文件下载
     */
    download() {
      const url = `${
        process.env.BASE_API
      }/fileUpload/downloadFile?originalFilename=${this.file.name}&path=${
        this.file.data
          ? this.file.data.path
          : this.file.response.data.successFiles[0].path
      }&group=${
        this.file.data
          ? this.file.data.group
          : this.file.response.data.successFiles[0].group
      }`;
      const a = document.createElement('a');
      a.href = url;
      a.click();
    },
    /**
     * 文件预览
     */
    preview() {
      this.isOpen = true;
      if (this.previewType === 'image') {
        this.initImage();
      }
    },
    /**
     * 初始化图片宽高
     */
    initImage() {
      const boxWidth = 800;
      const boxHeight = 440;
      this.imgRotate = 0;
      this.imgScale = 1;
      const img = new Image();
      img.src = this.filePath;
      img.onload = () => {
        const _width = img.width;
        const _height = img.height;
        if (_width > boxWidth) {
          let h = ((_height * boxWidth) / _width).toFixed(0);
          let w = boxWidth;
          if (h > boxHeight) {
            w = ((w * boxHeight) / h).toFixed(0);
            h = boxHeight;
          }
          this.imgWidth = w;
          this.imgHeight = h;
        } else {
          if (_height > boxHeight) {
            this.imgWidth = ((_width * boxHeight) / _height).toFixed(0);
            this.imgHeight = boxHeight;
          } else {
            this.imgWidth = _width;
            this.imgHeight = _height;
          }
        }

        if (this.imgHeight < boxHeight) {
          this.imgTop = '50%';
          this.imgLeft = '50%';
          this.imgSelfTop = '-50%';
          this.imgSelfLeft = '-50%';
        } else {
          //            this.imgTop = 0;
          //            this.imgSelfTop = 0;
          //            this.imgLeft = 0
          //            this.imgSelfLeft = 0
          this.imgTop = '50%';
          this.imgLeft = '50%';
          this.imgSelfTop = '-50%';
          this.imgSelfLeft = '-50%';
        }
      };
    },
    /**
     * 图片旋转
     */
    rotate() {
      this.imgRotate = this.imgRotate === 360 ? 90 : this.imgRotate + 90;
    },
    /**
     * 图片放大
     */
    zoomIn() {
      this.imgScale = (Number(this.imgScale) + 0.1).toFixed(1);
      //        this.imgWidth
    },
    /**
     * 图片缩小
     */
    zoomOut() {
      if (this.imgScale > 0.1) {
        this.imgScale = (this.imgScale - 0.1).toFixed(1);
      } else {
        this.$XyyMessage.warning(`不能再小了`);
      }
    },
    /**
     * 图片拖拽
     */
    move(e) {
      //        浏览器的宽高 document.body.clientWidth   document.body.clientHeight
      //        获取最外层容器的宽 fa.offsetWidth   offsetHeight
      const odiv = e.target; // 获取目标元素img
      // 阻止默认事件的方法,如果不阻止默认事件onmouseup会无法触发
      e.preventDefault();
      //        获取外层元素距离浏览器的距离
      let clientWidth = document.body.clientWidth;
      let clientHeight = document.body.clientHeight;
      let faWidth = this.$refs.imgTopList.offsetWidth;
      let faHeight = this.$refs.imgTopList.offsetHeight;
      let faLeft = clientWidth / 2 - faWidth / 2;
      let faTop = clientHeight / 2 - faHeight / 2;
      // 算出鼠标相对元素的位置
      let disX = e.clientX - odiv.offsetLeft - faLeft;
      let disY = e.clientY - odiv.offsetTop - faTop;
      document.onmousemove = e => {
        console.log('移动');
        e.preventDefault();
        // 鼠标按下并移动的事件
        // 用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
        // 因为img居中对齐，所以还要减去目标元素距离body的偏移量
        const left = e.clientX - disX - faLeft;
        const top = e.clientY - disY - faTop;
        //移动当前元素
        this.imgLeft = left + 'px';
        this.imgTop = top + 'px';
      };
      document.onmouseup = e => {
        console.log('停止');
        document.onmousemove = null;
        document.onmouseup = null;
      };
    },
    /**
     * 关闭回调
     */
    close() {
      if (this.$refs['myVideo']) {
        this.$refs['myVideo'].load();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.imgTopList {
  height: 100%;
  overflow: auto;
  position: relative;
  text-align: center;
  img {
    cursor: pointer;
  }
}
.preview-box {
  line-height: 0;
  float: left;
  width: 123px;
  margin: 0 8px 10px;
  position: relative;
  margin-left: 0;
  .file-preview {
    width: 123px;
    height: 28px;
    overflow: visible;
    position: relative;
    margin: 0;
    left: 50%;
    transform: translateX(-50%);
    img {
      display: none;
    }
    i.file-icon {
      width: 100%;
      position: relative;
      top: 50%;
      transform: translateY(-50%);
      float: left;
    }
    i.file-icon {
      display: inline-block;
      height: 100%;
      box-sizing: border-box;
    }

    i.pdf {
      background: url(../../assets/fields/pdf.png) 0 0 no-repeat;
    }

    i.video,
    i.avi {
      background: url(../../assets/fields/video.png) 0 0 no-repeat;
    }

    i.zip {
      background: url(../../assets/fields/zip.png) 0 0 no-repeat;
    }

    i.word {
      background: url(../../assets/fields/word.png) 0 0 no-repeat;
    }

    i.ppt {
      background: url(../../assets/fields/ppt.png) 0 0 no-repeat;
    }

    i.excel {
      background: url(../../assets/fields/excel.png) 0 0 no-repeat;
    }

    i.txt {
      background: url(../../assets/fields/txt.png) 0 0 no-repeat;
    }

    .cover {
      display: none;
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: rgba(245, 247, 250, 1);
      z-index: 1;
      .el-icon-close {
        display: inline-block;
        color: #292933;
      }
      .el-progress {
        width: 100%;
        margin-top: 20px;
        /deep/.el-progress-bar {
          width: 73% !important;
          background: rgba(228, 228, 235, 1);
          padding: 0 !important;
        }
        /deep/.el-progress-bar__outer {
          background-color: #909399;
          .el-progress-bar__inner {
            background-color: rgba(59, 149, 168, 1);
          }
        }
        /deep/.el-progress__text {
          color: rgba(87, 87, 102, 1);
          transform: translateY(50%);
          top: -3px;
          font-size: 10px !important;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          position: absolute;
          right: 0;
        }
      }
      i.percent-size {
        display: inline-block;
        height: 12.3px;
        font-size: 8px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        font-style: normal;
        position: absolute;
        top: 50%;
        transform: translateY(-100%);
      }
      .el-button.file-abort {
        display: none;
        font-size: 10px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(77, 223, 254, 1);
        position: absolute;
        bottom: 7px;
        left: 50%;
        transform: translateX(-50%);
        padding: 0;
      }
      .operate-tools {
        width: 100%;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        i.el-icon-download {
          position: absolute;
          right: -2px;
          color: #292933;
          font-size: 14px;
          top: 2px;
          &:hover {
            color: rgba(59, 149, 168, 1);
          }
        }
        i.el-icon-paperclip {
          color: rgba(59, 149, 168, 1);
        }
        i.noShow {
          color: #292933 !important;
        }
        i.el-icon-close {
          color: #292933;
          top: 0;
          font-size: 14px;
          margin-top: 3px;
          margin-right: 1px;
        }
        i {
          font-size: 18px;
          cursor: pointer;
          margin: 0 5px;
        }
      }
      &.uploading {
        display: block;
      }
    }

    &:hover {
      .cover {
        display: block;
        .el-button.file-abort {
          display: block;
        }
      }
    }
  }
  .file-name1 {
    cursor: pointer;
  }
  .file-name {
    width: 65%;
    display: inline-block;
    text-align: left;
    padding-left: 3px;
    height: 17px;
    line-height: 17px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(59, 149, 168, 1);
    margin: 4px 0 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: absolute;
    top: 1px;
    left: 25px;
  }
}
.preview-box:first-child {
  margin-left: 0;
}

.preview-box:last-child::after {
  content: '';
  clear: both;
}
</style>

<style lang="scss">
.el-dialog.preview-dialog {
  height: 500px;
  top: 50%;
  transform: translateY(-50%);
  overflow: hidden;
  /deep/.el-dialog__header {
    height: 60px;
    padding: 15px 16px 20px 20px;
    border-bottom: 1px solid #dcdfe6;
    .preview-title {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(41, 41, 51, 1);
      line-height: 25px;
      height: 25px;
      span.preview-size {
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(96, 98, 102, 1);
        margin-left: 14px;
      }
      span.preview-tools {
        float: right;
        margin-right: 40px;
        border-right: 1px solid #dcdfe6;
        user-select: none;
        padding-right: 10px;
        i {
          color: #c0c4cc;
          margin: 0 10px;
          cursor: pointer;
        }
        .svg-icon {
          margin: 0 10px;
          cursor: pointer;
        }
      }
    }
  }
  /deep/ .el-dialog__body {
    height: calc(100% - 67px);
    /*overflow: auto;*/
    padding: 0;
    /*text-align: center;*/
    /*position: relative;*/
    img {
      position: absolute !important;
    }
    video {
      width: 100%;
      height: calc(100% - 5px);
    }
    object {
      width: 100%;
      height: calc(100% - 5px);
    }
  }
}
</style>

