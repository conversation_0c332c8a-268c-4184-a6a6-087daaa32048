'use strict';
import Vue from 'vue';
import Main from './index';

const MessageConstructor = Vue.extend(Main);
var instances = [];
let instance;
const XyyMessage = (options) => {
  options = options || {};
  if (typeof options === 'string') { // 判断有没有传递type值
    options = {
      message: options
    };
  }
  instance = new MessageConstructor({
    data: options
  });
  instance.vm = instance.$mount();
  document.body.appendChild(instance.vm.$el);
  instance.vm.visible = true; //  t弹框显示
  instances.push(instance.vm);
  return instance.vm;
};

['success', 'warning', 'info', 'error'].forEach(type => {
  XyyMessage[type] = options => {
    if (typeof options === 'string') {
      options = {
        message: options
      };
    }

    if (typeof options === 'undefined') {
      return false;
    }

    options.type = type;
    return XyyMessage(options);
  };
});

export default XyyMessage;
