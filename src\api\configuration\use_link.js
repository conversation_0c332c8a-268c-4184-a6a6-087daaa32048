import request from '@/utils/request-im';

/**
 *常用图片列表
 * @param {*}
 */
export function pictureList(params) {
  return request({
    url: '/picture/list',
    method: 'get',
    params: params
  });
}

/**
 *分页查询常用链接接口
 * @param
 */
export function linkList(params) {
  return request({
    url: '/link/list',
    method: 'get',
    params
  });
}

/**
 *添加修改链接接口
 * @param
 */
export function linkEdit(param) {
  return request({
    url: '/link/edit',
    method: 'post',
    data: param
  });
}
/**
 *添加修改链接接口
 * @param
 */
export function linkDelete(param) {
  return request({
    url: '/link/delete?linkId=' + param,
    method: 'post',
    data: param
  });
}

