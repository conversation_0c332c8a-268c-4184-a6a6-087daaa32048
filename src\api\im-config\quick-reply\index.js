import request from '@/utils/request-im';
import { formData } from '@/utils/index';

export function upload(data) {
  return request({
    header: { 'Content-Type': 'multipart/form-data' },
    url: '/quickreply/faqinfo/upload',
    method: 'post',
    data: formData(data)
  });
}
/**
 * 获取快捷回复数据
 */
export function getQRDatas() {
  return request({
    url: '/quickreply/listall',
    method: 'post'
  });
}

/**
 * 保存分类数据
 * @param {表单数据} data
 */
export function saveClassifyData(data) {
  return request({
    url: '/quickreply/faqtype/add',
    method: 'post',
    data: formData(data)
  });
}
/**
 * 保存问题数据
 */
export function saveQuestionData(data) {
  return request({
    url: '/quickreply/addfaqinfo',
    method: 'post',
    data: formData(data)
  });
}
/**
 * 修改排序
 */
export function updateSort(data) {
  return request({
    url: '/quickreply/batchedit',
    method: 'post',
    data
  });
}


/**
 * 删除分类数据
 */
export function delClassifyData(data) {
  return request({
    url: `/quickreply/faqtype/delete/${data.id}`,
    method: 'delete'
  });
}

/**
 * 删除问题数据
 */
export function delQuestionData(data) {
  return request({
    url: `/quickreply/faqinfo/delete/${data.id}`,
    method: 'delete'
  });
}
