<template>
  <div>
    <el-dialog
      :visible="dialogVisible"
      :title="title"
      custom-class="transfer-box"
      top="0"
      @close="close"
    >
      <div class="left-content">
        <div class="content-title">待选择</div>
        <div class="content-body">
          <checkbox-group
            v-for="data in leftDatas"
            :key="data.typeId"
            :data="data"
            :ref="'group'+data.typeId"
            :checked="leftCheckedDatas"
            :disabled="disabledIds"
            @changeCallback="getLeftCheckedDatas"
          ></checkbox-group>
        </div>
        <div class="btn-box">
          <el-button type="text" @click="checkAll(true)">全选</el-button>
          <el-button type="text" @click="checkAll(false)">全不选</el-button>
        </div>
      </div>
      <div class="center-content">
        <el-button
          :disabled="!leftCheckedDatas.length"
          icon="el-icon-back"
          class="btn-right"
          type="primary"
          @click="leftToRight"
        ></el-button>
        <el-button
          :disabled="!rightCheckedDatas.length"
          icon="el-icon-back"
          class="btn-left"
          type="primary"
          @click="rightToLeft"
        ></el-button>
      </div>
      <div class="right-content">
        <div class="content-title">已选择{{ rightDatas.length?`(${rightDatas.length})`:'' }}</div>
        <div class="content-body">
          <el-checkbox-group v-model="rightCheckedDatas">
            <el-checkbox v-for="el in rightDatas" :label="el" :key="el.id">{{ el.name }}</el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getFormDatas } from '@/api/satisfaction';
import checkboxGroup from './checkbox-group';
export default {
  components: {
    checkboxGroup
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '提示'
    },
    checked: {
      type: Array,
      default: function() {
        return [];
      }
    }
  },
  data() {
    return {
      leftDatas: [], // 左侧表单数据
      leftCheckedDatas: [], // 左侧选中表单数据
      rightDatas: [], // 右侧表单数据
      rightCheckedDatas: [], // 右侧选中表单数据
      disabledIds: [] // 禁用的表单id
    };
  },
  watch: {
    dialogVisible(val, old) {
      if (val) {
        this.getLeftDatas(this.initDatas);
      }
    }
  },
  mounted() {
    this.getLeftDatas(this.initDatas);
  },
  methods: {
    /**
     * 初始化选中数据
     */
    initDatas() {
      this.rightDatas = this.checked;
      this.setLeftCheckedDatas(this.checked);
      this.rightCheckedDatas = [];
    },
    /**
     * 关闭回调
     */
    close() {
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 保存回调
     */
    save() {
      if (!this.rightDatas.length) {
        this.$XyyMessage.error('未有选中项，请重新选择');
        return;
      }
      this.$emit('callback', this.rightDatas);
      this.close();
    },
    /**
     * 获取左侧数据
     */
    getLeftDatas(cb) {
      const id = this.$route.query.id ? this.$route.query.id : '';
      getFormDatas({ templateId: id })
        .then(res => {
          if (res.code === 1) {
            this.leftDatas = res.data.fromInfo;
            this.disabledIds = Array.from(new Set(res.data.filterBaseId)).map(
              el => String(el)
            );
            if (cb) {
              cb();
            }
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },
    /**
     * 获取左侧选中数据
     */
    getLeftCheckedDatas() {
      let _datas = [];
      if (this.leftDatas.length) {
        this.$nextTick(() => {
          this.leftDatas.forEach(el => {
            const _arr = this.$refs['group' + el.typeId][0].getCheckedDatas();
            _datas = _datas.concat(_arr);
          }, this);
          this.leftCheckedDatas = _datas;
        });
      }
    },
    /**
     * 设置左侧选中数据
     */
    setLeftCheckedDatas(datas) {
      if (this.leftDatas.length) {
        this.leftCheckedDatas = JSON.parse(JSON.stringify(datas));
        this.$nextTick(() => {
          this.leftDatas.forEach(el => {
            if (this.$refs['group' + el.typeId]) {
              this.$refs['group' + el.typeId][0].setCheckedDatas();
            }
          }, this);
        });
      }
    },
    /**
     * 选择右侧表单数据
     */
    leftToRight() {
      this.rightDatas = JSON.parse(JSON.stringify(this.leftCheckedDatas));
    },
    /**
     * 取消右侧表单数据
     */
    rightToLeft() {
      if (this.rightCheckedDatas.length) {
        const ids = this.rightCheckedDatas.map(el => el.id);
        this.rightDatas = this.rightDatas.filter(el => !ids.includes(el.id));
        this.setLeftCheckedDatas(this.rightDatas);
        this.rightCheckedDatas = [];
      }
    },
    /**
     * 全选操作
     */
    checkAll(val) {
      if (this.leftDatas.length) {
        this.$nextTick(() => {
          this.leftDatas.forEach(el => {
            if (this.$refs['group' + el.typeId]) {
              this.$refs['group' + el.typeId][0].setAllChecked(val);
            }
          }, this);
        });
      }
    }
  }
};
</script>

<style lang="scss">
.el-dialog.transfer-box {
  width: 850px;
  height: 502px;
  transform: translateY(-50%);
  top: 50%;
  .el-dialog__header {
    height: 52px;
    padding: 15px 20px;
    .el-dialog__title {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(41, 41, 51, 1);
      height: 22px;
      line-height: 22px;
    }
    .el-dialog__headerbtn {
      top: 18px;
    }
  }
  .el-dialog__body {
    height: calc(100% - 122px);
    padding: 6px 20px 0;
    > div {
      float: left;
      height: 100%;
      box-sizing: border-box;
      overflow: hidden;
      position: relative;
      .content-title {
        height: 55px;
        background: rgba(240, 242, 245, 1);
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(41, 41, 51, 1);
        padding: 17px 0 18px 20px;
      }
      .content-body {
        height: calc(100% - 55px);
        padding: 15px 20px 12px;
        overflow-y: auto;
      }
    }
    .left-content {
      width: 346px;
      border-radius: 2px;
      border: 1px solid rgba(228, 228, 235, 1);
      .content-body {
        padding-bottom: 32px;
        .el-checkbox {
          font-weight: normal;
        }
      }
      .btn-box {
        position: absolute;
        bottom: 0;
        padding-left: 20px;
        width: 100%;
        height: 32px;
        background: #fff;
        z-index: 9999;
        .el-button {
          padding: 0;
          height: 20px;
          line-height: 20px;
          font-weight: normal;
        }
      }
    }
    .center-content {
      width: calc(100% - 738px);
      position: relative;
      .el-button {
        padding: 0;
        width: 28px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        border-radius: 50%;
        position: absolute;
        top: 50%;
        &.btn-left {
          left: 50%;
          transform: translateX(-50%) translateY(80%);
          margin-left: 0;
        }
        &.btn-right {
          right: 50%;
          transform: rotate(180deg) translateX(-50%) translateY(80%);
        }
        &.is-disabled {
          background: rgba(220, 223, 230, 1);
          border-color: rgba(220, 223, 230, 1);
        }
      }
    }
    .right-content {
      width: 392px;
      border-radius: 2px;
      border: 1px solid rgba(228, 228, 235, 1);
      .el-checkbox-group {
        .el-checkbox {
          display: block;
          margin-bottom: 12px;
          font-weight: normal;
        }
      }
    }
  }
  .el-dialog__footer {
    padding: 20px;
    .el-button {
      height: 36px;
      line-height: 36px;
      padding: 0 19px;
    }
  }
}
</style>
