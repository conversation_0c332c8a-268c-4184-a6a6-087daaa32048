<template>
  <div>
    <el-dialog
      :visible="dialogVisible"
      :close-on-click-modal="false"
      :show-close="!uploading"
      title="提示"
      custom-class="import-box"
      top="0"
      @close="close"
      @open="open"
    >
      <span ref="content" class="import-content" v-html="content"></span>
      <dl v-show="completed" class="import-result">
        <dt>导入数据项{{ result.total }}</dt>
        <dt>成功{{ result.success }}</dt>
        <dt>失败{{ result.fail }}</dt>
      </dl>
      <el-upload
        ref="importFile"
        :before-upload="beforeUpload"
        :on-progress="handleProgress"
        :on-success="handleSuccess"
        :on-error="handleError"
        :action="url"
        accept=".xls, .xlsx"
      >
        <el-button v-show="!uploading && !completed" size="small" type="primary">上传文件</el-button>
        <div slot="file" slot-scope="{file}">
          <el-progress :percentage="percent"></el-progress>
          <span class="file-name">{{ file.name }}</span>
          <el-button size="small" type="primary" @click="abortFile(file)">取消上传</el-button>
        </div>
      </el-upload>
      <el-button v-show="completed" size="small" type="primary" @click="done">确定</el-button>
    </el-dialog>
  </div>
</template>

<script>
import { TEMPLATE_PATH, TEMPLATE_GROUP } from '@/api/fields';
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      content: '',
      url: process.env.BASE_API + '/field/analysisCascadeExcel',
      uploading: false, // 上传标识
      percent: 0, // 上传进度
      completed: false, // 导入完成
      result: {
        total: 0,
        success: 0,
        fail: 0
      }, // 导入结果
      datas: [], // 导入数据
      downloadUrl: '' // 失败数据地址
    };
  },
  watch: {
    dialogVisible(val, old) {
      if (val) {
        this.content = `请先<a>下载导入模板</a>，再点击上传按钮进行数据上传`;
        this.uploading = false;
        this.percent = 0;
        this.completed = false;
        this.result = {
          total: 0,
          success: 0,
          fail: 0
        };
        this.datas = [];
        this.downloadUrl = '';
      }
    }
  },
  methods: {
    open() {
      this.$nextTick(() => {
        const a = this.$refs['content'].children[0];
        // a.href = TEMPLATE_PATH;
        const name = '模板.xlsx';
        const url = `${process.env.BASE_API}/fileUpload/downloadFile?originalFilename=${name}&path=${TEMPLATE_PATH}&group=${TEMPLATE_GROUP}`;
        a.href = url;
      });
    },
    /**
     * 上传中回调
     */
    handleProgress(event, file) {
      if (file) {
        this.uploading = true;
        this.percent = Number(event.percent.toFixed(0));
        if (this.percent > 99) this.percent = 99;
      }
    },
    /**
     * 取消上传回调
     */
    abortFile(file) {
      this.$refs['importFile'].abort(file);
      this.$refs['importFile'].uploadFiles = [];
      this.uploading = false;
    },
    /**
     * 上传成功回调
     */
    handleSuccess(res, file) {
      if (res.code === 1) {
        this.percent = 100;
        this.completed = true;
        this.content = '上传完成';
        this.result = {
          total: res.data.totalNum,
          success: res.data.okNum,
          fail: res.data.failNum
        };
        this.datas = res.data.options;
        if (res.data.failNum) {
          this.downloadUrl = res.data.failUrl;
        }
        this.$refs['importFile'].uploadFiles = [];
      } else {
        this.$XyyMessage.error(res.msg);
        this.$refs['importFile'].uploadFiles = [];
      }
      this.uploading = false;
    },
    /**
     * 上传失败回调
     */
    handleError(res) {
      this.$refs['importFile'].uploadFiles = [];
      this.uploading = false;
      this.$XyyMessage.error(`文件上传失败`);
    },
    /**
     * 上传之前
     */
    beforeUpload(file) {
      if (file.type) {
        if (
          ![
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          ].includes(file.type)
        ) {
          this.$XyyMessage.error(`只能选择.xls或.xlsx格式的文件`);
          return false;
        }
      }
    },
    /**
     * 关闭回调
     */
    close() {
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 确定操作
     */
    done() {
      if (this.result.fail) {
        // 下载操作
        const url = `${process.env.BASE_API}${this.downloadUrl}`;
        this.download(url);
        this.close();
      } else {
        // 保存数据
        this.$emit('importCallback', this.datas);
        this.close();
      }
    },
    /**
     * 下载方法
     */
    download(url) {
      const a = document.createElement('a');
      a.href = url;
      a.click();
    }
  }
};
</script>

<style lang="scss">
.el-dialog.import-box {
  width: 400px;
  height: 240px;
  top: 50%;
  transform: translateY(-50%);
  .el-dialog__header {
    height: 50px;
    padding: 14px 20px;
    box-sizing: border-box;
    border-bottom: 1px solid rgba(238, 238, 238, 1);
    .el-dialog__title {
      height: 22px;
      font-size: 16px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: rgba(48, 49, 51, 1);
      line-height: 22px;
      float: left;
    }
    .el-dialog__headerbtn {
      top: 16px;
    }
  }
  .el-dialog__body {
    height: 190px;
    padding: 20px;
    position: relative;
    .import-content {
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(41, 41, 51, 1);
      a {
        color: #3b95a8;
      }
    }
    .import-result {
      margin: 0;
      padding: 0;
      overflow: hidden;
      dt {
        float: left;
        margin: 0 30px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(144, 147, 153, 1);
        &:first-child {
          margin-left: 0;
        }
        &:last-child {
          margin-right: 0;
        }
      }
    }
    > div {
      position: relative;
      height: 113px;
      .el-button {
        height: 36px;
        line-height: 36px;
        padding: 0 11px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        position: absolute;
        bottom: 0px;
        left: 50%;
        transform: translateX(-50%);
      }
      .el-upload-list {
        position: absolute;
        top: 18px;
        width: 100%;
        li {
          margin: 0;
          padding-top: 16px;
          height: 95px;
          .el-progress {
            height: 6px;
            top: 0px;
            .el-progress-bar {
              border-radius: 3px;
              height: 100%;
              width: calc(100% - 40px);
              float: left;
              .el-progress-bar__outer {
                height: 100% !important;
              }
            }
            .el-progress__text {
              top: 50%;
              transform: translateY(-50%);
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: rgba(144, 147, 153, 1);
            }
          }
          &:hover {
            background: none;
            .el-progress__text {
              display: inline;
            }
          }
          &:focus {
            outline: none;
          }
          span.file-name {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(144, 147, 153, 1);
          }
        }
      }
    }
    > .el-button {
      height: 36px;
      line-height: 36px;
      padding: 0 19px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      position: absolute;
      bottom: 20px;
      right: 20px;
    }
  }
}
</style>
