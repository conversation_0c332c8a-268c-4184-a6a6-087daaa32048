<template>
  <el-dialog :visible.sync="shown" :title="title" top="0" custom-class="template-preview-box">
    <el-form label-width="40px">
      <layout :lay-datas="layDatas" :col-datas="list">
        <template slot="col" slot-scope="{ arr,row,col }">
          <div class="template-col">
            <preview
              v-if="arr[row][col]"
              :keys="arr[row][col].fieldType"
              :preview="arr[row][col]"
              :read="true"
            ></preview>
          </div>
        </template>
      </layout>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="shown = false">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import preview from '@/components/Fields/preview';
import layout from '@/components/templates/layout';
export default {
  name: 'TemplatePreview',
  components: { preview, layout },
  props: {
    title: {
      type: String,
      default: '提示框'
    },
    width: {
      type: String,
      default: '737px'
    },
    list: {
      type: Array,
      default: () => []
    },
    layDatas: {
      type: String,
      default: ''
    }
  },
  data() {
    return { shown: false };
  },
  methods: {
    show() {
      this.shown = true;
    }
  }
};
</script>
<style lang="scss">
.el-dialog.template-preview-box {
  width: 750px;
  height: 500px;
  transform: translateY(-50%);
  top: 50%;
  .el-dialog__header {
    height: 52px;
    padding: 15px 20px;
    border-bottom: 1px solid #e4e4eb;
    .el-dialog__title {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(41, 41, 51, 1);
      height: 22px;
      line-height: 22px;
    }
    .el-dialog__headerbtn {
      top: 18px;
    }
  }
  .el-dialog__body {
    height: calc(100% - 128px);
    padding: 10px;
    box-sizing: border-box;
    overflow-y: auto;
    .template-col {
      min-height: 1px;
      .preview {
        .single {
          margin: 10px;
        }
      }
    }
  }
  .el-dialog__footer {
    padding: 20px;
    .el-button {
      height: 36px;
      line-height: 36px;
      padding: 0 19px;
    }
  }
}
</style>
