<template>
  <div class="step-container">
    <!-- <transition name="fade">
      <img
        v-if="(active==1&&showNum==0)||showGuide"
        class="guid"
        src="../../../assets/common/guide.png"
        @click="handleGuide"
      />
    </transition>-->
    <el-steps :active="active" finish-status="success" align-center>
      <template v-for="step in steps">
        <el-step :title="step.name" :key="step.name" />
      </template>
    </el-steps>
    <!-- <xyy-button v-show="active==1" icon-class="book" class="new-guide" type="text" @click="showGuide=true">新手引导</xyy-button> -->
  </div>
</template>

<script>
export default {
  props: {
    active: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      steps: [
        { name: '基础配置' },
        { name: '模板页面编辑' },
        { name: '模板字段联动' }
      ],
      showNum: 0,
      showGuide: false
    };
  },
  mounted() {
    this.showNum = localStorage.getItem('showNum')
      ? parseInt(localStorage.getItem('showNum'))
      : 0;
  },
  methods: {
    handleGuide() {
      this.showNum++;
      localStorage.setItem('showNum', this.showNum);
      this.showGuide = false;
    }
  }
};
</script>
<style lang="scss" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 1s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}
.guid {
  position: absolute;
  margin: 0 auto;
  width: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}
.new-guide {
  position: absolute;
  right: 0;
  bottom: -20px;
}
.el-button.new-guide.is-plain {
  &:hover,
  &:focus {
    border: none !important;
  }
}
.step-container {
  position: relative;
  padding: 10px 200px;
  /deep/ .el-step__line {
    height: 1px;
    background: #e9e9e9;
    margin: 0 24px !important;
  }
  /deep/ .el-step__head.is-success {
    color: #3b95a8;
    border-color: #3b95a8;
    .el-step__line {
      background: #3b95a8;
    }
  }
  /deep/ .el-step__title.is-success {
    color: #3b95a8;
  }
  /deep/ .is-process .is-text {
    color: #ffffff;
    background: #3b95a8;
    border-color: #3b95a8;
  }
}
</style>
