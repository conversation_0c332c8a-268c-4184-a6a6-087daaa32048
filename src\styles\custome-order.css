ul,
ol,
li,
div,
dl,
dt,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
span {
  margin: 0;
  padding: 0;
}
ul,
ol {
  list-style: none;
}
em,
i {
  font-style: normal;
}
input:focus {
  border: 1px solid #e4e4eb !important;
  box-shadow: none !important;
}
.marnone li {
  width: 16.6% !important;
}
.bind-custome {
  font-family: PingFangSC-Semibold, PingFangSC;
}

.left-container {
  float: left;
  width: calc(100% - 490px);
  height: 100%;
}
.right-container {
  float: left;
  width: 468px;
  height: 100%;
  margin: 0;
  margin-left: 20px;
  border: 1px solid #dcdee3;
  overflow: auto;
}

.ele-searchbox {
  padding: 15px 22px 0 20px;
}

.ele-searchbox .el-autocomplete {
  width: 100%;
}

.ele-searchbox .el-input input {
  height: 36px;
}

.ele-info {
  padding-top: 22px;
}

.ele-info header {
  padding-left: 20px;
  padding-bottom: 16px;
  height: 22px;
  font-size: 16px;
  font-weight: 600;
  color: rgba(48, 49, 51, 1);
  line-height: 22px;
}

.ele-info .shop-name {
  padding-left: 20px;
  padding-bottom: 8px;
  width: calc(100% - 30px);
  font-size: 14px;
  font-weight: 500;
  color: rgba(48, 49, 51, 1);
  line-height: 28px;
}

.ele-info .shop-name em {
  display: block;
  width: calc(100% - 80px);
  line-height: 22px;
  color: #303133;
}

.ele-info .shop-name input {
  width: 68px;
  height: 28px;
  outline: none;
  background: rgba(255, 255, 255, 1);
  border-radius: 2px;
  border: 1px solid rgba(59, 149, 168, 1);
  color: rgba(59, 149, 168, 1);
}

.line {
  margin-top: 7px;
  height: 0px;
  border-top: 1px dashed rgba(220, 222, 227, 1);
  margin-left: 20px;
  margin-right: 20px;
}

.ele-info ul {
  margin: 0;
  padding-left: 20px;
  padding-bottom: 7px;
  font-weight: normal;
}

.ele-info ul li {
  padding-top: 7px;
  padding-bottom: 7px;
  font-size: 14px;
  font-weight: 400;
  min-height: 20px;
  line-height: 20px;
  min-width: 48%;
  clear: both;
}

.ele-info ul li i {
  display: inline-block;
  color: rgba(41, 41, 51, 1);
  width: 70px;
  float: left;
  text-align-last: justify;
}

.ele-info ul li span {
  display: inline-block;
  color: rgba(87, 87, 102, 1);
  max-width: 80%;
  float: left;
}

.ele-info ul li span > em {
  color: #67c23a;
}

.order-container {
  padding-left: 20px;
  padding-right: 20px;
}

.order-header {
  padding-top: 20px;
  padding-bottom: 10px;
}

.order-info header .el-input {
  width: 75%;
  height: 38px;
  border-radius: 2px;
  border: 1px solid rgba(228, 228, 235, 1);
}
/deep/.order-info header .el-input__inner {
  border: none;
  background: none;
  height: 100%;
  outline: none;
}

.search-btn {
  width: 68px;
  height: 36px;
  background: rgba(59, 149, 168, 1);
  border-radius: 2px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 1);
  line-height: 20px;
  border: none;
  outline: none;
  cursor: pointer;
}

.order-list li {
  margin-top: 10px;
  margin-bottom: 10px;
  background: rgba(255, 255, 255, 1);
  border-radius: 2px;
  border: 1px solid rgba(241, 242, 244, 1);
}

.order-list li header {
  height: 36px;
  background: rgba(241, 242, 244, 0.33);
  line-height: 36px;
  padding-left: 12px;
  padding-right: 12px;
}

.order-list li header p {
  font-size: 12px;
  color: rgba(144, 147, 153, 1);
}

.order-list li header i {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 2px;
  font-size: 12px;
  line-height: 16px;
  height: 16px;
}

.order-list-wrapper a.img {
  display: block;
  width: 100px;
  height: 100px;
  margin: 11px 20px 12px 12px;
}

.order-list-wrapper a.img img {
  max-width: 100%;
}

.order-list-wrapper .detail {
  width: calc(100% - 132px);
}

.order-list-wrapper .detail header {
  padding-left: 0;
  padding-right: 12px;
  background: #fff;
}

.order-list-wrapper .detail header h2 {
  width: 70%;
  height: 36px;
  line-height: 36px;
  max-width: 270px;
  font-size: 14px;
  margin: 0;
  padding: 0;
}

.order-list-wrapper .detail header a {
  display: inline-block;
  width: 30%;
  color: rgba(64, 163, 184, 1);
  text-align: right;
  font-size: 12px;
}

.order-list-wrapper .detail time {
  display: block;
  height: 17px;
  font-size: 12px;
  color: rgba(144, 147, 153, 1);
  line-height: 17px;
  margin-top: 7px;
}

.order-list-wrapper .detail p {
  margin-top: 14px;
  margin-right: 0;
  height: 22px;
  line-height: 22px;
  color: rgba(87, 87, 102, 1);
}

.order-list-wrapper .detail p span:first-child {
  margin-right: 10px;
}

.order-list-wrapper .detail p span:last-child {
  font-size: 12px;
}

.order-list-wrapper .detail p span:last-child i {
  font-size: 16px;
}

/*订单详情*/
.order-detail {
  margin-top: 15px;
}

.order-detail dl dd {
  padding-top: 7px;
  padding-bottom: 7px;
  border-bottom: 1px dashed rgba(220, 222, 227, 1);
}

.order-detail dl dd:last-child {
  border-bottom: none;
}
.order-detail p:first-child {
  margin-top: 0;
}
.order-detail p,
.refundPic {
  /*height:35px;*/
  line-height: 35px;
  font-size: 14px;
  color: rgba(87, 87, 102, 1);
  margin-top: 7px;
  margin-bottom: 7px;
}

.order-detail p em {
  font-size: 14px;
  color: #575766;
  padding-right: 10px;
}

.order-detail-header {
  position: relative;
}

.order-detail-user em {
  display: inline-block;
  font-weight: bold;
}

.order-detail-user span {
  display: inline-block;
  max-width: calc(100% - 75px);
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  font-weight: normal;
}

.back-btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 74px;
  height: 32px;
  border-radius: 2px;
  color: rgba(87, 87, 102, 1);
  border: 1px solid rgba(228, 228, 235, 1);
  font-size: 14px;
  background: url("../assets/common/back_icon.png") no-repeat 12% center;
  background-size: 14px;
  padding-left: 12px;
}

.order-detail-header h1 {
  font-size: 16px;
  font-weight: 600;
  color: rgba(48, 49, 51, 1);
  text-align: center;
  height: 32px;
  line-height: 32px;
}

.order-detail dl dd .left {
  display: inline-block;
  width: 68px;
  height: 35px;
  line-height: 35px;
  padding-right: 10px;
  font-size: 14px;
  font-weight: 600;
  color: rgba(87, 87, 102, 1);
}
.order-detail dl dd .right {
  width: 327px;
}

.order-detail-price .right {
  text-align: right;
}

.order-detail-price .right span {
  color: rgba(255, 48, 36, 1);
}

.order-detail-price .right span i {
  font-size: 18px;
  font-weight: 500;
}

.order-detail-info .el-collapse {
  width: 100%;
  border: none !important;
}

/deep/ .order-detail-info .el-collapse-item__header {
  border: none !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  color: rgba(87, 87, 102, 1) !important;
}

/deep/.order-detail-info .el-collapse-item__wrap {
  border: none;
}

/*商品明细*/
.product-list-wrapper li {
  background: rgba(245, 247, 250, 1);
  border-radius: 2px;
  border: 1px solid rgba(228, 228, 235, 1);
  margin-bottom: 14px;
}

.product-list-wrapper-header {
  padding-top: 12px;
  padding-bottom: 12px;
}

.product-list-wrapper-header a.img {
  display: block;
  width: 100px;
  height: 100px;
  margin: 11px 20px 12px 12px;
  overflow: hidden;
}
.product-list-wrapper-header a.img img {
  width: 100%;
}

.product-list-wrapper-header .detail {
  width: calc(100% - 132px);
  padding-right: 10px;
}

.product-list-wrapper-header header h2 {
  width: 70%;
  font-size: 14px;
  line-height: 20px;
  color: rgba(41, 41, 51, 1);
  font-weight: normal;
}

.product-list-wrapper-header header a {
  display: inline-block;
  width: 30%;
  color: rgba(41, 41, 51, 1);
  font-size: 14px;
  text-align: right;
}

.product-list-wrapper-header .detail p {
  height: 25px;
  line-height: 25px;
  color: #909399;
  font-size: 12px;
  font-weight: normal;
}

.product-list-wrapper-header .detail p span:nth-child(1) {
  display: block;
  width: 30px;
}

.product-list-wrapper-header .detail p span:nth-child(2) {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.product-list-wrapper-header .detail p:nth-child(2) {
  margin-top: 10px;
}

.product-list-wrapper-header .detail p:nth-child(2) span:nth-child(1) {
  display: inline-block;
  width: 80%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.product-list-wrapper-header .detail p:nth-child(2) span:nth-child(2) {
  font-size: 10px;
  color: rgba(87, 87, 102, 1);
}

.product-list-wrapper-header .detail p:nth-child(3) span:nth-child(1),
.product-list-wrapper-header .detail p:nth-child(4) span:nth-child(1) {
  display: inline-block;
  width: 14px;
  height: 14px;
  line-height: 14px;
  background: rgba(241, 242, 244, 0.32);
  border-radius: 2px;
  border: 1px solid rgba(220, 222, 227, 1);
  margin-right: 10px;
}

.product-list-wrapper-header .detail p:nth-child(3) span:nth-child(1) i,
.product-list-wrapper-header .detail p:nth-child(4) span:nth-child(1) i {
  font-size: 10px;
  /*color:rgba(87,87,102,1);*/
  color: #909399;
  -webkit-transform: scale(0.8);
  -moz-transform: scale(0.8);
  -ms-transform: scale(0.8);
  -o-transform: scale(0.8);
  transform: scale(0.8);
}

.product-list-wrapper-bottom {
  padding-top: 7px;
  padding-bottom: 7px;
  border-top: 1px dashed rgba(220, 222, 227, 1);
  padding-left: 14px;
}

.product-list-wrapper-bottom span {
  display: inline-block;
  width: 50%;
  height: 34px;
  font-size: 14px;
  color: rgba(87, 87, 102, 1);
  line-height: 34px;
}

.product-list-wrapper-bottom span i {
  font-size: 16px;
  font-weight: 500;
  color: rgba(87, 87, 102, 1);
}

.order-detail-info .el-collapse-item__content {
  padding-bottom: 0 !important;
}

/*订单状态：1-待配送，2-配送中，3-已配送，4-取消，5-已删除,6-已拆单,7-出库中,10-未支付,90-退款审核中,91-已退款,20-已送达,21-已拒签--%>*/
.status-20 {
  color: rgba(93, 177, 51, 1);
  background: rgba(230, 251, 220, 1);
}

.status-10,
.status-2,
.status-7,
.status-3 {
  color: rgba(230, 162, 60, 1);
  background: rgba(253, 246, 236, 1);
}

.status-4,
.status-91,
.status-90,
.status-5,
.status-21 {
  color: rgba(255, 48, 36, 1);
  background: rgba(255, 233, 232, 1);
}

.bg-none {
  background: none;
}
.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

/deep/ .el-menu-item {
  padding: 0 20px;
}
/deep/ .el-collapse-item__header {
  border-bottom: none;
}
