const getters = {
  token: state => state.user.token,
  avatar: state => state.user.avatar,
  containerid: state => state.imProperty.containerid, // 会话ID
  kfid: state => state.imProperty.kfid, // 客服ID
  khid: state => state.imProperty.khid, // 客户ID
  chat: state => state.imProperty.chat, //会话信息
  returnResult: state => state.imProperty.returnResult, // 点击右侧常用列表返回结果
  name: state => state.user.name,
  channelList: state => state.user.channelList, // 用户渠道列表
  channel: state => state.user.channel, // 用户渠道
  userId: state => state.user.userId,
  roles: state => state.user.roles,
  menu: state => state.user.menu,
  validBtn: state => state.user.validBtn,
  permission_routes: state => state.permission.routes,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  excludeViews: state => state.tagsView.excludeViews,
  pageLoading: state => state.page.pageLoading,
  isChild: state => state.page.isChild,
  isDot: state => state.message.isDot,
  box_status: state => state.message.status,
  draftNum: state => state.draft.draftNum,
  newListClose: state => state.tagsView.newListClose,
  workType: state => state.workType.workType,
  newRoutes: state => state.workType.newRoutes,
  path: state => state.tagsView.path,
  imcreate: state => state.imProperty.containercreat,
  getSelectView: state => view => {
    return state.tagsView.visitedViews.find(route => route.path === view.path);
  },
  getActiveTagViewRouter: state => state.tagsView.activeTagViewRouter, //获取当前激活tagView的router对象
  editContentData: state => key => state.editContent.editContentData[key],
  editDynamicFormData: state => key =>
    state.editDynamicForm.editDynamicFormData[key],
  orderModifyFlagtoList: state => state.workSheet.orderModifyFlagtoList,
  sheetItem: state => key => state.imProperty.sheetItem[key]
};
export default getters;
