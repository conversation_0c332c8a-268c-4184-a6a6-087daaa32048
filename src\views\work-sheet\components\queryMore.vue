<template>
  <div class="more-query-component-container">
    <div class="top-selector">
      <el-form size="medium">
        <el-form-item>
          <el-select
            placeholder="-请选择筛选条件-"
            v-model="selector"
            popper-class="query-more-selector"
            @change="selectorChange"
          >
            <el-option
              v-for="item in exchangeSelectorList()"
              :key="item.field"
              :value="item.field"
              :label="item.label"
              :disabled="item.disabled"
            ></el-option>
          </el-select>
          <el-button type="primary" @click="selectorAdd">添加</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="middle-content">
      <el-form size="medium" :inline="true" label-width="100px">
        <!-- 遍历获取表单,begin -->
        <template v-for="selector in selectedFieldList">
          <el-form-item :key="selector" :label="selectorList[selector]['label']">
            <div class="item-form-input">
              <!-- input类型 -->
              <template v-if="selectorList[selector]['fieldType'] === 'input'">
                <!-- 电话，20位数字 -->
                <template v-if="selectorList[selector]['inputType'] === 'tel'">
                  <el-input v-model="selectorList[selector]['vModel']" maxlength="20"></el-input>
                </template>
                <template v-else>
                  <el-input v-model="selectorList[selector]['vModel']"></el-input>
                </template>
              </template>
              <!-- 评分 -->
              <template v-else-if="selectorList[selector]['fieldType'] === 'rate'">
                <el-rate v-model="selectorList[selector]['vModel']"></el-rate>
              </template>
              <!-- 单选 -->
              <template v-else-if="selectorList[selector]['fieldType'] === 'select'">
                <el-select v-model="selectorList[selector]['vModel']" placeholder="请选择">
                  <el-option
                    v-for="item in selectorList[selector]['options']"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled ? item.disabled : false"
                  ></el-option>
                </el-select>
              </template>
              <!-- 多选 -->
              <template v-else-if="selectorList[selector]['fieldType'] === 'multipleSelect'">
                <el-select v-model="selectorList[selector]['vModel']" multiple placeholder="请选择">
                  <el-option
                    v-for="item in selectorList[selector]['options']"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabledMenuIndex?item.disabledMenuIndex.includes(menuIndex):false"
                  ></el-option>
                </el-select>
              </template>
              <!-- 时分秒时间选择 -->
              <template v-else-if="selectorList[selector]['fieldType'] === 'rangeSeparator'">
                <el-time-picker v-model="selectorList[selector]['vModel']" placeholder="时分秒"></el-time-picker>
              </template>
              <!-- 级联选择 -->
              <template v-else-if="selectorList[selector]['fieldType'] === 'cascader'">
                <el-cascader
                  v-model="selectorList[selector]['vModel']"
                  :options="selectorList[selector]['options']"
                  :props="{ checkStrictly: true,emitPath:false,value:'typeCode',label:'typeName' }"
                  clearable
                ></el-cascader>
              </template>
              <!-- 日期范围 -->
              <template v-else-if="selectorList[selector]['fieldType'] === 'dateRange'">
                <el-date-picker
                  v-model="selectorList[selector]['vModel']"
                  :default-time="['00:00:00', '23:59:00']"
                  type="datetimerange"
                  range-separator="-"
                  size="small"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  prefix-icon="el-icon-date"
                />
              </template>
              <!-- 日期范围 -->
              <template v-else-if="selectorList[selector]['fieldType'] === 'dateRangeTime'">
                <el-date-picker
                  v-model="selectorList[selector]['vModel']"
                  :default-time="['00:00:00', '23:59:00']"
                  type="datetimerange"
                  range-separator="-"
                  size="small"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  prefix-icon="el-icon-date"
                />
              </template>
            </div>
            <el-button
              class="item-btn-del"
              type="danger"
              icon="el-icon-delete"
              circle
              @click="selectorDel(selector)"
            ></el-button>
          </el-form-item>
        </template>
        <!-- 遍历获取表单,end -->
      </el-form>
    </div>

    <div class="bottom-btn-group">
      <el-button size="medium" type="primary" @click="search">查询</el-button>
      <el-button size="medium" @click="reset">重置</el-button>
      <el-button size="medium" @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script>
import moment from 'moment';
import {
  getCustomerSourceList,
  getTypeformat,
  getListSourceChannel,
} from '@/api/mySheetManage';
export default {
  name: '',
  components: {},
  filters: {},
  props: {
    menuIndex: {
      //  '1':'已超时';
      //  '2':'即将超时';
      //  '3':'我处理中的';
      //  '4':'待领取的';
      //  '5':'我已处理的';
      //  '6':'我发起的';
      //  '7':'抄送给我的';
      //  '8':'全部';
      type: String,
      default: '',
    },
    formTypeId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      selector: '', // 条件集合下拉框
      // 条件集合
      selectorList: {
        workorder_num: {
          fieldType: 'input', // 字段类型
          label: '工单编号', // 名称
          disabled: false, // 是否禁用
          vModel: '', // v-model绑定的值
        },
        s1164456270271483904: {
          fieldType: 'multipleSelect',
          label: '优先级',
          disabled: false,
          vModel: '',
          options: [
            {
              value: '1',
              label: '普通',
            },
            {
              value: '2',
              label: '紧急',
            },
            {
              value: '3',
              label: '加急',
            },
          ],
        },
        s1164456058157142016: {
          fieldType: 'input',
          label: '客户名称',
          disabled: false,
          vModel: '',
        },
        s1164724692221825024: {
          fieldType: 'cascader',
          label: '问题分类',
          disabled: false,
          vModel: '',
          options: [],
        },
        s1164723822566445056: {
          fieldType: 'input',
          inputType: 'tel',
          label: '客户电话',
          disabled: false,
          vModel: '',
        },
        s1870039678284075008: {
          fieldType: 'input',
          label: '商家ID',
          disabled: false,
          vModel: '',
        },
        current_state: {
          fieldType: 'multipleSelect',
          label: '当前状态',
          disabled: false,
          vModel: '',
          options: [
            //  '1':'已超时';
            //  '2':'即将超时';
            //  '3':'我处理中的';
            //  '4':'待领取的';
            //  '5':'我已处理的';
            //  '6':'我发起的';
            //  '7':'抄送给我的';
            //  '8':'全部';
            {
              value: '0',
              label: '待领取',
              disabledMenuIndex: ['3'],
            },
            {
              value: '1',
              label: '处理中',
              disabledMenuIndex: ['4'],
            },
            {
              value: '2',
              label: '已结单',
              disabledMenuIndex: ['3', '4'],
            },
            {
              value: '3',
              label: '退回',
              disabledMenuIndex: ['3', '4'],
            },
            {
              value: '5',
              label: '作废',
              disabledMenuIndex: ['3', '4'],
            },
          ],
        },
        s1164723673773510656: {
          fieldType: 'multipleSelect',
          label: '来源渠道',
          disabled: false,
          vModel: '',
          options: [
            // {
            //   value: '0',
            //   label: '电话',
            // },
            // {
            //   value: '1',
            //   label: '微信公众号',
            // },
            // {
            //   value: '2',
            //   label: 'APP',
            // },
            // {
            //   value: '3',
            //   label: 'PC端退款',
            // },
            // {
            //   value: '4',
            //   label: '邮件',
            // },
            // {
            //   value: '6',
            //   label: '企业微信',
            // },
            // {
            //   value: '7',
            //   label: '智慧脸',
            // },
            // {
            //   value: '8',
            //   label: 'POP',
            // },
            // {
            //   value: '5',
            //   label: '其他（非客服中心发起）',
            // },
          ],
        },
        s1176037136521105408: {
          fieldType: 'multipleSelect',
          label: '客户所在地',
          disabled: false,
          vModel: '',
          options: [],
        },
        current_processing_person: {
          fieldType: 'input',
          label: '当前受理人',
          disabled: false,
          vModel: '',
        },
        recent_acceptance: {
          fieldType: 'dateRange',
          label: '最近受理时间',
          disabled: false,
          vModel: '',
        },
        latest_submission: {
          fieldType: 'dateRange',
          label: '最近提交时间',
          disabled: false,
          vModel: '',
        },
        creator: {
          fieldType: 'input',
          label: '工单创建人',
          disabled: false,
          vModel: '',
        },
        query: {
          fieldType: 'dateRange',
          label: '工单发起时间',
          disabled: false,
          vModel: '',
        },
        end_person: {
          fieldType: 'input',
          label: '工单结单人',
          disabled: false,
          vModel: '',
        },
        completion: {
          fieldType: 'dateRange',
          label: '工单完成时间',
          disabled: false,
          vModel: '',
        },
        customer_satisfaction: {
          fieldType: 'select',
          label: '满意度评价',
          disabled: false,
          vModel: '',
          options: [
            {
              value: '0',
              label: '否',
            },
            {
              value: '1',
              label: '是',
            },
          ],
        },
        satisfaction_time: {
          fieldType: 'dateRangeTime',
          label: '评价时间',
          disabled: false,
          vModel: '',
        },
        starLevel1: {
          fieldType: 'rate',
          label: '评价维度1',
          disabled: false,
          vModel: 0,
        },
        starLevel2: {
          fieldType: 'rate',
          label: '评价维度2',
          disabled: false,
          vModel: 0,
          // options: [
          //   {
          //     value: '0',
          //     label: '☆'
          //   },
          //   {
          //     value: '1',
          //     label: '☆☆'
          //   },
          //   {
          //     value: '2',
          //     label: '☆☆☆'
          //   },
          //   {
          //     value: '3',
          //     label: '☆☆☆☆'
          //   },
          //   {
          //     value: '4',
          //     label: '☆☆☆☆☆'
          //   }
          // ]
        },
        evaluate: {
          fieldType: 'select',
          label: '评价回复',
          disabled: false,
          vModel: '',
          options: [
            {
              value: '0',
              label: '未回复',
            },
            {
              value: '1',
              label: '已回复',
            },
          ],
        },
      },
      selectedFieldList: [], //已经选择的条件
    };
  },
  computed: {
    // 获取业务线code
    computerBusinessPartCode: function() {
      if (this.$store.getters.channel && this.$store.getters.channel.businessPartCode) {
        return this.$store.getters.channel.businessPartCode;
      }
      return ''
    }
  },
  watch: {
    formTypeId: function (id) {
      this.selectorList.s1164724692221825024.vModel = '';
      this.selectorList.s1164724692221825024.options = [];
      this.$nextTick(() => {
        this.getTypeformat(id);
      });
    },
  },
  created() {},
  mounted() {
    this.exchangeSelectorList();
  },
  methods: {
    /**
     * 条件下拉框json转换
     */
    exchangeSelectorList() {
      let retList = [];      
      Object.keys(this.selectorList).forEach((key) => {
        retList.push(
          Object.assign({}, this.selectorList[key], {
            field: key,
          })
        );
      });

      if(this.computerBusinessPartCode) {
        // 非药帮忙业务线，过滤调商家ID筛选项
        if(!['S00009999'].includes(this.computerBusinessPartCode)) {
          retList = retList.filter(rl => rl.field != 's1870039678284075008')
        }
      }

      return retList;
    },

    /**
     * selector的change事件
     */
    selectorChange(value) {
      switch (value) {
        case 's1176037136521105408':
          //客户所在地请求
          this.getCustomerSourceList();
          break;
        case 's1164724692221825024':
          //问题分类
          this.getTypeformat(this.formTypeId);
          break;
        case 's1164723673773510656':
          //来源渠道
          this.getListSourceChannel();
          break;
        default:
          break;
      }
    },

    /**
     * 获取客户所在地
     */
    getCustomerSourceList() {
      getCustomerSourceList({
        _t: new Date().getTime(),
      }).then((resp) => {
        if (resp.code === 1) {
          if (resp.data) {
            this.selectorList.s1176037136521105408.options = []; //清空已有数据
            resp.data.forEach((item) => {
              this.selectorList.s1176037136521105408.options.push({
                value: item.id,
                label: item.sourceName,
              });
            });
          }
        }
      });
    },

    /**
     * 获取问题分类
     */
    getTypeformat(formTypeId) {
      getTypeformat({
        formTypeId,
      }).then((resp) => {
        if (resp.code === 1) {
          this.selectorList.s1164724692221825024.options =
            resp.data.treeOptions.optionsArray;
        }
      });
    },

    /**
     * 获取渠道来源
     */
    getListSourceChannel() {
      getListSourceChannel().then((resp) => {
        if (resp.code === 1) {
          if (resp.data) {
            try {
              let data = JSON.parse(resp.data);
              if (
                data.selectOptions &&
                data.selectOptions.optionsArray &&
                data.selectOptions.optionsArray.length
              ) {
                this.selectorList.s1164723673773510656.options = []; //清空已有数据
                data.selectOptions.optionsArray.forEach((item) => {
                  this.selectorList.s1164723673773510656.options.push({
                    value: item.val,
                    label: item.optionsDesc,
                  });
                });
              }
            } catch (e) {}
          }
        }
      });
    },

    /**
     * 添加条件
     */
    selectorAdd() {
      if (!this.selector) {
        return false;
      }
      if (!this.selectedFieldList.includes(this.selector)) {
        this.selectedFieldList.push(this.selector);
        this.selectorList[this.selector].disabled = true;
      }
    },

    /**
     * 删除条件
     */
    selectorDel(selector) {
      if (this.selectedFieldList.includes(selector)) {
        this.selectedFieldList.splice(
          this.selectedFieldList.indexOf(selector),
          1
        );
        this.selectorList[selector].disabled = false;
        this.selectorList[selector].vModel = '';
      }
    },

    /**
     * 获取自定义的查询条件对象
     */
    getQueryMoreParams() {
      let retParams = {};
      for (const item in this.selectorList) {
        if (this.selectorList.hasOwnProperty(item)) {
          if (
            this.selectorList[item].disabled &&
            this.selectorList[item].vModel
          ) {
            switch (this.selectorList[item].fieldType) {
              case 'multipleSelect':
                if (
                  Object.prototype.toString
                    .call(this.selectorList[item].vModel)
                    .indexOf('Array') > -1 &&
                  this.selectorList[item].vModel.length
                ) {
                  retParams[item] = this.selectorList[item].vModel.join(',');
                }
                break;
              case 'dateRange':
                retParams[item + '_start_time'] = this.selectorList[
                  item
                ].vModel[0];
                retParams[item + '_end_time'] = this.selectorList[
                  item
                ].vModel[1];
                break;
              case 'dateRangeTime':
                retParams['startTime'] = this.selectorList[item].vModel[0];
                retParams['endTime'] = this.selectorList[item].vModel[1];
                break;
              case 'rangeSeparator':
                retParams[item] = moment(this.selectorList[item].vModel).format(
                  'HH:mm:ss'
                );
                break;
              default:
                retParams[item] = this.selectorList[item].vModel;
                break;
            }
          }
        }
      }
      return retParams;
    },

    /**
     * 查询
     */
    search() {
      this.$emit('search', this.getQueryMoreParams());
    },

    /**
     * 重置
     */
    reset() {
      this.selectedFieldList = [];
      for (const item in this.selectorList) {
        if (this.selectorList.hasOwnProperty(item)) {
          this.selectorList[item].disabled = false;
          this.selectorList[item].vModel = '';
        }
      }
    },

    /**
     * 取消
     */
    cancel() {
      this.$emit('hide');
    },
  },
};
</script>

<style lang="scss" scoped>
.more-query-component-container {
  width: 100%;
  padding: 12px;
  /deep/ .el-form-item--medium .el-form-item__label {
    line-height: 18px;
  }
  /deep/ .el-form-item__label {
    max-width: 95px;
  }
  .top-selector {
    margin-bottom: 12px;
    .el-form-item {
      margin-bottom: 0;

      /deep/ .el-form-item__content {
        display: flex;

        .el-button {
          margin-left: 10px;
        }
      }
    }
  }

  .middle-content {
    width: 100%;
    padding: 12px;
    min-height: 200px;
    max-height: 300px;
    overflow-y: scroll;

    .el-form-item {
      width: 100%;
      margin-right: 0;
      margin-bottom: 12px;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      /deep/ .el-form-item__content {
        flex-grow: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .item-form-input {
          flex-grow: 1;
          display: flex;

          & > .el-input,
          & > .el-select,
          & > .el-cascader,
          & > .el-date-editor {
            width: 100%;
          }
        }

        .item-btn-del {
          padding: 6px;
          margin-left: 12px;

          & > i {
            width: 16px;
            height: 16px;
            line-height: 16px;
          }
        }
      }
    }
  }

  .bottom-btn-group {
    width: 100%;
    margin-top: 12px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}

.query-more-selector {
  .el-select-dropdown__item.is-disabled {
    color: #c0c4cc !important;
    font-weight: normal !important;

    & + .hover {
      background-color: #fff !important;
    }
  }
}
</style>
