import Layout from "@/views/layout/Layout";
export default [
  {
    path: "/shop",
    component: Layout,
    name: "shop",
    meta: {
      title: "业务查询",
      affix: true,
      code: "menu:cs:business",
      mark: "shop"
    },
    children: [
      {
        path: "list",
        name: "shopList",
        meta: {
          title: "商品管理",
          code: "menu:cs:merchandisemanagement",
          mark: "shop",
          componentName: 'Index'
        },
        component: () => import("@/views/shop/manage")
      },
      {
        path: "customerInfoRegistration",
        name: "shopCustomerInfoRegistration",
        meta: {
          title: "客户信息登记",
          code: "menu:cs:shopcustomerinforegistration",
          mark: "shop",
          componentName: 'CustomerInfoRegistration'
        },
        component: () => import("@/views/shop/customerInfoRegistration")
      }
    ]
  }
];
