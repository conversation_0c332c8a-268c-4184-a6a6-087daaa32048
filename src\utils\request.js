import axios from 'axios';
import { Message, MessageBox } from 'element-ui';
import store from '../store';
import { getToken } from '@/utils/auth';
import router from '../router';

const pending = [];
const CancelToken = axios.CancelToken;
const removePending = config => {
  const url =
    config.url.indexOf(process.env.BASE_API) >= 0
      ? config.url
      : process.env.BASE_API + config.url;
  pending.forEach((el, i) => {
    if (process.env.BASE_API + el.key === url + '&' + config.method) {
      el.cancel(); // 执行取消操作
      pending.splice(i, 1); // 把这条记录从数组中移除
    }
  });
};
// 创建axios实例
const service = axios.create({
  baseURL: process.env.BASE_API,
  // baseURL: 'http://ec-service.test.ybm100.com',
  headers: {
    // is_ajax_request: true,
    'X-Requested-With': 'XMLHttpRequest'
  },
  // baseURL: 'http://ec-service.dev.ybm100.com:80',
  // baseURL: 'http://ec-service.dev.ybm100.com:8019',
  // timeout: 5000, // 请求超时时间
  withCredentials: process.env.credential // 本地跨域请求，携带cookie标识
});
// request拦截器
service.interceptors.request.use(
  config => {
    if (store.getters.channel && store.getters.channel.businessPartCode) {
      config.headers.businessPartCode = store.getters.channel.businessPartCode;
    }
    // if (store.getters.token) {
    //   config.headers['X-Token'] = getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
    // }
    removePending(config); // 在一个ajax发送前执行一下取消操作
    config.cancelToken = new CancelToken(cb => {
      // 这里的ajax标识我是用请求地址&请求方式拼接的字符串，当然你可以选择其他的一些方式
      pending.push({ key: config.url + '&' + config.method, cancel: cb });
    });
    return config;
  },
  error => {
    // Do something with request error
    Promise.reject(error);
  }
);

// response 拦截器
service.interceptors.response.use(
  response => {
    removePending(response.config); // 在一个ajax响应后再执行一下取消操作，把已经完成的请求从pending中移除
    const res = response.data;

    if (response.status !== 200) {
      Message({
        message: res.message,
        type: 'error',
        duration: 5 * 1000
      });
      // 如果302重定向了，则需要登录
      // if (response.status === 401) {
      //   window.location = 'https://messo.dev.ybm100.com';
      // } else {
      //   Message({
      //     message: '系统异常',
      //     type: 'error'
      //   });
      // }
    } else {
      if (res.code === 401) {
        window.location = res.url;
      }
      if (res.code === 100) {
        window.location = 'http://sso.ybm100.com';
      } else {
        return res;
      }
    }
  },
  error => {
    /** 
    if (!error.message || !error.msg) {
      const promise = new Promise((resolve, reject) => {
        console.log(error);
        // reject(error);
      }).catch(reason => {
        console.log(reason);
      });
      return promise;
    }
    */

    if (navigator.onLine) {
      const errorOptions = {
        '404': {
          msg: '请求错误，未找到该资源'
        },
        '500': {
          msg: '服务器错误',
          cb: () => {
            router.replace({ path: '/error/500' });
          }
        }
      };
      if (error && error.response) {
        const { msg, cb } = errorOptions[error.response];
        msg &&
          Message({
            message: msg,
            type: 'error'
          });
        cb && cb();
      } else {
        /*
        Message({
          message: error.message,
          type: 'error'
        });
        */
        //return Promise.reject(error);
        if (error.__CANCEL__) {
          return error;
        }
      }
    } else {
      Message({
        message: '网络异常，请检查网络',
        type: 'error'
      });
      router.replace({ path: '/error/NoNetwork' });
    }
    return error;
  }
);

export default service;
