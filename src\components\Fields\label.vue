<template>
  <div v-if="data.length>wordsLen">
    <el-popover :content="data"
                placement="bottom"
                popper-class="popClass"
                trigger="hover">
      <label slot="reference"
             class="item-label">{{ data }}</label>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'Label',
  props: {
    data: {
      type: String,
      default: function () {
        return '';
      }
    },
    wordsLen: {
      type: Number,
      default: function () {
        return 5;
      }
    }
  }
};
</script>

<style lang="scss">
.popClass {
  padding: 8px 8px !important;
}
</style>
