<template>
  <div>
    <el-form-item
      :label="itemData.fieldLable"
      :prop="itemData.fieldKey?itemData.fieldKey:itemData.fieldName"
    >
      <div v-if="itemData.fieldType==0">
        <el-input
          v-model.trim="$attrs.form[item.fieldKey?item.fieldKey:item.fieldName]"
          :placeholder="itemData.placeholder"
        />
      </div>
      <div v-else-if="itemData.fieldType==1">
        <el-input
          v-model.trim="$attrs.form[item.fieldKey?item.fieldKey:item.fieldName]"
          :placeholder="itemData.placeholder"
          type="textarea"
        />
      </div>
      <div v-else-if="itemData.fieldType==2">
        <el-select
          v-model="$attrs.form[item.fieldKey?item.fieldKey:item.fieldName]"
          :placeholder="itemData.placeholder"
        >
          <el-option
            v-for="(sItem,index) in itemData.optionSettings"
            :key="index"
            :value="sItem"
            :label="sItem"
          />
        </el-select>
      </div>

      <div v-else-if="itemData.fieldType==3">
        <el-radio-group
          v-model="$attrs.form[item.fieldKey?item.fieldKey:item.fieldName]"
          :placeholder="itemData.placeholder"
        >
          <el-radio
            v-for="(sItem,index) in itemData.optionSettings"
            :key="index"
            :label="sItem"
          >{{ sItem }}</el-radio>
        </el-radio-group>
      </div>
      <div v-else-if="itemData.fieldType==4">
        <el-checkbox-group
          v-if="$attrs.form[item.fieldKey?item.fieldKey:item.fieldName]"
          v-model="$attrs.form[item.fieldKey?item.fieldKey:item.fieldName]"
        >
          <el-checkbox
            v-for="(sItem,index) in itemData.optionSettings"
            :name="itemData.fieldKey?itemData.fieldKey:itemData.fieldName"
            :label="sItem"
            :key="index"
          />
        </el-checkbox-group>
      </div>
    </el-form-item>
  </div>
</template>

<script>
// 0:单行输入,1:多行输入,2:下拉,3:单选,4:多选--
export default {
  name: 'DynimicForm',
  props: {
    item: {
      type: Object,
      default: function() {
        return {};
      }
    }
  },
  data: function() {
    return {
      itemData: {}
    };
  },
  created() {
    this.itemData = JSON.parse(JSON.stringify(this.item));
    // console.log(this.itemData);
  }
};
</script>

<style scoped>
</style>
