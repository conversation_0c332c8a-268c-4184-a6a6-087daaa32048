<template>
  <xyy-list-page>
    <template slot="header">
      <xyy-button icon-class="btn-add"
                  class-name="btn-add-icon"
                  @click="toEdit">新建模板</xyy-button>
    </template>
    <template slot="body">
      <description :info="info"
                   class="help-msg-box"></description>
      <xyy-table :data="list"
                 :list-query="listQuery"
                 :col="col"
                 :operation="operation"
                 @get-data="getList"
                 @operation-click="operationClick">
        <template slot="gmtModified"
                  slot-scope="{col}">
          <el-table-column :key="col.index"
                           :prop="col.index"
                           :label="col.name"
                           :width="col.width"
                           :formatter="getFormatDate" />
        </template>
        <template slot="tpStatus"
                  slot-scope="{col}">
          <el-table-column :key="col.index"
                           :label="col.name"
                           :width="col.width">
            <template slot-scope="{row}">
              <span :class="'status-tip '+(row.tpStatus === 1?'open':'close')"></span>
              {{ row.tpStatus === 1?'启用':'禁用' }}
            </template>
          </el-table-column>
        </template>
      </xyy-table>
    </template>
  </xyy-list-page>
</template>

<script>
import {
  getSatisfactionList,
  changeTemplateStatus,
  delTemplateData
} from '@/api/satisfaction';
import description from '@/components/tools/description.vue';
/* 查询关联 */
// import { listTemplate } from '../../api/fields/fields-comment';

export default {
  name: 'SatisfactionList',
  components: {
    description
  },
  data () {
    return {
      info: [
        { title: '未收到短信的情况', info: '' },
        { title: '', info: '1.手机号不存在' },
        { title: '', info: '2.手机号已停机' },
        { title: '', info: '3.手机无信号' },
        { title: '', info: '4.第三方短信接口故障（较小几率）' }
      ],
      list: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      col: [
        {
          index: 'tpName',
          name: '评价模板名称',
          width: 300,
          ellipsis: true,
          resizable: true
        },
        {
          index: 'tpDescribe',
          name: '说明',
          width: 350,
          ellipsis: true,
          resizable: true
        },
        { index: 'gmtModified', name: '更新时间', slot: true, resizable: true },
        { index: 'editorName', name: '更新人', width: 130, resizable: true },
        {
          index: 'tpStatus',
          name: '状态',
          width: 90,
          slot: true,
          resizable: true
        },
        {
          index: 'operation',
          name: '操作',
          width: 220,
          operation: true,
          resizable: true
        }
      ],
      operation: [
        {
          name: '启用',
          type: 0,
          format: function (row) {
            return ['启用', '禁用'][row.tpStatus];
          }
        },
        {
          name: '编辑',
          type: 1
        },
        {
          name: '删除',
          type: 2,
          disabled: function (row) {
            return !!row.tpStatus;
          }
        }
      ]
    };
  },
  activated () {
    this.getList(this.listQuery);
  },
  methods: {
    /**
     * 跳转到模板编辑页
     */
    toEdit (data) {
      if (data) {
        this.$router.replace({
          path: `/worksheet/satisfactionEdit/${data.id}`,
          query: { id: data.id, tpCode: data.tpCode }
        });
      } else {
        this.$router.replace({
          path: `/worksheet/satisfactionEdit/${new Date().getTime()}`
        });
      }
    },
    getList: function (listQuery) {
      const { page, pageSize } = listQuery;
      getSatisfactionList({ pageNum: page, pageSize })
        .then(res => {
          if (res.code === 1) {
            const { list, total } = res.data;
            this.list = list;
            this.listQuery = {
              ...this.listQuery,
              total
            };
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => { });
    },
    operationClick: function (type, row) {
      switch (type) {
        case 0:
          this.$XyyMsg({
            title: '提示',
            content: `是否${row.tpStatus === 0 ? '启用' : '禁用'}?`,
            onSuccess: () => {
              this.changeStatus(row);
            }
          });
          break;
        case 1:
          this.toEdit(row);
          break;
        case 2:
          this.$XyyMsg({
            title: '提示',
            content: '确定要删除模板吗?',
            onSuccess: () => {
              this.delData(row);
            }
          });

          break;
      }
    },
    /**
     * 更改模板状态
     */
    changeStatus (data) {
      changeTemplateStatus({
        id: data.id,
        status: data.tpStatus === 0 ? 1 : 0
      }).then(res => {
        if (res.code === 1) {
          const msg = data.tpStatus === 0 ? '启用' : '禁用';
          this.$XyyMessage.success(`模板已${msg}`);
          // 刷新列表
          this.getList(this.listQuery);
        } else {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: res.msg, // html代码串
            onSuccess: () => { }
          });
        }
      });
    },
    /**
     * 删除模板数据
     */
    delData (data) {
      delTemplateData(data.id).then(res => {
        if (res.code === 1) {
          this.$XyyMessage.success('删除成功');
          // 刷新列表
          this.getList(this.listQuery);
        } else if (res.code === 9) {
          // 查询字段是否被关联
          let html =
            '当前正有表单应用使用此满意度评价模板，请修改评价模板后删除';
          this.$XyyMsg({
            title: '提示',
            content: html,
            closeBtn: false,
            onSuccess: function () { }
          });
        } else {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: res.msg, // html代码串
            onSuccess: () => { }
          });
        }
      });
    },
    getFormatDate: function (row, column, cellValue, index) {
      return new Date(cellValue + 8 * 3600 * 1000)
        .toJSON()
        .substr(0, 19)
        .replace('T', ' ');
    }
  }
};
</script>

<style scoped lang="scss">
/deep/.page-header {
  padding-bottom: 8px !important;
}
.search-box {
  width: 344px;
  float: right;
  .el-input {
    width: calc(100% - 76px);
    /deep/ .el-input__inner {
      height: 36px;
      line-height: 36px;
    }
  }
  button {
    width: 68px;
    height: 36px;
    float: right;
  }
}
.status-tip {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 2px 2px 0;
  &.open {
    background: #67c23a;
  }
  &.close {
    background: #ff3024;
  }
}
.help-msg-box {
  float: right;
  margin-bottom: 5px;
  &::after {
    content: '';
    clear: both;
  }
}
.operation-box {
  width: auto;
  white-space: nowrap;
  letter-spacing: 0;
  margin: 0 -10px;
  .el-button {
    position: relative;
    margin: 0 10px;
  }
  .el-button::before {
    position: absolute;
    top: 14px;
    right: -10px;
    content: '';
    display: block;
    width: 1px;
    height: 12px;
    background: #dcdfe6;
  }
  .el-button:first-child {
    margin-left: 0;
  }
  .el-button:last-child {
    margin-right: 0;
  }
  .el-button:last-child::before {
    display: none;
  }
}
</style>

