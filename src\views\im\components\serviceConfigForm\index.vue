<template>
  <div>
    <div v-if="!pageState" id="service" class="xia-container">
      <header>服务总结</header>
      <el-form
        ref="dynamicForm"
        :rules="rules"
        :model="dynamicForm"
        class="dynamic-form"
        label-width="100px"
      >
        <dynimic-form v-for="(item,index) in fileds" :key="index" :item="item" :form="dynamicForm"></dynimic-form>
        <p>
          <el-button type="primary" @click="submitForm('dynamicForm')">提交</el-button>
        </p>
      </el-form>
    </div>

    <div v-if="pageState" class="tab_clom">
      <div v-for="(item, index) in serviceSumArray" :key="index" class="tab_row">
        <div class="user_title">{{ item.fieldLable }}</div>
        <div class="user_datail">
          <template v-if="item.fieldType === 1">
            <pre
              style="margin-top: 0;margin-bottom: 0;white-space: pre-wrap !important;font-family: inherit !important;"
            >{{ item.labelValue }}</pre>
          </template>
          <template v-else>{{ item.labelValue }}</template>
        </div>
      </div>
      <p>
        <el-button v-show="serviceSumArray.length" type="primary" @click="updateServiceSummary()">修改</el-button>
      </p>
    </div>
  </div>
</template>

<script>
import dynimicForm from '@/components/serviceConfig/dynimicForm';
import {
  getTemplate,
  saveFormDate,
  serviceDetail,
  selectCustomerNameFieldKey,
} from '@/api/im_view/serviceConfigForm';
export default {
  name: 'ServiceConfigForm',
  components: {
    dynimicForm,
  },
  inject: ['compIMViewRight'],
  data() {
    return {
      dynamicForm: {},
      fileds: [],
      rules: {},
      oldContainerID: '0', // 上一个会话id
      currentContainerID: '0', // 当前会话id
      dynamicFormLocal: [],
      serviceSumArray: [], // 服务总结数据
      pageState: false, // 页面状态：false 编辑状态，true：展示状态
      chatInfo: {},
      fieldKeyID: '0',
      customNickName: '0',
      oldCustomNickName: '',
    };
  },
  // computed: {
  //   containerId: function() {
  //     this.oldContainerID = this.currentContainerID;
  //     this.currentContainerID = this.$store.getters.containerid;
  //     return this.currentContainerID;
  //     // return this.$store.getters.containerid;
  //   }
  // },
  // watch: {
  //   containerId: function(newVal, oldVal) {
  //     if (newVal !== oldVal) {
  //       // this.pageState = false;
  //       this.saveFormContent();
  //       this.updateFormContent();
  //     }
  //   }
  // },
  created() {
    this.getSelectCustomerNameFieldKey();
    this.getTemplate();
  },
  mounted() {},
  methods: {
    init(customNickName) {
      this.serviceConfigFormSelect(customNickName);
    },

    //选中会话更新服务小结的会话信息
    serviceConfigFormSelect(chat) {
      this.customNickName = chat;
      this.oldContainerID = this.currentContainerID;
      this.currentContainerID = this.$store.getters.containerid;

      this.saveFormContent();
      this.updateFormContent();
    },
    // 获取字段模板
    getTemplate() {
      getTemplate()
        .then((res) => {
          if (res.code !== 1) {
            this.$XyyMessage.warning(res.msg);
            return;
          }
          this.fileds = res.data;
          this.fileds.forEach((item) => {
            let key = '';
            if (item.fieldKey) {
              if (!isNaN(item.fieldKey)) {
                item.fieldKey = item.fieldKey.toString();
                key = item.fieldKey;
              } else {
                key = item.fieldKey;
              }
            } else {
              key = item.fieldName;
            }
            item.fieldType === 4
              ? this.$set(this.dynamicForm, key, [])
              : this.$set(this.dynamicForm, key, '');
          });
          this.fileds.forEach((item) => {
            if (
              typeof item.optionSettings === 'string' &&
              item.optionSettings
            ) {
              item.optionSettings = JSON.parse(item.optionSettings);
            }
          });
          // 0:单行输入,1:多行输入,2:下拉,3:单选,4:多选
          this.fileds.forEach((item) => {
            if (
              item.optionSettings &&
              typeof item.optionSettings === 'string'
            ) {
              item.optionSettings = JSON.parse(item.optionSettings);
            }
            let arr = [];
            if (item.required) {
              switch (parseInt(item.fieldType)) {
                case 0:
                case 1:
                  arr = [{ required: true, message: '必填', trigger: 'blur' }];
                  break;
                case 2:
                case 3:
                  arr = [
                    { required: true, message: '必填', trigger: 'change' },
                  ];
                  break;
                case 4:
                  arr = [
                    {
                      type: 'array',
                      required: true,
                      message: '必填',
                      trigger: 'change',
                    },
                  ];
                  break;
              }
              this.$set(this.rules, item.fieldKey, arr);
            }
          });
        })
        .finally(() => {
          this.init(this.compIMViewRight.chatInfo.customNickName);
        });
    },
    // 提交保存服务小结
    submitForm(formName) {
      if (!this.$store.getters.containerid || !this.$store.getters.khid) {
        this.$XyyMessage.error('请选择会话');
        return;
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (
            !this.$store.getters.containerid ||
            !this.$store.getters.khid ||
            !this.$store.getters.kfid
          ) {
            this.$XyyMessage.warning('请选择会话');
            return;
          }
          const sendData = {
            imFormData: {
              formId: 1,
              dialogId: this.$store.getters.containerid,
              customerId: this.$store.getters.khid,
              kefuId: this.$store.getters.kfid,
            },
            imFormDataExtend: [],
          };
          const arr = [];
          Object.keys(this[formName]).forEach((item) => {
            arr.push({ fieldKey: item, value: this[formName][item] });
          });

          sendData.imFormDataExtend = arr;
          saveFormDate(JSON.stringify(sendData)).then((res) => {
            if (res.code !== 1) {
              this.$XyyMessage.warning(res.msg);
              return;
            }
            this.emits(
              this,
              'send',
              'im_result',
              this.$store.getters.containerid
            );
            this.$XyyMessage.success('保存成功');
            this.getServiceSummary(); // 保存成功获取最新数据及状态
            this.dynamicForm = {};
            this.$store.commit('deleteDynamicFormData', {
              key: this.currentContainerID,
            });
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 存
    saveFormContent() {
      const _fileds = JSON.parse(JSON.stringify(this.fileds));
      _fileds.forEach((item) => {
        Object.keys(this.dynamicForm).forEach((itemForm) => {
          if (item.fieldKey === itemForm) {
            item.content = this.dynamicForm[itemForm];
          }
        });
      });

      if (this.oldContainerID) {
        this.$store.commit('updataEditDynamicFormData', {
          key: this.oldContainerID,
          value: _fileds,
        });
      }

      _fileds.forEach((item) => {
        let key = '';
        if (item.fieldKey) {
          if (!isNaN(item.fieldKey)) {
            item.fieldKey = item.fieldKey.toString();
            key = item.fieldKey;
          } else {
            key = item.fieldKey;
          }
        } else {
          key = item.fieldName;
        }
        item.fieldType === 4
          ? this.$set(this.dynamicForm, key, [])
          : this.$set(this.dynamicForm, key, '');
      });
    },
    // 取
    updateFormContent() {
      const that = this;
      let contents = '';

      this.dynamicFormLocal = this.$store.getters.editDynamicFormData(
        this.currentContainerID
      );

      // 判断store中有没有数据
      if (this.dynamicFormLocal) {
        // this.fileds = [];
        // this.fileds = this.dynamicFormLocal;
        this.dynamicFormLocal.forEach((item) => {
          let key = '';
          if (item.fieldKey) {
            if (!isNaN(item.fieldKey)) {
              item.fieldKey = item.fieldKey.toString();
              key = item.fieldKey;
            } else {
              key = item.fieldKey;
            }
          } else {
            key = item.fieldName;
          }
          this.$set(this.dynamicForm, key, item.content);

          //判断是否是客户信息服务小结，更新客户名称
          if (key === that.fieldKeyID) {
            if (that.customNickName !== item.content) {
              this.$set(this.dynamicForm, key, that.customNickName);
            }
          } else {
            //item.content 可能存在为null的情况
            contents += item.content && item.content.length ? item.content : '';
          }
        });
        // 如果store中有content不为空，则展示store里的数据
        if (contents) {
          this.pageState = false;
        } else {
          // 否则通过api查询数据
          this.getServiceSummary();
        }
      } else {
        // store没有数据，则通过api查询数据
        this.getServiceSummary();
      }
    },
    // 获取服务总结
    getServiceSummary() {
      const that = this;
      that.serviceSumArray = [];
      return new Promise((resolve, reject) => {
        serviceDetail('', that.currentContainerID).then((res) => {
          that.pageState = false;
          if (res.data === null) {
            // that.$XyyMessage.error(res.msg);
            const _fileds = JSON.parse(JSON.stringify(that.fileds));
            _fileds.forEach((item) => {
              let key = '';
              if (item.fieldKey) {
                if (!isNaN(item.fieldKey)) {
                  item.fieldKey = item.fieldKey.toString();
                  key = item.fieldKey;
                } else {
                  key = item.fieldKey;
                }
              } else {
                key = item.fieldName;
              }
              item.fieldType === 4
                ? this.$set(this.dynamicForm, key, [])
                : this.$set(this.dynamicForm, key, '');
              if (key === this.fieldKeyID) {
                // that.$set(this.dynamicForm, key, that.chatInfo.customNickName);
                that.$set(this.dynamicForm, key, that.customNickName);
              }
            });
            return;
          }
          that.serviceSumArray = res.data.imFormDataExtend;
          that.pageState = that.serviceSumArray.length;
          resolve(that.serviceSumArray);
        });
      });
    },
    // 切换到修改状态
    updateServiceSummary() {
      // 切换到编辑状态时获取新数据
      this.getServiceSummary().then((val) => {
        this.pageState = false;
        this.serviceSumArray.forEach((item) => {
          this.$set(
            this.dynamicForm,
            item.fieldKey,
            item.fieldType === 4
              ? item.labelValue
                ? item.labelValue.split(',')
                : []
              : item.labelValue
          );
        });
      });
    },
    getSelectCustomerNameFieldKey() {
      // 获取客户名称key
      selectCustomerNameFieldKey().then((res) => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.fieldKeyID = res.data;
      });
    },
  },
};
</script>

<style lang="scss"  scoped>
.xia-container {
  margin-bottom: 30px;
}
.xia-container header {
  padding: 15px 20px 20px 20px;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  color: rgba(48, 49, 51, 1);
  line-height: 22px;
}
.xia-container form {
  /*padding-right: 48px;*/
}
el-input {
  height: 36px;
  /* line-height:36px; */
}
.el-input input {
  height: 100% !important;
}
.el-radio__input.is-checked .el-radio__inner {
  border-color: #3b95a8 !important;
  background: #3b95a8 !important;
}

.tab_clom {
  display: flex;
  flex-direction: column;
}

.tab_row {
  display: flex;
  flex-direction: row;
  min-height: 30px;
}

.user_title {
  font-size: 14px;
  color: rgb(48, 49, 51);
}

.user_datail {
  margin-left: 10px;
  font-size: 14px;
  color: #575766;
}

/deep/.el-form-item__label {
  font-weight: normal;
}
/deep/.el-button--text {
  border: none !important;
}
/deep/.el-button {
  padding-left: 15px;
  padding-right: 15px;
  font-size: 14px;
  color: rgba(87, 87, 102, 1);
  height: 36px;
  border-radius: 2px;
  border: 1px solid rgba(228, 228, 235, 1);
}
/deep/.el-button:focus,
.el-button:hover {
  font-size: 14px;
  color: rgba(87, 87, 102, 1);
  height: 36px;
  border-radius: 2px;
  border: 1px solid rgba(228, 228, 235, 1);
}
/deep/.el-button--primary {
  color: #fff;
  height: 36px;
  background: rgba(59, 149, 168, 1);
  border-radius: 2px;
}
/deep/.el-button--primary:focus,
.el-button--primary:hover {
  height: 36px;
  background: rgba(59, 149, 168, 1);
  border-radius: 2px;
  color: #fff;
}
/deep/.el-radio__input.is-checked + .el-radio__label {
  color: #3b95a8;
}
/deep/.el-radio-group,
.el-checkbox-group {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -moz-justify-content: flex-start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -moz-align-items: center;
  -webkit-align-items: center;
  align-items: center;
}
/deep/.el-radio,
.el-checkbox {
  height: 36px;
  line-height: 36px;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -moz-justify-content: flex-start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -moz-align-items: center;
  -webkit-align-items: center;
  align-items: center;
}
/deep/.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: rgba(59, 149, 168, 1);
  border-color: rgba(59, 149, 168, 1);
}
/deep/.el-checkbox__inner:hover {
  border-color: rgba(59, 149, 168, 1);
}
/deep/.el-checkbox__input.is-checked + .el-checkbox__label {
  color: rgba(59, 149, 168, 1);
}
/deep/.el-radio__inner:hover {
  border-color: rgba(59, 149, 168, 1);
}
/deep/.el-form-item {
  margin-bottom: 16px;
}
/deep/.el-form-item__content {
  line-height: 30px;
}
/deep/.el-form-item__error {
  padding-top: 0;
}

// 展示状态样式
.user_datail {
  margin-left: 10px;
  font-size: 14px;
  color: #575766;
}
.user_title {
  font-size: 14px;
  color: rgb(48, 49, 51);
}
</style>
