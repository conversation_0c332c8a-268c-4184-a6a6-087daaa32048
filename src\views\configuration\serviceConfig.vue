<template>
  <div class="total-container">
    <div class="box row-start column-bottom title">
      <header>服务总结配置</header>
      <em>注：服务总结模板配置，支持默认字段内容编辑（表头及类型不可更改）、自定义字段的设置，方便坐席服务过程中记录会话相关信息</em>
    </div>
    <div id="template-container">
      <div class="box row-start template-container">
        <div class="left-container">
          <header class="box row-start column-center">
            <h2>字段类型</h2>
            <el-tooltip content="通过拖拽的形式将自定义字段放置模板样式中，选中字段后可在右侧配置字段属性"
                        placement="top"
                        effect="light">
              <svg-icon class="question-draggle"
                        icon-class="question_draggle"></svg-icon>
            </el-tooltip>
          </header>
          <draggable :list="fields"
                     :sort="false"
                     :group="{ name: 'template', pull: 'clone', put: false }"
                     :clone="cloneItem"
                     class="fields-body"
                     @start="handleDraggle">
            <div v-for="item in fields"
                 :key="item.fieldType"
                 :class="{'fields-item-active':currentType==item.fieldType}"
                 class="box row-start column-center fields-item"
                 @click="itemClick(item.fieldType)">
              <svg-icon :icon-class="getSvgHref(item.fieldType)"
                        class="svg-icon"></svg-icon>
              <span>{{ item.fieldName }}</span>
            </div>
          </draggable>
        </div>
        <div class="box row-start right-container">
          <div class="center">
            <header>模板样式</header>
            <draggable :list="datas"
                       class="editor-body"
                       group="template">
              <div v-for="(item,index) in datas"
                   :key="index"
                   :class="currentId==item.id?'active':''"
                   class="fields-item"
                   @click="targetItemClick(item)">
                <div class="box row-between column-center content-container">
                  <div class="field-content">
                    <dynimic-template :keys="item.fieldType"
                                      :preview="item"
                                      refs="field"></dynimic-template>
                  </div>
                  <i v-if="!item.fieldLocked&&currentId==item.id"
                     class="el-icon-close"
                     @click="removeAt(index)"></i>
                </div>
              </div>
            </draggable>
          </div>
          <div class="right">
            <header>字段属性</header>
            <div>
              <el-form ref="form"
                       :model="form"
                       label-width="90px"
                       class="single-attr-form">
                <div>
                  <el-form-item label="字段类型:">
                    <p v-model="form.fieldName"
                       style="line-height:40px;margin:0;">{{ form.fieldType|typeToName }}</p>
                  </el-form-item>
                  <el-form-item label="标题名称:"
                                prop="fieldLable">
                    <el-input v-model="form.fieldLable"
                              :disabled="form.fieldLocked==1?true:false"></el-input>
                  </el-form-item>
                  <el-form-item label="提示语:"
                                prop="placeholder">
                    <el-input v-model="form.placeholder"></el-input>
                  </el-form-item>
                  <el-form-item v-if="form.fieldType==2||form.fieldType==3||form.fieldType==4"
                                label="选项内容:">
                    <div class="form-content">
                      <el-button v-for="(item,index) in form.optionSettings"
                                 :key="index"
                                 type="primary"
                                 class="add-content-text">
                        <span class="box row-around column-center">
                          {{ item }}
                          <i class="el-icon-close close-button"
                             @click="closeContent(item)"></i>
                        </span>
                      </el-button>
                      <p>
                        <el-input v-if="isAdd"
                                  ref="addContentText"
                                  v-model.trim="addContentText"
                                  class="add-content-text"
                                  @keyup.enter.native="addContent"></el-input>
                      </p>
                      <p>
                        <el-button type="text"
                                   @click="isShowInput">
                          <span class="box row-around column-center"
                                style="color:rgba(59,149,168,1);">
                            <svg-icon class="svg-icon-add"
                                      icon-class="add"
                                      @click="isShowInput"></svg-icon>添加
                          </span>
                        </el-button>
                        <el-button type="text"
                                   @click="isShowTextarea">
                          <span class="box row-around column-center"
                                style="color:rgba(59,149,168,1);">
                            <svg-icon class="svg-icon-add"
                                      icon-class="batch_add"
                                      @click="isShowTextarea"></svg-icon>批量添加
                          </span>
                        </el-button>
                      </p>
                      <el-dialog :visible.sync="dialogVisible"
                                 width="30%">
                        <div slot="title"
                             class="box row-start column-center dialog-header">
                          <header>批量添加</header>
                          <span>请输入选项值，每行一个</span>
                        </div>
                        <el-input :rows="2"
                                  v-model="batchTextarea"
                                  type="textarea"
                                  placeholder="请输入内容"
                                  maxlength="3000"
                                  class="batch-textarea"></el-input>
                        <p style="font-size:12px;">说明：一行为一个选项内容，回车换行即为增加选项，不支持空格和“、”等区分方式</p>
                        <span slot="footer"
                              class="dialog-footer">
                          <el-button @click="dialogVisible = false">取 消</el-button>
                          <el-button type="primary"
                                     @click="batchAddContent">确 定</el-button>
                        </span>
                      </el-dialog>
                    </div>
                  </el-form-item>
                  <el-form-item label="是否必填:">
                    <el-radio-group v-model="form.required">
                      <el-radio label="1">是</el-radio>
                      <el-radio label="0">否</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="可搜索:">
                    <el-radio-group v-model="form.bequeried">
                      <el-radio label="1">是</el-radio>
                      <el-radio label="0">否</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="列表展示:">
                    <el-radio-group v-model="form.showInlist">
                      <el-radio label="1">是</el-radio>
                      <el-radio label="0">否</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </div>
        </div>
      </div>
      <p style="text-align: left;margin-left: 985px;margin-top:10px;">
        <el-button type
                   @click="reset('form')">重置</el-button>
        <el-button type="primary"
                   @click="save">保存</el-button>
      </p>
    </div>
  </div>
</template>

<script>
import dynimicTemplate from '@/components/serviceConfig/dynimicTemplate';
// 0:单行输入,1:多行输入,2:下拉,3:单选,4:多选
import vuedraggable from 'vuedraggable';
import { getTemplate, saveTemplate } from '@/api/configuration/seriveConfig.js';

let idGlobal = 9;
export default {
  name: 'compServiceConfig',
  components: {
    draggable: vuedraggable,
    dynimicTemplate,
  },
  data: function () {
    return {
      fields: [
        {
          id: 1,
          formId: 1,
          fieldName: '单选',
          fieldType: '3',
          fieldLable: '单选',
          placeholder: '',
          defaultValue: '',
          optionsValue: '',
          optionSettings: [],
          fieldLocked: 0,
          required: '0',
          bequeried: '0',
          showInlist: '0',
        },
        {
          id: 2,
          formId: 1,
          fieldName: '多选',
          fieldType: '4',
          fieldLable: '多选',
          placeholder: '',
          defaultValue: '',
          optionsValue: '',
          optionSettings: [],
          fieldLocked: 0,
          required: '0',
          bequeried: '0',
          showInlist: '0',
        },
        {
          id: 3,
          formId: 1,
          fieldName: '单行文本',
          fieldType: '0',
          fieldLable: '单行文本',
          placeholder: '',
          defaultValue: '',
          optionsValue: '',
          optionSettings: [],
          fieldLocked: 0,
          required: '0',
          bequeried: '0',
          showInlist: '0',
        },
        {
          id: 4,
          formId: 1,
          fieldName: '多行文本',
          fieldType: '1',
          fieldLable: '多行文本',
          placeholder: '',
          defaultValue: '',
          optionsValue: '',
          optionSettings: [],
          fieldLocked: 0,
          required: '0',
          bequeried: '0',
          showInlist: '0',
        },
        {
          id: 5,
          formId: 1,
          fieldName: '下拉框',
          fieldType: '2',
          fieldLable: '下拉框',
          placeholder: '',
          defaultValue: '',
          optionsValue: '',
          optionSettings: [],
          fieldLocked: 0,
          required: '0',
          bequeried: '0',
          showInlist: '0',
        },
      ],
      datas: [
        {
          id: 6,
          formId: 1,
          fieldName: '单行文本',
          fieldType: '0',
          fieldLable: '客户名称',
          placeholder: '',
          defaultValue: '',
          optionsValue: '',
          optionSettings: [],
          fieldLocked: 0,
          required: '0',
          bequeried: '0',
          showInlist: '0',
        },
        {
          id: 7,
          formId: 1,
          fieldName: '单选',
          fieldType: '3',
          fieldLable: '客户地区',
          placeholder: '',
          defaultValue: '',
          optionsValue: '',
          optionSettings: ['湖北', '安徽', '吉宁', '辽宁'],
          fieldLocked: 0,
          required: '0',
          bequeried: '0',
          showInlist: '0',
        },
        {
          id: 8,
          formId: 1,
          fieldName: '单选',
          fieldType: '3',
          fieldLable: '身份类型',
          placeholder: '',
          defaultValue: '',
          optionsValue: '',
          optionSettings: ['客户', '业务员', '司机', '其他'],
          fieldLocked: 0,
          required: '0',
          bequeried: '0',
          showInlist: '0',
        },
        {
          id: 9,
          formId: 1,
          fieldName: '多行文本',
          fieldType: '1',
          fieldLable: '备注',
          placeholder: '',
          defaultValue: '',
          optionsValue: '',
          optionSettings: [],
          fieldLocked: 0,
          required: '0',
          bequeried: '0',
          showInlist: '0',
        },
      ],
      currentType: '0',
      currentId: '0',
      form: {},
      dialogVisible: false,
      batchTextarea: '',
      isAdd: false,
      addContentText: '',
      previewModal: {},
      maxLen: 20,
    };
  },
  watch: {
    form: function () {
      const idArr = this.datas.map((item) => {
        return item.id;
      });
      const index = idArr.indexOf(this.form.id);
      if (index >= 0) {
        this.datas.splice(index, 1, this.form);
      }
    },
  },
  mounted () {
    this.getTemplate();
  },
  methods: {
    isShowTextarea () {
      this.batchTextarea = '';
      this.dialogVisible = true;
    },
    isShowInput () {
      this.addContentText = '';
      this.isAdd = this.isAdd != true;
    },
    getTemplate () {
      getTemplate({ fromId: 1 })
        .then((res) => {
          if (res.code !== 1) {
            this.$XyyMessage.warning(res.msg);
            return;
          }
          if (this.datas.length > 0) {
            // 清空datas,获取最大id值
            this.datas.splice(0, this.datas.length);
            this.datas = res.data;
            const ids = this.datas.map((item) => {
              if (
                typeof item.optionSettings === 'string' &&
                item.optionSettings
              )
                item.optionSettings = JSON.parse(item.optionSettings);
              return parseInt(item.id);
            });
            ids.sort(function (a, b) {
              return a - b;
            });
            idGlobal = ids[ids.length - 1] || idGlobal; // 解决首次增加模板样式 ids[ids.length - 1] 为undefined 的问题，后续需优化 idGlobal 最大前后端同步
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    getSvgHref: function (value) {
      if (!value) return '';
      let str = '';
      switch (value) {
        case '0':
          str += 'input';
          break;
        case '1':
          str += 'textarea';
          break;
        case '2':
          str += 'select';
          break;
        case '3':
          str += 'radio';
          break;
        case '4':
          str += 'checkbox';
          break;
        default:
          str += 'radio';
          break;
      }
      return str;
    },
    removeAt (idx) {
      this.datas.splice(idx, 1);
    },
    itemClick (type) {
      this.currentType = type;
    },
    targetItemClick (item) {
      this.currentId = item.id;
      this.form = JSON.parse(JSON.stringify(item));
      if (typeof this.form.required === 'number')
        this.form.required = this.form.required.toString();
      if (typeof this.form.bequeried === 'number')
        this.form.bequeried = this.form.bequeried.toString();
      if (typeof this.form.showInlist === 'number')
        this.form.showInlist = this.form.showInlist.toString();
    },
    cloneItem (item) {
      const json = { ...item };
      json.id = ++idGlobal;
      return json;
    },
    handleDraggle () {
      this.currentType = this.fields[arguments[0].oldIndex].fieldType;
    },
    closeContent (item) {
      const index = this.form.optionSettings.indexOf(item);
      this.form.optionSettings.splice(index, 1);
    },
    addContent () {
      if (this.form.optionSettings.indexOf(this.addContentText) >= 0) {
        this.$XyyMessage.warning('请不要输入重复的值');
        return false;
      }
      if (this.addContentText.length > 0) {
        if (this.addContentText.length > this.maxLen) {
          this.addContentText = this.addContentText.substring(0, this.maxLen);
        }
        this.form.optionSettings.push(this.addContentText);
      }
    },
    batchAddContent () {
      if (!this.batchTextarea) {
        this.$XyyMessage.warning('请输入文字');
        return;
      }
      const batchArr = this.batchTextarea.split(/[(\r\n)\r\n]+/);
      const arr = [...this.form.optionSettings];
      batchArr.forEach((item) => {
        let str = item.replace(/^\s*|\s*$/g, '');
        if (str.length > 0) {
          if (str.length > this.maxLen) {
            str = str.substring(0, this.maxLen);
          }
          const index = arr.indexOf(str);
          if (index < 0) arr.push(str);
        }
      });
      if (arr.length == 0) {
        this.$XyyMessage.warning('重复添加');
        return;
      }
      this.form.optionSettings.splice(0, this.form.optionSettings.length);
      this.form.optionSettings.push(...arr);
      this.dialogVisible = false;
    },
    reset (formName) {
      this.$confirm('此操作将删除配置项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$refs[formName].resetFields();
          this[formName].optionSettings.splice(
            0,
            this[formName].optionSettings.length
          );
          this.getTemplate();
        })
        .catch((err) => {
          this.$XyyMessage({
            type: 'info',
            message: '已取消删除',
          });
        });
    },
    save () {
      // 0:单行输入,1:多行输入,2:下拉,3:单选,4:多选
      const rules = [];
      this.datas.forEach((item, index) => {
        const arr = ['第' + (index + 1) + '行'];
        if (item.fieldLable.length > this.maxLen) {
          arr.push('标题名称大于' + this.maxLen + '个字符串');
        }
        if (!item.fieldLable) {
          arr.push('标题名称必填');
        }
        if (item.placeholder.length > this.maxLen) {
          arr.push('提示语' + this.maxLen + '个字符串');
        }
        if (
          (item.fieldType == 2 || item.fieldType == 3 || item.fieldType == 4) &&
          (!item.optionSettings || item.optionSettings.length == 0)
        ) {
          arr.push('选项内容缺少配置选项');
        }
        if (arr.length > 1) {
          rules.push(arr.join('-'));
        }
      });
      console.log(rules);

      if (rules.length > 0) {
        this.$XyyMessage.warning(rules.join(','));
        return;
      }
      saveTemplate(JSON.stringify({ param: this.datas })).then((res) => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.$XyyMessage.success('保存成功');
      });
    },
  },
};
</script>

<style>
.batch-textarea .el-textarea__inner {
  border: none;
  height: 100%;
}
.el-radio-group {
  line-height: unset;
}
</style>
<style scoped>
@import '../../styles/service.css';
.total-container {
  padding-top: 16px;
  padding-left: 18px;
}
.question-draggle {
  width: 16px;
  height: 16px;
}
</style>
