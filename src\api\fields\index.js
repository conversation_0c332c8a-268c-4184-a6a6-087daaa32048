import request from '@/utils/request';
import { formData } from '@/utils/index';

/* 自定义列表 */
export function getCustomFieldsList(pageNum, pageSize, fieldName) {
  return request({
    url: '/field/listCustomField',
    method: 'get',
    params: {
      pageNum,
      pageSize,
      fieldName
    }
  });
}

/**
 * 批量启用
 * @param {字段id数组} ids
 */
export function enableFieldDatas(ids) {
  return request({
    url: '/field/batchEnbleField',
    method: 'patch',
    data: ids
  });
}
/**
 * 批量禁用
 * @param {字段id数组} ids
 */
export function disableFieldDatas(ids) {
  return request({
    url: '/field/batchProhibitField',
    method: 'patch',
    data: ids
  });
}

/* 系统字段列表 */
export function listSystemField(pageNum, pageSize) {
  return request({
    url: '/field/listSystemField',
    method: 'get',
    params: {
      pageNum,
      pageSize
    }
  });
}
/* 字段复制*/
export function saveCopy(params) {
  return request({
    url: '/field/saveCopy',
    method: 'post',
    data: formData(params)
  });
}
/* 删除字段*/
export function deleteFieldById(params) {
  return request({
    url: '/field/deleteFieldById',
    method: 'delete',
    data: formData(params)
  });
}
/* 启用字段*/
export function enableSystemField(params) {
  return request({
    url: '/field/enableField',
    method: 'patch',
    data: formData(params)
  });
}
/* 禁用字段*/
export function prohibitSystemField(params) {
  return request({
    url: '/field/prohibitField',
    method: 'patch',
    data: formData(params)
  });
}
/* 查询关联字段*/
export function listTemplateByFieldId(params) {
  return request({
    url: '/field/listTemplateByFieldId',
    method: 'get',
    params
  });
}

/* 查询字段信息*/
export function getFieldByFieldId(params) {
  return request({
    url: '/field/getFieldByFieldId',
    method: 'get',
    params
  });
}

/* 新增自定义字段保存 */
export function saveCustomField(params) {
  return request({
    url: '/field/saveCustomField',
    method: 'post',
    data: formData(params)
  });
}
/* 新增系统自定义字段保存 */
export function saveSystemField(params) {
  return request({
    url: '/field/saveSystemField',
    method: 'post',
    data: formData(params)
  });
}
/* 更新系统字段 */
export function updateSystemField(params) {
  return request({
    url: '/field/updateSystemField',
    method: 'patch',
    data: formData(params)
  });
}
/* 更新自定义字段 */
export function updateCustomerField(params) {
  return request({
    url: '/field/updateCustomerField',
    method: 'patch',
    data: formData(params)
  });
}
/* 系统排序 */
export function updateSystemFieldSort(params) {
  console.log(params);
  return request({
    url: '/field/updateSystemFieldSort',
    method: 'patch',
    data: params
  });
}
// 获取省市区信息
export function getProvinceCityArea(params) {
  return request({
    url: '/area/getProvinceCityArea',
    method: 'get',
    params
  });
}
// 上传文件
export function uploadFile(params) {
  return request({
    header: { 'Content-Type': 'multipart/form-data' },
    url: '/fileUpload/uploadFile',
    method: 'post',
    data: params
  });
}
// 上传图片
export function uploadJpg(params) {
  return request({
    header: { 'Content-Type': 'multipart/form-data' },
    url: '/fileUpload/upload',
    method: 'post',
    data: params
  });
}

/**
 * 校验选项
 */
export function validateOptions(params) {
  return request({
    url: '/field/fieldVerification',
    method: 'get',
    params
  });
}

/**
 * 校验字段
 */
export function validateField(params) {
  return request({
    url: '/field/checkIsConditionField',
    method: 'get',
    params
  });
}

export const FILE_PATH = process.env.NODE_ENV === 'production' ? 'http://upload.ybm100.com/' : 'http://upload.test.ybm100.com/';
// export const FILE_PATH = process.env.NODE_ENV === 'production' ? 'http://upload.test.ybm100.com/' : 'http://upload.test.ybm100.com/';

// export const TEMPLATE_PATH = process.env.NODE_ENV === 'production' ? 'http://upload.ybm100.com/G2/M00/10/C2/CiFJIl3qIzyAZw2hAAAhdyDEkE803.xlsx' : 'http://upload.test.ybm100.com/G1/M00/0C/A0/Cgoz1F3bdcKALTcKAAAhdyDEkE892.xlsx';

export const TEMPLATE_PATH = process.env.NODE_ENV === 'production' ? 'M00/10/C2/CiFJIl3qIzyAZw2hAAAhdyDEkE803.xlsx' : 'M00/0C/A0/Cgoz1F3bdcKALTcKAAAhdyDEkE892.xlsx';
// export const TEMPLATE_PATH = process.env.NODE_ENV === 'production' ? 'M00/0C/A0/Cgoz1F3bdcKALTcKAAAhdyDEkE892.xlsx' : 'M00/0C/A0/Cgoz1F3bdcKALTcKAAAhdyDEkE892.xlsx';
export const TEMPLATE_GROUP = process.env.NODE_ENV === 'production' ? 'G2' : 'G1';
// export const TEMPLATE_GROUP = process.env.NODE_ENV === 'production' ? 'G1' : 'G1';
/**
 * gif bmp jpg jpeg png pdf zip rar mp4 avi
 * 获取文件mime-type
 * @param {后缀} suffix
 */
export function getMimeType(suffix) {
  let _type = '';
  switch (suffix) {
    case 'gif': _type = 'image/gif'; break;
    case 'bmp': _type = 'image/bmp'; break;
    case 'jpg': _type = 'image/jpeg'; break;
    case 'jpeg': _type = 'image/jpeg'; break;
    case 'png': _type = 'image/png'; break;
    case 'pdf': _type = 'application/pdf'; break;
    case 'zip': _type = 'application/zip'; break;
    case 'rar': _type = 'application/x-rar-compressed'; break;
    case 'txt': _type = 'text/plain'; break;
    case 'doc': _type = 'application/msword'; break;
    case 'docx': _type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'; break;
    case 'ppt': _type = 'application/vnd.ms-powerpoint'; break;
    case 'pptx': _type = 'application/vnd.openxmlformats-officedocument.presentationml.presentation'; break;
    case 'xls': _type = 'application/vnd.ms-excel'; break;
    case 'xlsx': _type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'; break;
    case 'mp4': _type = 'video/mp4'; break;
    case 'avi': _type = 'video/avi'; break;
  }
  return _type;
}

export const MIME_TYPES = ['image/gif', 'image/bmp', 'image/jpeg', 'image/png', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'application/pdf', 'application/zip', 'application/x-zip-compressed', 'application/x-rar-compressed', 'video/mp4', 'video/avi'];
