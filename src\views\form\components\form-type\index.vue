<template>
  <el-form
    :model="preview"
    :ref="preview.id"
    :key="preview.id"
    class="preview-condition"
    label-width="100px"
  >
    <!--单行输入-->
    <template v-if="[0,1].includes(keys)">
      <el-form-item :key="preview.id" class="single" label-width="0px">
        <el-col>
          <el-input
            :placeholder="preview.tips"
            v-model="preview.defaultValue"
            :maxlength="preview.limitNumberCharacters"
            :disabled="read"
            @change="change"
          />
        </el-col>
      </el-form-item>
    </template>
    <!-- 下拉列表 -->
    <template v-if="[2,3,4].includes(keys)">
      <el-form-item :key="preview.id" class="single" label-width="0px">
        <el-col>
          <el-select
            ref="mySelect"
            v-model="preview.optionSettings[optionsName].optionsValue"
            :placeholder="preview.tips"
            :multiple="symbol===3"
            :disabled="read"
            @change="change"
          >
            <el-option
              v-for="(item, index) in preview.optionSettings[optionsName].optionsArray"
              :key="index"
              :label="item.optionsDesc"
              :value="item.val?item.val:index"
            />
          </el-select>
        </el-col>
      </el-form-item>
    </template>
    <!-- 级联 -->
    <template v-if="keys === 6">
      <el-form-item :key="preview.id" class="single" label-width="0px">
        <el-col class="date-box">
          <el-cascader
            ref="myCascader"
            v-model="preview.optionSettings['treeOptions'].optionsValue"
            :placeholder="preview.tips"
            :options="preview.optionSettings['treeOptions'].optionsArray"
            :disabled="read"
            @change="handleCascader"
            @expand-change="handleExpandCascader"
          >
            <template slot-scope="{ node, data }">
              <span>{{ data.label }}</span>
            </template>
          </el-cascader>
        </el-col>
      </el-form-item>
    </template>
  </el-form>
</template>

<script>
export default {
  name: 'Preview',
  model: {
    prop: 'preview',
    event: 'input'
  },
  props: {
    model: {
      type: Object,
      default: function() {
        return {};
      }
    },
    keys: {
      // 字段类型
      type: Number,
      default: function() {
        return -1;
      }
    },
    read: {
      // 是否是只读状态
      type: Boolean,
      default: function() {
        return false;
      }
    },
    symbol: {
      // 判断方式
      type: Number,
      default: function() {
        return -1;
      }
    },
    preview: {
      // 预览的参数 输入时v-model的参数
      type: Object,
      default: function() {
        return {};
      }
    }
  },
  data() {
    return {
      optionsName: 'selectOptions',
      valueFirst: '',
      inputMap: {
        2: 'selectOptions',
        3: 'radioOptions',
        4: 'checkedOptions',
        6: 'treeOptions'
      }
    };
  },
  watch: {
    keys(n) {
      if ([2, 3, 4, 6].includes(n)) this.optionsName = this.inputMap[n];
    },
    symbol(val) {
      this.resetValue(this.keys);
    }
  },
  created() {
    let value = this.preview.valueFirst ? this.preview.valueFirst : '';
    if ([2, 3, 4, 6].includes(this.keys)) {
      this.optionsName = this.inputMap[this.keys];
      if (this.keys === 6 || this.symbol === 3) {
        value = this.preview.valueFirst
          ? this.preview.valueFirst.split(',')
          : [];
        value.forEach((el, index) => {
          value[index] = parseInt(el);
        });
      } else {
        value = this.preview.valueFirst
          ? parseInt(this.preview.valueFirst)
          : '';
      }
      if (this.preview.optionSettings) {
        if (typeof this.preview.optionSettings === 'string') {
          let options = this.preview.optionSettings;
          options = JSON.parse(options.replace(/&quot;/g, '"'));
          this.preview.optionSettings = options;
        }
        this.preview.optionSettings[this.optionsName].optionsValue = value;
      }
    } else {
      this.preview.defaultValue = value;
    }
  },
  methods: {
    resetValue(keys) {
      let value = '';
      if ([2, 3, 4, 6].includes(keys)) {
        const optionsName = this.inputMap[keys];
        if (keys === 6 || this.symbol === 3) {
          value = [];
          if (this.symbol === 3) this.$refs['mySelect'].selectedLabel = '';
        } else {
          value = '';
        }

        if (this.preview.optionSettings[optionsName]) {
          this.optionsName = optionsName;
          this.preview.optionSettings[optionsName].optionsValue = value;
        }
      } else {
        this.preview.defaultValue = value;
      }
      this.preview.valueFirst = value;
    },
    initData(keys, value) {
      if ([2, 3, 4, 6].includes(keys)) {
        this.optionsName = this.inputMap[this.keys];
        if (keys === 6 || this.symbol === 3) {
          value = value ? value.split(',') : [];
          value = value.map(el => (Number(el) ? Number(el) : el));
          // value.forEach((el, index) => {
          //   value[index] = Number(el);
          // });
        } else {
          value = Number(value) ? Number(value) : '';
        }
        if (this.preview.optionSettings) {
          this.preview.optionSettings[this.optionsName].optionsValue = value;
        }
      } else {
        this.preview.defaultValue = value;
      }
    },
    change(v) {
      let val = v;
      if (val && this.symbol === 3) {
        val = val.join(',');
      }
      this.preview.valueFirst = val;
    },
    // 级联菜单
    handleCascader(e) {
      this.preview.optionSettings.treeOptions.optionsLabel = this.$refs.myCascader.getCheckedNodes()[0].pathLabels;
      this.preview.valueFirst = e.join();
    },
    handleExpandCascader(e) {
      if (e.length > 0) {
        this.preview.optionSettings.treeOptions.optionsValue = e;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.preview-condition {
  text-align: left;
  .el-form-item__label {
    word-break: break-word;
    line-height: 27px;
  }
  /deep/ .el-form-item__content {
    vertical-align: middle;
  }
  .single {
    position: relative;
    top: -2px;
    .el-form-item__content {
      .el-col-24 {
        display: flex;
        align-items: start;
      }
    }
    span {
      display: inline-block;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    .el-input,
    .el-textarea,
    .el-select,
    .el-date-editor,
    .el-cascader {
      vertical-align: top;
    }
  }
  /* 日期 省市区 */
  .date-box {
    position: relative;
    height: 40px;
    .el-date-editor,
    .el-cascader {
      width: 90%;
    }
    .date-show {
      min-width: 300px;
      height: 100%;
      background: #fff;
      padding-left: 30px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      color: #606266;
      .el-icon-time {
        position: absolute;
        top: 50%;
        left: 10px;
        transform: translateY(-50%);
      }
    }
    .date-opcity {
      opacity: 0;
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      z-index: 10;
    }
  }
  &.is-disabled {
    /deep/.el-select__tags {
      .el-tag {
        color: #c0c4cc !important;
      }
    }
  }
}
</style>

