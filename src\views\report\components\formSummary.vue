<template>
  <div calss="formSummary">
    <el-button icon="el-icon-upload2" class="export-excel" @click="Summaryexport">导出Excel</el-button>
    <el-form
      ref="listQuery"
      :rules="rules"
      :model="listQuery"
      :inline="true"
      class="search-form"
      label-position="right"
    >
      <el-row :gutter="10" type="flex" class="row-bg" justify="space-between">
        <!-- 发起时间-->
        <el-form-item label="日期" prop="dataRange">
          <el-date-picker
            v-model="listQuery.dataRange"
            :picker-options="pickerOptions"
            type="daterange"
            range-separator="-"
            size="small"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            text="erdf"
            prefix-icon="el-icon-date"
            class="innerSelTime"
            @focus="dateTimeFocus()"
          />
        </el-form-item>
        <!--工单类型-->
        <el-form-item label="工单类型" prop="workOrderType">
          <el-select v-model="listQuery.workOrderType" placeholder="请选择">
            <el-option label="全部" value></el-option>
            <el-option
              v-for="item in sheetTypeList"
              :key="item.id"
              :label="item.typeName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <!--客户所在地-->
        <el-form-item label="客户所在地" prop="customArea">
          <el-select v-model="listQuery.customArea" placeholder="请选择">
            <el-option label="全部" value></el-option>
            <el-option
              v-for="item in workLocation"
              :key="item.id"
              :label="item.sourceName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-row>
      <el-row type="flex" class="row-bg" justify="end">
        <el-form-item class="ConditionP">
          <el-button
            plain
            type="primary"
            size="small"
            class="searchCondition"
            @click="handerSearch('listQuery')"
          >查询</el-button>
          <el-button plain size="small" @click="resetForm('listQuery')">重置</el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div class="info-container">
      <info :info="info"></info>
    </div>
    <transition name="fade">
      <keep-alive>
        <xyy-table
          :data="list"
          :col="col"
          :list-query="listQuery"
          :is-pagination="false"
          :is-stripe="false"
        ></xyy-table>
      </keep-alive>
    </transition>

    <!-- 导出提示框-->
    <expor-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    ></expor-tip>
  </div>
</template>

<script>
import { getUserSummary, Summaryexport } from '@/api/report/index';
import exporTip from '@/views/work-sheet/components/exportTip';
export default {
  name: 'FormSummary',
  components: {
    exporTip
  },
  props: {
    sheetTypeList: {
      type: Array,
      default: function() {
        return [];
      }
    },
    workLocation: {
      type: Array,
      default: function() {
        return [];
      }
    }
  },
  data() {
    return {
      listQuery: {
        // 搜索变量
        dataRange: [new Date(new Date().setHours(0, 0, 0, 0)), new Date()], // 日期
        workOrderType: '', // 工单类型
        customArea: '' // 客户所在地
      },
      rules: {
        dataRange: [
          { required: true, message: '日期为必填项', trigger: 'blur' }
        ]
      },
      list: [], // 表单数据
      col: [
        {
          index: 'reportDate',
          name: '日期',
          width: 120,
          fixed: true,
          resizable: true
        },
        {
          index: 'currentCreatedCount',
          name: '当日创建工单数',
          width: 140,
          resizable: true
        },
        {
          index: 'currentReturnCount',
          name: '被退回工单数',
          smallName: '当日新增',
          resizable: true,
          width: 140
        },
        {
          index: 'pastReturnCount',
          name: '被退回工单数',
          smallName: '历史工单',
          resizable: true,
          width: 140
        },
        {
          index: 'currentTakeCount',
          name: '领取工单数',
          smallName: '当日新增',
          resizable: true,
          width: 140
        },
        {
          index: 'pastTakeCount',
          name: '领取工单数',
          smallName: '历史工单',
          resizable: true,
          width: 140
        },
        {
          index: 'allTakeCount',
          name: '领取工单数',
          smallName: '所有工单',
          resizable: true,
          width: 140
        },
        {
          index: 'currentHandledCount',
          name: '已处理工单数',
          smallName: '当日新增',
          resizable: true,
          width: 140
        },
        {
          index: 'pastHandledCount',
          name: '已处理工单数',
          smallName: '历史工单',
          resizable: true,
          width: 140
        },
        {
          index: 'allHandledCount',
          name: '已处理工单数',
          smallName: '所有工单',
          resizable: true,
          width: 140
        },
        {
          index: 'currentHandledTimes',
          name: '已处理工单次数',
          smallName: '当日新增',
          resizable: true,
          width: 140
        },
        {
          index: 'pastHandledTimes',
          name: '已处理工单次数',
          smallName: '历史工单',
          resizable: true,
          width: 140
        },
        {
          index: 'allHandledTimes',
          name: '已处理工单次数',
          smallName: '所有工单',
          resizable: true,
          width: 140
        },
        {
          index: 'unhandledCount',
          name: '未处理工单数',
          width: 140,
          resizable: true
        },
        {
          index: 'currentFinishedCount',
          name: '完成工单数',
          smallName: '当日新增',
          resizable: true,
          width: 140
        },
        {
          index: 'pastFinishedCount',
          name: '完成工单数',
          smallName: '历史工单',
          resizable: true,
          width: 140
        },
        {
          index: 'allFinishedCount',
          name: '完成工单数',
          smallName: '所有工单',
          resizable: true,
          width: 140
        },
        {
          index: 'currentOvertimetHandledCount',
          name: '超时处理工单数',
          smallName: '当日新增',
          resizable: true,
          width: 140
        },
        {
          index: 'pastOvertimeHandledCount',
          name: '超时处理工单数',
          smallName: '历史工单',
          resizable: true,
          width: 140
        },
        {
          index: 'allOvertimeHandledCount',
          name: '超时处理工单数',
          smallName: '所有工单',
          resizable: true,
          width: 140
        },
        {
          index: 'avgTakeUseTime',
          name: '平均领取总时长(秒)',
          width: 170,
          resizable: true
        },
        {
          index: 'sumHandleUseTime',
          name: '工单处理总时长(小时)',
          width: 180,
          resizable: true
        },
        {
          index: 'avgHandleUseTime',
          name: '工单平均处理时长(小时)',
          width: 200,
          resizable: true
        },
        {
          index: 'firstResponseCount',
          name: '首次响应工单数',
          width: 140,
          resizable: true
        },
        {
          // 2020.4.1,rl,v1.7.4
          index: 'currentTakeFirstResponseCount',
          name: '领取当日首次响应工单数',
          width: 200,
          resizable: true
        },
        {
          index: 'avgFirstResponseCount',
          name: '平均首次响应时长(秒)',
          resizable: true,
          width: 180
        },
        {
          index: 'rollinCount',
          name: '转入工单数',
          width: 140,
          resizable: true
        },
        {
          index: 'rolloutCount',
          name: '转出工单数',
          width: 140,
          resizable: true
        },
        {
          index: 'allotCount',
          name: '分配工单数',
          width: 140,
          resizable: true
        },
        {
          index: 'closeRate12',
          name: '12H结案率',
          width: 140,
          resizable: true
        },
        {
          index: 'closeRate24',
          name: '24H结案率',
          width: 140,
          resizable: true
        },
        {
          index: 'closeRate48',
          name: '48H结案率',
          width: 140,
          resizable: true
        },
        {
          index: 'closeRate72',
          name: '72H结案率',
          width: 140,
          resizable: true
        },
        {
          index: 'closeNum12',
          name: '12H结案工单数',
          width: 140,
          resizable: true
        },
        {
          index: 'closeNum24',
          name: '24H结案工单数',
          width: 140,
          resizable: true
        },
        {
          index: 'closeNum48',
          name: '48H结案工单数',
          width: 140,
          resizable: true
        },
        {
          index: 'closeNum72',
          name: '72H结案工单数',
          width: 140,
          resizable: true
        },
        {
          index: 'todayTimeoutClaimNum',
          name: '超时领取工单数',
          smallName: '当日新增',
          width: 160,
          resizable: true
        },
        {
          index: 'historyTimeoutClaimNum',
          name: '超时领取工单数',
          smallName: '历史工单',
          width: 160,
          resizable: true
        },
        {
          index: 'allTimeoutClaimNum',
          name: '超时领取工单数',
          smallName: '所有工单',
          width: 160,
          resizable: true
        },
        {
          index: 'todayTimeoutCloseNum',
          name: '超时完结工单数',
          smallName: '当日新增',
          width: 160,
          resizable: true
        },
        {
          index: 'historyTimeoutCloseNum',
          name: '超时完结工单数',
          smallName: '历史工单',
          width: 160,
          resizable: true
        },
        {
          index: 'allTimeoutCloseNum',
          name: '超时完结工单数',
          smallName: '所有工单',
          width: 160,
          resizable: true
        }
      ],
      // totalData: {},
      changeExport: false,
      minDate: '',
      maxDate: '',
      pickerOptions: {
        // 设置事件跨度
        onPick: ({ maxDate, minDate }) => {
          this.minDate = minDate;
          this.maxDate = maxDate;
        },
        disabledDate: time => {
          const curDate = new Date().getTime();
          const two = 365 * 2 * 24 * 3600 * 1000;
          const twoyear = curDate - two;
          const three = 92 * 24 * 3600 * 1000;
          if (this.minDate) {
            return (
              time.getTime() > Date.now() ||
              time.getTime() < twoyear ||
              time > new Date(this.minDate.getTime() + three) ||
              time < new Date(this.minDate.getTime() - three)
            );
          }
          return time.getTime() > Date.now() || time.getTime() < twoyear;
        }
      },
      dialogVisible: false, // 控制导出弹框的出现
      info: [
        // 问号数据
        {
          title: '当日创建工单数',
          info: '工单创建时间在当日内，计算各用户组&组内人员这些工单的总量'
        },
        {
          title: '被退回工单数（当日新增）',
          info:
            '工单退回在当日内，工单创建时间也在当日内，统计发起这些工单的用户组或人员各被退回了多少条工单'
        },
        {
          title: '被退回工单数（历史工单）',
          info:
            '工单退回在当日内，工单创建时间早于当日，统计发起这些工单的用户组或人员各被退回了多少条工单'
        },
        {
          title: '领取工单数（当日新增）',
          info:
            '工单创建时间在当日内，工单领取时间也为当日，计算各用户组&组内人员这些工单的总量。（多次领取同一条工单计为1）'
        },
        {
          title: '领取工单数（历史工单）',
          info:
            '工单创建时间早于当日，工单领取时间也为当日，计算各用户组&组内人员这些工单的总量。（多次领取同一条工单计为1）'
        },
        {
          title: '领取工单数（所有工单）',
          info:
            '工单领取时间在当日内，不论发起时间是哪天，计算各用户组&组内人员这些工单的总量。（多次领取同一条工单计为1）'
        },
        {
          title: '已处理工单数（当日新增）',
          info:
            '工单创建时间在当日内，工单处理（提交回复）时间也为当日，计算各用户组&组内人员这些工单的总量。（多次处理同一条工单计为1）'
        },
        {
          title: '已处理工单数（历史工单）',
          info:
            '工单创建时间不在当日内，工单处理（提交回复）时间也为当日，计算各用户组&组内人员这些工单的总量。（多次处理同一条工单计为1）'
        },
        {
          title: '已处理工单数（所有工单）',
          info:
            '工单处理（提交回复）时间在当日内，不论发起时间是否为当日，计算各用户组&组内人员这些工单的总量。（多次处理同一条工单计为1）'
        },
        {
          title: '已处理工单次数（当日新增）',
          info:
            '工单创建时间在当日内，工单处理（提交回复）时间也为当日，计算各用户组&组内人员操作这些工单的总次量。（多次处理同一条工单计为n）'
        },
        {
          title: '已处理工单次数（历史工单）',
          info:
            '工单创建时间不在当日内，工单处理（提交回复）时间也为当日，计算各用户组&组内人员操作这些工单的总次量。（多次处理同一条工单计为n）'
        },
        {
          title: '已处理工单次数（所有工单）',
          info:
            '工单处理（提交回复）时间在当日内，不论发起时间是否为当日，计算各用户组&组内人员操作这些工单的总次量。（多次处理同一条工单计为n）'
        },
        {
          title: '未处理工单数',
          info: '领取工单数（所有工单）-已处理工单数（所有工单）'
        },
        {
          title: '完成工单数（当日新增）',
          info:
            '工单创建时间在当日内，工单完结（关闭工单）时间也为当日，计算各用户组&组内人员这些工单的总量。'
        },
        {
          title: '完成工单数（历史工单）',
          info:
            '工单创建时间不在当日内，工单完结（关闭工单）时间也为当日，计算各用户组&组内人员这些工单的总量。'
        },
        {
          title: '完成工单数（所有工单）',
          info:
            '工单完结（关闭工单）时间在当日内，不论发起时间是否为当日，计算各用户组&组内人员这些工单的总量。'
        },
        {
          title: '超时处理工单数（当日新增）',
          info:
            '工单处理时间在当日内，且处理提交时工单已超时，工单创建时间在当日内。计算各用户组&组内人员这些工单的总量'
        },
        {
          title: '超时处理工单数（历史工单）',
          info:
            '工单处理时间在当日内，且处理提交时工单已超时，工单创建时间早于当日。计算各用户组&组内人员这些工单的总量'
        },
        {
          title: '超时处理工单数（所有工单）',
          info:
            '工单处理时间在当日内，且处理提交时工单已超时，不论工单创建的时间是哪天。计算各用户组&组内人员这些工单的总量'
        },
        {
          title: '平均领取总时长',
          info:
            '当日领取的所有工单中，计算各用户组&组内人员对于这些工单从工单到本节点到员工领取工单之间的平均用时'
        },
        {
          title: '工单处理总时长',
          info:
            '当日处理的所有工单中，计算各用户组&组内人员对于这些工单从领取工单到工单处理提交之间的总用时'
        },
        {
          title: '工单平均处理时长',
          info:
            '当日处理的所有工单中，计算各用户组&组内人员对于这些工单从领取工单到工单处理提交之间的平均用时'
        },
        {
          title: '首次响应工单数',
          info:
            '首次点击“去处理”的时间在当日内，计算各用户组&组内人员这些工单的总量'
        },
        {
          title: '领取当日首次响应工单数',
          info:
            '领取当日首次响应工单数 = 某部门/坐席 当天领取，并且首次响应的工单数。如当日响应多次仅计一次'
        },
        {
          title: '平均首次响应时长',
          info:
            '工单响应时间在当日内，响应时长=工单响应时间-工单领取时间，计算各用户组&组内人员这些工单总量的平均用时'
        },
        {
          title: '转入工单数',
          info: '操作工单转移的时间为当日，转给各用户组&组内人员的工单量'
        },
        {
          title: '转出工单数',
          info: '操作工单转移的时间为当日，从各用户组&组内人员中转出的工单量'
        },
        {
          title: '分配工单数',
          info: '操作工单分配的时间为当日，分配给各用户组&组内人员的工单量'
        },
        {
          title: '12H结案率',
          info:
            '当日用户组内人员已完结的所有工单中，完结时间(结单时间-创建时间)<=12小时的工单数/结案工单数(所有工单)'
        },
        {
          title: '24H结案率',
          info:
            '当日用户组内人员已完结的所有工单中，完结时间(结单时间-创建时间)<=24小时的工单数/结案工单数(所有工单)'
        },
        {
          title: '48H结案率',
          info:
            '当日用户组内人员已完结的所有工单中，完结时间(结单时间-创建时间)<=48小时的工单数/结案工单数(所有工单)'
        },
        {
          title: '72H结案率',
          info:
            '当日用户组内人员已完结的所有工单中，完结时间(结单时间-创建时间)<=72小时的工单数/结案工单数(所有工单)'
        },
        {
          title: '12H结案工单数',
          info:
            '当日用户组内人员已完结的所有工单中，完结时间(结单时间-创建时间)<=12小时的工单数'
        },
        {
          title: '24H结案工单数',
          info:
            '当日用户组内人员已完结的所有工单中，完结时间(结单时间-创建时间)<=24小时的工单数'
        },
        {
          title: '48H结案工单数',
          info:
            '当日用户组内人员已完结的所有工单中，完结时间(结单时间-创建时间)<=48小时的工单数'
        },
        {
          title: '72H结案工单数',
          info:
            '当日用户组内人员已完结的所有工单中，完结时间(结单时间-创建时间)<=72小时的工单数'
        },
        {
          title: '超时领取工单数(当日新增)',
          info:
            '工单创建时间在当日内，工单领取时间也为当日，计算各用户组&组内人员超时领取工单的总量。（多次领取同一条工单计为1）'
        },
        {
          title: '超时领取工单数(历史工单)',
          info:
            '工单创建时间早于当日，工单领取时间为当日，计算各用户组&组内人员超时领取工单的总量。（多次领取同一条工单计为1）'
        },
        {
          title: '超时领取工单数(所有工单)',
          info:
            '工单领取时间在当日内，不论发起时间是哪天，计算各用户组&组内人员超时领取工单的总量。（多次领取同一条工单计为1）'
        },
        {
          title: '超时完结',
          info: '工单从发起到完结的实际总时长>工单的预计完结时长'
        },
        {
          title: '超时完结工单数(当日新增)',
          info:
            '工单创建时间在当日内，工单完结（关闭工单）时间为当日，计算各用户组&组内人员超时完结工单的总量'
        },
        {
          title: '超时完结工单数(历史工单)',
          info:
            '工单创建时间早于当日，工单完结（关闭工单）时间为当日，计算各用户组&组内人员超时完结工单的总量'
        },
        {
          title: '超时完结工单数(所有工单)',
          info:
            '工单完结（关闭工单）时间在当日内，不论发起时间是哪天，计算各用户组&组内人员超时完结工单的总量'
        }
      ]
    };
  },
  methods: {
    // 表格导出事件
    Summaryexport() {
      const that = this;
      if (!that.list || !that.list.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      //         以下是导出的数据的参数
      const { dataRange } = this.listQuery;
      const params = JSON.parse(JSON.stringify(this.listQuery));
      delete params.dataRange;
      params.startDate = new Date(dataRange[0]).Format('yy-MM-dd');
      params.endDate = new Date(dataRange[1]).Format('yy-MM-dd');
      Summaryexport(params).then(res => {
        console.log(res);
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.changeExport = true;
      });
    },
    //    跳转事件
    handleChangeExport() {
      this.changeExport = false;
      // 接口请求 提交工单明细或流转记录生成 下载
      this.$router.push({
        path: '/other/index/'
      });
    },
    //      表单查询事件
    handerSearch(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          const { dataRange } = this.listQuery;
          const params = JSON.parse(JSON.stringify(this.listQuery));
          delete params.dataRange;
          params.startDate = new Date(dataRange[0]).Format('yy-MM-dd');
          params.endDate = new Date(dataRange[1]).Format('yy-MM-dd');
          this.getUserSummary(params);
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    //      表单重置
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //      获取表格数据接口
    getUserSummary(params) {
      getUserSummary(params).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }

        let dataListList = res.data.dataList;
        if (
          !dataListList &&
          typeof dataListList != 'undefined' &&
          dataListList != 0
        ) {
          this.$XyyMessage.success('暂时无数据');
          this.list = [];
          // this.totalData = {};
          return;
        }

        if (res.data && res.data.dataList) {
          res.data.totalData.reportDate = '合计';
          res.data.dataList.push(res.data.totalData);
          this.list = res.data.dataList;
          // this.totalData = res.data.totalData;
        } else {
          this.list = [];
          // this.totalData = {};
        }
      });
    },
    // 事件选择获取焦点时触发
    dateTimeFocus() {
      this.minDate = undefined;
      // 条件时间查询
      const that = this;
      that.$nextTick(function() {
        document
          .getElementsByClassName('el-picker-panel')[0]
          .addEventListener('click', function() {
            // that.$refs.messageDrop.show();
          });
      });
    },
    //     导出弹框里面的事件
    handleExoprClose() {
      this.changeExport = false;
    }
  }
};
</script>
<style scoped lang="scss">
/deep/.el-form-item {
  margin-bottom: 20px !important;
}
/deep/.el-form-item__label {
  font-weight: 500;
}
/deep/.el-table {
  /deep/.el-table__fixed-body-wrapper {
    top: 58px !important;
  }
}
/deep/.page .page-body {
  height: 0;
}
/deep/ .el-tabs__content {
  overflow: visible;
}
/deep/ .el-tabs__nav-wrap::after {
  background-color: #fff;
}
/deep/.el-date-editor {
  /*width: 450px;*/
}
/*合计滚动条样式*/
/*/deep/.el-table {*/
/*  overflow-x: auto;*/
/*}*/
/*/deep/.el-table__header-wrapper,*/
/*/deep/.el-table__body-wrapper,*/
/*/deep/.el-table__footer-wrapper {*/
/*  overflow: visible;*/
/*  margin-bottom: 0 !important;*/
/*}*/
/*/deep/.el-table::after {*/
/*  position: relative;*/
/*}*/
/*/deep/.el-table--scrollable-x .el-table__body-wrapper {*/
/*  overflow: visible;*/
/*}*/
/*合计滚动条样式--------结束----*/
.el-menu-item.is-active {
  color: #3b95a8 !important;
}
/deep/.el-input__inner {
  height: 36px !important;
  line-height: 36px !important;
}
/deep/.el-button {
  height: 36px;
}
.el-menu.el-menu--horizontal {
  border-bottom: 1px dashed #e4e4eb;
  padding-bottom: 20px;
}
/deep/.search-form {
  border-bottom: 1px dashed #e4e4eb;
}

.page {
  position: relative;
}
.export-excel {
  position: absolute;
  right: 20px;
  top: -84px;
  z-index: 999;
  padding-left: 12px;
  padding-right: 19px;
  border-radius: unset;
}

.searchCondition.is-plain {
  background: rgba(59, 149, 168, 1);
  color: #fff;
}
/deep/.searchCondition.is-plain:hover {
  background: rgba(40, 126, 144, 1);
}
/deep/.el-table th {
  padding-top: 10px;
  padding-bottom: 10px;
  text-align: left;
  padding-right: 19px;
  p {
    margin: 0;
    height: 20px;
    text-align: right;
  }
  small {
    color: #909399;
    font-size: 12px;
    height: 20px;
    display: block;
    text-align: right;
  }
}
.search-form {
  margin-top: 18px;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
/deep/.el-table__body tr td {
  border-bottom: 1px solid #ebeef5;
  background: #fff;
}
</style>
