import request from '@/utils/request';

/* 删除公告 */
export function delKnowledgeNotice(data) {
  return request({
    url: '/imapi/knowledgeNotice/delKnowledgeNotice',
    method: 'get',
    params: data
  });
}
/* 查询公告详情*/
export function findKnowledgeNoticeDetail(data) {
  return request({
    url: '/imapi/knowledgeNotice/findKnowledgeNoticeDetail',
    method: 'get',
    params: data
  });
}
/* 分页查询公告列表*/
export function findKnowledgeNoticeList(data) {
  return request({
    url: '/imapi/knowledgeNotice/findKnowledgeNoticeList',
    method: 'post',
    data: data
  });
}
/* 新增公告*/
export function saveKnowledgeNotice(data) {
  return request({
    url: '/imapi/knowledgeNotice/saveKnowledgeNotice',
    method: 'post',
    data: data
  });
}
/* 修改公告*/
export function updateKnowledgeNotice(data) {
  return request({
    url: '/imapi/knowledgeNotice/updateKnowledgeNotice',
    method: 'post',
    data: data
  });
}
/* 知识分类-删除分类*/
export function delKnowledgeClassification(data) {
  return request({
    url: '/imapi/knowledgeClassification/delKnowledgeClassification',
    method: 'get',
    params: data
  });
}
/* 知识分类-保存知识分类*/
export function saveOrUpdateKnowledgeClassification(data) {
  return request({
    url: '/imapi/knowledgeClassification/saveOrUpdateKnowledgeClassification',
    method: 'post',
    data: data
  });
}
/* 知识管理-知识树列表*/
export function listAllClassification(data) {
  return request({
    url: '/imapi/knowledgeClassification/listAllClassification',
    method: 'get',
    params: data
  });
}
/* 知识管理-保存知识管理草稿*/
export function saveOrUpdateKnowledgeManagementDraftBox(data) {
  return request({
    url:
      '/imapi/knowledgeManagementDraftBox/saveOrUpdateKnowledgeManagementDraftBox',
    method: 'post',
    data: data
  });
}
/* 知识管理-删除知识管理草稿*/
export function deleteKnowledgeManagementDraftBox(data) {
  return request({
    url: '/imapi/knowledgeManagementDraftBox/deleteKnowledgeManagementDraftBox',
    method: 'get',
    params: data
  });
}
/* 知识管理-获取知识管理草稿*/
export function getKnowledgeManagementDraftBox(data) {
  return request({
    url: '/imapi/knowledgeManagementDraftBox/getKnowledgeManagementDraftBox',
    method: 'get',
    params: data
  });
}
/* 知识管理-分页查询知识管理草稿箱列表*/
export function listKnowledgeManagementDraftBox(data) {
  return request({
    url: '/imapi/knowledgeManagementDraftBox/listKnowledgeManagementDraftBox',
    method: 'post',
    data: data
  });
}
/* 知识管理-发布知识管理草稿*/
export function reportKnowledgeManagement(data) {
  return request({
    url: '/imapi/knowledgeManagementDraftBox/reportKnowledgeManagement',
    method: 'get',
    params: data
  });
}
/* 知识管理-修改或者保存知识管理*/
export function saveOrUpdateKnowledgeManagement(data) {
  return request({
    url: '/imapi/knowledgeManagement/saveOrUpdateKnowledgeManagement',
    method: 'post',
    data: data
  });
}
/* 知识管理-分页查询知识管理列表*/
export function findKnowledgeManagementList(data) {
  return request({
    url: '/imapi/knowledgeManagement/findKnowledgeManagementList',
    method: 'post',
    data: data
  });
}
/* 知识管理-复制知识管理地址*/
export function copyKnowledgeManagementAddress(data) {
  return request({
    url: '/imapi/knowledgeManagement/copyKnowledgeManagementAddress',
    method: 'get',
    params: data
  });
}
/* 知识管理-删除知识管理*/
export function deleteKnowledgeManagement(data) {
  return request({
    url: '/imapi/knowledgeManagement/deleteKnowledgeManagement',
    method: 'get',
    params: data
  });
}
/* 知识管理-查询知识管理详情*/
export function getKnowledgeManagementDetail(data) {
  return request({
    url: '/imapi/knowledgeManagement/getKnowledgeManagementDetail',
    method: 'get',
    params: data
  });
}
/* 知识管理-列出知识管理历史*/
export function listKnowledgeManagementHistory(data) {
  return request({
    url: '/imapi/knowledgeManagement/listKnowledgeManagementHistory',
    method: 'get',
    params: data
  });
}
/* 知识管理-启用禁用知识管理*/
export function updateKnowledgeManagementEnableOrDisable(data) {
  return request({
    url: '/imapi/knowledgeManagement/updateKnowledgeManagementEnableOrDisable',
    method: 'post',
    data: data
  });
}
/* 知识管理-批量转移知识*/
export function batchMoveKnowledgeManagement(data) {
  return request({
    url: '/imapi/knowledgeManagement/batchMoveKnowledgeManagement',
    method: 'post',
    data: data
  });
}

/* 知识搜索 */
export function searchKnowledgeList(data) {
  return request({
    url: '/imapi/knowledge/query/search',
    method: 'post',
    data: data
  });
}

/**
 * 知识大厅-热词TOP：历史、当天、当周、当月
 */
export function queryTopKeywords(data) {
  return request({
    url: '/imapi/knowledgeHall/topKeywords',
    method: 'get',
    params: data
  });
}

/**
 * 知识大厅-热词TOP：查询收藏总数
 */
export function queryFavoriteCount() {
  return request({
    url: '/imapi/favorite/count',
    method: 'get'
  })
}

/**
 * 根据关键字查询分页启动状态的知识，支持排序
 */
export function pageKnowledgeByKeyword(params) {
  return request({
    url: '/imapi/knowledgeHall/pageKnowledgeByKeyword',
    method: 'get',
    params
  })
}

/**
 * 根据分类ID查询分页启动状态的知识，支持排序
 */
export function pageKnowledgeByCategory(params) {
  return request({
    url: '/imapi/knowledgeHall/pageKnowledgeByCategory',
    method: 'get',
    params
  })
}

/**
 * 根据分类id和关键词组合查询，支持排序
 */
export function pageKnowledge(params){
  return request({
    url:'/imapi/knowledgeHall/pageKnowledge',
    method:'get',
    params
  })
}

/* 知识详情-收藏'*/
export function favoriteConfirm(data) {
  return request({
    url: '/imapi/favorite/confirm',
    method: 'get',
    params: data
  });
}
/* 知识详情-取消收藏*/
export function favoriteCancel(data) {
  return request({
    url: '/imapi/favorite/cancel',
    method: 'get',
    params: data
  });
}
/* 查询知识收藏知识列表*/
export function favoriteSelectList(data) {
  return request({
    url: '/imapi/favorite/select',
    method: 'post',
    data: data
  });
}
