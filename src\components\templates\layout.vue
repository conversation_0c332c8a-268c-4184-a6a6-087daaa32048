<template>
  <div ref="layout" :class="{'is-editing':editing}" class="template-layout">
    <el-row v-for="(row,index) in layArray" :key="index" :ref="`row${index}`">
      <el-col
        v-for="(col,i) in row"
        :key="i"
        :data-row="index"
        :data-col="i"
        :span="row.length===1?24:12"
        class="field-container"
      >
        <slot :arr="colArray" :row="index" :col="i" name="col"></slot>
      </el-col>
      <span :class="{active:rowStatus[index]}" class="layout-operation">
        <el-dropdown
          v-if="editing"
          :key="`op-${index}`"
          trigger="click"
          @command="operateRow($event,index)"
          @visible-change="handleChange($event,index)"
        >
          <i class="op-layout" title="行操作"></i>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="0">上方插入行</el-dropdown-item>
            <el-dropdown-item command="1">下方插入行</el-dropdown-item>
            <el-dropdown-item
              v-show="layArray[index].length>1 && layArray[index].some(el=>!Number(el))"
              command="2"
            >合并行</el-dropdown-item>
            <el-dropdown-item
              v-show="layArray.length>1 && layArray[index].every(el=>!Number(el))"
              command="3"
            >删除行</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </span>
    </el-row>
  </div>
</template>

<script>
export default {
  props: {
    layDatas: {
      type: String,
      default: '0-0,0-0,0-0'
    },
    colDatas: {
      type: Array,
      default: () => []
    },
    editing: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rowStatus: []
    };
  },
  computed: {
    layArray() {
      return this.layDatas.split(',').map(el => el.split('-'));
    },
    colArray() {
      const arr = [];
      let k = 0;
      this.layArray.forEach((cols, i) => {
        arr.push([]);
        cols.forEach((col, j) => {
          if (Number(col)) {
            arr[i][j] = this.colDatas[k++];
          }
        }, this);
      }, this);
      return arr;
    },
    rowLength() {
      return this.layDatas.split(',').length;
    }
  },
  watch: {
    /**
     * 动态变换行高
     */
    layArray: {
      handler(arr) {
        if (this.editing) {
          this.initRowHeight(arr);
          this.$emit('resizeLayWidth');
        }
      },
      deep: true
    },
    /**
     * 监听 行 长度
     */
    rowLength(len) {
      if (this.editing) {
        this.initRowStatus();
        this.$emit('resizeLayWidth');
      }
    }
  },
  mounted() {
    if (this.editing) this.initRowStatus();
  },
  methods: {
    /**
     * 行 操作按钮 命令
     */
    operateRow(type, index) {
      const arr = JSON.parse(JSON.stringify(this.layArray));
      if (type === '0') {
        arr.splice(index, 0, [0, 0]);
      } else if (type === '1') {
        arr.splice(index + 1, 0, [0, 0]);
      } else if (type === '2') {
        // 仅兼容2列
        if (arr[index].every(el => Number(el))) {
          this.$XyyMessage.error('请先删除字段');
          return;
        } else if (arr[index].some(el => Number(el))) {
          arr.splice(index, 1, [1]);
        } else {
          arr.splice(index, 1, [0]);
        }
      } else if (type === '3') {
        arr.splice(index, 1);
      }
      this.$emit('setLayDatas', arr.map(cols => cols.join('-')).join());
    },
    /**
     * 行操作按钮 状态变换
     */
    handleChange(status, index) {
      this.rowStatus.splice(index, 1, status);
    },
    /**
     * 初始化 行 操作按钮状态
     */
    initRowStatus() {
      const arr = [];
      let i = 0;
      while (i < this.rowLength) {
        arr[i++] = false;
      }
      this.rowStatus = arr;
    },
    /**
     * 获取列坐标
     */
    getPosition(x, y) {
      let row = -1;
      let col = -1;
      const layout = this.$refs['layout'];
      let node = layout;
      let layTop = 0;
      let layLeft = 0;
      let scrollTop = 0;
      let scrollLeft = 0;
      while (node.offsetParent) {
        layTop += node.offsetTop;
        layLeft += node.offsetLeft;
        scrollTop += node.scrollTop;
        scrollLeft += node.scrollLeft;
        node = node.offsetParent;
      }
      const pTop = layTop - scrollTop;
      const pLeft = layLeft - scrollLeft;
      const pageX = x - pLeft;
      const pageY = y - pTop;
      this.layArray.forEach((el, i) => {
        const arr = this.$refs[`row${i}`][0].$el.children;
        for (let j = 0; j < arr.length; j++) {
          if (arr[j].tagName.toLowerCase() === 'div') {
            const left = arr[j].offsetLeft + arr[j].offsetParent.offsetLeft;
            const right = left + arr[j].offsetWidth;
            const top = arr[j].offsetTop + arr[j].offsetParent.offsetTop;
            const bottom = top + arr[j].offsetHeight;
            if (
              pageX >= left &&
              pageX <= right &&
              pageY >= top &&
              pageY <= bottom
            ) {
              row = Number(arr[j].getAttribute('data-row'));
              col = Number(arr[j].getAttribute('data-col'));
            }
          }
        }
      }, this);
      return { row, col };
    },
    /**
     * 初始化行高
     */
    initRowHeight(arr) {
      if (!arr) {
        arr = this.layArray;
      }
      arr.forEach((el, i) => {
        this.$nextTick(() => {
          const empty = arr[i].every(col => !Number(col));
          let maxHeight = 66;
          this.$refs[`row${i}`][0].$children.forEach(obj => {
            if (obj.tag === 'div') {
              let _height = 0;
              if (obj.$children[0].$el.children[0]) {
                _height = obj.$children[0].$el.children[0].offsetHeight;
              }
              if (_height > maxHeight) {
                maxHeight = _height;
              }
            }
          });
          const height = empty ? 66 : maxHeight;
          this.$refs[`row${i}`][0].$children.forEach(obj => {
            if (obj.tag === 'div') {
              obj.$children[0].$el.style.minHeight = height + 'px';
            }
          });
        });
      }, this);
    }
  }
};
</script>

<style lang="scss" scoped>
.template-layout {
  width: 100%;
  position: relative;
  .el-row {
    position: relative;
  }
  &.is-editing {
    .el-row {
      border-bottom: 1px solid rgba(228, 228, 235, 1);
      .el-col {
        border-right: 1px solid rgba(228, 228, 235, 1);
        &:last-child {
          border-right: none;
        }
        &:first-child {
          border-left: 1px solid rgba(228, 228, 235, 1);
        }
      }
      .layout-operation {
        width: 25px;
        height: 25px;
        position: absolute;
        background: #f0f2f5;
        top: 0;
        right: -25px;
        padding: 4.5px;
        cursor: pointer;
        display: none;
        .el-dropdown {
          width: 100%;
          height: 100%;
          /deep/.el-dropdown-selfdefine {
            display: inline-block;
            width: 100%;
            height: 100%;
            outline: none;
          }
        }
      }
      &:hover {
        .layout-operation {
          display: inline-block;
        }
      }
      .layout-operation.active {
        display: inline-block;
      }
      i.op-layout {
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url(../../assets/icons/op-row.png) 0 0 no-repeat;
        &:hover {
          background: url(../../assets/icons/op-row-active.png) 0 0 no-repeat;
        }
      }
    }
  }
}
</style>
