import request from '@/utils/request-im';

export function getDistributeData() {
  return request({
    url: '/setting/leavmessage',
    method: 'get'
  });
}

export function saveDistributeData(data) {
  return request({
    url: '/setting/leavmessage/save',
    method: 'post',
    data: data
  });
}

export function getCommentList(params) {
  return request({
    url: '/leave-message/list',
    method: 'get',
    params: params
  });
}

export function getGroupDatas() {
  return request({
    url: '/leave-message/onlinekefu',
    method: 'get',
    params: { undergroup: true }
  });
}

export function getAllKefuGroupDatas() {
  return request({
    url: '/leave-message/allkefu',
    method: 'get',
    params: { undergroup: true }
  });
}

export function setAssignData(data) {
  return request({
    url: '/leave-message/assign',
    method: 'post',
    data: data
  });
}

export function setAssignDatas(data) {
  return request({
    url: '/leave-message/assign-batch',
    method: 'post',
    data: data
  });
}

export function getCommentData(params) {
  return request({
    url: `/dialogs/dialog-detail-info/${params.dialogId}`,
    method: 'get'
  });
}

export function getServiceData(params) {
  return request({
    url: '/serviceSummary/detail',
    method: 'get',
    params
  });
}
/**
 * 获取手动指派权限
 */
export function getAssignAcross() {
  return request({
    url: '/leave-message/can-assign',
    method: 'get'
  });
}
