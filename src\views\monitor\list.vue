<template>
  <div class="page-content">
    <el-row>
      <!-- <el-col :md="24" class="m-b"> -->
      <el-card class="box-card" shadow="never">
        <span>工单类型</span>
        <el-select
          v-model="formTypeId"
          placeholder="请选择"
          size="medium"
          popper-class="select-m"
          @change="changeType"
        >
          <el-option v-for="item in typeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-card>
      <!-- </el-col> -->

      <el-col :md="24" class="m-b">
        <statics2-item ref="statics2" />
      </el-col>

      <el-col :md="24" class="m-b">
        <order-monitor ref="orderMonitor" />
      </el-col>

      <el-col :md="24" class="m-b">
        <el-card class="box-card" shadow="never">
          <el-form :inline="true" label-position="right">
            <el-row class="herader-title" type="flex" justify="space-between" align="middle">
              <span style="font-size:16px">人员维度监控 TOP 10</span>
            </el-row>
            <el-row style="text-align:right;float:right;margin-top: -50px;">
              <el-form-item>
                <div class="searchTwoButton">
                  <xyy-button
                    style=" &:focus {background: #fff;border-color: #fff;}"
                    type="text"
                    class="export-excel"
                    @click="groupOpen=true"
                    icon-class="icon-fields-setting"
                  >字段设置</xyy-button>
                  <el-divider direction="vertical"></el-divider>
                  <el-button
                    icon="el-icon-upload2"
                    style="margin-right: 10px;"
                    type="text"
                    class="export-excel"
                    @click="exportUserGroupWorkData"
                  >导出excel</el-button>
                </div>
              </el-form-item>
            </el-row>
            <el-row type="flex" class="row-bg" justify="space-between">
              <el-row
                :gutter="10"
                type="flex"
                class="row-bg"
                justify="space-between"
                style="margin-left: 0"
              >
                <el-form-item label="用户组">
                  <el-input v-model="groupName" placeholder="请输入用户组" maxlength="20" size="medium"></el-input>

                  <!-- <el-select
                    v-model="listQuery.CustLocation"
                    :remote-method="remoteMethod"
                    filterable
                    remote
                    clearable
                    placeholder="请选择用户组"
                    size="small"
                  >
                    <el-option
                      v-for="item in options2"
                      :key="item.id"
                      :label="item.name+'('+item.code+')'"
                      :value="item.id"
                    ></el-option>
                  </el-select>-->
                </el-form-item>
                <el-form-item>
                  <el-button
                    plain
                    type="primary"
                    size="small"
                    class="searchCondition"
                    @click="handerSearchGroupName()"
                  >查询</el-button>
                  <el-button plain size="small" @click="resetFormGroupName">重置</el-button>
                </el-form-item>
              </el-row>
            </el-row>
          </el-form>

          <xyy-table
            style="width: 98.5%"
            :is-stripe="true"
            :data="userGroupWorkDataList"
            :col="filterUserList"
            :offset-top="240"
          ></xyy-table>
        </el-card>
      </el-col>

      <el-col :md="24" class="m-b">
        <el-card class="box-card" shadow="never">
          <el-form ref="nodeName" :inline="true" label-position="right">
            <el-row class="herader-title" type="flex" justify="space-between" align="middle">
              <span style="font-size:16px">节点维度监控 TOP 10</span>
            </el-row>
            <el-row style="text-align:right;float:right;margin-top: -50px;">
              <el-form-item>
                <div class="searchTwoButton">
                  <el-button
                    icon="el-icon-upload2"
                    style="margin-right: 10px;"
                    type="text"
                    class="export-excel"
                    @click="exportNodeWorkData"
                  >导出excel</el-button>
                </div>
              </el-form-item>
            </el-row>
            <el-row type="flex" class="row-bg" justify="space-between">
              <el-row
                :gutter="10"
                type="flex"
                class="row-bg"
                justify="space-between"
                style="margin-left: 0"
              >
                <el-form-item label="节点" prop="nodeName">
                  <el-input v-model="nodeName" placeholder="请输入节点" maxlength="20" size="medium"></el-input>
                  <!-- <el-select
                    v-model="nodeName"
                    :remote-method="remoteMethod"
                    filterable
                    remote
                    clearable
                    placeholder="请输入节点"
                    size="small"
                  >
                    <el-option
                      v-for="item in options2"
                      :key="item.id"
                      :label="item.name+'('+item.code+')'"
                      :value="item.id"
                    ></el-option>
                  </el-select>-->
                </el-form-item>
                <el-form-item>
                  <el-button
                    plain
                    type="primary"
                    size="small"
                    class="searchCondition"
                    @click="handerSearchNodeName()"
                  >查询</el-button>
                  <el-button plain size="small" @click="resetFormNodeName">重置</el-button>
                </el-form-item>
              </el-row>
            </el-row>
          </el-form>

          <xyy-table
            style="width: 98.5%"
            :is-stripe="true"
            :data="nodeWorkDataList"
            :col="colNode"
            :offset-top="240"
          ></xyy-table>
        </el-card>
      </el-col>

      <el-col :md="24" class="m-b">
        <question-classification-monitor
          ref="questionClassificationMonitor"
          @callbackCityCode="setCityCode"
          @callbackTab="setTab"
        />
      </el-col>

      <fieldsSetting
        :status.sync="groupOpen"
        :left-datas="employeeDatas"
        :checked="colPersonnel"
        title="字段设置"
        :formTypeId="formTypeId"
        @callback="setGroupDatas"
      ></fieldsSetting>
    </el-row>
  </div>
</template>

<script>
import {
  getNodeTotal,
  getStateTotal,
  getNodesUser,
  getNodesOrder
} from '@/api/monitor';
import loading from '@/mixin/loading';
import { getAllFormTypeList } from '@/api/formManage';
import Statics2Item from './components/statics2';
import orderMonitor from './components/orderMonitor';
import fieldsSetting from './components/fieldsSetting';
import questionClassificationMonitor from './components/questionClassificationMonitor';
import {
  getFindPersonnelMonitoringShowFieldData,
  getMonitorAllData
} from '@/api/monitor/index.js';
export default {
  name: 'List',
  mixins: [loading],
  components: {
    Statics2Item,
    orderMonitor,
    fieldsSetting,
    questionClassificationMonitor
  },
  data() {
    return {
      employeeDatas: [
        {
          index: 'currentCreatedCount',
          name: '当日创建工单数',
          minWidth: 160,
          resizable: true
        },
        {
          index: 'currentReturnCount',
          name: '被退回工单数(当日新增)',
          minWidth: 180,
          resizable: true
        },
        {
          index: 'pastReturnCount',
          name: '被退回工单数(历史工单)',
          minWidth: 180,
          resizable: true
        },
        {
          index: 'currentTakeCount',
          name: '领取工单数(当日新增)',
          minWidth: 180,
          resizable: true
        },
        {
          index: 'pastTakeCount',
          name: '领取工单数(历史工单)',
          minWidth: 160,
          resizable: true
        },
        {
          index: 'allTakeCount',
          name: '领取工单数(所有工单)',
          minWidth: 160,
          resizable: true
        },
        {
          index: 'currentHandledCount',
          name: '已处理工单数(当日新增)',
          minWidth: 180,
          resizable: true
        },
        {
          index: 'pastHandledCount',
          name: '已处理工单数(历史工单)',
          minWidth: 180,
          resizable: true
        },
        {
          index: 'allHandledCount',
          name: '已处理工单数(所有工单)',
          minWidth: 180,
          resizable: true
        },
        {
          index: 'currentHandledTimes',
          name: '已处理工单次数(当日新增)',
          minWidth: 200,
          resizable: true
        },
        {
          index: 'pastHandledTimes',
          name: '已处理工单次数(历史工单)',
          minWidth: 200,
          resizable: true
        },
        {
          index: 'allHandledTimes',
          name: '处理工单次数(所有工单)',
          minWidth: 180,
          resizable: true
        },
        {
          index: 'unhandledCount',
          name: '未处理工单数',
          minWidth: 140,
          resizable: true
        },
        {
          index: 'currentFinishedCount',
          name: '完成工单数(当日新增)',
          minWidth: 180,
          resizable: true
        },
        {
          index: 'pastFinishedCount',
          name: '完成工单数(历史工单)',
          minWidth: 180,
          resizable: true
        },
        {
          index: 'allFinishedCount',
          name: '完成工单数(所有工单)',
          minWidth: 180,
          resizable: true
        },
        {
          index: 'currentOvertimetHandledCount',
          name: '超时处理工单数(当日新增)',
          minWidth: 200,
          resizable: true
        },
        {
          index: 'pastOvertimeHandledCount',
          name: '超时处理工单数(历史工单)',
          minWidth: 200,
          resizable: true
        },
        {
          index: 'allOvertimeHandledCount',
          name: '超时处理工单数(所有工单)',
          minWidth: 200,
          resizable: true
        },
        {
          index: 'avgTakeUseTime',
          name: '平均领取总时长(秒)',
          minWidth: 170,
          resizable: true
        },
        {
          index: 'sumHandleUseTime',
          name: '工单处理总时长(小时)',
          minWidth: 180,
          resizable: true
        },
        {
          index: 'avgHandleUseTime',
          name: '工单平均处理时长(小时)',
          minWidth: 180,
          resizable: true
        },
        {
          index: 'firstResponseCount',
          name: '首次响应工单数',
          minWidth: 140,
          resizable: true
        },
        {
          // 2020.4.1,rl,v1.7.4
          index: 'currentTakeFirstResponseCount',
          name: '领取当日首次响应工单数',
          minWidth: 200,
          resizable: true
        },
        {
          index: 'avgFirstResponseTime',
          name: '平均首次响应时长(秒)',
          minWidth: 180,
          resizable: true
        },
        {
          index: 'rollinCount',
          name: '转入工单数',
          minWidth: 140,
          resizable: true
        },
        {
          index: 'rolloutCount',
          name: '转出工单数',
          minWidth: 140,
          resizable: true
        },
        {
          index: 'allotCount',
          name: '分配工单数',
          minWidth: 140,
          resizable: true
        },
        {
          index: 'closeRate12Str',
          name: '12H结案率',
          minWidth: 140,
          resizable: true
        },
        {
          index: 'closeRate24Str',
          name: '24H结案率',
          minWidth: 140,
          resizable: true
        },
        {
          index: 'closeRate48Str',
          name: '48H结案率',
          minWidth: 140,
          resizable: true
        },
        {
          index: 'closeRate72Str',
          name: '72H结案率',
          minWidth: 140,
          resizable: true
        },
        {
          index: 'closeNum12',
          name: '12H结案工单数',
          minWidth: 140,
          resizable: true
        },
        {
          index: 'closeNum24',
          name: '24H结案工单数',
          minWidth: 140,
          resizable: true
        },
        {
          index: 'closeNum48',
          name: '48H结案工单数',
          minWidth: 140,
          resizable: true
        },
        {
          index: 'closeNum72',
          name: '72H结案工单数',
          minWidth: 140,
          resizable: true
        },
        {
          index: 'todayTimeoutClaimNum',
          name: '超时领取工单数(当日新增)',
          minWidth: 200,
          resizable: true
        },
        {
          index: 'historyTimeoutClaimNum',
          name: '超时领取工单数(历史工单)',
          minWidth: 200,
          resizable: true
        },
        {
          index: 'allTimeoutClaimNum',
          name: '超时领取工单数(所有工单)',
          minWidth: 200,
          resizable: true
        },
        {
          index: 'todayTimeoutCloseNum',
          name: '超时完结工单数(当日新增)',
          minWidth: 200,
          resizable: true
        },
        {
          index: 'historyTimeoutCloseNum',
          name: '超时完结工单数(历史工单)',
          minWidth: 200,
          resizable: true
        },
        {
          index: 'allTimeoutCloseNum',
          name: '超时完结工单数(所有工单)',
          minWidth: 200,
          resizable: true
        }
      ],
      defaultColPersonnel: [
        {
          index: 'reportDate',
          name: '序号',
          minWidth: 50,
          fixed: true,
          resizable: true
        },
        {
          index: 'userGroup',
          name: '用户组',
          resizable: true,
          minWidth: 138,
          fixed: true
        },
        {
          index: 'userName',
          name: '人员',
          resizable: true,
          minWidth: 138,
          fixed: true
        }
      ],
      colNode: [
        {
          index: 'reportDate',
          name: '序号',
          resizable: true
        },
        {
          index: 'nodeName',
          name: '节点',
          resizable: true
        },
        {
          index: 'unclaimedWorkOrderCount',
          name: '待领取工单数',
          resizable: true
        },
        {
          index: 'waitDealWorkOrderCount',
          name: '待处理工单数',
          resizable: true
        },
        {
          index: 'soonTimeoutWorkOrderCount',
          name: '即将超时工单数',
          resizable: true
        },
        {
          index: 'timeOutWorkOrderHandledCount',
          name: '超时处理工单数',
          resizable: true
        }
      ],
      colPersonnel: [],
      formTypeId: '',
      cityCode: '',
      dateTimeType: '',
      groupName: '',
      nodeName: '',
      status: 0, // 调换了顺序，这个位置需要改
      timer: null,
      currentNode: {},
      typeList: [],
      total: {},
      nodeTotal: [],
      nodeClass: null,
      nodeCode: null,
      groupOpen: false, // 弹框状态
      props: {
        children: 'children',
        label: function(data, node) {
          return data;
        },
        isLeaf: 'leaf'
      },
      params: {
        formTypeId: this.formTypeId,
        cityCode: this.cityCode,
        dateTimeType: this.dateTimeType,
        groupName: this.groupName,
        nodeName: this.nodeName
      },
      nodeWorkDataList: [],
      userGroupWorkDataList: [],
      isset: '',
      groupNameCache: '', // 人员维度输入框内文案的缓存
      nodeNameCache: '' // 节点维度输入框内文案的缓存
    };
  },
  computed: {
    filterUserList() {
      return this.defaultColPersonnel.concat(this.colPersonnel);
    }
  },
  mounted() {
    // this.currentState = this.iconData[0].currentState;
    // this.timeoutState = this.iconData[0].timeoutState;
    this.getType(); // 工单类型
    // this.getFindPersonnelMonitoringShowFieldData(); // 人员监控显示配置字段
    // this.APIgetMonitorAllData(); // 工单监控整合数据
  },
  beforeDestroy() {
    // 注销前
    clearInterval(this.isset);
    window.onresize = null;
  },
  deactivated() {
    // 缓存组件停用
    clearInterval(this.isset);
    window.onresize = null;
  },
  // created() {
  //   this.getType(); // 工单类型
  //   this.getFindPersonnelMonitoringShowFieldData(); // 人员监控显示配置字段
  //   this.APIgetMonitorAllData();
  // },
  activated() {
    // 缓存启用 4秒刷新
    this.isset = setInterval(() => {
      // this.getType(); // 工单类型
      // this.getFindPersonnelMonitoringShowFieldData(); // 人员监控显示配置字段
      this.APIgetMonitorAllData();
    }, 5000);
  },
  methods: {
    // 人员维度表头
    setGroupDatas(datas) {
      this.colPersonnel = datas;
    },
    // 城市code
    setCityCode(code) {
      this.cityCode = code;
      this.APIgetMonitorAllData();
    },
    // 当日创建，当月历史
    setTab(tab) {
      this.dateTimeType = tab;
      this.APIgetMonitorAllData();
    },
    // 人员维度
    handerSearchGroupName() {
      this.groupNameCache = this.groupName;
      this.APIgetMonitorAllData();
    },
    // 节点维度
    handerSearchNodeName() {
      this.nodeNameCache = this.nodeName;
      this.APIgetMonitorAllData();
    },
    // 工单类型
    changeType() {
      this.getFindPersonnelMonitoringShowFieldData(); // 人员监控显示配置字段
      this.APIgetMonitorAllData();
    },
    /*
    工单监控整合数据
    */
    APIgetMonitorAllData() {
      this.params = {
        formTypeId: this.formTypeId,
        cityCode: this.cityCode,
        dateTimeType: this.dateTimeType,
        groupName: this.groupNameCache,
        nodeName: this.nodeNameCache
      };
      getMonitorAllData(this.params).then(res => {
        const { data } = res;
        if (data) {
          this.nodeWorkDataList = data.nodeWorkDataList; // 节点维度
          this.userGroupWorkDataList = data.userGroupWorkDataList; // 人员维度

          let reportDate = {};
          this.nodeWorkDataList.forEach((item, index) => {
            reportDate = {};
            this.nodeWorkDataList[index] = Object.assign(
              {},
              this.nodeWorkDataList[index],
              {
                reportDate: index + 1
              }
            );
          });
          this.userGroupWorkDataList.forEach((item, index) => {
            reportDate = {};
            this.userGroupWorkDataList[index] = Object.assign(
              {},
              this.userGroupWorkDataList[index],
              {
                reportDate: index + 1
              }
            );
          });
          // 初始话渠道监控 全国当日工单统计
          this.$refs.orderMonitor.initChart(
            data.cityWorkDataList,
            this.formTypeId
          );
          // 问题分类
          this.$refs.questionClassificationMonitor.initChart(
            data.questionTypeWorkDataList,
            this.formTypeId
          );
          // 工单监控
          if (data.commonStateWorkData) {
            this.$refs.statics2.setPersonCount(data.commonStateWorkData);
          }
        }
      });
    },
    /*
    人员监控显示配置字段
    */
    getFindPersonnelMonitoringShowFieldData() {
      getFindPersonnelMonitoringShowFieldData(this.formTypeId).then(res => {
        const { data } = res;
        if (data) {
          this.colPersonnel = this.employeeDatas.filter(el =>
            data.map(_el => _el.fieldKey).includes(el.index)
          );
        }
      });
    },
    /*
    获取工单类型
    */
    getType() {
      getAllFormTypeList({ status: 1 }).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        const data = res.data;
        if (data.length === 0) return;
        this.typeList = data.sort(function(a, b) {
          return parseInt(a.id) - parseInt(b.id);
        });
        this.formTypeId = this.typeList[0].id;
        this.getFindPersonnelMonitoringShowFieldData(); // 人员监控显示配置字段
        this.APIgetMonitorAllData(); // 工单监控整合数据
      });
    },
    // 重置人员维度
    resetFormGroupName() {
      this.groupName = '';
      this.groupNameCache = this.groupName;
      this.APIgetMonitorAllData();
    },
    // 重置节点维度
    resetFormNodeName() {
      this.nodeName = '';
      this.nodeNameCache = this.nodeName;
      this.APIgetMonitorAllData();
    },
    // 人员维度导出
    exportUserGroupWorkData() {
      const that = this;
      if (!that.userGroupWorkDataList || !that.userGroupWorkDataList.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      this.url = `${process.env.BASE_API}/workorder/monitor/listMonitorGroupUserWorkTotalExport?formTypeId=${that.formTypeId}&groupName=${that.groupName}`;
      if (
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
      ) {
        this.url =
          this.url +
          `&businessPartCode=${this.$store.getters.channel.businessPartCode}`;
      }
      const a = document.createElement('a');
      a.href = this.url;
      a.click();
    },
    // 节点维度导出
    exportNodeWorkData() {
      const that = this;
      if (!that.nodeWorkDataList || !that.nodeWorkDataList.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      this.url = `${process.env.BASE_API}/workorder/monitor/listMonitorNodeWorkTotalExport?formTypeId=${that.formTypeId}&nodeName=${that.nodeName}`;
      if (
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
      ) {
        this.url =
          this.url +
          `&businessPartCode=${this.$store.getters.channel.businessPartCode}`;
      }
      const a = document.createElement('a');
      a.href = this.url;
      a.click();
    }
    // loadNode(node, resolve) {
    //   if (node.level === 1) {
    //     if (this.status === 2) {
    //       resolve([]);
    //       return;
    //     } // 待领取工单不显示用户组
    //     const params = JSON.parse(JSON.stringify(this.params));
    //     this.nodeCode = params.nodeCode = node.data.nodeCode;
    //     this.nodeClass = params.nodeClass = node.data.nodeClass;
    //     getNodesUser(params).then(res => {
    //       if (res.code !== 1) {
    //         this.$XyyMessage.warning(res.msg);
    //         return;
    //       }

    //       //2020.4.13,rl,v1.7.4,新增显示待领取
    //       const arr = res.data.filter(item => {
    //         //return item.userGroupCode && item.total != 0;
    //         //超时订单、即将超时订单，显示待领取数量
    //         if ([1, 2].includes(this.timeoutState)) {
    //           return (
    //             (item.userGroupCode && item.total != 0) ||
    //             (item.unclaimedCount && Number(item.unclaimedCount) > 0)
    //           );
    //         } else {
    //           return item.userGroupCode && item.total != 0;
    //         }
    //       });

    //       //resolve(arr);
    //       setTimeout(() => {
    //         arr.forEach(item => {
    //           if (item.unclaimedCount) {
    //             item.leaf = true;
    //           }
    //         });
    //         resolve(arr);
    //       }, 500);
    //     });
    //   }
    //   if (node.level === 2) {
    //     const params = JSON.parse(JSON.stringify(this.params));
    //     params.groupCode = node.data.userGroupCode;
    //     params.nodeClass = this.nodeClass;
    //     params.nodeCode = this.nodeCode;
    //     getNodesOrder(params).then(res => {
    //       if (res.code !== 1) {
    //         this.$XyyMessage.warning(res.msg);
    //         return;
    //       }
    //       const dataArr = res.data.filter(item => {
    //         item.leaf = true;
    //         return item.processorName && item.total != 0;
    //       });
    //       dataArr.length === 0 ? resolve([]) : resolve(dataArr);
    //     });
    //   }
    //   if (node.level === 3) {
    //     this.nodeClass = null;
    //     this.nodeCode = null;
    //     resolve([]);
    //   }
    // },
    // refresh() {
    //   // 保留当前状态刷新数据
    //   this.params = {
    //     formTypeId: this.formTypeId,
    //     cityCode: this.cityCode,
    //     dateTimeType: this.dateTimeType,
    //     groupName: this.groupName,
    //     nodeName: this.nodeName
    //   };
    //   this.nodeClass = null;
    //   this.nodeCode = null;
    //   this.getTotal();
    // },

    // getNodeTotal() {
    //   setTimeout(_ => {
    //     this.closeLoading(this.loading);
    //   }, 200);
    //   getNodeTotal(this.params).then(res => {
    //     if (res.code !== 1) {
    //       this.nodeTotal = [];
    //       this.$XyyMessage.warning(res.msg);
    //       return;
    //     }
    //     if (this.status === 2 && res.data) {
    //       res.data.forEach(item => {
    //         item.leaf = true;
    //       });
    //     }
    //     this.nodeTotal = JSON.parse(JSON.stringify(res.data));
    //   });
    // },
    // getStateTotal() {
    //   getStateTotal(this.params).then(res => {
    //     if (res.code !== 1) {
    //       this.$XyyMessage.warning(res.msg);
    //       return;
    //     }
    //     this.total = res.data;
    //     const {
    //       processingWorkorder,
    //       unclaimedWorkorder,
    //       timeOutWorkorder,
    //       soonTimeoutWorkorder
    //     } = this.total;
    //     // this.iconData[0].num = processingWorkorder;
    //     // this.iconData[1].num = unclaimedWorkorder;
    //     // this.iconData[2].num = timeOutWorkorder;
    //     // this.iconData[3].num = soonTimeoutWorkorder;

    //     this.iconData[0].num = timeOutWorkorder;
    //     this.iconData[1].num = soonTimeoutWorkorder;
    //     this.iconData[2].num = unclaimedWorkorder;
    //     this.iconData[3].num = processingWorkorder;
    //   });
    // },
    // getTotal() {
    //   // this.loading = this.openLoading();
    //   // this.getNodeTotal();
    //   // this.getStateTotal();
    // },
    // changeStatus(index, item) {
    //   this.status = index;
    //   this.currentState = item.currentState;
    //   this.timeoutState = item.timeoutState;
    //   this.params = {
    //     formTypeId: this.formTypeId,
    //     cityCode: this.cityCode,
    //     dateTimeType: this.dateTimeType,
    //     groupName: this.groupName,
    //     nodeName: this.nodeName
    //   };
    //   this.getTotal();
    // },
    // handleNodeClick(data) {
    //   this.currentNode = data.nodeCode
    //     ? data.nodeCode
    //     : data.userGroupCode
    //     ? data.userGroupCode
    //     : data.processorId;
    // }
  }
};
</script>
<style>
.total input {
  height: 36px !important;
  line-height: 36px !important;
}
.el-tree-node__content {
  display: block\9 !important ;
  width: 100%\9 !important ;
  height: 21px\9;
  clear: both\9;
}
.el-tree-node__expand-icon {
  display: block\9 !important;
  width: 30px\9;
  height: 21px\9;
  float: left\9;
}
</style>
<style lang="scss" scoped>
@import '@/styles/variables.scss';
@mixin space {
  padding-left: 20px;
  padding-right: 20px;
}

.page-content {
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: rgba(87, 87, 102, 1);
  background: #f0f2f5 !important;

  .box-card {
    min-width: 1080px !important;
    overflow: auto !important;
  }

  .herader-title {
    font-size: 16px;
    color: #292933;
    background: #fff;
    margin-bottom: 20px;
  }

  .searchCondition.is-plain {
    background: rgba(59, 149, 168, 1);
    color: #fff;
  }
  /deep/.searchCondition.is-plain:hover {
    background: rgba(40, 126, 144, 1);
  }

  .m-b {
    margin-bottom: 15px;
    .row-bg button {
      width: 70px;
      height: 36px;
      line-height: 36px;
      padding-top: 0;
      padding-bottom: 0;
    }
  }

  .m-b:last-of-type {
    margin-bottom: 0;
  }
}
</style>
