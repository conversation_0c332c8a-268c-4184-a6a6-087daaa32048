<template>
  <div>
    <el-dialog :visible="status" title="导入" custom-class="im-import-box" top="0" @close="close">
      <el-upload :show-file-list="false" :http-request="upload" action>
        <el-button plain>{{ fileName?'重新上传':'上传文件' }}</el-button>
      </el-upload>
      <div class="file-name">{{ fileName }}</div>
      <div class="import-msg">
        <blockquote>提示</blockquote>
        <blockquote>1.预导入内容必须放在excel文件的第一个sheet页；</blockquote>
        <blockquote>2.表头内容及位置：所属分类（第一行，第一列）、排序（第一行，第二列）、问题描述（第一行，第三列）、问题答案（第一行，第四列）；</blockquote>
        <blockquote>3.表格数据的格式要求：所有表格内数据均为文本格式，表格中所有数据中间不允许有空行；</blockquote>
        <blockquote>4.所属分类，问题描述和问题答案列数据不允许为空；排序列书写内容格式为数字格式；</blockquote>
        <blockquote>5.标准excel格式，尾缀 .xls。</blockquote>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button plain @click="close">关闭</el-button>
        <!-- <el-button type="primary" @click="save">确定</el-button> -->
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { upload } from '@/api/im-config/quick-reply';
export default {
  props: {
    status: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileName: ''
    };
  },
  methods: {
    close() {
      this.$emit('update:status', false);
    },
    // save() {
    //   this.$emit('importCallback');
    // },
    upload(obj) {
      upload({ uploadFile: obj.file })
        .then(res => {
          if (res.code === 1) {
            this.handleSuccess(res.data, obj.file);
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },
    handleSuccess(data, file) {
      this.fileName = file.name;
      if (data.m === 0) {
        this.$XyyMessage.success('导入成功');
        this.close();
      } else {
        this.close();
        let msg = `<p>部分数据未导入成功！出错行号及原因如下：</p>`;
        for (let key in data.failReason) {
          msg += `<p>行${key}导入失败，原因：${data.failReason[key]}</p>`;
        }
        this.$XyyMsg({
          title: '提示',
          closeBtn: false,
          content: msg,
          onSuccess: () => {}
        });
      }
      this.$emit('importCallback');
    }
  }
};
</script>

<style lang="scss">
.el-dialog.im-import-box {
  width: 400px;
  top: 50%;
  transform: translateY(-50%);
  /deep/.el-dialog__header {
    padding: 15px 20px;
    position: relative;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: rgba(41, 41, 51, 1);
    border-bottom: 1px solid #f1f1f5;
    .el-dialog__headerbtn {
      top: 19px;
    }
  }
  .el-dialog__body {
    padding: 20px;
    .el-upload {
      position: relative;
      left: 50%;
      transform: translateX(-50%);
      margin-bottom: 12px;
      .el-button {
        height: 36px;
        line-height: 36px;
        padding: 0 12px;
        border: 1px solid rgba(59, 149, 168, 1);
        color: #3b95a8;
        border-radius: 2px;
      }
    }
    .file-name {
      height: 20px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .import-msg {
      margin-top: 10px;
      line-height: 20px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(153, 153, 153, 1);
      blockquote {
        margin: 0;
      }
    }
  }
  .el-dialog__footer {
    height: 56px;
    padding: 0 20px;
    box-sizing: border-box;
    .el-button {
      padding: 0 20px;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      font-weight: 400;
      font-family: PingFangSC-Regular, PingFang SC;
      &.el-button--default {
        border: 1px solid rgba(228, 228, 235, 1);
        color: rgba(87, 87, 102, 1);
        &:hover,
        &:focus {
          border: 1px solid rgba(228, 228, 235, 1);
          color: rgba(87, 87, 102, 1);
        }
      }
    }
  }
}
</style>
