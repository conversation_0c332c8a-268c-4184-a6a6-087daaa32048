<template>
  <!-- <el-dialog :visible.sync="dialogTableVisible" :before-close="beforeClose" :append-to-body="true"> -->
  <div class="bigScrell" :hidden="dialogTableVisible" @click="hiddenview()">
    <div class="bigchild">
      <img style="object-fit:contain; max-width:1200px; max-height:600px;" :src="imgdata" alt />
    </div>
  </div>
  <!-- </el-dialog> -->
</template>

<script>
export default {
  data() {
    return {
      dialogTableVisible: true,
      imgdata: ''
    };
  },
  methods: {
    openView(imgurl) {
      this.imgdata = imgurl;
      this.dialogTableVisible = false;
    },
    hiddenview() {
      this.dialogTableVisible = true;
    },
    beforeClose() {
      this.dialogTableVisible = true;
    }
  }
};
</script>

<style scoped>
.bigScrell {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgb(0, 0, 0, 0.5);
  z-index: 999999;
}

.bigchild {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.el-dialog__body {
  padding: 30px 20px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>