<template>
  <div class="description-icon">
    <el-popover
      :popper-class="`m-pop ${infoClass}`"
      placement="bottom"
      trigger="hover"
      effect="light"
    >
      <div v-for="(item,index) in info" :key="index">
        <header>{{ item.title }}</header>
        <p @click="callback" v-html="item.info"></p>
      </div>
      <div slot="reference">
        <svg-icon class="icon-info" icon-class="info"></svg-icon>
      </div>
    </el-popover>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Array,
      default: () => []
    },
    infoClass: {
      type: String,
      default: ''
    }
  },
  methods: {
    callback(e) {
      if (e.target.tagName.toLowerCase() === 'a') {
        this.$emit('callback');
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.description-icon {
  display: inline-block;
}
</style>
