<template>
  <xyy-list-page>
    <template slot="header">
      <el-form :inline="true" class="search-box">
        <el-form-item label="分组名称">
          <el-input v-model="listQuery.groupName"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList(listQuery)">查询</el-button>
        </el-form-item>
      </el-form>
      <div class="btn-box">
        <xyy-button icon-class="btn-add" @click="toEdit">新建员工组</xyy-button>
      </div>
    </template>
    <template slot="body">
      <xyy-table
        :data="list"
        :list-query="listQuery"
        :col="col"
        :operation="operation"
        :has-index="true"
        @get-data="getList"
        @operation-click="operationClick"
      ></xyy-table>
    </template>
  </xyy-list-page>
</template>

<script>
import { getGroupList, delGroupData } from '@/api/im-config/employees';
export default {
  name: 'compEmployeeList',
  data() {
    return {
      list: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0,
        groupName: '',
      },
      col: [
        { index: 'groupName', name: '名称', ellipsis: true },
        { index: 'groupTotalSize', name: '员工数' },
        { index: 'modifyName', name: '最近修改人', ellipsis: true },
        { index: 'modifyTime', name: '最近修改时间' },
        { index: 'operation', name: '操作', width: 150, operation: true },
      ],
      operation: [
        {
          name: '编辑',
          type: 0,
        },
        {
          name: '删除',
          type: 1,
        },
      ],
    };
  },
  activated() {
    this.getList(this.listQuery);
  },
  methods: {
    /**
     * 跳转到模板编辑页
     */
    toEdit(data) {
      if (data) {
        this.$router.replace({
          path: `/chat/employeeEdit/${data.id}`,
          query: { id: data.id },
        });
      } else {
        this.$router.replace({
          path: `/chat/employeeEdit/${new Date().getTime()}`,
        });
      }
    },
    getList: function (listQuery) {
      const { page, pageSize, groupName } = listQuery;
      getGroupList({ page, size: pageSize, groupName })
        .then((res) => {
          if (res.code === 1) {
            const { records, total } = res.data;
            this.list = records;
            this.listQuery = {
              ...this.listQuery,
              total: Number(total),
            };
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },
    operationClick: function (type, row) {
      switch (type) {
        case 0:
          this.toEdit(row);
          break;
        case 1:
          this.$XyyMsg({
            title: '提示',
            content: '员工组删除后地域组中的此员工组也将被移除，请谨慎操作',
            onSuccess: () => {
              this.delData(row);
            },
          });
          break;
      }
    },
    /**
     * 删除模板数据
     */
    delData(data) {
      delGroupData({ id: data.id }).then((res) => {
        if (res.code === 1) {
          this.$XyyMessage.success('删除成功');
          // 刷新列表
          this.getList(this.listQuery);
        } else {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: res.msg, // html代码串
            onSuccess: () => {},
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.search-box {
  border-bottom: 1px dashed #e4e4eb;
  padding-bottom: 20px;
  /deep/.el-form-item {
    margin-right: 18px;
    margin-bottom: 0;
    /deep/.el-form-item__label {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(41, 41, 51, 1);
      padding-right: 6px;
    }

    .el-input {
      .el-input__inner {
        height: 36px;
        line-height: 36px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
      }
    }

    button {
      width: 68px;
      height: 36px;
      line-height: 36px;
      padding: 0;
      &.el-button--default {
        border: 1px solid rgba(228, 228, 235, 1);
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
        &:hover,
        &:focus {
          border: 1px solid rgba(228, 228, 235, 1);
          background: #fff;
          color: rgba(87, 87, 102, 1);
        }
      }
    }
  }
}
.btn-box {
  padding-top: 20px;
}
.operation-box {
  width: auto;
  white-space: nowrap;
  letter-spacing: 0;
  margin: 0 -10px;
  .el-button {
    position: relative;
    margin: 0 10px;
  }
  .el-button::before {
    position: absolute;
    top: 14px;
    right: -10px;
    content: '';
    display: block;
    width: 1px;
    height: 12px;
    background: #dcdfe6;
  }
  .el-button:first-child {
    margin-left: 0;
  }
  .el-button:last-child {
    margin-right: 0;
  }
  .el-button:last-child::before {
    display: none;
  }
}
</style>
