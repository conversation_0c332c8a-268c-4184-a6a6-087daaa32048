<template>
  <el-button :type="type" plain @click="handleClick">
    <svg v-if="iconClass" :class="svgClass" aria-hidden="true" v-on="$listeners">
      <use :xlink:href="iconName" />
      <title>{{ title }}</title>
    </svg>
    <slot></slot>
  </el-button>
</template>

<script>
export default {
  name: 'XyyButton',
  props: {
    iconClass: {
      type: String,
      default: ''
    },
    className: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'primary'
    },
    title: {
      type: String,
      default: ''
    }
  },
  computed: {
    iconName() {
      return `#icon-${this.iconClass}`;
    },
    svgName() {
      return this.className
        ? this.className
        : this.iconClass
          ? `${this.iconClass}-icon`
          : '';
    },
    svgClass() {
      if (this.svgName) {
        return 'svg-icon ' + this.svgName;
      } else {
        return 'svg-icon';
      }
    }
  },
  methods: {
    handleClick: function(params) {
      this.$emit('click');
    }
  }
};
</script>

<style scoped lang="scss">
.svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
  margin-right: 4px;
}

.el-button.el-button--primary.is-plain {
  padding: 8px 12px;
  height: 36px;
  //line-height: 36px;
  border-radius: 2px;
  border: 1px solid #3b95a8;
  color: #40a3b8;
  background: #fff;

  &:hover,
  &:focus {
    background: #3b95a8;
    border-color: #3b95a8;
    color: #ffffff;
  }
}

.el-button.el-button--normal.is-plain {
  padding: 8px 12px;
  height: 36px;
  border-radius: 2px;
  &:hover,
  &:focus {
    background: #fff;
    border-color: #dcdfe6;
    color: #606266;
  }
}
</style>
