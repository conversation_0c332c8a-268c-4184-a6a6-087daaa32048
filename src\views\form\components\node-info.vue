<template>
  <div class="node-content">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      :hide-required-asterisk="true"
      :disabled="readOnly"
      label-width="80px"
    >
      <div class="form-item">
        <!-- 节点名称 start-->
        <el-form-item label="节点名称" prop="nodeName">
          <el-input
            v-if="form.nodeClass===0"
            v-model.trim="form.nodeName"
            :disabled="editable"
            maxlength="20"
          />
          <span v-else>{{ form.nodeName }}</span>
        </el-form-item>
        <!-- 节点名称 end-->

        <!-- 节点类型 start-->
        <el-form-item label="节点类型">
          <span>{{ form.nodeType | filterNodeType }}</span>
        </el-form-item>
        <!-- 节点类型 end-->

        <!-- 关闭方式 start -->
        <el-form-item v-if="form.nodeType === 3" label="关闭方式">
          <el-radio-group v-if="form.nodeClass===0" v-model="form.closeWay" :disabled="editable">
            <el-radio :label="0">人工手动关闭</el-radio>
            <el-radio :label="1">系统自动关闭</el-radio>
          </el-radio-group>
          <span v-else>{{ form.closeWay===0?'人工手动关闭':'系统自动关闭' }}</span>
        </el-form-item>
        <!-- 关闭方式 end-->

        <!-- 参与人 start-->
        <el-form-item v-if="form.closeWay !== 1" label="参与人">
          <div v-if="form.nodeClass===0" :class="{'is-disabled':readOnly}" class="tag-user-box">
            <el-tag
              v-for="(user, index) in form.nodeUserList"
              :key="user.userGroupCode"
              :disable-transitions="false"
              :closable="!editable && !readOnly"
              @close="delUser(index)"
            >{{ user.userGroupName }}</el-tag>
            <el-button
              :disabled="editable"
              class="form-btn"
              type
              icon="el-icon-plus"
              size="small"
              @click="openUserBox"
            >添加</el-button>
          </div>
          <div v-else class="user-group-box">{{ userGroupNames }}</div>
          <div v-if="form.nodeType === 0" class="link-info">
            注：发起工单时选择问题分类对应不同的模板供用户填写，规则在
            <el-button v-if="!readOnly" type="text" @click="toQuestion">问题分类-模板配置</el-button>
            <a v-else class="btn-template" @click="toQuestion">问题分类-模板配置</a>&nbsp;中设置
          </div>
          <div v-if="form.nodeType === 1">
            <el-checkbox
              :disabled="editable || form.nodeClass===1"
              v-model="form.responsibly"
              :true-label="1"
              :false-label="0"
            >处理人全程负责（流程多次流转到本节点，系统指给本节点第一次处理的员工，无需领取）</el-checkbox>
          </div>
          <div v-if="form.nodeType === 3">
            <el-checkbox
              :disabled="editable || form.nodeClass===1"
              v-model="form.closeBy"
              :true-label="1"
              :false-label="0"
            >由所选用户组中参与流程的参与人关闭，而非整个用户组都有权限关闭</el-checkbox>
          </div>
        </el-form-item>
        <!-- 参与人 end-->

        <!-- 处理时限 start -->
        <el-form-item
          v-if="(form.nodeType === 1 || form.nodeType === 3) && form.closeWay !== 1"
          label="处理时限"
        >
          <el-select
            v-if="form.nodeClass===0"
            v-model="form.processTimeout"
            :disabled="editable"
            placeholder="请选择时间"
          >
            <el-option :value="0" label="请选择时间" />
            <el-option value="1" label="1" />
            <el-option label="2" value="2" />
            <el-option label="4" value="4" />
            <el-option label="6" value="6" />
            <el-option label="8" value="8" />
            <el-option label="12" value="12" />
            <el-option label="24" value="24" />
            <el-option label="36" value="36" />
            <el-option label="48" value="48" />
            <el-option label="72" value="72" />
          </el-select>
          <span v-else>{{ form.processTimeout }}</span>
          <span>小时</span>
        </el-form-item>
        <!-- 处理时限 end -->

        <!-- 选择模板 start-->
        <el-form-item v-if="form.nodeType !== 2 && form.closeWay !== 1" label="选择模板">
          <el-select
            v-if="form.nodeClass===0"
            :disabled="editable"
            v-model="form.templateCode"
            filterable
            popper-class="select-class-option"
            placeholder="请选择选择模板"
            @change="getFieldList($event,true)"
          >
            <el-option
              v-for="item in templateList"
              :key="item.id"
              :label="item.name"
              :value="item.versionCode"
            />
          </el-select>
          <span v-else>{{ templateFormat }}</span>
          <el-button
            v-if="form.templateCode && !readOnly"
            class="btn-template"
            type="text"
            @click="openPreview"
          >预览模板</el-button>
          <a v-if="form.templateCode && readOnly" class="btn-template" @click="openPreview">预览模板</a>
        </el-form-item>
        <!-- 选择模板 end-->

        <!-- 下一节点 start-->
        <el-form-item v-if="form.nodeType !== 3" label="下一节点">
          <el-radio v-model="form.toNext" :label="0" :disabled="editable">流转到一个节点</el-radio>
          <el-radio
            v-if="form.nodeType === 0||form.nodeType === 1"
            :disabled="editable"
            v-model="form.toNext"
            :label="1"
            @change="handleToMultiples"
          >流转到多个节点</el-radio>

          <div class="node-condition">
            <!-- 一个节点 -->
            <div v-if="form.toNext === 0" class="node-item">
              <el-form
                v-for="(i, index) in form.nodeRelationList"
                :disabled="readOnly"
                :key="index"
                :inline="true"
              >
                <el-form-item v-if=" i.nodeConditionType === 1" label="节点">
                  <el-select
                    v-model="i.nextNodeId"
                    :disabled="editable"
                    popper-class="select-class-option"
                    placeholder="选择节点/搜索节点名称"
                    filterable
                  >
                    <!-- <el-option value="0" label="请选择节点" /> -->
                    <el-option
                      v-for="item in nodeList"
                      :key="item.id"
                      :label="item.nodeName"
                      :value="item.id"
                    />
                  </el-select>
                  <description key="help" :info="info"></description>
                </el-form-item>
              </el-form>
            </div>

            <!-- 多个节点 -->
            <div v-if="form.toNext ===1">
              <div v-for="(i, index) in form.nodeRelationList" :key="index" class="node-item">
                <pre></pre>
                <!-- 普通节点 -->
                <div v-if="i.nodeConditionType == 0">
                  <span class="numbers-icon">{{ index+1 }}</span>
                  <!-- 节点 -->
                  <div class="node-card">
                    <el-form :inline="true" :disabled="readOnly">
                      <el-form-item label="节点">
                        <el-select
                          v-model="i.nextNodeId"
                          :disabled="editable"
                          popper-class="select-class-option"
                          placeholder="选择节点/搜索节点名称"
                          filterable
                        >
                          <!-- <el-option value="0" label="请选择节点" /> -->
                          <el-option
                            v-for="item in nodeList"
                            :key="item.id"
                            :label="item.nodeName"
                            :value="item.id"
                          />
                        </el-select>
                        <description :info="info" :key="'help'+i.nodeConditionType+index"></description>
                      </el-form-item>
                    </el-form>
                    <el-tooltip v-if="index > 0" effect="light" content="删除节点" placement="bottom">
                      <i class="el-icon-minus first" @click="delNode(index)"></i>
                    </el-tooltip>
                  </div>
                  <!-- 条件 -->
                  <div v-for="(o, ind) in i.nodeConditionList" :key="ind" class="node-card">
                    <el-form :inline="true" :disabled="readOnly">
                      <el-form-item label="条件">
                        <el-select
                          v-model="o.conditionFieldCode"
                          :disabled="editable"
                          placeholder="选择字段/搜索字段名称"
                          popper-class="select-class-option"
                          @focus="validateTemplate"
                          @change="handleConditionFieldChange($event,index, ind)"
                        >
                          <el-option
                            v-for="item in conditionFields"
                            :label="item.fieldText"
                            :value="item.fieldCode"
                            :key="item.fieldCode"
                            :disabled="item.disabled"
                          />
                        </el-select>
                      </el-form-item>
                      <el-form-item label>
                        <el-select
                          v-model="o.conditionSymbol"
                          :disabled="editable"
                          placeholder="判断方式"
                        >
                          <el-option
                            v-for="item in filterSymbolList(o.fieldType)"
                            :label="item.label"
                            :value="item.value"
                            :key="item.value"
                          />
                        </el-select>
                      </el-form-item>
                      <el-form-item label>
                        <!-- 表单组件 -->
                        <input-model
                          v-if="o.conditionFieldCode"
                          :ref="`preview${index}${ind}`"
                          :key="`preview${index}${ind}`"
                          :keys="o.fieldType"
                          :preview="o"
                          :read="editable || readOnly"
                          :symbol="Number(o.conditionSymbol)"
                          :class="{'is-disabled':editable}"
                        />
                      </el-form-item>
                    </el-form>
                    <el-tooltip v-if="ind > 0" effect="light" content="删除条件" placement="bottom">
                      <i class="el-icon-minus" @click="delCondition(ind, index)"></i>
                    </el-tooltip>
                  </div>
                  <el-button :disabled="editable" type="text" @click="addCondition(index)">
                    <i class="el-icon-plus"></i>添加条件
                  </el-button>
                </div>
                <!-- 其他节点 -->
                <div v-if="i.nodeConditionType === 1">
                  <span class="numbers-icon">{{ index+1 }}</span>
                  <div>
                    <el-form :inline="false" :disabled="readOnly">
                      <el-form-item label="节点">
                        <el-select
                          v-model="i.nextNodeId"
                          :disabled="editable"
                          popper-class="select-class-option"
                          placeholder="请选择节点/搜索节点名称"
                          filterable
                        >
                          <!-- <el-option value="0" label="请选择节点" /> -->
                          <el-option
                            v-for="item in nodeList"
                            :key="item.id"
                            :label="item.nodeName"
                            :value="item.id"
                          />
                        </el-select>
                        <description :info="info" :key="'help'+i.nodeConditionType"></description>
                      </el-form-item>
                      <el-form-item label="条件">
                        <span>其他（当不满足上述条件时）</span>
                      </el-form-item>
                    </el-form>
                  </div>
                </div>
              </div>
            </div>

            <!-- 添加节点 -->
            <div v-if="form.toNext === 1" class="node-item">
              <div class="add-node">
                <el-button
                  :disabled="editable"
                  type="text"
                  @click="checkTimer(addConditionNode,'timer')()"
                >
                  <i class="el-icon-plus"></i>添加节点
                </el-button>
              </div>
            </div>
          </div>
        </el-form-item>
        <!-- 下一节点 start-->

        <!-- 操作按钮 start -->
        <el-form-item>
          <el-button
            v-if="!editable"
            class="save-btn"
            plain
            @click="checkTimer(saveNode,'timer1')()"
          >保存</el-button>
          <el-button
            v-if="editable && !(form.nodeType===3 && form.nodeClass===1)"
            type="primary"
            @click="editable = !editable"
          >编辑</el-button>
          <el-button v-if="!editable" @click="cancel">取消</el-button>
        </el-form-item>
        <!-- 操作按钮 end -->
      </div>
    </el-form>

    <!-- 参与人选择弹窗 start -->
    <el-dialog :visible.sync="userListVisible" custom-class="user-group-box" title="选择用户组" top="0">
      <div class="user-content">
        <div class="user-list">
          <div class="search-box">
            <el-input
              v-model="searchKey"
              placeholder="搜索用户组名称"
              maxlength="20"
              suffix-icon="el-icon-search"
            />
          </div>
          <div class="main-box">
            <el-checkbox-group v-if="filterUserList.length" v-model="checkedUser">
              <div v-for="(item, index) in filterUserList" :key="item.id" class="checkBox">
                <div style="width:25px;display:inline-block">
                  <el-checkbox
                    :key="item.id"
                    :label="item.versionCode"
                    @change="getUserInfo($event, item.id, index)"
                  >&nbsp;</el-checkbox>
                </div>

                <span
                  :key="item.userGroupName"
                  :class="{active: item.active}"
                  style="font-size:14px;cursor: pointer;"
                  @click="getUserInfo($event, item.id, index)"
                >{{ item.userGroupName }}</span>
              </div>
            </el-checkbox-group>
            <span v-else class="empty-result">暂未找到相关用户组</span>
          </div>
        </div>
        <div class="user-info">
          <div class="title-box">组内成员信息</div>
          <el-row>
            <!-- <el-col :span="24" style="font-size:14px;font-weight:600">成员信息</el-col> -->
            <div class="main-box">
              <el-col
                v-for="item in checkedUserInfoList"
                :key="item.userId"
                :span="8"
              >{{ item.nickname }}</el-col>
            </div>
          </el-row>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="userListVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmAddUser">确 定</el-button>
      </div>
    </el-dialog>
    <!--  参与人选择弹窗 end -->
    <form-view ref="formView" :list="previewList" :lay-datas="layDatas" title="预览模板" />
  </div>
</template>

<script>
import {
  getListTemplate,
  getUserList,
  getUserInfo,
  updateNode,
  getListNodesByFormId,
  getListNodesByFormIdExcludeCurrentNode,
  getTemplateFields,
  getOtherTemplateFields
} from '@/api/formManage';
import utils from '@/utils/form';
import inputModel from './form-type';
import formView from './template-preview';
import description from '@/components/tools/description.vue';
export default {
  name: 'NodeInfo',
  components: { inputModel, formView, description },
  filters: {
    filterNodeType: function(value) {
      const map = {
        '0': '发起节点',
        '1': '处理节点',
        '2': '抄送节点',
        '3': '关闭节点'
      };
      return map[value];
    },
    parseFiledType: function(value) {
      const map = {
        '0': '单行输入',
        '1': '多行输入',
        '2': '下拉',
        '3': '单选',
        '4': '多选',
        '5': '日期',
        '6': '级联',
        '7': '省市区',
        '8': '电话',
        '9': '邮箱',
        '10': '附件'
      };
      return map[value];
    }
  },
  props: {
    form: {
      type: Object,
      default: () => {}
    },
    formId: {
      type: [String, Number, Boolean],
      required: true
    }
  },
  data() {
    return {
      info: [
        {
          title: '',
          info:
            '节点来源于当前页面左侧节点列表，绑定下一节点前请点击“<i class="el-icon-plus"></i>”,先创建节点'
        }
      ],
      timer: null,
      timer1: null,
      editable: true, // 操作按钮：0保存 1编辑
      userListVisible: false,
      rules: {
        nodeName: [
          { required: true, message: '请输入节点名称', trigger: 'blur' },
          { max: 20, message: ' 20字以内', trigger: 'blur' }
        ]
      },
      nodeUserList: [],
      checkedUser: [],
      checkedUserInfoList: [],
      previewList: [], // 预览列表
      templateList: [], // 模板列表
      nodeList: [], // 当前节点外的节点集合
      fieldList: [], // 字段集合（包含类型：下拉、单选、多选）
      nextNodeId: '', // 下一节点id,下一步为一个节点时有效
      defaultNodeRelation:
        // 页面节点关系
        {
          formId: this.formId, // 表单id
          nextNodeId: '', // 下一节点id
          nodeId: this.form.id, // 节点id
          nodeConditionType: 1, // 节点类型： 0-有条件的节点 1-其他节点
          nodeConditionList: [
            {
              conditionFieldCode: '', // 条件字段id
              conditionSymbol: '', // 条件符号
              valueFirst: '', // 第一个值
              fieldType: -1, // 表单组件类型
              defaultValue: '', // 表单组件默认值
              optionSettings: null, // 选项设置
              limitNumberCharacters: 1000 // 限制字符数
            }
          ] // 节点跳转关系条件
        },
      oldUserList: [], // 旧用户组数据
      oldRelationList: [], // 旧节点关系数据
      searchKey: '',
      layDatas: '', // 布局数据
      readOnly: false // 只读状态
    };
  },
  computed: {
    filterUserList() {
      return this.nodeUserList.filter(
        el =>
          this.formatStr(el.userGroupName).search(
            this.formatStr(this.searchKey)
          ) >= 0,
        this
      );
    },
    conditionSymbolList() {
      return utils.conditionSymbolList;
    },
    conditionFields() {
      return this.fieldList.filter(el =>
        [0, 1, 2, 3, 4, 6].includes(el.fieldType)
      );
    },
    templateFormat() {
      if (this.form.templateCode && this.templateList.length) {
        const arr = this.templateList.filter(
          el => el.versionCode === this.form.templateCode
        );
        if (arr.length) {
          return arr[0].name;
        } else {
          return '';
        }
      } else {
        return '';
      }
    },
    userGroupNames() {
      return this.form.nodeUserList && this.form.nodeUserList.length
        ? this.form.nodeUserList.map(el => el.userGroupName).join('/')
        : '';
    }
  },
  mounted() {
    if (!Object.keys(this.form).length) return false;
    this.editable = this.form.editable;
    this.getListTemplate(); // 获取模板列表
    this.getListNodesByFormIdExcludeCurrentNode(); // 获取除当前节点外的其他节点集合
    this.getFieldList(this.form.templateCode);
    this.oldUserList = JSON.parse(JSON.stringify(this.form.nodeUserList));
    this.oldRelationList = JSON.parse(
      JSON.stringify(this.form.nodeRelationList)
    );
    this.readOnly = this.$route.query.readOnly
      ? Boolean(this.$route.query.readOnly)
      : false;
  },
  methods: {
    formatStr(val) {
      val = val.replace(/[\'\"\\\/\b\f\n\r\t]/g, '');
      val = val.replace(/[\@\#\$\%\^\&\*\(\)\{\}\:\"\L\<\>\?\[\]]/g, '');
      return val;
    },
    toQuestion() {
      this.$router.push({
        path: `/worksheet/configuration`
      });
    },
    openUserBox() {
      this.userListVisible = true;
      this.initUserList();
    },
    initUserList(cb) {
      this.searchKey = '';
      this.getUserList(list => {
        this.checkedUserInfoList = this.checkedUserInfoList.filter(el =>
          list.map(_el => _el.id).includes(el)
        );
        this.form.nodeUserList = this.form.nodeUserList.filter(el =>
          list.map(_el => _el.versionCode).includes(el.userGroupCode)
        );
        this.checkedUser = this.form.nodeUserList.map(el => el.userGroupCode);
        if (cb) {
          cb();
        }
      });
    },
    filterSymbolList(type) {
      if ([0, 1].includes(type)) {
        return this.conditionSymbolList.filter(el => el.value === 2);
      } else if ([2, 3].includes(type)) {
        return this.conditionSymbolList.filter(el =>
          [0, 1, 3].includes(el.value)
        );
      } else if (type === 4) {
        return this.conditionSymbolList.filter(el => el.value === 3);
      } else if (type === 6) {
        return this.conditionSymbolList.filter(el => [4, 5].includes(el.value));
      }
    },
    // 获取当前节点外的其他节点集合
    getListNodesByFormIdExcludeCurrentNode() {
      const [formId, nodeId] = [this.formId, this.form.id];
      if (!nodeId) {
        getListNodesByFormId(formId)
          .then(res => {
            if (res.code === 1) {
              this.nodeList = res.data;
            } else {
              this.$XyyMessage.error(res.msg);
            }
          })
          .catch(() => {});
        return false;
      }
      getListNodesByFormIdExcludeCurrentNode({ formId, nodeId })
        .then(res => {
          if (res.code === 1) {
            this.nodeList = res.data;
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },
    // 获取用户信息
    getUserList(cb) {
      getUserList({ pageSize: 1000, pageNum: 1 }).then(res => {
        if (res.code === 1) {
          this.nodeUserList = res.data.list.map(el => {
            el.active = false;
            return el;
          });
          if (cb) {
            cb(this.nodeUserList);
          }
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 获取模板列表
    getListTemplate() {
      getListTemplate({ type: this.form.nodeType === 0 ? 0 : 1 }).then(res => {
        if (res.code === 1) {
          this.templateList = res.data;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 获取字段列表
    getFieldList(templateCode, reload) {
      if (!templateCode) {
        this.fieldList = [];
        this.initNodeRelationList();
      } else {
        if (this.form.nodeType === 0) {
          getTemplateFields({ templateCode, formId: this.formId }).then(res => {
            if (res.code === 1) {
              this.fieldList = res.data;
              if (reload) {
                this.form.nodeRelationList = [];
              }
              this.initNodeRelationList();
            } else {
              this.$XyyMessage.error(res.msg);
            }
          });
        } else {
          getOtherTemplateFields({ templateCode }).then(res => {
            if (res.code === 1) {
              this.fieldList = res.data.list;
              if (reload) {
                this.form.nodeRelationList = [];
              }
              this.initNodeRelationList();
            } else {
              this.$XyyMessage.error(res.msg);
            }
          });
        }
      }
    },
    // 添加参与人
    confirmAddUser() {
      if (!this.checkedUser.length) {
        this.$XyyMessage.error('请先选择用户');
        return false;
      }
      this.userListVisible = false;
      const tem = [];
      this.nodeUserList.forEach(el => {
        if (this.checkedUser.includes(el.versionCode)) {
          tem.push({
            userGroupName: el.userGroupName,
            userGroupCode: el.versionCode,
            formId: this.formId,
            nodeId: this.form.id
          });
        }
      });
      this.form.nodeUserList = tem;
    },
    // 删除参与人
    delUser(index) {
      this.form.nodeUserList.splice(index, 1);
    },
    // 打开模板预览弹窗
    openPreview() {
      getOtherTemplateFields({ templateCode: this.form.templateCode }).then(
        res => {
          if (res.code === 1) {
            this.previewList = res.data.list;
            this.layDatas = res.data.layout;
            if (!this.layDatas && this.previewList.length) {
              const arr = [];
              for (let i = 0; i < this.previewList.length; i++) {
                arr.push([1]);
              }
              this.layDatas = arr.map(cols => cols.join('-')).join();
            }
            this.previewList.length &&
              this.previewList.forEach(el => {
                if (
                  el.optionSettings &&
                  typeof el.optionSettings === 'string'
                ) {
                  el.optionSettings = JSON.parse(
                    el.optionSettings.replace(/&quot;/g, '"')
                  );
                }
              });
            this.$refs.formView.show();
          } else {
            this.$XyyMessage.error(res.msg);
          }
        }
      );
    },
    // 条件字段类型改变
    handleConditionFieldChange(val, index, ind) {
      const arr = this.form.nodeRelationList[index].nodeConditionList;
      if (arr.some((el, i) => el.conditionFieldCode === val && i !== ind)) {
        this.$XyyMessage.error('同一节点下条件不能相同！');
        this.$set(
          this.form.nodeRelationList[index].nodeConditionList,
          ind,
          this.defaultNodeRelation.nodeConditionList[0]
        );
        this.form.nodeRelationList[index].nodeConditionList[
          ind
        ].conditionFieldCode = '';
        return;
      }

      this.setConditionData(val, index, ind);

      this.form.nodeRelationList[index].nodeConditionList[ind].conditionSymbol =
        '';
      this.$nextTick(() => {
        this.$refs['preview' + index + ind][0].resetValue(
          this.form.nodeRelationList[index].nodeConditionList[ind].fieldType
        );
      });
    },
    validateTemplate() {
      if (!this.form.templateCode) {
        this.$XyyMessage.error('请先选择模板');
        return;
      }
    },
    /**
     * 设置条件数据
     */
    setConditionData(code, index, ind) {
      const temp = JSON.parse(
        JSON.stringify(this.form.nodeRelationList[index])
      );
      temp.nodeConditionList[ind].conditionSymbol =
        Number(temp.nodeConditionList[ind].conditionSymbol) < 0
          ? ''
          : temp.nodeConditionList[ind].conditionSymbol;
      if (code) {
        let item = this.conditionFields.filter(el => el.fieldCode === code)[0];
        item = JSON.parse(JSON.stringify(item));
        if (item.optionSettings && typeof item.optionSettings === 'string') {
          item.optionSettings = JSON.parse(
            item.optionSettings.replace(/&quot;/g, '"')
          );
        }
        temp.nodeConditionList[ind].limitNumberCharacters =
          item.limitNumberCharacters;
        temp.nodeConditionList[ind].optionSettings = item.optionSettings;
        temp.nodeConditionList[ind].defaultValue = '';
        temp.nodeConditionList[ind].conditionFieldCode = item.fieldCode;
        temp.nodeConditionList[ind].fieldType = item.fieldType;
      }
      this.form.nodeRelationList.splice(index, 1, temp);
    },
    // 初始化节点关系数组
    initNodeRelationList() {
      const res = [];
      if (!this.form.nodeRelationList || !this.form.nodeRelationList.length) {
        res.push(utils.deepCopy(this.defaultNodeRelation));
        // 多个节点
        if (this.form.toNext === 1) {
          res.push(utils.deepCopy(this.defaultNodeRelation));
          res[0].nodeConditionType = 0;
          // res[1].nodeConditionType = 1;
        }
        this.form.nodeRelationList = res;
      } else {
        this.handleToMultiples(this.form.toNext);
        // 回显数据
        this.form.nodeRelationList.forEach((el, index) => {
          if (el.nodeConditionType === 0) {
            el.nodeConditionList.length &&
              el.nodeConditionList.forEach((item, ind) => {
                this.setConditionData(item.conditionFieldCode, index, ind);
                this.$nextTick(() => {
                  this.$refs['preview' + index + ind][0].initData(
                    item.fieldType,
                    item.valueFirst
                  );
                });
              }, this);
          }
        }, this);
      }
      this.$forceUpdate;
    },
    // 多个节点时添加节点对象
    handleToMultiples(val) {
      const temp = utils.deepCopy(this.defaultNodeRelation);
      if (val === 1) {
        if (
          this.form.nodeRelationList.length === 1 &&
          this.form.nodeRelationList[0].nodeConditionType === 1
        ) {
          temp.nodeConditionType = 0;
          this.form.nodeRelationList.unshift(temp);
        } else if (
          !this.form.nodeRelationList.some(el => el.nodeConditionType === 1)
        ) {
          this.form.nodeRelationList.push(temp);
        }
      } else {
        if (!this.form.nodeRelationList.length) {
          this.form.nodeRelationList.push(temp);
        }
      }
    },
    // 添加条件
    addCondition(index) {
      const tem = utils.deepCopy(this.defaultNodeRelation.nodeConditionList[0]);
      this.form.nodeRelationList[index].nodeConditionList.push(tem);
      this.$forceUpdate();
    },
    // 删除条件
    delCondition(ind, index) {
      if (this.editable) return false;
      this.form.nodeRelationList[index].nodeConditionList.splice(ind, 1);
      this.$forceUpdate();
    },
    // 添加节点
    addConditionNode() {
      const target = this.form.nodeRelationList.length - 1;
      const tem = utils.deepCopy(this.defaultNodeRelation);
      tem.nodeConditionType = 0;
      this.form.nodeRelationList.splice(target, 0, tem);
      this.$forceUpdate();
    },
    // 删除节点
    delNode(index) {
      if (this.readOnly || this.editable) return false;
      this.form.nodeRelationList.splice(index, 1);
      this.$forceUpdate();
    },
    // 获取用户组用户详情
    getUserInfo(isChecked, userGroupId, index) {
      if (isChecked) {
        this.nodeUserList.forEach((el, index) => {
          el.active = false;
          this.nodeUserList.splice(index, 1, el);
        });
        this.nodeUserList[index].active = true;
        getUserInfo(userGroupId).then(res => {
          if (res.code === 1) {
            this.checkedUserInfoList = res.data;
          } else {
            this.$XyyMessage.error(res.msg);
          }
        });
      } else {
        this.checkedUserInfoList = [];
      }
    },
    // 取消操作
    cancel() {
      this.editable = true;
    },
    // 保存节点
    saveNode(index, currentNodeId, step) {
      // 保存||编辑
      this.$refs['form'].validate(pass => {
        if (pass) {
          const formData = this.getSubmitFormData();
          if (formData.nodeRelationList && formData.nodeRelationList.length) {
            const nextIds = formData.nodeRelationList.map(el => el.nextNodeId);
            const crNextIds = Array.from(new Set(nextIds)); // 去重
            if (nextIds.length > crNextIds.length) {
              this.$XyyMessage.error('流程分支不能存在相同的下一节点，请修改');
              return;
            }
          }
          const result = utils.validateFormData(formData);
          const loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(255,255,255, 0.8)'
          });
          formData.nodeUserList = this.getUserDatas(
            this.oldUserList,
            formData.nodeUserList
          );
          if (formData.nodeRelationList && formData.nodeRelationList.length) {
            formData.nodeRelationList = this.getRelationDatas(
              this.oldRelationList,
              formData.nodeRelationList
            );
          }
          updateNode(formData).then(res => {
            loading.close();
            if (res.code === 1) {
              this.$XyyMessage.success('保存成功！');
              // 设置项完整性判断
              if (currentNodeId) res.data.currentNodeId = currentNodeId;
              this.$emit('saveDone', formData, result, index, res.data, step);
            } else {
              this.$XyyMessage.error(res.msg);
            }
          });
        }
      });
    },
    /**
     * 获取需要更新的用户组数据
     */
    getUserDatas(oldList, newList) {
      let arr = [];
      const delList = oldList
        .filter(el =>
          newList && newList.length
            ? !newList.map(_el => _el.userGroupCode).includes(el.userGroupCode)
            : true
        )
        .map(el => {
          return { id: el.id, userGroupCode: el.userGroupCode };
        });
      if (newList && newList.length) {
        arr = newList.filter(
          el =>
            !oldList.map(_el => _el.userGroupCode).includes(el.userGroupCode)
        );
      }
      arr = arr.concat(delList);
      return arr;
    },
    getRelationDatas(oldList, newList) {
      let arr = [];
      const _newList = JSON.parse(JSON.stringify(newList));
      const delList = oldList
        .filter(el => !_newList.map(_el => _el.id).includes(el.id))
        .map(el => {
          return { id: el.id, nextNodeId: el.nextNodeId };
        });
      arr = _newList
        .filter(el => {
          if (!el.id) {
            if (el.nextNodeId) {
              return true;
            } else {
              return false;
            }
          } else {
            return oldList.some(_el => {
              if (el.id === _el.id) {
                const obj = this.compareRelation(_el, el);
                if (obj.update) {
                  el.nodeConditionList = obj.condition;
                }
                return obj.update;
              } else {
                return false;
              }
            }, this);
          }
        }, this)
        .concat(delList);
      return arr;
    },
    compareRelation(oldData, newData) {
      let update = false;
      // 下一节点更新
      if (oldData.nextNodeId !== newData.nextNodeId) {
        update = true;
      }
      const oldCondition = oldData.nodeConditionList;
      const newCondition = newData.nodeConditionList;
      let arr = [];
      if (
        oldCondition &&
        oldCondition.length &&
        (!newCondition || !newCondition.length)
      ) {
        update = true;
        arr = [];
      } else if (
        newCondition &&
        newCondition.length &&
        (!oldCondition || !oldCondition.length)
      ) {
        update = true;
        arr = newCondition;
      } else if (
        newCondition &&
        newCondition.length &&
        oldCondition &&
        oldCondition.length
      ) {
        const delCondition = oldCondition
          .filter(el => !newCondition.map(_el => _el.id).includes(el.id))
          .map(el => {
            return { id: el.id };
          });
        arr = newCondition
          .filter(el => {
            if (!el.id) {
              return true;
            } else {
              return oldCondition.some(
                _el =>
                  el.id === _el.id &&
                  (el.conditionFieldCode !== _el.conditionFieldCode ||
                    el.conditionSymbol !== _el.conditionSymbol ||
                    el.valueFirst !== _el.valueFirst)
              );
            }
          })
          .concat(delCondition);
        update = update || !!arr.length;
      }
      return {
        update: update,
        condition: arr
      };
    },
    // 从表单数据中提取需提交的字段
    getUseFormData(fields, data) {
      const o = {};
      for (const k in data) {
        if (fields.hasOwnProperty(k)) {
          o[k] = data[k];
        }
      }
      return o;
    },
    // 根据节点类型组装表单数据
    getSubmitFormData() {
      const data = utils.deepCopy(this.form);
      const formMap = {
        '0': {
          // 开始节点
          id: 'id',
          formId: 'formId',
          nodeName: 'nodeName',
          nodeType: 'nodeType',
          nodeClass: 'nodeClass',
          nodeUserList: 'nodeUserList',
          templateCode: 'templateCode',
          toNext: 'toNext',
          nodeRelationList: 'nodeRelationList'
        },
        '1': {
          // 处理节点
          id: 'id',
          formId: 'formId',
          nodeName: 'nodeName',
          nodeType: 'nodeType',
          nodeClass: 'nodeClass',
          nodeUserList: 'nodeUserList',
          templateCode: 'templateCode',
          toNext: 'toNext',
          nodeRelationList: 'nodeRelationList',
          processTimeout: 'processTimeout',
          responsibly: 'responsibly'
        },
        '2': {
          // 抄送节点
          id: 'id',
          formId: 'formId',
          nodeName: 'nodeName',
          nodeType: 'nodeType',
          nodeClass: 'nodeClass',
          nodeUserList: 'nodeUserList',
          toNext: 'toNext',
          nodeRelationList: 'nodeRelationList'
        },
        '3': {
          id: 'id',
          formId: 'formId',
          nodeName: 'nodeName',
          nodeType: 'nodeType',
          nodeClass: 'nodeClass',
          nodeUserList: 'nodeUserList',
          templateCode: 'templateCode',
          toNext: 'toNext',
          processTimeout: 'processTimeout',
          closeBy: 'closeBy',
          closeWay: 'closeWay'
        } // 结束节点
      };
      const listMap = {
        nodeUserList: {
          id: 'id',
          formId: 'formId',
          nodeId: 'nodeId',
          userGroupCode: 'userGroupCode',
          userGroupName: 'userGroupName'
        },
        nodeConditionList: {
          id: 'id',
          // authId: 'authId',
          conditionFieldCode: 'conditionFieldCode',
          conditionSymbol: 'conditionSymbol',
          formId: 'formId',
          nodeId: 'nodeId',
          valueFirst: 'valueFirst'
        },
        nodeRelationList: {
          id: 'id',
          formId: 'formId',
          nodeId: 'nodeId',
          nextNodeId: 'nextNodeId',
          nodeConditionType: 'nodeConditionType',
          nodeConditionList: 'nodeConditionList'
        }
      };

      const formData = this.getUseFormData(formMap[data.nodeType], data);
      if (formData.hasOwnProperty('templateCode')) {
        if (!formData.templateCode) formData.templateCode = '';
      }
      if (formData.toNext === 0 && formData.nodeType !== 3) {
        formData.nodeRelationList = formData.nodeRelationList.filter(el => {
          return el.nodeConditionType === 1;
        });
        formData.nodeRelationList[0].nodeConditionList = [];
      }
      if (formData.nodeType === 3 && formData.closeWay === 1) {
        delete formData.nodeUserList;
        delete formData.processTimeout;
        formData.templateCode = '';
        delete formData.closeBy;
      }

      formData.nodeUserList &&
        formData.nodeUserList.forEach((el, index) => {
          formData.nodeUserList[index] = this.getUseFormData(
            listMap.nodeUserList,
            el
          );
        });

      formData.nodeRelationList &&
        formData.nodeRelationList.forEach((el, index) => {
          if (el.nodeConditionType === 1) el.nodeConditionList = [];
          formData.nodeRelationList[index] = this.getUseFormData(
            listMap.nodeRelationList,
            el
          );

          el.nodeConditionList &&
            el.nodeConditionList.forEach((item, ind) => {
              formData.nodeRelationList[index].nodeConditionList[
                ind
              ] = this.getUseFormData(listMap.nodeConditionList, item);
              formData.nodeRelationList[index].nodeConditionList[
                ind
              ].conditionType = 1;
              if (
                Array.isArray(
                  formData.nodeRelationList[index].nodeConditionList[ind]
                    .valueFirst
                )
              ) {
                formData.nodeRelationList[index].nodeConditionList[
                  ind
                ].valueFirst = formData.nodeRelationList[
                  index
                ].nodeConditionList[ind].valueFirst.join(',');
              }
            });
        });

      return formData;
    }
  }
};
</script>
<style lang="scss">
.select-class-option {
  z-index: 8 !important;
}
.el-dialog.user-group-box {
  width: 750px;
  height: 500px;
  transform: translateY(-50%);
  top: 50%;
  .el-dialog__header {
    height: 50px;
    padding: 15px 20px;
    .el-dialog__title {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(41, 41, 51, 1);
      height: 22px;
      line-height: 22px;
    }
    .el-dialog__headerbtn {
      top: 18px;
    }
  }
  .el-dialog__body {
    padding: 5px 20px 0;
    height: calc(100% - 126px);
    .user-content {
      height: 100%;
      .user-list {
        float: left;
        width: 290px;
        height: 100%;
        border: 1px solid rgba(228, 228, 235, 1);
        div.search-box {
          height: 55px;
          background: #f0f2f5;
          padding: 12px 20px 11px;
          .el-input {
            /deep/.el-input__inner {
              height: 32px;
              line-height: 32px;
            }
            /deep/.el-input__icon.el-icon-search {
              line-height: 32px;
            }
          }
        }
        div.main-box {
          padding: 20px;
          position: relative;
          height: calc(100% - 55px);
          overflow-y: auto;
          .el-checkbox {
            display: block;
            margin-bottom: 10px;
          }
          .active {
            color: #3b95a8;
          }
          .empty-result {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translateX(-50%) translateY(-50%);
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(174, 174, 191, 1);
          }
        }
      }
      .user-info {
        float: right;
        width: calc(100% - 298px);
        height: 100%;
        overflow: auto;
        border: 1px solid rgba(228, 228, 235, 1);
        div.title-box {
          height: 55px;
          line-height: 55px;
          background: rgba(240, 242, 245, 1);
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: rgba(41, 41, 51, 1);
          padding: 0 20px;
        }
        div.main-box {
          padding: 20px;
          .el-col {
            margin-bottom: 10px;
          }
        }
      }
    }
  }

  .el-dialog__footer {
    padding: 20px;
    .el-button {
      height: 36px;
      line-height: 36px;
      padding: 0 19px;
    }
  }
}
</style>
<style lang="scss" scoped>
.node-content {
  margin-left: -10px;
  margin-top: 15px;
  .form-item {
    width: 100%;
    .el-form-item {
      margin-bottom: 10px;
      /deep/.el-form-item__label {
        line-height: 36px;
        font-weight: normal;
      }
      /deep/.el-form-item__content {
        line-height: 36px;
        font-weight: normal;
        > span {
          color: #575766;
        }
        .el-input {
          width: 420px;
          /deep/.el-input__inner {
            height: 36px;
            line-height: 36px;
          }
        }
        .el-select {
          width: 420px;
          /deep/.el-input__inner {
            height: 36px;
            line-height: 36px;
          }
        }
        .tag-user-box {
          .el-tag {
            margin-right: 10px;
            height: 36px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(87, 87, 102, 1);
            background: rgba(245, 247, 250, 1);
            border-radius: 2px;
            border: 1px solid rgba(228, 228, 235, 1);
            line-height: 36px;
          }
          &.is-disabled {
            .el-tag {
              color: #c0c4cc;
            }
          }
        }

        .user-group-box {
          min-height: 36px;
          color: #575766;
        }
        .link-info {
          color: #909399;
        }
        .btn-template {
          padding: 0;
          line-height: 36px;
          border: none;
        }
        a.btn-template {
          color: #3b95a8;
        }
        .node-condition {
          margin-top: 13px;
          background: rgba(245, 247, 250, 1);
          border-radius: 2px;
          border: 1px solid rgba(228, 228, 235, 1);
          border-bottom: none;
          .el-input {
            width: 210px;
          }
          .el-select {
            width: 210px;
          }
          .node-item {
            position: relative;
            padding: 20px 5px 20px 30px;
            border-bottom: 1px solid #e4e4eb;
            .numbers-icon {
              display: inline-block;
              width: 20px;
              height: 20px;
              background: #e4e4eb;
              position: absolute;
              color: #909399;
              font-size: 12px;
              z-index: 9999999999;
              line-height: 19px;
              text-align: center;
              border-radius: 10px;
              top: 45px;
              left: 5px;
              z-index: 0;
            }
            .add-node {
              text-align: center;
              background: rgba(255, 255, 255, 1);
              border-radius: 2px;
              border: 1px dashed rgba(220, 223, 230, 1);
              height: 36px;
              box-sizing: border-box;
              .el-button--text {
                padding: 0;
                height: 100%;
                line-height: 36px;
                width: 100%;
              }
            }
            .node-card {
              position: relative;
              margin-bottom: 15px;
              .el-form {
                .el-form-item {
                  margin-right: 5px;
                }
              }
              .el-icon-minus {
                position: absolute;
                width: 16px;
                height: 16px;
                border: 1px solid rgba(59, 149, 168, 1);
                color: rgba(59, 149, 168, 1);
                top: 12px;
                right: 10px;
                cursor: pointer;
                &.first {
                  left: 300px;
                }
              }
              .preview {
                width: 210px;
                /deep/.el-form-item {
                  width: 100%;
                  margin: 0;
                  .el-form-item__content {
                    width: 100%;
                    .el-select,
                    .el-input,
                    .el-cascader {
                      width: 100%;
                    }
                    .el-input__inner {
                      min-height: 36px !important;
                      height: 36px;
                      line-height: 36px;
                    }
                  }
                }
                &.is-disabled {
                  /deep/.el-select__tags-text {
                    color: #c0c4cc;
                  }
                }
              }
            }
          }
        }
        .form-btn.el-button {
          height: 36px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(87, 87, 102, 1);
          position: relative;
          top: -1px;
          &.is-disabled {
            color: #c0c4cc;
          }
        }
      }
    }

    .el-button {
      height: 36px;
    }

    .el-button.save-btn {
      border: 1px solid #3b95a8;
      color: #3b95a8;
      &.is-disabled {
        border-color: #ebeef5;
        color: #c0c4cc;
      }
    }
  }
}
</style>

