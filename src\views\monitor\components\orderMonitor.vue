<template>
  <div class="channe-content">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span class="titlefont">全国当日工单统计</span>
        <el-button
          icon="el-icon-upload2"
          style="float: right; padding: 3px 0"
          type="text"
          @click="downOrderMonitor"
        >导出excel</el-button>
      </div>
      <div style="width:100%">
        <div id="orderMonitor" style="width: 100%;height:450px;"></div>
      </div>
    </el-card>
  </div>
</template>

<script>
import echarts from 'echarts';
export default {
  name: 'OrderMonitor',
  data() {
    return {
      DataUrl: '',
      arr: '',
      formTypeId: ''
    };
  },
  mounted() {
    // this.initChart();
  },
  methods: {
    initChart(arr, formTypeId) {
      this.arr = arr;
      this.formTypeId = formTypeId;
      if (arr === null) {
        return;
      }
      const xAxisData = [];
      const daySeriesData = [];
      const receiveSeriesData = [];

      for (const key in arr) {
        xAxisData.push(arr[key].cityName);
        daySeriesData.push(arr[key].createWorkOrderCount);
        receiveSeriesData.push(arr[key].unclaimedWorkOrderCount);
      }
      const myChart = echarts.init(document.getElementById('orderMonitor'));

      const option = {
        legend: {
          data: ['当日创建工单数', '待领取工单数'],
          y: 'bottom',
          x: 'center'
        },
        // dataZoom: [
        //   //1.横向使用滚动条
        //   {
        //     type: 'slider', //有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
        //     show: true, //是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
        //     start: 0, //数据窗口范围的起始百分比0-100
        //     end: 80, //数据窗口范围的结束百分比0-100
        //     xAxisIndex: [0] // 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
        //   }
        // ],

        tooltip: {
          //提示框组件
          trigger: 'item', //触发类型,'item'数据项图形触发，主要在散点图，饼图等无类目轴的图表中使用。 'axis'坐标轴触发，主要在柱状图，折线图等会使用类目轴的图表中使用。
          triggerOn: 'mousemove', //提示框触发的条件,'mousemove'鼠标移动时触发。'click'鼠标点击时触发。'mousemove|click'同时鼠标移动和点击时触发。'none'不在 'mousemove' 或 'click' 时触发
          showContent: true //是否显示提示框浮层
        },
        grid: {
          left: '1%',
          right: '3%',
          bottom: '8%',
          containLabel: true
          // width: '100%'
        },
        xAxis: {
          boundaryGap: true,
          type: 'category',
          axisLabel: {
            interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
            formatter: function(params) {
              var newParamsName = ''; // 最终拼接成的字符串
              var paramsNameNumber = params.length; // 实际标签的个数
              var provideNumber = 2; // 每行能显示的字的个数
              var rowNumber = Math.ceil(paramsNameNumber / provideNumber); // 换行的话，需要显示几行，向上取整
              /** 判断标签的个数是否大于规定的个数， 如果大于，则进行换行处理 如果不大于，即等于或小于，就返回原标签 */

              if (paramsNameNumber > provideNumber) {
                // 条件等同于rowNumber>1
                /** 循环每一行,p表示行 */
                for (var p = 0; p < rowNumber; p++) {
                  var tempStr = ''; // 表示每一次截取的字符串
                  var start = p * provideNumber; // 开始截取的位置
                  var end = start + provideNumber; // 结束截取的位置
                  // 此处特殊处理最后一行的索引值
                  if (p == rowNumber - 1) {
                    // 最后一次不换行
                    tempStr = params.substring(start, paramsNameNumber);
                  } else {
                    // 每一次拼接字符串并换行
                    tempStr = params.substring(start, end) + '\n';
                  }
                  newParamsName += tempStr; // 最终拼成的字符串
                }
              } else {
                // 将旧标签的值赋给新标签
                newParamsName = params;
              }
              //将最终的字符串返回
              return newParamsName;
            }
          },
          data: xAxisData
        },
        yAxis: {
          name: '   工单数',
          type: 'value',
          minInterval: 1
        },
        series: [
          {
            color: '#60B1FF',
            // animation: false,
            // barGap: '15%',
            name: '当日创建工单数',
            type: 'bar',
            barWidth: 20, //柱图宽度
            data: daySeriesData
            // itemStyle: {
            //   normal: {
            //     label: {
            //       show: true, //开启显示
            //       position: 'top', //在上方显示
            //       textStyle: {
            //         //数值样式
            //         color: '#60B1FF',
            //         fontSize: 12
            //       }
            //     }
            //   }
            // }
          },
          {
            color: '#F0AC4E',
            // animation: false,
            // barGap: '15%',
            name: '待领取工单数',
            type: 'bar',
            barWidth: 20, //柱图宽度
            data: receiveSeriesData
            // itemStyle: {
            //   normal: {
            //     label: {
            //       show: true, //开启显示
            //       position: 'top', //在上方显示
            //       textStyle: {
            //         //数值样式
            //         color: '#F0AC4E',
            //         fontSize: 12
            //       }
            //     }
            //   }
            // }
          }
        ]
      };

      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option);
      //根据窗口的大小变动图表 --- 重点
      window.onresize = function() {
        myChart.resize();
      };
      // 暴露DataURL
      this.DataUrl = myChart.getDataURL({
        pixelRatio: 1,
        backgroundColor: '#fff'
      });
    },
    downImg() {
      this.downloadFile('监控.png', this.DataUrl);
    },
    // 下载
    downloadFile(fileName, content) {
      // content = content.substring(content.indexOf(',') + 1);
      const aLink = document.createElement('a');
      const blob = this.base64ToBlob(content); // new Blob([content]);
      const evt = document.createEvent('HTMLEvents');
      evt.initEvent('click', true, true); // initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
      aLink.download = fileName;
      aLink.href = URL.createObjectURL(blob);
      aLink.dispatchEvent(
        new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window
        })
      ); // 兼容火狐
    },
    // base64转blob
    base64ToBlob(code) {
      const parts = code.split(';base64,');
      const contentType = parts[0].split(':')[1];
      const raw = window.atob(parts[1]);
      const rawLength = raw.length;

      const uInt8Array = new Uint8Array(rawLength);

      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i);
      }
      return new Blob([uInt8Array], { type: contentType });
    },
    //导出图表
    downOrderMonitor() {
      const that = this;
      if (!that.arr || !that.arr.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      this.url = `${process.env.BASE_API}/workorder/monitor/listMonitorCityWorkTotalExport?formTypeId=${that.formTypeId}`;
      if (
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
      ) {
        this.url =
          this.url +
          `&businessPartCode=${this.$store.getters.channel.businessPartCode}`;
      }
      const a = document.createElement('a');
      a.href = this.url;
      a.click();
    }
  }
};
</script>
<style lang="scss" scoped>
.titlefont {
  font-weight: bold;
  font-size: 16px;
}
.box-card {
  min-width: 1080px !important;
  overflow: auto !important;
}
</style>>
