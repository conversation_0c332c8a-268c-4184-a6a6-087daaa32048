<template>
  <div class="tabs-box">
    <header class="tabs-box-header">
      <xyy-button icon-class="btn-add" class-name="btn-add-icon" @click="addNewClick(1)">新建字段</xyy-button>
      <xyy-button icon-class="sort" type="normal" @click="systemSort">排序</xyy-button>
    </header>
    <div class="info-container">
      <info :info="info"></info>
    </div>
    <xyy-table
      :data="list"
      :col="col"
      :operation="operation"
      :is-pagination="false"
      @get-data="getList"
      @operationClick="operationClick"
    >
      <template slot="fieldType" slot-scope="{col}">
        <el-table-column
          :key="col.index"
          :prop="col.index"
          :label="col.name"
          :width="col.width"
          :resizable="col.resizable"
          :formatter="getField"
        />
      </template>
      <template slot="gmtModified" slot-scope="{col}">
        <el-table-column
          :key="col.index"
          :prop="col.index"
          :label="col.name"
          :width="col.width"
          :formatter="getFormatDate"
        />
      </template>
      <template slot="currentState" slot-scope="{col}">
        <el-table-column :key="col.index" :label="col.name" :width="col.width">
          <template slot-scope="item">
            <span :class="'status-tip '+(item.row.currentState === 0?'open':'close')"></span>
            {{ item.row.currentState === 0?'启用':'禁用' }}
          </template>
        </el-table-column>
      </template>
      <template slot="operation" slot-scope="{col}">
        <el-table-column
          :label="col.name"
          :width="col.width"
          fixed="right"
          class-name="operation-box"
        >
          <template slot-scope="{row}">
            <div v-if="row.editState !== 1">
              <el-button
                v-for="item in filterOptions(row.fieldCode)"
                :key="item.type"
                type="text"
                @click="operationClick(item.type,row)"
              >{{ item.type === 1?item.name[row.currentState]:item.name }}</el-button>
            </div>
          </template>
        </el-table-column>
      </template>
    </xyy-table>
    <!-- 排序 -->
    <el-dialog :visible.sync="centerDialogVisible" title="系统字段排序" width="30%">
      <div class="dialog-row-box">
        <el-row class="dialog-row">
          <el-col :span="16">已选择</el-col>
          <el-col :span="8">操作</el-col>
        </el-row>
        <el-row v-for="(item, index) in sortData" :key="index" class="dialog-row">
          <el-col :span="16">{{ item.fieldName }}</el-col>
          <el-col :span="8">
            <el-button
              :class="['dialog-but', index !== (sortData.length - 1)? 'down-background-ok' : 'down-background-no']"
              type="text"
              size="small"
              @click="systemMobile(index, 1)"
            />
            <el-button
              :class="['dialog-but', index !== 0 ? 'up-background-ok' : 'up-background-no']"
              type="text"
              size="small"
              @click="systemMobile(index, -1)"
            />
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="checkTimer(sortAscertain,'timer')()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
/* 查询列表 排序保存 启用 禁用 */
import {
  enableSystemField,
  listSystemField,
  prohibitSystemField,
  updateSystemFieldSort
} from '@/api/fields/index';
/* 查询关联 */
import { listTemplate } from '../../api/fields/fields-comment';
/* 字段类型 时间格式化 */
import { getFieldType, getFormatDate } from '../../assets/filter/commpent';

export default {
  name: 'SystemFields',
  data() {
    return {
      info: [
        {
          title: '所有工单默认的字段，定义为系统字段。系统字段不可修改',
          info: ''
        },
        { title: '系统字段不可编辑或禁用', info: '' },
        {
          title: '启用/禁用',
          info:
            '禁用即失效状态，新建工单时该字段不会显示；启用即正常可用状态，新建工单字段正常显示。'
        },
        { title: '编辑', info: '可修改字段的配置项，但不能修改字段类型' },
        {
          title: '',
          info:
            '注：问题分类“字段较特殊，编辑功能在”参数配置->问题分类-模板配置“页面',
          mark: true
        }
      ],
      timer: null,
      centerDialogVisible: false, // 排序弹窗
      sortData: [], // 系统字段排序字段
      list: [],
      col: [
        { index: 'fieldName', name: '字段名', resizable: true },
        {
          index: 'fieldType',
          name: '字段类型',
          slot: true,
          resizable: true,
          width: 150
        },
        {
          index: 'remarks',
          name: '备注说明',
          width: 200,
          ellipsis: true,
          resizable: true
        },
        { index: 'gmtModified', name: '更新时间', slot: true, resizable: true },
        { index: 'editorName', name: '更新人', resizable: true, width: 130 },
        {
          index: 'currentState',
          name: '状态',
          width: 90,
          slot: true,
          resizable: true
        },
        {
          index: 'operation',
          name: '操作',
          width: 220,
          slot: true,
          resizable: true
        }
      ],
      operation: [
        {
          name: ['禁用', '启用'],
          type: 1
        },
        {
          name: '编辑',
          type: 2
        }
      ]
    };
  },
  // watch: {
  //   // 路径切换
  //   $route(to, form) {
  //     if (to.name === 'systemFields') this.getList();
  //   }
  // },
  activated() {
    this.getList(this.listQuery);
  },
  methods: {
    filterOptions(code) {
      return code === 's1164724692221825024'
        ? [this.operation[0]]
        : this.operation;
    },
    // 新建1/编辑2字段  id: 编辑的查询字段
    addNewClick(status, id) {
      const str =
        status === 1
          ? '/worksheet/newAddFields/' + new Date().getTime()
          : '/worksheet/editFields/' + id;
      this.$router.push({
        path: str,
        query: {
          type: 'system',
          state: status,
          id: id
        }
      });
    },
    // 显示系统字段排序
    systemSort() {
      if (this.list.length === 0) {
        this.$XyyMessage.error('暂无系统列表数据');
        return false;
      }
      this.centerDialogVisible = true;
      this.sortData = this.list;
    },
    /**
     * 排序移动
     *
     * @param {number} index 要移动的index下标
     * @param {number} num 上移 -1 or下移 1
     */
    systemMobile(index, num) {
      const temporary = this.sortData[index];
      if (
        (index === 0 && num === -1) ||
        (index === this.sortData.length - 1 && num === 1)
      )
        return;
      this.sortData[index] = this.sortData[index + num];
      this.sortData[index + num] = temporary;
      this.$forceUpdate();
    },
    // 排序确定
    sortAscertain() {
      const that = this;
      const params = [];
      const paramsEnd = [];
      // 获取排序传递的参数
      that.sortData.map(function(item, index) {
        params.push({
          id: item.id,
          sort: index + 1
        });
      });
      // 获取只改变的参数
      params.map(function(item, index) {
        for (let i = 0; i < that.list.length; i++) {
          if (item.id === that.list[i].id && item.sort !== that.list[i].sort) {
            paramsEnd.push({
              id: item.id,
              sort: item.sort
            });
            return false;
          }
        }
      });
      if (paramsEnd.length === 0) {
        that.$XyyMessage.error('排序无变化');
        return false;
      }
      updateSystemFieldSort(paramsEnd).then(res => {
        if (res.code === 1) {
          that.centerDialogVisible = false;
          that.$XyyMessage.success('排序修改成功');
          that.getList();
        }
      });
    },
    /*
     * 获取列表数据
     * */
    getList: function(params) {
      listSystemField()
        .then(res => {
          if (res.code === 1) {
            const { list } = res.data;
            this.list = list;
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },
    // 操作按钮
    operationClick: function(type, row) {
      const that = this;
      if (type === 1) {
        // 禁用状态 要启用
        if (row.currentState === 1) {
          this.$XyyMsg({
            content: '确定要启用此字段吗？',
            onSuccess: function() {
              that.enableSystem(row, that);
            }
          });
        } else {
          // 启用状态 -- > 禁用
          listTemplate({ fieldId: row.id }).then(datas => {
            if (datas.data && datas.data.length > 0) {
              let html = '当前有模板正在使用此字段。请先修改模板(';
              datas.data.map(function(item) {
                html += `<span style="color: red">${item.name},</span>`;
              });
              html += ')';
              that.$XyyMsg({
                title: '提示',
                content: html,
                closeBtn: false,
                onSuccess: function() {}
              });
            } else {
              that.$XyyMsg({
                title: '提示',
                content: '此字段为系统字段，确定禁用此字段吗？',
                onSuccess: function() {
                  that.prohibitSystem(row, that);
                }
              });
            }
          });
        }
      }
      if (type === 2) {
        this.addNewClick(2, row.id);
      }
    },
    // 禁用字段
    prohibitSystem(row, that) {
      prohibitSystemField({ fieldId: row.id }).then(res => {
        if (res.code === 1) {
          that.$XyyMessage.success('字段已禁用');
          that.getList();
        } else {
          that.$XyyMessage.error(res.msg);
        }
      });
    },
    // 启用状态
    enableSystem(row, that) {
      enableSystemField({ fieldId: row.id }).then(res => {
        if (res.code === 1) {
          that.$XyyMessage.success('字段已启用');
          that.getList();
        } else {
          that.$XyyMessage.error(res.msg);
        }
      });
    },
    // 字段类型文本
    getField: function(row) {
      return getFieldType(row.fieldType);
    },
    // 时间格式化
    getFormatDate: function(row) {
      return getFormatDate(row.gmtModified);
    }
  }
};
</script>
<style lang="scss" scoped>
.up-background-ok {
  background: url('../../assets/fields/up-ok.png') no-repeat center center;
  background-size: cover;
}
.up-background-no {
  background: url('../../assets/fields/up-no.png') no-repeat center center;
  background-size: cover;
}
.down-background-ok {
  background: url('../../assets/fields/down-ok.png') no-repeat center center;
  background-size: cover;
}
.down-background-no {
  background: url('../../assets/fields/down-no.png') no-repeat center center;
  background-size: cover;
}
.dialog-row-box {
  border: 1px solid #ddd;
  max-height: 440px;
  overflow-y: auto;
  .dialog-row {
    width: 100%;
    padding: 15px;
    &:first-child {
      background: #ddd;
    }
    .dialog-but {
      width: 14px;
      height: 14px;
    }
  }
}
</style>
<style lang="scss">
.el-dialog__body {
  padding-top: 20px;
  padding-bottom: 10px;
}
</style>
