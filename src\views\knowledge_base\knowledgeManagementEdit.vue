<template >
  <div class="knowledge-managementEdit-box">
    <el-form ref="from" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="知识标题" prop="name" id="ming">
        <el-input
          v-model.trim="form.name"
          placeholder="请输入标题"
          style="width: 480px;"
          maxlength="50"
        />
      </el-form-item>

      <el-form-item label="选择分类" prop="groups">
        <el-cascader
          v-model="form.groups"
          placeholder="请选择分类"
          :options="options"
          :props="optionProps"
          clearable
          :show-all-levels="true"
          style="width: 480px;"
        ></el-cascader>
      </el-form-item>

      <el-form-item prop="editor" label="内容">
        <div>
          <!-- 编辑器 -->
          <editor-bar
            class="editor"
            v-model="form.editorText"
            :is-clear="isClear"
            @change="change"
            :index="1"
          ></editor-bar>
        </div>
      </el-form-item>

      <!-- 附件 -->
      <template>
        <el-form-item class="marginTopList" label="附件">
          <el-upload
            ref="myUploader"
            :action="urlAction"
            :limit="fileNums"
            :with-credentials="true"
            :before-upload="beforeUpload"
            :on-exceed="validateNums"
            :on-progress="handleProgress"
            :on-success="handleSuccess"
            :on-error="handleError"
            :class="[{completed:uploadList.length===fileNums}, previewNew ]"
            name="files"
            multiple
            list-type="picture-card"
            accept=".gif, .bmp, .jpg, .jpeg, .png, .pdf, .zip, .rar, .mp4, .avi, .doc, .docx, .ppt, .pptx, .xls, .xlsx, .txt"
            class="upload-file-box"
          >
            <el-button slot="trigger" size="small" type="primary" @click="uploadStart">
              <span class="el-icon-upload2"></span>上传附件
            </el-button>
            <div slot="file" slot-scope="{file}" class="filePreview">
              <file-preview
                :file="file"
                :percent="file.percent?file.percent:0"
                :editable="true"
                @abortCallback="abortFile(file)"
                @delCallback="delFile(file)"
              />
            </div>
          </el-upload>
          <p style="color:#E6A23C;margin: 0; margin-top: 14px;">
            <span class="el-icon-warning"></span>
            <span
              style="font-size: 12px"
            >附件请小于500MB，格式仅限gif/bmp/jpg/jpeg/png/pdf/zip/rar/mp4/avi/doc/docx/xls/xlsx/ppt/pptx/txt</span>
          </p>
        </el-form-item>
      </template>

      <el-form-item label="标签">
        <!-- 标签管理 -->
        <div class="boxer">
          <div class="tag-list">
            <el-tag
              v-for="tag in tagList"
              :key="tag"
              closable
              hit
              disable-transitions
              @close="tagClose(tag)"
            >{{tag}}</el-tag>
          </div>
          <xyy-button @click="newIncrease">
            <span style="font-size: 18px">+</span>添加标签
          </xyy-button>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button plain size="medium" @click="preview">预览</el-button>
        <el-button plain size="medium" @click="seveDraft(form.id)">暂存草稿</el-button>
        <el-button
          plain
          size="medium"
          class="searchCondition"
          @click="seveAnnouncement(form.id)"
        >保存知识</el-button>
        <el-button plain size="medium" @click="cancel">取消</el-button>
      </el-form-item>
    </el-form>
    <!-- 添加标签 -->
    <el-dialog :visible.sync="dialogEditor" title="添加标签" class="dialogInner" width="400px">
      <el-form :inline="true" :model="formTitle" label-position="left" ref="formTitle">
        <el-input
          v-model.trim="formTitle.search"
          maxlength="10"
          onkeyup="this.value=this.value.replace(/[, ]/g,'')"
        ></el-input>
      </el-form>

      <span class="dialog-footer" slot="footer">
        <el-button @click="dialogCancel">取 消</el-button>
        <el-button type="primary" @click="dialogDetermine">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 查看草稿箱 -->
    <el-dialog :visible.sync="dialogDraftBox" title="提示" class="dialogInner" width="400px">
      <!-- <el-form :inline="true" :model="formTitle" label-position="left" ref="formTitle"> -->
      <span>{{draftBoxText}}</span>
      <el-button @click="checkDraftBox" type="text">查 看</el-button>
      <!-- </el-form> -->

      <span class="dialog-footer" slot="footer">
        <el-button @click="dialogDraftBoxCancel">取 消</el-button>
        <el-button type="primary" @click="dialogDraftBoxDetermine">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 添加弹出框-开始-->
    <el-dialog :visible.sync="dialogTableVisible" class="dialogInner2" title="预览">
      <div class="header">
        <div class="title">
          <div>
            <h3>{{form.name}}</h3>
          </div>
        </div>
      </div>

      <div class="knowledge-managementEdit-wrap anowledge-managementEdit-wrap_2">
        <div class="message" v-html="form.editorText"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import EditorBar from '../knowledge_base/richTextEditor2/wangeditor2';
import filePreview from '@/components/Fields/file-preview';
import { MIME_TYPES } from '@/api/fields';
import {
  compareFile,
  initFileDatas,
  adapter,
  typeCopyVal
} from '@/utils/tools.js';
import {
  listAllClassification,
  saveOrUpdateKnowledgeManagement,
  saveOrUpdateKnowledgeManagementDraftBox,
  getKnowledgeManagementDraftBox,
  getKnowledgeManagementDetail
} from '@/api/knowledge_base';
export default {
  components: {
    EditorBar,
    filePreview
  },
  name: 'KnowledgeManagementEdit',
  data() {
    return {
      form: {
        knowledgeManagementId: '',
        isDraftBox: false,
        id: '',
        name: '',
        editorText: '',
        link: '',
        describe: '',
        groups: [],
        inputTag: '',
        firstMessageReply: '',
        firstMessageReplyState: 2,
        customOfflineReply: '',
        customOfflineReplyState: 2,
        imAutoReplyQuestionList: [
          {
            questionAnswer: '',
            questionDesc: '',
            selectStatus: '',
            sortValue: ''
          }
        ],
        currentVersion: ''
      },
      rules: {
        name: [
          { required: true, message: '选项名称为必填项', trigger: 'blur' }
        ],
        editor: [
          { required: true, message: '选项名称为必填项', trigger: 'blur' }
        ],
        groups: [
          { required: true, message: '选项名称为必填项', trigger: 'blur' }
        ]
      },
      tagList: [],
      dialogEditor: false, // 新建标签弹框
      formTitle: {
        search: '' // title搜索
      },
      editorText: ' ',
      dialogTableVisible: false,
      dialogDraftBox: false, // 查看草稿箱弹框
      draftBoxText: '',
      options: [],
      optionProps: {
        value: 'id',
        label: 'name',
        checkStrictly: true
      }, // 问题分类字段适配
      isClear: false,
      previewNew: 'previewNew',
      urlAction: process.env.BASE_API + '/fileUpload/uploadFile',
      fileNums: 10, // 文件最大上传数
      uploadList: [], // 上传队列
      failureList: [], // 上传失败文件列表
      uploadingList: false, // 文件上传中
      previewModal: [], // 附件上传返回值的数组
      previewModalarr: [], // 向后台传的数据
      oldForm: {
        oldName: '',
        oldEditorText: '',
        oldTagList: [],
        oldPreviewModal: ''
      }
    };
  },
  mounted() {
    this.initTree();
    setTimeout(_ => {
      this.initFiles();
    }, 1000);
    this.form.id = this.$route.query.templateId
      ? this.$route.query.templateId
      : '';
    //是否是草稿箱过来
    this.form.isDraftBox = this.$route.query.isDraftBox
      ? this.$route.query.isDraftBox
      : false;
    if (this.form.id) {
      this.$store.dispatch('tagsView/updateVisitedView', {
        ...this.$route,
        meta: {
          title: '编辑知识'
        }
      });
      this.tagList = [];
      if (this.form.isDraftBox) {
        getKnowledgeManagementDraftBox({
          id: this.form.id
        }).then(res => {
          if (res.code === 1) {
            this.form.name = res.data.title;
            this.form.editorText = res.data.content;
            this.form.groups = res.data.classificationId;
            this.form.knowledgeManagementId = res.data.knowledgeManagementId;
            if (res.data.synonyms) {
              this.tagList = res.data.synonyms.split(',');
            }
            const enclosureItem = this.initEnclosure(res.data.attachment);
            this.uploadList = [].concat(enclosureItem);
            this.uploadList.forEach((item, index) => {
              this.previewModal.push(item);
            });

            this.oldForm.oldName = this.form.name;
            this.oldForm.oldEditorText = this.form.editorText;
            this.oldForm.oldPreviewModal = res.data.attachment;
            this.oldForm.oldTagList = JSON.parse(JSON.stringify(this.tagList));
          } else {
            this.$XyyMessage.error(res.msg);
          }
        });
      } else {
        getKnowledgeManagementDetail({
          id: this.form.id
          // currentVersion: this.form.currentVersion
        }).then(res => {
          if (res.code === 1) {
            this.form.name = res.data.title;
            this.form.editorText = res.data.content;
            this.form.groups = res.data.imKnowledgeClassifications;
            if (res.data.synonyms) {
              this.tagList = res.data.synonyms.split(',');
            }
            const enclosureItem = this.initEnclosure(res.data.attachment);
            this.uploadList = [].concat(enclosureItem);
            this.uploadList.forEach((item, index) => {
              this.previewModal.push(item);
            });
            this.oldForm.oldName = this.form.name;
            this.oldForm.oldEditorText = this.form.editorText;
            this.oldForm.oldPreviewModal = res.data.attachment;
            this.oldForm.oldTagList = JSON.parse(JSON.stringify(this.tagList));
          } else {
            this.$XyyMessage.error(res.msg);
          }
        });
      }
    } else {
      this.form.name = '';
      this.form.editorText = '';
    }
  },
  activated() {},
  methods: {
    // 附件初始化
    initEnclosure(item) {
      return initFileDatas(item);
    },
    /**
     * 初始化附件数据
     */
    initFiles() {
      if (this.$refs['myUploader']) {
        this.$refs['myUploader'].uploadFiles = this.uploadList;
      }
    },
    /**
     * 初始化左侧树
     */
    initTree() {
      listAllClassification().then(res => {
        if (res.code === 1) {
          // this.options = res.data;
          this.options = this.getTreeData(res.data);
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 递归判断列表，把最后的children设为undefined
    getTreeData(data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          // children若为空数组，则将children设为undefined
          data[i].children = undefined;
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          this.getTreeData(data[i].children);
        }
      }
      return data;
    },
    // 保存
    seveAnnouncement(id) {
      if (!this.form.name) {
        this.$XyyMessage.error('请填写知识标题');
        return;
      }

      if (!this.form.editorText) {
        this.$XyyMessage.error('请填写知识内容');
        return;
      }

      if (this.form.groups.length <= 0) {
        this.$XyyMessage.error('请选择知识分类');
        return;
      }

      //附件list
      let previewModalarrCopy = [];
      previewModalarrCopy = JSON.parse(JSON.stringify(this.previewModalarr));
      this.previewModal.forEach((item, index) => {
        previewModalarrCopy.push(item.data);
      });
      let previewModalStr = JSON.stringify(previewModalarrCopy);

      //判断是否修改除分类以外内容，记录知识版本 true表示只修改了分类或者什么都没修改
      let isName = this.strEqual(this.form.name, this.oldForm.oldName);
      let isEditorText = this.strEqual(
        this.form.editorText,
        this.oldForm.oldEditorText
      );
      let isPreviewModal = this.strEqual(
        previewModalStr,
        this.oldForm.oldPreviewModal
      );
      let isTagList = this.arrayEqual(this.tagList, this.oldForm.oldTagList);
      let isForm = isName && isEditorText && isPreviewModal && isTagList;

      //参数
      let params = {
        title: this.form.name,
        content: this.form.editorText,
        classificationId: this.form.groups,
        synonyms: this.tagList.join(','),
        attachment: previewModalStr,
        onlyModifyClassification: isForm
      };

      if (this.form.isDraftBox) {
        params.knowledgeManagementId = this.form.knowledgeManagementId
          ? this.form.knowledgeManagementId
          : '';
        params.knowledgeManagementDraftBoxId = this.form.id ? this.form.id : '';
      } else {
        params.id = this.form.id ? this.form.id : '';
      }

      saveOrUpdateKnowledgeManagement(params)
        .then(res => {
          if (res.code === 1) {
            this.$XyyMessage.success('保存成功');
            this.$store.dispatch('tagsView/delView', this.$route);
            if (this.form.isDraftBox) {
              this.$router.push({
                path: '/knowledge_base/draftBoxList'
              });
            } else {
              this.$router.push({
                path: '/knowledge_base/knowledgeManagement'
              });
            }
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },
    // 草稿
    seveDraft(id) {
      if (!this.form.name) {
        this.$XyyMessage.error('请填写知识标题');
        return;
      }

      if (!this.form.editorText) {
        this.$XyyMessage.error('请填写知识内容');
        return;
      }

      if (this.form.groups.length <= 0) {
        this.$XyyMessage.error('请选择知识分类');
        return;
      }

      let previewModalarrCopy = [];
      previewModalarrCopy = JSON.parse(JSON.stringify(this.previewModalarr));
      this.previewModal.forEach((item, index) => {
        previewModalarrCopy.push(item.data);
      });
      let previewModalStr = JSON.stringify(previewModalarrCopy);

      let params = {
        title: this.form.name,
        content: this.form.editorText,
        classificationId: this.form.groups,
        synonyms: this.tagList.join(','),
        attachment: previewModalStr
      };

      if (this.form.isDraftBox) {
        params.id = this.form.id ? this.form.id : '';
      } else {
        params.knowledgeManagementId = this.form.id ? this.form.id : '';
      }

      saveOrUpdateKnowledgeManagementDraftBox(params)
        .then(res => {
          if (res.code === 1) {
            this.$XyyMessage.success('保存成功');
            this.$store.dispatch('tagsView/delView', this.$route);
            if (this.form.isDraftBox) {
              this.$router.push({
                path: '/knowledge_base/draftBoxList'
              });
            } else {
              this.$router.push({
                path: '/knowledge_base/knowledgeManagement'
              });
            }
          } else if (res.code === 90001) {
            this.draftBoxText = res.msg + '已保存草稿，请在草稿箱中 ';
            this.dialogDraftBox = true;
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },
    /**
     * 取消
     */
    cancel() {
      this.$store.dispatch('tagsView/delView', this.$route);
      if (this.form.isDraftBox) {
        this.$router.push({
          path: '/knowledge_base/draftBoxList'
        });
      } else {
        this.$router.push({
          path: '/knowledge_base/knowledgeManagement'
        });
      }
    },
    dialogDraftBoxCancel() {
      this.dialogDraftBox = false;
    },
    dialogDraftBoxDetermine() {
      this.dialogDraftBox = false;
    },
    checkDraftBox() {
      // this.$store.dispatch('tagsView/delView', this.$route);
      this.$router.push({
        path: '/knowledge_base/draftBoxList'
      });
      this.dialogDraftBox = false;
    },

    // 新建标签取消事件
    dialogCancel() {
      this.dialogEditor = false;
    },
    // 点击新增按钮事件
    newIncrease() {
      if (this.tagList.length >= 20) {
        this.$XyyMessage.error('添加标签已达上限');
        return;
      }
      this.dialogEditor = true;
    },
    // 点击确定添加标签
    dialogDetermine() {
      //   获取搜索框里面的数据 传给后台
      const searchDate = this.formTitle.search;
      if (!searchDate) {
        this.$XyyMessage.error('请输入标签');
        return;
      }
      this.tagList.push(searchDate);
      // 传给后台以后 数据清空 搜索框消失 重新更新数据
      this.formTitle.search = '';
      this.dialogEditor = false;
    },
    /**
     * 停用标签
     */
    tagClose(name) {
      this.tagList.forEach((item, index) => {
        if (item === name) {
          this.tagList.splice(index, 1);
        }
      });
    },
    change() {},
    // 点击添加事件
    async preview() {
      this.dialogTableVisible = true;
    },
    makeNo() {
      this.dialogTableVisible = false;
    },
    // 关联问题弹窗确定
    makeSure() {
      this.dialogTableVisible = false;
    },
    /**
     * 上传之前
     */
    beforeUpload(file) {
      this.uploadingList = false;
      this.uploadList = [].concat(file);
      if (file.size / 1024 / 1000 > 500) {
        // 附件上传大小的限制
        this.$XyyMessage.error(`附件上传大小不能超过500M`);
        return false;
      }

      if (file.type) {
        if (!MIME_TYPES.includes(file.type)) {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: `上传文件格式仅限：<span style="color:red;">gif bmp jpg jpeg png pdf zip rar mp4 avi doc docx xls xlsx ppt pptx txt</span>`, // html代码串
            onSuccess: () => {}
          });
          return false;
        }
      } else {
        // element ui 插件bug 无法检测rar rar文件以后缀方式判断
        const _type = file.name
          .slice(file.name.lastIndexOf('.') + 1)
          .toLowerCase();
        if (_type !== 'rar') {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: `上传文件格式仅限：<span style="color:red;">gif bmp jpg jpeg png pdf zip rar mp4 avi doc docx xls xlsx ppt pptx txt</span>`, // html代码串
            onSuccess: () => {}
          });
          return false;
        }
      }
    },
    /**
     * 文件个数超出回调
     */
    validateNums(file, fileList) {
      this.$XyyMessage.error(`最多上传${this.fileNums}个`);
    },
    /**
     * 上传中回调
     */
    handleProgress(event, file) {
      if (file) {
        this.uploadingList = true;
        file.percent = Number(event.percent.toFixed(0));
        if (file.percent > 99) file.percent = 99;
      }
    },

    //    上传成功的回调
    handleSuccess(res, file) {
      if (res.code === 1) {
        if (!res.data.failFiles.length) {
          file.percent = 100;
          const _file = {
            uid: file.uid,
            name: file.name,
            url:
              res.data.baseUrl +
              (res.data.successFiles[0].thumbImagePath ||
                res.data.successFiles[0].group +
                  '/' +
                  res.data.successFiles[0].path), // 预览路径
            path:
              res.data.baseUrl +
              res.data.successFiles[0].group +
              '/' +
              res.data.successFiles[0].path, // 真实路径
            percent: 100,
            raw: {
              type: file.raw.type
                ? file.raw.type
                : 'application/x-rar-compressed',
              size: file.raw.size
            },
            data: res.data.successFiles[0]
          };
          this.previewModal.push(_file);
        } else {
          this.$refs['myUploader'].uploadFiles.forEach((el, i) => {
            if (el.uid === file.uid) {
              const _file = this.$refs['myUploader'].uploadFiles.splice(i, 1);
              this.failureList = this.failureList.concat(_file);
            }
            this.uploadList = [].concat(this.$refs['myUploader'].uploadFiles);
          });

          let msg = '';
          if (this.failureList.length > 1) {
            msg = `${this.failureList[0].name}等${this.failureList.length}个文件上传失败`;
          } else {
            msg = `${this.failureList[0].name}上传失败`;
          }
          this.$XyyMessage.error(msg);
        }
      } else {
        this.$XyyMessage.error(res.msg);
      }
      this.uploadingList = false;
    },
    //    上传失败的回调
    handleError(res) {
      this.uploadingList = false;
      this.$XyyMessage.error(`文件上传失败`);
    },
    /**
     * 上传点击事件 初始化上传失败数组
     */
    uploadStart() {
      this.failureList = [];
    },
    /**
     * 取消上传
     */
    abortFile(file) {
      this.$refs['myUploader'].abort(file);
      this.$refs['myUploader'].uploadFiles.forEach((el, i) => {
        if (el.uid === file.uid) {
          this.$refs['myUploader'].uploadFiles.splice(i, 1);
        }
      });
      this.uploadList = [].concat(this.$refs['myUploader'].uploadFiles);
      this.uploadingList = false;
    },

    // 附件删除
    delFile(file) {
      // 删除上传队列中的数据
      this.$refs['myUploader'].uploadFiles.forEach((el, i) => {
        if (el.uid === file.uid) {
          this.$refs['myUploader'].uploadFiles.splice(i, 1);
        }
      });
      this.uploadList = [].concat(this.$refs['myUploader'].uploadFiles);
      // 删除存储的数据
      this.previewModal.forEach((item, index) => {
        if (file.uid === item.uid) {
          this.previewModal.splice(index, 1);
        }
      });
    },
    /**
     * JS判断两个数组是否相等
     * @param {Array} arr1
     * @param {Array} arr2
     * @returns {boolean} 返回true 或 false
     */
    arrayEqual(arr1, arr2) {
      // debugger;
      if (arr1 === arr2) return true;
      if (arr1.length != arr2.length) return false;
      for (var i = 0; i < arr1.length; ++i) {
        if (arr1[i] !== arr2[i]) return false;
      }
      return true;
    },
    /* JS判断两个字符串是否相等
     * @returns {boolean} 返回true 或 false
     */
    strEqual(str1, str2) {
      if (str1 === str2) return true;
      return false;
    }
  }
};
</script>

<style lang="scss">
.knowledge-managementEdit-box {
  .el-form {
    width: calc(100% - 280px);
    margin: 20px 20px;
    .el-form-item {
      margin-bottom: 20px;

      /deep/.el-form-item__label {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(41, 41, 51, 1);
      }
      .el-input {
        /deep/.el-input__inner {
          height: 36px;
          line-height: 36px;
          border: 1px solid rgba(228, 228, 235, 1);
        }
      }
      .el-textarea {
        /deep/.el-textarea__inner {
          height: 211px;
          border: 1px solid rgba(228, 228, 235, 1);
        }
      }
      // .el-button {
      //   height: 36px;
      //   padding: 0 12px;
      //   line-height: 36px;
      //   font-size: 14px;
      //   font-family: PingFangSC-Regular, PingFang SC;
      //   font-weight: 400;
      //   color: rgba(87, 87, 102, 1);
      //   border-radius: 2px;
      //   border: 1px solid rgba(228, 228, 235, 1);
      //   &:focus,
      //   &:hover {
      //     background: #fff;
      //     border-color: rgba(228, 228, 235, 1);
      //   }
      //   &.el-button--primary {
      //     color: rgba(255, 255, 255, 1);
      //     padding: 0 20px;
      //     border: none;
      //   }
      //   &.el-button--primary:focus,
      //   &.el-button--primary:hover {
      //     background: #3b95a8;
      //     border-color: #3b95a8;
      //   }
      // }
      /deep/ .el-form-item__content {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
      }
    }
  }

  .marginTopList {
    .previewNew {
      position: relative;
      /deep/.el-upload--picture-card {
        width: 102px;
        height: 36px;
        display: inline-block;
        line-height: 32px;
        border: 0 none;
        position: absolute;
        top: 0;
        left: 0;
        .el-button {
          width: 102px;
          height: 36px;
          background: rgba(255, 255, 255, 1);
          border-radius: 2px;
          border: 1px solid rgba(228, 228, 235, 1);
          color: rgba(87, 87, 102, 1);
        }
      }
      /deep/.el-upload-list--picture-card {
        display: inline-block;
        width: 271px;
        margin-top: 40px;
      }
      /deep/.el-upload-list__item {
        width: 126px;
        height: 30px;
        margin-bottom: 15px;
        display: inline-block;
        background: rgba(245, 247, 250, 1);
        border-radius: 2px;
        border: 1px solid rgba(228, 228, 235, 1);
        overflow: visible;
      }
    }
    .upload-file-box.completed {
      /deep/.el-upload--picture-card {
        display: none;
      }
    }
  }

  .searchCondition.is-plain {
    background: rgba(59, 149, 168, 1);
    color: #fff;
  }

  .boxer {
    width: 100%;
    height: calc(100% - 75px);
    overflow-y: auto;

    .tag-list {
      .el-tag {
        margin: 0 10px 10px 0;
      }
    }
  }
  .dialogInner {
    .el-input__inner {
      height: 37px;
      line-height: 37px;
      margin-right: 5px;
      width: 320px;
    }
  }

  .dialogInner2 {
    .shezhiyin {
      position: absolute;
      top: 5px;
      left: 20px;
      /deep/.el-form-item__content {
        margin-left: 0 !important;
      }
    }
    .line {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(41, 41, 51, 1);
      margin-top: 0;
      margin-bottom: 0;
      span {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(174, 174, 191, 1);
        margin-left: 8px;
      }
    }
    .header {
      margin: 0 auto;
      .title {
        text-align: center;
        // margin: 0 auto;
        // display: flex;
        // position: absolute;
        // left: 50%;
        // -webkit-transform: translateX(-50%);
        // transform: translateX(-50%);
        .title-info {
          margin-left: 20px;
        }
      }
    }
    .nowledge-managementEdit-wrap_2 {
      background: #fff;
      /deep/.elIconDelete {
        display: none;
      }
    }
    .knowledge-managementEdit-wrap {
      .message {
        min-height: 400px;
        width: 100%;
        height: auto;
        /* table 样式 */
        /deep/ {
          table {
            border-top: 1px solid #ccc;
            border-left: 1px solid #ccc;
          }
          table td,
          table th {
            border-bottom: 1px solid #ccc;
            border-right: 1px solid #ccc;
            // padding: 3px 5px;
          }
          table th {
            background-color: #eee;
            // border-bottom: 2px solid #ccc;
            text-align: center;
          }

          /* blockquote 样式 */
          blockquote {
            display: block;
            border-left: 8px solid #d0e5f2;
            padding: 5px 10px;
            margin: 10px 0;
            line-height: 1.4;
            font-size: 100%;
            background-color: #f1f1f1;
          }

          /* code 样式 */
          code {
            display: inline-block;
            *display: inline;
            *zoom: 1;
            background-color: #f1f1f1;
            border-radius: 3px;
            padding: 3px 5px;
            margin: 0 3px;
          }
          pre code {
            display: block;
          }

          /* ul ol 样式 */
          ul,
          ol {
            margin: 0;
          }

          ul,
          dl {
            list-style-type: disc;
          }
          a {
            color: #428bca;
            text-decoration: none;
          }
          a:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}
</style>
