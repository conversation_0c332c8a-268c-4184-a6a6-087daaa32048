# xyy-table

基于 el-table 的中台项目定制化 table

## 属性

| 参数          | 说明                                | 类型    | 可选值 | 默认值 |
| ------------- | ----------------------------------- | ------- | ------ | ------ |
| data          | 显示的数据                          | Array   | ---    | ---    |
| list-query    | 查询参数:目前只有分页信息           | Object  | ---    | ---    |
| col           | 列属性                              | Array   | ---    | ---    |
| operation     | 右侧操作区域                        | Array   | ---    | ---    |
| is-pagination | 是否显示分页器                      | Boolean | ---    | true   |
| offset-top    | 表格距视口高度,用于动态计算表格高度 | Number  | ---    | 189    |

### list-query

| 参数     | 说明             | 类型   | 可选值 | 默认值 |
| -------- | ---------------- | ------ | ------ | ------ |
| page     | 当前页数         | Number | ---    | 1      |
| pageSize | 每页显示条目个数 | Number | ---    | 10     |
| total    | 总条目数         | Number | ---    | 0      |

### col

| 参数     | 说明                                                                                                   | 类型    | 可选值 | 默认值 |
| -------- | ------------------------------------------------------------------------------------------------------ | ------- | ------ | ------ |
| index    | 对应列内容的字段名                                                                                     | String  | ---    | ---    |
| name     | 显示的标题                                                                                             | String  | ---    | ---    |
| width    | 对应列的宽度                                                                                           | Number  | ---    | ---    |
| ellipsis | 当内容过长被隐藏时显示'...',且鼠标移入显示 tip                                                         | Boolean | ---    | false  |
| slot     | 内容插入，可将 el-table-column 整个插入，可将列的其他属性全部覆盖，但列的位置仍由其在 col 中的顺序决定 | Boolean | ---    | false  |

### operation

| 参数   | 说明                 | 类型               | 可选值 | 默认值 |
| ------ | -------------------- | ------------------ | ------ | ------ |
| name   | 操作按钮名称         | String             | ---    | ---    |
| type   | 自定义的操作按钮类型 | Number             | ---    | ---    |
| format | 用来格式化 name      | Function(type,row) | ---    | ---    |

## 事件

| 事件名称        | 说明                                                               | 回调参数                             |
| --------------- | ------------------------------------------------------------------ | ------------------------------------ |
| get-data        | 数据需要更新时触发，需在数据更新后手动更新 data 和 list-query 属性 | { page(当前页), pageSize(每页条数) } |
| operation-click | 操作按钮点击事件                                                   | { type(按钮类型), row(当前行数据) }  |

## log

#### 待解决

1. ~~表格初始化时 el-table\_\_fixed-body-wrapper 的 top 属性会多计算 7px，导致固定列下移 7px，更改表格高度后产生~~ 2019.8.22 周明哲
2. ~~表格内容的底部白边问题，更改表格高度后产生~~ 2019.8.22 周明哲

#### 待优化

1. page 和 pageSize 同时改变时，会出现重复请求
