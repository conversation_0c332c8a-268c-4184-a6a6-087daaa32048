<template>
  <div class="im-config">
    <el-tabs v-model="activeTab" type="card" class="im-tabs">
      <el-tab-pane label="常用" name="usual" v-if="chatState==1">
        <common-use></common-use>
      </el-tab-pane>
      <el-tab-pane label="客户信息" name="customer">
        <custome-info></custome-info>
      </el-tab-pane>
      <el-tab-pane label="订单信息" name="order">
        <custome-order></custome-order>
      </el-tab-pane>
      <!-- <el-tab-pane label="历史工单" name="history">
        <history-list v-if="isHistoryList" :status.sync="isHistoryList"></history-list>
        <history-edit v-else :status.sync="isHistoryList"></history-edit>
      </el-tab-pane>-->
      <el-tab-pane label="聊天记录" name="record">
        <chat-record></chat-record>
      </el-tab-pane>
      <el-tab-pane label="服务小结" name="service">
        <service-config-form></service-config-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import historyList from './components/history/list';
import historyEdit from './components/history/edit';
import customeInfo from './components/customeInfo';
import commonUse from './components/commonUse';
import customeOrder from './components/customeOrder';
import chatRecord from './components/chatRecord';
import serviceConfigForm from './components/serviceConfigForm';
export default {
  components: {
    historyList,
    historyEdit,
    customeInfo,
    commonUse,
    customeOrder,
    chatRecord,
    serviceConfigForm
  },
  data() {
    return {
      activeTab: 'usual',
      isHistoryList: true,
      chatInfo: {},
      chatState: 0 // 1：会话中,2：其他
    };
  },
  methods: {
    tohistory() {
      this.activeTab = 'record';
    },
    rightSelect({ type, userId, chat }) {
      const that = this;
      that.chatInfo = chat;
      if (type === '0') {
        if (that.chatInfo.dialogEndScene === 0) {
          that.chatState = 1;
          that.activeTab = 'usual';
        } else {
          that.chatState = 2;
          that.activeTab = 'customer';
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.im-config {
  padding: 20px;
  height: 100%;
  .el-tabs {
    width: 494px;
    height: 100%;
    margin: 0 auto;
    /deep/.el-tabs__header {
      margin-bottom: 0;
      .el-tabs__nav {
        width: 100%;
        border-radius: 0;
        display: flex;
        .el-tabs__item {
          background: rgba(245, 247, 250, 1);
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(144, 147, 153, 1);
          border-color: #dcdee3;
          padding: 0;
          min-width: 82px;
          box-sizing: border-box;
          text-align: center;
          &.is-active {
            color: rgba(41, 41, 51, 1);
            background: #fff;
            border-bottom: none;
          }
          flex-grow: 1;
        }
      }
    }
    /deep/.el-tabs__content {
      height: calc(100% - 40px);
      border: 1px solid rgba(220, 222, 227, 1);
      border-top: 0;
      padding: 15px 20px 0;
      overflow-y: auto;
    }
  }
}
</style>
