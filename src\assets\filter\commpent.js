// 字段类型
export var getFieldType = function (fieldType) {
  switch (fieldType) {
    case 0: return '单行输入';
    case 1: return '多行输入';
    case 2: return '下拉列表';
    case 3: return '单选';
    case 4: return '多选';
    case 5: return '日期';
    case 6: return '级联';
    case 7: return '省市区';
    case 8: return '电话';
    case 9: return '邮箱';
    case 10: return '附件';
  }
};
// 时间格式化
export var getFormatDate = function (gmtModified) {
  return new Date(gmtModified + 8 * 3600 * 1000)
    .toJSON()
    .substr(0, 19)
    .replace('T', ' ');
};
