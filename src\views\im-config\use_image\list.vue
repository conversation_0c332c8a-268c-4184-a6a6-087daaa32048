<template>
  <div>
    <el-upload
      ref="myUploader"
      :disabled="true"
      :action="url"
      :limit="fileNums"
      :class="{completed:uploadList.length===fileNums}"
      :file-list="fileList"
      :http-request="uploadStart"
      name="files"
      multiple
      list-type="picture-card"
      accept=".jpg, .jpeg, .png"
      class="upload-file-box"
    >
      <i slot="default" class="el-icon-plus" @click="uploadStart"></i>
      <div slot="file" slot-scope="{file}">
        <file-preview
          :file="file"
          :percent="file.percent?file.percent:0"
          :editable="true"
          @abortCallback="abortFile(file)"
          @delCallback="delFile(file)"
        />
      </div>
    </el-upload>
    <dialog-upload ref="previewDialog" :is-open="uploadIsOpen" @dialogClose="dialogClose"></dialog-upload>
  </div>
</template>

<script>
import { pictureList, pictureDel } from '@/api/configuration/use_image';
import { MIME_TYPES } from '@/api/fields';
import filePreview from './components/file-preview';
import dialogUpload from './components/dialog-upload';
export default {
  name: 'compImage',
  components: {
    filePreview,
    dialogUpload,
  },
  data() {
    return {
      uploadIsOpen: false,
      fileList: [
        // {
        //   id: 8,
        //   smallurl:
        //     'http://************:8024/read/fui?fileName=4067161321524457.jpg',
        //   bigurl:
        //     'http://************:8024/read/fui?fileName=4067161178818482.jpg',
        //   corpid: 10000,
        //   name: '测试'
        // }
      ],
      url: process.env.BASE_API + '/fileUpload/uploadFile',
      fileNums: 10, // 文件最大上传个数
      uploadList: [], // 上传队列
      failureList: [], // 上传失败文件列表
      uploading: false, // 文件上传中
    };
  },
  mounted() {
    this.pictureList();
  },
  methods: {
    pictureList() {
      pictureList().then((res) => {
        if (res.code === 1) {
          this.fileList = res.data;
          if (
            document.domain === 'wzj.dev.ybm100.com' &&
            this.fileList.length > 0
          ) {
            this.fileList = this.fileList.map((i) => {
              i.smallurl = `http://ec-service.dev.ybm100.com/${i.smallurl}`;
              i.bigurl = `http://ec-service.dev.ybm100.com/${i.bigurl}`;
              return i;
            });
          }
        }
      });
    },
    dialogClose() {
      this.uploadIsOpen = false;
    },
    /**
     * 上传点击事件 初始化上传失败数组
     */
    uploadStart() {
      this.uploadIsOpen = true;
      // this.failureList = [];
    },
    /**
     * 取消上传
     */
    abortFile(file) {
      this.$refs['myUploader'].abort(file);
      this.$refs['myUploader'].uploadFiles.forEach((el, i) => {
        if (el.uid === file.uid) {
          this.$refs['myUploader'].uploadFiles.splice(i, 1);
        }
      });
      this.uploadList = [].concat(this.$refs['myUploader'].uploadFiles);
      this.uploading = false;
    },
    delFile(file) {
      const that = this;
      const param = { imgid: file.id };
      pictureDel(param).then((res) => {
        console.log('res:', res);
        if (res.code === 1) {
          that.pictureList();
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.upload-file-box {
  float: left;
  margin-left: 22px;
  margin-top: 20px;
  /deep/.el-upload--picture-card {
    width: 80px;
    height: 80px;
    line-height: 80px;
    // background: rgba(245, 247, 250, 1);
    border-radius: 2px;
    border: 1px solid #dcdfe6;
    margin-left: 10px;
    .el-icon-plus {
      color: rgba(216, 216, 216, 1);
      padding: 20px 25px 30px;
    }
  }
  /deep/.el-upload-list--picture-card {
    .el-upload-list__item {
      width: 100px;
      height: auto;
      background: none;
      border: none;
      margin: 0;
      &:focus {
        border: none;
        outline: none;
      }
    }
  }
  /deep/.el-upload__tip {
    line-height: 20px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(144, 147, 153, 1);
  }
}
.upload-file-box.completed {
  /deep/.el-upload--picture-card {
    display: none;
  }
}
</style>
