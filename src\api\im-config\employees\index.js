import request from '@/utils/request-im';
import { formData } from '@/utils/index';

export function getGroupList(page) {
  return request({
    url: '/imKefuGroup/list',
    method: 'get',
    params: page
  });
}

export function getEmployeeList(params) {
  return request({
    url: '/imKefuGroup/listKefu',
    method: 'get',
    params
  });
}

export function saveGroupData(data) {
  return request({
    url: '/imKefuGroup/saveOrUpdate',
    method: 'post',
    data: data
  });
}

export function delGroupData(params) {
  return request({
    url: '/imKefuGroup/delete',
    method: 'get',
    params
  });
}

export function getGroupData(id) {
  return request({
    url: '/imKefuGroup/selectById',
    method: 'get',
    params: { id }
  });
}
