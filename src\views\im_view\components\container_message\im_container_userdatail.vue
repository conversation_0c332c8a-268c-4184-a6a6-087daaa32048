<template>
  <div class="view_main">
    <el-tabs
      v-if="type==='history'"
      v-model="activeName"
      :style="{height:prohieght}"
      class="view-tab"
      @tab-click="handleClick"
    >
      <el-tab-pane label="访客信息" name="first">
        <div class="tab_clom">
          <div v-for="(item, index) in userDetailArray" :key="index" class="tab_row">
            <div class="user_title">{{ item.title }}</div>
            <div class="user_datail">{{ item.detail }}</div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="服务总结" name="second">
        <div class="tab_clom">
          <div v-for="(item, index) in serviceSumArray" :key="index" class="tab_row">
            <div class="user_title">{{ item.title }}</div>
            <div class="user_datail">{{ item.detail }}</div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <div v-else class="view-tab">
      <div
        style="margin-top:13px; margin-bottom:15px;font-size:16px;color:#303133;font-weight:600;"
      >访客信息</div>
      <div class="tab_clom">
        <div v-for="(item, index) in userDetailArray" :key="index" class="tab_row">
          <div class="user_title">{{ item.title }}</div>
          <div class="user_datail">{{ item.detail }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { serviceDetail } from '@/api/im_view/serviceConfigForm';
export default {
  props: {
    currentID: {
      type: String
    },
    prohieght: {
      type: String
    },
    type: {
      type: String
    },
    userDetailArray: {
      type: Array
    }
  },
  data() {
    return {
      serviceSumArray: ['', '', '', ''],
      activeName: 'first'
    };
  },
  computed: {
    containerId() {
      return this.$store.getters.containerid;
    }
  },
  watch: {
    containerId(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.apiRequest();
      }
    }
  },
  mounted() {
    this.apiRequest();
  },
  methods: {
    handleClick(tab, event) {
      this.activeName = tab.name;
    },
    apiRequest() {
      this.serviceSumArray = [];
      serviceDetail('', this.containerId).then(res => {
        if (res.data === null) {
          // this.$XyyMessage.error(res.msg);
          return;
        }
        const servicedata = res.data.imFormDataExtend;
        const serviceArr = [];
        for (let index = 0; index < servicedata.length; index++) {
          const element = servicedata[index];
          serviceArr.push({
            title: element.fieldLable,
            detail: element.labelValue
          });
        }
        this.serviceSumArray = serviceArr;
      });
    }
  }
};
</script>

<style scoped>
.view_main {
  padding-left: 20px;
  padding-top: 20px;
  padding-right: 20px;
  width: 534px;
}
.view-tab {
  padding: 0px 20px;
  width: 494px;
  border: 1px solid rgba(220, 222, 227, 1);
}
.user_datail {
  margin-left: 10px;
  font-size: 14px;
  color: #575766;
}
.user_title {
  font-size: 14px;
  color: rgb(48, 49, 51);
}
.tab_row {
  display: flex;
  flex-direction: row;
  min-height: 30px;
}
.tab_clom {
  display: flex;
  flex-direction: column;
}
</style>
