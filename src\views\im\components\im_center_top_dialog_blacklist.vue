<template>
  <div>
    <el-dialog
      :title="'添加黑名单'"
      :visible.sync="dialogFormVisible"
      width="850px"
      :before-close="befoClose"
    >
      <div class="im_dialog_blacklist_body">
        <div class="im_dialog_blacklist_body_top">
          <div style="color:red;">*</div>
          <div style="margin-right:12px;">时限</div>
          <el-button style="margin-right:12px;" size="medium" @click="allDayAction()">24h</el-button>
          <el-button style="margin-right:12px; margin-left:0px;" size="medium" @click="allTime()">永久</el-button>
          <el-button
            v-if="autofocus===true"
            style="margin-right:12px; margin-left:0px;"
            size="medium"
            type="success"
            @click="customTimeAction()"
          >自定义</el-button>
          <el-button
            v-else
            style="margin-right:12px; margin-left:0px;"
            size="medium"
            @click="customTimeAction()"
          >自定义</el-button>
          <div
            style="display: flex;flex-direction: row;align-items: center; border:1px solid #dcdfe6; height:36px; width:204px; border-radius:4px;"
          >
            <el-input
              v-model="customTime"
              placeholder="请输入"
              style="width:110px;"
              @input="inputchange"
              ref="cutom_time_input"
              :readonly="cantEdit"
            ></el-input>
            <div style="color:#909399">小时</div>
            <div
              style="width:44px; text-align: center; margin-left:20px; background:rgba(245,247,250,1);"
            >
              <div style="border:1px solid #dcdfe6;" @click="addNumber()">
                <img style="width:9px;height:9px;" :src="host+'/pc/static/upicon.png'" alt />
              </div>
              <div style="border:1px solid #dcdfe6;" @click="minuNumber()">
                <img style="width:9px;height:9px;" :src="host+'/pc/static/downicon.png'" alt />
              </div>
            </div>
          </div>
        </div>
        <div class="im_dialog_blacklist_body_bottom">
          <div style="margin-right:12px;">备注说明</div>
          <el-input
            style="width:463px; height:211px"
            type="textarea"
            placeholder="请输入"
            v-model="textarea"
            :resize="'none'"
            :maxlength="100"
            show-word-limit
          ></el-input>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="befoClose()">取 消</el-button>
        <el-button type="primary" @click="sureAction()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: ['dialogFormVisible'],
  watch: {
    dialogFormVisible(newvalue, oldvalue) {
      this.customTime = '';
      this.textarea = '';
      this.timeclick = '';
      this.cantEdit = true;
      this.autofocus = false;
    }
  },
  data() {
    return {
      customTime: '',
      textarea: '',
      timeclick: '',
      cantEdit: true,
      autofocus: false,
      host: process.env.BASE_API
    };
  },
  methods: {
    sureAction() {
      if (this.customTime === '' && this.timeclick === '') {
        this.$message('请选择时限');
        return;
      }
      var data = {
        time:
          this.customTime === ''
            ? this.timeclick.toString()
            : this.customTime.toString(),
        remark: this.textarea
      };
      this.$emit('im_blacklist_dialog', data, false);
    },
    befoClose(done) {
      this.$emit('im_blacklist_dialog', '', false);
    },
    allDayAction() {
      this.timeclick = 24;
      this.autofocus = false;
      this.customTime = '';
      this.cantEdit = true;
    },
    allTime() {
      this.timeclick = 0;
      this.autofocus = false;
      this.customTime = '';
      this.cantEdit = true;
    },
    inputchange(value) {
      const number = value.replace(/\D/g, '');
      if (value >= 99999) {
        this.customTime = 999999;
        return;
      }
      this.customTime = number;
    },
    customTimeAction() {
      this.autofocus = true;
      this.timeclick = '';
      this.cantEdit = false;
      this.$refs.cutom_time_input.focus();
    },
    addNumber() {
      if (this.cantEdit === true) {
        return;
      }
      var customTimeNum = parseInt(
        this.customTime === '' ? 0 : this.customTime
      );
      customTimeNum += 1;
      this.customTime = customTimeNum.toString();
    },
    minuNumber() {
      if (this.cantEdit === true) {
        return;
      }
      var customTimeNum = parseInt(
        this.customTime === '' ? 0 : this.customTime
      );
      if (customTimeNum <= 0) {
        this.$message('已经是最小了');
        return;
      }

      customTimeNum -= 1;
      this.customTime = customTimeNum.toString();
    }
  }
};
</script>

<style>
.im_dialog_blacklist_body_top .el-input__inner {
  -webkit-appearance: none;
  background-color: #ffffff;
  background-image: none;
  border-radius: 4px;
  border: 0px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  font-size: inherit;
  height: 34px;
  line-height: 34px;
  outline: none;
  -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  /* width: 100%; */
}

.im_dialog_blacklist_body_top .el-input__icon {
  height: 100%;
  width: 25px;
  text-align: center;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  line-height: 36px;
}

.im_dialog_blacklist_body_bottom .el-textarea__inner {
  display: block;
  resize: vertical;
  padding: 5px 15px;
  line-height: 1.5;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
  height: 211px;
  font-size: inherit;
  color: #606266;
  background-color: #ffffff;
  background-image: none;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.im_dialog_blacklist_body .el-button--success {
  color: #3b95a8;
  border-color: #c4dfe5;
  background-color: #ebf4f6;
}
</style>

<style scoped>
.im_dialog_blacklist_body {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.im_dialog_blacklist_body_top {
  width: 550px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}

.im_dialog_blacklist_body_bottom {
  width: 550px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>