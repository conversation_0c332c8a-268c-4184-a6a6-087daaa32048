import request from '@/utils/request-im';

/**
 *常用图片列表
 * @param {*}
 */
// export function pictureList(params) {
//   return request({
//     url: '/picture/list',
//     method: 'get',
//     params: params
//   });
// }

/**
 *new 消息自动回复设置接口
 * @param
 */
export function autoMsgAdd (param) {
  return request({
    url: '/autoReply/autoMsgAdd',
    method: 'post',
    data: param
  });
}

/**
 *获取消息自动回复设置
 * @param {*}
 */
export function autoMsgs (params) {
  return request({
    url: '/autoReply/autoMsgs',
    method: 'get',
    params: params
  });
}
/**
 *new 会话分配保存接口
 * @param
 */
export function dialogassignruleAdd (param) {
  return request({
    url: '/dialogassignrule/add',
    method: 'post',
    data: param
  });
}

/**
 * 获取会话分配信息
 */
export function getSessionMessage () {
  return request({
    url: '/dialogassignrule/loadRule',
    method: 'get'
  });
}
