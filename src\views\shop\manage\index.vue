<template>
  <xyy-list-page>
    <template slot="header">
      <search @handleSearch="handleSearch"></search>
    </template>
    <template slot="body">
      <!--列表------>
      <xyy-table
        :data="list"
        :col="col"
        :list-query="listQuery"
        :operation="operation"
        @get-data="getList"
        @operation-click="operationClick"
      >
        <template slot="shopName" slot-scope="{col}">
          <el-table-column :label="col.name" width="150">
            <template slot-scope="scope">
              {{ shopList[scope.$index] }}
            </template>
          </el-table-column>
        </template>
        <template slot="imageUrl" slot-scope="{col}">
          <el-table-column :label="col.name" width="100">
            <template slot-scope="{row}">
              <img :src="imgurl+row[col.index]" alt="" width="50">
            </template>
          </el-table-column>
        </template>
        <template slot="status" slot-scope="{col}">
          <el-table-column :label="col.name" width="100">
            <template slot-scope="{row}">{{ row[col.index] |statusToName }}</template>
          </el-table-column>
        </template>
        <template slot="drugClassificationName" slot-scope="{col}">
          <el-table-column :label="col.name" width="80">
            <template slot-scope="{row}">{{ row[col.index] |nameToName }}</template>
          </el-table-column>
        </template>
        <template slot="totalAvailableQty" slot-scope="{col}">
          <el-table-column :label="col.name" width="180">
            <template slot-scope="{row}">{{ row[col.index] |nullToName }}</template>
          </el-table-column>
        </template>
        <template slot="availableQtyType" slot-scope="{col}">
          <el-table-column :label="col.name" width="120">
            <template slot-scope="{row}">{{ row[col.index]|typeToName }}</template>
          </el-table-column>
        </template>
        <template slot="isLimited" slot-scope="{col}">
          <el-table-column :label="col.name">
            <template slot-scope="{row}">{{ row|computeTime }}</template>
          </el-table-column>
        </template>
        <template slot="isPromotion" slot-scope="{col}">
          <el-table-column :label="col.name">
            <template slot-scope="{row}">{{ row[col.index]|numToName }}</template>
          </el-table-column>
        </template>
        <template slot="isControl" slot-scope="{col}">
          <el-table-column :label="col.name">
            <template slot-scope="{row}">{{ row[col.index]|numToName }}</template>
          </el-table-column>
        </template>
        <template slot="isGive" slot-scope="{col}">
          <el-table-column :label="col.name">
            <template slot-scope="{row}">{{ row[col.index]|numToName }}</template>
          </el-table-column>
        </template>
        <template slot="highGross" slot-scope="{col}">
          <el-table-column :label="col.name">
            <template slot-scope="{row}">{{ row[col.index]|numToName2 }}</template>
          </el-table-column>
        </template>
        <template slot="lastDownTime" slot-scope="{col}">
          <el-table-column :label="col.name" width="150">
            <template slot-scope="{row}">{{ row[col.index]|formatTime }}</template>
          </el-table-column>
        </template>
      </xyy-table>
      <detail :status.sync="status" :csu-id="csuId"></detail>
    </template>
  </xyy-list-page>
</template>

<script>
import { getPageInfo } from '@/api/shop';
import search from '../components/search';
import detail from '@/components/ec/product-detail';
export default {
  name: 'Index',
  components: { search, detail },
  filters: {
    numToName: function(value) {
      return parseInt(value) === 1 ? '是' : value === null ? '-' : '否';
    },
    numToName2: function(value) {
      return parseInt(value) === 2 ? '是' : value === null ? '-' : '否';
    },
    typeToName: function(value) {
      return parseInt(value) == 1 ? '真实库存' : value == null ? '-' : '虚拟库存';
    },
    statusToName: function(value) {
      let str = '';
      switch (value) {
        case 1:
          str = '销售中';
          break;
        case 2:
          str = '已售罄';
          break;
        case 4:
          str = '下架';
          break;
        case 6:
          str = '待上架';
          break;
        case 7:
          str = '已录入';
          break;
        case 8:
          str = '待审核';
          break;
        default:
          str = '-';
      }
      return str;
    },
    nameToName:function(value){
       return  value.toLowerCase()=="rx"?'处方药':value
    },
    nullToName:function(value){
      return !value?'-':value
    },
    computeTime(row){
      const now=new Date();
      if(row.purchaseTimeEnd&&row.purchaseTimeStart){
        const purchaseTimeEnd=new Date(row.purchaseTimeEnd);
        const purchaseTimeStart=new Date(row.purchaseTimeStart);
        if(row.limitedQty&&parseInt(row.limitedQty)>0&&((purchaseTimeEnd>=now&&purchaseTimeStart<now)||(row.purchaseTimeStart==null&&row.purchaseTimeEnd==null))){
          return '是'
        }
      }else{
        return '否'
      }

    }
  },
  data() {
    return {
      status: false, // 查看详情弹框状态
      searchdata: {},
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      operation: [
        {
          name: '查看',
          type: 1
        }
      ],
      list: [],
      searchNum: 0,
      col: [
        { index: 'code', name: '条码', resizable: true, width: 150 },
        { index: 'barcode', name: '商品编号', resizable: true, width: 150 },
        { index: 'shopName', name: '店铺名称', resizable: true, width: 300, slot: true },
        { index: 'shopCode', name: '店铺ID', resizable: true, width: 100 },
        { index: 'showName', name: '展示名称', resizable: true, width: 200 },
        // { index: '', name: '销售渠道', resizable: true, width: 200 },
        { index: 'imageUrl', name: '图片', resizable: true, width: 100, slot: true },
        { index: 'spec', name: '规格', resizable: true, width: 150 },
        {
          index: 'manufacturer',
          name: '生产厂家',
          resizable: true,
          width: 150
        },
        { index: 'fob', name: '售价', resizable: true, width: 80 },
        { index: 'companyName', name: '商家', resizable: true, width: 150 },
        {
          index: 'status',
          name: '状态',
          resizable: true,
          width: 150,
          slot: true
        },
        {
          index: 'drugClassificationName',
          name: '药品类型',
          resizable: true,
          width: 80,
          slot:true
        },
        {
          index: 'totalAvailableQty',
          name: '总库存（药帮忙总库存）',
          resizable: true,
          width: 180,
          slot:true
        },
        {
          index: 'availableQty',
          name: '库存（药帮忙库存）',
          resizable: true,
          width: 150
        },
        { index: 'availableQtyType', name: '药帮忙库存类型', resizable: true, width: 100, slot: true },
        {
          index: 'isLimited',
          name: '是否限购',
          resizable: true,
          width: 150,
          slot: true
        },
        {
          index: 'isPromotion',
          name: '是否促销',
          resizable: true,
          width: 150,
          slot: true
        },
        {
          index: 'isControl',
          name: '是否控销',
          resizable: true,
          width: 150,
          slot: true
        },
        {
          index: 'isGive',
          name: '是否赠品',
          resizable: true,
          width: 150,
          slot: true
        },
        {
          index: 'highGross',
          name: '是否高毛',
          resizable: true,
          width: 150,
          slot: true
        },
        { index: 'id', name: 'CSU ID', resizable: true, width: 100 },
        {
          index: 'lastDownTime',
          name: '最后一次下架时间',
          resizable: true,
          width: 150,
          slot:true
        },
        {
          index: 'operation',
          name: '操作',
          resizable: true,
          width: 150,
          operation: true
        }
      ],
      csuId: '', // 商品id
      imgurl: process.env.BASE_IMG_URL + '/ybm/product/min/',
      shopList: []
    };
  },
  methods: {
    handleSearch(data) {
      this.searchNum++;
      this.listQuery.pageNum = 1;
      this.listQuery.page = 1;
      this.searchdata = Object.assign(data, this.listQuery);
      getPageInfo(data).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }

        if (res.data.shop) {
          this.shopList = res.data.shop;
        }
        if (res.data.csu) {
          const { list, pageNum, pageSize, total } = res.data.csu;
          this.list = list;
          this.listQuery = {
            ...this.listQuery,
            pageNum,
            pageSize,
            total
          };
          return;
        }
        this.$XyyMessage.success('暂时无数据');
      });
    },
    getList(listQuery) {
      if (this.searchNum == 0) return;
      const { page, pageSize } = listQuery;
      this.listQuery.pageNum = page;
      this.searchdata = Object.assign(this.searchdata, this.listQuery);
      getPageInfo(this.searchdata).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        if (res.data.shop) {
          this.shopList = res.data.shop;
        }
        if (res.data.csu) {
          const { list, pageNum, pageSize, total } = res.data.csu;
          this.list = list;
          this.listQuery = {
            ...this.listQuery,
            pageNum,
            pageSize,
            total
          };
          return;
        }
        this.$XyyMessage.success('暂时无数据');
      });
    },
    operationClick(type, row) {
      if (type === 1) {
        this.status = true;
        this.csuId = String(row.id);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
  /deep/ .page-body .el-table{

    .table-row{
      height: auto !important;
    }

    tr td{
      height: auto !important;

      .cell{
        height: auto;
        line-height: normal;
        white-space: normal;
      }
    }
  }
</style>

<style lang="scss" scoped>
/deep/.pagination-container {
  margin-bottom: 33px;
}
/deep/.el-form-item{
  margin-bottom:10px;
}
</style>
