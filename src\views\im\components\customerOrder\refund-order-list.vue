<template>
  <div class="refund-order-list-component-container">
    <ul class="order-list">
      <li v-for="item in refundList" :key="item.refundOrderNo">
        <header class="box row-between column-center">
          <p>
            <em>退款编号:</em>&nbsp;
            <span>{{ item.refundOrderNo }}</span>
          </p>
          <i :class="'status-4'">{{ item.auditStateStr }}</i>
        </header>
        <div class="box row-start order-list-wrapper">
          <a class="box row-center img" href="javascript:void(0);">
            <img :src="item.imageUrl?item.imageUrl:defaultUrl" :alt="item.merchantName" />
          </a>
          <div class="detail">
            <header class="box row-between column-start">
              <h2 class="ellipsis">
                <el-tooltip
                  :content="item.merchantName"
                  class="item"
                  effect="dark"
                  placement="top-start"
                >
                  <span>{{ item.merchantName }}</span>
                </el-tooltip>
              </h2>
              <el-button
                type="text"
                style="width: 42px; font-size: 12px;"
                @click="checkRefundDetail(item)"
              >
                查看
                <i class="el-icon-arrow-right" style="padding: 2px 2px;"></i>
              </el-button>
            </header>
            <time>{{ item.createTime }}</time>
            <p class="box row-start">
              <span>共{{ item.refundVarietyNum }}种商品</span>
              <span>
                退款总额：¥
                <i>{{ item.refundFee }}</i>
              </span>
            </p>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: '',
  components: {},
  filters: {},
  props: {
    refundList: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      defaultUrl: '/css/images/default.png',
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    /**
     * 查看订单详情
     */
    checkRefundDetail(order) {
      order && window.open(order.refundOrderDetailUrl, '_blank');
    },
  },
};
</script>

<style lang="scss" scoped>
@import '../../../../styles/custome-order.css';
.refund-order-list-component-container {
  width: 100%;

  .order-list li:first-child {
    margin-top: 0;
  }

  .order-list li:last-child {
    margin-bottom: 0;
  }
}
</style>