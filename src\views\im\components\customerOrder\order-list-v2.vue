<template>
  <div class="order-list-container">
    <template v-for="(orderItem, orderIndex) in orderList">
      <div class="order-item" :key="orderItem.orderNo">

        <div class="order-info">

          <div class="row-status">
            <template v-if="orderType == '1'">
              <span :class="['status-txt', 'order-status-' + orderItem.status]">{{ orderItem.statusName }}</span>
            </template>
            <template v-else-if="orderType == '2'">
              <span :class="['status-txt', 'refund-status-' + orderItem.auditState]">{{ orderItem.auditStateStr
                }}</span>
            </template>

            <span class="order-preview" @click="handleOrderPreview(orderItem, orderIndex)">查看<img class="p-arrow"
                src="@/assets/common/icon-arrow-down.png" /></span>
          </div>


          <template v-if="orderType == '1'">
            <div class="row-line">
              <span class="title">订单编号：</span>
              <span class="context">{{ orderItem.orderNo }}</span>
            </div>
          </template>
          <template v-else-if="orderType == '2'">
            <div class="row-line">
              <span class="title">退款编号：</span>
              <span class="context">{{ orderItem.refundOrderNo }}</span>
            </div>
          </template>


          <div class="row-between">
            <div class="col-left" v-if="orderType == '2'">
              <span class="title">药店名称：</span>
              <span class="context" :title="orderItem.merchantName">{{ orderItem.merchantName }}</span>
            </div>
            <div class="col-left" v-if="orderType == '1'">
              <span class="title">商家名称：</span>
              <span class="context" :title="orderItem.companyName">{{ orderItem.companyName }}</span>
            </div>

            <template v-if="orderType == '1'">
              <div class="col-right">
                <span class="title">物流信息：</span>
                <span class="context">
                  <div class="buttons">
                    <div class="button" @click="handleOrderLogisticsPreview(orderItem, orderIndex)">查看</div>
                  </div>
                </span>
              </div>
            </template>
          </div>


          <div class="row-between">
            <template v-if="orderType == '1'">
              <div class="col-left">
                <span class="title">下单时间：</span>
                <span class="context">{{ orderItem.createTime }}</span>
              </div>
            </template>
            <template v-else-if="orderType == '2'">
              <div class="col-left">
                <span class="title">申请时间：</span>
                <span class="context">{{ orderItem.createTime }}</span>
              </div>
            </template>

            <template v-if="orderType == '1'">
              <div class="col-right">
                <span class="title">发票信息：</span>
                <span class="context">
                  <div class="buttons">
                    <div class="button" @click="handleOrderInvoicePreview(orderItem, orderIndex)">查看</div>
                    <div class="button" v-if="operable" @click="handleOrderInvoiceSend(orderItem, orderIndex)">发送</div>
                  </div>
                </span>
              </div>
            </template>
          </div>


          <div class="row-between">
            <template v-if="orderType == '1'">
              <div class="col-left">
                <span class="title">商品种数：</span>
                <span class="context">{{ orderItem.varietyNum }}</span>
              </div>
            </template>
            <template v-else-if="orderType == '2'">
              <div class="col-left">
                <span class="title">商品种数：</span>
                <span class="context">{{ orderItem.refundVarietyNum }}</span>
              </div>
            </template>

            <template v-if="orderType == '1'">
              <div class="col-right">
                <span class="title">订单备注：</span>
                <span class="context">
                  <div class="buttons">
                    <div class="button" @click="handleOrderRemarkPreview(orderItem, orderIndex)">查看</div>
                    <!-- 自营客服不能添加备注 -->
                    <!-- <div class="button" @click="handleOrderRemarkAdd(orderItem, orderIndex)">添加</div> -->
                  </div>
                </span>
              </div>
            </template>
          </div>


          <template v-if="orderType == '1'">
            <div class="row-line">
              <span class="title">实付总额：</span>
              <span class="context txt-light">¥{{ orderItem.money }}</span>
            </div>
          </template>
          <template v-else-if="orderType == '2'">
            <div class="row-line" style="margin-bottom: 0;">
              <span class="title">退款总额：</span>
              <span class="context txt-light">¥{{ orderItem.refundFee }}</span>
            </div>
          </template>

          <template v-if="orderType == '1'">
            <div class="row-handle">
              <span :class="['h-expand', orderItem.isExpand ? 'is-expand' : '']"
                @click="handleOrderExpand(orderItem, orderIndex)">{{ orderItem.isExpand ? '收起' : '展开'
                }}<img class="h-arrow" src="@/assets/common/icon-arrow-down.png" /></span>
              <div class="h-send" v-if="operable" @click="handleOrderSend(orderItem, orderIndex)">发送</div>
            </div>
          </template>

        </div>

        <template v-if="orderType == '1'">
          <div class="order-product" v-if="orderItem.isExpand">
            <template v-if="orderItem.productList.length">
              <div class="order-product-preview">
                <template v-for="(productItem, productIndex) in orderItem.productList">
                  <div class="product-item" :key="orderItem.orderNo + productItem.skuId">
                    <img class="pd-preview-image" :src="productItem.imageUrl" />
                    <div class="pd-preview-info">
                      <div class="pd-pr-inf-row">
                        <span class="pd-pr-inf-ttl">商品编号：</span>
                        <span class="pd-pr-inf-ctt">{{ productItem.barCode }}</span>
                      </div>
                      <div class="pd-pr-inf-row">
                        <span class="pd-pr-inf-ttl">订单编号：</span>
                        <span class="pd-pr-inf-ctt">{{ orderItem.orderNo }}</span>
                      </div>
                      <div class="pd-pr-inf-row">
                        <span class="pd-pr-inf-ttl">商品名称：</span>
                        <span class="pd-pr-inf-ctt" :title="productItem.productName">{{ productItem.productName
                          }}</span>
                      </div>
                      <div class="pd-pr-inf-row">
                        <span class="pd-pr-inf-ttl">规格：</span>
                        <span class="pd-pr-inf-ctt">{{ productItem.spec }}</span>
                      </div>
                      <div class="pd-pr-inf-row">
                        <span class="pd-pr-inf-ttl">生产厂家：</span>
                        <span class="pd-pr-inf-ctt" :title="productItem.manufacturer">{{ productItem.manufacturer
                          }}</span>
                      </div>
                      <div class="pd-pr-inf-row">
                        <span class="pd-pr-inf-ttl">发货数量：</span>
                        <span class="pd-pr-inf-ctt">{{ productItem.productAmount }}</span>
                      </div>
                      <div class="pd-pr-inf-row">
                        <span class="pd-pr-inf-ttl">实付金额：</span>
                        <span class="pd-pr-inf-ctt txt-light">¥{{ productItem.realPayAmount }}</span>
                      </div>
                    </div>
                    <div class="pd-pre-btn-send" v-if="operable"
                      @click="handleProductSend(orderItem, orderIndex, productItem, productIndex)">发送</div>
                  </div>
                </template>
              </div>
            </template>
            <template v-else>
              <div class="empty-data-list">暂无数据</div>
            </template>
          </div>
        </template>

      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: "OrderListV2",
  props: {
    orderListName: {
      require: true,
      type: String,
      default: '',
    },
    orderList: {
      type: Array,
      default: () => [],
    },
    operable: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    orderType: function () {
      let type = '' // 1:销售订单;2:退款单
      switch (this.orderListName) {
        case 'recent_list':
        case 'unfinished_list':
        case 'finished_list':
        case 'closed_list':
          type = '1'
          break;
        case 'refund_list':
          type = '2'
          break;
      }
      return type
    }
  },
  data() {
    return {}
  },
  methods: {
    // 查看订单
    handleOrderPreview(orderItem, orderIndex) {
      this.$emit('order-event', {
        handle: 'order-preview',
        orderListName: this.orderListName,
        orderItem,
        orderIndex
      })
    },

    // 查看物流
    handleOrderLogisticsPreview(orderItem, orderIndex) {
      this.$emit('order-event', {
        handle: 'order-logistics-preview',
        orderListName: this.orderListName,
        orderItem,
        orderIndex
      })
    },

    // 查看发票
    handleOrderInvoicePreview(orderItem, orderIndex) {
      this.$emit('order-event', {
        handle: 'order-invoice-preview',
        orderListName: this.orderListName,
        orderItem,
        orderIndex
      })
    },

    // 发送发票
    handleOrderInvoiceSend(orderItem, orderIndex) {
      this.$emit('order-event', {
        handle: 'order-invoice-send',
        orderListName: this.orderListName,
        orderItem,
        orderIndex
      })
    },

    // 查看备注
    handleOrderRemarkPreview(orderItem, orderIndex) {
      this.$emit('order-event', {
        handle: 'order-remark-preview',
        orderListName: this.orderListName,
        orderItem,
        orderIndex
      })
    },

    // 添加备注
    handleOrderRemarkAdd(orderItem, orderIndex) {
      this.$emit('order-event', {
        handle: 'order-remark-add',
        orderListName: this.orderListName,
        orderItem,
        orderIndex
      })
    },

    // 展开&收起
    handleOrderExpand(orderItem, orderIndex) {
      this.$emit('order-event', {
        handle: 'order-expand',
        orderListName: this.orderListName,
        orderItem,
        orderIndex
      })
    },

    // 发送订单
    handleOrderSend(orderItem, orderIndex) {
      this.$emit('order-event', {
        handle: 'order-send',
        orderListName: this.orderListName,
        orderItem,
        orderIndex
      })
    },

    // 发送商品
    handleProductSend(orderItem, orderIndex, productItem, productIndex) {
      this.$emit('order-event', {
        handle: 'order-product-send',
        orderListName: this.orderListName,
        orderItem,
        orderIndex,
        productItem,
        productIndex
      })
    }
  },
}
</script>

<style lang="scss" scoped>
@import '@/utils/order-status.scss';

.order-list-container {
  width: 100%;
  padding: 12px 12px 0;
  box-sizing: border-box;
  background-color: #fff;

  .order-item {
    width: 100%;
    margin-bottom: 12px;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;

    .order-info {
      width: 100%;
      padding: 12px;
      box-sizing: border-box;
      background-color: #F9F9F9;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: stretch;

      .row-status {
        width: 100%;
        margin-bottom: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .status-txt {
          line-height: 1;
          padding: 2px 4px;
          box-sizing: border-box;
          border-radius: 2px;
          border: 1px solid #acacac;
          background-color: #f2f2f2;
          color: #444444;
          font-size: 12px;
          font-weight: 400;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .order-preview {
          line-height: 1;
          cursor: pointer;
          user-select: none;
          color: #444444;
          font-size: 12px;
          font-weight: 400;
          display: flex;
          justify-content: flex-end;
          align-items: center;

          .p-arrow {
            width: 12px;
            height: 12px;
            margin-left: 2px;
            transform: rotate(-90deg);
          }
        }
      }

      .title {
        flex-shrink: 0;
        width: 66px;
        line-height: 1;
        color: #888888;
        font-size: 13px;
        font-weight: 400;
      }

      .context {
        flex-grow: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #222222;
        font-size: 13px;
        font-weight: 400;

        &.txt-light {
          color: #FF2121;
        }
      }

      .buttons {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .button {
          line-height: 1;
          padding: 4px 12px;
          color: #222222;
          font-size: 12px;
          font-weight: 400;
          border: 1px solid #D4D9DD;
          border-radius: 2px;
          background-color: #ffffff;
          cursor: pointer;
          margin: 0 8px 0 0;
          display: flex;
          justify-content: center;
          align-items: center;

          &:hover {
            color: #3B95A8;
            border-color: #3B95A8;
            // background-color: #dae9ec;
          }
        }
      }

      .row-line {
        width: 100%;
        margin-bottom: 8px;
        overflow: hidden;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }

      .row-between {
        width: 100%;
        margin-bottom: 8px;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .col-left,
        .col-right {
          flex: 1;
          overflow: hidden;
          display: flex;
          justify-content: flex-start;
          align-items: center;
        }
      }

      .row-handle {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .h-expand {
          color: #444444;
          font-size: 12px;
          font-weight: 400;
          cursor: pointer;
          user-select: none;
          display: flex;
          justify-content: flex-end;
          align-items: center;

          .h-arrow {
            margin-left: 2px;
            width: 12px;
            height: 12px;
            transform: rotate(0deg);
          }

          &.is-expand {
            .h-arrow {
              transform: rotate(-180deg);
            }
          }
        }

        .h-send {
          line-height: 1;
          margin-left: 12px;
          padding: 6px 18px;
          border-radius: 2px;
          border: 1px solid #3B95A8;
          color: #3B95A8;
          font-size: 12px;
          font-weight: 400;
          cursor: pointer;
          user-select: none;
          display: flex;
          justify-content: center;
          align-items: center;

          &:hover {
            background-color: #3B95A8;
            color: #ffffff;
          }

          &:active {
            opacity: 0.8;
          }
        }
      }
    }

    .order-product {
      width: 100%;
      border-top: 1px solid #D8D8D8;
      background-color: #F9F9F9;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: stretch;

      .empty-data-list {
        width: 100%;
        padding: 20px 0;
        text-align: center;
        color: #888888;
        font-size: 14px;
        font-weight: 400;
      }

      .order-product-preview {
        flex-grow: 1;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;

        .product-item {
          width: 100%;
          padding: 12px;
          border-bottom: 1px solid #D8D8D8;
          box-sizing: border-box;
          position: relative;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;

          &:last-child {
            border-bottom: 0;
          }

          .pd-preview-image {
            flex-shrink: 0;
            width: 64px;
            height: 64px;
            border-radius: 4px;
            margin-right: 12px;
          }

          .pd-preview-info {
            flex-grow: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: stretch;

            .pd-pr-inf-row {
              flex-grow: 1;
              overflow: hidden;
              margin: 0 0 8px 0;
              display: flex;
              justify-content: flex-start;
              align-items: center;

              &:last-child {
                margin-bottom: 0;
              }

              .pd-pr-inf-ttl {
                flex-shrink: 0;
                width: 60px;
                line-height: 1;
                color: #888888;
                font-size: 12px;
                font-weight: 400;
              }

              .pd-pr-inf-ctt {
                flex-grow: 1;
                line-height: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #222222;
                font-size: 12px;
                font-weight: 400;

                &.txt-light {
                  color: #FF2121;
                }
              }

            }
          }

          .pd-pre-btn-send {
            line-height: 1;
            padding: 6px 18px;
            border-radius: 2px;
            border: 1px solid #3B95A8;
            color: #3B95A8;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
            user-select: none;
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            right: 12px;
            bottom: 12px;

            &:hover {
              background-color: #3B95A8;
              color: #ffffff;
            }

            &:active {
              opacity: 0.8;
            }
          }
        }
      }
    }
  }
}
</style>