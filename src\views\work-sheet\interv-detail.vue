<template>
  <div>
    <div class="page">
      <el-row type="flex" class="sheeTitle" justify="space-between" align="middle">
        <span>
          <span
            v-show="baseWorkorderInfo.form_type_name && baseWorkorderInfo.workorder_num"
          >{{ baseWorkorderInfo.form_type_name }}-{{ baseWorkorderInfo.workorder_num }}</span>
          <i>{{ baseWorkorderInfo.current_state | statusFormat }}</i>
        </span>
        <span v-if="tags.length" class="order-tag">
          当前标签：
          <el-popover placement="right" width="400" trigger="click">
            <el-button
              v-if="orderTag.tagId"
              size="mini"
              class="tag-btn"
              type="danger"
              icon="el-icon-delete"
              @click="clearTag()"
            >清除标签</el-button>
            <el-button
              v-for="(tag,key) in tags.filter(item=>item.id!=orderTag.tagId)"
              :key="key"
              size="mini"
              class="tag-btn"
              @click="switchTag(tag)"
            >{{ tag.tagName }}</el-button>
            <el-button
              v-if="!orderTag.tagId"
              slot="reference"
              :disabled="isDisabledTag"
              type="info"
              size="mini"
              class="tag-btn"
              icon="el-icon-plus"
              circle
            ></el-button>
            <el-button
              v-if="orderTag.tagId"
              slot="reference"
              :disabled="isDisabledTag"
              size="mini"
              class="tag-btn"
            >{{ orderTag.tagName }}</el-button>
          </el-popover>
        </span>
        <el-row type="flex">
          <!--  -->
          <!--          v-if="baseWorkorderInfo.invalid"-->
          <!--          baseWorkorderInfo.current_state==3-->
          <xyy-button
            v-if="baseWorkorderInfo.invalid"
            icon-class="restart2"
            style="margin-right:10px"
            type="normal"
            @click="reOnline"
          >再次发起
          </xyy-button>
          <xyy-button
            v-if="checkPermission('btn:cs:workordertransfer') &&baseWorkorderInfo.current_state!='3'&& baseWorkorderInfo.current_state!='4'&&baseWorkorderInfo.current_state!='5'&&baseWorkorderInfo.current_state!='2' "
            icon-class="assSheet"
            style="margin-right:10px"
            type="normal"
            @click=" sheetAssignment"
          >转移工单
          </xyy-button>
          <div v-if="baseWorkorderInfo.send_back">
            <xyy-button
              v-if="baseWorkorderInfo.send_back"
              type="normal"
              icon-class="backh"
              style="margin-right:10px"
              @click="returnSheetHid = true"
            >退回工单
            </xyy-button>
          </div>
          <div v-if="baseWorkorderInfo.invalid">
            <xyy-button type="normal" icon-class="cancel" @click="updateWorkOrderToVoid">作废</xyy-button>
          </div>
          <xyy-button
            v-if="checkPermission('btn:cs:workorderrestart') && baseWorkorderInfo.current_state=='2'"
            icon-class="restart2"
            type="normal"
            @click="sheetResetShow = true"
          >重启工单
          </xyy-button>
          <!--  -->
          <xyy-button class="el-icon-refresh" type="normal" @click="mountedHandle">刷新</xyy-button>
        </el-row>
      </el-row>
      <el-divider></el-divider>
      <el-row type="flex" justify="start" align="top">
        <div class="detailInstr">
          <el-button
            v-if="checkPermission('btn:cs:workorderedit')"
            type="text"
            icon="el-icon-edit-outline"
            class="editFrom"
            @click="editFromStart()"
          >编辑
          </el-button>
          <open-info
            :clear-empty="true"
            :data-info="extendWorkorderFieldinfo"
            :lay-datas="layDatas"
            :info="true"></open-info>
          <div
            v-if="sheetPre.length>0&&baseWorkorderInfo.reply_submit||baseWorkorderInfo.closed"
            class="replyPlate"
          >
            <div class="title">回复工单</div>
            <div class="reply-form-content">
              <template v-if="baseWorkorderInfo.reply_submit||baseWorkorderInfo.closed">
                <layout :lay-datas="replyLayDatas" :col-datas="sheetPre">
                  <template slot="col" slot-scope="{ arr,row,col }">
                    <div class="template-col">
                      <!-- 有关联关系 -->
                      <div
                        v-if="arr[row][col]&&arr[row][col].conditionList && arr[row][col].conditionList.length > 0"
                      >
                        <preview
                          v-show="getAuthTypeStatus(arr[row][col])"
                          :ref="'ref'+arr[row][col].fieldCode"
                          :keys="arr[row][col].fieldType"
                          :preview="arr[row][col]"
                          :read="conditionAA(arr[row][col]) === 1"
                          :requirs="getAuthTypeStatus(arr[row][col])?requirsConditionAAReply(arr[row][col])===1:false"
                          :search-result="searchResult"
                          refs="preSheet"
                          class="opinionHandle"
                          @keywordSearch="keywordToSearch"
                          @keywordSelected="keywordToSelected"
                        />
                      </div>
                      <preview
                        v-else-if="arr[row][col]"
                        v-show="arr[row][col].authType"
                        :ref="'ref'+arr[row][col].fieldCode"
                        :keys="arr[row][col].fieldType"
                        :preview="arr[row][col]"
                        :read="arr[row][col].authType===1"
                        :requirs="getAuthTypeStatus(arr[row][col])?requirsConditionAAReply(arr[row][col])===1:false"
                        :search-result="searchResult"
                        refs="preSheet"
                        class="opinionHandle"
                        @keywordSearch="keywordToSearch"
                        @keywordSelected="keywordToSelected"
                      />
                    </div>
                  </template>
                </layout>
                <div class="submit">
                  <!-- 节点信息后台未定义 toclose -->
                  <el-button
                    v-if="baseWorkorderInfo.reply_submit"
                    type="primary"
                    @click="submitReply"
                  >提交回复
                  </el-button>
                  <el-button
                    v-if="baseWorkorderInfo.closed"
                    type="primary"
                    @click="submitReply('closeNode')"
                  >关闭工单
                  </el-button>
                </div>
              </template>
            </div>
          </div>

          <!--    -->
          <div>
            <!-- 催单接口为定义 -->
            <el-button
              v-if="baseWorkorderInfo.urge && baseWorkorderInfo.current_state!=='2'"
              type="primary"
              @click="urgeWorkCheck"
            >催单
            </el-button>
          </div>
        </div>
        <!-- v-if="checkPermission('btn:cs:workorderedit')" -->
        <el-divider direction="vertical"></el-divider>
        <el-col class="detailShow">
          <processs
            ref="processs"
            :id="workorderId"
            :attr="baseWorkorderInfo"
            @publishCallback="issueAppendHid = true"
          ></processs>
        </el-col>
      </el-row>
    </div>
    <!-- 工单重启 -->
    <div class="sheetReset">
      <el-dialog
        :visible.sync="sheetResetShow"
        title="工单重启"
        width="850px"
        top="0"
        @open="restartOpen"
      >
        <el-checkbox v-model="useAssign">重启时使用工单转移</el-checkbox>
        <div v-show="useAssign" class="assign-inner-content">
          <el-radio-group v-model="assignType" @change="resetAssign">
            <el-radio label="people">转移给人</el-radio>
            <el-radio label="node">转移给节点</el-radio>
          </el-radio-group>

          <el-form
            ref="assignForm"
            :inline="true"
            :model="assignForm"
            :rules="assignRules"
            label-width="80px"
            class="assign-form"
          >
            <el-form-item label="节点" class="whole-width" prop="assignNode">
              <el-select
                v-model="assignForm.assignNode"
                placeholder="请选择节点"
                filterable
                @change="loadAssignGroups"
              >
                <el-option
                  v-for="item in assignNodes"
                  :key="item.nodeId"
                  :label="item.nodeName"
                  :value="item.nodeId"
                />
              </el-select>
            </el-form-item>
            <div v-show="assignType==='people'">
              <el-form-item
                :rules="assignType==='people'?[
                  { required: true, message: '请选择用户组', trigger: 'change' }
                ]:[]"
                label="用户组"
                prop="assignUsergroup"
              >
                <el-select
                  v-model="assignForm.assignUsergroup"
                  placeholder="请选择用户组"
                  @change="loadAssignMembers"
                >
                  <el-option
                    v-for="item in assignGroups"
                    :key="item.userGroupId"
                    :label="item.userGroupName"
                    :value="item.userGroupId"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                :rules="assignType==='people'?[
                  { required: true, message: '请选择组内成员', trigger: 'change' }
                ]:[]"
                label="组内成员"
                prop="assignMembers"
              >
                <el-select v-model="assignForm.assignMembers" placeholder="请选择组内成员">
                  <el-option
                    v-for="item in assignUsers"
                    :key="item.userId"
                    :label="item.nickname"
                    :value="item.userId"
                  />
                </el-select>
              </el-form-item>
            </div>
          </el-form>
        </div>
        <el-form ref="restartForm" :model="restartForm" class="restart-form">
          <el-form-item
            :rules="[
              { required: true, message: '请输入重启原因', trigger: 'blur' }
            ]"
            label="重启原因"
            prop="restartReason"
          >
            <el-input
              v-model="restartForm.restartReason"
              type="textarea"
              placeholder="请输入原因(必填)"
              maxlength="200"
              show-word-limit
              resize="none"
            ></el-input>
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button @click="sheetResetShow = false">取 消</el-button>
          <el-button type="primary" @click="handleSheetRese()">确 定</el-button>
        </span>
      </el-dialog>
    </div>
    <!-- 催单说明 -->
    <el-dialog :visible.sync="urgeWorkHid" class="urgeStyle" title="催单" width="40%">
      <el-row class="urgeTitle">
        工单所处当前节点：{{ currentNodeName }}
        <span>处理人：{{ currentProcessPersonName ? currentProcessPersonName : '无' }}</span>
      </el-row>
      <el-input
        v-model="urgeWorkCon"
        type="textarea"
        placeholder="请输入催单内容"
        maxlength="200"
        show-word-limit
        resize="none"
        class="urgeCon"
      ></el-input>
      <el-row class="urgeTip">通知方式：短信</el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="urgeWorkCalce">取 消</el-button>
        <el-button type="primary" @click="checkTimer(urgeWork,'time')()">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 追加说明 -->
    <el-dialog :visible.sync="issueAppendHid" title="追加说明" width="40%" class="instructions">
      <el-input
        v-model="appendNotesCon"
        type="textarea"
        placeholder="请输入"
        maxlength="500"
        show-word-limit
        resize="none"
        class="issueAppend"
      ></el-input>
      <div class="previewNewInner">
        <span class="shangchuanfujian">上传附件</span>
        <el-upload
          ref="myUploader"
          :action="urlAction"
          :limit="fileNums"
          :with-credentials="true"
          :before-upload="beforeUpload"
          :on-exceed="validateNums"
          :on-progress="handleProgress"
          :on-success="handleSuccess"
          :on-error="handleError"
          :class="[{completed:uploadList.length===fileNums}, previewNew ]"
          name="files"
          multiple
          list-type="picture-card"
          accept=".gif, .bmp, .jpg, .jpeg, .png, .pdf, .zip, .rar, .mp4, .avi, .doc, .docx, .ppt, .pptx, .xls, .xlsx, .txt"
          class="upload-file-box"
        >
          <!--<i slot="default" class="el-icon-plus" @click="uploadStart"></i>-->
          <el-button slot="trigger" size="small" type="primary" @click="uploadStart">
            <span class="el-icon-upload2"></span>上传附件
          </el-button>
          <div slot="file" slot-scope="{file}">
            <file-preview
              :file="file"
              :percent="file.percent?file.percent:0"
              :editable="true"
              @abortCallback="abortFile(file)"
              @delCallback="delFile(file)"
            />
          </div>
        </el-upload>
        <p style="color:#E6A23C;margin: 0; margin-top: 14px;">
          <span class="el-icon-warning"></span>
          <span
            style="font-size: 12px"
          >附件请小于500MB，格式仅限gif/bmp/jpg/jpeg/png/pdf/zip/rar/mp4/avi/doc/docx/xls/xlsx/ppt/pptx/txt</span>
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="debounce(issueAppend(),1000)">发布</el-button>
      </span>
    </el-dialog>
    <!-- 退回工单 -->
    <el-dialog :visible.sync="returnSheetHid" title="退回工单" width="40%">
      <div class="returnSheetRadio">
        <el-radio v-model="returnSheetRadio" label="1">退回上一环节</el-radio>
        <label>/</label>
        <el-radio v-model="returnSheetRadio" label="0">退回发起人</el-radio>
      </div>
      <el-input
        v-model="returnSheetCon"
        type="textarea"
        placeholder="请输入原因(非必填)"
        maxlength="200"
        show-word-limit
        resize="none"
        class="returnSheet"
      ></el-input>
      <div class="returnSheetTip">{{ returnSheetTip[returnSheetRadio] }}</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="returnSheetHid = false">取 消</el-button>
        <el-button type="primary" @click="returnSheetBtn">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 编辑表单 -->
    <el-dialog
      ref="editBox"
      :visible.sync="editForm"
      :title="baseWorkorderInfo.invalid&&againRestart?'再次发起':'编辑表单'"
      width="80%"
      custom-class="systemField-dialog"
      class="editBox"
      @close="closeEditbox"
    >
      <div v-if="!baseWorkorderInfo.invalid" class="tips">注：对工单进行二次编辑只会修改工单内容，并不会影响工单既定流程
      </div>
      <div class="editTitle">{{ baseWorkorderInfo.form_type_name }}</div>
      <div v-if="editfromData.length !==0" class="editInput">
        <el-row>
          <el-col :span="9">
            <div class="systemField-form">
              <header>系统字段</header>

              <div v-for="(item,index) in systemArr" v-if="item.systemFieldFlag==0" :key="index">
                <!-- 有关联关系 -->
                <div v-if="item.conditionList && item.conditionList.length > 0">
                  <preview
                    v-show="editGetAuthTypeStatus(item)"
                    :ref="'edite'+item.fieldCode"
                    :keys="item.fieldType"
                    :preview="item"
                    :read="editConditionAA(item) === 1"
                    :requirs="getAuthTypeStatus(item)?requirsConditionAA(item)===1:false"
                    :sheet-type-val="baseWorkorderInfo.form_type_id"
                    :workorder-no="baseWorkorderInfo.workorder_num"
                    :sheet-preview-all="editfromData"
                    :search-result="searchResult"
                    refs="preSheet"
                    class="opinionHandle"
                    @keywordSearch="keywordToSearch"
                    @keywordSelected="keywordToSelected"
                    @handleProblemClass="handleSelVal"
                    @queryOrderInfo="queryOrderInfo"
                    @onCloseRepeatWorksheetDialog="onCloseRepeatWorksheetDialog"
                  />
                </div>
                <preview
                  v-else
                  v-show="item.authType"
                  :ref="'edite'+item.fieldCode"
                  :keys="item.fieldType"
                  :preview="item"
                  :read="item.authType===1"
                  :requirs="getAuthTypeStatus(item)?requirsConditionAA(item)===1:false"
                  :sheet-type-val="baseWorkorderInfo.form_type_id"
                  :workorder-no="baseWorkorderInfo.workorder_num"
                  :sheet-preview-all="editfromData"
                  :search-result="searchResult"
                  refs="preSheet"
                  class="opinionHandle"
                  @keywordSearch="keywordToSearch"
                  @keywordSelected="keywordToSelected"
                  @handleProblemClass="handleSelVal"
                  @queryOrderInfo="queryOrderInfo"
                  @onCloseRepeatWorksheetDialog="onCloseRepeatWorksheetDialog"
                />
              </div>
            </div>
          </el-col>
          <el-col :span="15">
            <div class="custome-form custome-form_list">
              <header>{{ baseWorkorderInfo.form_name }}</header>
              <div class="custom-content">
                <layout :lay-datas="layDatas" :col-datas="customeArr">
                  <template slot="col" slot-scope="{ arr,row,col }">
                    <div class="template-col">
                      <div
                        v-if="arr[row][col]&&arr[row][col].conditionList && arr[row][col].conditionList.length > 0"
                      >
                        <preview
                          v-if="editGetAuthTypeStatus(arr[row][col])"
                          :keys="arr[row][col].fieldType"
                          :preview="arr[row][col]"
                          :ref="'edite'+arr[row][col].fieldCode"
                          :read="editConditionAA(arr[row][col]) === 1"
                          :requirs="getAuthTypeStatus(arr[row][col])?requirsConditionAA(arr[row][col])===1:false"
                          :show-popo="true"
                          :sheet-type-val="baseWorkorderInfo.form_type_id"
                          :workorder-no="baseWorkorderInfo.workorder_num"
                          :sheet-preview-all="editfromData"
                          :search-result="searchResult"
                          refs="preSheet"
                          class="opinionHandle"
                          @keywordSearch="keywordToSearch"
                          @keywordSelected="keywordToSelected"
                          @handleProblemClass="handleSelVal"
                          @queryOrderInfo="queryOrderInfo"
                          @onCloseRepeatWorksheetDialog="onCloseRepeatWorksheetDialog"
                        ></preview>
                      </div>
                      <preview
                        v-else-if="arr[row][col]"
                        v-show="arr[row][col].authType"
                        :ref="'edite'+arr[row][col].fieldCode"
                        :keys="arr[row][col].fieldType"
                        :preview="arr[row][col]"
                        :read="arr[row][col].authType===1"
                        :requirs="getAuthTypeStatus(arr[row][col])?requirsConditionAA(arr[row][col])===1:false"
                        :show-popo="true"
                        :sheet-type-val="baseWorkorderInfo.form_type_id"
                        :workorder-no="baseWorkorderInfo.workorder_num"
                        :sheet-preview-all="editfromData"
                        :search-result="searchResult"
                        refs="preSheet"
                        class="opinionHandle"
                        @keywordSearch="keywordToSearch"
                        @keywordSelected="keywordToSelected"
                        @handleProblemClass="handleSelVal"
                        @queryOrderInfo="queryOrderInfo"
                        @onCloseRepeatWorksheetDialog="onCloseRepeatWorksheetDialog"
                      />
                    </div>
                  </template>
                </layout>
              </div>
            </div>
          </el-col>
          <!-- </el-col> -->
        </el-row>
      </div>
      <div class="dialogFooterOuter">
        <div slot="footer" class="dialog-footer">
          <el-button
            v-if="baseWorkorderInfo.invalid&&againRestart"
            type="primary"
            @click="checkTimer(workorderLocationCheck,'time2')()"
          >发起
          </el-button>
          <el-button @click="editForm = false">取 消</el-button>
          <!-- 编辑保存时需校验重复工单，@click="updateField()" -->
          <el-button v-if="!againRestart" type="primary" @click="workorderRepeateCheck">保存修改</el-button>
        </div>
      </div>
    </el-dialog>
    <!-----客户所在地验证-----开始----->
    <el-dialog
      :visible.sync="repeatWorksheet.addSheetCheckHid"
      custom-class="addSheetCheckStyle"
      title="提示"
      width="400"
    >
      <span>
        近期有内容相似的工单已被创建，工单编号：
        <span
          class="worderId"
          style="cursor: pointer"
          @click="goDetail(repeatWorksheet.workorderId)"
        >{{ repeatWorksheet.workorderNum }}</span> 是否仍要发起
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="repeatWorksheet.addSheetCheckHid = false">取 消</el-button>
        <el-button type="primary" @click="checkTimer(restart,'time4')()">继续发起</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :visible.sync="locationStatus"
      custom-class="addSheetCheckStyle"
      title="提示"
      width="400"
    >
      <span>{{ locationMsg }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="checkTimer(workorderRepeateCheck,'time3')()">继续发起</el-button>
        <el-button type="primary" @click="locationStatus = false">去核对</el-button>
      </span>
    </el-dialog>
    <!-----客户所在地验证-----结束----------->
    <!-- 工单转移 -->
    <el-row class="assignmentSheet">
      <el-dialog
        :visible.sync="assignmentDialog"
        title="工单转移"
        width="400px"
        top="0"
        @close="assCacel"
      >
        <el-row class="sheetNumber">
          <el-radio v-model="assRadioSign" label="people" @change="assRadioChange">转移给人</el-radio>
          <el-radio v-model="assRadioSign" label="node" @change="assRadioChange">转移给节点</el-radio>
        </el-row>
        <el-form
          ref="assRuleForm"
          :model="assRuleForm"
          :rules="assRules"
          label-width="88px"
          class="demo-ruleForm"
        >
          <el-form-item label="节点" prop="selNodeVal">
            <el-select
              v-model="assRuleForm.selNodeVal"
              placeholder="请选择节点"
              filterable
              @change="handleChangeNodeVal(assRuleForm.selNodeVal)"
            >
              <el-option
                v-for="item in currentNodeInfo"
                :key="item.nodeId"
                :label="item.nodeName"
                :value="item.nodeId"
              />
            </el-select>
          </el-form-item>
          <template v-if="assRadioSign !=='node'">
            <el-form-item label="用户组" prop="userGroupVal">
              <el-select
                v-model="assRuleForm.userGroupVal"
                placeholder="请选择用户组"
                @change="handleChangeUserGroup(assRuleForm.userGroupVal)"
              >
                <el-option
                  v-for="item in currentNodeGroupInfo"
                  :key="item.userGroupId"
                  :label="item.userGroupName"
                  :value="item.userGroupId"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="组内成员" prop="memberVal">
              <el-select v-model="assRuleForm.memberVal" placeholder="请选择组内成员">
                <el-option
                  v-for="item in memberArray"
                  :key="item.userId"
                  :label="item.nickname"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
          </template>
          <el-form-item type="flex" justify="end" align="end" class="assSubmit">
            <xyy-button type="normal" @click="assignmentDialog=false">取 消</xyy-button>
            <xyy-button
              class="queryAss"
              type="primary"
              @click="checkTimer(queryAss,'time1')('assRuleForm')"
            >确定转移
            </xyy-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </el-row>
  </div>
</template>

<script>
// /workorder/operation/startAgai
import {
  deleteTag,
  getCurrentUrgeCon,
  getCurrtNodeList,
  getMerchantByAccountMobile,
  getNodeList,
  getOrderDetailTag,
  getTagList,
  getUserList,
  getWorkorderInfoAndReplyInfo,
  getWorkorderLocationCheck,
  getWorkorderRepeateCheck,
  listReplyFieldByWorkorderId,
  listTemplateFieldAuthByCode,
  listUserGroupsByNodeId,
  putSwitchTag,
  restartWorkorder,
  saveSendBackWorkOrderNew,
  saveWorkorderAppendNote,
  saveWorkorderReply,
  startAgai,
  updateWorkorder,
  updateWorkOrderToVoid,
  urgeWorkeApi,
  workorderTransfer
} from '@/api/mySheetManage';
import dataTime from '@/utils/filter.js';
import Preview from '~/Fields/preview';
import filePreview from '@/components/Fields/file-preview';
import fileprev from '@/components/Fields/file-preview';
import openInfo from './components/openInfo';
import processs from './components/process';
import { MIME_TYPES } from '@/api/fields';
import layout from '@/components/templates/layout';
import { adapter, compareFile, initFileDatas, typeCopyVal } from '@/utils/tools.js';
import pubUrl from './mixin/pubUrl';

export default {
  name: 'SheetList',
  components: {
    Preview,
    filePreview,
    openInfo,
    fileprev,
    processs,
    layout
  },
  filters: {
    statusFormat(val) {
      let name = '';
      switch (val) {
        case 0:
          name = '待领取';
          break;
        case 1:
          name = '处理中';
          break;
        case 2:
          name = '完结';
          break;
        case 3:
          name = '已退回';
          break;
        case 4:
          name = '异常';
          break;
        case 5:
          name = '作废';
          break;
      }
      return name;
    }
  },
  mixins: [pubUrl],
  data() {
    return {
      previewNew: 'previewNew',
      layDatas: '', // 布局数据
      replyLayDatas: '',
      urlAction: process.env.BASE_API + '/fileUpload/uploadFile',
      fileNums: 10, // 文件最大上传数
      uploadList: [], // 上传队列
      failureList: [], // 上传失败文件列表
      uploadingList: false, // 文件上传中
      previewModal: [], // 附件上传返回值的数组
      previewModalarr: [], // 向后台传的数据
      update_problem_classification: false, // 切换问题分类为true
      time1: null,
      time2: null,
      time3: null,
      time4: null,
      locationStatus: false, // 客户所在地校验弹框
      locationMsg: '', // 客户所在地校验信息
      assRadioSign: 'people', // 工单转移
      assignmentDialog: false, // 工单转移
      assRuleForm: {
        selNodeVal: '', // 选择节点值
        userGroupVal: '',
        memberVal: ''
      },
      currentNodeInfo: [],
      currentNodeGroupInfo: [], //
      memberArray: [],
      assRules: {
        selNodeVal: [
          { required: true, message: '请选择节点名称', trigger: 'blur' }
        ],
        userGroupVal: [
          { required: true, message: '请选择用户组', trigger: 'blur' }
        ],
        memberVal: [
          { required: true, message: '请选择组内成员', trigger: 'blur' }
        ]
      },
      urgeWorkHid: false,
      currentProcessPersonName: '',
      currentNodeName: '',
      urgeWorkCon: '',
      textarea: '',
      editfromData: [], // 编辑表单
      sheetPre: [
        // {
        //   id: 33, // 主键
        //   formId: 1, // 表单ID
        //   nodeId: 71, // 节点ID
        //   fieldId: 23, // 字段ID
        //   authType: 0, // 权限类型 0-隐藏 1-只读 2-编辑
        //   authTypeCondition: 2, // 满足条件的权限类型 0-隐藏 1-只读 2-编辑
        //   required: 0, // 是否必填 0-否 1-是
        //   requiredCondition: 0, // 满足条件后是否必填 0-否 1-是
        //   ganged: 0, // 是否是联动字段：0-否 1-是
        //   deleted: null, // 是否删除 0-否 1-是
        //   duplicate: null, // 是否是副本：0-否 1-是
        //   creatorId: null, // 创建人ID
        //   orgId: null, // 组织ID
        //   gmtCreate: null, // 创建时间
        //   gmtModified: null, // 更新时间
        //   fieldText: '工单处理意见', // 字段文本
        //   fieldName: '字段名12', // 字段名称
        //   fieldCode: '1161472515860729856', // 字段key
        //   fieldType: 1, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
        //   defaultValue: '', // 默认值
        //   optionSettings: '', // 选项设置
        //   tips: '', // 提示信息
        //   conditionList: null // 节点权限字段关联条件list
        // }
      ],
      extendWorkorderField: [], // 编辑工单发起字段对象
      record: 1, // 1.流转 2.催单
      editForm: false,
      sheetResetShow: false,
      returnSheetHid: false, // 退回工单
      returnSheetRadio: '0', // 1:退回上一环节,0:退回发起人
      returnSheetCon: '', // 退回工单内容
      returnSheetTip: {
        '1': '提示：退单后，工单将退回给上一环节处理人',
        '0': '提示：退单后，工单将退回给发起人，工单流程终止'
      }, // 退回工单提示
      // restartWorkorder: '', // 重启工单内容
      baseWorkorderInfo: {
        // 工单基础信息
        // append_notes: true, //  是否可以追加说明
        // urge: true, //	是否可以催办
        // reply_submit: false, //  是否可以提交回复
        // send_back: false, //	是否可以退回工单
        // closed: false, //	是否可以关闭工单
        // invalid: false, //	是否可以作废工单
        // completionTime: 1566211887000, // 完成时间
        // creatorId: 12345678910, // 创建人ID
        // creatorName: '张三', // 创建人姓名
        // currentProcessingPersonId: 123, // 当前处理人ID
        // currentProcessingPersonName: '', // 当前处理人姓名
        // currentState: 5, // 当前处理状态'0'':‘待领取’,''1'':''处理中''.''2'':‘完结’,''3'':''退回'',‘4’:''异常'',''5'':''作废'''
        // editorId: 0, // 编辑人ID
        // editorName: '', // 编辑人姓名
        // endPersonId: 0, // 结单人ID
        // endPersonName: '', // 结单人姓名
        // formId: 1, // 表单ID
        // formTypeId: 1, // 表单类型ID
        // formTypeName: '表单类型1', // 表单类型
        // gmt_create: 1565836407000, // 创建时间
        // gmt_modified: 1566215944000, // 修改时间
        // workOrder_id: 7, // 工单id
        // received: 1, // 领取状态‘0’:‘待领取’,''1'':''已领取
        // completion_time: 1577599718274,
        // completion_time_string: '2019',
        // creator_id: '3904',
        // creator_name: '刘某人',
        // current_processing_person_id: 3904,
        // current_processing_person_name: '刘某人',
        // current_state: 1,
        // customer_id: '123',
        // deleted: 0,
        // editor_id: '3904',
        // editor_name: '刘某人',
        // end_person_id: 123,
        // end_person_name: 1577599718274,
        // form_id: '123',
        // form_type_id: '16',
        // form_type_name: 'MongoDB表单类型',
        // gmt_create: 1577599140197,
        // gmt_modified: 1577599718274,
        // latest_submission_time: 1577599718274,
        // processing_duration: 1577599718274,
        // recent_acceptance_time: 1577599718274,
        // time_limited: 1577599718274,
        // timeout_state: 0,
        // workorder_id: '1211164694467973120',
        // workorder_num: 'MGDB20191229000002'
      },
      issueAppendHid: false,
      appendNotesCon: '',
      extendWorkorderFieldinfo: [
        // 工单右侧定义字段信息
        // {
        //   fieldType: 3, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
        //   fieldCode: 1161472666121670656, // 字段唯一Key
        //   fieldMultipleValue: '{"0": "苹果"}', // 字段为多选时的值
        //   fieldName: '客户名称', // 字段的Name
        //   fieldSingleValue: 0,
        //   fieldText: '优先级' // 字段显示的文本
        // },
        // {
        //   fieldType: 0, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
        //   fieldCode: 1161472666121670651,
        //   fieldName: '客户名称',
        //   fieldSingleValue: '李四',
        //   fieldText: '客户名称'
        // },
        // {
        //   fieldType: 2, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
        //   fieldCode: 1161472666121670652,
        //   fieldName: '来源渠道',
        //   fieldSingleValue: '0',
        //   fieldText: '来源渠道',
        //   optionName: '网页接入'
        // },
        // {
        //   fieldType: 4, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
        //   fieldCode: 1161472666121670656, // 字段唯一Key
        //   fieldMultipleValue: '{"0": "苹果","1": "红色"}', // 字段为多选时的值
        //   fieldName: '客户名称', // 字段的Name
        //   fieldSingleValue: '张三',
        //   fieldText: '多选' // 字段显示的文本
        // },
        // {
        //   fieldType: 6, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
        //   fieldCode: 1161472666121670656, // 字段唯一Key
        //   fieldMultipleValue: '{"0": "苹果","1": "红色"}', // 字段为多选时的值
        //   fieldName: '客户名称', // 字段的Name
        //   fieldSingleValue: '张三',
        //   fieldText: '级联' // 字段显示的文本
        // },
        // {
        //   fieldType: 10, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
        //   fieldCode: 1161472666121670656, // 字段唯一Key
        //   fieldMultipleValue:
        //     '[{
        // 	"raw": {
        // 		"size": 241656,
        // 		"type": "image/jpeg"
        // 	},
        // 	"uid": 1571973330356,
        // 	"url": "http://t-upload.ybm100.com/ybm/ec-cs/file/f70d875d-bcca-4cad-b1e6-d720dbb5642e.jpg",
        // 	"name": "123.jpg",
        // 	"path": ["ybm/ec-cs/file/f70d875d-bcca-4cad-b1e6-d720dbb5642e.jpg"],
        // 	"percent": 100
        // }]', // 字段为多选时的值
        //   fieldName: '客户名称', // 字段的Name
        //   fieldSingleValue: '张三',
        //   fieldText: '客户名称' // 字段显示的文本
        // }
      ],
      isCloseNode: false,
      workorderId: '',
      time: null,
      useAssign: true,
      assignType: 'people',
      assignForm: {
        assignNode: '',
        assignUsergroup: '',
        assignMembers: ''
      },
      assignRules: {
        assignNode: [
          { required: true, message: '请选择节点名称', trigger: 'change' }
        ]
      },
      assignNodes: [], // 转移节点数据
      assignGroups: [], // 转移节点用户组数据
      assignUsers: [], // 转移节点用户数据
      restartForm: {
        restartReason: ''
      },
      againRestart: false,
      workorderNum: '',
      labelArray: [],
      systemArr: [],
      customeArr: [],
      orderTag: {
        tagId: '',
        tagName: ''
      },
      tags: [],
      searchResult: [], // 根据手机号搜索出来的店铺信息
      tempMerchantInfo: {}, // 根据客户电话匹配出来的客户信息

      // 重复工单
      repeatWorksheet: {
        workorderId: '',
        workorderNum: '',
        addSheetCheckHid: false
      }
    };
  },
  computed: {
    circulaBrief: function() {
      return this.circulationRecordS.slice(0, 3);
    },
    reminderRecordBrief: function() {
      return this.reminderRecordS.slice(0, 2);
    },
    appendRecordSBrief: function() {
      return this.appendNotesRecordS.slice(0, 2);
    },
    currentState() {
      return this.baseWorkorderInfo.current_state;
    },
    isDisabledTag() {
      return !this.$store.getters.validBtn.some(item => {
        return item.code === 'btn:cs:workordertag';
      });
    }
  },
  watch: {
    editfromData: function(val) {
      this.systemArr.splice(0, this.systemArr.length);
      this.customeArr.splice(0, this.customeArr.length);
      val.forEach(item => {
        if (item.systemFieldFlag === 0) {
          this.systemArr.push(item);
        } else {
          this.customeArr.push(item);
        }
      });
    }
  },
  mounted: function() {
    this.mountedHandle(); // 页面初始话及请求
  },
  methods: {
    keywordToSearch(fieldCode, keyword) {
      // 客户电话点击搜索
      if (fieldCode === 's1164723822566445056') {
        getMerchantByAccountMobile({
          mobile: keyword
        }).then((res) => {
          this.searchResult = res.data || [];
          if (this.searchResult.length === 0) {
            this.$message.info('搜索结果为空');
            this.editfromData.forEach(function(item, index) {
              // 客户名称
              if (item.fieldCode === 's1164456058157142016') {
                item.defaultValue = '';
              }
              // 客户ID
              if (item.fieldCode === 's1863875885430607872') {
                item.defaultValue = '';
              }
            });
          }
        });
      }
    },
    keywordToSelected(info) {
      console.log('选中的item', info);
      this.tempMerchantInfo = info;
      console.log('赋值前的', this.sheetPre, 'editfromData', this.editfromData);
      this.editfromData.forEach(function(item, index) {
        // 客户名称
        if (item.fieldCode === 's1164456058157142016') {
          console.log('选中的', item);
          item.defaultValue = info.realName;
          console.log('赋值后的', item, this.sheetPre, this.editfromData);
        }
        // 客户ID
        if (item.fieldCode === 's1863875885430607872') {
          item.defaultValue = info.id.toString();
        }
      });
    },

    // 订单编号搜索回调,定向更新某些字段
    queryOrderInfo(orderInfo) {
      const templateProductNameFieldCodes = [];
      this.editfromData.forEach((item, index) => {
        // 客户名称
        if (item.fieldCode === 's1164456058157142016') {
          item.defaultValue = orderInfo.merchantName;
        }
        // 客户电话
        if (item.fieldCode === 's1164723822566445056') {
          item.defaultValue = orderInfo.mobile;
        }
        // 客户ID
        if (item.fieldCode === 's1863875885430607872') {
          item.defaultValue = orderInfo.merchantId.toString();
        }
        // 商家ID
        if (item.fieldCode === 's1870039678284075008') {
          item.defaultValue = orderInfo.orgId.toString();
        }
        // 商品
        if ([
          'c1216381125258252288', // 商品名称A
          'c1216381794576896000', // 商品名称B
          'c1216381703413698560', // 商品名称C
          'c1864595898831605760', // 商品名称D
          'c1866073198404177920' // 商品名称E
        ].includes(item.fieldCode)) {
          templateProductNameFieldCodes.push(item.fieldCode);
        }
      });
      // 依次填充商品名称字段
      // 模板有商品名称字段 && 有选择商品
      if (templateProductNameFieldCodes.length && orderInfo.productList.length) {
        templateProductNameFieldCodes.forEach((codeItem, codeIndex) => {
          const index = this.editfromData.findIndex(p => p.fieldCode === codeItem);
          if (orderInfo.productList[codeIndex]) {
            const product = orderInfo.productList[codeIndex];
            this.editfromData[index].defaultValue = `商品编号：${product.barcode || ''}\n商品名称：${product.productName || ''}\n套餐编号：${product.packageId || ''}\n套餐数量：${product.packageCount || ''}\n生产厂家：${product.manufacturer || ''}\n规格：${product.spec || ''}\n商品原价：${product.productPrice || ''}\n商品购买单价：${product.purchasePrice || ''}\n近效期至：${product.nearEffect || ''}\n远效期至：${product.farEffect || ''}\n商品数量：${product.productAmount || ''}\n实际出库数量：${product.realSendNum || ''}`;
          } else {
            this.editfromData[index].defaultValue = '';
          }
        });
      }
    },

    goDetail(id) {
      // console.log('继续发起：', id);
      this.repeatWorksheet.addSheetCheckHid = false;
      this.editForm = false;
      this.$router.push({
        // path: '/' + this.pubUrl + '/sheetDetail/' + id,
        path: '/workStatus' + this.baseWorkorderInfo.form_type_id + '/sheetDetail/' + id,
        query: { id: id, type: 'newSheet' }
      });
    },
    onCloseRepeatWorksheetDialog() {
      this.editForm = false;
    },

    //    上传附件--开始
    // 附件上传之前拦截
    beforeUpload(file) {
      this.uploadingList = false;
      this.uploadList = [].concat(file);
      if (file.size / 1024 / 1000 > 500) {
        // 附件上传大小的限制
        this.$XyyMessage.error(`附件上传大小不能超过500M`);
        return false;
      }

      if (file.type) {
        if (!MIME_TYPES.includes(file.type)) {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: `上传文件格式仅限：<span style="color:red;">gif bmp jpg jpeg png pdf zip rar mp4 avi doc docx xls xlsx ppt pptx txt</span>`, // html代码串
            onSuccess: () => {
            }
          });
          return false;
        }
      } else {
        // element ui 插件bug 无法检测rar rar文件以后缀方式判断
        const _type = file.name
          .slice(file.name.lastIndexOf('.') + 1)
          .toLowerCase();
        if (_type !== 'rar') {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: `上传文件格式仅限：<span style="color:red;">gif bmp jpg jpeg png pdf zip rar mp4 avi doc docx xls xlsx ppt pptx txt</span>`, // html代码串
            onSuccess: () => {
            }
          });
          return false;
        }
      }
    },
    /**
     * 文件个数超出回调
     */
    validateNums(file, fileList) {
      this.$XyyMessage.error(`最多上传${this.fileNums}个`);
    },
    /**
     * 上传中回调
     */
    handleProgress(event, file) {
      if (file) {
        this.uploadingList = true;
        file.percent = Number(event.percent.toFixed(0));
        if (file.percent > 99) file.percent = 99;
      }
    },

    //    上传成功的回调
    handleSuccess(res, file) {
      if (res.code === 1) {
        if (!res.data.failFiles.length) {
          file.percent = 100;
          const _file = {
            uid: file.uid,
            name: file.name,
            url:
              res.data.baseUrl +
              (res.data.successFiles[0].thumbImagePath ||
                res.data.successFiles[0].group +
                '/' +
                res.data.successFiles[0].path), // 预览路径
            path:
              res.data.baseUrl +
              res.data.successFiles[0].group +
              '/' +
              res.data.successFiles[0].path, // 真实路径
            percent: 100,
            raw: {
              type: file.raw.type
                ? file.raw.type
                : 'application/x-rar-compressed',
              size: file.raw.size
            },
            data: res.data.successFiles[0]
          };
          this.previewModal.push(_file);
        } else {
          this.$refs['myUploader'].uploadFiles.forEach((el, i) => {
            if (el.uid === file.uid) {
              const _file = this.$refs['myUploader'].uploadFiles.splice(i, 1);
              this.failureList = this.failureList.concat(_file);
            }
            this.uploadList = [].concat(this.$refs['myUploader'].uploadFiles);
          });

          let msg = '';
          if (this.failureList.length > 1) {
            msg = `${this.failureList[0].name}等${this.failureList.length}个文件上传失败`;
          } else {
            msg = `${this.failureList[0].name}上传失败`;
          }
          this.$XyyMessage.error(msg);
        }
      } else {
        this.$XyyMessage.error(res.msg);
      }
      this.uploadingList = false;
    },
    //    上传失败的回调
    handleError(res) {
      this.uploadingList = false;
      this.$XyyMessage.error(`文件上传失败`);
    },
    /**
     * 上传点击事件 初始化上传失败数组
     */
    uploadStart() {
      this.failureList = [];
    },
    /**
     * 取消上传
     */
    abortFile(file) {
      this.$refs['myUploader'].abort(file);
      this.$refs['myUploader'].uploadFiles.forEach((el, i) => {
        if (el.uid === file.uid) {
          this.$refs['myUploader'].uploadFiles.splice(i, 1);
        }
      });
      this.uploadList = [].concat(this.$refs['myUploader'].uploadFiles);
      this.uploadingList = false;
    },

    // 附件删除
    delFile(file) {
      // 删除上传队列中的数据
      this.$refs['myUploader'].uploadFiles.forEach((el, i) => {
        if (el.uid === file.uid) {
          this.$refs['myUploader'].uploadFiles.splice(i, 1);
        }
      });
      this.uploadList = [].concat(this.$refs['myUploader'].uploadFiles);
      // 删除存储的数据
      this.previewModal.forEach((item, index) => {
        if (file.uid === item.uid) {
          this.previewModal.splice(index, 1);
        }
      });
    },

    //    上传附件--结束
    /**
     * 工单重启 弹框开启
     */
    restartOpen() {
      if (this.sheetResetShow) {
        this.useAssign = true;
        this.assignType = 'people';
        this.assignNodes = [];
        this.assignGroups = [];
        this.assignMembers = [];
        if (this.$refs['assignForm']) {
          this.$refs['assignForm'].resetFields();
        }
        this.assignForm.assignNode = '';
        this.assignForm.assignUsergroup = '';
        this.assignForm.assignMembers = '';
        if (this.$refs['restartForm']) {
          this.$refs['restartForm'].resetFields();
        }
        getCurrtNodeList(
          this.baseWorkorderInfo.form_id,
          this.baseWorkorderInfo.workorder_id
        ).then(res => {
          if (res.code === 1) {
            this.assignNodes = res.data;
          } else {
            this.$XyyMessage.error(res.msg);
          }
        });
      }
    },
    /**
     * 初始化转移数据
     */
    resetAssign(val) {
      if (val === 'node') {
        this.assignForm.assignUsergroup = '';
        this.assignForm.assignMembers = '';
      } else {
        if (!this.assignGroups.length) {
          this.loadAssignGroups(this.assignForm.assignNode);
        }
      }
    },
    /**
     * 加载节点用户组数据
     */
    loadAssignGroups(val) {
      if (this.assignType === 'people') {
        const curNode = this.assignNodes.filter(el => el.nodeId === val)[0];
        if (curNode.nodeType === 3 && curNode.closeWay === 1) {
          this.$XyyMessage.warning('节点自动关闭，无处理人，请选择转移给节点');
          return;
        }
        listUserGroupsByNodeId(val, this.workorderId).then(res => {
          if (res.code === 1) {
            this.assignGroups = res.data;
            this.assignForm.assignUsergroup = '';
            this.assignForm.assignMembers = '';
          } else {
            this.$XyyMessage.error(res.msg);
          }
        });
      }
    },
    /**
     * 加载用户组用户数据
     */
    loadAssignMembers(val) {
      getUserList(val).then(res => {
        if (res.code === 1) {
          this.assignUsers = res.data;
          this.assignForm.assignMembers = '';
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 工单转移查询开始
    sheetAssignment(row) {
      const that = this;
      getCurrtNodeList(
        that.baseWorkorderInfo.form_id,
        that.baseWorkorderInfo.workorder_id
      ).then(res => {
        that.currentNodeInfo = res.data;
        that.assignmentDialog = true;
      });
    },
    assRadioChange(val) {
      const that = this;
      that.assRuleForm.selNodeVal = '';
      that.assRuleForm.userGroupVal = '';
      that.assRuleForm.memberVal = '';
      that.currentNodeGroupInfo = [];
      that.memberArray = [];
      getCurrtNodeList(
        that.baseWorkorderInfo.form_id,
        that.baseWorkorderInfo.workorder_id
      ).then(res => {
        that.currentNodeInfo = res.data;
      });
    },
    handleChangeNodeVal(nodeId) {
      const that = this;
      that.assRuleForm.userGroupVal = '';
      that.assRuleForm.memberVal = '';
      that.currentNodeGroupInfo = [];
      that.memberArray = [];
      const currtItem = that.currentNodeInfo.find(
        item => item.nodeId === nodeId
      );
      if (
        currtItem.nodeType === 3 &&
        currtItem.closeWay === 1 &&
        that.assRadioSign !== 'node'
      ) {
        that.$XyyMessage.warning('节点自动关闭，无处理人，请选择转移给节点');
      }
      if (that.assRadioSign !== 'node') {
        listUserGroupsByNodeId(nodeId, that.workorderId).then(res => {
          that.currentNodeGroupInfo = res.data;
        });
      }
    },
    handleChangeUserGroup(userGroupVal) {
      const that = this;
      that.assRuleForm.memberVal = '';
      that.memberArray = [];
      getUserList(userGroupVal).then(res => {
        that.memberArray = res.data;
      });
    },
    queryAss(formName) {
      const that = this;
      this.$refs[formName].validate(valid => {
        if (valid) {
          const param = {
            fromFlowId: that.baseWorkorderInfo.flow_id,
            toNodeId: that.assRuleForm.selNodeVal,
            toUserGroupId: that.assRuleForm.userGroupVal,
            toUserId: that.assRuleForm.memberVal,
            transferType: that.assRadioSign === 'node' ? 0 : 1,
            workorderId: that.baseWorkorderInfo.workorder_id
          };
          // console.log('工单转移请求参数：', param);
          workorderTransfer(param).then(res => {
            if (res.code === 1) {
              // 20200716 DT 修复批量转移后跳转到列表弹出 “您无权查看该信息” 后又跳回到工单详情的问题
              // this.getWorkorderInfoAndReplyInfo({
              //   workorderId: this.workorderId
              // }); // 获取字段信息
              this.listReplyFieldByWorkorderId({
                workorderId: this.workorderId
              }); // sheetPre

              that.$refs['processs'].getRecordDatas(); // 获取流转记录-催单记录-追加记录
              that.$XyyMessage.success('工单转移成功!');
              this.addModifyFlag();
              // that.$store.dispatch('tagsView/delView', that.$route);
              // that.$router.push({
              //   // path: `/${that.pubUrl}`,
              //   path: that.$route.query.currentUrl,
              //   query: {
              //     t: new Date().getTime(),
              //     jump: true
              //   }
              // });
            } else {
              // that.$XyyMessage.error(res.msg);
              that
                .$confirm(res.msg, '提示', {
                  confirmButtonText: '确定',
                  showCancelButton: false,
                  type: 'warning'
                })
                .then(() => {
                  that.assignmentDialog = false;
                  this.addModifyFlag();
                  // that.$store.dispatch('tagsView/delView', that.$route);
                  // that.$router.push({
                  //   // path: `/${that.pubUrl}`,
                  //   path: that.$route.query.currentUrl,
                  //   query: {
                  //     t: new Date().getTime(),
                  //     jump: true
                  //   }
                  // });
                  // that.getWorkorderInfoAndReplyInfo({
                  //   workorderId: that.workorderId
                  // }) // 获取字段信息
                  // that.listReplyFieldByWorkorderId({
                  //   workorderId: that.workorderId
                  // }) // sheetPre

                  that.$refs['processs'].getRecordDatas(); // 获取流转记录-催单记录-追加记录
                })
                .catch(() => {
                });
            }
            that.assRadioSign = 'node';
            that.assRuleForm.selNodeVal = '';
            that.assRuleForm.userGroupVal = '';
            that.assRuleForm.memberVal = '';
          });
          this.assignmentDialog = false;
        } else {
          this.assignmentDialog = true;
          return false;
        }
      });
    },
    assCacel() {
      const that = this;
      that.assRadioSign = 'node';
      that.assRuleForm.selNodeVal = '';
      that.assRuleForm.userGroupVal = '';
      that.assRuleForm.memberVal = '';
      this.assignmentDialog = false;
    },
    urgeWorkCheck() {
      const that = this;

      getCurrentUrgeCon(
        that.baseWorkorderInfo.flow_id,
        that.baseWorkorderInfo.workorder_id
      ).then(res => {
        if (res.code) {
          that.currentNodeName = res.data.currentNodeName;
          that.currentProcessPersonName = res.data.currentProcessingPersonName;
          that.urgeWorkHid = true;
        } else {
          that.$XyyMessage.warning(res.msg);
        }
      });
    },
    // 催单
    urgeWork() {
      const that = this;
      if (that.urgeWorkCon === '') {
        that.$XyyMessage.error('请输入催单内容！');
        return false;
      }
      const param = {
        flowId: that.baseWorkorderInfo.flow_id,
        urgeContents: that.urgeWorkCon,
        workorderId: that.baseWorkorderInfo.workorder_id
      };
      urgeWorkeApi(param).then(res => {
        if (res.code) {
          that.$refs['processs'].getRecordDatas(); // 获取流转记录-催单记录-追加记录
          that.$XyyMessage.success('催单成功！');
          that.urgeWorkCon = '';
          that.urgeWorkHid = false;
        } else {
          that.urgeWorkCon = '';
          that.urgeWorkHid = false;
          that.$XyyMessage.error(res.msg);
        }
        that.urgeWorkHid = false;
      });
    },
    urgeWorkCalce() {
      this.urgeWorkCon = '';
      this.urgeWorkHid = false;
    },
    // 获取显隐状态
    getAuthTypeStatus(item) {
      const condition = this.conditionAA(item);
      return condition !== 0;
    },
    // 是否只读？
    conditionAA(obj) {
      // authType: 0, // 权限类型 0-隐藏 1-只读 2-编辑
      // authTypeCondition: 2, // 满足条件的权限类型 0-隐藏 1-只读 2-编辑
      const that = this;
      let authTypeCondition = obj.authType;
      if (!obj.conditionList) {
        obj.conditionList = [];
      }
      if (obj.conditionList[0]) {
        this.sheetPre.forEach(function(item, index) {
          if (item.fieldCode === obj.conditionList[0].conditionFieldCode) {
            let optionsValue;
            if (
              item.optionSettings &&
              typeof item.optionSettings === 'string'
            ) {
              const rest = item.optionSettings.replace(/(&quot;)/g, '"');
              optionsValue = JSON.parse(rest);
            } else {
              optionsValue = item.optionSettings;
            }
            // fieldType: 1, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
            let symbolSign = false;
            switch (item.fieldType) {
              case 0:
              case 1:
              case 5:
              case 8:
              case 9:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  item.defaultValue
                );
                break;
              case 2:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  optionsValue.selectOptions.optionsValue
                );
                break;
              case 3:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  optionsValue.radioOptions.optionsValue
                );
                break;
              // eslint-disable-next-line no-case-declarations
              case 4:
                let value = [...optionsValue.checkedOptions.optionsValue];
                value = value.sort();
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  value.join()
                );
                break;
            }
            if (symbolSign) {
              // 满足条件的权限类型 0-隐藏 1-只读 2-编辑
              authTypeCondition = obj.authTypeCondition;
            }
          }
        });
      }

      return authTypeCondition;
    },
    // 获取显隐状态
    editGetAuthTypeStatus(item) {
      const condition = this.editConditionAA(item);
      return condition !== 0;
    },
    // 是否只读？
    editConditionAA(obj) {
      // authType: 0, // 权限类型 0-隐藏 1-只读 2-编辑
      // authTypeCondition: 2, // 满足条件的权限类型 0-隐藏 1-只读 2-编辑
      const that = this;
      let authTypeCondition = obj.authType;
      if (!obj.conditionList) {
        obj.conditionList = [];
      }
      if (obj.conditionList[0]) {
        this.editfromData.forEach(function(item, index) {
          if (item.fieldCode === obj.conditionList[0].conditionFieldCode) {
            let optionsValue;
            if (
              item.optionSettings &&
              typeof item.optionSettings === 'string'
            ) {
              const rest = item.optionSettings.replace(/(&quot;)/g, '"');
              optionsValue = JSON.parse(rest);
            } else {
              optionsValue = item.optionSettings;
            }
            // fieldType: 1, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
            let symbolSign = false;
            switch (item.fieldType) {
              case 0:
              case 1:
              case 5:
              case 8:
              case 9:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  item.defaultValue
                );
                break;
              case 2:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  optionsValue.selectOptions.optionsValue
                );
                break;
              case 3:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  optionsValue.radioOptions.optionsValue
                );
                break;
              // eslint-disable-next-line no-case-declarations
              case 4:
                let value = [...optionsValue.checkedOptions.optionsValue];
                value = value.sort();
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  value.join()
                );
                break;
            }
            if (symbolSign) {
              // 满足条件的权限类型 0-隐藏 1-只读 2-编辑
              authTypeCondition = obj.authTypeCondition;
            }
          }
        });
      }

      return authTypeCondition;
    },
    // 是否必填？
    requirsConditionAAReply(obj) {
      //   required: 1, // 是否必填 0-否 1-是
      //   requiredCondition: 0, // 满足条件后是否必填 0-否 1-是
      const that = this;
      let requiredCondition = obj.required;
      if (obj.conditionList[0]) {
        this.sheetPre.forEach(function(item, index) {
          if (item.fieldCode === obj.conditionList[0].conditionFieldCode) {
            let optionsValue;
            if (
              item.optionSettings &&
              typeof item.optionSettings === 'string'
            ) {
              const rest = item.optionSettings.replace(/(&quot;)/g, '"');
              optionsValue = JSON.parse(rest);
            } else {
              optionsValue = item.optionSettings;
            }
            // fieldType: 1, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
            let symbolSign = false;
            switch (item.fieldType) {
              case 0:
              case 1:
              case 5:
              case 8:
              case 9:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  item.defaultValue
                );
                break;
              case 2:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  optionsValue.selectOptions.optionsValue
                );
                break;
              case 3:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  optionsValue.radioOptions.optionsValue
                );
                break;
              // eslint-disable-next-line no-case-declarations
              case 4:
                let value = [...optionsValue.checkedOptions.optionsValue];
                value = value.sort();
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  value.join()
                );
                break;
              case 6:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  optionsValue.treeOptions.optionsValue
                );
                break;
            }
            if (symbolSign) {
              //   requiredCondition: 0, // 满足条件后是否必填 0-否 1-是
              requiredCondition = obj.requiredCondition;
            }
          }
        });
      }
      return requiredCondition;
    },
    // 是否必填？
    requirsConditionAA(obj) {
      //   required: 1, // 是否必填 0-否 1-是
      //   requiredCondition: 0, // 满足条件后是否必填 0-否 1-是
      const that = this;
      let requiredCondition = obj.required;
      if (obj.conditionList[0]) {
        this.editfromData.forEach(function(item, index) {
          if (item.fieldCode === obj.conditionList[0].conditionFieldCode) {
            let optionsValue;
            if (
              item.optionSettings &&
              typeof item.optionSettings === 'string'
            ) {
              const rest = item.optionSettings.replace(/(&quot;)/g, '"');
              optionsValue = JSON.parse(rest);
            } else {
              optionsValue = item.optionSettings;
            }
            // fieldType: 1, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
            let symbolSign = false;
            switch (item.fieldType) {
              case 0:
              case 1:
              case 5:
              case 8:
              case 9:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  item.defaultValue
                );
                break;
              case 2:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  optionsValue.selectOptions.optionsValue
                );
                break;
              case 3:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  optionsValue.radioOptions.optionsValue
                );
                break;
              // eslint-disable-next-line no-case-declarations
              case 4:
                let value = [...optionsValue.checkedOptions.optionsValue];
                value = value.sort();
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  value.join()
                );
                break;
              case 6:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  optionsValue.treeOptions.optionsValue
                );
                break;
            }
            if (symbolSign) {
              //   requiredCondition: 0, // 满足条件后是否必填 0-否 1-是
              requiredCondition = obj.requiredCondition;
            }
          }
        });
      }
      return requiredCondition;
    },
    getConditionSymbol(obj, defaultValue) {
      // defaultValue:输入值 valueFirst:条件值
      // 条件符号 0-等于 1-大于 2-小于 3-大于等于 4-小于等于 5-介于 6-无
      const value = obj.valueFirst
        .split(',')
        .sort((a, b) => a - b)
        .join(',');
      if (Object.prototype.toString.call(defaultValue) === '[object Array]') {
        defaultValue = defaultValue.join(',');
      }

      switch (obj.conditionSymbol) {
        case 0:
          return defaultValue === value;
        case 1:
          return defaultValue !== value;
        case 2:
          if (!defaultValue) {
            return false;
          } else {
            return defaultValue.indexOf(value) !== -1;
          }

        // eslint-disable-next-line no-case-declarations
        case 3:
          const valArr = value.split(',');
          for (let i = 0; i <= valArr.length; i++) {
            if (String(defaultValue).indexOf(valArr[i]) !== -1) {
              return true;
            }
          }
          return false;
        case 4:
          return defaultValue === value;
        case 5:
          return defaultValue !== value;
      }
    },
    // 获取工单详情字段
    getWorkorderInfoAndReplyInfo(orderParam) {
      const that = this;
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      return getWorkorderInfoAndReplyInfo(orderParam)
        .then(response => {
          if (response.code === 1) {
            const data = response.data;
            if (data) {
              that.baseWorkorderInfo = data.base_field;
              this.layDatas = data.base_field.layout;
              that.extendWorkorderFieldinfo = data.custom_field;
              if (!that.layDatas && that.extendWorkorderFieldinfo.length) {
                const arr = [];
                for (let i = 0; i < that.extendWorkorderFieldinfo.length; i++) {
                  if (that.extendWorkorderFieldinfo[i].system_field_flag === 1) {
                    arr.push([1]);
                  }
                }
                that.layDatas = arr.map(cols => cols.join('-')).join();
              }
            }
            that.initWorkInfo();
            that.getTags();
            that.getOrderTag(orderParam.workorderId); // 获取工单标签
            // if (that.baseWorkorderInfo.im_dialog_id)
            that.$refs.processs.getConversation(
              that.baseWorkorderInfo.im_dialog_id
            ); // 获取工单会话记录
          } else {
            that.loading.close();
            this.$alert(response.msg, '提示', {
              confirmButtonText: '确定',
              callback: action => {
                this.addModifyFlag(true); // 增加标识，回对应的工单列表重查数据
                that.$store.dispatch('tagsView/delView', that.$route);
                if (this.$route.query.currentUrl) {
                  that.$router.push({
                    path: this.$route.query.currentUrl
                  });
                } else {
                  this.$router.go(-1);
                }
              }
            });
          }
        })
        .catch(function(error) {
          that.loading.close();
          console.log(error);
        });
    },
    // 工单详情字段初始化
    initWorkInfo() {
      const baseWorkorderInfo = this.baseWorkorderInfo;
      let extendWorkorderFieldinfo = this.extendWorkorderFieldinfo;
      extendWorkorderFieldinfo = adapter(extendWorkorderFieldinfo); // 增加适配器

      if (baseWorkorderInfo.gmt_create) {
        baseWorkorderInfo.gmt_create = dataTime.dataTime(
          baseWorkorderInfo.gmt_create,
          'yy-mm-dd HH:ss:nn'
        );
      }
      for (const ite of extendWorkorderFieldinfo) {
        if (
          ite.fieldMultipleValue &&
          typeof ite.fieldMultipleValue === 'string'
        ) {
          // 格式化多选数据
          ite.fieldMultipleValue = JSON.parse(ite.fieldMultipleValue);
        }
      }
      this.extendWorkorderFieldinfo = extendWorkorderFieldinfo;
      this.loading.close();
    },
    closeEditbox() {
      this.update_problem_classification = false;
    },
    // 编辑表单
    editFromStart() {
      this.againRestart = false;
      const that = this;

      const param = {
        workorderId: that.baseWorkorderInfo.workorder_id,
        formId: that.baseWorkorderInfo.form_id,
        isAuth: false
      };
      that.getNodeList(param);
    },
    // 获取editfromData值
    getNodeList(param) {
      const that = this;
      return getNodeList(param)
        .then(response => {
          if (response.msg === 'success') {
            const data = response.data.list;
            if (response.data.list.length) {
              that.editfromData = data;
              that.layDatas = response.data.layout;
              if (!that.layDatas && that.editfromData.length) {
                const arr = [];
                for (let i = 0; i < that.editfromData.length; i++) {
                  arr.push([1]);
                }
                //布局 有 n 行（n 等于 that.editfromData.length）每行只有一列
                that.layDatas = arr.map(cols => cols.join('-')).join();
              }
              // console.log('查询编辑节点字段:', response);
              // console.log('查询右侧返回字段:', that.extendWorkorderFieldinfo);
              //   fieldType: 0, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
              that.extendWorkorderFieldinfo.forEach(function(item, idex) {
                that.editfromData.forEach(function(nodeItem, idx) {
                  if (item.fieldCode === nodeItem.fieldCode) {
                    switch (item.fieldType) {
                      case 0:
                      case 1:
                      case 5:
                      case 8:
                      case 9:
                        if (!item.fieldSingleValue) {
                          item.fieldSingleValue = '';
                        }
                        that.addEditFormDefultValue(
                          nodeItem,
                          item.fieldSingleValue
                        );
                        break;
                      case 2:
                        if (!item.fieldSingleValue) {
                          item.fieldSingleValue = '';
                        }
                        that.addEditFormDefultValue(
                          nodeItem,
                          item.fieldSingleValue
                        );
                        break;
                      case 3:
                        if (!item.fieldSingleValue) {
                          item.fieldSingleValue = '';
                        }
                        that.addEditFormDefultValue(
                          nodeItem,
                          item.fieldSingleValue
                        );
                        break;
                      case 4:
                      case 6:
                        if (!item.fieldMultipleValue) {
                          item.fieldMultipleValue = '';
                        }
                        that.addEditFormDefultValue(
                          nodeItem,
                          item.fieldMultipleValue
                        );
                        break;
                      case 7:
                        if (!item.fieldSingleValue) {
                          item.fieldSingleValue = '';
                        }
                        that.addEditFormDefultValue(
                          nodeItem,
                          item.fieldSingleValue
                        );
                        break;
                      // eslint-disable-next-line no-case-declarations
                      case 10:
                        if (!item.fieldMultipleValue) {
                          item.fieldMultipleValue = [];
                        }
                        const enclosureItem = that.initEnclosure(
                          item.fieldMultipleValue
                        );
                        that.addEditFormDefultValue(nodeItem, enclosureItem);
                        that.$nextTick(() => {
                          if (that.$refs['edite' + nodeItem.fieldCode]) {
                            that.$refs[
                              'edite' + nodeItem.fieldCode
                            ].initFiles();
                          }
                        });
                        break;
                    }
                  } else {
                    // 隐藏当前项
                  }
                });
              });
              that.editForm = true;
              // that.$nextTick(() => {
              //   // console.log('3333', that.$refs('editBox'));
              //   // window.scrollTo(0, 0);
              //   // console.log(
              //   //   'document.body.scrollTop',
              //   //   document.documentElement.scrollTop
              //   // );
              // });
            }
          } else {
            that.$message({
              type: 'info',
              message: response.msg
            });
          }
        })
        .catch(function(error) {
          // console.log(error);
          that.$XyyMessage.error(error);
        });
    },
    /** 根据nodeItem和fieldType赋值 */
    addEditFormDefultValue(nodeItem, value) {
      //   fieldType: 0, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
      switch (nodeItem.fieldType) {
        case 0:
        case 1:
        case 8:
        case 9:
          nodeItem.defaultValue = value;
          break;
        case 5:
          // console.log(nodeItem);
          if (typeof nodeItem.optionSettings === 'string') {
            nodeItem.optionSettings = JSON.parse(nodeItem.optionSettings);
            nodeItem.optionSettings.dateOptions.dateValue = new Date(
              value
            ).getTime();
          } else {
            nodeItem.optionSettings.dateOptions.dateValue = new Date(
              value
            ).getTime();
          }
          break;
        case 2:
          if (typeof nodeItem.optionSettings === 'string') {
            let optionSettings = nodeItem.optionSettings.replace(
              /(&quot;)/g,
              '"'
            );
            optionSettings = JSON.parse(optionSettings);
            optionSettings.selectOptions.optionsValue = value
              ? parseInt(value)
              : optionSettings.selectOptions.optionsValue === 0
                ? 0
                : '';
            nodeItem.optionSettings = optionSettings;
          } else {
            nodeItem.optionSettings.selectOptions.optionsValue = value
              ? parseInt(value)
              : '';
            nodeItem.optionSettings = nodeItem.optionSettings;
          }

          break;
        case 3:
          if (typeof nodeItem.optionSettings === 'string') {
            let optionSettings = nodeItem.optionSettings.replace(
              /(&quot;)/g,
              '"'
            );
            optionSettings = JSON.parse(optionSettings);
            optionSettings.radioOptions.optionsValue = parseInt(value);
            nodeItem.optionSettings = optionSettings;
          } else {
            nodeItem.optionSettings.radioOptions.optionsValue = parseInt(value);
          }
          break;
        case 4: // 多选
          if (typeof nodeItem.optionSettings === 'string') {
            let optionSettings = nodeItem.optionSettings.replace(
              /(&quot;)/g,
              '"'
            );
            optionSettings = JSON.parse(optionSettings);
            optionSettings.checkedOptions.optionsValue = Object.keys(
              value
            ).map(item => parseInt(item));
            nodeItem.optionSettings = optionSettings;
          } else {
            nodeItem.optionSettings.checkedOptions.optionsValue = Object.keys(
              value
            ).map(item => parseInt(item));
          }
          break;
        case 6:
          if (typeof nodeItem.optionSettings === 'string') {
            let optionSettings = nodeItem.optionSettings.replace(
              /(&quot;)/g,
              '"'
            );
            optionSettings = JSON.parse(optionSettings);
            optionSettings.treeOptions.optionsValue = Object.keys(value).map(
              item => {
                return isNaN(parseInt(item)) ? item : parseInt(item);
              }
            );
            nodeItem.optionSettings = optionSettings;
          } else {
            nodeItem.optionSettings.treeOptions.optionsValue = Object.keys(
              value
            ).map(item => parseInt(item));
          }
          // 问题分类记录初始值
          if (nodeItem.fieldCode === 's1164724692221825024') {
            localStorage.setItem(
              'oldProblemVal',
              JSON.stringify(nodeItem.optionSettings.treeOptions.optionsValue)
            );
          }
          break;
        case 7:
          if (typeof nodeItem.optionSettings === 'string') {
            let optionSettings = nodeItem.optionSettings.replace(
              /(&quot;)/g,
              '"'
            );
            optionSettings = JSON.parse(optionSettings);
            const detailArea = value.split(' ')[1];
            const area = value.split(' ')[0].split(',');
            optionSettings.cityOptions.optionsValue = area;
            if (detailArea) {
              optionSettings.cityOptions.optionsArray = detailArea;
            }
            nodeItem.optionSettings = optionSettings;
          } else {
            nodeItem.optionSettings.cityOptions.optionsValue = Object.keys(
              value
            ).map(item => parseInt(item));
          }
          break;
        case 10:
          if (typeof nodeItem.optionSettings === 'string') {
            let optionSettings = nodeItem.optionSettings.replace(
              /(&quot;)/g,
              '"'
            );
            optionSettings = JSON.parse(optionSettings);
            optionSettings.fileObj.optionsArray = value;
            optionSettings.fileObj.optionsValue = value.map(el => el.url);

            const optionCopy = JSON.stringify(optionSettings);
            nodeItem.optionSettings = optionCopy;
          }
          break;
      }
      return;
    },
    // 附件初始化
    initEnclosure(item) {
      return initFileDatas(item);
    },
    // 查询列表回复文件
    listReplyFieldByWorkorderId(orderParam) {
      const that = this;
      return listReplyFieldByWorkorderId(orderParam)
        .then(response => {
          // console.log('查询列表回复文件:', response);
          const data = response.data;
          if (data && data.list.length > 0) {
            this.replyLayDatas = data.layout;
            that.sheetPre = data.list;
            if (!that.replyLayDatas && that.sheetPre.length) {
              const arr = [];
              for (let i = 0; i < that.sheetPre.length; i++) {
                if (that.sheetPre[i].systemFieldFlag === 1) {
                  arr.push([1]);
                }
              }
              that.replyLayDatas = arr.map(cols => cols.join('-')).join();
            }
            this.$nextTick(() => {
              document.querySelector('.el-divider--vertical').style.height =
                'auto';
            });
          }
        })
        .catch(function(error) {
          this.$XyyMessage.warning(error);
        });
    },
    // 处理级联（问题分类）改变
    handleSelVal(Array, callback) {
      const that = this;
      const Modal = Array[0];
      // 判断是否是系统字段问题分类
      if (Modal.fieldCode === 's1164724692221825024') {
        // 初始问题分类值不等于新值update_problem_classification参数为true
        const oldProblemVal = localStorage.getItem('oldProblemVal');
        const NewProblemVal = JSON.stringify(
          Modal.optionSettings.treeOptions.optionsValue
        );
        if (oldProblemVal !== NewProblemVal) {
          that.update_problem_classification = true;
        }
        const TmpCode = Array[1]; // Array[1];'1210821830613536768'
        localStorage.setItem('oldCostomArr', JSON.stringify(that.editfromData));
        // 根据选项结果查接口获取自定义字段  newCostomArr=[]
        // console.log('that.workorderId:', that.workorderId);
        if (TmpCode === '') {
          const param = {
            workorderId: that.baseWorkorderInfo.workorder_id,
            formId: that.baseWorkorderInfo.form_id,
            isAuth: false
          };
          getNodeList(param)
            .then(response => {
              if (response.msg === 'success') {
                const data = response.data;
                this.layDatas = response.data.layout;
                if (response.data.list.length) {
                  const newCostomArr = data.list;
                  const oldCostomArr = JSON.parse(
                    localStorage.getItem('oldCostomArr')
                  ); // 获取oldCostomArr
                  newCostomArr.forEach(el => {
                    oldCostomArr.forEach(item => {
                      if (el.fieldCode === item.fieldCode) {
                        const it = typeCopyVal(item, el);
                        el = it;
                        if (el.fieldType === 10) {
                          this.$nextTick(() => {
                            if (this.$refs['edite' + item.fieldCode]) {
                              this.$refs['edite' + item.fieldCode].initFiles();
                            }
                          });
                        }
                      }
                    }, this);
                  }, this);
                  that.editfromData = [...newCostomArr];
                  if (!that.layDatas && that.editfromData.length) {
                    const arr = [];
                    for (let i = 0; i < that.editfromData.length; i++) {
                      arr.push([1]);
                    }
                    that.layDatas = arr.map(cols => cols.join('-')).join();
                  }
                }

                callback && callback();
              } else {
                that.$message({
                  type: 'info',
                  message: response.msg
                });
              }
            })
            .catch(function(error) {
              that.$XyyMessage.error(error);
            });
        } else {
          listTemplateFieldAuthByCode(
            TmpCode,
            that.workorderId,
            this.baseWorkorderInfo.form_type_id
          ).then(res => {
            if (res.code === 1) {
              this.layDatas = res.data.layout;
              const newCostomArr = res.data.list;
              const oldCostomArr = JSON.parse(
                localStorage.getItem('oldCostomArr')
              ); // 获取oldCostomArr
              newCostomArr.forEach(el => {
                oldCostomArr.forEach(item => {
                  if (el.fieldCode === item.fieldCode) {
                    const it = typeCopyVal(item, el);
                    el = it;
                    if (el.fieldType === 10) {
                      this.$nextTick(() => {
                        if (this.$refs['edite' + item.fieldCode]) {
                          this.$refs['edite' + item.fieldCode].initFiles();
                        }
                      });
                    }
                  }
                }, this);
              }, this);
              that.editfromData = [...newCostomArr];
              if (!that.layDatas && that.editfromData.length) {
                const arr = [];
                for (let i = 0; i < that.editfromData.length; i++) {
                  arr.push([1]);
                }
                that.layDatas = arr.map(cols => cols.join('-')).join();
              }

              callback && callback();
            }
          });
        }
      }
    },
    getResult(arr, value, i) {
      arr.forEach(item => {
        if (item.value === value[i]) {
          if (item.children && item.children.length > 0) {
            i++;
            this.labelArray.push(item.label);
            this.getResult(item.children, value, i);
          } else {
            this.labelArray.push(item.label);
          }
        }
      });
    },
    // 编辑工单 保存修改
    updateField: function() {
      const that = this;
      this.repeatWorksheet.addSheetCheckHid = false;
      let uploading = false;
      that.editfromData.forEach(el => {
        if (el.fieldType === 10) {
          if (
            this.$refs['ref' + el.fieldCode] &&
            this.$refs['ref' + el.fieldCode].uploading
          ) {
            uploading = true;
          }
        }
      }, this);
      if (uploading) {
        this.$XyyMessage.warning('尚有未上传完成的附件，请稍后');
        return;
      }
      var result = [];
      const refList = this.$refs;
      for (var i in refList) {
        const aaa =
          refList[i] && typeof refList[i] !== 'undefined' && refList[i] !== 0;
        if (aaa && i.indexOf('edite') !== -1) {
          var rest = new Promise((resolve, reject) => {
            if (refList[i][0]) {
              refList[i][0].submitForm().then(
                valid => {
                  resolve();
                },
                () => {
                  reject();
                }
              );
            } else {
              refList[i].submitForm().then(
                valid => {
                  resolve();
                },
                () => {
                  reject();
                }
              );
            }
          });
          result.push(rest);
        }
      }
      Promise.all(result)
        .then(res => {
          that.combUpdataVal();
          // console.log('成功数据列表', that.editfromData);
        })
        .catch(res => {
          console.log('失败' + res);
        });
    },
    combUpdataVal() {
      const extendWorkorderField = [];
      const startValue = [];
      const that = this;
      let uploading = false;
      that.editfromData.forEach(function(item, index) {
        // fieldType: 1, // 字段类型
        // 0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期
        // 6:级联,7:省市区,8:电话,9:邮箱,10:附件
        const isShow = that.editGetAuthTypeStatus(item);
        if ((item.authType !== 0 || item.authTypeCondition === 2) && isShow) {
          // 客户名称优先取通过手机号匹配出来的名称
          if (item.fieldCode === 's1164456058157142016') {
            startValue.push({
              field_code: item.fieldCode,
              field_type: item.fieldType,
              field_value: that.tempMerchantInfo.realName || item.defaultValue.trim() || ''
            });
            if (that.isDefaultVal(item)) {
              // console.log('单行输入,多行输入默认值');
              extendWorkorderField.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: that.tempMerchantInfo.realName || item.defaultValue.trim() || ''
              });
              // console.log('单行输入,多行输入', item.defaultValue);
            }
          } else if (
            item.fieldType === 0 ||
            item.fieldType === 1 ||
            item.fieldType === 8 ||
            item.fieldType === 9
          ) {
            startValue.push({
              field_code: item.fieldCode,
              field_type: item.fieldType,
              field_value:
                item.fieldType === 0
                  ? item.defaultValue.trim()
                  : item.defaultValue
            });
            if (that.isDefaultVal(item)) {
              // console.log('单行输入,多行输入默认值');
              extendWorkorderField.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value:
                  item.fieldType === 0
                    ? item.defaultValue.trim()
                    : item.defaultValue
              });
              // console.log('单行输入,多行输入', item.defaultValue);
            }
          } else if (item.fieldType === 2) {
            if (
              !item.optionSettings.selectOptions ||
              item.optionSettings.selectOptions.optionsValue === ''
            ) {
              startValue.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: {
                  option_name: '',
                  option_value: ''
                }
              });
            } else {
              startValue.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: {
                  option_name: item.optionSettings.selectOptions.optionsArray.filter(
                    (el, i) =>
                      el.val
                        ? el.val ===
                        item.optionSettings.selectOptions.optionsValue
                        : i === item.optionSettings.selectOptions.optionsValue
                  )[0].optionsDesc,
                  option_value: item.optionSettings.selectOptions.optionsValue
                }
              });
            }
            if (that.isDefaultVal(item)) {
              // console.log('下拉不是默认值');
              if (
                !item.optionSettings.selectOptions ||
                item.optionSettings.selectOptions.optionsValue === ''
              ) {
                extendWorkorderField.push({
                  field_code: item.fieldCode,
                  field_type: item.fieldType,
                  field_value: {
                    option_name: '',
                    option_value: ''
                  }
                });
              } else {
                extendWorkorderField.push({
                  field_code: item.fieldCode,
                  field_type: item.fieldType,
                  field_value: {
                    option_name: item.optionSettings.selectOptions.optionsArray.filter(
                      (el, i) =>
                        el.val
                          ? el.val ===
                          item.optionSettings.selectOptions.optionsValue
                          : i === item.optionSettings.selectOptions.optionsValue
                    )[0].optionsDesc,
                    option_value: item.optionSettings.selectOptions.optionsValue
                  }
                });
              }
            }
          } else if (item.fieldType === 3) {
            if (
              !item.optionSettings.radioOptions ||
              item.optionSettings.radioOptions.optionsValue === ''
            ) {
              startValue.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: {
                  option_name: '',
                  option_value: ''
                }
              });
            } else {
              if (
                item.optionSettings.radioOptions.optionsArray.some((el, i) =>
                  el.val
                    ? el.val === item.optionSettings.radioOptions.optionsValue
                    : i === item.optionSettings.radioOptions.optionsValue
                )
              ) {
                startValue.push({
                  field_code: item.fieldCode,
                  field_type: item.fieldType,
                  field_value: {
                    option_name: item.optionSettings.radioOptions.optionsArray.filter(
                      (el, i) =>
                        el.val
                          ? el.val ===
                          item.optionSettings.radioOptions.optionsValue
                          : i === item.optionSettings.radioOptions.optionsValue
                    )[0].optionsDesc,
                    option_value: item.optionSettings.radioOptions.optionsValue
                  }
                });
              }
            }
            if (that.isDefaultVal(item)) {
              // console.log('单选不是默认值');
              if (
                !item.optionSettings.radioOptions ||
                item.optionSettings.radioOptions.optionsValue === ''
              ) {
                extendWorkorderField.push({
                  field_code: item.fieldCode,
                  field_type: item.fieldType,
                  field_value: {
                    option_name: '',
                    option_value: ''
                  }
                });
              } else {
                if (
                  item.optionSettings.radioOptions.optionsArray.some((el, i) =>
                    el.val
                      ? el.val === item.optionSettings.radioOptions.optionsValue
                      : i === item.optionSettings.radioOptions.optionsValue
                  )
                ) {
                  extendWorkorderField.push({
                    field_code: item.fieldCode,
                    field_type: item.fieldType,
                    field_value: {
                      option_name: item.optionSettings.radioOptions.optionsArray.filter(
                        (el, i) =>
                          el.val
                            ? el.val ===
                            item.optionSettings.radioOptions.optionsValue
                            : i ===
                            item.optionSettings.radioOptions.optionsValue
                      )[0].optionsDesc,
                      option_value:
                      item.optionSettings.radioOptions.optionsValue
                    }
                  });
                }
              }
            }
          } else if (item.fieldType === 4) {
            const obj = {};
            for (const i of item.optionSettings.checkedOptions.optionsValue) {
              if (item.optionSettings.checkedOptions.optionsArray[i]) {
                obj[i] =
                  item.optionSettings.checkedOptions.optionsArray[
                    i
                  ].optionsDesc;
              } else if (
                item.optionSettings.checkedOptions.optionsArray.some(
                  el => el.val === i
                )
              ) {
                obj[i] = item.optionSettings.checkedOptions.optionsArray.filter(
                  el => el.val === i
                )[0].optionsDesc;
              }
            }
            startValue.push({
              field_code: item.fieldCode,
              field_type: item.fieldType,
              field_value: {
                option_name: Object.values(obj).join('/'),
                option_value: Object.keys(obj).join(',')
              }
            });
            if (that.isDefaultVal(item)) {
              // console.log('多选不是默认值');

              // extendWorkorderField.push({
              //   fieldCode: item.fieldCode,
              //   fieldMultipleValue: JSON.stringify(obj),
              //   sort: index
              // });
              extendWorkorderField.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: {
                  option_name: Object.values(obj).join('/'),
                  option_value: Object.keys(obj).join(',')
                }
              });
              // console.log('多选', obj);
            }
          } else if (item.fieldType === 5) {
            if (
              !item.optionSettings.dateOptions ||
              item.optionSettings.dateOptions === ''
            ) {
              startValue.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: ''
              });
            } else {
              const dateOptions = item.optionSettings.dateOptions;
              const dateValue = dataTime.dataTime(
                dateOptions.dateValue,
                dateOptions.dateSelect
              );
              startValue.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: dateValue
              });
              // console.log('日期', dateValue
            }
            if (that.isDefaultVal(item)) {
              // console.log('日期不是默认值');
              if (
                !item.optionSettings.dateOptions ||
                item.optionSettings.dateOptions === ''
              ) {
                // extendWorkorderField.push({
                //   fieldCode: item.fieldCode,
                //   fieldSingleValue: '',
                //   sort: index
                // });
                extendWorkorderField.push({
                  field_code: item.fieldCode,
                  field_type: item.fieldType,
                  field_value: ''
                });
              } else {
                const dateOptions = item.optionSettings.dateOptions;
                const dateValue = dataTime.dataTime(
                  dateOptions.dateValue,
                  dateOptions.dateSelect
                );
                // extendWorkorderField.push({
                //   fieldCode: item.fieldCode,
                //   fieldSingleValue: dateValue,
                //   sort: index
                // });
                extendWorkorderField.push({
                  field_code: item.fieldCode,
                  field_type: item.fieldType,
                  field_value: dateValue
                });
                // console.log('日期', dateValue);
              }
            }
          } else if (item.fieldType === 6) {
            if (
              item.optionSettings.treeOptions ||
              item.optionSettings.treeOptions.optionsValue
            ) {
              const val = JSON.parse(
                JSON.stringify(item.optionSettings.treeOptions.optionsValue)
              );
              const options = JSON.parse(
                JSON.stringify(item.optionSettings.treeOptions.optionsArray)
              );
              const i = 0;
              that.labelArray.splice(0, that.labelArray.length);
              that.getResult(options, val, i);
            }

            startValue.push({
              field_code: item.fieldCode,
              field_type: item.fieldType,
              field_value: {
                option_name: that.labelArray.join('/'),
                option_value: item.optionSettings.treeOptions.optionsValue.join(
                  ','
                )
              }
            });
            if (that.isDefaultVal(item)) {
              const obj = {};
              if (item.optionSettings.treeOptions.optionsLabel) {
                const optionsLabelArr =
                  item.optionSettings.treeOptions.optionsLabel;
                item.optionSettings.treeOptions.optionsValue.forEach(function(
                  item,
                  index
                ) {
                  obj[item] = optionsLabelArr[index];
                });
              }
              // console.log('级联不是默认值')
              extendWorkorderField.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: {
                  option_name: Object.values(obj).join('/'),
                  option_value: Object.keys(obj).join(',')
                }
              });
              // console.log('级联', obj);
            }
          } else if (item.fieldType === 7) {
            startValue.push({
              field_code: item.fieldCode,
              field_type: item.fieldType,
              field_value:
                item.optionSettings.cityOptions.optionsValue +
                ' ' +
                item.optionSettings.cityOptions.optionsArray
            });
            if (that.isDefaultVal(item)) {
              // console.log('地址不是默认值');
              // extendWorkorderField.push({
              //   fieldCode: item.fieldCode,
              //   fieldSingleValue:
              //     item.optionSettings.cityOptions.optionsValue +
              //     ' ' +
              //     item.optionSettings.cityOptions.optionsArray,
              //   sort: index
              // });
              extendWorkorderField.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value:
                  item.optionSettings.cityOptions.optionsValue +
                  ' ' +
                  item.optionSettings.cityOptions.optionsArray
              });
              // console.log(
              //   '省市区',
              //   item.optionSettings.cityOptions.optionsValue +
              //     ' ' +
              //     item.optionSettings.cityOptions.optionsArray
              // );
            }
          } else if (item.fieldType === 10) {
            if (
              that.$refs['edite' + item.fieldCode] &&
              that.$refs['edite' + item.fieldCode].uploading
            ) {
              uploading = true;
              return;
            }
            let obj = [];
            const optionsArray =
              item.optionSettings && !item.optionSettings.fileObj
                ? JSON.parse(item.optionSettings).fileObj.optionsArray
                : item.optionSettings.fileObj.optionsArray;
            if (optionsArray.length > 0) {
              obj = JSON.stringify(optionsArray);
              obj = JSON.parse(obj);
            }
            const objCopy = [];
            for (let i = 0; i < obj.length; i++) {
              objCopy.push(obj[i].data);
            }
            // console.log('obCopyj222', objCopy);
            if (objCopy.length > 0) {
              startValue.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: JSON.stringify(objCopy)
              });
            } else {
              startValue.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: []
              });
            }
            if (that.isDefaultVal(item)) {
              // console.log('附件不是默认值');
              // console.log('obCopyj222', objCopy);
              if (objCopy.length > 0) {
                extendWorkorderField.push({
                  field_code: item.fieldCode,
                  field_type: item.fieldType,
                  field_value: JSON.stringify(objCopy)
                });
              } else {
                extendWorkorderField.push({
                  field_code: item.fieldCode,
                  field_type: item.fieldType,
                  field_value: []
                });
              }
            }
          }
        }
      });
      this.extendWorkorderField = extendWorkorderField;
      if (uploading) {
        this.$XyyMessage.warning('尚有未上传完成的附件，请稍后');
        return;
      }
      this.againRestart ? this.startAgai(startValue) : this.updateWorkorder();
    },
    startAgai(startValue) {
      const params = {
        base_field: {
          edited: this.extendWorkorderField.length !== 0,
          workorder_id: this.baseWorkorderInfo.workorder_id,
          im_client_id: this.tempMerchantInfo.id || ''
        },
        custom_field: startValue
      };
      startAgai(params).then(res => {
        if (res.code === 1) {
          this.$XyyMessage({
            type: 'success',
            message: '保存成功!'
          });
          this.editForm = false;
          this.addModifyFlag();
          // this.$store.dispatch('tagsView/delView', this.$route);
          // this.$router.push({
          //   path: this.$route.query.currentUrl,
          //   query: { t: new Date().getTime() }
          // });
        } else {
          this.$XyyMessage.warning(res.msg);
        }
      });
    },
    updateWorkorder() {
      const that = this;
      // 更新工单
      if (that.extendWorkorderField.length === 0) {
        that.$message({
          type: 'info',
          message: '无更新数据!'
        });
        return;
      }
      const param = {
        base_field: {
          workorder_id: that.workorderId,
          update_problem_classification: that.update_problem_classification,
          template_id: that.editfromData[0]
            ? that.editfromData[0].templateId
            : '',
          im_client_id: that.tempMerchantInfo.id || ''
        },
        custom_field: that.extendWorkorderField
      };

      // console.log('更新对象：', JSON.stringify(param));
      return updateWorkorder(param).then(response => {
        if (response.msg === 'success') {
          that.$message({
            type: 'success',
            message: '更新成功!'
          });
          this.addModifyFlag(true);
          const orderParam = { workorderId: that.workorderId };
          that.getWorkorderInfoAndReplyInfo(orderParam); // 获取字段信息
          that.$refs['processs'].getRecordDatas(); // 获取流转记录-催单记录-追加记录
          that.editForm = false;
        } else {
          that.$message({
            type: 'info',
            message: response.msg
          });
        }
      });
    },
    isDefaultVal(newItem) {
      // 0722日修改所有字段改动判断逻辑：（当新字段 存在老模板中 && 新值和旧值不相等）|| （当新字段 不存在老模板中 && 值不为空 && 问题分类标识为true）
      const that = this;
      const isField = that.extendWorkorderFieldinfo.filter(oldItem => {
        return newItem.fieldCode === oldItem.fieldCode;
      });

      if (
        newItem.fieldType === 0 ||
        newItem.fieldType === 1 ||
        newItem.fieldType === 8 ||
        newItem.fieldType === 9 ||
        newItem.fieldType === 2 ||
        newItem.fieldType === 3 ||
        newItem.fieldType === 7
      ) {
        // 原 0：单行输入:1：多行输入，8：电话，9：邮箱
        let newValue = newItem.defaultValue; // 新值
        const oldValue = isField.length ? isField[0].fieldSingleValue : ''; // 旧值

        if (newItem.fieldType === 2) {
          // fieldType === 2 下拉列表取值
          newValue = newItem.optionSettings.selectOptions.optionsValue;
        }

        if (newItem.fieldType === 3) {
          // fieldType === 3  单选按钮取值
          newValue = newItem.optionSettings.radioOptions.optionsValue;
        }

        if (newItem.fieldType === 7) {
          // fieldType === 7 省市区 取值
          newValue =
            newItem.optionSettings.cityOptions.optionsValue +
            ' ' +
            newItem.optionSettings.cityOptions.optionsArray;
        }

        if (
          (isField.length && oldValue !== newValue) ||
          (!isField.length && newValue && that.update_problem_classification)
        ) {
          return true;
        } else {
          return false;
        }
      }
      if (newItem.fieldType === 4) {
        // 多选
        if (isField.length) {
          const oldItem = isField[0];
          if (
            newItem.optionSettings.checkedOptions.optionsValue &&
            newItem.optionSettings.checkedOptions.optionsValue.length === 1 &&
            !newItem.optionSettings.checkedOptions.optionsValue[0]
          ) {
            return false;
          } else {
            return (
              [...newItem.optionSettings.checkedOptions.optionsValue]
                .sort()
                .toString() !== Object.keys(oldItem.fieldMultipleValue)
                .sort()
                .toString()
            );
          }
        } else if (
          newItem.optionSettings.checkedOptions.optionsValue.length &&
          that.update_problem_classification
        ) {
          return true;
        }
      }
      if (newItem.fieldType === 5) {
        // 日期
        const dateOptions = newItem.optionSettings.dateOptions;
        const dateValue = dateOptions.dateValue
          ? dataTime.dataTime(dateOptions.dateValue, dateOptions.dateSelect)
          : '';

        if (isField.length) {
          const oldItem = isField[0];
          if (
            (!dateValue && oldItem.fieldSingleValue !== '') ||
            dateValue !== oldItem.fieldSingleValue
          ) {
            return true;
          }
        } else if (dateValue && that.update_problem_classification) {
          return true;
        }
      }
      if (newItem.fieldType === 6) {
        // 级联
        if (isField.length) {
          const oldItem = isField[0];
          const jilianArr = Object.values(oldItem.fieldMultipleValue);
          // console.log('jilianArr11:', JSON.stringify(jilianArr));
          let newArr = null;
          if (newItem.optionSettings.treeOptions.optionsLabel) {
            newArr = JSON.stringify(
              newItem.optionSettings.treeOptions.optionsLabel
            );
          } else {
            newArr = JSON.stringify(jilianArr);
          }
          // console.log('jilianArr22:', newArr);
          return JSON.stringify(jilianArr) !== newArr;
        } else if (
          newItem.optionSettings.treeOptions.optionsLabel &&
          that.update_problem_classification
        ) {
          return true;
        }
      }
      if (newItem.fieldType === 10) {
        // 附件
        if (isField.length) {
          const oldItem = isField[0];
          newItem.optionSettings =
            newItem.optionSettings && !newItem.optionSettings.fileObj
              ? JSON.parse(newItem.optionSettings)
              : newItem.optionSettings;
          if (newItem.optionSettings.fileObj.optionsArray === '') {
            return (
              newItem.fieldCode === oldItem.fieldCode &&
              compareFile(
                newItem.optionSettings.fileObj.optionsArray,
                oldItem.fieldMultipleValue
              )
            );
          } else {
            return (
              newItem.fieldCode === oldItem.fieldCode &&
              compareFile(
                newItem.optionSettings.fileObj.optionsArray.map(el => el.data),
                oldItem.fieldMultipleValue
              )
            );
          }
        } else if (
          newItem.optionSettings.fileObj.optionsArray.length &&
          that.update_problem_classification
        ) {
          return true;
        }
      }
    },
    /** 追加去发布log_btn */
    issueAppend() {
      if (this.uploadingList === true) {
        this.$message('您还有正在上传的文件');
        return false;
      }
      const that = this;
      if (that.appendNotesCon === '') {
        that.$message({
          type: 'info',
          message: '请输入内容!'
        });
        return false;
      }
      that.previewModal.forEach((item, index) => {
        that.previewModalarr.push(item.data);
      });
      const param = {
        appendix: JSON.stringify(that.previewModalarr),
        appendContent: that.appendNotesCon,
        workorderId: that.workorderId
      };

      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      return saveWorkorderAppendNote(param, that)
        .then(response => {
          // console.log('追加发布:', response);
          if (response.msg === 'success') {
            that.$message({
              type: 'success',
              message: '追加成功!'
            });
          }
          that.appendNotesCon = '';
          that.issueAppendHid = false;
          that.$refs['myUploader'].clearFiles(); // 清空上传列表
          that.previewModal = []; // 清空上传的参数
          that.previewModalarr = []; // 清空arr数组
          that.$refs['processs'].getRecordDatas();
          loading.close();
        })
        .catch(error => {
          this.$message({
            type: 'info',
            message: '追加失败!'
          });
          that.issueAppendHid = false;
          console.log(error);
          loading.close();
        });
    },
    reOnline() {
      this.againRestart = true;
      const that = this;
      // console.log('that.$refs', that.$refs);
      const param = {
        workorderId: that.baseWorkorderInfo.workorder_id,
        formId: that.baseWorkorderInfo.form_id,
        isAuth: false
      };
      that.getNodeList(param);
    },
    workorderLocationCheck() {
      let customerLocation = '';
      this.editfromData.forEach(el => {
        // 客户所在地
        if (
          el.fieldCode === 's1176037136521105408' &&
          el.optionSettings.selectOptions.optionsValue
        ) {
          customerLocation = el.optionSettings.selectOptions.optionsArray.filter(
            opt => opt.val === el.optionSettings.selectOptions.optionsValue
          )[0].optionsDesc;
        }
      });
      if (customerLocation) {
        console.log(this.sheetNameVal);
        getWorkorderLocationCheck({
          customerLocation,
          formBaseId: this.baseWorkorderInfo.form_id
        }).then(res => {
          if (res.code === 1) {
            this.workorderRepeateCheck();
          } else {
            this.locationStatus = true;
            this.locationMsg = res.msg;
          }
        });
      } else {
        this.workorderRepeateCheck();
      }
    },
    workorderRepeateCheck() {
      this.locationStatus = false;
      const that = this;
      const orderAndProb = [];
      that.editfromData.forEach(el => {
        if (
          el.fieldCode === 's1164724692221825024' ||
          el.fieldCode === 's1176037234466492416'
        ) {
          orderAndProb.unshift(el);
        }
      });
      // console.log('问题分类or订单编号', orderAndProb);
      if (orderAndProb.length > 0) {
        let problemClassification, orderNumber;
        orderAndProb.forEach(el => {
          if (el.fieldCode === 's1164724692221825024') {
            // console.log('问题分类');
            problemClassification = el.optionSettings.treeOptions.optionsValue.join(
              ','
            );
          } else if (el.fieldCode === 's1176037234466492416') {
            // console.log('订单编号');
            orderNumber = el.defaultValue;
          }
        });

        console.log('问题分类or订单编号', problemClassification, orderNumber);

        // 重置重复工单参数
        that.repeatWorksheet = {
          workorderId: '',
          workorderNum: '',
          addSheetCheckHid: false
        };

        getWorkorderRepeateCheck(
          orderNumber,
          problemClassification,
          this.baseWorkorderInfo.form_type_id,
          this.baseWorkorderInfo.workorder_num
        ).then(res => {
          // console.log('获取成功：', res);
          if (res.code) {
            that.restart();
          } else {
            /**
             that.workorderId = res.data.workorder_id;
             that.workorderNum = res.data.workorder_num;
             that.addSheetCheckHid = true;
             */
            that.repeatWorksheet = {
              workorderId: res.data.workorder_id,
              workorderNum: res.data.workorder_num,
              addSheetCheckHid: true
            };
            return false;
          }
        });
      } else {
        that.restart(); // 没有问题分类or订单编号
      }
    },
    restart() {
      this.updateField();
    },
    handleSheetRese() {
      if (this.useAssign) {
        if (this.$refs['assignForm']) {
          this.$refs['assignForm'].validate(valid => {
            if (valid) {
              if (this.$refs['restartForm']) {
                this.$refs['restartForm'].validate(valid => {
                  if (valid) {
                    // 整理传参
                    const params = {
                      fromFlowId: this.baseWorkorderInfo.flow_id,
                      toNodeId: this.assignForm.assignNode,
                      transferType: this.assignType === 'people' ? 1 : 0,
                      useTransfer: this.useAssign,
                      workorderId: this.workorderId,
                      reason: this.restartForm.restartReason
                    };
                    if (this.assignType === 'people') {
                      params.toUserGroupId = this.assignForm.assignUsergroup;
                      params.toUserId = this.assignForm.assignMembers;
                    }
                    restartWorkorder(params).then(res => {
                      if (res.code === 1) {
                        this.sheetResetShow = false;
                        this.addModifyFlag();
                        // this.$store.dispatch('tagsView/delView', this.$route);
                        // this.$router.push({
                        //   path: this.$route.query.currentUrl,
                        //   query: { t: new Date().getTime() }
                        // });
                        this.$XyyMessage.success('重启成功');
                      } else {
                        this.$XyyMessage.error(res.msg);
                      }
                    });
                  }
                });
              }
            }
          });
        }
      } else {
        if (this.$refs['restartForm']) {
          this.$refs['restartForm'].validate(valid => {
            if (valid) {
              // 整理传参
              const params = {
                fromFlowId: this.baseWorkorderInfo.flow_id,
                useTransfer: this.useAssign,
                workorderId: this.workorderId,
                reason: this.restartForm.restartReason
              };
              restartWorkorder(params).then(res => {
                if (res.code === 1) {
                  this.sheetResetShow = false;
                  this.addModifyFlag();
                  // this.$store.dispatch('tagsView/delView', this.$route);
                  // this.$router.push({
                  //   path: this.$route.query.currentUrl,
                  //   query: { t: new Date().getTime() }
                  // });
                  this.$XyyMessage.success('重启成功');
                } else {
                  this.$XyyMessage.error(res.msg);
                }
              });
            }
          });
        }
      }
    },

    debounce(func, delay) {
      let timeOut;
      return function() {
        clearTimeout(timeOut);
        const args = arguments;
        timeOut = setTimeout(() => {
          func.apply(this, args);
        }, delay);
      };
    },

    /** 退回工单 */
    returnSheetBtn() {
      const that = this;
      const param = {
        approvalStatus: 1, // 审批状态’0’:通过,’1’:’退回’
        backNode: that.returnSheetRadio, // 2020.4.14,rl,新增退回上一环节
        sendBackReason: that.returnSheetCon,
        workorderId: that.workorderId
      };
      that.returnSheetHid = false;
      return saveSendBackWorkOrderNew(param)
        .then(response => {
          // console.log('退回工单:', response);
          const { msg } = response;
          if (msg === 'success') {
            that.returnSheetCon = '';
            that.$message({
              message: '退回成功',
              type: 'success'
            });
            this.addModifyFlag();
            // that.$store.dispatch('tagsView/delView', that.$route);
            // that.$router.push({
            //   // path: '/' + that.pubUrl,
            //   path: this.$route.query.currentUrl,
            //   query: {
            //     t: new Date().getTime(),
            //     jump: true
            //   }
            // });
          } else {
            that.returnSheetCon = '';
            that.$message({
              message: msg,
              type: 'info'
            });
            this.addModifyFlag();
            // that.$store.dispatch('tagsView/delView', that.$route);
            // that.$router.push({
            //   // path: '/' + that.pubUrl,
            //   path: that.$route.query.currentUrl,
            //   query: {
            //     t: new Date().getTime(),
            //     jump: true
            //   }
            // });
          }
        })
        .catch(function(error) {
          that.returnSheetCon = '';
          console.log(error);
        });
    },
    submitReply(isCloseNode) {
      const that = this;
      if (isCloseNode === 'closeNode') {
        this.isCloseNode = true;
      } else {
        this.isCloseNode = false;
      }
      // 校验;
      var result = [];
      const refList = this.$refs;
      for (var i in refList) {
        if (i.indexOf('ref') !== -1 && refList[i]) {
          var rest = new Promise((resolve, reject) => {
            if (refList[i][0]) {
              refList[i][0].submitForm().then(
                valid => {
                  resolve();
                },
                () => {
                  reject();
                }
              );
            } else {
              refList[i].submitForm().then(
                valid => {
                  resolve();
                },
                () => {
                  reject();
                }
              );
            }
          });
          result.push(rest);
        }
      }
      Promise.all(result)
        .then(res => {
          // console.log('详情回复校验通过' + res);
          // console.log('数据列表', that.sheetPre);
          that.combSelVal();
        })
        .catch(res => {
          console.log('详情回复校验失败' + res);
        });
    },
    combSelVal() {
      const that = this;
      let uploading = false;
      that.sheetPre.forEach(el => {
        if (el.fieldType === 10) {
          if (
            this.$refs['ref' + el.fieldCode] &&
            this.$refs['ref' + el.fieldCode].uploading
          ) {
            uploading = true;
          }
        }
      }, this);
      if (uploading) {
        this.$XyyMessage.warning('尚有未上传完成的附件，请稍后');
        return;
      }
      const replyObj = [];

      that.sheetPre.forEach(function(item, index) {
        // fieldType: 1, // 字段类型
        // 0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期
        // 6:级联,7:省市区,8:电话,9:邮箱,10:附件
        const isShow = that.getAuthTypeStatus(item);
        if ((item.authType !== 0 || item.authTypeCondition === 2) && isShow) {
          // 客户名称优先取通过手机号匹配出来的名称
          if (item.fieldCode === 's1164456058157142016') {
            replyObj.push({
              field_code: item.fieldCode,
              field_type: item.fieldType,
              field_value: that.tempMerchantInfo.realName || item.defaultValue.trim() || ''
            });
          } else if (
            item.fieldType === 0 ||
            item.fieldType === 1 ||
            item.fieldType === 8 ||
            item.fieldType === 9
          ) {
            replyObj.push({
              field_code: item.fieldCode,
              field_type: item.fieldType,
              field_value: item.defaultValue.trim()
            });
            // console.log('单行输入,多行输入', item.defaultValue);
          } else if (item.fieldType === 2) {
            if (
              !item.optionSettings.selectOptions ||
              item.optionSettings.selectOptions.optionsValue === ''
            ) {
              replyObj.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: {
                  option_name: '',
                  option_value: ''
                }
              });
            } else {
              replyObj.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: {
                  option_name: item.optionSettings.selectOptions.optionsArray.filter(
                    (el, i) =>
                      el.val
                        ? el.val ===
                        item.optionSettings.selectOptions.optionsValue
                        : i === item.optionSettings.selectOptions.optionsValue
                  )[0].optionsDesc,
                  option_value: item.optionSettings.selectOptions.optionsValue
                }
              });
            }
          } else if (item.fieldType === 3) {
            if (
              !item.optionSettings.radioOptions ||
              item.optionSettings.radioOptions.optionsValue === ''
            ) {
              replyObj.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: {
                  option_name: '',
                  option_value: ''
                }
              });
            } else {
              if (
                // item.optionSettings.radioOptions.optionsArray[
                //   item.optionSettings.radioOptions.optionsValue
                // ]
                item.optionSettings.radioOptions.optionsArray.some((el, i) =>
                  el.val
                    ? el.val === item.optionSettings.radioOptions.optionsValue
                    : i === item.optionSettings.radioOptions.optionsValue
                )
              ) {
                replyObj.push({
                  field_code: item.fieldCode,
                  field_type: item.fieldType,
                  field_value: {
                    option_name: item.optionSettings.radioOptions.optionsArray.filter(
                      (el, i) =>
                        el.val
                          ? el.val ===
                          item.optionSettings.radioOptions.optionsValue
                          : i === item.optionSettings.radioOptions.optionsValue
                    )[0].optionsDesc,
                    option_value: item.optionSettings.radioOptions.optionsValue
                  }
                });
              }
            }
          } else if (item.fieldType === 4) {
            const obj = {};
            for (const i of item.optionSettings.checkedOptions.optionsValue) {
              if (item.optionSettings.checkedOptions.optionsArray[i]) {
                obj[i] =
                  item.optionSettings.checkedOptions.optionsArray[
                    i
                  ].optionsDesc;
              } else if (
                item.optionSettings.checkedOptions.optionsArray.some(
                  el => el.val === i
                )
              ) {
                obj[i] = item.optionSettings.checkedOptions.optionsArray.filter(
                  el => el.val === i
                )[0].optionsDesc;
              }
            }
            replyObj.push({
              field_code: item.fieldCode,
              field_type: item.fieldType,
              field_value: {
                option_name: Object.values(obj).join('/'),
                option_value: Object.keys(obj).join(',')
              }
            });
            // console.log('多选', obj);
          } else if (item.fieldType === 5) {
            if (
              !item.optionSettings.dateOptions ||
              item.optionSettings.dateOptions === ''
            ) {
              replyObj.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: ''
              });
            } else {
              const dateOptions = item.optionSettings.dateOptions;
              const dateValue = dataTime.dataTime(
                dateOptions.dateValue,
                dateOptions.dateSelect
              );
              replyObj.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: dateValue
              });
              // console.log('日期', dateValue);
            }
          } else if (item.fieldType === 6) {
            const obj = {};
            // console.log(
            //   'item.optionSettings.treeOptions.optionsValue:',
            //   item.optionSettings.treeOptions.optionsValue
            // );
            // console.log(
            //   'item.optionSettings.treeOptions.optionsArray:',
            //   item.optionSettings.treeOptions.optionsArray
            // );
            if (item.optionSettings.treeOptions.optionsLabel) {
              const optionsLabelArr =
                item.optionSettings.treeOptions.optionsLabel;
              item.optionSettings.treeOptions.optionsValue.forEach(function(
                item,
                index
              ) {
                obj[item] = optionsLabelArr[index];
              });
            }
            replyObj.push({
              field_code: item.fieldCode,
              field_type: item.fieldType,
              field_value: {
                option_name: Object.values(obj).join('/'),
                option_value: Object.keys(obj).join(',')
              }
            });
            // console.log('级联', obj);
          } else if (item.fieldType === 7) {
            replyObj.push({
              field_code: item.fieldCode,
              field_type: item.fieldType,
              field_value:
                item.optionSettings.cityOptions.optionsValue +
                ' ' +
                item.optionSettings.cityOptions.optionsArray
            });
            // console.log(
            //   '省市区',
            //   item.optionSettings.cityOptions.optionsValue +
            //     ' ' +
            //     item.optionSettings.cityOptions.optionsArray
            // );
          } else if (item.fieldType === 10) {
            // console.log('附件合并', item);
            let obj = [];
            const optionsArray = item.optionSettings.fileObj.optionsArray;
            // if (optionsArray.length > 0) {
            //   obj = [...optionsArray];
            // }
            if (optionsArray.length > 0) {
              obj = JSON.stringify(optionsArray);
              obj = JSON.parse(obj);
            }
            const objCopy = [];
            for (let i = 0; i < obj.length; i++) {
              objCopy.push(obj[i].data);
            }
            if (objCopy.length > 0) {
              replyObj.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: JSON.stringify(objCopy)
              });
            } else {
              replyObj.push({
                field_code: item.fieldCode,
                field_type: item.fieldType,
                field_value: []
              });
            }
          }
        }
      });

      this.replyObj = replyObj;
      this.saveWorkorderReply();
    },
    /** 提交回复 */
    saveWorkorderReply() {
      const that = this;
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      // 提交回复
      if (that.isCloseNode) {
        const param = {
          base_field: {
            approval_status: 0,
            workorder_id: this.workorderId,
            closeNode: true,
            im_client_id: that.tempMerchantInfo.id || ''
          },
          custom_field: that.replyObj
        };
        // console.log('关闭回复：', JSON.stringify(param));
        return saveWorkorderReply(param).then(response => {
          loading.close();
          // console.log('提交状态:', response);
          if (response.msg === 'success') {
            that.$message({
              type: 'success',
              message: '提交成功!'
            });
            this.addModifyFlag();
            // that.$store.dispatch('tagsView/delView', that.$route.query.currentUrl);
            // that.$router.push({
            //   // path: '/' + that.pubUrl,
            //   path: that.$route.query.currentUrl,
            //   query: {
            //     t: new Date().getTime(),
            //     jump: true
            //   }
            // });
          } else {
            that.$message({
              type: 'info',
              message: response.msg
            });
            this.addModifyFlag();
            // that.$store.dispatch('tagsView/delView', that.$route);
            // that.$router.push({
            //   // path: '/' + that.pubUrl,
            //   path: that.$route.query.currentUrl,
            //   query: {
            //     t: new Date().getTime(),
            //     jump: true
            //   }
            // });
          }
        });
      } else {
        const param = {
          base_field: {
            approval_status: 0,
            workorder_id: this.workorderId,
            im_client_id: that.tempMerchantInfo.id || ''
          },
          custom_field: that.replyObj
        };
        // console.log('提交回复：', JSON.stringify(param));
        return saveWorkorderReply(param)
          .then(response => {
            // console.log('提交状态:', response);
            if (response.msg === 'success') {
              that.$message({
                type: 'success',
                message: '提交成功!'
              });
              this.addModifyFlag();
              // that.$store.dispatch('tagsView/delView', that.$route);
              // that.$router.push({
              //   path: that.$route.query.currentUrl,
              //   query: {
              //     t: new Date().getTime(),
              //     jump: true
              //   }
              // });
            } else {
              that.$message({
                type: 'info',
                message: response.msg
              });
              this.addModifyFlag();
              // that.$store.dispatch('tagsView/delView', that.$route);
              // that.$router.push({
              //   // path: '/' + that.pubUrl,
              //   path: that.$route.query.currentUrl,
              //   query: {
              //     t: new Date().getTime(),
              //     jump: true
              //   }
              // });
            }
          })
          .error(() => {
            loading.close();
          });
      }
    },
    /** 作废工单 */
    updateWorkOrderToVoid() {
      const that = this;
      that.$XyyMsg({
        title: '提示',
        content: '确定作废此工单？',
        onSuccess: function() {
          that.toVoid();
        }
      });
    },
    toVoid() {
      const that = this;
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });

      // 提交回复
      const param = {
        workorderId: that.workorderId
      };
      return updateWorkOrderToVoid(param).then(response => {
        that.$nextTick(() => {
          loading.close();
        });
        if (response.msg === 'success') {
          that.$message({
            type: 'success',
            message: '已作废!'
          });
          this.addModifyFlag();
          // that.$store.dispatch('tagsView/delView', that.$route);
          // that.$router.push({
          //   path: that.$route.query.currentUrl,
          //   query: {
          //     t: new Date().getTime(),
          //     jump: true
          //   }
          // });
        } else {
          that.$message({
            type: 'info',
            message: response.msg
          });
          this.addModifyFlag();
          // that.$store.dispatch('tagsView/delView', that.$route);
          // that.$router.push({
          //   path: that.$route.query.currentUrl,
          //   query: {
          //     t: new Date().getTime(),
          //     jump: true
          //   }
          // });
        }
      });
    },
    downFile(name, filePath) {
      const preParam = { fieldName: name, fieldPath: filePath };

      function httpPost(URL, PARAMS) {
        var temp = document.createElement('form');
        temp.action = URL;
        temp.method = 'get';
        temp.style.display = 'none';

        for (var x in PARAMS) {
          var opt = document.createElement('textarea');
          opt.name = x;
          opt.value = PARAMS[x];
          temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
      }

      let domain = document.domain;
      if (domain === 'localhost') {
        domain = 'ec-service.dev.ybm100.com';
      }
      httpPost('http://' + domain + '/fileUpload/fileDownload', preParam);
    },
    // 查询工单标签
    getOrderTag(id) {
      getOrderDetailTag(id).then(res => {
        this.orderTag = { ...res.data };
      });
    },
    // 获取标签字典
    getTags() {
      getTagList().then(res => {
        this.tags = res.data;
      });
    },
    // 切换标签
    switchTag(tag) {
      putSwitchTag(tag.id, this.workorderId).then(res => {
        if (res.code === 1) {
          this.orderTag = {
            tagId: tag.id,
            tagName: tag.tagName
          };
        }
      });
    },
    // 清除标签
    clearTag() {
      deleteTag(this.workorderId).then(res => {
        if (res.code === 1) {
          this.orderTag = {
            tagId: '',
            tagName: ''
          };
        }
      });
    },
    /**
     * 页面初始加载
     */
    mountedHandle() {
      Object.assign(this.$data, this.$options.data()); // 重置data数据
      const { id, type } = this.$route.query;
      this.pageType = type;
      this.workorderId = id;
      const orderParam = { workorderId: id };
      this.getWorkorderInfoAndReplyInfo(orderParam); // 获取字段信息
      this.listReplyFieldByWorkorderId(orderParam); // sheetPre
      this.$refs['processs'].getRecordDatas(); // 获取流转记录-催单记录-追加记录
    },
    /**
     * 更新数据后store增加上一级列表url，以便打开列表更新数据
     */
    addModifyFlag(refs) {
      if (this.$route.query.currentUrl) {
        this.$store.commit(
          'workSheet/SET_MODIFY_FLAG',
          this.$route.query.currentUrl
        );
      }
      if (!refs) this.mountedHandle();
    }
  }
};
</script>
<style scoped lang="scss">
.dialogFooterOuter {
  position: absolute;
  bottom: 0;
  right: 0;
  background: #fff;
  width: 100%;
  border-top: 1px solid #e4e4eb;
  padding-right: 10px;
  padding-bottom: 10px;
}

.previewNewInner {
  /deep/ .previewNew {
    /deep/ .el-upload-list--picture-card {
      width: auto;
    }
  }
}

.custome-form_list {
  /deep/ .el-form-item__content {
    width: calc(100% - 98px) !important;
  }
}

.previewNew {
  position: relative;

  /deep/ .el-upload--picture-card {
    width: 102px;
    height: 36px;
    display: inline-block;
    line-height: 32px;
    border: 0 none;
    position: absolute;
    top: 0;
    left: 0;

    .el-button {
      width: 102px;
      height: 36px;
      background: rgba(255, 255, 255, 1);
      border-radius: 2px;
      border: 1px solid rgba(228, 228, 235, 1);
      color: rgba(87, 87, 102, 1);
    }
  }

  /deep/ .el-upload-list--picture-card {
    display: inline-block;
    width: 271px;
    margin-top: 40px;
  }

  /deep/ .el-upload-list__item {
    width: 126px;
    height: 30px;
    margin-bottom: 15px;
    display: inline-block;
    background: rgba(245, 247, 250, 1);
    border-radius: 2px;
    border: 1px solid rgba(228, 228, 235, 1);
    overflow: visible;
  }
}

.systemField-form,
.custome-form {
  border-radius: 2px;
  border: 1px solid rgba(228, 228, 235, 1);

  header {
    height: 40px;
    background: rgba(240, 242, 245, 1);
    padding-left: 16px;
    font-weight: 500;
    color: rgba(41, 41, 51, 1);
    line-height: 40px;
  }
}

/deep/ .systemField-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /*height:600px;*/
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);

  .el-dialog__body {
    overflow-y: auto;
    padding-top: 10px !important;
    padding-bottom: 60px;

    /deep/ .custom-content {
      overflow-x: auto;
    }
  }

  .el-col-9 {
    width: 35.5%;
  }
}

.systemField-form:nth-child(1) {
  margin-right: 15px;
}

.custome-form {
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;

  .custom-content {
    padding: 20px 20px 0;
    box-sizing: border-box;
  }

  .preview {
    /deep/ .el-form-item__label {
      width: 97px;
    }
  }

  .custome-form-item {
    width: 50%;
    float: left;
  }
}

.instructions {
  /deep/ .el-dialog {
    height: 566px;
  }

  /deep/ .el-dialog__body {
    padding: 10px 20px;
    height: 450px;
    overflow-y: auto;
  }
}

/*/deep/.el-upload-list__item {*/
/*width: 80px;*/
/*height: 122px;*/
/*border: 0 none;*/
/*margin-right: 10px;*/
/*-webkit-border-radius: 0 !important;*/
/*-moz-border-radius: 0 !important;*/
/*border-radius: 0 !important;*/
/*}*/
/deep/ .el-upload--picture-card {
  width: 80px;
  height: 80px;
  line-height: 86px;
}
</style>
<style lang="scss">
.el-dialog.addSheetCheckStyle {
  width: 400px;
  height: 200px;

  .el-dialog__header {
    border: 1px solid rgba(238, 238, 238, 1);
  }

  .el-dialog__body {
    padding: 20px 20px 30px 20px;
    font-size: 14px;
    color: #292933;

    .worderId {
      color: #3b95a8;
    }
  }
}

.shangchuanfujian {
  padding-top: 20px;
  padding-bottom: 20px;
  font-size: 16px;
  display: inline-block;
  font-weight: 700;
}

.urgeCon .el-textarea__inner {
  height: 211px;
}

.urgeStyle {
  .el-dialog__body {
    padding: 0 20px;
  }

  .el-dialog__title {
    color: #292933;
  }

  .el-dialog__footer {
    padding-bottom: 20px;
  }
}

/deep/ .el-step.is-vertical .el-step__line {
  top: 5px;
}

/* 标签按钮 */
.tag-btn {
  height: auto !important;
  margin: 2px 0;
}

.el-popover {
  text-align: unset;
}
</style>
<style lang="scss" scoped>
.upload-file-box.completed {
  /deep/ .el-upload--picture-card {
    display: none;
  }
}

/deep/ .el-button {
  padding: 10px 20px;
}

/deep/ .el-dialog__body {
  font-weight: normal;
}

.assignmentSheet {
  /deep/ .el-dialog {
    top: 70%;
    transform: translateY(-50%);
  }

  .queryAss {
    background-color: rgba(59, 149, 168, 1);
    color: #fff;
    border-radius: 2px;
  }

  /deep/ .el-button.el-button--primary.is-plain {
    background-color: rgba(59, 149, 168, 1);
    color: #fff;
  }

  .userGroup {
    margin-bottom: 20px;
  }

  .label {
    display: inline-block;
    width: 56px;
    text-align: right;
    color: #292933;
    margin-right: 8px;
  }

  .sheetNumber {
    font-size: 14px;
    color: rgba(144, 147, 153, 1);
    padding: 0 0 20px 20px;
  }

  /deep/ .el-dialog__header {
    padding: 0 20px;
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    font-weight: 600;
    color: rgba(48, 49, 51, 1);
    border-bottom: 1px solid rgba(238, 238, 238, 1);
  }

  /deep/ .el-dialog__body {
    padding: 14px 0 0 0px;
  }

  /deep/ .el-input {
    width: 290px;
  }

  /deep/ .el-button {
    height: 36px;
  }

  .assSubmit {
    padding: 0 20px 20px 0;
  }
}

.urgeStyle {
  font-size: 14px;

  .urgeTitle {
    color: #575766;
    font-size: 14px;
    padding-bottom: 12px;
  }

  .urgeCon .el-textarea__inner {
    height: 211px;
  }

  .urgeTip {
    padding-top: 12px;
    color: #909399;
  }
}

.marg-top20 {
  margin-top: 20px;
}

.el-divider--horizontal {
  margin: 0;
}

.el-divider--vertical {
  height: 718px;
}

.editFrom {
  z-index: 9;
  position: absolute;
  top: -10px;
  right: 0;
  width: 90px;
  height: 40px;
  margin-top: 11px;
}

/deep/ .el-divider--vertical {
  min-height: 765px;
}

.sheeTitle {
  position: relative;
  padding: 16px 20px;
  color: #303133;
  font-size: 18px;
  font-weight: 600;

  i {
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(251, 114, 12, 1);
    font-style: normal;
    margin-left: 20px;
  }
}

.page {
  height: 100%;
  overflow: auto;
}

.detailInstr {
  position: relative;
  padding: 30px 10px 20px 20px;
  font-size: 14px;
  font-weight: 400;
  width: 55%;
  box-sizing: border-box;

  /deep/ .lsCon {
    /deep/ .el-row {
      margin-top: 8px;
    }
  }

  /deep/ .preview-box {
    border: 1px solid #e4e4eb;
    overflow: hidden;
    border-radius: 4px;
  }
}

.detailArea {
  width: 667px;
  margin: 12px 0 20px;
}

.detailShow {
  width: 45%;
  padding: 10px 20px 20px;
  box-sizing: border-box;
}

.attr {
  border-bottom: 1px dotted #e5e5ec;

  .attrCon {
    min-height: 120px;
    text-align: center;
    line-height: 100px;
  }

  .noData {
    font-size: 14px;
    color: #aeaebf;
  }

  .title {
    color: #303133;
    font-size: 14px;
    margin-bottom: 16px;
    font-weight: 600;
  }

  .con {
    margin-bottom: 16px;
    text-align: left;
    line-height: 14px;
  }
}

.record {
  position: relative;
  padding-top: 19px;
  padding-bottom: 14px;
  border-bottom: 1px dotted #e5e5ec;

  .getMore {
    position: absolute;
    right: 0px;
    top: 10px;
    z-index: 99;
  }
}

.selTag {
  font-size: 14px;
  color: #303133;
  font-weight: 600;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: -12px 20px 0;
}

.noRecord {
  height: 70px;
  line-height: 50px;
  text-align: center;
  font-size: 14px;
  color: #aeaebf;
}

.el-menu.el-menu--horizontal {
  border-bottom: none;
}

.el-menu--horizontal > .el-menu-item.is-active {
  border-bottom: 1px solid rgba(59, 149, 168, 1);
}

.selTag:nth-child(1) {
  margin-left: 5px;
}

.selTag:hover {
  background: #fff !important;
}

.el-timeline {
  margin-left: -35px;
  margin-top: 20px;
}

/deep/ .el-timeline-item__node--normal {
  width: 9px;
  height: 9px;
}

.activeRoam {
  /deep/ .el-timeline-item__tail {
    border-left: 1px solid #3b95a8;
    left: 3px;
  }

  /deep/ .el-timeline-item__node {
    background: #3b95a8;
  }
}

/deep/ .roamDetail {
  .el-dialog__body {
    height: 466px;
    padding: 0 20px 10px;
    overflow-y: auto;
  }
}

.reCon {
  padding: 20px 0 0 7px;
}

.append {
  padding-top: 10px;

  .title {
    font-size: 14px;
    color: #303133;
    font-weight: 600;
  }
}

.appendCon {
  padding-top: 20px;
  font-size: 14px;

  .conName {
    color: #292933;
  }

  .conTime {
    font-size: 12px;
    color: #aeaebf;
  }
}

.noAppendCon {
  padding-top: 20px;
  height: 70px;
  text-align: center;
  font-size: 14px;
  color: #aeaebf;
}

.appendTip {
  padding-top: 8px;
  color: #909399;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.elDivider {
  width: 1px;
  height: 12px;
  margin: 0 10px;
  border-left: 1px solid rgba(220, 223, 230, 1);
}

.returnSheetRadio {
  width: 100%;
  padding: 0 70px 20px;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .el-radio {
    margin-right: 0;
  }
}

.returnSheet {
  /deep/ .el-textarea__inner {
    height: 211px;
  }
}

.issueAppend {
  /deep/ .el-textarea__inner {
    height: 300px;
  }
}

.returnSheetTip {
  color: #909399;
  font-size: 14px;
  margin: 20px 0px;
}

.reminDetail {
  /deep/ .el-dialog__body {
    padding: 0;
    height: 466px;
    overflow-y: auto;
  }

  .reminderCon {
    padding-left: 20px;
    padding-top: 20px;
    font-size: 14px;
    color: #292933;

    .reminderTime {
      color: #aeaebf;
      font-size: 12px;
    }

    .appendTip {
      padding-top: 8px;
      color: #909399;
    }
  }
}

// .opinionHandle {
//   margin-top: 65px;
//   /deep/.el-form-item__label {
//     margin-top: -25px;
//     line-height: 20px;
//     margin-left: -17px;
//     text-align: left;
//   }
//   /deep/.el-form-item__content {
//     margin-left: -20px !important;
//   }
// }
/deep/ .el-timeline-item__timestamp.is-top {
  color: #292933;
  // margin: 0;
  // padding: 0;
}

.timelineTitle {
  margin-top: -10px;
  color: #292933;
}

.timelineTitle span {
  padding: 3px 8px;
  background: rgba(253, 246, 236, 1);
  border-radius: 2px;
  font-size: 12px;
  color: rgba(230, 162, 60, 1);
}

.cirSubTime {
  font-size: 12px;
  color: #aeaebf;
  line-height: 8px;
}

.cirSubCon {
  font-size: 14px;
  color: #909399;
  line-height: 16px;
  margin-top: 1px;
}

.ellipsis {
  width: 270px;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  word-break: normal;
}

.replyPlate {
  width: 100%;
  background: rgba(245, 247, 250, 1);
  padding: 20px 0px;

  .title {
    font-size: 14px;
    font-weight: 600;
    color: rgba(48, 49, 51, 1);
    line-height: 20px;
    margin-left: 20px;
  }

  .submit {
    padding-left: 85px;
    box-sizing: border-box;
  }
}

.editInput {
  margin: 0 auto;
  margin-top: 16px;
}

.editTitle {
  font-size: 16px;
  font-weight: 600;
  color: rgba(48, 49, 51, 1);
  line-height: 22px;
  text-align: left;
}

.editBox {
  .tips {
    position: absolute;
    left: 98px;
    top: 27px;
    color: #e6a23c;
    font-size: 12px;
  }

  /deep/ .el-dialog__header {
    border-bottom: 1px solid rgba(228, 228, 235, 1);
  }

  .dialog-footer {
    padding-top: 10px;
    text-align: right;
    box-sizing: border-box;
  }
}

.sheetReset {
  // .tips {
  //   position: absolute;
  //   left: 98px;
  //   top: 27px;
  //   color: #e6a23c;
  //   font-size: 12px;
  // }
  /deep/ .el-dialog {
    top: 70%;
    transform: translateY(-50%);

    /deep/ .el-dialog__header {
      padding: 0 20px;
      height: 50px;
      line-height: 50px;
      border-bottom: 1px solid #e4e7ed;

      .el-dialog__headerbtn {
        top: 16px;
      }
    }

    /deep/ .el-dialog__footer {
      padding: 18px 20px 20px;
    }

    /deep/ .el-dialog__body {
      height: 375px;
      overflow: auto;
      padding: 18px 15px 0;

      .el-checkbox {
        margin-bottom: 15px;
      }

      .assign-inner-content {
        border: 1px solid #e4e4eb;

        .el-radio-group {
          padding: 10px;
        }

        .el-form.assign-form {
          background: #f5f7fa;
          width: 100%;
          overflow: hidden;
          border-top: 1px solid #e4e4eb;
          padding: 20px 15px 0 0;

          .el-form-item {
            width: 50%;
            margin-right: 0;
            float: left;
            margin-bottom: 20px;

            &.whole-width {
              margin-right: 1px;
            }

            .el-form-item__label {
              font-weight: normal;
            }

            .el-form-item__content {
              width: calc(100% - 80px);

              .el-select {
                width: 100%;

                .el-input__inner {
                  height: 36px;
                  line-height: 36px;
                }
              }
            }
          }
        }
      }

      .el-form.restart-form {
        .el-form-item {
          margin-bottom: 20px;

          /deep/ .el-form-item__label {
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #292933;
            height: 20px;
            line-height: 20px;
            margin: 20px 0 15px;
            display: block;
          }

          /deep/ .el-textarea__inner {
            height: 90px;
          }
        }
      }
    }
  }
}

/deep/ .template-col {
  min-height: 1px;

  /deep/ .el-row {
    width: 271px;
  }

  /deep/ .preview {
    .single {
      margin: 0 0 20px;
    }
  }
}

.reply-form-content {
  padding: 20px 20px 0;
}

.order-tag {
  font-weight: normal;
  font-size: 16px;

  .el-button {
    padding: 7px;
  }
}
</style>

