<template>
  <xyy-list-page class="knowledge-collection">
    <template slot="header">
      <div>
        <el-form ref="queryList" :model="queryList" class="knowledge-collection-box">
          <el-row type="flex" class="innerEl" justify="space-between" align="middle">
            <el-form-item class="serverNum" prop="serveNum">
              <el-input
                v-model="queryList.title"
                class="serverName"
                size="small"
                clearable
                placeholder="请输入关键字"
              />
              <el-button
                plain
                type="primary"
                size="medium"
                class="searchCondition"
                @click="checkTimer(handerSearch('queryList'))"
              >查询</el-button>
            </el-form-item>

            <el-form-item class="serverNum" prop="sort">
              <el-select
                v-model="queryList.sort"
                placeholder="请选择"
                clearable
                @change="changeSortList"
              >
                <!-- <el-option label="更新时间" value="createTime" key="createTime"></el-option> -->
                <el-option
                  v-for="item in sortList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
    </template>

    <template slot="body">
      <div>
        <el-row type="flex" justify="space-between" align="top">
          <xyy-table
            style="width: 100%"
            :data="list"
            :list-query="listQuery"
            :col="col"
            :offset-top="240"
            @get-data="getList"
            @row-dblclick="handleRowdblClick"
            class="customer-table"
          >
            <!-- 知识标题 -->
            <template slot="title" slot-scope="{ col }">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :label="col.name"
                :width="col.width"
              >
                <template slot-scope="item">
                  <span style="margin-right:4px;">{{ item.row["title"]}}</span>
                  <el-tag
                    size="mini"
                    class="tag-bg"
                    v-for="tag in item.row['tagList']"
                    :key="tag"
                    :hit="false"
                  >{{tag}}</el-tag>
                </template>
              </el-table-column>
            </template>
          </xyy-table>
          <!-- <el-table
          :data="list"
          :show-header="false"
          :list-query="listQuery"
          :border="false"
          :row-class-name="'im_container_leftview_table_row'"
          highlight-current-row
          @get-data="getList"
          ></el-table>-->
        </el-row>
      </div>
    </template>
  </xyy-list-page>
</template>

<script>
import { favoriteSelectList } from '@/api/knowledge_base';
import utils from '@/utils/filter';
export default {
  name: 'KnowledgeCollection',
  data() {
    return {
      queryList: {
        sort: 'createTime',
        title: ''
      },
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      list: [],
      sortList: [
        { id: 'createTime', name: '发布时间' },
        { id: 'modifyTime', name: '更新时间' },
        { id: 'hot', name: '热度排序' }
      ],
      created: false,
      col: [
        { index: 'title', name: '知识标题', slot: true },
        { index: 'viewCount', name: '阅读量', width: 120 },
        { index: 'modifyTime', name: '更新时间', width: 200 }
      ],
      sortRule: 1,
      manual: false
    };
  },
  computed: {},
  watch: {},
  mounted() {},
  created() {
    this.created = true;
  },
  activated() {
    this.manual = false;
    if (!this.created) this.getList(this.listQuery);
    this.created = false;
  },
  methods: {
    handerSearch(formName) {
      (this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      }),
        (this.manual = true);
      this.getList(this.listQuery);
    },
    getList: function(listQuery) {
      const { page, pageSize } = listQuery;
      let params = {
        pageNum: page,
        pageSize,
        title: this.queryList.title,
        sortFiled: this.queryList.sort,
        sortRule: this.sortRule,
        manual: this.manual
      };
      favoriteSelectList(params)
        .then(res => {
          if (res.code === 1) {
            const { total } = res.data;
            this.list = res.data.list;

            let modifyTime = {};
            let tagList = [];
            this.list.forEach((item, index) => {
              modifyTime = {};
              tagList = [];

              if (item.modifyTime == null) {
                modifyTime = '-';
              } else {
                modifyTime = utils.dataTime(
                  item.modifyTime,
                  'yy-mm-dd HH:ss:nn'
                );
              }

              if (item.synonyms) {
                tagList = item.synonyms.split(',');
              }

              this.list[index] = Object.assign({}, this.list[index], {
                modifyTime: modifyTime,
                tagList: tagList
              });
            });

            this.listQuery = {
              ...this.listQuery,
              page: Number(res.data.pageNum),
              pageSize: Number(res.data.pageSize),
              total: Number(res.data.total)
            };
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },
    changeSortList(data) {
      this.queryList.sort = data;
      (this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      }),
        (this.manual = true);
      this.getList(this.listQuery);
    },
    /**
     * 跳转到详情
     */
    handleRowdblClick(row, column, cell, event) {
      this.$router.push({
        path: `/knowledge_base/knowledgeDetails/${row.id}`,
        query: { templateId: row.id }
      });
    }
  }
};
</script>

<style lang="scss">
.knowledge-collection {
  /deep/.page-body {
    width: 100%;
  }
  .tag-bg {
    text-align: center;
    font-size: 10px;
    background: #f5f7fa;
    border: 1px solid #e4e4eb;
    color: #575766;
    border-radius: 3px;
    margin-right: 4px;
  }
  /deep/.table-containter {
    width: 100%;
    min-height: 400px;
  }
  /deep/.el-table__empty-block {
    width: 100 !important;
  }
  // 去掉表格单元格边框
  .customer-table th {
    border: none;
    // text-align: right;
  }
  .customer-table td,
  .customer-table th.is-leaf {
    border: none;
    // text-align: right;
  }
  // 表格最外边框
  // .el-table--border,
  // .el-table--group {
  //   border: none;
  // }
  // 头部边框
  .customer-table thead tr th.is-leaf {
    border: 1px solid #ebeef5;
    border-right: none;
  }
  .customer-table thead tr th:nth-last-of-type(2) {
    border-right: 1px solid #ebeef5;
  }
  // 表格最外层边框-底部边框
  .el-table--border::after,
  .el-table--group::after {
    width: 0;
  }
  .customer-table::before {
    width: 0;
  }
  .customer-table .el-table__fixed-right::before,
  .el-table__fixed::before {
    width: 0;
  }
  // 表格有滚动时表格头边框
  .el-table--border th.gutter:last-of-type {
    border: 1px solid #ebeef5;
    border-left: none;
  }
  .im_container_leftview_table_row {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100px;
  }

  .im_container_leftview_table_row_1 {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 60px;
  }
  .knowledge-collection-box {
    padding-bottom: 20px;
    margin-bottom: 10px;
    border-bottom: 1px dotted #e4e4eb;
    .innerEl {
      height: 38px;
      /deep/.el-input__inner {
        width: 100%;
        height: 36px;
        line-height: 36px;
      }
      /deep/.el-form-item {
        margin: 0;
      }
      .timeSel {
        width: 300px;
      }
      /deep/input[type='number']::-webkit-inner-spin-button,
      /deep/input[type='number']::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      .btnNew {
        color: #fff;
        background-color: rgba(59, 149, 168, 1);
      }
      .serverName {
        width: 200px;
      }
      .serverNum {
        margin-right: 20px;
      }
      .searchCondition.is-plain {
        background: rgba(59, 149, 168, 1);
        color: #fff;
      }
      &.top_20 {
        margin-top: 20px;
      }
    }
  }
}
</style>