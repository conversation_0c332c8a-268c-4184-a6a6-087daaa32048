<template >
  <div class="knowledge-management">
    <div>
      <el-form ref="queryList" :model="queryList" class="knowledge-management-box">
        <el-row class="innerEl" type="flex" justify="start" align="middle">
          <el-form-item class="serverNum" label="知识标题" label-width="80px" prop="serveNum">
            <el-input
              v-model="queryList.title"
              class="serverName"
              size="small"
              placeholder="请输入标题"
            />
          </el-form-item>

          <el-form-item class="serverNum" label="创建时间" label-width="80px" prop="dateRange01">
            <el-date-picker
              :picker-options="pickerOptions"
              v-model="queryList.dateRange01"
              type="daterange"
              range-separator="-"
              size="small"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              text="erdf"
              prefix-icon="el-icon-date"
              class="timeSel"
            />
          </el-form-item>

          <el-form-item class="serverNum" label="更新时间" label-width="80px" prop="dateRange02">
            <el-date-picker
              :picker-options="pickerOptions"
              v-model="queryList.dateRange02"
              type="daterange"
              range-separator="-"
              size="small"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              text="erdf"
              prefix-icon="el-icon-date"
              class="timeSel"
            />
          </el-form-item>
        </el-row>

        <el-row type="flex" class="innerEl top_20" justify="space-between" align="middle">
          <el-form-item class="serverNum" label="当前状态" label-width="80px" prop="currentState">
            <el-select v-model="queryList.currentState" placeholder="全部" clearable>
              <el-option label="全部" value></el-option>
              <el-option
                v-for="item in currentStateList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-row>

        <el-row type="flex" class="innerEl top_20" justify="space-between" align="middle">
          <el-form-item>
            <!-- <el-button class="btnNew" size="medium" @click="toEdit">
              <span class="el-icon-plus"></span>新建
            </el-button>-->
            <xyy-button class="btnNew" icon-class="btn-add" size="medium" @click="toEdit">新建</xyy-button>
            <xyy-button size="medium" @click="batchMobile">批量移动</xyy-button>
          </el-form-item>
          <el-form-item>
            <el-button plain size="medium" @click="toDraftBox">草稿箱</el-button>
            <el-button
              plain
              type="primary"
              size="medium"
              class="searchCondition"
              @click="checkTimer(handerSearch('queryList'))"
            >查询</el-button>
            <el-button plain size="medium" @click="resetForm">重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </div>

    <el-row type="flex" justify="space-between" align="top">
      <div class="knowledge-classification">
        <el-row class="header" type="flex" justify="state" align="middle">
          <!--  -->
          <span class="el-icon-plus-text" @click="newIncrease">
            知识分类
            <i class="el-icon-plus"></i>
          </span>
        </el-row>
        <div class="content">
          <drag-tree
            ref="tree"
            :props="{label:(data)=>data.name||data.children,children:'children'}"
            :list="datas"
            :expandedKeys="expandedKeysData"
            @delChildCallback="toDelQuestion"
            @clickCallback="getData"
            @addChildCallback="toAddQuestion"
            @delCallback="toDelClassify"
            @editCallback="toEditClassify"
          ></drag-tree>
          <!-- @dropCallback="sortData" -->
        </div>
      </div>

      <xyy-table
        ref="orderTable"
        :data="list"
        :list-query="listQuery"
        :col="col"
        :operation="operation"
        :has-selection="true"
        @get-data="getList"
        @operation-click="operationClick"
        @row-dblclick="handleRowdblClick"
        @selectionCallback="setCheckedDatas"
      >
        <template slot="currentState" slot-scope="{col}">
          <el-table-column :key="col.index" :label="col.name" :width="col.width">
            <template slot-scope="{row}">
              <span :class="'status-tip '+(row.currentState === 0?'open':'close')"></span>
              {{ row.currentState === 0?'启用':'禁用' }}
            </template>
          </el-table-column>
        </template>
      </xyy-table>
    </el-row>
    <!-- 添加标签 -->
    <el-dialog
      :visible.sync="questionEditable"
      :title="formTitle.title"
      class="dialogInner"
      width="400px"
    >
      <el-form :inline="true" :model="formTitle" label-position="left" ref="formTitle">
        <el-input v-model="formTitle.search" maxlength="20"></el-input>
      </el-form>

      <span class="dialog-footer" slot="footer">
        <el-button @click="dialogCancel">取 消</el-button>
        <el-button type="primary" @click="dialogDetermine">保 存</el-button>
      </span>
    </el-dialog>
    <!-- 批量移动 -->
    <el-dialog :visible.sync="isBatchMobile" title="移动到" class="dialogInner" width="400px">
      <el-form :inline="true" :model="formTitle" label-position="left" ref="formTitle">
        <el-tree
          :data="datas"
          :props="{label:(data)=>data.name||data.children,children:'children'}"
          accordion
          @node-click="getBatchMobileData"
        ></el-tree>
      </el-form>

      <span class="dialog-footer" slot="footer">
        <el-button @click="dialogCancelBatchMobile">取 消</el-button>
        <el-button type="primary" @click="dialogDetermineBatchMobile">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import dragTree from '@/components/im/drag-tree2';
import {
  findKnowledgeManagementList,
  listAllClassification,
  saveOrUpdateKnowledgeClassification,
  delKnowledgeClassification,
  updateKnowledgeManagementEnableOrDisable,
  deleteKnowledgeManagement,
  copyKnowledgeManagementAddress,
  batchMoveKnowledgeManagement
} from '@/api/knowledge_base';
import utils from '@/utils/filter';
import { copyValue } from '@/utils/tools';
export default {
  name: 'knowledgeManagement',
  components: {
    dragTree
  },

  data() {
    return {
      formTitle: {
        search: '', // title
        title: '新增分类',
        addOrEdit: true
      },
      queryList: {
        dateRange01: [],
        dateRange02: [],
        currentState: '',
        title: '',
        id: ''
      },
      currentStateList: [
        { id: 0, name: '启用' },
        { id: 1, name: '禁用' }
      ],
      list: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      datas: [], //树数据
      expandedKeysData: [], //默认展开节点
      question: {
        name: '',
        parentId: '',
        level: ''
      },
      classify: {
        name: '',
        id: ''
      },
      col: [
        { index: 'title', name: '知识标题', width: 168 },
        { index: 'classificationName', name: '分类', width: 220 },
        { index: 'viewCount', name: '浏览量', width: 120 },
        { index: 'collectionCount', name: '收藏量', width: 120 },
        { index: 'createTime', name: '创建时间', width: 220 },
        { index: 'creatorName', name: '创建人', width: 120 },
        { index: 'modifyTime', name: '更新时间', width: 220 },
        { index: 'modifyName', name: '更新人', width: 120 },
        {
          index: 'currentState',
          name: '当前状态',
          width: 120,
          slot: true,
          resizable: true
        },
        {
          index: 'operation',
          name: '操作',
          width: 340,
          operation: true,
          resizable: true
        }
      ],
      operation: [
        {
          name: '复制地址',
          type: 0
        },
        {
          name: '编辑',
          type: 1
          // disabled: function(row) {
          //   return !row.currentState;
          // }
        },
        {
          name: '启用',
          type: 2,
          format: function(row) {
            return ['禁用', '启用'][row.currentState];
          }
        },
        {
          name: '知识历史',
          type: 3
        },
        {
          name: '删除',
          type: 4,
          disabled: function(row) {
            return !row.currentState;
          },
          isDelete: 1
        }
      ],
      searchstartTime: '开始时间',
      searchendTime: '结束时间',
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.minDate = minDate;
          this.maxDate = maxDate;
        },
        disabledDate: time => {
          const curDate = new Date().getTime();
          const two = 365 * 2 * 24 * 3600 * 1000;
          const twoyear = curDate - two;
          const three = 184 * 24 * 3600 * 1000;
          if (this.minDate) {
            return (
              time.getTime() > Date.now() ||
              time.getTime() < twoyear ||
              time > new Date(this.minDate.getTime() + three) ||
              time < new Date(this.minDate.getTime() - three)
            );
          }
          return time.getTime() > Date.now() || time.getTime() < twoyear;
        }
      },
      dateRange01: '',
      dateRange02: '',
      created: false,
      questionEditable: false, // 新建弹框
      selections: [], //选中数据集合
      isBatchMobile: false, // 批量移动判断显示
      classificationId: '' //
    };
  },
  created() {
    this.getNowTimeDate();
    this.created = true;
  },
  activated() {
    if (!this.created) this.getList(this.listQuery);
    this.created = false;
  },
  mounted() {
    this.initTree();
  },

  methods: {
    /**
     * 设置选中数据
     */
    setCheckedDatas(datas) {
      this.selections = datas.map(el => el.id);
    },
    handerSearch(formName) {
      (this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      }),
        this.getList(this.listQuery);
    },
    getList: function(listQuery) {
      const { page, pageSize } = listQuery;
      let params = {
        pageNum: page,
        pageSize,
        // endTime: this.queryList.dateRange01[1],
        // startTime: this.queryList.dateRange01[0],
        // updateEndTime: this.queryList.dateRange02[1],
        // updateStartTime: this.queryList.dateRange02[0],
        title: this.queryList.title,
        currentState: this.queryList.currentState,
        // knowledgeClassificationId: JSON.parse(
        //   '[' + this.question.parentId + ']'
        // ),
        knowledgeClassificationId: this.question.parentId,
        level: this.question.level
      };

      if (this.queryList.dateRange01) {
        params.endTime = this.queryList.dateRange01[1];
        params.startTime = this.queryList.dateRange01[0];
      }
      if (this.queryList.dateRange02) {
        params.updateEndTime = this.queryList.dateRange02[1];
        params.updateStartTime = this.queryList.dateRange02[0];
      }

      findKnowledgeManagementList(params)
        .then(res => {
          if (res.code === 1) {
            const { total } = res.data;
            this.list = res.data.records;
            let createTime = {};
            let modifyTime = {};
            this.list.forEach((item, index) => {
              createTime = {};
              modifyTime = {};
              if (item.createTime == null) {
                createTime = '-';
              } else {
                createTime = utils.dataTime(
                  item.createTime,
                  'yy-mm-dd HH:ss:nn'
                );
              }
              if (item.modifyTime == null) {
                modifyTime = '-';
              } else {
                modifyTime = utils.dataTime(
                  item.modifyTime,
                  'yy-mm-dd HH:ss:nn'
                );
              }
              this.list[index] = Object.assign({}, this.list[index], {
                createTime: createTime,
                modifyTime: modifyTime
              });
            });

            this.listQuery = {
              ...this.listQuery,
              page: Number(res.data.current),
              pageSize: Number(res.data.size),
              total: Number(res.data.total)
            };
            // this.listQuery = {
            //   ...this.listQuery,
            //   total: Number(total)
            // };
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },

    // 重置
    resetForm() {
      (this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      }),
        (this.queryList = {
          dateRange01: [],
          dateRange02: [],
          currentState: '',
          title: '',
          id: ''
        }),
        (this.question = {
          name: '',
          parentId: '',
          level: ''
        });
      this.getNowTimeDate();
      this.initTree();
      this.getList(this.listQuery);
    },
    operationClick: function(type, row) {
      switch (type) {
        case 0:
          this.toCopy(row); // 复制
          break;
        case 1:
          this.toEdit(row);
          break;
        case 2:
          this.toCurrentState(row);
          break;
        case 3:
          this.toKnowledgeHistory(row);
          break;
        case 4:
          this.$XyyMsg({
            title: '提示',
            content: '确定要删除吗?',
            onSuccess: () => {
              this.delData(row);
            }
          });
          break;
      }
    },
    // 复制
    toCopy(row) {
      let path = `${process.env.BASE_API}/pc/#/knowledge_base/knowledgeDetails/${row.id}?templateId=${row.id}`;
      copyValue(path);
      this.$XyyMessage.success('复制成功');
      // &current_version=${row.currentVersion}
      // copyKnowledgeManagementAddress({ id: row.id })
      //   .then(res => {
      //     if (res.code === 1) {

      //     } else {
      //       this.$XyyMessage.error(res.msg);
      //     }
      //   })
      //   .catch(function(error) {});
    },
    toCurrentState(row) {
      let currentState = row.currentState === 0 ? 1 : 0;
      if (currentState === 0 && String(row.name).length > 20) {
        this.$XyyMessage.error('名称过长,请先修改名称');
        return;
      }
      const msgs = ['启用', '禁用'];
      this.$XyyMsg({
        title: '提示',
        content: '是否' + msgs[currentState] + '?',
        onSuccess: () => {
          updateKnowledgeManagementEnableOrDisable({
            id: row.id,
            currentState: currentState
          }).then(res => {
            if (res.code === 1) {
              this.$XyyMessage.success(msgs[currentState] + '成功');
              this.getList(this.listQuery);
            } else {
              this.$XyyMessage.error(res.msg);
            }
          });
        }
      });
    },
    /**
     * 跳转到知识历史
     */
    toKnowledgeHistory(row) {
      this.$router.push({
        path: `/knowledge_base/knowledgeHistory/${row.id}`,
        query: { templateId: row.id }
      });
      // , current_version: row.currentVersion
      // this.$router.replace({
      //   path: `/knowledge_base/knowledgeHistory`
      // });
    },
    /**
     * 跳转到详情
     */
    handleRowdblClick(row, column, cell, event) {
      this.$router.push({
        path: `/knowledge_base/knowledgeDetails/${row.id}`,
        query: { templateId: row.id }
        // , current_version: row.currentVersion
      });
    },
    /**
     * 跳转到草稿箱
     */
    toDraftBox() {
      this.$router.replace({
        path: `/knowledge_base/draftBoxList`
      });
    },

    /**
     * 跳转到模板编辑页
     */
    toEdit(row) {
      if (row) {
        this.$router.replace({
          path: `/knowledge_base/knowledgeManagementEdit/${row.id}`,
          query: {
            templateId: row.id,
            isDraftBox: false
          }
          // current_version: row.currentVersion,
        });
      } else {
        this.$router.replace({
          path: `/knowledge_base/knowledgeManagementEdit/${new Date().getTime()}`
        });
      }
    },
    // 删除知识
    delData(row) {
      this.$XyyMsg({
        title: '提示',
        content: '删除知识后不可恢复，是否继续?',
        onSuccess: () => {
          // 保存后
          deleteKnowledgeManagement({ id: row.id })
            .then(res => {
              if (res.code === 1) {
                this.$XyyMessage.success('删除成功');
                this.getList(this.listQuery);
              } else {
                this.$XyyMessage.error(res.msg);
              }
            })
            .catch(function(error) {});
        }
      });
    },
    // 当前时间
    getNowTimeDate() {
      const time = new Date(8 * 3600 * 1000 + +new Date())
        .toJSON()
        .substr(0, 10);

      const cc = new Date().getTime();
      var halfYear = (365 / 2) * 24 * 3600 * 1000;
      var pastResult = cc - halfYear;
      var pastDate = new Date(pastResult),
        pastYear = pastDate.getFullYear(),
        pastMonth =
          pastDate.getMonth() + 1 < 10
            ? '0' + (pastDate.getMonth() + 1)
            : pastDate.getMonth() + 1,
        pastDay =
          pastDate.getDate() < 10
            ? '0' + pastDate.getDate()
            : pastDate.getDate();

      const oldTime = pastYear + '-' + pastMonth + '-' + pastDay;
      this.queryList.dateRange01 = [oldTime, time];
      this.queryList.dateRange02 = [oldTime, time];
    },
    dateTimeFocus() {
      this.minDate = undefined;
      // 条件时间查询
      const that = this;
      that.$nextTick(function() {
        document
          .getElementsByClassName('el-picker-panel')[0]
          .addEventListener('click', function() {});
      });
    },
    queryTime() {},

    /**
     * 初始化左侧树
     */
    initTree() {
      listAllClassification().then(res => {
        if (res.code === 1) {
          this.datas = res.data;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    /**
     * 获取数据
     */
    getData({ data, node }) {
      this.question.name = data.name;
      this.question.parentId = data.id;
      this.question.level = data.level;

      this.expandedKeysData = [];
      this.expandedKeysData.push(data.id);
      this.getList(this.listQuery);
    },
    // 关闭知识分类
    dialogCancel() {
      this.formTitle.search = '';
      this.question.name = '';
      this.question.parentId = '';
      this.questionEditable = false;
    },
    // 点击新增按钮事件
    newIncrease() {
      this.formTitle.title = '添加分类';
      this.formTitle.search = '';
      this.formTitle.addOrEdit = true;
      this.question.name = '';
      this.question.parentId = '';
      this.questionEditable = true;
    },
    /**
     * 添加分类
     */
    toAddQuestion(data) {
      this.formTitle.title = '添加分类';
      this.formTitle.search = '';
      this.formTitle.addOrEdit = true;
      this.question.name = data.name;
      this.question.parentId = data.id;
      this.questionEditable = true;
    },
    // 编辑分类
    toEditClassify(data) {
      this.formTitle.title = '编辑分类';
      this.formTitle.search = data.name;
      this.formTitle.addOrEdit = false;
      this.question.name = data.name;
      this.question.parentId = data.id;
      this.questionEditable = true;
    },
    // 点击确定添加分类
    dialogDetermine() {
      let searchDate = {
        name: this.formTitle.search
        // parentId: this.question.parentId ? this.question.parentId : '0'
      };
      let ids = 'parentId';
      if (!this.formTitle.addOrEdit) {
        ids = 'id';
      }
      searchDate[ids] = this.question.parentId ? this.question.parentId : '0';
      // 传给后台以后 数据清空 搜索框消失 重新更新数据
      this.formTitle.search = '';
      this.questionEditable = false;
      // 保存后
      saveOrUpdateKnowledgeClassification(searchDate)
        .then(res => {
          if (res.code === 1) {
            this.initTree();
            this.$XyyMessage.success('添加成功');
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(function(error) {});
    },
    // 删除分类
    toDelClassify(data) {
      // if (data.children.length > 0) {
      //   this.$XyyMsg({
      //     title: '提示',
      //     content: `该分类下有知识，无法删除，请先修改知识分类?`,
      //     onSuccess: () => {}
      //   });
      //   return;
      // }

      const searchDate = {
        id: data.id ? data.id : '0'
      };
      this.questionEditable = false;
      this.$XyyMsg({
        title: '提示',
        content: '删除分类后不可恢复，是否继续?',
        onSuccess: () => {
          delKnowledgeClassification(searchDate)
            .then(res => {
              if (res.code === 1) {
                this.initTree();
                this.$XyyMessage.success('删除成功');
              } else {
                this.$XyyMsg({
                  title: '提示',
                  content: res.msg,
                  onSuccess: () => {}
                });
              }
            })
            .catch(function(error) {});
        }
      });
    },
    // 删除三级目录
    toDelQuestion(data) {
      const searchDate = {
        id: data.id ? data.id : '0'
      };
      this.questionEditable = false;
      this.$XyyMsg({
        title: '提示',
        content: '删除分类后不可恢复，是否继续?',
        onSuccess: () => {
          delKnowledgeClassification(searchDate)
            .then(res => {
              if (res.code === 1) {
                this.initTree();
                this.$XyyMessage.success('删除成功');
              } else {
                this.$XyyMsg({
                  title: '提示',
                  content: res.msg,
                  onSuccess: () => {}
                });
              }
            })
            .catch(function(error) {});
        }
      });
    },
    // 取消批量移动
    dialogCancelBatchMobile() {
      this.isBatchMobile = false;
    },
    // 确认批量移动 knowledgeManagementIdList知识管理id classificationId知识分类id
    dialogDetermineBatchMobile() {
      if (!this.classificationId) {
        this.$XyyMessage.warning('请选择分类');
        return;
      }
      //参数
      let params = {
        knowledgeManagementIdList: this.selections,
        classificationId: this.classificationId
      };
      batchMoveKnowledgeManagement(params)
        .then(res => {
          if (res.code === 1) {
            this.isBatchMobile = false;
            this.selections = [];
            this.classificationId = '';
            this.$refs.orderTable.clearSelection();
            this.getList(this.listQuery);
            this.$XyyMessage.success('批量转移成功');
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(function(error) {});
    },
    // 批量移动
    batchMobile(row) {
      if (this.selections.length <= 0) {
        this.$XyyMessage.warning('请选择知识');
        return;
      }
      this.isBatchMobile = true;
    },
    /**
     * 获取数据
     */
    getBatchMobileData(data) {
      this.classificationId = data.id;
    }
    // sortData({ dragNode, dropNode, list }) {
    //   if (dragNode.question) {
    //     // 二级节点
    //     const ids = [dragNode.typeid, dropNode.typeid];
    //     const datas = list
    //       .filter(el => ids.includes(el.id))
    //       .map(el => {
    //         el.questions = el.questions.map((q, i) => {
    //           q.orderkey = i;
    //           q.typeid = el.id;
    //           return q;
    //         });
    //         return el;
    //       });
    //     updateSort({
    //       modifyType: 2,
    //       typeList: datas
    //     }).then(res => {
    //       if (res.code === 1) {
    //         this.initTree();
    //       } else {
    //         this.$XyyMessage.error(res.msg);
    //       }
    //     });
    //   } else {
    //     // 一级节点
    //     const datas = list.map((el, index) => {
    //       el.orderKey = index;
    //       return el;
    //     });
    //     updateSort({
    //       modifyType: 1,
    //       typeList: datas
    //     }).then(res => {
    //       if (res.code === 1) {
    //         this.initTree();
    //       } else {
    //         this.$XyyMessage.error(res.msg);
    //       }
    //     });
    //   }
    // }
  }
};
</script>

<style lang="scss" scoped>
.knowledge-management {
  padding: 20px;
  /deep/.table-containter {
    width: 74%;
    min-height: 400px;
  }
  /deep/.el-table__empty-block {
    width: 100 !important;
  }
  .status-tip {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin: 0 2px 2px 0;
    &.open {
      background: #67c23a;
    }
    &.close {
      background: #ff3024;
    }
  }
  .knowledge-management-box {
    padding-bottom: 20px;
    margin-bottom: 10px;
    border-bottom: 1px dotted #e4e4eb;
    .innerEl {
      height: 38px;
      /deep/.el-input__inner {
        width: 100%;
        height: 36px;
        line-height: 36px;
      }
      /deep/.el-form-item {
        margin: 0;
      }
      .timeSel {
        width: 300px;
      }
      /deep/input[type='number']::-webkit-inner-spin-button,
      /deep/input[type='number']::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      .btnNew {
        color: #fff;
        background-color: rgba(59, 149, 168, 1);
      }
      .serverName {
        width: 200px;
      }
      .serverNum {
        margin-right: 20px;
      }
      .searchCondition.is-plain {
        background: rgba(59, 149, 168, 1);
        color: #fff;
      }
      &.top_20 {
        margin-top: 20px;
      }
    }
  }
  .knowledge-classification {
    display: inline-block;
    width: 25%;
    height: auto;
    background: rgba(255, 255, 255, 1);
    border-radius: 2px;
    position: relative;
    .header {
      position: absolute;
      top: 0;
      right: 0;
      height: 40px;
      width: 100%;
      background: rgba(245, 247, 250, 1);
      border-radius: 1px 1px 0px 0px;
      border: 1px solid rgba(220, 222, 227, 1);
      border-bottom: none;
      .el-icon-plus {
        cursor: pointer;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(59, 149, 168, 1);
      }
      .el-icon-plus-text {
        padding-left: 10px;
        cursor: pointer;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(59, 149, 168, 1);
      }
    }
    .content {
      position: absolute;
      width: 100%;
      min-height: 462px;
      overflow-y: scroll;
      top: 40px;
      left: 0;
      padding: 20px;
      border-radius: 1px 1px 0px 0px;
      border: 1px solid rgba(220, 222, 227, 1);
      border-top: none;

      //滚动条样式
      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
        height: 5px;
      }
      &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: #f5f7fa;
      }
      &::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        background: #fff;
      }
    }
  }
  .dialogInner {
    .el-input__inner {
      height: 37px;
      line-height: 37px;
      margin-right: 5px;
      width: 320px;
    }
  }
  /deep/.current-row td {
    background-color: #bad6dc !important;
  }
}
</style>
