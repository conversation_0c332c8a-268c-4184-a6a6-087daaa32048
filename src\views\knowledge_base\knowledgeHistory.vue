<template >
  <div class="knowledge-history">
    <el-row type="flex" justify="space-between" align="top">
      <el-col :md="6" class="m-b">
        <div class="knowledge-classification">
          <div class="chat-tabs-content">
            <left-chatlist @click="emitChatItemClick" @callback="setGroupDatas" ref="LeftChatlist"></left-chatlist>
          </div>
        </div>
      </el-col>
      <el-col :md="18" class="p-r m-b">
        <el-card class="box-card" shadow="never" style="width: 100%;min-height:600px;">
          <el-breadcrumb class="app-breadcrumb" separator="/">
            <el-breadcrumb-item v-for="(item) in levelList" :key="item">
              <span class="no-redirect">{{ item }}</span>
            </el-breadcrumb-item>
          </el-breadcrumb>
          <el-form ref="from" :model="form" label-width="80px">
            <div class="header">
              <div class="title">
                <div>
                  <h3>{{form.name}}</h3>
                </div>
                <div>
                  <span class="title-info">发布人:{{ form.modifyName }}</span>
                  <span class="title-info">
                    发布时间: {{
                    form.modifyTime | dateFormat
                    }}
                  </span>
                  <span class="title-info">浏览量:{{form.viewNum}}</span>
                </div>
              </div>
            </div>

            <div class="body">
              <div class="message" v-html="form.editorText"></div>
              <!-- 附件 -->
              <el-form-item
                class="marginTopList"
                label="附件"
                :class="[{completedNo:uploadList.length===fileNumsNo} ]"
              >
                <el-upload
                  :disabled="true"
                  ref="myUploader"
                  :action="urlAction"
                  :limit="fileNums"
                  :with-credentials="true"
                  :class="[{completed:true}, previewNew ]"
                  name="files"
                  multiple
                  list-type="picture-card"
                  accept=".gif, .bmp, .jpg, .jpeg, .png, .pdf, .zip, .rar, .mp4, .avi, .doc, .docx, .ppt, .pptx, .xls, .xlsx, .txt"
                  class="upload-file-box"
                >
                  <div slot="file" slot-scope="{file}" class="filePreview">
                    <file-preview
                      :file="file"
                      :percent="file.percent?file.percent:0"
                      @abortCallback="abortFile(file)"
                      @delCallback="delFile(file)"
                    />
                  </div>
                </el-upload>
              </el-form-item>

              <el-form-item label="标签" :class="[{tagNo:tagList.length===fileNumsNo} ]">
                <!-- 标签管理 -->
                <div class="boxer">
                  <div class="tag-list">
                    <el-tag
                      v-for="tag in tagList"
                      :key="tag.tagName"
                      :hit="false"
                      color="#fff"
                    >{{tag.tagName}}</el-tag>
                  </div>
                </div>
              </el-form-item>
            </div>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import filePreview from '@/components/Fields/file-preview';
import LeftChatlist from './components/left_chatlist';
import utils from '@/utils/filter';
import {
  listKnowledgeManagementHistory,
  getKnowledgeManagementDetail
} from '@/api/knowledge_base';
import {
  compareFile,
  initFileDatas,
  adapter,
  typeCopyVal
} from '@/utils/tools.js';
export default {
  name: 'KnowledgeHistory',
  components: {
    LeftChatlist,
    filePreview
  },

  filters: {
    dateFormat(val) {
      return utils.dataTime(val, 'yy-mm-dd HH:ss:nn');
    }
  },
  data() {
    return {
      levelList: [],
      queryList: {},
      getDataList: [],
      list: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 10
      },
      datas: {},
      levelList: null,
      form: {
        name: '',
        viewNum: '',
        modifyTime: '',
        modifyName: '',
        name: '',
        editorText: '',
        id: '',
        currentVersion: ''
      },
      tagList: [],
      previewNew: 'previewNew',
      urlAction: process.env.BASE_API + '/fileUpload/uploadFile',
      fileNumsNo: 0,
      fileNums: 10, // 文件最大上传数
      uploadList: [], // 上传队列
      failureList: [], // 上传失败文件列表
      uploadingList: false, // 文件上传中
      previewModal: [], // 附件上传返回值的数组
      previewModalarr: [] // 向后台传的数据
    };
  },
  created() {},
  mounted() {
    // this.getList();
  },
  activated() {
    this.form.id = this.$route.query.templateId
      ? this.$route.query.templateId
      : '';
    // this.form.currentVersion = this.$route.query.current_version
    //   ? this.$route.query.current_version
    //   : '';
    const params = {
      id: this.form.id
    };
    listKnowledgeManagementHistory(params)
      .then(res => {
        if (res.code === 1) {
          const { total } = res.data;
          this.getDataList = res.data;
          let assignChatItemSelected = {};
          this.getDataList.forEach((item, index) => {
            item.assignChatItemSelected = false;
          });

          this.setGroupDatas(this.getDataList[0]);
          this.$refs.LeftChatlist.refUpdateChatList(this.getDataList);
          // this.listQuery = {
          //   ...this.listQuery,
          //   total: Number(total)
          // };
        } else {
          this.$XyyMessage.error(res.msg);
        }
      })
      .catch(() => {});
  },
  methods: {
    // 附件初始化
    initEnclosure(item) {
      return initFileDatas(item);
    },
    /**
     * 初始化附件数据
     */
    initFiles() {
      if (this.$refs['myUploader']) {
        this.$refs['myUploader'].uploadFiles = this.uploadList;
      }
    },
    /**
     * 取消上传
     */
    abortFile(file) {
      this.$refs['dialogUploader'].abort(file);
      this.$refs['dialogUploader'].uploadFiles.forEach((el, i) => {
        if (el.uid === file.uid) {
          this.$refs['dialogUploader'].uploadFiles.splice(i, 1);
        }
      });
      this.uploadList = [].concat(this.$refs['dialogUploader'].uploadFiles);
      this.uploading = false;
    },
    delFile(file) {
      // 删除上传队列中的数据
      this.$refs['dialogUploader'].uploadFiles.forEach((el, i) => {
        if (el.uid === file.uid) {
          this.$refs['dialogUploader'].uploadFiles.splice(i, 1);
        }
      });
    },

    emitChatItemClick() {
      this.$refs.LeftChatlist.refUpdateChatList(this.getDataList);
    },
    setGroupDatas(datas) {
      this.getDataList.forEach((item, index) => {
        if (
          datas.currentVersion.toString() === item.currentVersion.toString()
        ) {
          item.assignChatItemSelected = true;
          this.emitChatItemClick();
        } else {
          item.assignChatItemSelected = false;
        }
      });

      setTimeout(_ => {
        this.initFiles();
      }, 1000);

      this.form.id = datas.id;
      this.form.currentVersion = datas.currentVersion;
      this.tagList = [];
      // 查询知识管理详情
      getKnowledgeManagementDetail({
        id: this.form.id,
        currentVersion: this.form.currentVersion
      }).then(res => {
        if (res.code === 1) {
          this.form.name = res.data.title;
          this.form.editorText = res.data.content;
          this.form.viewNum = res.data.viewCount;
          this.form.modifyTime = res.data.modifyTime;
          this.form.modifyName = res.data.modifyName;
          this.levelList = res.data.imKnowledgeClassificationNames;
          if (res.data.synonyms) {
            const tagList = res.data.synonyms.split(',');
            tagList.forEach(item => {
              this.tagList.push({
                tagName: item
              });
            });
          }
          const enclosureItem = this.initEnclosure(res.data.attachment);
          this.uploadList = [].concat(enclosureItem);
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.knowledge-history {
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: rgba(87, 87, 102, 1);
  line-height: 20px;
  background: #f0f2f5 !important;

  .app-breadcrumb.el-breadcrumb {
    display: inline-block;
    font-size: 14px;
    line-height: 50px;
    margin-left: 10px;
    .no-redirect {
      color: #97a8be;
      cursor: text;
    }
  }

  .p-r {
    padding-right: 15px;
  }
  .m-b {
    margin-bottom: 15px;
    button {
      height: 30px;
      line-height: 30px;
      padding-top: 0;
      padding-bottom: 0;
    }
  }

  .header {
    margin: 0 auto;
    .title {
      text-align: center;
      // margin: 0 auto;
      // display: flex;
      // position: absolute;
      // left: 50%;
      // -webkit-transform: translateX(-50%);
      // transform: translateX(-50%);
      .title-info {
        margin-left: 20px;
      }
    }
  }
  .body {
    margin: 20px 40px;

    .message {
      // min-height: 400px;
      width: 100%;
      height: auto;
      /* table 样式 */
      /deep/ {
        table {
          border-top: 1px solid #ccc;
          border-left: 1px solid #ccc;
        }
        table td,
        table th {
          border-bottom: 1px solid #ccc;
          border-right: 1px solid #ccc;
          // padding: 3px 5px;
        }
        table th {
          background-color: #eee;
          // border-bottom: 2px solid #ccc;
          text-align: center;
        }

        /* blockquote 样式 */
        blockquote {
          display: block;
          border-left: 8px solid #d0e5f2;
          padding: 5px 10px;
          margin: 10px 0;
          line-height: 1.4;
          font-size: 100%;
          background-color: #f1f1f1;
        }

        /* code 样式 */
        code {
          display: inline-block;
          *display: inline;
          *zoom: 1;
          background-color: #f1f1f1;
          border-radius: 3px;
          padding: 3px 5px;
          margin: 0 3px;
        }
        pre code {
          display: block;
        }

        /* ul ol 样式 */
        ul,
        ol {
          margin: 0;
        }

        ul,
        dl {
          list-style-type: disc;
        }
        a {
          color: #428bca;
          text-decoration: none;
        }
        a:hover {
          text-decoration: underline;
        }
      }
    }
    .completedNo {
      display: none;
    }
    .tagNo {
      display: none;
    }
    .marginTopList {
      .previewNew {
        position: relative;
        /deep/.el-upload--picture-card {
          width: 102px;
          height: 36px;
          display: inline-block;
          line-height: 32px;
          border: 0 none;
          position: absolute;
          top: 0;
          left: 0;
          .el-button {
            width: 102px;
            height: 36px;
            background: rgba(255, 255, 255, 1);
            border-radius: 2px;
            border: 1px solid rgba(228, 228, 235, 1);
            color: rgba(87, 87, 102, 1);
          }
        }
        /deep/.el-upload-list--picture-card {
          display: inline-block;
          width: 271px;
          margin-top: 40px;
        }
        /deep/.el-upload-list__item {
          width: 126px;
          height: 30px;
          margin-bottom: 15px;
          display: inline-block;
          background: rgba(245, 247, 250, 1);
          border-radius: 2px;
          border: 1px solid rgba(228, 228, 235, 1);
          overflow: visible;
        }
      }
      .upload-file-box.completed {
        /deep/.el-upload--picture-card {
          display: none;
        }
      }
      /deep/.el-upload--picture-card {
        width: 0px;
        height: 0px;
      }
    }

    .boxer {
      width: 100%;
      height: calc(100% - 75px);
      overflow-y: auto;

      .tag-list {
        .el-tag {
          margin: 0 10px 10px 0;
          border: 0;
        }
      }
    }
  }

  .chat-tabs-content {
    width: 100%;
    height: calc(100% - 35px);
    overflow-x: hidden;
    overflow-y: auto;

    .tabs-content-item {
      display: none;
      width: 100%;

      &.is-active {
        display: inline-block;
      }

      //会话中列表排序
      .btn-sort-wrap {
        // width: calc(100% - 10px);
        width: 100%;
        height: 35px;
        padding: 0 10px;
        margin: 0 auto;
        border-bottom: 1px solid rgb(228, 228, 235);
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .el-button {
          height: 100%;
          padding: 10px 0;
          font-size: 12px;
          color: #575766;

          span {
            i {
              margin-left: -4px;
              color: #aeaebf;
            }
          }

          :hover {
            opacity: 0.75;
          }
        }
      }

      //历史会话搜索
      .form-search-wrap {
        // width: calc(100% - 10px);
        width: 100%;
        height: 50px;
        padding: 0 16px;
        margin: 0 auto;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        /deep/ {
          .el-input {
            .el-input__inner {
              font-size: 12px;
              padding: 0 8px;
            }
            .el-icon-search {
              float: right;
            }
          }
        }
      }

      //分页
      .pager {
        width: 100%;
        padding: 20px 10px;
        font-size: 12px;
        color: #909399;
        text-align: center;
      }
    }

    //滚动条样式
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }
    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: #f5f7fa;
    }
    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      background: #fff;
    }
  }
}
</style>
