import request from '@/utils/request-im';

/**
 *黑名单管理-来源渠道
 * @param {Object}查询参数
 */
export function getSources(params) {
  return request({
    url: '/blackList/getChannels',
    method: 'get'
  });
}

/**
 *黑名单管理列表
 * @param {Object}查询参数
 */
export function blacklistMange(params) {
  return request({
    url: '/blackList/listByPage',
    method: 'get',
    params: params
  });
}
/**
 *移除黑名单
 * @param {Object}查询参数
 */
export function removeBlacklist(params) {
  return request({
    url: '/blackList/delete',
    method: 'delete',
    data: params
  });
}

/**
 *添加黑名单（编辑黑名单）
 * @param {Object}查询参数
 */
export function addBlacklist(params) {
  return request({
    url: '/blackList/edit',
    method: 'post',
    data: params
  });
}

/**
 *分页获取禁用词
 * @param {Object}查询参数
 */
export function disableWordList(params) {
  return request({
    url: '/forbiddenwords/list',
    method: 'post',
    params: params
  });
}

/**
 *添加禁用词
 * @param {Object}查询参数
 */
export function addWordList(params) {
  return request({
    url: '/forbiddenwords/add',
    method: 'post',
    params: params
  });
}

/**
 *删除禁用词
 * @param {Object}查询参数
 */
export function aremoveWordList(params) {
  return request({
    url: '/forbiddenwords/delete',
    method: 'post',
    params: params
  });
}

/**
 *编辑禁用词
 * @param {Object}查询参数
 */
export function editorWordList(params) {
  return request({
    url: '/forbiddenwords/edit',
    method: 'post',
    params: params
  });
}

/**启用禁用
* @param {Object}查询参数
*/
export function enableDisable(params) {
  return request({
    url: '/forbiddenwords/edit/status',
    method: 'get',
    params: params
  });
}









