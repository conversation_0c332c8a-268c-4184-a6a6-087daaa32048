<template>
  <div class="channe-content">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span class="titlefont">问题分类统计 TOP 10</span>
        <el-button
          icon="el-icon-upload2"
          style="float: right; padding: 3px 0"
          type="text"
          @click="downOrderquestionClassificationMonitor"
        >导出excel</el-button>
      </div>

      <div slot="header" class="clearfix question-header">
        <div slot="header" class="question-address">
          <span>地区</span>
          <el-select
            v-model="cityCode"
            placeholder="全国"
            size="medium"
            popper-class="select-m"
            @change="changeType"
          >
            <el-option label="全国" value></el-option>
            <el-option
              v-for="item in workLocation"
              :key="item.id"
              :label="item.sourceName"
              :value="item.id"
            ></el-option>
          </el-select>
        </div>

        <div class="tabs-question">
          <el-radio-group
            size="medium"
            class="tabs-question-radio"
            v-model="activeTab"
            @change="changeActiveTab"
          >
            <el-radio-button :label="'1'">当日创建</el-radio-button>
            <el-radio-button :label="'2'">当月历史</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <div style="width:100%">
        <div id="questionClassificationMonitor" style="width: 100%;height:450px"></div>
      </div>
    </el-card>
  </div>
</template>

<script>
import echarts from 'echarts';

import { getCustomerSourceList } from '@/api/monitor/index.js';
export default {
  name: 'QuestionClassificationMonitor',
  data() {
    return {
      DataUrl: '',
      activeTab: '1', //1:当日创建,2:当月历史
      workLocation: [], // 客户所在地,
      cityCode: '', // 城市编码
      arr: '',
      formTypeId: ''
    };
  },
  created() {
    this.getCustomerSourceList();
  },
  mounted() {
    // this.initChart();
  },
  methods: {
    // 获取客户所在地数据
    getCustomerSourceList() {
      getCustomerSourceList().then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.workLocation = res.data;
      });
    },
    changeType() {
      this.$emit('callbackCityCode', this.cityCode);
    },
    /**
     * tab切换
     */
    changeActiveTab() {
      this.$emit('callbackTab', this.activeTab);
    },
    initChart(arr, formTypeId) {
      this.arr = arr;
      this.formTypeId = formTypeId;
      if (arr === null) {
        return;
      }

      const xAxisData = [];
      const seriesData = [];

      for (const key in arr) {
        xAxisData.push(arr[key].questionTypeName);
        seriesData.push(arr[key].createWorkOrderCount);
      }

      const xName =
        this.activeTab === '1' ? '当日创建工单数' : '当月历史创建工单数';

      const myChart2 = echarts.init(
        document.getElementById('questionClassificationMonitor')
      );

      const option2 = {
        legend: { data: [xName], y: 'bottom', x: 'center' },
        // dataZoom: [
        //   //1.横向使用滚动条
        //   {
        //     type: 'slider', //有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
        //     show: true, //是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
        //     start: 0, //数据窗口范围的起始百分比0-100
        //     end: 80, //数据窗口范围的结束百分比0-100
        //     xAxisIndex: [0] // 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
        //   }
        // ],
        tooltip: {
          //提示框组件
          trigger: 'item', //触发类型,'item'数据项图形触发，主要在散点图，饼图等无类目轴的图表中使用。 'axis'坐标轴触发，主要在柱状图，折线图等会使用类目轴的图表中使用。
          triggerOn: 'mousemove', //提示框触发的条件,'mousemove'鼠标移动时触发。'click'鼠标点击时触发。'mousemove|click'同时鼠标移动和点击时触发。'none'不在 'mousemove' 或 'click' 时触发
          showContent: true //是否显示提示框浮层
        },
        grid: {
          left: '1%',
          right: '3%',
          bottom: '8%',
          containLabel: true
          // width: '100%'
        },
        xAxis: {
          boundaryGap: true,
          type: 'category',
          axisLabel: {
            interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
            // rotate: 55 //调整数值改变倾斜的幅度（范围-90到90）
            formatter: function(params) {
              var newParamsName = ''; // 最终拼接成的字符串
              var paramsNameNumber = params.length; // 实际标签的个数
              var provideNumber = 8; // 每行能显示的字的个数
              var rowNumber = Math.ceil(paramsNameNumber / provideNumber); // 换行的话，需要显示几行，向上取整
              /** 判断标签的个数是否大于规定的个数， 如果大于，则进行换行处理 如果不大于，即等于或小于，就返回原标签 */

              if (paramsNameNumber > provideNumber) {
                // 条件等同于rowNumber>1
                /** 循环每一行,p表示行 */
                for (var p = 0; p < rowNumber; p++) {
                  var tempStr = ''; // 表示每一次截取的字符串
                  var start = p * provideNumber; // 开始截取的位置
                  var end = start + provideNumber; // 结束截取的位置
                  // 此处特殊处理最后一行的索引值
                  if (p == rowNumber - 1) {
                    // 最后一次不换行
                    tempStr = params.substring(start, paramsNameNumber);
                  } else {
                    // 每一次拼接字符串并换行
                    tempStr = params.substring(start, end) + '\n';
                  }
                  newParamsName += tempStr; // 最终拼成的字符串
                }
              } else {
                // 将旧标签的值赋给新标签
                newParamsName = params;
              }
              //将最终的字符串返回
              return newParamsName;
            }
          },
          data: xAxisData
        },
        yAxis: {
          name: '   工单数',
          type: 'value',
          minInterval: 1
        },
        series: [
          {
            color: '#F0AC4E',
            // animation: false,
            // barGap: '15%',
            name: xName,
            type: 'bar',
            barWidth: 40, //柱图宽度
            data: seriesData
          }
        ]
      };

      // 使用刚指定的配置项和数据显示图表。
      myChart2.setOption(option2);
      //根据窗口的大小变动图表 --- 重点
      window.onresize = function() {
        myChart2.resize();
      };

      // 暴露DataURL
      this.DataUrl = myChart2.getDataURL({
        pixelRatio: 1,
        backgroundColor: '#fff'
      });
    },
    downImg() {
      this.downloadFile('监控.png', this.DataUrl);
    },
    // 下载
    downloadFile(fileName, content) {
      // content = content.substring(content.indexOf(',') + 1);
      const aLink = document.createElement('a');
      const blob = this.base64ToBlob(content); // new Blob([content]);
      const evt = document.createEvent('HTMLEvents');
      evt.initEvent('click', true, true); // initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
      aLink.download = fileName;
      aLink.href = URL.createObjectURL(blob);
      aLink.dispatchEvent(
        new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window
        })
      ); // 兼容火狐
    },
    // base64转blob
    base64ToBlob(code) {
      const parts = code.split(';base64,');
      const contentType = parts[0].split(':')[1];
      const raw = window.atob(parts[1]);
      const rawLength = raw.length;

      const uInt8Array = new Uint8Array(rawLength);

      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i);
      }
      return new Blob([uInt8Array], { type: contentType });
    },
    //导出图表
    downOrderquestionClassificationMonitor() {
      const that = this;
      if (!that.arr || !that.arr.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      this.url = `${process.env.BASE_API}/workorder/monitor/listMonitorQuestionTypeWorkTotalExport?formTypeId=${that.formTypeId}&cityCode=${that.cityCode}&dateTimeType=${that.activeTab}`;
      if (
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
      ) {
        this.url =
          this.url +
          `&businessPartCode=${this.$store.getters.channel.businessPartCode}`;
      }
      const a = document.createElement('a');
      a.href = this.url;
      a.click();
    }
  }
};
</script>
<style lang="scss" scoped>
.box-card {
  min-width: 1080px !important;
  overflow: auto !important;
}
.clearfix {
  color: #292933;
  background: #fff;
  margin-bottom: 20px;
  font-weight: bold;
  font-size: 16px;

  .question-address {
    float: left;
    font-size: 14px;
    font-weight: normal;
    color: #606266;
  }

  /deep/ .tabs-question {
    height: 36px;
    // text-align: right;
    float: right;
    // margin-top: -78px !important;
    // margin-right: 20px;

    .tabs-question-radio {
      display: flex;
      flex-direction: row;
      /deep/ .el-radio-button {
        flex: 1;
        .el-radio-button__inner {
          width: 100%;
        }
      }
    }
  }
}
.question-header {
  margin-bottom: 0px;
}
</style>>
