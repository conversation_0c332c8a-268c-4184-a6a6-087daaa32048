'use strict';
const merge = require('webpack-merge');
const prodEnv = require('./prod.env');
// const HOST = 'dev';
const HOST = "test";
module.exports = merge(prodEnv, {
  NODE_ENV: '"development"',
  // BASE_API: '"https://cc-order.ybm100.com"',
  // BASE_API_IM: '"https://cc-order.ybm100.com/imapi"',

  // BASE_API: `"https://ec-service.${HOST}.ybm100.com"`,
  BASE_API: `"/"`,
  BASE_API_IM: `"https://ec-service.${HOST}.ybm100.com/imapi"`,
  BASE_IMG_URL: `"http://upload.${HOST}.ybm100.com"`,
  credential: true
});
