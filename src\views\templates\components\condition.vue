<template>
  <div>
    <el-dialog
      :visible="status"
      :close-on-click-modal="false"
      custom-class="condition-box"
      title="设置联动条件"
      top="0"
      @close="close"
    >
      <el-form :inline="true" :form="form">
        <el-form-item label="条件">
          <el-select
            v-model="form.conditionFieldCode"
            placeholder="请选择字段名"
            @change="handleFiledChange"
          >
            <el-option
              v-for="item in filterFields"
              :label="item.fieldText"
              :value="item.fieldCode"
              :key="item.fieldCode"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label>
          <el-select v-model="form.conditionSymbol" placeholder="判断方式">
            <el-option
              v-for="item in filterSymbolList(form.fieldType)"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label>
          <!-- 表单组件 -->
          <input-model
            v-if="form.conditionFieldCode && form.conditionSymbol>-1"
            ref="preview"
            :keys="form.fieldType"
            :symbol="Number(form.conditionSymbol)"
            :preview="form"
          />
        </el-form-item>
      </el-form>
      <el-row>
        <el-col :span="4">触发条件后状态</el-col>
        <el-col :span="15">
          <div style="margin-bottom:15px">
            <el-radio
              v-model="form.authTypeCondition"
              :label="1"
              @change="form.requiredCondition = 0"
            >只读</el-radio>
          </div>
          <div style="margin-bottom:15px">
            <el-radio v-model="form.authTypeCondition" :label="2">编辑</el-radio>
            <el-checkbox
              v-model="form.requiredCondition"
              :disabled="form.authTypeCondition !== 2"
              :true-label="1"
              :false-label="0"
            >必填项</el-checkbox>
          </div>
          <div v-if="data && data.systemFieldFlag" style="margin-bottom:15px">
            <el-radio
              v-model="form.authTypeCondition"
              :label="0"
              @change="form.requiredCondition = 0"
            >隐藏</el-radio>
          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import utils from '@/utils/form';
import inputModel from '@/views/form/components/form-type';
export default {
  components: {
    inputModel
  },
  props: {
    status: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: null
    },
    fields: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        conditionType: 0,
        conditionFieldCode: '',
        conditionSymbol: '', // 条件符号
        valueFirst: '', // 值
        authTypeCondition: 2, // 满足条件的权限类型 0-隐藏 1-只读 2-编辑
        requiredCondition: 0, // 满足条件后是否必填 0-否 1-是
        fieldType: '', // 表单组件类型
        optionSettings: null, // 选项设置
        defaultValue: '',
        tips: '',
        limitNumberCharacters: 0
      },
      symbolList: utils.conditionSymbolList
    };
  },
  computed: {
    filterFields() {
      return this.fields.filter(el =>
        this.data ? el.fieldCode !== this.data.fieldCode : true
      );
    }
  },
  watch: {
    status(val) {
      if (val) {
        this.form.fieldType = this.data.conditionList[0].fieldType;
        this.form.optionSettings = this.data.conditionList[0].optionSettings; // 选项设置
        this.form.limitNumberCharacters = this.data.limitNumberCharacters;
        this.form.conditionFieldCode = this.data.conditionList[0].conditionFieldCode;
        this.form.conditionSymbol = this.data.conditionList[0].conditionSymbol; // 条件符号
        this.form.authTypeCondition = this.data.conditionList[0].authTypeCondition; // 满足条件的权限类型 0-隐藏 1-只读 2-编辑
        this.form.requiredCondition = this.data.conditionList[0].requiredCondition; // 满足条件后是否必填 0-否 1-是
        this.$nextTick(() => {
          this.form.valueFirst = this.data.conditionList[0].valueFirst; // 值
          this.$refs['preview'].initData(
            this.form.fieldType,
            this.form.valueFirst
          );
        });
      }
    }
  },
  methods: {
    close() {
      this.$emit('update:status', false);
    },
    handleFiledChange(code) {
      let item = this.fields.filter(el => el.fieldCode === code)[0];
      item = JSON.parse(JSON.stringify(item));
      // 赋值表单类型和表单数据
      if (item.optionSettings && typeof item.optionSettings === 'string') {
        item.optionSettings = JSON.parse(
          item.optionSettings.replace(/&quot;/g, '"')
        );
      }
      Object.assign(this.form, {
        optionSettings: item.optionSettings,
        fieldType: Number(item.fieldType),
        conditionSymbol: ''
      });
      this.$nextTick(() => {
        this.$refs['preview'].resetValue(item.fieldType);
      });
    },
    // 确认添加字段联动条件
    save() {
      // console.log(this.form.conditionFieldCode, this.form.conditionSymbol);
      if (
        this.form.conditionFieldCode &&
        String(this.form.conditionSymbol) &&
        String(this.form.valueFirst)
      ) {
        this.$emit('callback', this.form);
        this.close();
      } else {
        this.$XyyMessage.error('请输入条件');
      }
    },

    filterSymbolList(type) {
      if ([0, 1].includes(type)) {
        return this.symbolList.filter(el => el.value === 2);
      } else if ([2, 3].includes(type)) {
        return this.symbolList.filter(el => [0, 1, 3].includes(el.value));
      } else if (type === 4) {
        return this.symbolList.filter(el => el.value === 3);
      } else if (type === 6) {
        return this.symbolList.filter(el => [4, 5].includes(el.value));
      }
    }
  }
};
</script>

<style lang="scss">
.el-dialog.condition-box {
  width: 750px;
  transform: translateY(-50%);
  top: 50%;
  .el-dialog__header {
    height: 52px;
    padding: 15px 20px;
    .el-dialog__title {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(41, 41, 51, 1);
      height: 22px;
      line-height: 22px;
    }
    .el-dialog__headerbtn {
      top: 18px;
    }
  }
  .el-dialog__body {
    padding: 15px 20px 0;
    /deep/.el-input__inner {
      min-height: 36px !important;
      line-height: 36px;
      height: 36px;
    }
    .preview {
      /deep/.el-form-item__content {
        width: 100%;
      }
    }
  }
}
</style>
