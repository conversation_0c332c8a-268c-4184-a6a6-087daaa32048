<template>
  <div class="table-containter">
    <el-table
      id="tabled"
      ref="table"
      :data="data"
      :show-summary="showSummary"
      :summary-method="getSummaries"
      :stripe="isStripe"
      :row-key="getRowKey"
      row-class-name="table-row"
      header-row-class-name="table-header-row"
      style="width: 100%"
      border
      :highlight-current-row="highlight_current_row"
      @select-all="selectAll"
      @selection-change="handleSelectionChange"
      @cell-mouse-enter="handleCellMouseEnter"
      @cell-click="handleCellClick"
      @sort-change="handleSortChange"
      @row-dblclick="handleRowdblClick"
    >
      <el-table-column
        v-if="hasSelection"
        :reserve-selection="true"
        type="selection"
        width="50"
        :selectable="columnSelectable"
      ></el-table-column>
      <el-table-column
        v-if="hasIndex"
        :index="indexFormat"
        type="index"
        width="70"
        label="序号"
      ></el-table-column>
      <template v-for="item in col">
        <slot v-if="item.slot" :name="item.index" :col="item"></slot>
        <!-- 鼠标移入，对话框展示全部 -->
        <el-table-column
          v-else-if="item.ellipsis"
          :key="item.index"
          :label="item.name"
          :width="item.width"
          :min-width="item.minWidth || 100"
          :resizable="item.resizable || false"
          :fixed="item.fixed"
          show-overflow-tooltip
        >
          <!-- tips提示框 只有超过显示的才会提示-->
          <template slot-scope="{ row }">
            {{ row[item.index] }}
            <!--<el-popover :offset="200" placement="bottom" trigger="hover">
              <div style="max-width:250px">{{ row[item.index] }}</div>
              <span slot="reference" class="popover-reference" />
            </el-popover>-->
          </template>
        </el-table-column>
        <!-- 操作 -->
        <el-table-column
          v-else-if="item.operation"
          :key="item.index"
          :label="item.name"
          :width="item.width"
          :min-width="item.minWidth || 100"
          :resizable="item.resizable || false"
          fixed="right"
          class-name="operation-box"
        >
          <template slot-scope="{ row }">
            <el-button
              v-for="btn in operation"
              :key="btn.type"
              :disabled="(btn.disabled && btn.disabled(row)) || false"
              :class="
                'operation-tip ' + (btn.isDelete === 1 ? 'open' : 'close')
              "
              type="text"
              @click="operationClick(btn.type, row)"
              >{{ (btn.format && btn.format(row)) || btn.name }}</el-button
            >
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.smallName"
          :key="item.index"
          :width="item.width"
          :min-width="item.minWidth || 100"
          :resizable="item.resizable || false"
          :prop="item.index"
          :fixed="item.fixed"
        >
          <template slot="header" slot-scope="scope">
            <p>{{ item.name }}</p>
            <small>{{ item.smallName }}</small>
          </template>
          <template slot-scope="scope">
            <div>{{ scope.row[item.index] }}</div>
          </template>
        </el-table-column>
        <el-table-column
          v-else
          :key="item.index"
          :prop="item.index"
          :label="item.name"
          :width="item.width"
          :min-width="item.minWidth || 100"
          :resizable="item.resizable"
          :fixed="item.fixed"
        />
      </template>
      <div slot="empty" class="empty-data-container">
        <img :src="emptyDataImgUrl" />
      </div>
    </el-table>

    <div v-if="isPagination && listQuery.total" class="pagination-container">
      <el-pagination
        :pager-count="pagination.pagerCount"
        :background="pagination.background"
        :layout="pagination.layout"
        :page-sizes="pagination.pageSizes"
        :small="pagination.small"
        :current-page.sync="listQuery.page"
        :page-size.sync="listQuery.pageSize"
        :total="listQuery.total"
        @current-change="getList"
        @size-change="updateSize"
      />
    </div>
  </div>
</template>
<script>
import emptyDataImgUrl from '@/assets/common/empty-data-table.png';
export default {
  name: 'XyyTable',
  components: {},
  props: {
    spanMehtod: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Array,
      default: function () {
        return [];
      },
    },
    listQuery: {
      type: Object,
      default: function () {
        return {
          page: 1,
          pageSize: 10,
          total: 0,
          small: false,
          pagerCount: 7,
        };
      },
    },
    col: {
      type: Array,
      default: function () {
        return [];
      },
    },
    operation: {
      type: Array,
      default: function () {
        return [];
      },
    },
    offsetTop: {
      type: Number,
      default: 64 + 64 - 15 + 76,
    },
    isPagination: {
      type: Boolean,
      default: true,
    },
    hasSelection: {
      type: Boolean,
      default: false,
    },
    hasIndex: {
      type: Boolean,
      default: false,
    },
    showSummary: {
      type: Boolean,
      default: false,
    },
    totalData: {
      type: Object,
      default: function () {
        return {};
      },
    },
    isStripe: {
      type: Boolean,
      default: true,
    },
    assignParams: {
      type: [String, Object],
      default: '',
    },
    pagination: {
      type: Object,
      default: function () {
        return {
          background: true,
          layout: 'total, sizes, prev, pager, next, jumper',
          pageSizes: [10, 20, 30, 40, 50, 100],
        };
      },
    },
    highlight_current_row: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      marginRight: true,
      tableHeight: 50,
      spanArr: [],
      selectedList: [], // 翻页全选总数据
      hasSelectionList: [], // 翻页全选临时数组
      emptyDataImgUrl,
    };
  },
  watch: {
    data: {
      handler: function (tableData) {
        this.emptyDataSlotPositionMiddle(tableData);
      },
    },
  },
  activated() {
    this.emptyDataSlotPositionMiddle(this.data);
  },
  mounted() {
    this.$nextTick(function () {
      this.getList();
      const offsetTop = this.offsetTop;
      const offsetBottom = 20 + (this.isPagination && 65) + 20;
      this.tableHeight = window.innerHeight - offsetTop - offsetBottom;
      this.marginRightFun();
      // 监听窗口大小变化
      window.onresize = () => {
        this.marginRightFun();
        this.tableHeight = window.innerHeight - offsetTop - offsetBottom;
      };
    });
  },
  methods: {
    /**
     * 当table展示字段较多时，无数据slot初始居中展示
     */
    emptyDataSlotPositionMiddle(tableData) {
      // console.log('tableData watcher trigged');
      if (!tableData.length) {
        try {
          let tableBodyWrapper = this.$el.querySelector(
            '.el-table__body-wrapper'
          );
          tableBodyWrapper.scrollLeft =
            this.$el.querySelector('.el-table__empty-block').clientWidth / 2 -
            tableBodyWrapper.clientWidth / 2;
        } catch (err) {}
      }
    },

    marginRightFun() {
      var tabledId = document.getElementById('tabled');
      if (!tabledId) {
        return false;
      }
      const boxWidth = document.getElementById('tabled').offsetWidth;
      const tabledWidth =
        document.getElementById('tabled').children[2].children[0].offsetWidth;
      this.marginRight = boxWidth < tabledWidth;
      if (this.marginRight) {
        document.getElementById('tabled').children[3].style.marginBottom =
          '18px';
      } else {
        document.getElementById('tabled').children[3].style.marginBottom = '0';
      }
    },
    getList: function () {
      const that = this;
      const { page, pageSize } = this.listQuery;
      const assignParams = this.assignParams;
      this.$emit('get-data', { page, pageSize, assignParams });
    },
    updateSize: function (pageSize) {
      this.getList();
    },
    operationClick: function (type, row) {
      this.$emit('operation-click', type, row);
    },
    /**
     * 选中操作
     */
    handleSelectionChange(val) {
      if (this.hasSelection) {
        this.$emit('selectionCallback', val);
      }
    },
    selectAll(val) {
      console.log(val, '全选');
    },
    clearSelection() {
      this.$refs.table.clearSelection();
    },
    handleCellClick(row, column, cell, event) {
      this.$emit('handleCellClick', row, column, cell, event);
    },
    handleRowdblClick(row, column, event) {
      this.$emit('row-dblclick', row, column, event);
    },
    handleCellMouseEnter(row, column, cell, event) {
      this.$emit('handleCellMouseEnter', row, column, cell, event);
    },
    indexFormat(index) {
      return index + 1;
    },
    getSummaries(param) {
      if (!this.showSummary) return -1;
      const { columns, data } = param;
      if (data.length === 0) return -1;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        sums[index] = this.totalData[column.property];
      });
      return sums;
    },
    getRowKey(row) {
      return row.id;
    },

    /**
     * 自定义排序时间
     */
    handleSortChange({ column, prop, order }) {
      this.$emit('sortChange', { column, prop, order });
    },

    /**
     *  checkbox是否可以选中
     */
    columnSelectable(row, index) {
      if (row.selectable !== undefined) {
        return row.selectable;
      } else {
        return true;
      }
    },

    /**
     * 清空checkbox选中项
     */
    clearSelection() {
      this.$refs.table.clearSelection();
    },
  },
};
</script>

<style lang="scss">
/* 省略号 */
.popover-reference {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
/* table */
.el-table {
  &::before,
  ::before {
    background-color: #fff;
  }
  font-family: PingFangSC-Regular;
  font-weight: 400;
  color: #292933;
  border-radius: 2px;
  border: 1px solid #e4e4eb;
  //border-width: 1px 1px 0;
  tr {
    td {
      border-right: 1px solid #e4e4eb;
      padding-top: 0 !important;
      padding-bottom: 0 !important;
      .cell {
        padding-left: 20px;
        padding-right: 20px;
      }
    }
    td:last-child {
      border-right: none;
    }
  }

  thead {
    color: #292933;
    th {
      height: 40px;
      padding: 0;
      font-weight: 400;
      background: #eeeeee;
      border: none;
      border-right: 1px solid #e4e4eb;
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
    th:last-child {
      border-right: none;
    }
  }
  tr {
    td {
      height: 40px;
      padding: 0;
      background: #f5f7fa;
      border-bottom: none;
      .cell {
        height: 40px;
        line-height: 40px;
        overflow-x: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .el-table__body {
    // 隔行变色
    tr.el-table__row--striped {
      td {
        background: #ffffff;
      }
    }
  }
  .table-row,
  .table-header-row {
    height: 40px;
  }
  .el-table__fixed-right {
    bottom: 0;
    height: auto !important;
    // margin-bottom: 0 !important;
  }
  .el-table__fixed-right-patch {
    background: #eeeeee;
  }
  // 解决右侧操作列错位问题
  .el-table__fixed-body-wrapper {
    position: absolute;
    top: 40px !important;
    bottom: 0;
    height: auto !important;
  }
  // 解决整体出现滚动条后底部白边
  &.el-table--scrollable-y .el-table__body-wrapper {
    position: absolute;
    top: 40px;
    bottom: 0;
    height: auto !important;
  }

  .operation-tip {
    &.open {
      color: #ff3024;
    }
    &.close {
      color: #3b95a8;
    }
  }
  .el-button.is-disabled,
  .el-button.is-disabled:hover,
  .el-button.is-disabled:focus {
    color: #c0c4cc;
  }

  .operation-box {
    width: auto;
    white-space: nowrap;
    letter-spacing: 0;
    margin: 0 -10px;
    .el-button {
      position: relative;
      margin: 0 10px;
    }
    .el-button::before {
      position: absolute;
      top: 14px;
      right: -10px;
      content: '';
      display: block;
      width: 1px;
      height: 12px;
      background: #dcdfe6;
    }
    .el-button:first-child {
      margin-left: 0;
    }
    .el-button:last-child {
      margin-right: 0;
    }
    .el-button:last-child::before {
      display: none;
    }
  }

  //无数据样式
  .empty-data-container {
    width: 100%;
    height: 460px;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 305px;
      height: 219px;
    }
  }
}
/* 分页 */
.pagination-container {
  margin-top: 20px;
  text-align: right;
  .el-pagination {
    position: relative;
    padding: 0;
    .el-pagination__total {
      position: absolute;
      top: 2px;
      left: 5px;
      height: 32px;
      line-height: 32px;
      font-size: 14px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      color: #575766;
    }
    @mixin reset($h: 32px, $fs: 14px) {
      height: $h;
      line-height: $h;
      font-size: $fs;
      font-family: Helvetica;
      font-weight: 400;
    }
    span,
    button {
      @include reset;
    }
    .el-pager {
      li {
        @include reset;
      }
    }
    .el-input--mini .el-input__inner {
      @include reset;
    }
    .el-pagination__editor.el-input .el-input__inner {
      @include reset;
    }
    .btn-prev,
    .btn-next,
    .el-pager li {
      background: #fff;
      width: 32px;
      height: 32px;
      border-radius: 2px;
      border: 1px solid #e4e4eb;
    }
    .el-pagination__editor {
      margin: 0 9px;
    }
  }
}
/* tips */
.el-tooltip__popper.is-dark {
  width: 300px;
  background: #fff;
  margin: 0;
  color: #000;
  border-radius: 2px;
  /*border: 1px solid #333;*/
  box-shadow: 0 0 5px #a9a9a9;
  .popper__arrow {
    border-top-color: #fff;
    &:after {
      border-top-color: #fff;
    }
  }
}
</style>
