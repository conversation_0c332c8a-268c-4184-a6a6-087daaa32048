<template>
  <div class="history-editor">
    <div class="editor-title">
      <el-button size="small" plain @click="toList">
        <svg-icon icon-class="backh"></svg-icon>返回工单
      </el-button>
      <span class="title-content">新建工单</span>
    </div>
    <el-form class="filter-box" label-width="96px">
      <el-form-item label="选择工单类型">
        <el-select v-model="formTypeId" placeholder="请选择" @change="getFormList">
          <el-option v-for="item in typeList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item v-show="formTypeId" label="选择工单">
        <el-select v-model="formId" placeholder="请选择" @change="getFieldList">
          <el-option v-for="item in formList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
    </el-form>
    <div v-if="datas.length" class="form-box">
      <field
        v-for="el in datas"
        v-show="getFieldVisible(el)"
        :key="el.fieldKey"
        :ref="'im'+el.fieldKey"
        :keys="el.fieldType"
        :preview="el"
        :read="getFieldEditable(el)"
        :requirs="getFieldVisible(el) && getFieldRequired(el)"
        refs="imField"
      ></field>
      <el-button plain @click="checkTimer(validateWorkorderRepeat,'timer')()">发起</el-button>
    </div>
    <el-dialog :visible.sync="msgOpen" custom-class="sheet-msg" title="提示" width="400">
      <span>
        近期有内容相似的工单已被创建，工单编号：
        <span class="worderId" @click="goDetail(workorderId)">{{ workorderNum }}</span>是否仍要发起
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="msgOpen = false">取 消</el-button>
        <el-button type="primary" @click="addSheet">继续发起</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import field from '@/components/Fields/preview'; // 字段组件
import {
  getSheetTypeList,
  seleSheetList,
  getNodeList,
  saveWorkorder,
  getWorkorderRepeateCheck
} from '@/api/mySheetManage';
import dataTime from '@/utils/filter.js';
export default {
  components: {
    field
  },
  props: {
    status: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      formTypeId: '', // 表单类型id
      formId: '', // 表单id
      typeList: [], // 表单类型数据
      formList: [], // 表单数据
      datas: [], // 字段数据
      workorderNum: '', // 工单编号
      workorderId: '', // 工单id
      msgOpen: false, // 提示框状态
      extendWorkorderField: [], // 表单结果数据
      timer: null
    };
  },
  mounted() {
    this.getTypeList(); // 获取表单类型
  },
  methods: {
    getTypeList() {
      getSheetTypeList({ status: 1 })
        .then(res => {
          if (res.code === 1) {
            this.typeList = res.data;
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },
    getFormList(id) {
      seleSheetList({ formTypeId: id, status: 1 })
        .then(res => {
          if (res.code === 1) {
            this.formList = res.data;
            this.formId = '';
            this.datas = [];
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },
    getFieldList(id) {
      getNodeList({ formId: id })
        .then(res => {
          if (res.code === 1) {
            this.datas = res.data;
            this.datas.forEach(el => {
              if (el.fieldType === 10) {
                this.$nextTick(() => {
                  if (this.$refs['ref' + el.fieldKey]) {
                    this.$refs['ref' + el.fieldKey][0].initFiles();
                  }
                });
              }
            });
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },
    toList() {
      this.$emit('update:status', true);
    },
    /**
     * 获取字段是否可见
     */
    getFieldVisible(el) {
      return !!this.getFieldStatus(el, 'authType');
    },
    /**
     * 获取字段是否可编辑
     */
    getFieldEditable(el) {
      return this.getFieldStatus(el, 'authType') === 1;
    },
    /**
     * 获取字段是否必须
     */
    getFieldRequired(el) {
      return !!this.getFieldStatus(el, 'required');
    },
    /**
     * 获取字段状态
     * key authType-状态 required-必填项
     * authType 0-隐藏 1-只读 2-编辑
     * required 0-否 1-是
     */
    getFieldStatus(el, key) {
      let statusCondition = el[key];
      if (el.nodeConditionList && el.nodeConditionList.length) {
        const nodeCondition = el.nodeConditionList[0];
        this.datas.forEach(item => {
          if (item.fieldId === nodeCondition.conditionFieldId) {
            let optionsValue;
            if (
              item.optionSettings &&
              typeof item.optionSettings === 'string'
            ) {
              optionsValue = JSON.parse(
                item.optionSettings.replace(/(&quot;)/g, '"')
              );
            } else {
              optionsValue = item.optionSettings;
            }
            // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
            let symbolSign = false;
            let value = [];
            switch (item.fieldType) {
              case 0:
              case 1:
              case 5:
              case 8:
              case 9:
                symbolSign = this.getConditionSymbol(
                  nodeCondition,
                  item.defaultValue
                );
                break;
              case 2:
                symbolSign = this.getConditionSymbol(
                  nodeCondition,
                  optionsValue.selectOptions.optionsValue
                );
                break;
              case 3:
                symbolSign = this.getConditionSymbol(
                  nodeCondition,
                  optionsValue.radioOptions.optionsValue
                );
                break;
              case 4:
                value = [...optionsValue.checkedOptions.optionsValue];
                value = value.sort();
                symbolSign = this.getConditionSymbol(nodeCondition, value);
                break;
              case 6:
                value = [...optionsValue.treeOptions.optionsValue];
                symbolSign = this.getConditionSymbol(nodeCondition, value);
                break;
            }
            if (symbolSign) {
              // 满足条件的权限类型 0-隐藏 1-只读 2-编辑
              statusCondition = el[`${key}Condition`];
            }
          }
        }, this);
      }
      return statusCondition;
    },
    getConditionSymbol(obj, entryValue) {
      // entryValue:输入值 valueFirst:条件值
      // 条件符号 0-是 1-不是 2-包含 3-包含任意 4-级联是 5-级联否
      const value = obj.valueFirst
        .split(',')
        .sort((a, b) => a - b)
        .join(',');
      let pass = false;
      switch (obj.conditionSymbol) {
        case 0:
        case 4:
          pass = entryValue === value;
          break;
        case 1:
        case 5:
          pass = entryValue !== value;
          break;
        case 2:
          pass = entryValue.indexOf(value) >= 0;
          break;
        case 3: {
          const arr = value.split(',').map(el => String(el));
          pass = arr.some(el =>
            entryValue.map(_el => String(_el)).includes(el)
          );
          break;
        }
      }
      return pass;
    },
    validateWorkorderRepeat() {
      const orderAndProb = [];
      this.datas.forEach(el => {
        if (
          el.fieldKey === '1164724692221825024' ||
          el.fieldKey === '1176037234466492416'
        ) {
          orderAndProb.unshift(el);
        }
      });
      if (orderAndProb.length > 0) {
        let problemClassification, orderNumber;
        orderAndProb.forEach(el => {
          if (el.fieldKey === '1164724692221825024') {
            const obj = {};
            if (
              !el.optionSettings.treeOptions ||
              el.optionSettings.treeOptions.optionsLabel
            ) {
              const arr2 = el.optionSettings.treeOptions.optionsLabel;
              el.optionSettings.treeOptions.optionsValue.forEach(function(
                item,
                index
              ) {
                obj[item] = arr2[index];
              });
            }
            problemClassification = obj;
          } else if (el.fieldKey === '1176037234466492416') {
            orderNumber = el.defaultValue;
          }
        });
        // console.log('问题分类or订单编号', problemClassification, orderNumber);
        getWorkorderRepeateCheck(orderNumber, problemClassification).then(
          res => {
            if (res.code) {
              this.addSheet();
            } else {
              this.workorderId = res.data.id;
              this.workorderNum = res.data.workorderNum;
              this.msgOpen = true;
              return false;
            }
          }
        );
      }
    },
    addSheet() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      this.msgOpen = false;
      var result = [];
      const refList = this.$refs;
      for (var i in refList) {
        var rest = new Promise((resolve, reject) => {
          refList[i][0].submitForm().then(
            valid => {
              resolve();
            },
            () => {
              reject('请检查输入！');
            }
          );
        });
        result.push(rest);
      }

      Promise.all(result)
        .then(() => {
          this.combSelVal();
        })
        .catch(() => {
          this.loading.close();
        });
    },
    combSelVal() {
      const extendWorkorderField = [];
      let uploading = false;
      this.datas.forEach(el => {
        if (el.fieldType === 10) {
          if (
            this.$refs['ref' + el.fieldKey] &&
            this.$refs['ref' + el.fieldKey][0].uploading
          ) {
            uploading = true;
          }
        }
      }, this);
      if (uploading) {
        this.$XyyMessage.warning('尚有未上传完成的附件，请稍后');
        return;
      }
      this.datas.forEach((item, index) => {
        // 字段类型
        // 0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期
        // 6:级联,7:省市区,8:电话,9:邮箱,10:附件
        if (this.getFieldVisible(item)) {
          if ([0, 1, 8, 9].includes(item.fieldType)) {
            extendWorkorderField.push({
              fieldKey: item.fieldKey,
              fieldSingleValue: item.defaultValue,
              sort: index
            });
          } else if (item.fieldType === 2) {
            if (
              !item.optionSettings.selectOptions ||
              !item.optionSettings.selectOptions.optionsValue
            ) {
              extendWorkorderField.push({
                fieldKey: item.fieldKey,
                fieldSingleValue: '',
                optionName: '',
                sort: index
              });
            } else {
              extendWorkorderField.push({
                fieldKey: item.fieldKey,
                fieldSingleValue:
                  item.optionSettings.selectOptions.optionsValue,
                optionName: item.optionSettings.selectOptions.optionsArray.filter(
                  (el, i) =>
                    el.val
                      ? el.val ===
                        item.optionSettings.selectOptions.optionsValue
                      : i === item.optionSettings.selectOptions.optionsValue
                )[0].optionsDesc,
                sort: index
              });
            }
          } else if (item.fieldType === 3) {
            if (
              !item.optionSettings.radioOptions ||
              !item.optionSettings.radioOptions.optionsValue
            ) {
              extendWorkorderField.push({
                fieldKey: item.fieldKey,
                fieldSingleValue: '',
                optionName: '',
                sort: index
              });
            } else {
              if (
                item.optionSettings.radioOptions.optionsArray.some((el, i) =>
                  el.val
                    ? el.val === item.optionSettings.radioOptions.optionsValue
                    : i === item.optionSettings.radioOptions.optionsValue
                )
              ) {
                extendWorkorderField.push({
                  fieldKey: item.fieldKey,
                  fieldSingleValue:
                    item.optionSettings.radioOptions.optionsValue,
                  optionName: item.optionSettings.radioOptions.optionsArray.filter(
                    (el, i) =>
                      el.val
                        ? el.val ===
                          item.optionSettings.radioOptions.optionsValue
                        : i === item.optionSettings.radioOptions.optionsValue
                  )[0].optionsDesc,
                  sort: index
                });
              }
            }
          } else if (item.fieldType === 4) {
            const obj = {};
            for (const i of item.optionSettings.checkedOptions.optionsValue) {
              if (item.optionSettings.checkedOptions.optionsArray[i]) {
                obj[i] =
                  item.optionSettings.checkedOptions.optionsArray[
                    i
                  ].optionsDesc;
              } else if (
                item.optionSettings.checkedOptions.optionsArray.some(
                  el => el.val === i
                )
              ) {
                obj[i] = item.optionSettings.checkedOptions.optionsArray.filter(
                  el => el.val === i
                )[0].optionsDesc;
              }
            }
            extendWorkorderField.push({
              fieldKey: item.fieldKey,
              fieldMultipleValue: JSON.stringify(obj),
              sort: index
            });
          } else if (item.fieldType === 5) {
            const dateOptions = item.optionSettings.dateOptions;
            const dateValue = dateOptions
              ? dataTime.dataTime(dateOptions.dateValue, dateOptions.dateSelect)
              : '';
            extendWorkorderField.push({
              fieldKey: item.fieldKey,
              fieldSingleValue: dateValue,
              sort: index
            });
          } else if (item.fieldType === 6) {
            const obj = {};
            if (
              item.optionSettings.treeOptions &&
              item.optionSettings.treeOptions.optionsLabel
            ) {
              const arr2 = item.optionSettings.treeOptions.optionsLabel;
              item.optionSettings.treeOptions.optionsValue.forEach(function(
                item,
                index
              ) {
                obj[item] = arr2[index];
              });
            }
            extendWorkorderField.push({
              fieldKey: item.fieldKey,
              fieldMultipleValue: JSON.stringify(obj),
              sort: index
            });
          } else if (item.fieldType === 7) {
            extendWorkorderField.push({
              fieldKey: item.fieldKey,
              fieldSingleValue:
                item.optionSettings.cityOptions.optionsValue +
                ' ' +
                item.optionSettings.cityOptions.optionsArray,
              sort: index
            });
          } else if (item.fieldType === 10) {
            let obj = [];
            const optionsArray = item.optionSettings.fileObj.optionsArray;
            if (optionsArray.length > 0) {
              obj = JSON.parse(JSON.stringify(optionsArray));
            }
            const objCopy = [];
            for (let i = 0; i < obj.length; i++) {
              objCopy.push(obj[i].data);
            }
            extendWorkorderField.push({
              fieldKey: item.fieldKey,
              fieldMultipleValue: JSON.stringify(objCopy),
              sort: index
            });
          }
        }
      }, this);
      this.extendWorkorderField = extendWorkorderField;
      this.saveWorkorder();
    },
    saveWorkorder() {
      // 发起工单
      const param = {
        baseWorkorderField: {
          formId: this.formId,
          formTypeId: this.formTypeId
        },
        extendWorkorderField: this.extendWorkorderField
      };
      return saveWorkorder(param).then(res => {
        if (res.code === 1) {
          this.loading.close();
          this.$XyyMessage.success('保存成功!');
          this.toList();
        } else {
          this.$XyyMessage.error(res.msg);
          this.loading.close();
        }
      });
    },
    goDetail(id) {
      this.msgOpen = false;
      this.$router.replace({
        path: '/workSheet/sheetDetail/' + id,
        query: { id: id, type: 'newSheet' }
      });
    }
  }
};
</script>
<style lang="scss">
.el-dialog.sheet-msg {
  width: 400px;
  height: 200px;
  .el-dialog__header {
    border: 1px solid rgba(238, 238, 238, 1);
  }
  .el-dialog__body {
    padding: 20px 20px 30px 20px;
    font-size: 14px;
    color: #292933;
    .worderId {
      color: #3b95a8;
    }
  }
}
</style>
<style lang="scss" scoped>
.history-editor {
  .editor-title {
    position: relative;
    .el-button.el-button--small {
      padding: 0 13px;
      line-height: 32px;
      &.el-button--default {
        border: 1px solid rgba(228, 228, 235, 1);
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
        &:hover,
        &:focus {
          border: 1px solid rgba(228, 228, 235, 1);
          background: #fff;
          color: rgba(87, 87, 102, 1);
        }
      }
      .svg-icon {
        margin-right: 8px;
      }
    }
    .title-content {
      font-size: 16px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: rgba(48, 49, 51, 1);
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translateX(-50%) translateY(-50%);
    }
  }
  .filter-box {
    margin-top: 20px;
    border-bottom: 1px dashed #e4e4eb;
    .el-form-item {
      margin-bottom: 20px;
      .el-form-item__label {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(41, 41, 51, 1);
        padding-right: 8px;
      }
      .el-select {
        width: 100%;
        /deep/.el-input__inner {
          height: 36px;
          line-height: 36px;
        }
      }
    }
  }
  .form-box {
    margin-top: 20px;
    .el-button {
      height: 36px;
      line-height: 36px;
      padding: 0 20px;
      border-color: #3b95a8;
      color: #3b95a8;
      margin-left: 96px;
      border-radius: 2px;
    }
  }
}
</style>
