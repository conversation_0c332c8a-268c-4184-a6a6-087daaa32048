<template>
  <div class="localSetUpNew">
    <xyy-list-page>
      <template slot="body">
        <el-form ref="form" :model="form" label-width="100px">
          <el-form-item label="组合名称">
            <el-input v-model="form.combinaName"></el-input>
          </el-form-item>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="form.groupDes" maxlength="100" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="包含省市">
            <el-button v-for="(item, index) in form.checkProvince">{{ item }}<span class="el-icon-close" @click="deleteDataP(index)"></span></el-button>
            <el-button v-for="(item, index) in form.checkCityList">{{ item }}<span class="el-icon-close" @click="deleteData(item,index)"></span></el-button>
            <el-button @click="AddProvCities"><span class="el-icon-plus">添加</span></el-button>
            <!-- 添加弹出框-开始-->
            <el-dialog title="选择省市" :visible.sync="dialogTableVisible">
              <div class="levelPanel">
                <!--<el-cascader-panel ref="cascaderAddr" :options="options" emitPath='false' :props="props"  @change="chooseCascader" v-model="form.ap"></el-cascader-panel>-->
                <div>
                  <keep-alive>
                    <el-checkbox-group v-model="form.checkProvince">
                      <p v-for="(item,index) in province">
                        <el-checkbox :label="item.areaName" @change="getCity(item,$event)" :indeterminate="item.judge"></el-checkbox>
                        <span class="text" @click="getCityAll(item)">{{item.areaName}}</span>
                      </p>
                    </el-checkbox-group>
                  </keep-alive>
                </div>
                <div>
                  <keep-alive>
                    <el-checkbox-group v-model="form.checkCity" ref="checkboxL">
                      <p v-for="(item,index) in city">
                        <el-checkbox :label="item.areaName" @change="chooseCity(item,$event)"></el-checkbox>
                        <span class="text">{{item.areaName}}</span>
                      </p>
                    </el-checkbox-group>
                  </keep-alive>
                </div>
              </div>
              <div slot="footer" class="dialog-footer">
                <el-button @click="dialogTableVisible = false">取 消</el-button>
                <el-button type="primary" @click="makeSure">确 定</el-button>
              </div>
            </el-dialog>
            <!-- 添加弹出框结束-->
          </el-form-item>
          <el-form-item label="对应员工组">
            <el-select v-model="form.groups" multiple placeholder="请接入员工组">
              <el-option
                v-for="item in employeGroups"
                :key="item.id"
                :label="item.groupName"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handerSave">保存</el-button>
          </el-form-item>
        </el-form>

        <el-cascader
          v-model="aaaaa"
          :options="options"
          :props="props"
          clearable></el-cascader>
      </template>
    </xyy-list-page>
  </div>
</template>

<script>
import XyyListPage from '../../components/xyy/xyy-list-page/index';
import XyyButton from "../../components/xyy/xyy-button/index";
import { provProvince, provCity, newEditor, EmployeeGroups } from '@/api/configuration/RegionalMenu';
export default {
  name: 'localSetUpNew',
  components: { XyyButton, XyyListPage },
  data() {
    return {
      aaaaa:[],
      props: { multiple: true },
      form: {
        combinaName: '', // 组合名称
        groupDes: '', // 备注
        groups: '', // 对应员工组
        checkProvince: [], // 选择的省
        checkCityList: [], // 选择的市
        checkCity: [] // 每次选择的市存储的值
      },
      employeGroups: [],
      valueTo: {}, // 点击编辑传递过来的值
      provAndCity: [], // 省和市的组合
      province: [], // 获取的省
      city: [], // 省对应的市
      dialogTableVisible: false, // 控制弹出框
      options: [{
        value: '指南',
        label: '指南',
        children: [{
          value: '设计原则',
          label: '设计原则'
        }, {
          value: '导航',
          label: '导航',
        }]
      }, {
        value: '组件',
        label: '组件',
        children: [{
          value: 'basic',
          label: 'Basic'
        }, {
          value: 'form',
          label: 'Form',
        }, {
          value: 'data',
          label: 'Data',
        }, {
          value: 'notice',
          label: 'Notice',
        }, {
          value: 'navigation',
          label: 'Navigation',
        }, {
          value: 'others',
          label: 'Others'
        }]
      }]
    }
  },
  created() {
    this.getValue()
  },
  methods: {
    // 保存
    handerSave() {
      console.log(this.checkProvince)
      console.log(this.checkCityList)
      let params
      newEditor(params).then(response => {

      }).catch(function(error) {
        console.log(error);
      });
    },
    // 点击添加事件
    AddProvCities() {
      let that = this
      // 循环省 如果省里面包含judge这个字段说明不是第一次进去，那就不赋值； 如果没有说明第一次进去，就赋值
      if (this.province.length === 0) {
        that.accessProvCities()
      } else {
        that.province = that.province
      }
      this.dialogTableVisible = true
      // 获取省市
    },
    // 删除选择事件
    deleteData(item, index) {
      console.log(item)
      console.log(this.form.checkCity)
      this.form.checkCity = this.form.checkCity.filter(function(itemL){
        return itemL !== item
      })
      this.form.checkCityList.splice(index, 1)
    },
    // 删除选择事件
    deleteDataP(index) {
      this.form.checkProvince.splice(index, 1)
    },
    // 获取省市
    accessProvCities() {
      var that = this;
      provProvince().then(response => {
        this.province = response.data
        var preParam = {
          provinceId: this.province[0].id
        }
        provCity(preParam).then(response => {
          this.city = response.data
          console.log(response.data)
          this.city = response.data.map(i => {
            this.$set(i, 'addName', this.province[0].areaName)
            return i
          })
        }).catch(function(error) {
          console.log(error);
        });
      }).catch(function(error) {

      });
    },
    // 点击省获取市
    getCity(item, ev) {
      var that = this;
      // 省显示的横线去掉
      item.judge = false;
      this.form.checkCity = []
      var preParam = {
        provinceId: item.id
      }
      provCity(preParam).then(response => {
        that.city = response.data
        // 将省传递个市 以便删除市的时候 不选中省
        that.city = response.data.map(i => {
          this.$set(i, 'addName', item.areaName)
          return i;
        })
        var valAll = [];
        // 点击省选中，市全部选中 如果选中后边的即选中，如果不选中市也不选中
        if (ev === true) {
          // that.removePointById(that.form.checkCityList, that.form.checkCity)

          that.$nextTick(function(){
            that.removePointById(that.form.checkCityList, that.form.checkCity)
          });

          that.city.forEach(function(itemP, index) {
            valAll.push(itemP.areaName)
            that.form.checkCity.push(itemP.areaName)
          })
          that.provAndCity.push({ name: item.areaName, value: [valAll] })
        } else if (ev === false) {
          that.provAndCity = that.provAndCity.filter(function(i){
            return i.name !== item.areaName
          })
          that.city.forEach(function(itemP, index) {
            that.form.checkCity.forEach(function(itemL, indexL) {
              if (itemP.areaName === itemL) {
                that.form.checkCity.splice(indexL, 1)
              }
            })
          })
        }
        if (ev.target.checked === true  ) {
          that.city.forEach(function(itemP, index) {
            that.form.checkCity.push(itemP.areaName)
          })
        } else if (ev.target.checked === false) {
          that.city.forEach(function(itemP, index) {
            that.form.checkCity.forEach(function(itemL, indexL) {
              if (itemP.areaName === itemL) {
                that.form.checkCity.splice(indexL, 1)
              }
            })
          })
        }
      }).catch(function(error) {
        console.log(error);
      });
    },
    // 点击市
    chooseCity(item, ev) {
      var that = this;
      if (ev === false) {
        // 不选择的时候 去掉包含所有省市的 存储的市的值
        that.provAndCity.forEach(function(itemA, indexA) {
          if (item.addName === itemA.name) {
            itemA.value.forEach(function(itemJ, indexJ) {
              itemJ.forEach(function(itemH, indexH) {
                if (itemH === item.areaName) {
                  itemJ.splice(indexH, 1)
                }
              })
            })
          }
        })
        // 为去掉的省添加变量 是选择的样式发生变化
        that.province.forEach(function(itemH, indexH) {
          if (itemH.areaName === item.addName){
            that.$set(itemH, 'judge', true)
          }
        })
        // 选择市的时候 没有选中就去掉这个省
        that.form.checkProvince = that.form.checkProvince.filter((itemL, index, arr) =>{
          return itemL !== item.addName
        })

      } else if (ev === true) {
        // 如果刚进入即点击市没有点击省，that.provAndCity是不存在的；重新赋值that.provAndCity.push({ name: item.areaName, value: [valAll] })
        console.log(item)
        var aaa = []
        aaa.push(item.areaName)
        if (that.provAndCity.length === 0) {
          that.provAndCity.push({ name: item.addName, value: [aaa] })
        } else {
          that.provAndCity.forEach(function(itemA, indexA) {
            if (item.addName === itemA.name) {
              itemA.value[0].push(item.areaName)
            }
          })
        }
        // 为去掉的省添加变量 是选择的样式发生变化
        that.province.forEach(function(itemH, indexH) {
          if (itemH.areaName === item.addName) {
            that.$set(itemH, 'judge', true)
          }
        })
      }
      // 判断市是全选还是不全选
        that.allOrNone(item, that)
    },
    // 判断市 是全选还是不全选
    allOrNone(item, that) {
      if (this.form.checkCity.length === this.$refs.checkboxL.$children.length) {
        that.form.checkProvince.push(item.addName)
        // 点击市的时候 如果市全选 显示省 去掉所有省对应的市
        that.removePointById(that.form.checkCityList,that.form.checkCity)
        // 判断 如果点击市全选 控制省的样式
        that.province.forEach(function(itemH, indexH) {
          if (itemH.areaName === item.addName){
            itemH.judge = false
          }
        })
      } else {
        this.form.checkCity.forEach(function(itemO,indexO) {
          that.form.checkCityList.push(itemO)
        })
        that.form.checkCityList = that.unique1(that.form.checkCityList)
      }
    },
    // 删除两个相同的数组； arr1:父数组   arr2:子数组
    removePointById(arr1, arr2) {
      for (let i = 0; i < arr2.length; i++) {
        for (let j = 0; j < arr1.length; j++) {
          if (arr2[i] === arr1[j]) {
            let indexs = arr1.indexOf(arr1[j]);
            arr1.splice(indexs, 1);
          }
        }
      }
      return arr1
    },
    // 数组去重
    unique1(arr) {
      let map = new Map()
      let newArr = []
      for (let i = 0; i < arr.length; i++) {
        if (!map.has(arr[i])) {
          map.set(arr[i])
          newArr.push(arr[i])
        }
      }
      return newArr
    },
    // 确定按钮
    makeSure() {
      this.checkCity = []
      this.dialogTableVisible = false
    },
    // 点击文字出现对应的市
    getCityAll(item) {
      this.checkCity = []
      var that = this;
      var preParam = {
        provinceId: item.id
      }
      provCity(preParam).then(response => {
        that.city = response.data
        console.log(this.provAndCity)
        this.provAndCity.forEach(function(itemG, indexG) {
          if (item.areaName === itemG.name) {
            that.form.checkCity = itemG.value[0]
          }
        })
        that.city = response.data.map(i => {
          this.$set(i, 'addName', item.areaName)
          return i
        })
      }).catch(function(error) {
        console.log(error);
      });
    },
    getValue() {
      this.aaaaa = ['指南']
      //   获取员工组
      EmployeeGroups().then(response => {
        this.employeGroups = response.data
      }).catch(function(error) {
        console.log(error);
      });
      this.valueTo = this.$route.query.obj
    }
  }
}
</script>

<style lang="scss">
  .localSetUpNew{
    .el-dialog{
      width: 400px;
      height: 602px;
    }
    .page-body{
      display: flex;
      justify-content: center;
      /*align-items: center;*/
      .levelPanel{
        @extend .page-body
      }
      .levelPanel{
        .el-checkbox-group{
          width: 180px;
          height: 250px;
          overflow-y: auto;
          .text{
            font-size: 14px;
            margin-left: 4px;
            cursor: pointer;
          }
          .el-checkbox__label{
            display: none;
          }
        }
      }

      .el-form{
        width: 50%;
        .el-select{
          width: 100%;
        }
      }
    }

  }
</style>
