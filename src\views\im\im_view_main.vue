<template>
  <div class="im-view-main-container">
    <im-view-left
      style="height:100%"
      ref="im_view_left"
      @select="chatSelect"
      @message="sendNewMessage"
    ></im-view-left>
    <im-view-center ref="im_view_center" @recallMessage="recallMessage"></im-view-center>
    <div style="height:100%" class="im_container_rightview">
      <div style="overflow-y: auto;height:100%">
        <ImViewRight ref="rightview"></ImViewRight>
      </div>
    </div>
  </div>
</template>

<script>
import { sendlog } from '@/api/im_view/index';
import ImViewLeft from './im_view_left';
import ImViewCenter from './im_view_center';
import ImViewRight from './im_view_right';
import userdetail from '../im_view/components/container_message/im_container_userdatail';

import { log } from 'util';
export default {
  name: 'compIMViewMain',
  components: {
    ImViewLeft,
    ImViewCenter,
    ImViewRight,
    userdetail,
    showRight: true
  },
  filters: {},
  props: {},
  provide() {
    return {
      compIMViewMain: this
    };
  },
  data() {
    return {
      notification: null, //浏览器系统通知对象
      notificationCloseTimer: null, //浏览器系统通知对象关闭计时器
      titleFlashTimer: null //title闪烁计时器
    };
  },
  watch: {},
  created() {},
  mounted() {
    this.documentVisibilitychange();
    // 关闭页签为在线会话时，清楚store选中的会话数据
    this.emits(this, 'register', {
      type: 'tagclose_imcontainer',
      uid: this._uid,
      fn: () => {
        this.$store.commit('imProperty/DEL_IMProperty_chat'); // 清空store中关于会话，客服 客户ID等内容
        this.$store.commit('imProperty/DEL_IMSheetItem'); // 清空store中的工单内容
      }
    });
    document.addEventListener('copy', this.systemCopyListener);
  },
  beforeDestroy() {
    document.removeEventListener('copy', this.systemCopyListener);
  },
  methods: {
    // 去除复制到的空格
    systemCopyListener(event) {
      let selection = window.getSelection().toString();
      console.log(selection)
      let modifiedText = selection.trim();
      console.log(modifiedText)
      event.clipboardData.setData('text/plain', modifiedText);
      event.preventDefault(); // 阻止默认复制行为
    },
    
    /**
     * 选中某个会话，去重载中间和右边组件
     */
    chatSelect(params, locationId) {
      try {
        this.$store.commit(
          'imProperty/SET_IMProperty_containerid',
          params.chat.id
        );
        this.$store.commit(
          'imProperty/SET_IMProperty_kfid',
          params.chat.kefuid
        );
        this.$store.commit('imProperty/SET_IMProperty_khid', params.userId);
        this.$store.commit('imProperty/SET_IMProperty_chat', params.chat);
      } catch (err) {
        this.saveLocalStorage({
          key: 'read-message-catch',
          value: Object.assign({}, locationId, { message: err.message })
        });
      }

      this.$refs.im_view_center.chatSelect(params);
      this.$refs.rightview.rightSelect(params);
    },

    /**
     * 轮询回来新消息，传递给中间组件更新消息
     */
    sendNewMessage(message, locationId) {
      this.$refs.im_view_center.setMessage(message);
    },

    /**
     * 撤回消息
     */
    recallMessage(params) {
      this.$refs.im_view_left.recallMessage(params);
    },

    /**
     * 待跟进，留言列表中，发起会话，系统自动移除本条记录
     */
    reChatRemoveAuto(params) {
      this.$refs.im_view_left.reChatRemoveAuto(
        Object.assign({}, params, {
          by: 'system'
        })
      );
    },

    /**
     * 客服主动结束会话(移至待跟进或历史)，会话中列表需要移除本条记录
     * 非客服主动结束的会话，才需要展示在会话中已结束的状态
     */
    closeChatRemoveAuto(params) {
      this.$refs.im_view_left.closeChatRemoveAuto(params);
    },

    /**
     * 重置聊天窗体状态
     */
    resetChat(locationId) {
      try {
        this.$store.commit('imProperty/SET_IMProperty_containerid', undefined);
        this.$store.commit('imProperty/SET_IMProperty_kfid', undefined);
        this.$store.commit('imProperty/SET_IMProperty_khid', undefined);
        this.$store.commit('imProperty/SET_IMProperty_chat', {});
      } catch (err) {
        this.saveLocalStorage({
          key: 'read-message-catch',
          value: Object.assign({}, locationId, { message: err.message })
        });
      }
      this.$refs.im_view_center.resetChat();
      this.$refs.rightview.resetRight();
    },

    /**
     * 定位到聊天记录tab
     */
    tohistory() {
      this.$refs.rightview.tohistory();
    },

    /**
     * 绑定游客后刷新会话信息
     */
    refreshChatInfo(params) {
      try {
        this.$store.commit(
          'imProperty/SET_IMProperty_containerid',
          params.chat.id
        );
        this.$store.commit(
          'imProperty/SET_IMProperty_kfid',
          params.chat.kefuid
        );
        this.$store.commit('imProperty/SET_IMProperty_khid', params.chat.uid);
        this.$store.commit('imProperty/SET_IMProperty_chat', params.chat);
      } catch (err) {
        this.saveLocalStorage({
          key: 'read-message-catch',
          value: Object.assign(
            {},
            { id: 'refreshChatInfo' },
            { message: err.message }
          )
        });
      }

      this.$refs.im_view_left.refreshChatInfo(params);
    },

    /**
     * 浏览器系统通知
     */
    sendSystemNotice() {
      console.log('document.visibilityState = ', document.visibilityState);
      console.log('document.hidden = ', document.hidden);
      //判断当前窗口是否处于激活状态，否则发系统提醒
      if (!document.hidden) {
        //页签页面是显示状态
        //判断在线会话页签是否处于激活状态，否则发系统提醒
        if (
          this.$store.getters.getActiveTagViewRouter.path ===
          '/imcontainer/list'
        ) {
          return false;
        }
      }

      /**
       * Notification.permission属性，用于读取用户给予的权限，它是一个只读属性，它有三种状态
       * default：用户还没有做出任何许可，因此不会弹出通知。
       * granted：用户明确同意接收通知。
       * denied：用户明确拒绝接收通知。
       */
      if (window.Notification) {
        Notification.requestPermission(status => {
          if (status === 'granted') {
            if (this.notification) {
              this.notification.close();
              clearTimeout(this.notificationCloseTimer);
            }
            this.notification = new Notification('消息提醒', {
              body: '您有一条新消息，请及时处理！',
              icon: '../../pc/static/user_kh.png'
            });

            //播放音频
            this.audioPlay();

            //title闪烁
            this.titleFlash();

            //关闭通知
            this.notificationCloseTimer = setTimeout(() => {
              this.notification.close();
            }, 3000);
          }
        });
      }
    },

    /**
     * 播放音频
     * https://developers.google.com/web/updates/2017/09/autoplay-policy-changes
     */
    audioPlay() {
      let elAudio = document.getElementById('audioPlay');
      elAudio.volume = 0.5;
      elAudio.currentTime = 0;
      let playPromise = elAudio.play();
      if (playPromise !== undefined) {
        playPromise
          .then(_ => {
            // Autoplay started!
            console.log('Autoplay started');
          })
          .catch(error => {
            // Autoplay was prevented.
            // Show a "Play" button so that user can start playback.
            console.log('Autoplay was prevented');
          });
      }
    },

    /**
     * title闪烁
     */
    titleFlash() {
      clearInterval(this.titleFlashTimer);

      let step = 0;
      flash_title();

      function flash_title() {
        step++;
        switch (step) {
          case 1:
            document.title = '您有一条新消息，请及时处理！';
            break;
          case 2:
            document.title = '\u200E';
            step = 0;
            break;
        }
      }

      this.titleFlashTimer = setInterval(() => {
        flash_title();
      }, 500);
    },

    /**
     * 移除title闪烁
     */
    titleFlashClear() {
      clearInterval(this.titleFlashTimer);
      document.title = '客服系统';
    },

    /**
     * 页面激活状态事件监听
     */
    documentVisibilitychange() {
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
          if (
            this.$store.getters.getActiveTagViewRouter.path ===
            '/imcontainer/list'
          ) {
            this.titleFlashClear();
          }
        }
      });
    },

    /**
     * 错误信息存localStorage
     */
    saveLocalStorage({ key, value, kefuInfo }) {
      /*
      try {
        let storageValue = JSON.parse(window.localStorage.getItem(key));

        if (Object.prototype.toString.call(storageValue) !== '[object Array]') {
          storageValue = [];
        }

        //删除前500条数据
        if (storageValue.length >= 500) {
          storageValue.splice(0, 250);
        }

        storageValue.push(value);

        window.localStorage.setItem(key, JSON.stringify(storageValue));
      } catch (err) {
        alert('localStorage存储失败!');
      }
      */
      sendlog({
        key,
        value,
        kefuInfo,
        time: this.newDateFormatter(),
        path: window.location.href
      });
      try {
        webSdk.track(key, {
          time: this.newDateFormatter(),
          path: window.location.href,
          key,
          value,
          kefuInfo
        });
      } catch (err) {
        console.error(err);
      }
      console.error(
        this.newDateFormatter(),
        window.location.href,
        key,
        value,
        kefuInfo
      );
    },

    /**
     * 销毁组件
     */
    destoryHandler() {
      //this.$destroy(this.$options.name);
    },

    /**
     * 获取时间格式字符串到毫秒
     */
    newDateFormatter() {
      const d = new Date();
      return `${d.getFullYear()}-${(d.getMonth() + 1)
        .toString()
        .padStart(2, 0)}-${d
        .getDate()
        .toString()
        .padStart(2, 0)} ${d
        .getHours()
        .toString()
        .padStart(2, 0)}:${d
        .getMinutes()
        .toString()
        .padStart(2, 0)}:${d
        .getSeconds()
        .toString()
        .padStart(2, 0)}.${d
        .getMilliseconds()
        .toString()
        .padStart(3, 0)}`;
    },

    /**
     * 发生错误无法访问，关闭页面
     */
    closeCompIMViewMain(errMsgStr) {
      this.$alert(errMsgStr, '提示', {
        confirmButtonText: '确定',
        callback: action => {
          this.$store.dispatch('tagsView/delView', this.$route).then(() => {
            if (this.$store.getters.visitedViews.length) {
              if (this.$route.path === '/imcontainer/list') {
                //当前激活的页签是会话中,关闭后,跳到tagView中最后一个页面
                this.$router.push(
                  this.$store.getters.visitedViews[
                    this.$store.getters.visitedViews.length - 1
                  ]
                );
              } else {
                //当前激活的页签不是会话中,无动作
              }
            } else {
              this.$router.push({
                path: '/empty'
              });
            }
          });
        }
      });
    },

    /**
     * 发送附件消息时，将左侧消息预览临时改成发送中
     */
    exchangeChatingListLastMsgPreview(dialogId, msgStr) {
      if (!dialogId) {
        return false;
      }
      let chatlist_ing = this.$refs['im_view_left'].chatlist_ing;
      chatlist_ing.forEach(item => {
        if (dialogId == item.id) {
          item.assignLastMessage = msgStr;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.im-view-main-container {
  width: 100%;
  background-color: #f0f2f5 !important;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;

  .im_container_rightview {
    background-color: #fff;
    flex-grow: 1;
    /* border: 1px solid #dcdee3; */
  }
}
</style>
