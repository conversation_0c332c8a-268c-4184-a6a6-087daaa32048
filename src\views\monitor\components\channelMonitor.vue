<template>
  <div class="channe-content">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span class="titlefont">渠道进线监控</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="downImg">保存图片</el-button>
      </div>
      <div style="width:100%">
        <div id="channelContainer" style="width: 100%;height:300px;"></div>
      </div>
    </el-card>
  </div>
</template>

<script>
import echarts from 'echarts';
export default {
  name: 'ChannelMonitor',
  data() {
    return {
      DataUrl: ''
    };
  },
  mounted() {
    // this.initChart();
  },
  methods: {
    initChart(arr) {
      console.log(arr);
      if (arr['APP(药帮忙)']) {
        arr['APP(药帮忙)']['c'] = '#F0AC4E';
      }
      if (arr['APP(豆芽)']) {
        arr['APP(豆芽)']['c'] = '#58C975';
      }
      if (arr['PC(药帮忙)']) {
        arr['PC(药帮忙)']['c'] = '#60B1FF';
      }
      // if (arr['微信服务号']) {
      //   arr['微信服务号']['c'] = '#58C975';
      // }
      if (arr['微信H5']) {
        arr['微信H5']['c'] = '#F67741';
      }
      if (arr['客服工作台']) {
        arr['客服工作台']['c'] = '#336699';
      }
      const data = Object.keys(arr);
      const seriesData = [];

      for (const key in arr) {
        seriesData.push({
          value: arr[key] && arr[key]['count'] ? arr[key]['count'] : '0', // 解决没有key或者count属性报错
          name: key,
          itemStyle: {
            color: arr[key] && arr[key]['c'] ? arr[key]['c'] : '#F0AC4E'
          }
        });
      }
      const myChart = echarts.init(document.getElementById('channelContainer'));

      // 指定图表的配置项和数据
      const option = {
        tooltip: {
          show: false,
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          bottom: 0,
          icon: 'circle',
          data: data
        },
        series: [
          {
            minAngle: 5,
            avoidLabelOverlap: true,
            name: '渠道来源',
            type: 'pie',
            radius: ['30%', '45%'],
            animation: false,
            avoidLabelOverlap: true,
            label: {
              show: true,
              position: 'outer',
              color: '#000',
              fontSize: '11',
              alignTo: 'none',
              formatter: '{b}:\n{c} ({d}%)'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '11',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: true,
              length: 3
            },
            data: seriesData,
            top: -30,
            right: 10,
            left: 10
          }
        ]
      };

      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option);

      // 暴露DataURL
      this.DataUrl = myChart.getDataURL({
        pixelRatio: 1,
        backgroundColor: '#fff'
      });
    },
    downImg() {
      this.downloadFile('渠道监控.png', this.DataUrl);
    },
    // 下载
    downloadFile(fileName, content) {
      // content = content.substring(content.indexOf(',') + 1);
      const aLink = document.createElement('a');
      const blob = this.base64ToBlob(content); // new Blob([content]);
      const evt = document.createEvent('HTMLEvents');
      evt.initEvent('click', true, true); // initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
      aLink.download = fileName;
      aLink.href = URL.createObjectURL(blob);
      aLink.dispatchEvent(
        new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window
        })
      ); // 兼容火狐
    },
    // base64转blob
    base64ToBlob(code) {
      const parts = code.split(';base64,');
      const contentType = parts[0].split(':')[1];
      const raw = window.atob(parts[1]);
      const rawLength = raw.length;

      const uInt8Array = new Uint8Array(rawLength);

      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i);
      }
      return new Blob([uInt8Array], { type: contentType });
    }
  }
};
</script>
<style lang="scss" scoped>
.titlefont {
  font-weight: bold;
  font-size: 16px;
}
</style>>
