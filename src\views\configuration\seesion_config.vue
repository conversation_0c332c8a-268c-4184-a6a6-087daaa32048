<template>
  <div>
    <div class="newsConfig">
      <el-row class="isConfing" type="flex" justify="start" align="top">
        <span class="_lable">分配策略</span>
        <div>
          <el-row>
            <el-radio v-model="assignMethod" label="dialogCount">当前会话数量最少优先</el-radio>
          </el-row>
          <el-row class="tip">当有新会话进来时，优先分配给当前会话数占比最少的坐席</el-row>
          <el-row class="mar_top15">
            <el-radio v-model="assignMethod" label="lastDialogTime">在线等待时长最久优先</el-radio>
          </el-row>
          <el-row class="tip">根据当前时间距离各坐席的最新会话时间，优先分配给等待最久的坐席</el-row>
        </div>
      </el-row>

      <el-row class="isConfing" type="flex" justify="start" align="top">
        <span class="_lable">记忆分配</span>
        <div>
          <el-row>
            <el-radio v-model="memoryMethod" label="1">开启</el-radio>
            <el-radio v-model="memoryMethod" label="0" style="margin-left:60px;">关闭</el-radio>
          </el-row>
          <el-row class="tip">开启后，在记忆有效期内咨询过的访客，再次咨询时优先分配给接待过的坐席</el-row>
        </div>
      </el-row>

      <el-row class="isConfing" type="flex" justify="start" align="top">
        <span class="_lable _padding13-top">记忆有效期</span>
        <div>
          <el-input
            :maxlength="4"
            :disabled="memoryMethod==='0'"
            v-model="timeLimit"
            onkeyup="if(this.value<=0) { value = null } this.value=this.value.replace(/[^\d.]/g,'');this.value=this.value.replace(/\./g,'');"
            class="_time"
            placeholder="请输入"
          >
            <svg-icon slot="suffix" icon-class="hour" class="hour" />
          </el-input>
          <el-row class="tip3">默认24小时，建议最大记忆有效期不超过200小时</el-row>
        </div>
      </el-row>

      <!-- 坐席工作时间 -->
      <el-row class="isConfing" type="flex" justify="start" align="top">
        <span class="_lable _padding13-top">坐席工作时间设置</span>
        <div>
          <el-time-select
            v-model="stateTime"
            :picker-options="{
              start: '00:00',
              step: '00:01',
              end: '23:59'
            }"
            placeholder="开始时间"
            :editable="true"
          ></el-time-select>
          <span class="tip">至</span>
          <el-time-select
            v-model="endTime"
            :picker-options="{
              start: '00:00',
              step: '00:01',
              end: '23:59'
            }"
            placeholder="结束时间"
            :editable="true"
          ></el-time-select>
        </div>
      </el-row>

      <el-button class="submBtn mar_top20" type="primary" @click="checkTimer(saveBtn,'time1')()">保存</el-button>
    </div>
  </div>
</template>

<script>
import {
  dialogassignruleAdd,
  getSessionMessage
} from '@/api/configuration/config_im';
export default {
  name: 'compSeesionConfig',
  data() {
    return {
      time1: null,
      assignMethod: 'dialogCount',
      memoryMethod: '1',
      timeLimit: null,
      stateTime: '',
      endTime: ''
    };
  },
  mounted() {
    this.getSessionInfo();
  },
  methods: {
    saveBtn() {
      console.log('this.memoryMethod:', this.timeLimit);

      if (this.memoryMethod === '1') {
        if (!this.timeLimit || this.timeLimit == 0) {
          this.$XyyMessage.error('请填写记忆有效期！');
          return;
        }
      }
      const param = {
        assignStrategy: this.assignMethod,
        memoryAssign: this.memoryMethod,
        memoryAssignTime: this.timeLimit,
        kefuWorkStartTime: this.stateTime,
        kefuWorkEndTime: this.endTime
      };
      dialogassignruleAdd(param)
        .then(res => {
          if (res.code === 1) {
            this.$XyyMessage.success('设置完成');
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },
    getSessionInfo() {
      getSessionMessage()
        .then(res => {
          if (res.code === 1) {
            this.assignMethod = res.data.assignStrategy;
            this.memoryMethod = res.data.memoryAssign;
            this.timeLimit = res.data.memoryAssignTime;
            this.stateTime = res.data.kefuWorkStartTime;
            this.endTime = res.data.kefuWorkEndTime;
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    }
  }
};
</script>

<style lang="scss" scoped>
.mar_top15 {
  margin-top: 15px;
}
.mar_top20 {
  margin-top: 20px;
}
.mar_top12 {
  margin-top: 12px;
}
.newsConfig {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  .isConfing {
    padding-top: 20px;
    ._lable {
      padding-top: 2px;
      display: inline-block;
      font-size: 14px;
      font-weight: none;
      color: rgba(41, 41, 51, 1);
      width: 80px;
      text-align: right;
      padding-right: 12px;
      box-sizing: content-box;
    }
    ._lable._padding13-top {
      padding-top: 13px;
    }
    .tip {
      padding-top: 7px;
      font-size: 12px;
      color: rgba(144, 147, 153, 1);
    }
    ._time {
      width: 100px;
      height: 36px;
      border-radius: 2px;
    }
    .tip3 {
      padding-top: 12px;
      font-size: 12px;
      color: rgba(144, 147, 153, 1);
    }
    .hour {
      width: 28px;
      margin-top: 13px;
      margin-right: 5px;
    }
  }
  .submBtn {
    margin-left: 100px;
  }
}
</style>
