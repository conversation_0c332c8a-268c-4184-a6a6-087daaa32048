<template>
  <div class="gdnumclass">
    <xyy-list-page>
      <template slot="header">
        <el-row class="herader-title" type="flex" justify="space-between" align="middle">
          <span style="font-size:16px">访客来源统计报表</span>
        </el-row>
        <el-form
          ref="listQuery"
          :rules="rules"
          :inline="true"
          :model="listQuery"
          label-position="right"
          class="search-form"
        >
          <el-row type="flex" class="row-bg" justify="space-between">
            <!-- 日期 -->
            <el-form-item label="日期" prop="dataRange">
              <el-date-picker
                :picker-options="pickerOptions"
                v-model="listQuery.dataRange"
                type="daterange"
                range-separator="-"
                size="small"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                text="erdf"
                prefix-icon="el-icon-date"
                class="innerSelTime"
                @change="queryTime()"
                @focus="dateTimeFocus()"
              />
            </el-form-item>
            <!--来源-->
            <el-form-item label="来源渠道" prop="sheetType" class="biangeng">
              <el-select v-model="listQuery.sheetType" placeholder="全部">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in oderCityData"
                  :key="item.id"
                  :label="item.groupName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row style="text-align:right;float:right;margin-top: -65px;">
            <el-form-item>
              <div class="searchTwoButton">
                <el-button
                  type="primary"
                  size="small"
                  @click="checkTimer(handerSearch('listQuery'))"
                >查询</el-button>
                <!-- <el-button size="small" @click="checkTimer(oldSearch('listQuery'))">重置</el-button> -->
                <el-button class="export-excel" @click="exportData" style="padding-top: 9px;">导出</el-button>
              </div>
            </el-form-item>
          </el-row>

          <el-row type="flex" class="row-bg" justify="space-between">
            <!--应用-->
            <el-form-item label="来源应用" prop="applicationType" class="biangeng">
              <el-select v-model="listQuery.applicationType" placeholder="全部">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in applicationChannelsData"
                  :key="item.appId"
                  :label="item.appName"
                  :value="item.appId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-row>
        </el-form>
      </template>
      <template slot="body">
        <div class="info-container">
          <info :info="infodata()" v-if="wenhao"></info>
        </div>
        <xyy-table
          v-loading="tabLoading"
          :is-stripe="false"
          :data="list"
          :col="col"
          @get-data="getList"
          :list-query="queryList"
          :offset-top="240"
        ></xyy-table>
      </template>
    </xyy-list-page>
    <expor-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    ></expor-tip>
  </div>
</template>

<script>
import exporTip from '@/views/work-sheet/components/exportTip';
import { mockGDType, mockGDUser } from './components/mock';
import { VisitorReport, getSources } from '@/api/nodemanager';
import formTypeListVue from '../form/form-type-list.vue';
import axios from 'axios';
export default {
  name: 'visitor',
  components: {
    exporTip
  },
  data() {
    return {
      tabLoading: false,
      ortoCityData: [],
      queryList: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      wenhao: true,
      changeExport: false,
      loading: false,
      mocktype: this.APIMockGDType(),
      mockCity: this.APICityGDType(),
      oderCityData: [
        { id: 1000, groupName: 'PC' },
        // { id: 1001, groupName: '微信服务号' },
        { id: 1002, groupName: '微信H5' },
        { id: 1003, groupName: 'APP' },
        { id: 1004, groupName: '客服工作台' }
      ],
      applicationChannelsData: [],
      dataRange: '',
      formAdressId: '',
      formTypeId: '',
      created: false,
      list: [],
      listQuery: {
        dataRange: [], // 日期
        sheetType: '', // 来源
        applicationType: '' // 应用
      },
      radio: 3,
      rules: {
        dataRange: [
          { required: false, message: '日期为必填项', trigger: 'blur' }
        ]
      },
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.minDate = minDate;
          this.maxDate = maxDate;
        },
        disabledDate: time => {
          const curDate = new Date().getTime();
          const two = 365 * 2 * 24 * 3600 * 1000;
          const twoyear = curDate - two;
          const three = 92 * 24 * 3600 * 1000;
          if (this.minDate) {
            return (
              time.getTime() > Date.now() ||
              time.getTime() < twoyear ||
              time > new Date(this.minDate.getTime() + three) ||
              time < new Date(this.minDate.getTime() - three)
            );
          }
          return time.getTime() > Date.now() || time.getTime() < twoyear;
        }
      },
      col: [
        {
          index: 'reportDate',
          name: '序号',
          width: 50,
          fixed: true,
          resizable: true
        },
        {
          index: 'appName',
          name: '来源应用',
          width: 130,
          fixed: true,
          resizable: true
        },
        {
          index: 'channelName',
          name: '来源渠道',
          width: 110,
          fixed: true,
          resizable: true
        },
        {
          index: 'statisticDate',
          name: '时间段',
          width: 120,
          fixed: true,
          resizable: true
        },
        {
          index: 'visitorTotal',
          name: '访问量',
          width: 80
        },
        {
          index: 'realVisitorTotal',
          name: '实际访客数',
          width: 100,
          resizable: true
        },
        {
          index: 'dialogTotal',
          name: '总会话数',
          width: 100,
          resizable: true
        },
        {
          index: 'validDialogTotal',
          name: '有效会话数',
          width: 100,
          resizable: true
        },
        {
          index: 'messageTotal',
          name: '消息数',
          width: 80,
          resizable: true
        },
        {
          index: 'kefuInviteAdviceTotal',
          name: '系统推评量',
          width: 100,
          resizable: true
        },
        {
          index: 'adviceTotal',
          name: '评价量',
          width: 80,
          resizable: true
        },
        {
          index: 'adviceRate',
          name: '参评率',
          width: 80,
          resizable: true
        },
        {
          index: 'veryWellTotal',
          name: '非常满意数',
          width: 110,
          resizable: true
        },
        {
          index: 'veryWellRate',
          name: '非常满意率',
          width: 110,
          resizable: true
        },
        {
          index: 'wellTotal',
          name: '满意数',
          width: 80,
          resizable: true
        },
        {
          index: 'wellRate',
          name: '满意率',
          width: 80,
          resizable: true
        },
        {
          index: 'justTotal',
          name: '一般数',
          width: 80,
          resizable: true
        },
        {
          index: 'justRate',
          name: '一般率',
          width: 80,
          resizable: true
        },
        {
          index: 'unsatisfyTotal',
          name: '不满意数',
          width: 90,
          resizable: true
        },
        {
          index: 'unsatisfyRate',
          name: '不满意率',
          width: 90,
          resizable: true
        },
        {
          index: 'veryPoorTotal',
          name: '非常不满意数',
          width: 110,
          resizable: true
        },
        {
          index: 'veryPoorRate',
          name: '非常不满意率',
          width: 110,
          resizable: true
        }
      ],
      url: ``
    };
  },
  created() {
    this.getNowTimeDate();
    this.getSourcesList();
    this.created = true;
  },
  activated() {
    // if (!this.created) {
    //   this.APISelectWorkorderList();
    // }
    this.created = false;
    this.getList(this.queryList);
  },
  methods: {
    getList: function(queryList) {
      const { page, pageSize } = queryList;
      const name = this.queryList.creatorName;
      const param = {
        pageNum: page,
        pageSize,
        endDate: this.listQuery.dataRange[1],
        startDate: this.listQuery.dataRange[0],
        channelId: this.listQuery.sheetType,
        appId: this.listQuery.applicationType
      };
      this.tabLoading = true;
      VisitorReport(param)
        .then(res => {
          // console.log(res);
          this.tabLoading = false;
          const { total } = res.data;
          this.list = res.data.records;
          let reportDate = {};
          let adviceRate = {};
          let veryWellRate = {};
          let wellRate = {};
          let justRate = {};
          let unsatisfyRate = {};
          let veryPoorRate = {};
          this.list.forEach((item, index) => {
            reportDate = {};
            adviceRate = {};
            veryWellRate = {};
            wellRate = {};
            justRate = {};
            unsatisfyRate = {};
            veryPoorRate = {};
            this.list[index] = Object.assign({}, this.list[index], {
              reportDate: index + 1
            });
            this.list[index] = Object.assign({}, this.list[index], {
              adviceRate: item.adviceRate + '%',
              veryWellRate: item.veryWellRate + '%',
              wellRate: item.wellRate + '%',
              justRate: item.justRate + '%',
              unsatisfyRate: item.unsatisfyRate + '%',
              veryPoorRate: item.veryPoorRate + '%'
            });
          });
          this.queryList = {
            ...this.queryList,
            total: Number(total)
          };
        })
        .catch(() => {
          this.tabLoading = false;
        });
    },
    handleChangeExport(info) {
      const that = this;
      this.changeExport = false;
      if (info === 'go') {
        that.$router.push({
          path: '/other/index/'
        });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    exportData() {
      const that = this;
      if (!that.list || !that.list.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      this.url = `${process.env.BASE_API_IM}/visitorSourceReport/export?endDate=${that.listQuery.dataRange[1]}&startDate=${that.listQuery.dataRange[0]}&channelId=${this.listQuery.sheetType}&appId=${this.listQuery.applicationType}`;
      if (
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
      ) {
        this.url =
          this.url +
          `&businessPartCode=${this.$store.getters.channel.businessPartCode}`;
      }
      const a = document.createElement('a');
      a.href = this.url;
      a.click();
    },
    // oldSearch() {
    //   this.getNowTimeDate();
    //   this.listQuery.sheetType = '';
    //   this.listQuery.CustLocation = '';
    //   this.APISelectWorkorderList();
    // },
    handerSearch(formName) {
      this.getList(this.queryList);
    },
    // 时间格式化
    getFormatDate: function(row, column, cellValue, index) {
      return new Date(cellValue + 8 * 3600 * 1000)
        .toJSON()
        .substr(0, 19)
        .replace('T', ' ');
    },
    // 当前时间
    getNowTimeDate() {
      const time = new Date(8 * 3600 * 1000 + +new Date())
        .toJSON()
        .substr(0, 10);

      this.listQuery.dataRange = [time, time];
    },
    dateTimeFocus() {
      this.minDate = undefined;
      // 条件时间查询
      const that = this;
      that.$nextTick(function() {
        document
          .getElementsByClassName('el-picker-panel')[0]
          .addEventListener('click', function() {
            // that.$refs.messageDrop.show();
          });
      });
    },
    queryTime() {
      // this.$refs.messageDrop.show();
    },
    getSummaries() {
      const sums = [];
      this.col.forEach((column, index) => {
        if (index === 0) {
          sums[column.index] = '合计';
          return;
        }
        const values = this.list.map(item => Number(item[column.index]));
        if (!values.every(value => isNaN(value))) {
          sums[column.index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          // sums[index] += ' 元';
        } else {
          sums[column.index] = '';
        }
      });

      this.list.push(sums);
    },

    changeToNumber(data) {
      const newdata = {};
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        for (const key in element) {
          if (element.hasOwnProperty(key)) {
            const objelement = element[key];
            newdata;
          }
        }
        newdata[element];
      }
    },
    infodata() {
      return [
        {
          title: '访问量',
          info: '访问的所有人数（不去重）,客服工作台无访问量'
        },
        {
          title: '来源渠道',
          info: 'PC 微信H5 APP 客服工作台'
        },
        {
          title: '实际访客数',
          info: 'user iD 唯一的访客数 ,客服工作台无实际访客数'
        },
        {
          title: '参评率',
          info: '参评率=评价量/系统推评量'
        },
        {
          title: '总会话数',
          info: '渠道的总会话数（总会话数=有效会话+无效会话）'
        },
        {
          title: '消息数',
          info: '访客和客服发生的所有消息数量'
        },
        {
          title: '主动邀评数',
          info: '客服邀请客户评价的次数'
        },
        {
          title: '评价量',
          info: '客户填写评价的数量'
        },
        {
          title: '非常满意数（满意/一般/不满意/非常不满意）',
          info: '客户选择非常满意（满意/一般/不满意/非常不满意）评价的数量'
        },
        {
          title: '非常满意度（满意/一般/不满意/非常不满意）',
          info: '客户对应评价数/评价量*100%'
        }
      ];
    },
    // 获取来源渠道数据
    getSourcesList() {
      getSources()
        .then(response => {
          this.applicationChannelsData = response.data;
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    /**
     * 工单量报表API
     */

    APISelectWorkorderList() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      selectWorkorderList({
        customerSource: this.listQuery.CustLocation,
        endTime: this.listQuery.dataRange[1],
        channelId: this.listQuery.sheetType,
        addId: this.listQuery.applicationType,
        startTime: this.listQuery.dataRange[0]
      }).then(res => {
        this.loading.close();
        let dataListList = res.data;
        if (
          !dataListList &&
          typeof dataListList !== 'undefined' &&
          dataListList != 0
        ) {
          this.$XyyMessage.success('暂时无数据');
          this.list = [];
          return;
        }
        if (res.code === 1) {
          if (res.data.length === 0) {
            this.$XyyMessage.success('暂时无数据');
          }
          this.list = res.data;
        } else {
          this.$XyyMessage.error(res.msg);
        }
        // this.getSummaries();
      });
    },
    APIMockGDType() {
      return mockGDType();
    },
    APICityGDType() {
      return mockGDUser();
    }
  }
};
</script>
<style scoped lang="scss">
/deep/.el-date-editor {
  /*width: 490px;*/
}
/deep/.page-header {
  padding-bottom: 0 !important;
}
</style>
<style>
.el-form--inline .biangeng {
  width: 56%;
}
.el-form--inline .biangeng .el-form-item__content {
  width: 65%;
}
.kuaijie {
  font-size: 14px;
  color: #606266;
  padding-right: 15px;
}
.danxaun {
  margin-top: 12px;
}
.search-form {
  border-bottom: 1px dashed #e4e4eb;
  overflow: hidden;
}
.herader-title {
  font-size: 14px;
  color: #393943;
  padding: 10px 0;
  background: #e4e4e4;
  margin-bottom: 20px;
}

.gdnumclass .el-table th > .cell {
  position: relative;
  word-wrap: normal;
  text-overflow: ellipsis;
  vertical-align: middle;
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  /* display: flex;
    flex-direction: column;
    align-items: flex-start; */
  padding-top: 10px;
  padding-bottom: 10px;
}

.gdnumclass .el-table th > .cell p {
  display: block;
  margin-block-start: 0px;
  margin-block-end: 0px;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  text-align: right;
}

.gdnumclass .el-table th > .cell small {
  font-size: 12px;
  color: #909399;
  display: block;
  margin-block-start: 0px;
  margin-block-end: 0px;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  text-align: right;
}

.gdnumclass .el-table tr td .cell {
  height: 100%;
  line-height: 25px;
  overflow-x: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  word-break: normal;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  padding-left: 10px;
  padding-right: 10px;
  white-space: normal;
  display: block;
  text-align: center;
}

.gdnum_input_group .el-form-item__error {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  margin-left: 15px;
}
.el-dialog__body {
  height: 400px;
  overflow-x: auto;
}
</style>

<style scoped lang="scss">
/deep/.el-table {
  /deep/.el-table__fixed {
    height: auto !important;
    bottom: 16px;
    margin-bottom: 0 !important;
  }
  /deep/.el-table__fixed-body-wrapper {
    top: 44px !important;
  }
}
/deep/.el-table__body tr td {
  border-bottom: 1px solid #ebeef5;
  background: #fff;
}
.el-menu-item.is-active {
  color: #3b95a8 !important;
}
/deep/.el-input__inner {
  height: 36px !important;
  line-height: 36px !important;
}
/deep/.el-button {
  height: 36px;
}
.el-menu.el-menu--horizontal {
  border-bottom: 1px dashed #e4e4eb;
  padding-bottom: 20px;
}
.search-box {
  width: 344px;
  float: right;
  .el-input {
    width: calc(100% - 76px);
    /deep/ .el-input__inner {
      height: 36px;
      line-height: 36px;
    }
  }
  button {
    width: 68px;
    height: 36px;
    float: right;
  }
}
.status-tip {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 2px 2px 0;
  &.open {
    background: #67c23a;
  }
  &.close {
    background: #ff3024;
  }
}

.toprow {
  display: flex;
  flex-direction: row;
}

.timediv {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 14px;
  margin-right: 30px;
}

/*.innerSelTime {*/
/*margin-left: 15px;*/
/*}*/

.gdnumheader {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.gdnum_input_mainheader {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.gdnum_input_group {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.gdnum_button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}

.searchTwoButton {
  width: 100%;
}
.el-form--inline .biangeng {
  width: 56%;
}
</style>

