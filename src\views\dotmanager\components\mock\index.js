export function tableHeaderData () {
  return [
    {
      type: 'selection'
    },
    {
      type: undefined,
      prop: 'dotname',
      label: '节点名称',
    },
    {
      type: undefined,
      prop: 'dottype',
      label: '节点类型',
    },
    {
      type: undefined,
      prop: 'dotdetail',
      label: '节点说明',
      minWidth: '150px'
    },
    {
      type: undefined,
      prop: 'updatetime',
      label: '更新时间',
    },
    {
      type: undefined,
      prop: 'updateuser',
      label: '更新人',
    },
    {
      type: undefined,
      prop: 'dotstate',
      label: '状态',
    },
    {
      type: undefined,
      prop: 'edit',
      label: '操作',
      fixed: 'right',
      minWidth: '120px'
    }
  ]
}

export function tableColData () {
  return [{
    dotname: '湖北二线处理',
    dottype: '发起',
    dotdetail: '记录客户来电服务沟通内容概要',
    updatetime: '2019-02-25 22:53:56',
    updateuser: 'asd',
    dotstate: '启用',
  }, {
    dotname: '湖北二线处理',
    dottype: '发起',
    dotdetail: '记录客户来电服务沟通内容概要',
    updatetime: '2019-02-25 22:53:56',
    updateuser: 'asd',
    dotstate: '启用',
  }, {
    dotname: '湖北二线处理',
    dottype: '发起',
    dotdetail: '记录客户来电服务沟通内容概要',
    updatetime: '2019-02-25 22:53:56',
    updateuser: 'asd',
    dotstate: '启用',
  }, {
    dotname: '湖北二线处理',
    dottype: '发起',
    dotdetail: '记录客户来电服务沟通内容概要',
    updatetime: '2019-02-25 22:53:56',
    updateuser: 'asd',
    dotstate: '启用',
  }]
}

export function paginationData () {
  return { currentPage: 1, pageSize: 5, total: 20, sizes: [5, 10, 15] }
}

export function inputsData () {
  return [
    {
      key: 'name',
      type: 'input',
      title: '节点名称',
      detail: ''
    }, {
      key: 'type',
      type: 'label',
      title: '节点类型',
      detail: '发起节点'
    }, {
      key: 'type',
      type: 'select',
      title: '选择模板',
      detail: [{ label: '1', value: '22' }, { label: 'q', value: 'qq' }, { label: 'w', value: 'ww' }],
      childer: [
        {
          type: 'button',
          title: '预览模板',
          actionName: 'seeAction'
        },
        {
          type: 'label',
          title: ''
        }
      ]
    }, {
      key: 'shoujihao',
      type: 'input',
      title: '手机号',
      detail: ''
    }, {
      key: 'shouquanshuliang',
      type: 'input',
      title: '授权数量',
      detail: ''
    }];
}


export function mockGDType () {
  return [{ name: '闷骚型', id: 0 }, { name: '性感型', id: 1 }, { name: '开朗型', id: 2 }];
}
export function mockGDUser () {
  return [{ name: '中国', id: 0 }, { name: '美国', id: 1 }, { name: '印度', id: 2 }];
}