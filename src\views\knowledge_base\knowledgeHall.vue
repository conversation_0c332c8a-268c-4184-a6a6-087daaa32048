<template>
  <div class="knowledge-hall-component-container">
    <!-- 一级分类队列 -->
    <categories-lv1-board :category="listCategoryAll" v-show="shownMode === 'home'"></categories-lv1-board>

    <!-- 所有分类的级联 -->
    <categories-all-board
      ref="categoriesAllBoard"
      :category="listCategoryAll"
      v-show="shownMode === 'category'"
    ></categories-all-board>

    <div
      class="hall-content-panel"
      :style="{'min-height':shownMode === 'home'?'calc(100% - 182px)':'calc(100% - 92px)'}"
    >
      <!-- 热搜榜 -->
      <div class="hall-content-hot" v-show="shownMode === 'home'">
        <hot-search-board></hot-search-board>
      </div>

      <!-- 列表 -->
      <div
        class="hall-content-list"
        :style="{width:shownMode === 'home'?'calc(100% - 416px)':'100%'}"
      >
        <!-- 搜索条件 -->
        <div class="content-list-title">
          <div class="title-left">知识列表</div>
          <div class="title-right">
            <el-form :inline="true" size="medium">
              <el-form-item>
                <el-input
                  v-model.trim="keyword"
                  placeholder="请输入关键字"
                  clearable
                  @keyup.enter.native="reqKeywordSearch"
                >
                  <!-- <i slot="suffix" class="el-input__icon el-icon-search"></i> -->
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-select v-model="sortType" @change="reqSortTypeChange">
                  <el-option label="按更新时间最新" value="updateTime"></el-option>
                  <el-option label="按发布时间最新" value="releaseTime"></el-option>
                  <el-option label="按热度" value="viewCount"></el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <!-- table展示数据 -->
        <div class="content-list-table">
          <xyy-table
            :data="list"
            :list-query="listQuery"
            :col="col"
            @get-data="getList"
            @row-dblclick="handleRowdblClick"
            class="table-customer-style"
          >
            <!-- 知识标题 -->
            <template slot="title" slot-scope="{ col }">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :label="col.name"
                :width="col.width"
                :resizable="col.resizable"
              >
                <template slot-scope="scope">
                  <span style="margin-right:4px;">{{ scope.row["title"]}}</span>
                  <template v-for="(tag,tagIndex) in scope.row['tagList']">
                    <template v-if="tagIndex < 5">
                      <el-tag size="mini" class="tag-bg" :key="tag">{{tag}}</el-tag>
                    </template>
                  </template>
                  <template v-if="scope.row['tagList'].length > 5">
                    <span>...</span>
                  </template>
                </template>
              </el-table-column>
            </template>
          </xyy-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CategoriesLv1Board from './components_knowledgeHall/categoriesLv1Board';
import CategoriesAllBoard from './components_knowledgeHall/categoriesAllBoard';
import HotSearchBoard from './components_knowledgeHall/hotSearchBoard';
import { listAllClassification, pageKnowledge } from '@/api/knowledge_base';
import utils from '@/utils/filter';
export default {
  name: 'knowledgeHall',
  components: {
    CategoriesLv1Board,
    CategoriesAllBoard,
    HotSearchBoard,
  },
  filters: {},
  props: {},
  provide() {
    return {
      knowledgeHall: this,
    };
  },
  data() {
    return {
      shownMode: 'home', //home:起始页模式,category:级联分类模式

      listCategoryAll: [], //分类树形体

      keyword: '', //关键词搜索
      sortType: 'updateTime',

      //搜索条件参数,//默认查询所有一级分类下的知识
      paramsQuery: {
        categoryId: '',
        level: 1,
      },

      //table数据
      col: [
        { index: 'title', name: '知识标题', resizable: false, slot: true },
        { index: 'viewCount', name: '阅读量', width: 100, resizable: false },
        { index: 'modifyTime', name: '更新时间', width: 200, resizable: false },
      ],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      list: [],
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.queryListAllClassification();
  },
  methods: {
    /**
     * 查询所有分类
     */
    queryListAllClassification() {
      listAllClassification()
        .then((resp) => {
          if (resp.code === 1) {
            this.listCategoryAll = this.listCategoryAllAdapter(resp.data);
          } else {
            this.$XyyMessage.error(resp.msg);
          }
        })
        .catch(() => {
          this.$XyyMessage.error('获取分类数据失败，请重试！');
        });
    },

    /**
     * 处理分类层级数据,添加是否叶子节点标识
     */
    listCategoryAllAdapter(listCategoryAll) {
      let list = listCategoryAll;
      for (let i = 0; i < list.length; i++) {
        if (list[i].name && list[i].name.length > 10) {
          list[i].nameShort = list[i].name.substr(0, 10) + '...';
        } else {
          list[i].nameShort = list[i].name;
        }
        if (list[i].children && list[i].children.length) {
          list[i].isLeaf = false;
          this.listCategoryAllAdapter(list[i].children);
        } else {
          list[i].isLeaf = true;
          delete list[i].children;
        }
      }
      return list;
    },

    /**
     * table查询数据
     */
    getList(listQuery) {
      const loading = this.$loading({
        target: document.querySelector('#app'),
        lock: true,
        spinner: 'el-icon-loading',
        text: '加载中...',
        background: 'rgba(255,255,255, 0.8)',
      });
      let queryParams = listQuery || this.listQuery;

      //根据类型id和关键词搜索
      pageKnowledge({
        categoryId: this.paramsQuery.categoryId,
        level: this.paramsQuery.level,
        keyword: this.keyword,
        sortFiled: this.sortType,
        pageNumber: queryParams.page,
        pageSize: queryParams.pageSize,
      })
        .then((resp) => {
          if (resp.code === 1) {
            this.list = resp.data.records;

            this.listQuery = {
              page: queryParams.page,
              pageSize: queryParams.pageSize,
              total: resp.data.total,
            };

            //处理标签
            let modifyTime = '';
            let tagList = [];
            this.list.forEach((item, index) => {
              modifyTime = '';
              tagList = [];

              if (item.modifyTime == null) {
                modifyTime = '-';
              } else {
                modifyTime = utils.dataTime(
                  item.modifyTime,
                  'yy-mm-dd HH:ss:mm'
                );
              }

              if (item.synonyms) {
                tagList = item.synonyms.split(',');
              }
              this.list[index] = Object.assign({}, this.list[index], {
                modifyTime: modifyTime,
                tagList: tagList,
              });
            });
          } else {
            this.$XyyMessage.error(resp.msg);
          }
        })
        .catch(() => {
          this.$XyyMessage.error('获取知识列表失败，请重试！');
        })
        .finally(() => {
          loading.close();
        });
    },

    /**
     * 双击进详情
     */
    handleRowdblClick(row, column, cell, event) {
      this.$router.push({
        path: `/knowledge_base/knowledgeDetails/${row.id}`,
        query: { templateId: row.id },
      });
    },

    /**
     * 顶部一级分类，选择了某个一级分类
     */
    toQueryCategoryLv1(categoryLv1) {
      this.$refs.categoriesAllBoard.cascaderListViewAdapter([categoryLv1.id]);
      this.shownMode = 'category';

      //分页参数
      this.listQuery.page = 1;
      this.listQuery.pageSize = 10;

      //查询的关键词
      this.keyword = '';

      //查询的id参数
      this.paramsQuery.categoryId = categoryLv1.id;
      this.paramsQuery.level = 1;

      this.getList(this.listQuery);
    },

    /**
     * 左侧热搜榜，点击任意一个关键词
     */
    toQueryKnowledgeByKeyword(keyword) {
      this.shownMode = 'home';

      //分页参数
      this.listQuery.page = 1;
      this.listQuery.pageSize = 10;

      //查询的关键词
      this.keyword = keyword;

      //查询的id参数
      this.paramsQuery.categoryId = '';
      this.paramsQuery.level = 1;

      this.getList(this.listQuery);
    },

    /**
     * 顶部所有分类，选择了全部分类
     */
    toQueryCategoryAll() {
      this.shownMode = 'home';

      //分页参数
      this.listQuery.page = 1;
      this.listQuery.pageSize = 10;

      //查询的关键词
      this.keyword = '';

      //查询的id参数
      this.paramsQuery.categoryId = '';
      this.paramsQuery.level = 1;

      this.getList(this.listQuery);
    },

    /**
     * 顶部所有分类，选择了任意一个分类
     */
    toQueryCategoryLv(categoryIdsClicked) {
      this.$refs.categoriesAllBoard.cascaderListViewAdapter(categoryIdsClicked);
      this.shownMode = 'category';

      //分页参数
      this.listQuery.page = 1;
      this.listQuery.pageSize = 10;

      //查询的关键词
      this.keyword = '';

      //查询的id参数
      this.paramsQuery.categoryId =
        categoryIdsClicked[categoryIdsClicked.length - 1];
      this.paramsQuery.level = categoryIdsClicked.length;

      this.getList(this.listQuery);
    },

    /**
     * 搜索条件输入框enter查询
     */
    reqKeywordSearch() {
      switch (this.shownMode) {
        case 'home':
          //查所有的

          //分页参数
          this.listQuery.page = 1;
          this.listQuery.pageSize = 10;

          //查询的id参数
          this.paramsQuery.categoryId = '';
          this.paramsQuery.level = 1;

          this.getList(this.listQuery);
          break;
        case 'category':
          //查某个分类下的

          //分页参数
          this.listQuery.page = 1;
          this.listQuery.pageSize = 10;

          //查询的id参数
          let cascaderValue = this.$refs.categoriesAllBoard.cascaderList[0][
            'value'
          ];
          this.paramsQuery.categoryId = cascaderValue[cascaderValue.length - 1];
          this.paramsQuery.level = cascaderValue.length;

          this.getList(this.listQuery);
          break;
      }
    },

    /**
     * 排序方式改变事件
     */
    reqSortTypeChange() {
      switch (this.shownMode) {
        case 'home':
          //查所有的

          //分页参数
          this.listQuery.page = 1;
          this.listQuery.pageSize = 10;

          //查询的id参数
          this.paramsQuery.categoryId = '';
          this.paramsQuery.level = 1;

          this.getList(this.listQuery);
          break;
        case 'category':
          //查某个分类下的

          //分页参数
          this.listQuery.page = 1;
          this.listQuery.pageSize = 10;

          //查询的id参数
          let cascaderValue = this.$refs.categoriesAllBoard.cascaderList[0][
            'value'
          ];
          this.paramsQuery.categoryId = cascaderValue[cascaderValue.length - 1];
          this.paramsQuery.level = cascaderValue.length;

          this.getList(this.listQuery);
          break;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.knowledge-hall-component-container {
  width: 100%;
  height: 100%;
  background-color: #f0f2f5 !important;

  .hall-content-panel {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: stretch;

    .hall-content-hot {
      flex-shrink: 0;
      flex-grow: 0;
      margin-top: 16px;
      margin-right: 16px;
    }

    .hall-content-list {
      flex-grow: 1;
      flex-shrink: 1;
      background-color: #fff;
      margin-top: 16px;

      .content-list-title {
        height: 60px;
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title-left {
          font-size: 16px;
          font-weight: 600;
          color: #292933;
        }

        .title-right {
          .el-form {
            font-size: 0;
            .el-form-item {
              width: 200px;
              margin-right: 0;
              margin-bottom: 0;
              margin-left: 12px;

              /deep/ .el-form-item__content {
                width: 100%;

                .el-input,
                .el-select {
                  width: 100%;
                }
              }
            }
          }
        }
      }

      .content-list-table {
        padding: 0 20px 20px;

        .table-customer-style {
          /deep/ {
            .el-table__header-wrapper {
              .table-header-row th {
                border: none;
              }
            }

            .el-table__body-wrapper {
              .el-table__row {
                td {
                  border: none;

                  .tag-bg {
                    text-align: center;
                    font-size: 10px;
                    background: #f5f7fa;
                    border: 1px solid #e4e4eb;
                    color: #575766;
                    border-radius: 3px;
                    margin-right: 4px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>