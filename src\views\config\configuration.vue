<template>
  <div class="temConfiguration">
    <xyy-list-page>
      <template slot="header">
        <el-form ref="formType" :model="formType" label-width="80px">
          <el-form-item label="工单类型" class="formTypeClass">
            <el-select v-model="formType.orderType" placeholder="请选择" @change="handerChangeType">
              <el-option
                v-for="item in orderTypeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <xyy-button @click="handerImport" class="importClass">
          <span class="el-icon-download"></span>导入
        </xyy-button>
        <el-popover placement="bottom-start" width="359" popper-class="popoverAll" trigger="hover">
          <p>
            <span>导入</span>
            <br />下载导出模板，填写数据后，可进行导入操作。因导入是全量替换原数据，如果问题分类作为了流程分支的条件，会引起流程出错，所以这种情况系统不允许导入。
          </p>
          <p>
            <span>导入失败</span>
            <br />存在以下情况会导致导入部分数据失败。
            <br />整体文件导入失败：1.此文件不是excel文件 2.没有找到可读取的sheet 3.解析文件失败（是excel但是文件有问题） 4.文件列名不匹配 5.文件列名不匹配：文件列名比预期多
            <br />文件部分数据导入失败（excel中提示语）：1.有多余列数据 2.数据有断级 3.数据重复
          </p>
          <div slot="reference" style="cursor:pointer;">
            <svg-icon class="icon-info" icon-class="info"></svg-icon>
          </div>
        </el-popover>
      </template>
      <template slot="body">
        <div class="tableTree">
          <div class="tableTreeRight tableTreeTitle">
            <span>
              <div style="padding-left: 16px;">问题分类</div>
            </span>
            <span :style="{width: tableTreeTwo}" ref="tableTreeTwo" class="tableTreeTwo">
              <span class="tableTreeTwoSpan1">表单模板</span>
              <span class="tableTreeTwoSpan2" style="text-align: right;padding-right: 132px;">
                操作
                <el-popover
                  placement="bottom"
                  width="359"
                  popper-class="popoverAll"
                  trigger="hover"
                >
                  <p>
                    <span>添加一级</span>
                    <br />添加一级选项，添加后选项默认显示在最下方
                  </p>
                  <p>
                    <span>添加下一级</span>
                    <br />添加当前选项的下一级，添加后选项默认显示在最下方
                  </p>
                  <p>
                    <span>修改</span>
                    <br />可修改选项名称及对应的模板
                  </p>
                  <p>
                    <span>删除</span>
                    <br />删除选项及子选项，请谨慎操作
                  </p>
                  <div slot="reference" style="cursor:pointer;">
                    <svg-icon class="icon-info" icon-class="info"></svg-icon>
                  </div>
                </el-popover>
              </span>
            </span>
          </div>
          <div class="tableTreeBody">
            <div class="tree-container" ref="treeContainer">
              <template v-if="!dataListNew.length">
                <div class="empty-data-container">
                  <img :src="emptyDataImgUrl" />
                </div>
              </template>
              <template v-else>
                <el-tree
                  :data="dataListNew"
                  :indent="0"
                  node-key="id"
                  default-expand-all
                  :expand-on-click-node="false"
                >
                  <el-row
                    class="custom-tree-node"
                    slot-scope="{ node, data }"
                    style="width:calc(100% - 40px)"
                  >
                    <div
                      class="tableTreeRight"
                      ref="tableTreeRight"
                      style="justify-content: flex-start;"
                    >
                      <span
                        style="flex-grow:1;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"
                        :title="data.typeName"
                      >{{ data.typeName }}</span>
                      <!--<span>{{ data.templateCode }}</span>-->
                      <span
                        :style="{width: tableTreeTwo}"
                        ref="tableTreeTwo"
                        class="tableTreeTwo"
                        style="flex-shrink:0"
                      >
                        <span
                          class="tableTreeTwoSpan1"
                          style="padding-left: 8px;"
                        >{{ data.templateName }}</span>
                        <span class="tableTreeTwoSpan2" style="text-align: right;">
                          <el-button
                            type="text"
                            size="mini"
                            @click="() => handleAdd(node, data)"
                          >添加下一级</el-button>
                          <span class="vertical"></span>
                          <el-button
                            type="text"
                            size="mini"
                            @click="() => handleEdit(node, data)"
                          >编辑</el-button>
                          <span class="vertical"></span>
                          <el-button
                            type="text"
                            size="mini"
                            @click="() => handleDelete(node, data)"
                          >删除</el-button>
                        </span>
                      </span>
                    </div>
                  </el-row>
                </el-tree>
              </template>
              <div class="addLevel">
                <span class="addLevelList" @click="handleAdd">
                  <span class="el-icon-plus"></span>添加一级选项
                </span>
              </div>
            </div>
          </div>
        </div>
        <!--<div class="submitBtnTemplate">-->
        <!--<div class="submitBtnTemplate-1">-->
        <!--<el-button size="small" @click="handerCancel">取消</el-button>-->
        <!--<el-button type="primary" size="small" @click="checkTimer(handerSave,'timer')()">保存</el-button>-->
        <!--</div>-->
        <!--</div>-->
      </template>
    </xyy-list-page>
    <!-- 点击导入按钮弹出框-开始-->
    <el-dialog
      :visible="centerDialogImport"
      :close-on-click-modal="false"
      :show-close="!uploading"
      title="提示"
      custom-class="import-box"
      top="0"
      @close="close"
      @open="open"
    >
      <span ref="content" class="import-content" v-html="content"></span>
      <dl v-show="completed" class="import-result">
        <dt>导入数据项{{ result.total }}</dt>
        <dt>成功{{ result.success }}</dt>
        <dt>失败{{ result.fail }}</dt>
      </dl>
      <el-upload
        ref="importFile"
        :with-credentials="true"
        :before-upload="beforeUpload"
        :on-progress="handleProgress"
        :on-success="handleSuccess"
        :on-error="handleError"
        :action="url"
        accept=".xls, .xlsx"
      >
        <el-button v-show="!uploading && !completed" size="small" type="primary">上传文件</el-button>
        <div slot="file" slot-scope="{file}">
          <el-progress :percentage="percent"></el-progress>
          <span class="file-name">{{ file.name }}</span>
          <el-button size="small" type="primary" @click="abortFile(file)">取消上传</el-button>
        </div>
      </el-upload>
      <el-button v-show="completed" size="small" type="primary" @click="done">确定</el-button>
    </el-dialog>

    <!-- 修改下一级弹框-开始-->
    <el-dialog title="编辑问题分类" :visible.sync="dialogFormAdd" custom-class="elDialog">
      <el-form ref="formAdd" :model="formAdd" label-width="100px" :rules="addRules">
        <el-form-item label="选项名称" prop="typeName">
          <el-input v-model="formAdd.typeName" maxlength="30"></el-input>
        </el-form-item>
        <el-form-item label="模板" prop="templateName">
          <el-select
            v-model="formAdd.templateName"
            filterable
            placeholder
            :clearable="true"
            @clear="handerClear"
            @change="handelModifyChange"
          >
            <el-option
              v-for="item in repairOrderTemp"
              :key="item.id"
              :label="item.name"
              :value="item.versionCode"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="标准处理时长" prop="standardProcessing">
          <el-input
            v-model="formAdd.standardProcessingDays"
            :maxlength="2"
            onkeyup="if(this.value<0) { value = '' } this.value=this.value.replace(/[^\d.]/g,'');"
            class="_time"
            placeholder="请输入"
          ></el-input>
          <span class="timeTip">天</span>
          <el-input
            v-model="formAdd.standardProcessingHours"
            :maxlength="2"
            onkeyup="if(this.value<0) { value = '' } this.value=this.value.replace(/[^\d.]/g,'');"
            class="_time"
            placeholder="请输入"
          ></el-input>
          <span class="timeTip">小时</span>
          <el-input
            v-model="formAdd.standardProcessingMinutes"
            :maxlength="2"
            onkeyup="if(this.value<0) { value = '' } this.value=this.value.replace(/[^\d.]/g,'');"
            class="_time"
            placeholder="请输入"
          ></el-input>
          <span class="timeTip">分钟</span>
        </el-form-item>
        <el-form-item label="临期提醒" prop="reminder">
          <el-input
            v-model="formAdd.reminderDays"
            :maxlength="2"
            onkeyup="if(this.value<0) { value = '' } this.value=this.value.replace(/[^\d.]/g,'');"
            class="_time"
            placeholder="请输入"
          ></el-input>
          <span class="timeTip">天</span>
          <el-input
            v-model="formAdd.reminderHours"
            :maxlength="2"
            onkeyup="if(this.value<0) { value = '' } this.value=this.value.replace(/[^\d.]/g,'');"
            class="_time"
            placeholder="请输入"
          ></el-input>
          <span class="timeTip">小时</span>
          <el-input
            v-model="formAdd.reminderMinutes"
            :maxlength="2"
            onkeyup="if(this.value<0) { value = '' } this.value=this.value.replace(/[^\d.]/g,'');"
            class="_time"
            placeholder="请输入"
          ></el-input>
          <span class="timeTip">分钟</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addCancel">取 消</el-button>
        <el-button type="primary" @click="addDetermine('formAdd')">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 修改下一级弹框-结束-->
    <!-- 添加问题分类弹框-开始-->
    <el-dialog title="添加问题分类" :visible.sync="dialogFormVisible" custom-class="elDialog">
      <el-form ref="formAditor" :model="formAditor" label-width="100px" :rules="addRules">
        <el-form-item label="选项名称" prop="typeName">
          <el-input v-model.trim="formAditor.typeName" maxlength="30"></el-input>
        </el-form-item>
        <el-form-item label="模板" prop="templateCode">
          <el-select
            v-model="formAditor.templateCode"
            filterable
            placeholder
            :clearable="true"
            @change="handelAddChange"
          >
            <el-option
              v-for="item in repairOrderTemp"
              :key="item.id"
              :label="item.name"
              :value="item.versionCode"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标准处理时长" prop="standardProcessing">
          <el-input
            v-model="formAditor.standardProcessingDays"
            :maxlength="2"
            onkeyup="if(this.value<0) { value = '' } this.value=this.value.replace(/[^\d.]/g,'');"
            class="_time"
            placeholder="请输入"
          ></el-input>
          <span class="timeTip">天</span>
          <el-input
            v-model="formAditor.standardProcessingHours"
            :maxlength="2"
            onkeyup="if(this.value<0) { value = '' } this.value=this.value.replace(/[^\d.]/g,'');"
            class="_time"
            placeholder="请输入"
          ></el-input>
          <span class="timeTip">小时</span>
          <el-input
            v-model="formAditor.standardProcessingMinutes"
            :maxlength="2"
            onkeyup="if(this.value<0) { value = '' } this.value=this.value.replace(/[^\d.]/g,'');"
            class="_time"
            placeholder="请输入"
          ></el-input>
          <span class="timeTip">分钟</span>
        </el-form-item>
        <el-form-item label="临期提醒" prop="reminder">
          <el-input
            v-model="formAditor.reminderDays"
            :maxlength="2"
            onkeyup="if(this.value<0) { value = '' } this.value=this.value.replace(/[^\d.]/g,'');"
            class="_time"
            placeholder="请输入"
          ></el-input>
          <span class="timeTip">天</span>
          <el-input
            v-model="formAditor.reminderHours"
            :maxlength="2"
            onkeyup="if(this.value<0) { value = '' } this.value=this.value.replace(/[^\d.]/g,'');"
            class="_time"
            placeholder="请输入"
          ></el-input>
          <span class="timeTip">小时</span>
          <el-input
            v-model="formAditor.reminderMinutes"
            :maxlength="2"
            onkeyup="if(this.value<0) { value = '' } this.value=this.value.replace(/[^\d.]/g,'');"
            class="_time"
            placeholder="请输入"
          ></el-input>
          <span class="timeTip">分钟</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editorCancel">取 消</el-button>
        <el-button type="primary" @click="editorDetermine('formAditor')">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 添加问题分类弹框-结束-->
    <!-- 确认导入模板弹框-开始-->
    <componentTree
      ref="treeConten"
      v-show="panelShow"
      :panelShow="panelShow"
      :promptAialog="promptAialog"
      :dataListNewList="dataListNewList"
      @passValue="passValue"
      @add-comment="getDate"
      @setValue="setValue"
    ></componentTree>
    <!-- 确认导入弹框-结束-->
  </div>
</template>

<script>
import emptyDataImgUrl from '@/assets/common/empty-data-table.png';
import XyyListPage from '../../components/xyy/xyy-list-page/index';
import XyyButton from '../../components/xyy/xyy-button/index';
import componentTree from './components/configurationTem.vue';
import {
  getProblemClassTree,
  getTemplate,
  judgeProcessClass,
  UploadFile,
  UploadFileFailure,
  getCheck,
  getWorkType,
  addProblemType,
  modifyProblemType,
  deleteProblemType
} from '@/api/QClassification/index';
import { TEMPLATE_PATH, TEMPLATE_GROUP } from '@/api/fields';
export default {
  name: 'configuration',
  components: { XyyButton, XyyListPage, componentTree },
  data() {
    return {
      dataListNew: [],
      dataListNewList: [],
      sourceData: [],
      panelShow: false, // 子组件的显示隐藏
      promptAialog: '', // 传递过去的工单名称
      tableTreeTwo: '',
      repairOrderTemp: [], // 工单模板下拉
      // ------------以下为导入弹框变量
      dialogFormAdd: false,
      fileList: [],
      timer: null,
      fileShow: true, // 控制提交文件按钮显示隐藏
      fileName: '', // 文件名字
      successlFail: true, // 上传成功，未上传成功的显示
      url: process.env.BASE_API + '/businessType/analysisExcel',
      uploading: false, // 上传标识
      percent: 0, // 上传进度
      completed: false, // 导入完成
      content: '',
      result: {
        total: 0,
        success: 0,
        fail: 0
      }, // 导入结果
      datas: [], // 导入数据
      downloadUrl: '', // 失败数据地址
      // ------------以下为添加问题分类变量
      formAdd: {
        typeName: '',
        templateName: '',
        templateCode: '',
        timeLimt: '',
        standardProcessingDays: '',
        standardProcessingHours: '',
        standardProcessingMinutes: '',
        reminderDays: '',
        reminderHours: '',
        reminderMinutes: ''
      },
      formType: {
        orderType: '' // 工单类型
      },
      orderTypeList: [], // 工单类型下拉选项
      rowList: [], // 存放添加后的源数据
      rowListNew: [],
      nodeList: [], // 节点存储
      currentRow: {}, // 当前节点储存
      currentRowModify: {}, // 修改节点储存
      currentRowModifyNode: {}, // 修改
      saveValueList: [],
      codeEach: 0,
      // ------------以下为修改问题分类变量--------
      dialogFormVisible: false, // 编辑问题分类 弹框控制显示隐藏
      centerDialogImport: false, // 点击导入按钮弹出框
      parentCode: '', // 传递的编号
      formAditor: {
        // 编辑问题表单
        typeName: '', // 选项名称
        typeCode: '', // 选项Code
        templateName: '', // 模板
        templateCode: '',
        standardProcessingDays: '',
        standardProcessingHours: '',
        standardProcessingMinutes: '',
        reminderDays: '',
        reminderHours: '',
        reminderMinutes: ''
      },
      removeId: [], // 存储所有删除的id
      removeCode: [], // 存储所有删除的code
      // 编辑问题分类 校验
      addRules: {
        typeName: [
          { required: true, message: '选项名称为必填项', trigger: 'blur' }
        ]
      },
      isHover: 0,
      emptyDataImgUrl
    };
  },
  created() {
    this.getDate();
    this.getWidth();
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        let treeContainer = this.$refs.treeContainer.clientWidth; // 最外边的长度
        this.tableTreeTwo = (treeContainer / 4) * 2 + 'px';
      })();
    };
  },
  methods: {
    // 点击导入按钮事件
    handerImport() {
      // 判断：当前级联的选项数据是否被设置为流程分支的判断条件
      let params = {
        formTypeId: this.formType.orderType
      };
      judgeProcessClass(params).then(res => {
        if (res.code === 1) {
          // 否 出弹层
          this.centerDialogImport = true;
          this.content = `请先<a class="DownloadTem">下载导入模板</a>，再点击上传按钮进行数据上传`;
          this.uploading = false;
          this.percent = 0;
          this.completed = false;
          this.result = {
            total: 0,
            success: 0,
            fail: 0
          };
          this.datas = [];
          this.downloadUrl = '';
        } else if (res.code === 0) {
          // 是  提示：无法进行导入操作（级联选项已作为流程分支条件，导入替换新数据时会导致现有流程异常，建议先解除绑定关系）
          this.$message({
            message:
              '无法进行导入操作（级联选项已作为流程分支条件，导入替换新数据时会导致现有流程异常，建议先解除绑定关系）'
          });
          //          this.$alert(
          //            '无法进行导入操作（级联选项已作为流程分支条件，导入替换新数据时会导致现有流程异常，建议先解除绑定关系）',
          //            '提示',
          //            {
          //              confirmButtonText: '确定',
          //              callback: action => {}
          //            }
          //          );
        }
      });
    },
    /**
     * 上传中回调
     */
    handleProgress(event, file) {
      if (file) {
        this.uploading = true;
        this.percent = Number(event.percent.toFixed(0));
        if (this.percent > 99) this.percent = 99;
      }
    },
    /**
     * 取消上传回调
     */
    abortFile(file) {
      this.$refs['importFile'].abort(file);
      this.$refs['importFile'].uploadFiles = [];
      this.uploading = false;
    },
    /**
     * 上传成功回调
     */
    handleSuccess(res, file) {
      if (res.code === 1) {
        this.percent = 100;
        this.completed = true;
        this.content = '上传完成';
        this.result = {
          total: res.data.totalNum,
          success: res.data.okNum,
          fail: res.data.failNum
        };
        this.datas = res.data.options;
        if (res.data.failNum) {
          this.downloadUrl = res.data.failUrl;
        }
        this.$refs['importFile'].uploadFiles = [];
      } else {
        this.$XyyMessage.error(res.msg);
        this.$refs['importFile'].uploadFiles = [];
      }
      this.uploading = false;
    },
    /**
     * 上传失败回调
     */
    handleError(res) {
      this.$refs['importFile'].uploadFiles = [];
      this.uploading = false;
      this.$XyyMessage.error(`文件上传失败`);
    },
    /**
     * 上传之前
     */
    beforeUpload(file) {
      this.percent = 0;
      if (file.type) {
        if (
          ![
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          ].includes(file.type)
        ) {
          this.$XyyMessage.error(`只能选择.xls或.xlsx格式的文件`);
          return false;
        }
      }
    },
    /**
     * 关闭回调
     */
    close() {
      this.centerDialogImport = false;
    },
    /**
     * 确定操作
     */
    done() {
      if (this.result.fail) {
        // 下载操作
        const url = `${process.env.BASE_API}${this.downloadUrl}`;
        this.download(url);
        this.close();
      } else {
        // 保存数据
        this.$emit('importCallback', this.datas);
        this.close();
        //          有成功的才弹出弹框
        if (this.result.success >= 1) {
          this.dataListNewList = this.datas;
          //            this.promptAialog = this.formType.orderType
          //            orderTypeList
          this.orderTypeList.forEach((item, index) => {
            if (this.formType.orderType === item.id) {
              this.promptAialog = item.name;
            }
          });
          this.panelShow = true;
          //          调用子组件的加载样式事件
          this.$refs.treeConten.getWidthInner();
        }
      }
    },
    /**
     * 下载方法
     */
    download(url) {
      const a = document.createElement('a');
      a.href = url;
      a.click();
    },
    open() {
      this.$nextTick(() => {
        const a = this.$refs['content'].children[0];
        const name = '模板.xlsx';
        const url = `${process.env.BASE_API}/fileUpload/downloadFile?originalFilename=${name}&path=${TEMPLATE_PATH}&group=${TEMPLATE_GROUP}`;
        a.href = url;
      });
    },
    // 工单类型改变事件--开始
    handerChangeType(val) {
      //        本地存储数据
      sessionStorage.setItem('treeIndex', this.formType.orderType);
      //获取相应工单类型的表单数据
      let params = {
        formTypeId: val
      };
      getProblemClassTree(params).then(res => {
        this.dataListNew = res.data;
      });
    },
    //工单类型改变事件--结束
    // ------------------------------------添加下一级事件和一级选项------------------
    // 添加下一级事件
    handleAdd(node, data) {
      this.currentRow = [];
      getTemplate().then(res => {
        this.repairOrderTemp = res.data;
      });
      // data为undefined时 为添加一级选项
      if (!data) {
        this.formAditor.typeName = '';
        this.formAditor.templateName = '';
        this.formAditor.templateCode = '';
        this.formAditor.standardProcessingDays = '';
        this.formAditor.standardProcessingHours = '';
        this.formAditor.standardProcessingMinutes = '';
        this.formAditor.reminderDays = '';
        this.formAditor.reminderHours = '';
        this.formAditor.reminderMinutes = '';
      } else {
        // 添加下一级
        //          this.formAditor.typeName = data.typeName;
        this.formAditor.typeName = '';
        // 添加下一级默认为空
        // this.formAditor.templateName = data.templateName;
        // this.formAditor.templateCode = data.templateCode;
        this.formAditor.templateName = ' ';
        this.formAditor.templateCode = ' ';
        this.formAditor.standardProcessingDays = '';
        this.formAditor.standardProcessingHours = '';
        this.formAditor.standardProcessingMinutes = '';
        this.formAditor.reminderDays = '';
        this.formAditor.reminderHours = '';
        this.formAditor.reminderMinutes = '';
        this.currentRow = data;
      }
      this.dialogFormVisible = true;
    },
    // 添加下一级change事件
    handelAddChange(val) {
      let obj = {};
      obj = this.repairOrderTemp.find(item => {
        // 这里的userList就是上面遍历的数据源
        return item.versionCode === val; // 筛选出匹配数据
      });
      this.formAditor.templateName = obj.name;
      this.formAditor.templateCode = obj.versionCode;
    },
    // 添加下一级确定事件
    editorDetermine(name) {
      let that = this;
      this.$refs[name].validate(valid => {
        if (valid) {
          //           添加一级选项 添加一级选项前 获取sort的最大值
          let maximum = [];
          let maximumObj;
          if (this.dataListNew.length > 0) {
            this.dataListNew.forEach((item, index) => {
              maximum.push(item.sort);
            });
            maximumObj = maximum.reduce((num1, num2) => {
              return num1 > num2 ? num1 : num2;
            });
          } else {
            maximumObj = 0;
          }

          if (
            (!this.formAditor.standardProcessingDays ||
              this.formAditor.standardProcessingDays <= 0) &&
            (!this.formAditor.standardProcessingHours ||
              this.formAditor.standardProcessingHours <= 0) &&
            (!this.formAditor.standardProcessingMinutes ||
              this.formAditor.standardProcessingMinutes <= 0)
          ) {
            this.$XyyMessage.warning('标准处理时长为必填项');
            return;
          }

          if (
            (!this.formAditor.reminderDays ||
              this.formAditor.reminderDays <= 0) &&
            (!this.formAditor.reminderHours ||
              this.formAditor.reminderHours <= 0) &&
            (!this.formAditor.reminderMinutes ||
              this.formAditor.reminderMinutes <= 0)
          ) {
            this.$XyyMessage.warning('临期提醒为必填项');
            return;
          }

          let obj = {};
          if (this.currentRow.length === 0) {
            obj = {
              sort: Number(maximumObj) + 1,
              typeName: that.formAditor.typeName,
              templateCode: that.formAditor.templateCode,
              templateName: that.formAditor.templateName,
              formTypeId: Number(this.formType.orderType),
              parentCode: '0',
              standardProcessingDays: this.formAditor.standardProcessingDays
                ? this.formAditor.standardProcessingDays
                : 0,
              standardProcessingHours: this.formAditor.standardProcessingHours
                ? this.formAditor.standardProcessingHours
                : 0,
              standardProcessingMinutes: this.formAditor
                .standardProcessingMinutes
                ? this.formAditor.standardProcessingMinutes
                : 0,
              reminderDays: this.formAditor.reminderDays
                ? this.formAditor.reminderDays
                : 0,
              reminderHours: this.formAditor.reminderHours
                ? this.formAditor.reminderHours
                : 0,
              reminderMinutes: this.formAditor.reminderMinutes
                ? this.formAditor.reminderMinutes
                : 0
            };
          } else {
            obj = {
              sort: that.codeEach++,
              typeName: that.formAditor.typeName,
              templateCode: that.formAditor.templateCode,
              templateName: that.formAditor.templateName,
              formTypeId: Number(this.formType.orderType),
              parentCode: this.currentRow.typeCode,
              standardProcessingDays: this.formAditor.standardProcessingDays
                ? this.formAditor.standardProcessingDays
                : 0,
              standardProcessingHours: this.formAditor.standardProcessingHours
                ? this.formAditor.standardProcessingHours
                : 0,
              standardProcessingMinutes: this.formAditor
                .standardProcessingMinutes
                ? this.formAditor.standardProcessingMinutes
                : 0,
              reminderDays: this.formAditor.reminderDays
                ? this.formAditor.reminderDays
                : 0,
              reminderHours: this.formAditor.reminderHours
                ? this.formAditor.reminderHours
                : 0,
              reminderMinutes: this.formAditor.reminderMinutes
                ? this.formAditor.reminderMinutes
                : 0
            };
          }
          this.addProblemType(obj);
        }
      });
    },
    //      添加下一级调用接口
    addProblemType(params) {
      addProblemType(params).then(res => {
        if (res.code === 1) {
          this.$message('添加成功');
          this.getDate();
          this.dialogFormVisible = false;
        } else {
          this.$message(res.msg);
        }
      });
    },
    // 添加下一级取消事件
    editorCancel() {
      this.dialogFormVisible = false;
    },

    // -----------------------修改事件

    // 修改事件
    handleEdit(node, data) {
      this.formAdd.typeName = data.typeName;
      this.formAdd.templateName = data.templateName;
      this.formAdd.templateCode = data.templateCode;

      this.formAdd.standardProcessingDays = data.standardProcessingDays;
      this.formAdd.standardProcessingHours = data.standardProcessingHours;
      this.formAdd.standardProcessingMinutes = data.standardProcessingMinutes;
      this.formAdd.reminderDays = data.reminderDays;
      this.formAdd.reminderHours = data.reminderHours;
      this.formAdd.reminderMinutes = data.reminderMinutes;

      this.dialogFormAdd = true;
      getTemplate().then(res => {
        this.repairOrderTemp = res.data;
      });
      this.currentRowModify = data;
      this.currentRowModifyNode = node;
    },
    //      编辑删除事件
    handerClear() {
      this.formAdd.templateName = '';
      this.formAdd.templateCode = '';
    },
    // 修改下一级change事件
    handelModifyChange(val) {
      let obj = {};
      obj = this.repairOrderTemp.find(item => {
        // 这里的userList就是上面遍历的数据源
        return item.versionCode === val; // 筛选出匹配数据
      });
      this.formAdd.templateName = obj.name;
      this.formAdd.templateCode = obj.versionCode;
    },
    // 修改弹框确定事件
    addDetermine(name) {
      this.$refs[name].validate(valid => {
        if (valid) {
          if (
            (!this.formAdd.standardProcessingDays ||
              this.formAdd.standardProcessingDays <= 0) &&
            (!this.formAdd.standardProcessingHours ||
              this.formAdd.standardProcessingHours <= 0) &&
            (!this.formAdd.standardProcessingMinutes ||
              this.formAdd.standardProcessingMinutes <= 0)
          ) {
            this.$XyyMessage.warning('标准处理时长为必填项');
            return;
          }

          if (
            (!this.formAdd.reminderDays || this.formAdd.reminderDays <= 0) &&
            (!this.formAdd.reminderHours || this.formAdd.reminderHours <= 0) &&
            (!this.formAdd.reminderMinutes || this.formAdd.reminderMinutes <= 0)
          ) {
            this.$XyyMessage.warning('临期提醒为必填项');
            return;
          }

          let obj = {
            sort: this.currentRowModify.sort,
            id: this.currentRowModify.id,
            typeName: this.formAdd.typeName,
            typeCode: this.currentRowModify.typeCode,
            templateCode: this.formAdd.templateCode,
            templateName: this.formAdd.templateName,
            formTypeId: Number(this.formType.orderType),
            parentCode: this.currentRowModify.parentCode,
            standardProcessingDays: this.formAdd.standardProcessingDays
              ? this.formAdd.standardProcessingDays
              : 0,
            standardProcessingHours: this.formAdd.standardProcessingHours
              ? this.formAdd.standardProcessingHours
              : 0,
            standardProcessingMinutes: this.formAdd.standardProcessingMinutes
              ? this.formAdd.standardProcessingMinutes
              : 0,
            reminderDays: this.formAdd.reminderDays
              ? this.formAdd.reminderDays
              : 0,
            reminderHours: this.formAdd.reminderHours
              ? this.formAdd.reminderHours
              : 0,
            reminderMinutes: this.formAdd.reminderMinutes
              ? this.formAdd.reminderMinutes
              : 0
          };
          // 调用编辑接口
          this.modifyProblemType(obj);
        }
      });
    },
    //      修改事件调用接口
    modifyProblemType(params) {
      modifyProblemType(params).then(res => {
        if (res.code === 1) {
          this.$message('修改成功');
          this.getDate();
          this.dialogFormAdd = false;
        } else {
          this.$message(res.msg);
        }
      });
    },
    // 修改弹框取消事件
    addCancel() {
      this.dialogFormAdd = false;
    },
    // 删除事件
    handleDelete(node, data) {
      this.$XyyMsg({
        title: '提示',
        content: '确定删除该选项？',
        onSuccess: () => {
          deleteProblemType(data.typeCode).then(res => {
            if (res.code === 1) {
              this.$message('删除成功');
              this.getDate();
            } else {
              this.$message(res.msg);
            }
          });
        }
      });
    },
    //      关闭弹框重新加载数据 子组件传递过来的 直接赋值给页面的数据即可dataListNew
    passValue(res) {
      console.log('res');
      console.log(res);
    },
    // 关闭弹框
    setValue(res) {
      this.panelShow = res;
    },
    // 获取表单数据
    async getDate() {
      //  获取工单类型下拉选项
      await getWorkType({ deleted: 0 }).then(res => {
        let orderType = this.formType.orderType;
        if (orderType === ' ' || !orderType) {
          this.formType.orderType = res.data[0].id;
        }
        this.orderTypeList = res.data;
      });
      sessionStorage.setItem('treeIndex', this.formType.orderType);
      //        获取表单数据
      let params = {
        formTypeId: this.formType.orderType
      };

      await getProblemClassTree(params).then(res => {
        this.dataListNew = res.data;
        this.sourceData = this.deepCopy(res.data);
      });
    },
    deepCopy(obj) {
      var that = this;
      var result = Array.isArray(obj) ? [] : {};
      for (var key in obj) {
        if (obj.hasOwnProperty(key)) {
          if (typeof obj[key] === 'object' && obj[key] !== null) {
            result[key] = that.deepCopy(obj[key]); // 递归复制
          } else {
            result[key] = obj[key];
          }
        }
      }
      return result;
    },
    // 获取页面div的宽度
    getWidth() {
      this.$nextTick(() => {
        let treeContainer = this.$refs.treeContainer.clientWidth; // 最外边的长度
        this.tableTreeTwo = (treeContainer / 4) * 2 + 'px';
      });
    }
  }
};
</script>
<style lang="scss">
.formTypeClass {
  /deep/.el-form-item__content {
    width: 200px;
  }
  /deep/.el-input__inner {
    height: 36px;
  }
}
.temConfiguration {
  .page-header > span {
    display: inline-block;
  }
  .el-select {
    width: 100%;
  }
  .el-popover__reference {
    width: 18px;
  }
}
.popoverAll {
  p {
    color: rgba(144, 147, 153, 1);
    font-size: 12px;
    span {
      font-size: 14px;
      color: rgba(41, 41, 51, 1);
    }
  }
}
.tableTreeBody {
  .addLevel {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #ccc;
    padding-left: 16px;
    font-size: 14px;
    color: rgba(64, 163, 184, 1);
    .addLevelList {
      cursor: pointer;
    }
  }
}
.tree-container {
  height: auto;
  .el-row::before {
    display: contents !important;
  }
  .el-tree-node:focus > .el-tree-node__content {
    background-color: #fff !important;
  }
  .el-tree-node__content {
    height: 40px;
    &:visited {
      background: #fff !important;
    }
    &:active {
      background: #fff !important;
    }
    &:hover {
      background-color: #f5f7fa;
    }
  }
}
.tableTreeTitle {
  height: 40px;
  background: rgba(238, 238, 238, 1);
  line-height: 40px;
  font-size: 14px;
}
.tree-container {
  border: none !important;
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    font-size: 14px;
    padding-right: 8px;
    span {
      /*width: 30%;*/
      display: inline-block;
    }
  }
  .el-tree-node__expand-icon.expanded {
    transform: rotate(0deg);
  }
  .el-icon-caret-right:before {
    background: url('../../assets/repeat/icon_open.svg') no-repeat;
    content: '';
    display: block;
    width: 18px;
    height: 18px;
    font-size: 18px;
    background-size: 18px;
  }
}
.tree-container /deep/ {
  .el-tree > .el-tree-node:after {
    border-top: none !important;
  }
  /** 迷之代码。。。*/
  // .el-tree-node__expand-icon {
  //   pointer-events: none;
  // }
  /** 迷之代码。。。*/

  .el-tree-node {
    position: relative;
    padding-left: 16px;
  }
  //节点有间隙，隐藏掉展开按钮就好了,如果觉得空隙没事可以删掉
  .el-tree-node__expand-icon.is-leaf:before {
    background: url('../../assets/repeat/icon_close.png') no-repeat;
    content: '';
    display: block;
    width: 18px;
    height: 18px;
    font-size: 18px;
    background-size: 18px;
  }
  .el-tree-node__children {
    padding-left: 16px;
  }

  .el-tree-node :last-child:before {
    height: 33px;
    top: -16px;
  }

  .el-tree > .el-tree-node:before {
    border-left: none;
  }

  .el-tree > .el-tree-node:after {
    border-top: none;
  }

  .el-tree-node:before {
    content: '';
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:after {
    content: '';
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:before {
    border-left: 1px dashed rgba(228, 228, 235, 1);
    bottom: 0px;
    height: 79%;
    top: -13px;
    width: 1px;
  }

  .el-tree-node:after {
    border-top: 1px dashed rgba(228, 228, 235, 1);
    height: 20px;
    top: 19px;
    width: 24px;
  }
}
.tableTree {
  border: 1px solid rgba(228, 228, 235, 1);
  border-bottom: 0;
  margin-bottom: 45px;
  .tree-container {
    //树的parent，样式自定
    width: 100% !important;
    .el-tree-node {
      border-bottom: 1px solid rgba(228, 228, 235, 1);
      padding-bottom: 12px;
      .el-tree-node__children {
        .el-tree-node {
          border: 0 !important;
          padding-bottom: 0;
        }
      }
    }
  }
}
.DownloadTem {
  color: rgba(64, 163, 184, 1);
}
.tableTreeRight {
  width: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px !important;
  padding-right: 8px;
  .el-button--mini {
    font-size: 14px !important;
  }
}
.tableTreeTwo {
  span {
    display: inline-block;
  }
  .tableTreeTwoSpan1 {
    width: 60%;
    /*margin-right: 130px;*/
    @extend span;
  }
  .tableTreeTwoSpan2 {
    width: 38%;
    .vertical {
      display: inline-block;
      width: 1px;
      height: 12px;
      background: rgba(220, 223, 230, 1);
      margin: 0 6px;
    }
    @extend span;
  }
}
.temConfiguration {
  .el-tree-node > .el-tree-node__children {
    overflow: visible;
  }
  .elDialog {
    width: 502px;
    height: 354px;
    top: 50%;
    margin-top: -242px !important;
    ._time {
      width: 74px;
      height: 36px;
    }
    .timeTip {
      font-size: 14px;
      padding-right: 12px;
      color: rgba(41, 41, 51, 1);
    }
    .el-dialog__header {
      padding-top: 15px;
    }
    .el-dialog__footer {
      padding-top: 8px;
    }
    .el-form-item {
      margin-bottom: 18px;
    }
    .el-button {
      height: 36px;
      line-height: 36px;
      padding-top: 0;
      padding-bottom: 0;
    }
    .el-dialog__body {
      padding: 0px 20px;
    }
    .el-input__inner {
      height: 36px;
    }
  }
}

.el-dialog.import-box {
  width: 400px;
  height: 240px;
  top: 50%;
  transform: translateY(-50%);
  .el-dialog__header {
    height: 50px;
    padding: 14px 20px;
    box-sizing: border-box;
    border-bottom: 1px solid rgba(238, 238, 238, 1);
    .el-dialog__title {
      height: 22px;
      font-size: 16px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: rgba(48, 49, 51, 1);
      line-height: 22px;
      float: left;
    }
    .el-dialog__headerbtn {
      top: 16px;
    }
  }
  .el-dialog__body {
    height: 190px;
    padding: 20px;
    position: relative;
    .import-content {
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(41, 41, 51, 1);
      a {
        color: #3b95a8;
      }
    }
    .import-result {
      margin: 0;
      padding: 0;
      overflow: hidden;
      dt {
        float: left;
        margin: 0 30px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(144, 147, 153, 1);
        &:first-child {
          margin-left: 0;
        }
        &:last-child {
          margin-right: 0;
        }
      }
    }
    > div {
      position: relative;
      height: 113px;
      .el-button {
        height: 36px;
        line-height: 36px;
        padding: 0 11px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        position: absolute;
        bottom: 0px;
        left: 50%;
        transform: translateX(-50%);
      }
      .el-upload-list {
        position: absolute;
        top: 18px;
        width: 100%;
        li {
          margin: 0;
          padding-top: 16px;
          height: 95px;
          .el-progress {
            height: 6px;
            top: 0px;
            .el-progress-bar {
              border-radius: 3px;
              height: 100%;
              width: calc(100% - 40px);
              float: left;
              .el-progress-bar__outer {
                height: 100% !important;
              }
            }
            .el-progress__text {
              top: 50%;
              transform: translateY(-50%);
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: rgba(144, 147, 153, 1);
            }
          }
          &:hover {
            background: none;
            .el-progress__text {
              display: inline;
            }
          }
          &:focus {
            outline: none;
          }
          span.file-name {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(144, 147, 153, 1);
          }
        }
      }
    }
    > .el-button {
      height: 36px;
      line-height: 36px;
      padding: 0 19px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      position: absolute;
      bottom: 20px;
      right: 20px;
    }
  }
}
</style>
<style scoped lang="scss">
.temConfiguration {
  .el-table__body-wrapper {
    button {
      color: #3b95a8;
      background: transparent;
      border: 0;
    }
  }
  .importClass {
    width: 74px;
    height: 36px;
    border: 1px solid rgba(228, 228, 235, 1) !important;
    color: rgba(87, 87, 102, 1) !important;
  }
}
.el-button.importClass.el-button--primary.is-plain:hover,
.el-button.importClass.el-button--primary.is-plain:focus {
  background: #fff;
  color: #40a3b8 !important;
  border: 1px solid !important;
}
.submitBtnTemplate {
  position: fixed;
  bottom: 0px;
  right: 0;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px -2px 2px 0px rgba(145, 144, 144, 0.11);
  filter: blur(0px);
  width: 100%;
  height: 57px;
  .submitBtnTemplate-1 {
    display: inline;
    position: absolute;
    right: 20px;
    top: 10px;
  }
}

//无数据样式
.empty-data-container {
  width: 100%;
  height: 460px;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 305px;
    height: 219px;
  }
}
</style>
