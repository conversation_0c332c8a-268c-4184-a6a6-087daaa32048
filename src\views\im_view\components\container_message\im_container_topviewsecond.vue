<template>
  <div>
    <div v-if="viewtype==='ing'&&isShow" class="im_user_top_view_show">
      <div class="show_body">
        <div class="rightview">
          <div v-for="(item, index) in viewData.textData" :key="index" class="rightview_item">
            <div :title="item" class="rightview_item_text">{{item}}</div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="viewtype==='ing'&&isShow===false" class="im_user_top_view_hidden">
      <div class="rightview_item_text" >{{viewData.textData[0]}}</div>
      <div class="threebutton">
        <el-button v-if="blackListAuth==='1'" size="mini" @click="addBlckList()">加入黑名单</el-button>
        <el-button size="mini" type="success" plain @click="closeContainerAction()">结束对话</el-button>
        <el-button size="mini" type="text" @click="downAction()">{{'展开 v'}}</el-button>
      </div>
    </div>
    <div v-if="viewtype==='history'" class="im_user_top_view_sendmessage">
      <div class="rightview_item_text">{{viewData.textData[0]}}</div>
    </div>
    <div v-if="viewtype==='other'" class="im_user_top_view_sendmessage">
      <div class="avataName">
        <img :src="viewData.avatar" class="avarta" />
        <div class="rightview_item_text">{{viewData.textData[0]}}</div>
      </div>
      <el-button size="mini" type="success" plain @click="startContainerAction()">发起对话</el-button>
    </div>
    <blacklistdialog :dialogFormVisible="blackAlerShow" @im_blacklist_dialog="blackCloseAction"></blacklistdialog>
  </div>
</template>

<script>
import blacklistdialog from './im_container_dialog_blacklist';
export default {
  components: {
    blacklistdialog
  },
  props: {
    blackListAuth: {
      type: String,
      default: '1'
    },
    viewData: {
      type: Object
    },
    viewtype: {
      type: String
    },
    containerId: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      isShow: true,
      blackAlerShow: false
    };
  },
  methods: {
    //收起
    upAction() {
      if (this.containerId === '0' || this.containerId === null) {
        this.$XyyMessage.error('请选择会话');
        return;
      }
      this.isShow = false;
      this.$emit('user_top_view', 'close');
    },
    // 展开
    downAction() {
      if (this.containerId === '0' || this.containerId === null) {
        this.$XyyMessage.error('请选择会话');
        return;
      }
      this.isShow = true;
      this.$emit('user_top_view', 'show');
    },
    // 加入黑名单
    addBlckList() {
      if (this.containerId === '0' || this.containerId === null) {
        this.$XyyMessage.error('请选择会话');
        return;
      }
      this.blackAlerShow = true;
    },
    //关闭黑名单
    blackCloseAction(data, hidden) {
      if (this.containerId === '0' || this.containerId === null) {
        this.$XyyMessage.error('请选择会话');
        return;
      }
      this.blackAlerShow = hidden;
      if (data === '') {
        return;
      } else {
        this.$emit('user_top_view', 'addblacklist', data);
      }
    },
    //结束聊天
    closeContainerAction() {
      if (this.containerId === '0' || this.containerId === null) {
        this.$XyyMessage.error('请选择会话');
        return;
      }
      this.$emit('user_top_view', 'closeContainer');
    },
    //开始聊天
    startContainerAction() {
      if (this.containerId === '0' || this.containerId === null) {
        this.$XyyMessage.error('请选择会话');
        return;
      }
      this.$emit('user_top_view', 'startContainer');
    }
  }
};
</script>

<style scoped>
.im_user_top_view_showbutton {
  /* height: 20px; */
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}

.im_user_top_view_show {
  width:778px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #F5F7FA;
}

.im_user_top_view_hidden {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 45px;
  background-color: white;
  border: 1px solid #dcdee3;
  padding-left: 10px;
  padding-right: 10px;
}

.im_user_top_view_sendmessage {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 81px;
  z-index: 100;
  background-color: white;
  border: 1px solid #dcdee3;
  padding-left: 10px;
  padding-right: 10px;
}

.avataName {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  /* height: 81px; */
  background-color: white;
  padding-left: 10px;
  padding-right: 10px;
}

.avarta {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  margin-right: 25px;
}

.rightview {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  line-height: 30px;
  padding:9px 0;
  background-color: #F5F7FA;
  color: #575766;
}

.rightview_item {
  width: 250px;
  padding-right: 20px;
  padding-left: 20px;
}
.rightview_item_text {
  font-size: 12px;
  color: #575766;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.twobutton {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  padding-bottom: 10px;
}

.show_body {
  display: flex;
  flex-direction: row;
}

.im_user_top_view_show .el-button--success {
  color: #3b95a8;
  background-color: #fff;
  border-color: #3b95a8;
}

.im_user_top_view_sendmessage .el-button--success {
  color: #3b95a8;
  background-color: #fff;
  border-color: #3b95a8;
}

.im_user_top_view_hidden .el-button--success {
  color: #3b95a8;
  background-color: #fff;
  border-color: #3b95a8;
}

.threebutton {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>