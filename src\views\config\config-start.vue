<template>
  <div>
    <div class="configStyel">
      <el-row class="isConfing">
        <el-radio :disabled="!isSave" v-model="isOpen" label="open">开启</el-radio>
        <el-radio :disabled="!isSave" v-model="isOpen" label="close">关闭</el-radio>
      </el-row>
      <el-select
        v-model="configVal"
        :disabled="isSave &&isOpen==='open'?false:true"
        placeholder="请选择"
        class="input_sheetType"
      >
        <el-option v-for="item in configArray" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-row class="configTip">小时内，同一订单编号、问题分类的工单，发起时提示</el-row>
      <el-button v-if="isSave" type="primary" @click="checkTimer(saveConfig,'timer')()">保存</el-button>
      <el-button v-if="!isSave" type="primary" @click="checkTimer(modifyCofig,'timer')()">修改</el-button>
    </div>
  </div>
</template>

<script>
import {
  getRepeateCheckSetting,
  updateRepeateCheckSetting
} from '@/api/mySheetManage';
export default {
  name: 'ConfigStart',
  data() {
    return {
      isSave: false,
      id: '',
      isOpen: 'open',
      configVal: '',
      configArray: [
        { id: 12, name: 12 },
        { id: 24, name: 24 },
        { id: 36, name: 36 },
        { id: 72, name: 72 }
      ],
      timer: null
    };
  },
  mounted() {
    const that = this;
    getRepeateCheckSetting().then(res => {
      if (res.code) {
        that.configVal = res.data.detectionDuration;
        that.id = res.data.id;
        res.data.openState === 1
          ? (that.isOpen = 'close')
          : (that.isOpen = 'open');
      }
    });
  },
  methods: {
    modifyCofig() {
      this.isSave = true;
    },
    saveConfig() {
      const that = this;
      const param = {
        detectionDuration: parseInt(this.configVal),
        id: this.id,
        openState: this.isOpen === 'close' ? 1 : 0
      };
      updateRepeateCheckSetting(param).then(res => {
        console.log(res);
        if (res.code) {
          that.isSave = false;
          that.$XyyMessage.success('设置成功！');
        } else {
          that.$XyyMessage.error(res.msg);
        }
      });
    }
  }
};
</script>
<style lang="scss">
</style>
<style scoped lang="scss">
.configStyel {
  width: 430px;
  margin-left: 50%;
  margin-top: 234px;
  transform: translate(-50%);
  .isConfing {
    padding-bottom: 20px;
  }
  .input_sheetType {
    padding-bottom: 8px;
    width: 430px;
  }
  .configTip {
    color: #909399;
    font-size: 14px;
    padding-bottom: 20px;
  }
}
</style>

