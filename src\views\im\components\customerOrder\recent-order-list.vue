<template>
  <div class="recent-order-list-component-container">
    <ul class="order-list">
      <li v-for="item in orderList" :key="item.orderNo">
        <header class="box row-between column-center">
          <p>
            <em>订单编号:</em>&nbsp;
            <span>{{ item.orderNo }}</span>
          </p>
          <i :class="'status-'+item.status">{{ item.statusName }}</i>
        </header>
        <div class="box row-start order-list-wrapper">
          <a class="box row-center img" href="javascript:void(0);">
            <img :src="item.imageUrl?item.imageUrl:defaultUrl" :alt="item.merchantName" />
          </a>
          <div class="detail">
            <header class="box row-between column-start">
              <h2 class="ellipsis">
                <el-tooltip
                  :content="item.merchantName"
                  class="item"
                  effect="dark"
                  placement="top-start"
                >
                  <span>{{ item.merchantName }}</span>
                </el-tooltip>
              </h2>
              <el-button
                type="text"
                style="width: 42px; font-size: 12px;"
                @click="checkProductDetail(item)"
              >
                查看
                <i class="el-icon-arrow-right" style="padding: 2px 2px;"></i>
              </el-button>
            </header>
            <time>{{ item.createTime|formatTime }}</time>
            <p class="box row-start">
              <span>共{{ item.varietyNum }}种商品</span>
              <span>
                实付总额：¥
                <i>{{ item.money }}</i>
              </span>
            </p>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: '',
  components: {},
  filters: {},
  props: {
    orderList: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      defaultUrl: '/css/images/default.png',
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    /**
     * 查看订单详情
     */
    checkProductDetail(order) {
      order && window.open(order.orderDetailUrl, '_blank');
    },
  },
};
</script>

<style lang="scss" scoped>
@import '../../../../styles/custome-order.css';
.recent-order-list-component-container {
  width: 100%;

  .order-list li:first-child {
    margin-top: 0;
  }

  .order-list li:last-child {
    margin-bottom: 0;
  }
}
</style>