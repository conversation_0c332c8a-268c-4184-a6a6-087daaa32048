<template>
  <!-- 开票信息弹窗 -->
  <el-dialog
    class="dialog-preview-product"
    width="960px"
    append-to-body
    destroy-on-close
    :show-close="false"
    :visible.sync="dialogVisible"
    v-if="dialogVisible"
  >
    <div class="dialog-body">
      <div class="dialog-title">
        <span class="title-txt">请选择商品</span>
        <i class="el-icon-close" @click="handleCloseDialog"></i>
      </div>
      <div class="dialog-content">
        <xyy-table
          v-loading="loading"
          has-selection
          :data="list"
          :col="col"
          @selectionCallback="selectionCallback"
        ></xyy-table>
      </div>
      <div class="dialog-footer">
        <el-button class="btn-default" @click="handleCloseDialog"
          >关闭</el-button
        >
        <el-button class="btn-primary" @click="handleSubmitDialog"
          >确定</el-button
        >
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { queryProductListByOrderNo } from '@/api/mySheetManage';
export default {
  name: 'dialog-preview-product',
  data() {
    return {
      dialogVisible: false,

      loading: false,
      col: [
        {
          index: 'barcode',
          name: '商品编号',
          width: 120
        },
        {
          index: 'productName',
          name: '商品名称',
          width: 220
        },
        {
          index: 'packageId',
          name: '套餐编号',
          width: 120
        },
        {
          index: 'packageCount',
          name: '套餐数量',
          width: 120
        },
        {
          index: 'manufacturer',
          name: '生产厂家',
          width: 220
        },
        {
          index: 'spec',
          name: '规格',
          width: 120
        },
        {
          index: 'productPrice',
          name: '商品原价',
          width: 120
        },
        {
          index: 'purchasePrice',
          name: '商品购买单价',
          width: 120
        },
        {
          index: 'nearEffect',
          name: '近效期至',
          width: 120
        },
        {
          index: 'farEffect',
          name: '远效期至',
          width: 120
        },
        {
          index: 'productAmount',
          name: '商品数量',
          width: 120
        },
        {
          index: 'realSendNum',
          name: '实际出库数量',
          width: 120
        }
      ],
      orderInfo: {}, // 订单相关信息
      list: [], // 订单商品信息
      listSelected: []
    };
  },
  methods: {
    init({ orderNo }) {
      this.loading = false;
      this.orderInfo = {};
      this.list = [];
      this.listSelected = [];
      this.dialogVisible = true;
      this.queryDataList({ orderNo });
    },

    // 查询列表信息
    async queryDataList({ orderNo }) {
      try {
        this.loading = true;
        const { code, data, msg } = await queryProductListByOrderNo({
          orderNo
        });
        if (code == 1) {
          this.orderInfo = data;
          if (Array.isArray(data.productList) && data.productList.length) {
            this.list = data.productList.map(productItem => {
              return {
                ...productItem,
                id: productItem.productCode
              };
            });
          }
        } else {
          throw new Error(msg);
        }
      } catch (e) {
        this.$XyyMessage.error(e.message);
      } finally {
        this.loading = false;
      }
    },

    // 选中商品
    selectionCallback(data) {
      this.listSelected = data;
      console.log(this.listSelected);
    },

    // 提交商品
    handleSubmitDialog() {
      /**
      if(!this.listSelected.length) {
        this.$XyyMessage.error('请选择商品');
        return
      }
      */

      if (Object.keys(this.orderInfo).length) {
        this.$emit('submit', {
          ...this.orderInfo,
          productList: this.listSelected
        });
      }
      this.$nextTick(() => {
        this.dialogVisible = false;
      });
    },

    // 关闭弹窗
    handleCloseDialog() {
      this.dialogVisible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-preview-product {
  /deep/ {
    .el-dialog {
      border-radius: 4px;

      .el-dialog__header {
        display: none;
      }

      .el-dialog__body {
        width: 100%;
        padding: 20px;
        box-sizing: border-box;

        .dialog-body {
          width: 100%;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: stretch;

          .dialog-title {
            width: 100%;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .title-txt {
              flex-grow: 1;
              color: #222222;
              font-size: 16px;
              font-weight: 400;
            }

            .el-icon-close {
              flex-shrink: 0;
              cursor: pointer;
              font-size: 16px;
              font-weight: bold;

              &:hover {
                opacity: 0.6;
              }
            }
          }

          .dialog-content {
            width: 100%;
            margin-bottom: 20px;
          }

          .dialog-footer {
            width: 100%;
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .el-button {
              padding: 9px 18px;
              border-radius: 4px;
              font-size: 14px;
              font-weight: 500;

              &:hover {
                opacity: 0.6;
              }

              &:active {
                opacity: 1;
              }

              + .el-button {
                margin-left: 16px;
              }

              &.btn-primary {
                color: #ffffff;
                background-color: #3b95a8;
                border-color: #3b95a8;
              }

              &.btn-default {
                color: #222222;
                background-color: #fff;
                border-color: #d4d9dd;
              }
            }
          }
        }
      }
    }
  }
}
</style>
