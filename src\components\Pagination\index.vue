<template>
  <div class="pagination-container">
    <el-pagination
      :background="background"
      :current-page.sync="currentPage"
      :layout="layout"
      :total="total"
      v-bind="$attrs"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
// import { scrollTo } from '@/utils/scroll-to';

export default {
  name: 'Pagination',
  props: {
    total: {
      required: true,
      type: Number
    },
    page: {
      type: Number,
      default: 1
    },
    limit: {
      type: Number,
      default: 20
    },
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    background: {
      type: Boolean,
      default: true
    },
    autoScroll: {
      type: Boolean,
      default: true
    },
    hidden: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    currentPage: {
      get() {
        return this.page;
      },
      set(val) {
        this.$emit('update:page', val);
      }
    },
    pageSize: {
      get() {
        return this.limit;
      },
      set(val) {
        this.$emit('update:limit', val);
      }
    }
  },
  methods: {
    handleSizeChange(val) {
      this.$emit('pagination', { page: this.currentPage, limit: val });
      if (this.autoScroll) {
        // scrollTo(0, 800);
      }
    },
    handleCurrentChange(val) {
      this.$emit('pagination', { page: val, limit: this.pageSize });
      if (this.autoScroll) {
        // scrollTo(0, 800);
      }
    }
  }
};
</script>

<style lang="scss">
.pagination-container {
  margin-top: 20px;
  text-align: right;
  .el-pagination {
    position: relative;
    padding: 0;
    .el-pagination__total {
      position: absolute;
      top: 2px;
      left: 5px;
      height: 32px;
      line-height: 32px;
      font-size: 14px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      color: #575766;
    }
    @mixin reset($h: 32px, $fs: 14px) {
      height: $h;
      line-height: $h;
      font-size: $fs;
      font-family: Helvetica;
      font-weight: 400;
    }
    span,
    button {
      @include reset;
    }
    .el-pager {
      li {
        @include reset;
      }
    }
    .el-input--mini .el-input__inner {
      @include reset;
    }
    .el-pagination__editor.el-input .el-input__inner {
      @include reset;
    }
    .btn-prev,
    .btn-next,
    .el-pager li {
      background: #fff;
      width: 32px;
      height: 32px;
      border-radius: 2px;
      border: 1px solid #E4E4EB;
    }
    .el-pagination__editor{
      margin: 0 9px;
    }
  }
}
</style>
