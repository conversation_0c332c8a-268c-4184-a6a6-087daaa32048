<template>
  <div class="item-content">
    <el-card class="box-card" shadow="never">
      <!-- <div slot="header" class="clearfix">
        <span class="titlefont">全员监控</span>
        <div class="info-container">
          <info :info="info"></info>
        </div>
      </div>-->
      <header class="box row-between">
        <div class="box row-start column-center left">
          <h3>全员监控</h3>
          <info :info="info"></info>
        </div>
      </header>
      <el-row>
        <el-col :md="6">
          <div class="item">
            <div class="img">
              <svg-icon class="iconsvg" icon-class="monitor_today_click_total"></svg-icon>
            </div>
            <div class="right">
              <div class="count">{{ data.todayClickTotal }}</div>
              <div class="normal">点击量（次）</div>
            </div>
          </div>
        </el-col>
        <el-col :md="6">
          <div class="item">
            <div class="img">
              <svg-icon class="iconsvg" icon-class="monitor_today_join_kefu_total"></svg-icon>
            </div>
            <div class="right">
              <div class="count">{{ data.todayJoinKefuTotal }}</div>
              <div class="normal">访问量（次）</div>
            </div>
          </div>
        </el-col>
        <el-col :md="6">
          <div class="item">
            <div class="img">
              <svg-icon class="iconsvg" icon-class="monitor_total_chatting_dialog"></svg-icon>
            </div>
            <div class="right">
              <div class="count">{{ data.totalChattingDialog }}</div>
              <div class="normal">当前总通话数（次）</div>
            </div>
          </div>
        </el-col>
        <el-col :md="6">
          <div class="item">
            <div class="img">
              <svg-icon class="iconsvg" icon-class="monitor_today_success_chat"></svg-icon>
            </div>
            <div class="right">
              <div class="count">{{ data.todaySuccessChatDialogTotal }}</div>
              <div class="normal">今日总会话数（次）</div>
            </div>
          </div>
        </el-col>
        <el-col :md="6">
          <div class="item">
            <div class="img">
              <svg-icon class="iconsvg" icon-class="monitor_queue_dialog_total"></svg-icon>
            </div>
            <div class="right">
              <div class="count">{{ data.queueDialogTotal }}</div>
              <div class="normal">当前总排队数（人）</div>
            </div>
          </div>
        </el-col>
        <el-col :md="6">
          <div class="item">
            <div class="img">
              <svg-icon class="iconsvg" icon-class="monitor_kefu_pick_uprate"></svg-icon>
            </div>
            <div class="right">
              <div class="count">{{ data.kefuPickUpRate }}</div>
              <div class="normal">人工接起率（%）</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'StaticsItem',
  data() {
    return {
      info: [
        {
          title: '点击量（次）',
          info: '当日点击在线客服入口的总点击量；'
        },
        {
          title: '访问量（次）',
          info: '访客进线次数总和；'
        },
        { title: '当前总通话数（次）', info: '当前处于会话中的会话数总和；' },
        {
          title: '今日总会话数（次）',
          info: '当日访客主动进线成功的会话数总和；'
        },
        {
          title: '当前总排队数（人）',
          info: '当前排队的访客数总和；'
        },
        {
          title: '人工接起率（%）',
          info: '接起率=今日总会话数/访问量*100%；'
        }
      ],
      data: {
        // visitCount: 0,
        // callCount: 0,
        // waitCount: 0,
        // personCount: 0
        kefuPickUpRate: '', //当天客服人工接起率 接起率=今日总会话数/访问量*100%
        queueDialogTotal: 0, //当前排队总会话数
        todayClickTotal: 0, //当天客服用户端点击量
        todayJoinKefuTotal: 0, //当天用户端进线数（访问量）
        todaySuccessChatDialogTotal: 0, //当天进线成功总会话数（排除客服发起的会话）
        totalChattingDialog: 0 //当前通话中总会话数(排除客服发起的会话)
      }
    };
  },
  mounted() {},
  methods: {
    initData(arr) {
      this.data['waitCount'] = 0;
      this.data['callCount'] = 0;
      arr[1].forEach(item => {
        this.data.waitCount += parseInt(item);
      });
      arr[2].forEach(item => {
        this.data.callCount += parseInt(item);
      });
      // this.data.visitCount = this.data.waitCount + this.data.callCount;
    },
    setPersonCount(data) {
      // this.data['visitCount'] = 0;
      // this.data['personCount'] = 0;
      // this.data.visitCount = data.data.totalVisits;
      // this.data.personCount = data.data.pickUpRate;
      this.data.kefuPickUpRate = data.data.kefuPickUpRate;
      this.data.queueDialogTotal = data.data.queueDialogTotal;
      this.data.todayClickTotal = data.data.todayClickTotal;
      this.data.todayJoinKefuTotal = data.data.todayJoinKefuTotal;
      this.data.todaySuccessChatDialogTotal =
        data.data.todaySuccessChatDialogTotal;
      this.data.totalChattingDialog = data.data.totalChattingDialog;
    }
  }
};
</script>
<style>
.el-card__body {
  padding-right: 0;
}
</style>
<style lang="scss" scoped>
.item-content {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(48, 49, 51, 1);
  .item {
    // height: 101px;
    padding: 24px;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.08);
    border-radius: 2px;
    padding: 14px 8px;
    margin-right: 20px;
    display: flex;
    img {
      vertical-align: super;
      margin-right: 16px;
    }
    .right {
      margin-left: 20px;
      display: inline-block;
      .count {
        font-size: 24px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        line-height: 33px;
        color: rgba(48, 49, 51, 1);
      }
      .normal {
        line-height: 22px;
      }
      .des {
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(174, 174, 191, 1);
        line-height: 13px;
      }
    }
  }
  .iconsvg {
    width: 50px;
    height: 50px;
  }
}
</style>>

