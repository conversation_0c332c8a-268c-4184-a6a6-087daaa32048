<template>
  <div>
    <el-dialog :visible="status"
               title="留言记录"
               custom-class="comment-detail-box"
               top="0"
               @close="close">
      <div class="user-info">
        <span class="picture-box">
          <img :src="data.customerData.customHeadImg?data.customerData.customHeadImg:path" />
        </span>
        <span class="user-name">{{ data.customerData.customName }}（{{ data.customerData.referer }}）</span>
        <span class="comment-time">{{ data.customerData.createTime }}</span>
      </div>
      <div class="comment-content">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="留言内容"
                       name="content">
            <chatRecordChild :get-data-list="data.messages"></chatRecordChild>
          </el-tab-pane>
          <el-tab-pane label="访客信息"
                       name="user">
            <ul v-if="data.customerData">
              <li>
                <label class="info-label">渠道类型：</label>
                <span class="info-value">{{ data.customerData.referer }}</span>
              </li>
              <li v-if="data.customerData.referer.search('微信')>=0">
                <label class="info-label">微信昵称：</label>
                <span class="info-value">{{ data.customerData.nickName }}</span>
              </li>
              <li>
                <label class="info-label">客户名称：</label>
                <span class="info-value">{{ data.customerData.customName }}</span>
              </li>
              <li>
                <label class="info-label">所在地区：</label>
                <span class="info-value">{{ data.customerData.city }}</span>
              </li>
            </ul>
          </el-tab-pane>
          <el-tab-pane :disabled="!data.serviceData.length"
                       label="服务总结"
                       name="service">
            <ul>
              <li v-for="el in data.serviceData"
                  :key="el.id">
                <label class="info-label">{{ el.fieldLable }}：</label>
                <span class="info-value">{{ el.labelValue }}</span>
              </li>
            </ul>
          </el-tab-pane>
        </el-tabs>
      </div>
      <span slot="footer"
            class="dialog-footer">
        <el-button plain
                   @click="close">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import picture from '@/assets/common/user-avatar.png';
import chatRecordChild from '@/components/im/chatRecord';
import dateFormatObj from '@/utils/filter.js';
export default {
  filters: {
    dateFormat (val) {
      return dateFormatObj.dataTime(val, 'yy-mm-dd HH:ss:nn');
    }
  },
  components: {
    chatRecordChild
  },
  props: {
    status: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => {
        return { messages: [], customerData: '', serviceData: [] };
      }
    }
  },
  data () {
    return {
      path: picture,
      activeTab: 'content'
    };
  },
  watch: {
    status (val) {
      if (val) {
        this.activeTab = 'content';
      }
    }
  },
  methods: {
    close () {
      this.$emit('update:status', false);
    }
  }
};
</script>

<style lang="scss">
.el-dialog.comment-detail-box {
  width: 850px;
  top: 50%;
  transform: translateY(-50%);
  .el-dialog__header {
    padding: 15px 20px;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: rgba(41, 41, 51, 1);
    .el-dialog__headerbtn {
      top: 17px;
    }
    border-bottom: 1px solid #f1f1f5;
  }
  .el-dialog__body {
    padding: 20px;
    div.user-info {
      height: 36px;
      line-height: 36px;
      margin-bottom: 20px;
      span {
        float: left;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
      }
      span.picture-box {
        width: 36px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 9px;
        img {
          width: 100%;
        }
      }
      span.user-name {
        margin-right: 24px;
      }
    }
    div.comment-content {
      border-radius: 2px;
      border: 1px solid rgba(228, 228, 235, 1);
      .el-tabs {
        .el-tabs__header {
          margin-bottom: 0;
          .el-tabs__nav-scroll {
            padding: 0 20px;
          }
          .el-tabs__nav-wrap::after {
            background: #e4e4eb;
          }
        }
        .el-tabs__content {
          height: 280px;
          padding: 20px;
          overflow-y: auto;
          /deep/ul {
            margin: 0;
            padding: 0;
            li {
              margin: 0 0 15px;
              overflow: hidden;
              div.date-content {
                height: 20px;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: rgba(230, 162, 60, 1);
                line-height: 20px;
              }
              div.message-content {
                min-height: 20px;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: rgba(87, 87, 102, 1);
                line-height: 20px;
              }
              label.info-label {
                height: 20px;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: rgba(41, 41, 51, 1);
                line-height: 20px;
                float: left;
              }
              span.info-value {
                height: 20px;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: rgba(87, 87, 102, 1);
                line-height: 20px;
                float: left;
              }
            }
          }
        }
      }
    }
  }
  .el-dialog__footer {
    height: 56px;
    padding: 0 20px;
    box-sizing: border-box;
    .el-button {
      padding: 0 20px;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      font-weight: 400;
      font-family: PingFangSC-Regular, PingFang SC;
      &.el-button--default {
        border: 1px solid rgba(228, 228, 235, 1);
        color: rgba(87, 87, 102, 1);
        &:hover,
        &:focus {
          border: 1px solid rgba(228, 228, 235, 1);
          color: rgba(87, 87, 102, 1);
        }
      }
    }
  }
}
</style>
