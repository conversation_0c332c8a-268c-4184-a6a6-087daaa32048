import request from '@/utils/request';
import { formData } from '@/utils/index';
/**
 * 获取字段数据
 * @param {字段类型} fieldType
 */
export function getFieldDatas(fieldType, fieldName) {
  return request({
    url: '/field/listCustomField',
    method: 'get',
    params: {
      fieldType,
      fieldName,
      currentState: 0
    }
  });
}

/**
 * 获取基础信息数据
 * @param {模板id} id
 */
export function getBaseData(id) {
  return request({
    url: '/template/base/getTemplateById',
    method: 'get',
    params: {
      id
    }
  });
}

/**
 * 基础信息保存
 * @param {表单数据} data
 */
export function saveBaseData(data) {
  return request({
    url: '/template/base/save',
    method: 'post',
    data: formData(data)
  });
}
/**
 * 基础信息修改
 * @param {表单数据} data
 */
export function updateBaseData(data) {
  return request({
    url: '/template/base/update',
    method: 'put',
    data: formData(data)
  });
}
/**
 * 获取模板字段数据
 */
export function getTemplateData(params) {
  return request({
    url: '/template/field/listFieldByTemplateId',
    method: 'get',
    params
  });
}
/**
 * 获取模板字段权限
 */
export function getAuthFieldDatas(params) {
  return request({
    url: '/templateFieldAuth/listTemplateFieldAuth',
    method: 'get',
    params
  });
}
/**
 * 保存权限数据
 */
export function saveAuthFieldDatas(data) {
  return request({
    url: '/templateFieldAuth/saveTemplateFieldAuth',
    method: 'post',
    data
  });
}
/**
 * 更新权限数据
 */
export function updateAuthFieldDatas(data, sure, hasDuplicate) {
  return request({
    url: `/templateFieldAuth/updateTemplateFieldAuth?sure=${sure}&hasDuplicate=${hasDuplicate}`,
    method: 'put',
    data
  });
}
/**
 * 模板数据保存
 * @param {模板字段数据} data
 */
export function saveTemplateData(data, layout) {
  return request({
    url: '/template/field/saveList?layout=' + layout,
    method: 'post',
    data: data
  });
}

/**
 * 模板数据修改
 * @param {模板字段数据} data
 */
export function updateTemplateData(data, layout, templateId, sure) {
  return request({
    url: `/template/field/updateList?layout=${layout}&templateId=${templateId}&sure=${sure}`,
    method: 'put',
    data: data
  });
}
/**
 * 获取模板列表
 * @param {分页数据} page
 */
export function getTemplateList(page) {
  return request({
    url: '/template/base/listTemplateByFuzzySearch',
    method: 'get',
    params: page
  });
}
/**
 * 改变模板状态
 * @param {状态数据} data
 */
export function changeTemplateStatus(data) {
  return request({
    url: '/template/base/changeStatus',
    method: 'put',
    data: formData(data)
  });
}
/**
 * 复制模板数据
 * @param {模板id} id
 */
export function copyTemplateData(id) {
  return request({
    url: '/template/base/copyTemplate',
    method: 'post',
    data: formData({ templateId: id })
  });
}
/**
 * 删除模板数据
 * @param {模板id} id
 */
export function delTemplateData(id) {
  return request({
    url: '/template/base/delete',
    method: 'delete',
    data: formData({ id })
  });
}
/**
 * 校验字段是否被引用
 * @param {字段id} fieldId
 * @param {模板id} templateId
 */
export function validateField(fieldId, templateId) {
  return request({
    url: '/template/field/listFormForFieldAsCondition',
    method: 'get',
    params: {
      fieldId,
      templateId
    }
  });
}
/**
 * 批量启用
 * @param {模板id数组} ids
 */
export function enableTemplateDatas(ids) {
  return request({
    url: '/template/base/batchEnableTemplate',
    method: 'put',
    data: ids
  });
}
/**
 * 批量禁用
 * @param {模板id数组} ids
 */
export function disableTemplateDatas(ids) {
  return request({
    url: '/template/base/batchDisableTemplate',
    method: 'put',
    data: ids
  });
}

// 字段类型
export const FIELD_TYPES = [
  { value: -1, label: '全部' },
  { value: 0, label: '单行输入' },
  { value: 1, label: '多行输入' },
  { value: 2, label: '下拉列表' },
  { value: 3, label: '单选' },
  { value: 4, label: '多选' },
  { value: 5, label: '日期' },
  { value: 6, label: '级联' },
  { value: 7, label: '省市区' },
  { value: 8, label: '电话' },
  { value: 9, label: '邮箱' },
  { value: 10, label: '附件' }
];
