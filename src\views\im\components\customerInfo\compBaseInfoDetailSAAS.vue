<template>
  <div class="info-detail-component-container">
    <!-- 药店名 -->
    <div class="header-content">
      <span class="shop-name">{{ customer.realName }}</span>
      <template v-if="showBind">
        <el-button
          type="primary"
          size="mini"
          plain
          @click="handlBindBtnClick"
        >绑定</el-button
        >
      </template>
    </div>
    <!-- 详情信息 -->
    <div class="detail-section">
      <div class="item-info whole-line">
        <span class="info-field">药店名称：</span>
        <span class="info-value">{{ customer.realName }}</span>
      </div>
      <div class="item-info whole-line">
        <span class="info-field" style="width:98px;">智慧脸账号：</span>
        <span class="info-value">{{ customer.account }}</span>
      </div>
      <!-- <div class="item-info whole-line">
        <span class="info-field" style="width:98px;">注册手机号码：</span>
        <span class="info-value" style="width:calc(100% - 98px);">{{ customer.mobile }}</span>
      </div>-->
      <div class="item-info whole-line">
        <span class="info-field">地址：</span>
        <span class="info-value">{{ customer.registAddress }}</span>
      </div>
      <div class="item-info whole-line">
        <span class="info-field">智慧脸ID：</span>
        <span class="info-value">{{ customer.organSign }}</span>
      </div>
      <div class="item-info whole-line">
        <span class="info-field">客户省份：</span>
        <span class="info-value">{{ customer.province }}</span>
      </div>
      <div class="item-info whole-line">
        <span class="info-field" style="width:98px;">客户负责人：</span>
        <span class="info-value" style="width:calc(100% - 98px);">{{
          customer.sysRealName
        }}</span>
      </div>
      <div class="item-info whole-line">
        <span class="info-field">销售电话：</span>
        <span class="info-value">{{ customer.mobile }}</span>
      </div>
      <div class="item-info whole-line">
        <span class="info-field">软件费用：</span>
        <span class="info-value">{{
          customer.contractVO && customer.contractVO.softAmount
        }}</span>
      </div>
      <div class="item-info whole-line">
        <span class="info-field" style="width:98px;">合同开始日期：</span>
        <span class="info-value" style="width:calc(100% - 98px);">{{
          customer.contractVO && customer.contractVO.startTime
        }}</span>
      </div>
      <div class="item-info whole-line">
        <span class="info-field" style="width:98px;">合同结束日期：</span>
        <span class="info-value" style="width:calc(100% - 98px);">{{
          customer.contractVO && customer.contractVO.endTime
        }}</span>
      </div>
      <div class="item-info whole-line">
        <span class="info-field">合同类型：</span>
        <span class="info-value">{{
          customer.contractVO && customer.contractVO.conType == 1
            ? '主合同'
            : customer.contractVO && customer.contractVO.conType == 2
              ? '补充协议'
              : '无'
        }}</span>
      </div>
      <div class="item-info whole-line">
        <span class="info-field">合同年限：</span>
        <span class="info-value">{{
          customer.contractVO && customer.contractVO.term
        }}</span>
      </div>
      <div class="item-info whole-line">
        <span class="info-field">达标规则：</span>
        <span class="info-value">{{ getStandardRule() }}</span>
      </div>
      <div class="item-info whole-line">
        <span class="info-field">实施时间：</span>
        <span class="info-value">{{
          customer.contractVO && customer.contractVO.implementationTime
        }}</span>
      </div>
      <div class="item-info whole-line">
        <span class="info-field">软件类别：</span>
        <span class="info-value">{{
          customer.contractVO && customer.contractVO.softCategory
        }}</span>
      </div>
      <div class="item-info whole-line">
        <span class="info-field">赠品类别：</span>
        <span class="info-value">{{
          customer.contractVO && customer.contractVO.giftCategory
        }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { bindOrCancelUser } from '@/api/im_view/customeInfo';
export default {
  name: '',
  components: {},
  filters: {},
  props: {
    customer: {
      type: Object,
      default: () => {}
    },
    showBind: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      standardRuleStatus: ''
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    /**
     * 绑定
     */
    handlBindBtnClick() {
      if (this.$store.getters.containerid && this.customer.id) {
        bindOrCancelUser({
          dialogId: this.$store.getters.containerid,
          uid: this.customer.id
        }).then(resp => {
          if (resp.code === 1) {
            this.$parent.emitCustomerBind(resp.data);
          } else {
            this.$XyyMessage.warning(resp.msg);
          }
        });
      } else {
        this.$XyyMessage.error('请选择会话');
      }
    },
    getStandardRule() {
      if (
        this.customer.contractVO &&
        this.customer.contractVO.standardRule == 1
      ) {
        this.standardRuleStatus = '1.0-鲮鲤智慧脸合同达标规则';
      } else if (
        this.customer.contractVO &&
        this.customer.contractVO.standardRule == 2
      ) {
        this.standardRuleStatus = '2.0-鲮鲤智慧脸合同达标规则';
      } else if (
        this.customer.contractVO &&
        this.customer.contractVO.standardRule == 3
      ) {
        this.standardRuleStatus = '3.0-鲮鲤智慧脸合同达标规则';
      } else if (
        this.customer.contractVO &&
        this.customer.contractVO.standardRule == 4
      ) {
        this.standardRuleStatus = '4.0-鲮鲤智慧脸合同达标规则';
      } else if (
        this.customer.contractVO &&
        this.customer.contractVO.standardRule == 5
      ) {
        this.standardRuleStatus = '5.0-鲮鲤智慧脸合同达标规则';
      } else if (
        this.customer.contractVO &&
        this.customer.contractVO.standardRule == 6
      ) {
        this.standardRuleStatus = '6.0-鲮鲤智慧脸合同达标规则';
      } else {
        this.standardRuleStatus = '不返券';
      }
      return this.standardRuleStatus;
    }
  }
};
</script>

<style lang="scss" scoped>
.info-detail-component-container {
  width: 100%;

  .header-title {
    width: 100%;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .header-content {
    width: 100%;
    padding: 18px 0 18px;
    border-bottom: 1px dashed #dcdee3;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .shop-name {
      color: #303133;
      font-size: 15px;
      font-weight: 600;
    }
  }

  .detail-section {
    width: 100%;
    padding: 20px 0 0;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;

    .item-info {
      width: 49%;
      line-height: 20px;
      margin-bottom: 10px;
      flex-grow: 0;
      flex-shrink: 0;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;

      &:nth-child(even) {
        margin-left: 2%;
      }

      .info-field {
        width: 70px;
        color: #292933;
        font-size: 14px;
        font-weight: 500;
      }

      .info-value {
        width: calc(100% - 70px);
        color: #575766;
        font-size: 14px;
        font-weight: 500;
      }

      .light {
        color: #67c23a;
      }

      &.whole-line {
        width: 100%;
        &:nth-child(even) {
          margin-left: 0;
        }
      }
    }
  }

  .detail-bank-section {
    width: 100%;

    .item-bank {
      width: 100%;
      margin-bottom: 10px;

      .info-field {
        width: 70px;
        color: #292933;
        font-size: 14px;
        font-weight: 500;
      }

      .info-value {
        width: calc(100% - 70px);
        color: #575766;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}
</style>
