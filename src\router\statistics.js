import Layout from '@/views/layout/Layout';
export default [
  {
    path: '/report',
    component: Layout,
    name: 'report',
    redirect: '/report',
    meta: {
      title: '工单',
      icon: 'report',
      affix: true,
      code: 'menu:cs:total',
      mark: 'statistics'
    },
    children: [
      {
        path: 'gdnum',
        name: 'gdnum',
        meta: {
          title: '工单量相关',
          code: 'menu:cs:workloadtotal',
          mark: 'statistics',
          componentName: 'gdnum'
        },
        component: () => import('@/views/dotmanager/gdnum')
      },
      {
        path: 'Qclassification',
        name: 'Qclassification',
        meta: {
          title: '问题分类相关',
          code: 'menu:cs:questioncategorytotal',
          mark: 'statistics',
          componentName: 'Qclassification'
        },
        component: () => import('@/views/report/Qclassification/index.vue')
      },
      {
        path: 'userGroupReport',
        name: 'userGroupReport',
        meta: {
          title: '用户组&人员相关',
          code: 'menu:cs:usergroupandpersontotal',
          mark: 'statistics',
          componentName: 'UserGroupReport'
        },
        component: () => import('@/views/report/userGroupReport')
      },
      {
        path: 'nodeRelate',
        name: 'nodeRelate',
        meta: {
          title: '节点相关',
          code: 'menu:cs:nodetotal',
          mark: 'statistics',
          componentName: 'NodeRelate'
        },
        component: () => import('@/views/report/nodeRelate')
      },
      {
        path: 'traffic',
        name: 'traffic',
        meta: {
          title: '员工话务报表',
          code: 'menu:cs:trafficget',
          mark: 'statistics',
          componentName: 'traffic'
        },
        component: () => import('@/views/report/traffic')
      },
      {
        path: 'statusChange',
        name: 'statusChange',
        meta: {
          title: '客服状态变更表',
          code: 'menu:cs:statusget',
          mark: 'statistics',
          componentName: 'statusChange'
        },
        component: () => import('@/views/report/statusChange')
      },
      {
        path: 'dialogue',
        name: 'dialogue',
        meta: {
          title: '对话明细报表',
          code: 'menu:cs:dialogueget',
          mark: 'statistics',
          componentName: 'dialogue'
        },
        component: () => import('@/views/report/dialogue')
      },
      {
        path: 'visitor',
        name: 'visitor',
        meta: {
          title: '访客来源统计报表',
          code: 'menu:cs:visitorget',
          mark: 'statistics',
          componentName: 'visitor'
        },
        component: () => import('@/views/report/visitor')
      },
      {
        path: 'online',
        name: 'online',
        meta: {
          title: '在线平台报表',
          code: 'menu:cs:onlineget',
          mark: 'statistics',
          componentName: 'online'
        },
        component: () => import('@/views/report/online')
      },
      {
        path: 'workOrderInformation',
        name: 'workOrderInformation',
        meta: {
          title: '工单信息报表',
          code: 'menu:cs:workorderinformationget',
          mark: 'statistics',
          componentName: 'workOrderInformation'
        },
        component: () => import('@/views/report/workOrderInformation'),
      }
    ]
  }
];
