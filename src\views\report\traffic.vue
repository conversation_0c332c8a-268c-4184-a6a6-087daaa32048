<template>
  <div class="gdnumclass admunhui">
    <xyy-list-page>
      <template slot="header">
        <el-row class="herader-title" type="flex" justify="space-between" align="middle">
          <span style="font-size:16px">员工话务报表</span>
        </el-row>
        <el-form
          ref="listQuery"
          :rules="rules"
          :inline="true"
          :model="listQuery"
          label-position="right"
          class="search-form"
        >
          <el-row type="flex" class="row-bg" justify="space-between">
            <!-- 日期 -->
            <el-form-item label="日期" prop="dataRange">
              <el-date-picker
                :picker-options="pickerOptions"
                v-model="listQuery.dataRange"
                type="daterange"
                range-separator="-"
                size="small"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                text="erdf"
                prefix-icon="el-icon-date"
                class="innerSelTime"
                @change="queryTime()"
                @focus="dateTimeFocus()"
              />
            </el-form-item>
            <!--员工类型-->
            <el-form-item label="员工组" prop="sheetType">
              <el-select v-model="listQuery.sheetType" placeholder="全部" @change="changeList">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in oderCityData"
                  :key="item.id"
                  :label="item.groupName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <!--坐席号-->
            <el-form-item label="坐席" prop="CustLocation">
              <el-select
                v-model="listQuery.CustLocation"
                :remote-method="remoteMethod"
                filterable
                remote
                clearable
                placeholder="请输入坐席"
              >
                <el-option
                  v-for="item in options2"
                  :key="item.code"
                  :label="item.name+'('+item.code+')'"
                  :value="item.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row>
            <!--时间段-->
            <el-form-item label="时间段" prop="timeType">
              <el-select v-model="listQuery.timeType" placeholder="请选择时间段" :disabled="disabled">
                <el-option
                  v-for="item in oderTypeDta"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <span class="kuaijie">快捷查询</span>
            <el-radio-group
              label="快捷查询"
              prop="radio"
              style="line-height: 30px;"
              v-model="radio"
              @change="tongji()"
            >
              <el-radio :label="3" class="danxaun">按日统计</el-radio>
              <el-radio :label="4" class="danxaun">按周统计</el-radio>
              <el-radio :label="5" class="danxaun">按月统计</el-radio>
            </el-radio-group>
          </el-row>
          <el-row style="text-align:right;float:right;margin-top: -65px;">
            <el-form-item>
              <div class="searchTwoButton">
                <el-button
                  type="primary"
                  size="small"
                  @click="checkTimer(handerSearch('listQuery'))"
                >查询</el-button>
                <!-- <el-button size="small" @click="checkTimer(oldSearch('listQuery'))">重置</el-button> -->
                <el-button class="export-excel" @click="exportData" style="padding-top: 9px;">导出</el-button>
              </div>
            </el-form-item>
          </el-row>
        </el-form>
      </template>
      <template slot="body">
        <div class="info-container">
          <info :info="infodata()" v-if="wenhao"></info>
        </div>
        <xyy-table
          v-loading="tabLoading"
          :is-stripe="false"
          :data="list"
          :col="col"
          :list-query="queryList"
          @get-data="getList"
          :offset-top="240"
        ></xyy-table>
      </template>
    </xyy-list-page>
    <expor-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    ></expor-tip>
  </div>
</template>

<script>
import exporTip from '@/views/work-sheet/components/exportTip';
import { mockGDType, mockGDUser } from './components/mock';
import {
  TrafficReport,
  getTrafficList,
  getTraffic,
  getRoleTra,
  exportReport
} from '@/api/nodemanager';
import chosseTypeAlert from './components/commonDialog';
import formTypeListVue from '../form/form-type-list.vue';
import axios from 'axios';
export default {
  name: 'traffic',
  components: {
    chosseTypeAlert,
    exporTip
  },
  data() {
    return {
      tabLoading: false,
      options2: [],
      disabled: false,
      ortoCityData: [],
      queryList: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      wenhao: true,
      changeExport: false,
      loading: false,
      mocktype: this.APIMockGDType(),
      mockCity: this.APICityGDType(),
      oderTypeDta: [
        { id: 1, typeName: '半小时' },
        { id: 2, typeName: '一小时' },
        { id: 3, typeName: '天' }
      ],
      oderCityData: [],
      dataRange: '',
      formAdressId: '',
      formTypeId: '',
      created: false,
      list: [],
      listQuery: {
        dataRange: [], // 日期
        sheetType: '', // 员工组
        CustLocation: '', // 坐席
        timeType: 3 // 时间段
      },
      radio: 3,
      rules: {
        dataRange: [
          { required: false, message: '日期为必填项', trigger: 'blur' }
        ]
      },
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.minDate = minDate;
          this.maxDate = maxDate;
        },
        disabledDate: time => {
          const curDate = new Date().getTime();
          const two = 365 * 2 * 24 * 3600 * 1000;
          const twoyear = curDate - two;
          const three = 92 * 24 * 3600 * 1000;
          if (this.minDate) {
            return (
              time.getTime() > Date.now() ||
              time.getTime() < twoyear ||
              time > new Date(this.minDate.getTime() + three) ||
              time < new Date(this.minDate.getTime() - three)
            );
          }
          return time.getTime() > Date.now() || time.getTime() < twoyear;
        }
      },
      col: [
        {
          index: 'reportDate',
          name: '序号',
          width: 50,
          fixed: true,
          resizable: true
        },
        {
          index: 'tiemduan',
          name: '时间段',
          width: 200,
          fixed: true,
          resizable: true
        },
        {
          index: 'callCenterCode',
          name: '坐席工号',
          width: 95,
          fixed: true,
          resizable: true
        },
        {
          index: 'callCenterName',
          name: '坐席名称',
          width: 95,
          fixed: true,
          resizable: true
        },
        {
          index: 'groupName',
          name: '员工组',
          width: 120,
          fixed: true,
          resizable: true
        },
        {
          index: 'onlineTime',
          name: '登录时长(s)',
          width: 100,
          resizable: true
        },
        {
          index: 'avgChatSecond',
          name: '平均会话时长(s)',
          width: 140,
          resizable: true
        },
        {
          index: 'allDialogNum',
          name: '总会话数',
          width: 80,
          resizable: true
        },
        {
          index: 'validDialogNum',
          name: '有效会话数',
          width: 100,
          resizable: true
        },
        // {
        //   index: 'joinDialogNum',
        //   name: '参与会话数',
        //   width: 100,
        //   resizable: true
        // },

        { index: 'ownDialogNum', name: '独立会话数', resizable: true },
        { index: 'invalidDialogNum', name: '无效会话', resizable: true },
        {
          index: 'invalidDialogRatioStr',
          name: '无效响应占比',
          width: 115,
          resizable: true
        },
        {
          index: 'transferOutTimes',
          name: '转出会话数',
          width: 115,
          resizable: true
        },
        {
          index: 'transferInTimes',
          name: '转入会话数',
          width: 115,
          resizable: true
        },
        {
          index: 'transferOutRatioStr',
          name: '转接率',
          width: 95,
          resizable: true
        },
        {
          index: 'kefuTimeOutCloseTimes',
          name: '坐席超时关闭会话数',
          width: 150,
          resizable: true
        },
        {
          index: 'receiveUserTimes',
          name: '接待访客数',
          width: 95,
          resizable: true
        },
        {
          index: 'timeOutTimes',
          name: '超时次数',
          width: 95,
          resizable: true
        },
        {
          index: 'firstReplyTimeoutTimes',
          name: '首次响应超时次数',
          width: 140,
          resizable: true
        },
        {
          index: 'userMessageNum',
          name: '访客消息数',
          width: 100,
          resizable: true
        },
        {
          index: 'callCenterMessageNum',
          name: '坐席消息数',
          width: 100,
          resizable: true
        },
        {
          index: 'autoReplyMessageNum',
          name: '自动回复消息数',
          width: 120,
          resizable: true
        },
        {
          index: 'replyMessageRatioStr',
          name: '坐席问答比',
          width: 100,
          resizable: true
        },
        {
          index: 'avgFirstReplySecond',
          name: '平均首次响应时长(s)',
          width: 160,
          resizable: true
        },
        {
          index: 'avgReplySecond',
          name: '平均响应时长(s)',
          width: 130,
          resizable: true
        },
        {
          index: 'vdoing1Four',
          name: '忙碌次数',
          width: 80,
          resizable: true
        },
        {
          index: 'vdoing1Five',
          name: '忙碌时长(s)',
          width: 100,
          resizable: true
        },
        {
          index: 'vdoing2One',
          name: '会议次数',
          width: 80,
          resizable: true
        },
        {
          index: 'vdoing2Two',
          name: '会议时长(s)',
          width: 100,
          resizable: true
        },
        {
          index: 'vdoing2Three',
          name: '培训次数',
          width: 80,
          resizable: true
        },
        {
          index: 'vdoing2Four',
          name: '培训时长(s)',
          width: 100,
          resizable: true
        },
        {
          index: 'vdoing2Five',
          name: '用餐次数',
          width: 80,
          resizable: true
        },
        {
          index: 'assignNum',
          name: '用餐时长(s)',
          width: 100,
          resizable: true
        },
        {
          index: 'transferNum',
          name: '在线次数',
          width: 80,
          resizable: true
        },
        {
          index: 'transNum',
          name: '在线时长(s)',
          width: 100,
          resizable: true
        },
        {
          index: 'lixianNum',
          name: '离线次数',
          width: 80,
          resizable: true
        },
        {
          index: 'lixianferNum',
          name: '离线时长(s)',
          width: 100,
          resizable: true
        },
        {
          index: 'xiaoxiuNum',
          name: '小休次数',
          width: 80,
          resizable: true
        },
        {
          index: 'xiaoxiuferNum',
          name: '小休时长(s)',
          width: 100,
          resizable: true
        },
        {
          index: 'satisfyNum',
          name: '满意评价数',
          width: 100,
          resizable: true
        },
        {
          index: 'satisfyRatioStr',
          name: '满意参数评率',
          width: 110,
          resizable: true
        },
        {
          index: 'feimancanNum',
          name: '非常满意数',
          width: 100,
          resizable: true
        },
        {
          index: 'feimancanLv',
          name: '非常满意率',
          width: 100,
          resizable: true
        },
        {
          index: 'manyiNum',
          name: '满意数',
          width: 80,
          resizable: true
        },
        {
          index: 'manyiLv',
          name: '满意率',
          width: 80,
          resizable: true
        },
        {
          index: 'yibanNum',
          name: '一般数',
          width: 80,
          resizable: true
        },
        {
          index: 'yibanLv',
          name: '一般率',
          width: 80,
          resizable: true
        },
        {
          index: 'bumanNum',
          name: '不满意数',
          width: 80,
          resizable: true
        },
        {
          index: 'bumanLv',
          name: '不满意率',
          width: 80,
          resizable: true
        },
        {
          index: 'feibuNum',
          name: '非常不满意数',
          width: 120,
          resizable: true
        },
        {
          index: 'feibuLv',
          name: '非常不满意率',
          width: 120,
          resizable: true
        }
      ],
      date: '',
      year: '',
      day: '',
      dayend: '',
      dangTime: ''
    };
  },
  created() {
    this.getNowTimeDate();
    this.APIGetTrafficList();
    this.created = true;
  },
  activated() {
    // if (!this.created) {
    //   this.APISelectWorkorderList();
    // }
    this.created = false;
    this.getList(this.queryList);
  },
  methods: {
    // 坐席搜索
    async remoteMethod(query) {
      if (query !== '') {
        var params = {
          page: 1,
          pagesize: 1000,
          searchtype: 'name',
          searchval: query
        };
        await getRoleTra(params).then(res => {
          this.options2 = res.data.page.datas;
          // console.log(this.options2);
          return res.data;
        });
      } else {
        this.options2 = [];
      }
    },
    tongji() {
      if (this.radio == 3) {
        this.listQuery.dataRange = [this.dangTime, this.dangTime];
        this.disabled = false;
      }
      if (this.radio == 5) {
        var myDate = new Date();
        var tYear = myDate.getFullYear();
        var tMonth = myDate.getMonth() + 1;
        tMonth = tMonth < 10 ? '0' + tMonth : tMonth;
        var yuetime = tYear + '-' + tMonth + '-01';
        this.listQuery.dataRange = [yuetime, this.dangTime];
        this.disabled = true;
      }
      if (this.radio == 4) {
        const getDays = new Date().getDay();
        // const getDays = new Date().getDay();
        const timeget7 = new Date().getTime() - 3600 * 1000 * 24 * 6;
        const timeget6 = new Date().getTime() - 3600 * 1000 * 24 * 5;
        const timeget5 = new Date().getTime() - 3600 * 1000 * 24 * 4;
        const timeget4 = new Date().getTime() - 3600 * 1000 * 24 * 3;
        const timeget3 = new Date().getTime() - 3600 * 1000 * 24 * 2;
        const timeget2 = new Date().getTime() - 3600 * 1000 * 24 * 1;
        const timeget1 = new Date().getTime();

        const tiemset7 = new Date().setTime(timeget7);
        const tiemset6 = new Date().setTime(timeget6);
        const tiemset5 = new Date().setTime(timeget5);
        const tiemset4 = new Date().setTime(timeget4);
        const tiemset3 = new Date().setTime(timeget3);
        const tiemset2 = new Date().setTime(timeget2);
        const tiemset1 = new Date().setTime(timeget1);
        const arr = [
          '星期天',
          '星期一',
          '星期二',
          '星期三',
          '星期四',
          '星期五',
          '星期六'
        ];
        this.disabled = true;
        if (arr[getDays] == '星期一') {
          this.date = new Date(tiemset1);
          this.year = this.date.getFullYear();
          this.month = this.date.getMonth() + 1;
          this.day = this.date.getDate();
          this.month = this.month < 10 ? '0' + this.month : this.month;
          this.day = this.day < 10 ? '0' + this.day : this.day;
          this.dayend = this.year + '-' + this.month + '-' + this.day;
          this.listQuery.dataRange[0] = this.dayend;
          this.listQuery.dataRange = [this.dayend, this.dangTime];
        } else if (arr[getDays] == '星期二') {
          this.date = new Date(tiemset2);
          this.year = this.date.getFullYear();
          this.month = this.date.getMonth() + 1;
          this.day = this.date.getDate();
          this.month = this.month < 10 ? '0' + this.month : this.month;
          this.day = this.day < 10 ? '0' + this.day : this.day;
          this.dayend = this.year + '-' + this.month + '-' + this.day;
          this.listQuery.dataRange[0] = this.dayend;
          this.listQuery.dataRange = [this.dayend, this.dangTime];
        } else if (arr[getDays] == '星期三') {
          this.date = new Date(tiemset3);
          this.year = this.date.getFullYear();
          this.month = this.date.getMonth() + 1;
          this.day = this.date.getDate();
          this.month = this.month < 10 ? '0' + this.month : this.month;
          this.day = this.day < 10 ? '0' + this.day : this.day;
          this.dayend = this.year + '-' + this.month + '-' + this.day;
          this.listQuery.dataRange[0] = this.dayend;
          this.listQuery.dataRange = [this.dayend, this.dangTime];
        } else if (arr[getDays] == '星期四') {
          this.date = new Date(tiemset4);
          this.year = this.date.getFullYear();
          this.month = this.date.getMonth() + 1;
          this.day = this.date.getDate();
          this.month = this.month < 10 ? '0' + this.month : this.month;
          this.day = this.day < 10 ? '0' + this.day : this.day;
          this.dayend = this.year + '-' + this.month + '-' + this.day;
          this.listQuery.dataRange[0] = this.dayend;
          this.listQuery.dataRange = [this.dayend, this.dangTime];
        } else if (arr[getDays] == '星期五') {
          this.date = new Date(tiemset5);
          this.year = this.date.getFullYear();
          this.month = this.date.getMonth() + 1;
          this.day = this.date.getDate();
          this.month = this.month < 10 ? '0' + this.month : this.month;
          this.day = this.day < 10 ? '0' + this.day : this.day;
          this.dayend = this.year + '-' + this.month + '-' + this.day;
          this.listQuery.dataRange[0] = this.dayend;
          this.listQuery.dataRange = [this.dayend, this.dangTime];
        } else if (arr[getDays] == '星期六') {
          this.date = new Date(tiemset6);
          this.year = this.date.getFullYear();
          this.month = this.date.getMonth() + 1;
          this.day = this.date.getDate();
          this.month = this.month < 10 ? '0' + this.month : this.month;
          this.day = this.day < 10 ? '0' + this.day : this.day;
          this.dayend = this.year + '-' + this.month + '-' + this.day;
          this.listQuery.dataRange[0] = this.dayend;
          this.listQuery.dataRange = [this.dayend, this.dangTime];
        } else if (arr[getDays] == '星期天') {
          this.date = new Date(tiemset7);
          this.year = this.date.getFullYear();
          this.month = this.date.getMonth() + 1;
          this.day = this.date.getDate();
          this.month = this.month < 10 ? '0' + this.month : this.month;
          this.day = this.day < 10 ? '0' + this.day : this.day;
          this.dayend = this.year + '-' + this.month + '-' + this.day;
          this.listQuery.dataRange[0] = this.dayend;
          this.listQuery.dataRange = [this.dayend, this.dangTime];
        }
      }
    },
    getList: function(queryList) {
      const { page, pageSize } = queryList;
      const name = this.queryList.creatorName;
      const { dataRange, timeType, sheetType, CustLocation } = this.listQuery;
      const param = {
        pageNum: page,
        pageSize,
        queryStartTime: new Date(dataRange[0]).getTime(new Date(dataRange[0])),
        queryEndTime: new Date(dataRange[1]).getTime(new Date(dataRange[1])),
        groupId: sheetType,
        callCenterCode: CustLocation || null,
        quickType: this.radio,
        statisticType: timeType
      };
      // console.log(param);
      // var paramil = JSON.stringify(param);
      // console.log(paramil);
      TrafficReport(param)
        .then(res => {
          // console.log(res);
          const { total } = res.data;
          this.list = res.data.list;
          let reportDate = {};
          let tiemduan = {};
          let feimancanNum = {};
          let feimancanLv = {};
          let manyiNum = {};
          let manyiLv = {};
          let yibanNum = {};
          let yibanLv = {};
          let bumanNum = {};
          let bumanLv = {};
          let feibuNum = {};
          let feibuLv = {};
          let onlineTime = {};
          let vdoing1Four = {};
          let vdoing1Five = {};
          let vdoing2One = {};
          let vdoing2Two = {};
          let vdoing2Three = {};
          let vdoing2Four = {};
          let vdoing2Five = {};
          let assignNum = {};
          let transferNum = {};
          let transNum = {};
          let lixianNum = {};
          let lixianferNum = {};
          let xiaoxiuNum = {};
          let xiaoxiuferNum = {};
          this.list.forEach((item, index) => {
            reportDate = {};
            tiemduan = {};
            feimancanNum = {};
            feimancanLv = {};
            manyiNum = {};
            manyiLv = {};
            yibanNum = {};
            yibanLv = {};
            bumanNum = {};
            bumanLv = {};
            feibuNum = {};
            feibuLv = {};
            onlineTime = {};
            vdoing1Four = {};
            vdoing1Five = {};
            vdoing2One = {};
            vdoing2Two = {};
            vdoing2Three = {};
            vdoing2Four = {};
            vdoing2Five = {};
            assignNum = {};
            transferNum = {};
            transNum = {};
            lixianNum = {};
            lixianferNum = {};
            xiaoxiuNum = {};
            xiaoxiuferNum = {};
            this.list[index] = Object.assign({}, this.list[index], {
              reportDate: index + 1
            });
            this.list[index] = Object.assign({}, this.list[index], {
              tiemduan: item.startTimeStr + ' ~ ' + item.endTimeStr
            });
            item.satisfyDataList.forEach((itemLi, indexLi) => {
              if (itemLi.satisfyType == 1) {
                feimancanNum = itemLi.satisfyNum;
                feimancanLv = itemLi.satisfyRatioStr;
              } else if (itemLi.satisfyType == 2) {
                manyiNum = itemLi.satisfyNum;
                manyiLv = itemLi.satisfyRatioStr;
              } else if (itemLi.satisfyType == 3) {
                yibanNum = itemLi.satisfyNum;
                yibanLv = itemLi.satisfyRatioStr;
              } else if (itemLi.satisfyType == 4) {
                bumanNum = itemLi.satisfyNum;
                bumanLv = itemLi.satisfyRatioStr;
              } else if (itemLi.satisfyType == 5) {
                feibuNum = itemLi.satisfyNum;
                feibuLv = itemLi.satisfyRatioStr;
              }
            });

            item.callCenterStatusData.forEach((itemList, indexList) => {
              if (itemList.callCenterStatus == 0) {
                if (
                  this.listQuery.timeType === 1 ||
                  this.listQuery.timeType === 2
                ) {
                  onlineTime = '-';
                } else {
                  onlineTime = itemList.statusChangeSecond;
                }
              } else if (itemList.callCenterStatus == 1) {
                transferNum = itemList.statusChangeTimes;
                if (
                  this.listQuery.timeType === 1 ||
                  this.listQuery.timeType === 2
                ) {
                  transNum = '-';
                } else {
                  transNum = itemList.statusChangeSecond;
                }
              } else if (itemList.callCenterStatus == 2) {
                vdoing2Three = itemList.statusChangeTimes;
                vdoing2Four = itemList.statusChangeSecond;
              } else if (itemList.callCenterStatus == 3) {
                vdoing1Four = itemList.statusChangeTimes;
                vdoing1Five = itemList.statusChangeSecond;
              } else if (itemList.callCenterStatus == 4) {
                vdoing2Five = itemList.statusChangeTimes;
                assignNum = itemList.statusChangeSecond;
              } else if (itemList.callCenterStatus == 5) {
                lixianNum = itemList.statusChangeTimes;
                lixianferNum = itemList.statusChangeSecond;
              } else if (itemList.callCenterStatus == 6) {
                // historyColsedTime = itemList.statusChangeTimes;
              } else if (itemList.callCenterStatus == 7) {
                xiaoxiuNum = itemList.statusChangeTimes;
                xiaoxiuferNum = itemList.statusChangeSecond;
              } else if (itemList.callCenterStatus == 8) {
                vdoing2One = itemList.statusChangeTimes;
                vdoing2Two = itemList.statusChangeSecond;
              }
            });
            this.list[index] = Object.assign({}, this.list[index], {
              feimancanNum: feimancanNum,
              feimancanLv: feimancanLv,
              manyiNum: manyiNum,
              manyiLv: manyiLv,
              yibanNum: yibanNum,
              yibanLv: yibanLv,
              bumanNum: bumanNum,
              bumanLv: bumanLv,
              feibuNum: feibuNum,
              feibuLv: feibuLv,
              onlineTime: onlineTime,
              vdoing1Four: vdoing1Four,
              vdoing1Five: vdoing1Five,
              vdoing2One: vdoing2One,
              vdoing2Two: vdoing2Two,
              vdoing2Three: vdoing2Three,
              vdoing2Four: vdoing2Four,
              vdoing2Five: vdoing2Five,
              assignNum: assignNum,
              transferNum: transferNum,
              transNum: transNum,
              lixianNum: lixianNum,
              lixianferNum: lixianferNum,
              xiaoxiuNum: xiaoxiuNum,
              xiaoxiuferNum: xiaoxiuferNum
            });
          });
          this.queryList = {
            ...this.queryList,
            total: Number(total)
          };
        })
        .catch(() => {
          this.tabLoading = false;
        });
    },
    handleChangeExport(info) {
      const that = this;
      this.changeExport = false;
      if (info === 'go') {
        that.$router.push({
          path: '/other/index/'
        });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    exportData() {
      const that = this;
      if (!that.list || !that.list.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      const param = {
        queryEndTime: that.listQuery.dataRange[1],
        queryStartTime: that.listQuery.dataRange[0],
        groupId: this.listQuery.sheetType,
        callCenterCode: this.listQuery.CustLocation,
        quickType: this.radio,
        statisticType: this.listQuery.timeType
      };
      let url = ``;
      exportReport(param).then(res => {
        if (res.hasOwnProperty('code')) {
          that.$XyyMessage.error(res.msg);
        } else {
          url = `${process.env.BASE_API_IM}/sessionReport/export?queryEndTime=${that.listQuery.dataRange[1]}&queryStartTime=${that.listQuery.dataRange[0]}&groupId=${this.listQuery.sheetType}&callCenterCode=${this.listQuery.CustLocation}&quickType=${this.radio}&statisticType=${this.listQuery.timeType}`;
          if (
            this.$store.getters.channel &&
            this.$store.getters.channel.businessPartCode
          ) {
            url =
              url +
              `&businessPartCode=${this.$store.getters.channel.businessPartCode}`;
          }
          const a = document.createElement('a');
          a.href = url;
          a.click();
        }
      });
    },
    // oldSearch() {
    //   this.getNowTimeDate();
    //   this.listQuery.sheetType = '';
    //   this.listQuery.CustLocation = '';
    //   this.APISelectWorkorderList();
    // },
    handerSearch(formName) {
      this.getList(this.queryList);
    },
    // 时间格式化
    getFormatDate: function(row, column, cellValue, index) {
      return new Date(cellValue + 8 * 3600 * 1000)
        .toJSON()
        .substr(0, 19)
        .replace('T', ' ');
    },
    // 当前时间
    getNowTimeDate() {
      const time = new Date(8 * 3600 * 1000 + +new Date())
        .toJSON()
        .substr(0, 10);
      this.dangTime = time;
      this.listQuery.dataRange = [time, time];
    },
    dateTimeFocus() {
      this.minDate = undefined;
      // 条件时间查询
      const that = this;
      that.$nextTick(function() {
        document
          .getElementsByClassName('el-picker-panel')[0]
          .addEventListener('click', function() {
            // that.$refs.messageDrop.show();
          });
      });
    },
    queryTime() {
      // this.$refs.messageDrop.show();
      console.log(this.listQuery.dataRange[1]);
    },
    getSummaries() {
      const sums = [];
      this.col.forEach((column, index) => {
        if (index === 0) {
          sums[column.index] = '合计';
          return;
        }
        const values = this.list.map(item => Number(item[column.index]));
        if (!values.every(value => isNaN(value))) {
          sums[column.index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          // sums[index] += ' 元';
        } else {
          sums[column.index] = '';
        }
      });

      this.list.push(sums);
    },

    changeToNumber(data) {
      const newdata = {};
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        for (const key in element) {
          if (element.hasOwnProperty(key)) {
            const objelement = element[key];
            newdata;
          }
        }
        newdata[element];
      }
    },
    infodata() {
      return [
        {
          title: '平均会话时长',
          info: '（会话结束时间-会话开始时间）之和/有效会话数'
        },
        {
          title: '总会话数',
          info: '有效会话数+无效会话数'
        },
        {
          title: '有效会话数',
          info: '访客消息数>0'
        },
        {
          title: '独立会话数',
          info: '由该坐席独立接待，并未发生转接动作的会话数'
        },
        {
          title: '无效会话数',
          info: '访客消息数为0的会话总数'
        },
        {
          title: '转出会话数',
          info: '由坐席主动，系统自动转接至其他坐席的会话数'
        },
        {
          title: '转入会话数',
          info: '由其他坐席转接至该坐席的会话数'
        },
        {
          title: '转接率',
          info: '转出次数/有效会话数'
        },
        {
          title: '坐席超时关闭会话数',
          info: '坐席超时关闭的会话总数'
        },
        {
          title: '接待访客数',
          info: 'user ID 唯一的访客总数'
        },
        {
          title: '超时次数',
          info: '坐席超出目标回应时间，还没有回复客户消息的次数和'
        },
        {
          title: '访客消息数',
          info: '访客发的消息数'
        },
        {
          title: '坐席消息数',
          info: '坐席发的消息数'
        },
        {
          title: '自动回复消息数',
          info: '客服自动回复消息+访客点击自动回复问题+自动回复问题答案数之和'
        },
        {
          title: '坐席问答比',
          info: '访客消息数/坐席消息数'
        },
        {
          title: '平均首次响应时长',
          info: '（首次回复时间-访客首条消息时间）/有效会话数'
        },
        {
          title: '平均响应时长',
          info: '（坐席回复时间-访客消息时间）之和/消息对数'
        },

        {
          title: '首次响应超时次数',
          info:
            '坐席首次响应客户超出目标值回应时间（设为14S）的会话数（不包括自动回复）'
        },
        {
          title: '无效响应占比',
          info: '（无效会话+客服未回复会话）/总会话数'
        },
        {
          title: '参评率',
          info: '满意度评价数/有效会话数（坐席）'
        },
        {
          title: '非常满意数（满意/一般/不满意/非常不满意）',
          info: '客服参评选择非常满意（满意/一般/不满意/非常不满意）的总数'
        },
        {
          title: '非常满意率（满意/一般/不满意/非常不满意）',
          info: '非常满意数（满意/一般/不满意/非常不满意）/所有参评客户总数'
        },
        {
          title:
            '忙碌次数(会议次数/培训次数/用餐次数/在线次数/离线次数/小休次数)',
          info: '坐席更改这些状态的次数'
        },
        {
          title: '忙碌（会议/培训/用餐/在线/离线/小休）时长',
          info: '坐席这些状态的持续时长'
        }
      ];
    },

    /**
     * 工单量报表API
     */

    APISelectWorkorderList() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      selectWorkorderList({
        customerSource: this.listQuery.CustLocation,
        endTime: this.listQuery.dataRange[1],
        formTypeId: this.listQuery.sheetType,
        startTime: this.listQuery.dataRange[0]
      }).then(res => {
        this.loading.close();
        let dataListList = res.data;
        if (
          !dataListList &&
          typeof dataListList !== 'undefined' &&
          dataListList != 0
        ) {
          this.$XyyMessage.success('暂时无数据');
          this.list = [];
          return;
        }
        if (res.code === 1) {
          if (res.data.length === 0) {
            this.$XyyMessage.success('暂时无数据');
          }
          this.list = res.data;
        } else {
          this.$XyyMessage.error(res.msg);
        }
        // this.getSummaries();
      });
    },
    APIGetTrafficList() {
      getTrafficList().then(res => {
        if (res.code === 1) {
          this.oderCityData = res.data;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    changeList() {
      var parm = this.listQuery.sheetType;
      if (parm) {
        getTraffic(parm).then(res => {
          if (res.code == 0) {
            this.$XyyMessage.error(res.msg);
          } else if (res.code == 1) {
            this.ortoCityData = res.data.userKefuList;
          }
        });
      }
    },

    APIMockGDType() {
      return mockGDType();
    },
    APICityGDType() {
      return mockGDUser();
    }
  }
};
</script>
<style scoped lang="scss">
/deep/.el-date-editor {
  /*width: 490px;*/
}
/deep/.page-header {
  padding-bottom: 0 !important;
}
</style>
<style>
.kuaijie {
  font-size: 14px;
  color: #606266;
  padding-right: 15px;
}
.danxaun {
  margin-top: 12px;
}
.search-form {
  border-bottom: 1px dashed #e4e4eb;
  overflow: hidden;
}
.herader-title {
  font-size: 14px;
  color: #393943;
  padding: 10px 0;
  background: #e4e4e4;
  margin-bottom: 20px;
}

.gdnumclass .el-table th > .cell {
  position: relative;
  word-wrap: normal;
  text-overflow: ellipsis;
  vertical-align: middle;
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  /* display: flex;
    flex-direction: column;
    align-items: flex-start; */
  padding-top: 10px;
  padding-bottom: 10px;
}

.gdnumclass .el-table th > .cell p {
  display: block;
  margin-block-start: 0px;
  margin-block-end: 0px;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  text-align: right;
}

.gdnumclass .el-table th > .cell small {
  font-size: 12px;
  color: #909399;
  display: block;
  margin-block-start: 0px;
  margin-block-end: 0px;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  text-align: right;
}

.gdnumclass .el-table tr td .cell {
  height: 100%;
  line-height: 25px;
  overflow-x: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  word-break: normal;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  padding-left: 10px;
  padding-right: 10px;
  white-space: normal;
  display: block;
  text-align: center;
}

.gdnum_input_group .el-form-item__error {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  margin-left: 15px;
}
.admunhui .el-table th:nth-child(18) > .cell {
  color: #fb2929;
}
.admunhui .el-table th:nth-child(25) > .cell {
  color: #fb2929;
}
</style>

<style scoped lang="scss">
/deep/.el-table {
  /deep/.el-table__fixed {
    height: auto !important;
    bottom: 16px;
    margin-bottom: 0 !important;
  }
  /deep/.el-table__fixed-body-wrapper {
    top: 44px !important;
  }
}
/deep/.el-table__body tr td {
  border-bottom: 1px solid #ebeef5;
  background: #fff;
}
.el-menu-item.is-active {
  color: #3b95a8 !important;
}
/deep/.el-input__inner {
  height: 36px !important;
  line-height: 36px !important;
}
/deep/.el-button {
  height: 36px;
}
.el-menu.el-menu--horizontal {
  border-bottom: 1px dashed #e4e4eb;
  padding-bottom: 20px;
}
.search-box {
  width: 344px;
  float: right;
  .el-input {
    width: calc(100% - 76px);
    /deep/ .el-input__inner {
      height: 36px;
      line-height: 36px;
    }
  }
  button {
    width: 68px;
    height: 36px;
    float: right;
  }
}
.status-tip {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 2px 2px 0;
  &.open {
    background: #67c23a;
  }
  &.close {
    background: #ff3024;
  }
}

.toprow {
  display: flex;
  flex-direction: row;
}

.timediv {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 14px;
  margin-right: 30px;
}

/*.innerSelTime {*/
/*margin-left: 15px;*/
/*}*/

.gdnumheader {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.gdnum_input_mainheader {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.gdnum_input_group {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.gdnum_button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}

.searchTwoButton {
  width: 100%;
}
</style>
