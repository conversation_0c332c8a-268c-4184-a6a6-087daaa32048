<template>
  <div class="access-denied-container">
    <img :src="imgUrl" />
    <p>{{message}}</p>
    <el-button type="primary"
               @click="relogin">重新登录</el-button>
  </div>
</template>

<script>
import imgUrl from '@/assets/common/null.png';
import { changeState } from '@/api/monitor/index.js';
import { getPersonalData } from '@/api/im-config/personal-setting';
export default {
  name: '',
  components: {},
  filters: {},
  props: {},
  data () {
    return {
      imgUrl,
      message: '无系统访问权限，请联系管理员授权！'
    };
  },
  computed: {},
  watch: {},
  created () {

  },
  mounted () {
    this.message = this.$route.query.message || '无系统访问权限，请联系管理员授权！'
  },
  methods: {
    relogin () {
      // 退出获取当前客服ID
      this.$store.dispatch('LogOut').then(res => {
        //this.$XyyMessage.success('退出成功！');
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.access-denied-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #f0f2f5;
  color: #93979b;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .el-button {
    margin-top: 12px;
  }
}
</style>