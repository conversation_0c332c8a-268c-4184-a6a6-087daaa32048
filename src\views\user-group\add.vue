<template>
  <div v-loading="isSubmitLoading" class="page user-group-add_page">
    <el-form ref="form" :model="form" :rules="rules" label-width="92px">
      <el-form-item class="itme-content_tag" label="参与人" required prop="users">
        <el-tag
          v-for="tag in form.users"
          :key="tag.userId"
          closable
          type="info"
          @close="removeTag(tag)"
        >{{ tag.nickname }}</el-tag>
        <el-button class="add-tag_button" plain @click="addTag">
          <svg-icon icon-class="btn-add" class="add-tag_icon" />添加
        </el-button>
        <info :info="info"></info>
      </el-form-item>
      <el-form-item label="用户组名称" prop="userGroupName">
        <el-input v-model="form.userGroupName" placeholder="请输入用户组名称"></el-input>
      </el-form-item>
      <el-form-item label="说明" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          rows="6"
          placeholder="请输入备注说明"
          maxlength="200"
          show-word-limit
        ></el-input>
      </el-form-item>
    </el-form>
    <div class="page-footer">
      <div class="footer-right">
        <el-button type="primary" @click="checkTimer(submit,'timer')()">保存</el-button>
        <el-button plain @click="closePage">取消</el-button>
      </div>
    </div>
    <!-- 选择参与人 -->
    <el-dialog :visible.sync="isAddDialog" title="选择用户组人员" width="877px">
      <div class="xyy-transfer">
        <div class="xyy-transfer-panel xyy-transfer-panel_left">
          <p class="xyy-transfer-panel_header">
            <el-input v-model="searchVal" placeholder="请输入" @keydown.enter.native="searchUser">
              <i slot="suffix" class="el-input__icon el-icon-search" @click="searchUser"></i>
            </el-input>
          </p>
          <div v-loading="!treeData.length" class="xyy-transfer-panel_body">
            <v-tree
              v-if="userListType===0"
              ref="tree"
              :data="treeData"
              :tpl="tpl"
              :multiple="true"
              :halfcheck="true"
              class="xyy-tree"
              @node-click="treeNodeClick"
              @node-expand="treeNodeExpand"
            />
            <div
              v-infinite-scroll="loadListData"
              v-if="userListType===1"
              :infinite-scroll-disabled="disabledLoad"
              class="user-list_right"
            >
              <el-checkbox-group
                v-loading="userListLoading"
                v-model="checkUserList.left"
                class="user-list"
              >
                <el-checkbox v-for="user in listData" :key="user.oaId" :label="user">
                  <span :title="user.name">{{ user.name }}</span>
                  <span :title="user.department">{{ user.department }}</span>
                  <span :title="user.dptName">{{ user.dptName }}</span>
                </el-checkbox>
              </el-checkbox-group>
              <!-- <p class="user-list-msg" v-if="userListLoading">加载中...</p> -->
              <!-- <p class="user-list-msg" v-if="userListNoMore">没有更多了</p> -->
              <p v-if="!userListLoading && !listData.length" class="user-list-msg">没有查询到结果</p>
            </div>
          </div>
        </div>
        <div class="xyy-transfer_buttons">
          <el-button
            :disabled="isAddUserDisabled"
            type="primary"
            class="xyy-transfer_button el-icon-arrow-right"
            @click="addChecked"
          ></el-button>
          <el-button
            :disabled="!checkUserList.right.length"
            type="primary"
            class="xyy-transfer_button el-icon-arrow-left"
            @click="removeChecked"
          ></el-button>
        </div>
        <div class="xyy-transfer-panel xyy-transfer-panel_right">
          <p class="xyy-transfer-panel_header">
            <span class="xyy-transfer-title">已选择（{{ userList.length }}）</span>
          </p>
          <div class="xyy-transfer-panel_body">
            <el-checkbox-group v-model="checkUserList.right" class="user-list">
              <el-checkbox v-for="user in userList" :key="user.id" :label="user.id">
                <span :title="user.name">{{ user.name }}</span>
                <span :title="user.department">{{ user.department }}</span>
                <span :title="user.dptName">{{ user.dptName }}</span>
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="isAddDialog = false">取 消</el-button>
        <el-button type="primary" @click="addUser">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getUserGroupInfo,
  getListDept,
  getDeptUserList,
  searchUserList,
  addUserGroup,
  updateUserGroup
} from '@/api/user-group';

export default {
  name: 'UserGroupAdd',
  data() {
    const validateRepeat = (rule, value, callback) => {
      setTimeout(() => {
        callback(new Error(value + ' 重复了'));
      }, 1000);
    };
    return {
      info: [
        { title: '可从公司组织部门树中选择人员进行添加（单人/多人）', info: '' },
        { title: '', info: '注：此处组织结构同步OA账号信息', mark: true },
        { title: '', info: '注：不能添加已离职的员工' }
      ],
      userGroupId: '',
      isSubmitLoading: false,
      form: {
        users: [],
        userGroupName: '',
        description: ''
      },
      rules: {
        users: [{ type: 'array', required: true, message: '请添加参与人' }],
        userGroupName: [
          { required: true, message: '请输入用户组名称', trigger: 'blur' },
          { max: 20, message: '用户组名称不能超过 20 个字符', trigger: 'blur' }
          /* { validator: validateRepeat, trigger: 'blur' } */
        ]
      },
      isAddDialog: false,
      userListType: 0,
      treeData: [],
      listData: [],
      listQuery: {
        page: 1,
        pageSize: 10
      },
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      checkUserList: {
        left: [],
        right: []
      },
      userListLoading: false,
      userListNoMore: false,
      userList: [],
      isTreeCheckUser: false,
      searchVal: '',
      timer: null,
      versionCode: ''
    };
  },
  computed: {
    isAddUserDisabled: function() {
      if (this.userListType === 0) {
        return !this.isTreeCheckUser;
      } else {
        return this.checkUserList.left.length === 0;
      }
    },
    disabledLoad: function() {
      return this.userListLoading || this.userListNoMore;
    }
  },
  watch: {
    isAddDialog: function(newVal, oldVal) {
      if (newVal) {
        this.getDeptList();
      }
    }
  },
  created() {
    this.initInfo();
  },
  methods: {
    initInfo: function() {
      const { userGroupId, userGroupName, description, versionCode } = this.$route.query;
      this.versionCode = versionCode;
      if (userGroupId) {
        this.userGroupId = userGroupId;
        this.isSubmitLoading = true;
        getUserGroupInfo(userGroupId).then(res => {
          this.isSubmitLoading = false;
          this.form = {
            userGroupName,
            description,
            users: res.data.map(item => {
              const { nickname, userId, staffNum } = item;
              return {
                nickname,
                userId,
                staffNum
              };
            })
          };
        }).catch(err => {
          this.isSubmitLoading = false;
        });
      }
    },
    removeTag: function(tag) {
      this.form.users = this.form.users.filter(item => {
        return tag.userId !== item.userId;
      });
    },
    addTag: function() {
      this.isAddDialog = true;
      this.userList = [];
    },
    getDeptList: function() {
      getListDept().then(res => {
        this.treeData = [this.formatTreeData(res.data)];
      });
    },
    formatTreeData: function(data) {
      if (data.children && data.children.length) {
        data.children = data.children.map(item => {
          item.async = true;
          return this.formatTreeData(item);
        });
      }
      if (data.id === 1) data.expanded = true;
      return data;
    },
    tpl(...args) {
      const { 0: node, 2: parent, 3: index } = args;
      const options = {
        tagClass: node.expanded ? 'icon-file-open' : 'icon-file-close',
        checkboxClass: node.checked
          ? node.halfcheck
            ? 'icon-checkbox-indeterminate'
            : 'icon-checkbox-checked'
          : 'icon-checkbox-uncheck',
        expandClass: node.expanded ? 'icon-expand-open' : 'icon-expand-close'
      };
      if (node.isUser) {
        return (
          <span class='tree-node-con'>
            <svg-icon icon-class='icon-tree-user' class='tree-node-tag-icon' />
            <label for={'checkbox-' + node.id} class='tree-node-label'>
              <svg-icon
                icon-class={options.checkboxClass}
                class='tree-node-checkbox-icon'
              />
              <input
                type='checkbox'
                style='display:none'
                id={'checkbox-' + node.id}
                checked={node.checked}
                onChange={e => {
                  if (!node.isTopNode) {
                    // 手动遍历同步所有状态
                    this.$refs.tree.nodeSelected(node);
                    // this.data = [this.resetTreeNode(this.data[0])];
                  }
                }}
              ></input>
              <span
                class={'tree-node-title ' + (node.checked ? 'node-active' : '')}
              >
                {node.name}
              </span>
            </label>
          </span>
        );
      } else {
        return (
          <span class='tree-node-con'>
            {node.id !== 1 ? (
              <svg-icon
                icon-class={options.expandClass}
                class='tree-node-expand-icon'
              />
            ) : (
              ''
            )}
            <svg-icon
              icon-class={options.tagClass}
              class='tree-node-tag-icon'
            />
            <label for={'checkbox-' + node.id} class='tree-node-label'>
              <svg-icon
                icon-class={options.checkboxClass}
                class='tree-node-checkbox-icon'
              />
              <input
                type='checkbox'
                style='display:none'
                id={'checkbox-' + node.id}
                checked={node.checked}
                onChange={e => {
                  // 手动遍历同步所有状态
                  this.$refs.tree.nodeSelected(node);
                  // this.data = [this.resetTreeNode(this.data[0])];
                }}
              ></input>
              <span
                class={'tree-node-title ' + (node.checked ? 'node-active' : '')}
              >
                {node.dptName}
              </span>
            </label>
          </span>
        );
      }
    },
    resetTreeNode: function(data) {
      /* const parentNode = this.$refs.tree.getNodes({ id: id }, this.data, true);
      if (parentNode.children.length) {
        if (parentNode.children.every(item => item.checked)) {
          parentNode.checked = true;
          parentNode.halfcheck = false;
          this.setTreeNode(parentNode);
        }
      } */
      if (data.children.length) {
        const flag = data.children.every(item => {
          return item.checked === data.children[0].checked;
        });
        data.children.forEach(item => {
          this.resetTreeNode(item);
        });
        data.halfcheck = !flag;
        if (flag) {
          data.checked = data.children[0].checked;
        } else {
          data.checked = true;
        }
      }
      return data;
    },
    treeNodeClick: function(node) {
      this.isTreeCheckUser = this.$refs.tree
        .getCheckedNodes(true)
        .some(item => item.isUser);
    },
    treeNodeExpand: function(node) {
      if (node.expanded && node.id !== 1) {
        this.$set(node, 'loading', true);
        getDeptUserList(node.oaId).then(res => {
          if (res.code === 1) {
            if (res.data) {
              let nodes = res.data.map(item => {
                return {
                  isUser: true,
                  id: item.sysUser.oaId,
                  staffNum: item.sysUser.staffNum,
                  // oaId: item.sysUser.oaId,
                  name: item.sysUser.realname,
                  dptName: item.sysDept.dptName,
                  department: node.dptName
                };
              });
              if (node.children) {
                nodes = node.children.concat(
                  nodes.filter(val => {
                    return node.children.every(n => val.id !== n.id);
                  })
                );
              }
              this.$set(node, 'children', nodes);
            } else if (!node.children) {
              this.$set(node, 'expanded', false);
              this.$XyyMessage.warning('当前部门人员为空！');
            }
            this.$set(node, 'loading', false);
          } else {
            this.$XyyMessage.warning(res.msg);
          }
        });
      }
    },
    searchUser: function() {
      if (this.searchVal) {
        this.userListType = 1;
        this.userListLoading = true;
        searchUserList(this.searchVal, 1, 999).then(res => {
          if (res.data) {
            this.listData = res.data.map(item => {
              return {
                isUser: true,
                id: item.sysUser.oaId,
                name: item.sysUser.realname,
                staffNum: item.sysUser.staffNum,
                dptName: item.sysDept.dptName,
                department: item.sysDeptP.dptName
              };
            });
          } else {
            this.listData = [];
          }
          this.userListLoading = false;
        });
      } else {
        this.userListType = 0;
      }
    },
    loadListData() {
      /* const { page, pageSize } = this.listQuery;
      this.userListLoading = true;
      searchUserList(this.searchVal, page + 1, pageSize).then(res => {
        this.userListLoading = false;
        if (res.data && res.data.length) {
          this.listQuery.page = page + 1;
          this.listData = this.listData.concat(res.data);
        } else {
          this.userListNoMore = false;
        }
      }); */
    },
    addChecked: function() {
      let checkList;
      if (this.userListType === 0) {
        checkList = this.$refs.tree.getCheckedNodes(true);
      } else {
        checkList = this.checkUserList.left;
      }
      this.userList = this.userList.concat(
        checkList.filter(
          item => !this.userList.some(val => val.id === item.id) && item.isUser
        )
      );
    },
    removeChecked: function() {
      this.userList = this.userList.filter(
        item => !this.checkUserList.right.some(val => val === item.id)
      );
    },
    addUser: function() {
      if (!this.userList.length) {
        this.$XyyMessage.warning('未选中需要添加的用户组人员');
      } else {
        const oldUsers = this.form.users;
        this.form.users = oldUsers.concat(
          this.userList
            .filter(item => oldUsers.every(val => val.userId !== item.id))
            .map(item => {
              const { name, id, staffNum } = item;
              return { nickname: staffNum + ' ' + name, userId: id, staffNum: staffNum };
            })
        );
        this.isAddDialog = false;
      }
    },
    closePage: function() {
      this.$store
        .dispatch('tagsView/delView', this.$route)
        .then(({ visitedViews }) => {
          this.$router.push({
            path: '/worksheet/userGroupList',
            query: { t: new Date().getTime() }
          });
        });
    },
    submit: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.$store.commit('page/SET_PAGE_LOADING', true);
          if (this.userGroupId) {
            updateUserGroup({ ...this.form, id: this.userGroupId, versionCode: this.versionCode }).then(
              res => {
                if (res.code === 1) {
                  this.$XyyMessage.success({
                    message: '保存成功'
                  });
                  this.closePage();
                } else {
                  this.$XyyMessage.warning(res.msg);
                }
                this.$store.commit('page/SET_PAGE_LOADING', false);
              }
            );
          } else {
            addUserGroup(this.form).then(res => {
              if (res.code === 1) {
                this.$XyyMessage.success({
                  message: '保存成功'
                });
                this.closePage();
              } else {
                this.$XyyMessage.warning(res.msg);
              }
              this.$store.commit('page/SET_PAGE_LOADING', false);
            });
          }
        }
      });
    }
  }
};
</script>

<style lang="scss">
.page {
  .page-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 56px;
    background: #ffffff;
    box-shadow: 0px -2px 2px 0px rgba(145, 144, 144, 0.11);
    filter: blur(0px);
    .footer-right {
      padding: 10px 0;
      text-align: right;
      .el-button {
        height: 36px;
        border-radius: 2px;
        padding: 8px 20px;
        &:last-child {
          margin-right: 20px;
        }
        & + .el-button {
          margin-left: 12px;
        }
      }
      .el-button--default {
        border: 1px solid #e4e4eb;
      }
    }
  }
}

.user-group-add_page {
  padding: 20px;
  overflow-y: auto;
  font-family: PingFangSC;
  .el-form {
    margin-bottom: 130px;
    .el-form-item {
      height: 36px;
      margin-bottom: 20px;
      .el-form-item__label {
        line-height: 36px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 400;
        color: #292933;
        &::before {
          color: #ff0000;
          margin-right: 1px;
        }
      }
      .el-form-item__content {
        width: 430px;
        line-height: 36px;
        .el-input {
          .el-input__inner {
            height: 36px;
            line-height: 36px;
            padding: 0 12px;
            border-radius: 2px;
            border: 1px solid #e4e4eb;
          }
        }
        .el-textarea {
          textarea {
            resize: none;
            border-radius: 2px;
            border: 1px solid #e4e4eb;
          }
          .el-input__count {
            right: 12px;
            bottom: 12px;
            width: 50px;
            height: 15px;
            font-size: 12px;
            font-family: PingFangSC;
            font-weight: 400;
            color: #aeaebf;
            line-height: 14px;
          }
        }
        .el-form-item__error {
          padding: 0;
          height: 17px;
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: 400;
          color: #ff3024;
          line-height: 17px;
        }
      }
    }
    .itme-content_tag {
      height: auto;
      .el-form-item__content {
        width: auto;
        margin-bottom: -12px;
        .el-form-item__error {
          margin-top: -12px;
        }
      }
    }
  }
  .el-tag {
    position: relative;
    height: 36px;
    line-height: 34px;
    padding: 0 33px 0 12px;
    background: #f5f7fa;
    border-radius: 2px;
    border: 1px solid #e4e4eb;
    margin-right: 12px;
    margin-bottom: 12px;
    font-size: 14px;
    font-family: PingFangSC;
    color: #575766;
    &:nth-last-child(2) {
      margin-right: 8px;
    }
    .el-icon-close {
      position: absolute;
      top: 9px;
      right: 10px;
    }
  }
  .add-tag_button {
    height: 36px;
    border-radius: 2px;
    border: 1px solid #e4e4eb;
    padding: 8px 12px;
    margin-bottom: 12px;
    .add-tag_icon {
      margin-right: 8px;
    }
  }
  .el-dialog {
    .el-dialog__body {
      padding: 10px 20px;
    }
  }
}
.xyy-transfer {
  font-size: 14px;
  .xyy-transfer-panel {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
    display: inline-block;
    vertical-align: middle;
    max-height: 100%;
    box-sizing: border-box;
    position: relative;
    .xyy-transfer-panel_header {
      height: 55px;
      line-height: 55px;
      background: #f5f7fa;
      margin: 0;
      padding: 0 20px;
      border-bottom: 1px solid #ebeef5;
      box-sizing: border-box;
      color: #000;
      .el-input {
        height: 32px;
        input {
          height: 100%;
          background: #ffffff;
          border-radius: 2px;
          border: 1px solid #e4e4eb;
        }
        .el-icon-search {
          cursor: pointer;
        }
      }
      .xyy-transfer-title {
        font-weight: 500;
        color: #292933;
      }
    }
    .xyy-transfer-panel_body {
      height: 346px;
      overflow-y: auto;
    }
  }
  .xyy-transfer_buttons {
    display: inline-block;
    vertical-align: middle;
    padding: 0 30px;
    .xyy-transfer_button {
      display: block;
      margin: 0 auto;
      padding: 10px;
      border-radius: 50%;
      &:first-child {
        margin-bottom: 10px;
      }
    }
  }
  .xyy-transfer-panel_left {
    width: 340px;
  }
  .xyy-transfer-panel_right {
    width: 390px;
  }
}
/* tree */
.xyy-tree {
  padding: 11px 20px;
  // overflow: hidden;
  margin: 0;
  .tree-node-el {
    .tree-expand {
      position: absolute;
      top: 2px;
      left: -30px;
      opacity: 0;
      z-index: 9;
    }
    .tree-node-con {
      white-space: nowrap;
      .tree-node-expand-icon {
        position: absolute;
        top: 2px;
        left: -30px;
        background: #fff;
      }
      .tree-node-label {
        margin-left: 10px;
        font-weight: normal;
        .tree-node-checkbox-icon {
          cursor: pointer;
        }
        .tree-node-title {
          margin-left: 8px;
          font-size: 14px;
          font-weight: 400;
          color: #575766;
          cursor: pointer;
          white-space: nowrap;
        }
        .top-title {
          font-weight: 500;
          color: #292933;
          cursor: default;
        }
        .node-active {
          color: #40a3b8;
        }
      }
      .tree-node-tag-icon {
        width: 16px;
        height: 16px;
      }
    }
  }
  ul {
    padding-top: 8px;
  }
  li {
    padding: 9px 5px 5px 15px;
    &::after,
    &::before {
      border-color: #d8dce6 !important;
    }
  }
  .inputCheck {
    display: none;
  }
}
/* right user list */
.user-list_right {
  width: 100%;
  height: 100%;
  overflow: auto;
  .user-list-msg {
    text-align: center;
  }
}
.user-list {
  padding: 20px;
  .el-checkbox {
    width: 100%;
    height: 20px;
    line-height: 20px;
    margin-bottom: 13px;
    .el-checkbox__label span {
      display: inline-block;
      vertical-align: middle;
      width: 90px;
      margin-right: 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>

