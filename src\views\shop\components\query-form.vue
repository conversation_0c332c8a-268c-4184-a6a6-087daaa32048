<template>
  <el-form ref="form" :model="form" class="ybm-common-search">
    <el-row :gutter="20" type="flex" justify="start" style="flex-wrap: wrap">
      <el-form-item
        v-for="(item, index) in options"
        :key="index"
        :class="item.className"
        :label="item.label"
        :label-width="item.labelWidth"
        class="form-item-box"
        style="margin-left: 16px;"
      >
        <!--输入框-->
        <el-input
          v-if="item.type === 'default'"
          v-model.trim="item.value"
          :placeholder="item.placeholder"
          :maxlength="item.maxlength"
          :size="item.size || 'small'"
          :disabled="item.disabled"
          clearable
        ></el-input>
        <!--输入框-->
        <!-- <xyy-number
            v-if="item.type === 'inputNumber'"
            v-model="item.value"
            :placeholder="item.placeholder"
            :precision="item.precision || 0"
            :disabled="item.disabled"
            clearable
        ></xyy-number>-->
        <!--远程搜索-->
        <el-autocomplete
          v-if="item.type == 'fetch'"
          v-model="item.value"
          :fetch-suggestions="item.fetchQuery"
          :maxlength="item.maxlength"
          :placeholder="item.placeholder"
          :size="item.size || 'small'"
          :trigger-on-focus="false"
          clearable
          @select="item.select"
          @input="handleAutoInput(item)"
        ></el-autocomplete>
        <!--多选-->
        <el-select
          v-if="item.type == 'list'"
          v-model="item.value"
          :size="item.size || 'small'"
          :clearable="item.clearable"
          placeholder="请选择"
        >
          <el-option
            v-for="(col) in item.options"
            :key="col.value"
            :label="col.label"
            :value="col.value"
          ></el-option>
        </el-select>
        <el-select
          v-if="item.type == 'list' && item.change"
          v-model="item.value"
          :clearable="item.clearable"
          :size="item.size || 'small'"
          placeholder="请选择"
          @change="item.change"
        >
          <el-option
            v-for="(col) in item.options"
            :key="col.value"
            :label="col.label"
            :value="col.value"
          ></el-option>
        </el-select>
        <!--单个日期-->
        <el-date-picker
          v-else-if="item.type === 'date'"
          v-model="item.value"
          :size="item.size || 'small'"
        ></el-date-picker>
        <!--区间日期-->
        <el-date-picker
          v-else-if="item.type === 'daterange' || item.type === 'datetimerange'"
          v-model="item.value"
          :type="item.type"
          :size="item.size || 'small'"
          :clearable="item.clearable"
          :range-separator="(item.separator || '-')"
          :start-placeholder="(item.startPlaceholder || '开始日期')"
          :end-placeholder="(item.endPlaceholder || '结束日期')"
          :ref="item.key"
          :value-format="item.valueFormat || 'yyyy-MM-dd'"
          @change="filterDate(item)"
        ></el-date-picker>
        <slot v-else-if="item.type === 'slot'" :name="item.slotName"></slot>
        <div v-else-if="item.type === 'button'" class="search-btns">
          <span v-for="(col, ikey) in item.btns" :key="ikey" class="btn-col">
            <el-button
              v-if="col.key === 'search'"
              :type="col.type"
              :size="col.size"
              @click="outPutData"
            >{{ col.name }}</el-button>
            <el-button
              v-else-if="col.key === 'reset'"
              :type="col.type"
              :size="col.size"
              @click="resetData"
            >{{ col.name }}</el-button>
            <el-button
              v-else-if="col.key === 'custom'"
              :type="col.type"
              :size="col.size"
              @click="exportData"
            >{{ col.name }}</el-button>
          </span>
        </div>
      </el-form-item>
    </el-row>
  </el-form>
</template>
<script>
// import xyyNumber from './xyy-number';
export default {
  // components: { xyyNumber },
  props: {
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      copyData: '',
      form: {}
    };
  },
  created() {
    this.copyData = this.getCopyData(this.options);
  },
  methods: {
    /* 向外传参 */
    outPutData() {
      const formData = this.handleOptionVal(this.options);
      if (formData) {
        this.$emit('fetch-data', formData);
      }
    },
    /* 重置 */
    resetData() {
      this.options.forEach(item => {
        item.value = this.copyData[item.key];
      });
      const formData = this.getCopyData(this.options);
      if (formData) {
        this.$emit('reset-opt', formData);
      }
    },
    exportData() {
      const formData = this.getCopyData(this.options);
      if (formData) {
        this.$emit('export-excel', formData);
      }
    },
    /* 格式化要传出的参数 */
    handleOptionVal(formData) {
      const params = {};
      for (let i = 0; i < formData.length; i++) {
        const key = formData[i].key;
        const type = formData[i].type;
        if (key) {
          if (formData[i].required && !formData[i].value) {
            // 校验必填值
            return this.$message.warning(
              formData[i].notify || `请选择${formData[i].label}`
            );
          } else if (
            formData[i].required &&
            (type == 'datetimerange' ||
              type == 'daterange' ||
              type === 'date') &&
            !formData[i].value.length
          ) {
            return this.$message.warning(
              formData[i].notify || `请选择${formData[i].label}`
            );
          } else {
            params[key] = formData[i].value;
          }
        }
      }
      return params;
    },
    /* 格式化传入的参数*/
    getCopyData(formData) {
      const params = {};
      for (let i = 0; i < formData.length; i++) {
        if (formData[i].key) {
          params[formData[i].key] = formData[i].value;
        }
      }
      // console.log(params);
      return params;
    },
    /* 选择时间的校验*/
    filterDate(item) {
      const { value, timeInterval, moreThanToday } = item;
      const now = new Date().getTime();
      if (!moreThanToday && new Date(value[0]).getTime() > now) {
        this.$message({
          message: '开始时间不能大于今日,请重新选择！',
          type: 'warning'
        });
        item.value = item.defaultValue;
        this.$refs[item.key][0].focus();
        return;
      } else if (!moreThanToday && new Date(value[1]).getTime() > now) {
        this.$message({
          message: '结束时间不能大于今日,请重新选择！',
          type: 'warning'
        });
        item.value = item.defaultValue;
        this.$refs[item.key][0].focus();
        return;
      }
      if (
        timeInterval &&
        new Date(value[1]).getTime() - new Date(value[0]).getTime() >
          timeInterval * 3600 * 24 * 1000
      ) {
        this.$message({
          message: `筛选时间间隔不能超过${timeInterval}天，请重新选择！`,
          type: 'warning'
        });
        item.value = item.defaultValue;
        this.$refs[item.key][0].focus();
        return;
      }
    },
    handleAutoInput(item) {
      // el-autocomplete 组件的清除
      let val = item.value;
      if (val) {
        val = val.trim();
      }
      if (val === '') {
        item.clear && item.clear();
      }
    }
  }
};
</script>
<style lang="scss">
.ybm-common-search {
  // .el-input__inner,
  // .el-select,
  // .el-autocomplete {
  //   width: 100%;
  // }
  .form-item-box {
    padding-bottom: 8px;
    margin-bottom: 0 !important;
    /deep/ .el-form-item__label {
      font-weight: 400;
    }
    /deep/ .el-input--suffix {
      height: 36px;
      /deep/ input {
        height: 36px;
      }
    }
    /deep/ .el-range-editor--small.el-input__inner {
      height: 36px;
      /deep/ input {
        line-height: 34px;
      }
    }
  }
  .grid-content {
    padding-top: 16px;
  }
  .label-item {
    display: inline-block;
    line-height: 36px;
    vertical-align: middle;
    font-size: 14px;
    // padding-bottom: 6px;
    color: #606266 !important;
    font-weight: 400 !important;
  }
  .label-button {
    text-indent: -100px;
    overflow: hidden;
  }
  .right {
    float: right;
  }
  .search-btns {
    // flex-grow: 1;
    // text-align: right;
    .el-button--small {
      padding: 10px 15px;
    }
  }
  .btn-col {
    margin-right: 10px;
    /deep/ span {
      font-size: 14px;
    }
  }
}
</style>
