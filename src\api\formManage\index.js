import request from '@/utils/request';
import { formData } from '@/utils/index';

// ------------------------表单应用接口--------------------------
/**
 *工单量相关导出相关报表
 * @param {params}查询参数
 */
export function exportData(params) {
  return request({
    url: '/workOrder/export/getWorkorderQuantityExport',
    method: 'get',
    params: params
  });
}
/**
 *获取表单应用列表
 * @param {Object}查询参数
 */
export function getFormList(params) {
  return request({
    url: '/form/base/listFormByFuzzySearch',
    method: 'get',
    params: params
  });
}

/**
 *切换表单应用状态
 * @param {Object} 状态值 id status
 */
export function changeStatus(params) {
  return request({
    url: 'form/base/changeStatus',
    method: 'put',
    data: formData(params)
  });
}

/**
 *复制表单应用
 * @param {*} 表单id
 */
export function copyForm(id) {
  return request({
    url: 'form/base/copyForm',
    method: 'post',
    data: formData({ id })
  });
}

/**
 *删除表单应用
 * @param {*} 表单id
 */
export function delForm(id) {
  return request({
    url: 'form/base/delete',
    method: 'delete',
    data: formData({ id })
  });
}

//  基础设置相关
/**
 *根据id查询表单基础设置信息
 * @param {id} 表单id
 */
export function getFormById(id) {
  return request({
    url: 'form/base/getFormById',
    method: 'get',
    params: { id }
  });
}

/**
 *新增表单基础设置信息
 * @param {object} 实体
 */
export function addBaseForm(object) {
  return request({
    url: 'form/base/save',
    method: 'post',
    data: formData(object)
  });
}

/**
 *更新表单基础设置信息
 * @param {object} 实体
 */
export function updateBaseForm(object) {
  return request({
    url: 'form/base/update',
    method: 'put',
    data: formData(object)
  });
}
// 流程相关
/**
 *通过表单id获取所有节点列表
 * @param {formId} 表单id
 */
export function getListNodesByFormId(formId) {
  return request({
    url: 'form/node/listNodesByFormId',
    method: 'get',
    params: { formId }
  });
}

/**
 *根据节点id查询表单节点信息
 * @param {id} 表单id
 */
export function getNodeInfoById(id) {
  return request({
    url: 'form/node/getNodeInfoById',
    method: 'get',
    params: { id }
  });
}

/**
 * 新增表单流程节点
 * @param {*} formId
 */
export function addFlowNode(formId) {
  return request({
    url: 'form/node/saveFlowNode',
    method: 'post',
    data: formData({ formId })
  });
}

/**
 * 删除表单流程节点
 * @param {*} id
 */
export function delFlowNode(id) {
  return request({
    url: 'form/node/delete',
    method: 'delete',
    data: formData({ id })
  });
}

/**
 * 移动节点
 * @param {节点数据} datas
 */
export function moveFlowNode(datas) {
  return request({
    url: '/form/node/updateNodeSort',
    method: 'put',
    data: datas
  });
}

/**
 * 拖拽节点
 * @param {{id: number|string}[]} data
 */
export function dragFlowNode(data) {
  return request({
    url: '/form/node/updateNodeSortDrag',
    method: 'put',
    data
  });
}

/**
 * 复制流程节点
 * @param {节点id} nodeId
 */
export function copyFlowNode(nodeId) {
  return request({
    url: '/form/node/copyNode',
    method: 'post',
    data: formData({ nodeId })
  });
}

/**
 * 查询全部模板列表
 */
export function getListTemplate(params) {
  return request({
    url: 'template/base/listTemplate',
    method: 'get',
    params: { status: 1, type: params.type }
  });
}

/**
 * 查询用户组列表
 * @param {object} 查询参数实体 pageSize,pageNum
 */
export function getUserList(object) {
  return request({
    url: 'user/group/listUserGroupWithNoDisable',
    method: 'get',
    params: object
  });
}

/**
 * 根据组ID获取用户信息
 * @param {userGroupId,type:工单分配，工单转移，工单重启 = 2,其他 = 1 }
 */
export function getUserInfo(userGroupId) {
  return request({
    url: 'user/group/listUserByUserGroupId',
    method: 'get',
    params: { userGroupId, type: 1 }
  });
}

/**
 * 根据模板id查询模板字段列表
 * @param {object} templateId needSystemField 预览时false,字段时true
 */
export function getFiledListByTemplateId(object) {
  return request({
    url: 'template/field/listFieldByTemplateId',
    method: 'get',
    params: object
  });
}
/**
 *根据表单id查询除当前节点外的表单节点列表
 * @param {String, String} formId 表单id nodeId 节点id
 */
export function getListNodesByFormIdExcludeCurrentNode({ formId, nodeId }) {
  return request({
    url: 'form/node/listNodesByFormIdExcludeCurrentNode',
    method: 'get',
    params: { formId, nodeId }
  });
}

/**
 * 新增流程节点
 * @param {*} object 节点信息实体
 */
export function saveNode(object) {
  return request({
    url: 'form/node/save',
    method: 'post',
    data: formData(object)
  });
}

/**
 * 更新流程节点
 * @param {*} object 节点信息实体
 */
export function updateNode(object) {
  return request({
    url: 'form/node/update',
    method: 'put',
    data: object
  });
}

// 通知设置相关

/**
 *通过表单id查询通知设置信息
 * @param {object} 实体
 */
export function getFormNotifyByFormId(id) {
  return request({
    url: 'form/notify/getFormNotifyByFormId',
    method: 'get',
    params: { formId: id }
  });
}

/**
 *新增配置信息
 * @param {object} 实体
 */
export function addNotify(object) {
  return request({
    url: 'form/notify/save',
    method: 'post',
    data: formData(object)
  });
}

/**
 *更新配置信息
 * @param {object} 实体
 */
export function updateNotify(object) {
  return request({
    url: 'form/notify/update',
    method: 'put',
    data: formData(object)
  });
}

// -------------------表单类型接口-----------------------------------------

/**
 *获取表单类型列表 --带分页
 * @param {Object}查询参数 pageSize pageNum
 */
export function getFormTypeList(params) {
  return request({
    url: 'form/type/listFormTypeForPage',
    method: 'get',
    params: params
  });
}

/**
 *获取表单类型列表 --全部
 * @param {params} {status:1 }不包含禁用项
 */
export function getAllFormTypeList(params) {
  return request({
    url: 'form/type/listFormType',
    method: 'get',
    params: params
  });
}

/**
 *切换表单类型应用状态
 * @param {Object} 状态值 id status
 */
export function changeTypeStatus(params) {
  return request({
    url: 'form/type/changeStatus',
    method: 'put',
    data: formData(params)
  });
}

/**
 *复制表单类型
 * @param {*} 表单id
 */
export function copyFormType(id) {
  return request({
    url: 'form/type/copyFormType',
    method: 'post',
    data: formData({ id })
  });
}

/**
 *删除表单类型
 * @param {*} 表单id
 */
export function delFormType(id) {
  return request({
    url: 'form/type/delete',
    method: 'delete',
    data: formData({ id })
  });
}

/**
 *新增表单类型
 * @param {*} 类型实体
 */
export function addFormType(object) {
  return request({
    url: 'form/type/save',
    method: 'post',
    data: formData(object)
  });
}

/**
 *更新表单类型
 * @param {*} 类型实体
 */
export function updateFormType(object) {
  return request({
    url: 'form/type/update',
    method: 'put',
    data: formData(object)
  });
}

/** v 1.7 *********************************/
export function getNodesByType(params) {
  return request({
    url: '/node/pool/listNodesByNodeType',
    method: 'get',
    params
  });
}
/**
 * 添加节点
 */
export function addCustomNode(data) {
  return request({
    url: '/form/node/saveCustomNode',
    method: 'post',
    data: formData(data)
  });
}

/**
 * 获取发起节点的模板字段数据
 */
export function getTemplateFields(params) {
  return request({
    url: '/form/node/listFieldForFlowCondition',
    method: 'get',
    params
  });
}
/**
 * 获取发起节点外模板字段数据
 */
export function getOtherTemplateFields(params) {
  return request({
    url: '/template/field/listFieldByTemplateCode',
    method: 'get',
    params
  });
}

export function saveRefNodes(data, formId) {
  return request({
    url: `/form/node/saveListForRefNode?formId=${formId}`,
    method: 'post',
    data
  });
}
