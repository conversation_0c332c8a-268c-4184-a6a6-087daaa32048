import request from '@/utils/request-im';

/**
 *人员管理列表&&列表导出接口
 * @param {Object}查询参数
 */
export function getPerMan(params) {
  return request({
    url: '/staff/list',
    method: 'get',
    params: params
  });
}

/**
 * 用户名模糊查询
 * @param {Object}查询参数
 */
export function fuzzyQuery(params) {
  return request({
    url: '/staff/oauser',
    method: 'get',
    params: params
  });
}


/**
 * 新增、修改页面返参
 * @param {Object}查询参数
 */
export function getAddModify(params) {
  return request({
    url: '/staff/detail',
    method: 'get',
    params: params
  });
}

/**
 * 人员管理新增或修改（保存）
 * @param {Object}查询参数
 */
export function newModified(params) {
  return request({
    url: '/staff/saveOrUpdate',
    method: 'post',
    data: params
  });
}


/**
 *上传头像
 * @param {Object}查询参数
 */
export function uploadPictures(params) {
  return request({
    url: '/staff/uploadimg',
    method: 'post',
    data: params
  });
}

/**
 *添加人员-用户名模糊查询
 * @param {Object}查询参数
 */
export function userNameQuery(params) {
  return request({
    url: '/staff/uploadimg',
    method: 'post',
    params: params
  });
}

/**
 *添加人员-角色查询
 * @param {Object}查询参数
 */
export function getRole(params) {
  return request({
    url: '/staff/roles',
    method: 'get',
  });
}

/**
 *添加人员-所属渠道
 * @param {Object}查询参数
 */
export function getSources(params) {
  return request({
    url: '/staff/merchants',
    method: 'get',
  });
}
