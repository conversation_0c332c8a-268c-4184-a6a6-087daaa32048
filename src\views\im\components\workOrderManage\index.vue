<!-- im工单管理首页 -->
<template>
  <div>
    <el-tabs v-model="activeName"
             @tab-click="tabsClick">
      <el-tab-pane label="新建工单"
                   name="first">
        <addorder :erectMode="true"
                  ref="workOrderManage"></addorder>
      </el-tab-pane>
      <el-tab-pane label="历史工单"
                   name="second">
        <div class="orderno-search-box">
          <el-select placeholder="请选择"
                     v-model="searchtype"
                     @change="selectEvents">
            <el-option :key="item.field"
                       :label="item.value"
                       :value="item.field"
                       v-for="item in conditionsOptions"></el-option>
          </el-select>
          <el-input :placeholder="searchvalHint"
                    v-model="searchval"></el-input>
          <el-button @click="query"
                     type="primary">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>

        <el-radio-group v-model="orderState"
                        @change="changeListAllStateWorkorder">
          <el-radio-button label="0">全部</el-radio-button>
          <el-radio-button label="1">未完成</el-radio-button>
          <el-radio-button label="2">已完成</el-radio-button>
        </el-radio-group>

        <xyy-table :data="list"
                   :col="col"
                   :list-query="listQuery"
                   :pagination="pagination"
                   @row-dblclick="operationClick"
                   @get-data="getListAllStateWorkorder"
                   @sortChange="sortChange">
          <!-- 问题分类 -->
          <template slot="1164724692221825024"
                    slot-scope="{col}">
            <el-table-column :key="col.index"
                             :prop="col.index"
                             :label="col.name"
                             :min-width="col.width"
                             :show-overflow-tooltip="true">
              <template slot-scope="item">
                <span>{{ item.row['1164724692221825024']? item.row['1164724692221825024'].option_name:'' }}</span>
              </template>
            </el-table-column>
          </template>
          <!-- 当前状态 -->
          <template slot="currentState"
                    slot-scope="{col}">
            <el-table-column :key="col.index"
                             :prop="col.index"
                             :width="col.width"
                             :label="col.name">
              <template slot-scope="{row}">
                <span v-if="row.currentState === 0"
                      class="currentState1">待领取</span>
                <span v-else-if="row.currentState === 1"
                      class="currentState1">处理中</span>
                <span v-else-if="row.currentState === 2"
                      class="currentState2">已结单</span>
                <span v-else-if="row.currentState === 3"
                      class="currentState3">退回</span>
                <span v-else-if="row.currentState === 4"
                      class="currentState4">异常</span>
                <span v-else-if="row.currentState === 5"
                      class="currentState5">作废</span>
                <span v-else
                      class="currentState0">待领取</span>
              </template>
            </el-table-column>
          </template>
          <!-- 工单发起时间 -->
          <template slot="gmtCreate"
                    slot-scope="{col}">
            <el-table-column :key="col.index"
                             :prop="col.index"
                             :label="col.name"
                             :width="col.width"
                             :formatter="getFormatDate"
                             :sortable="col.sortable || false" />
          </template>
        </xyy-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
// 例如：import 《组件名称》 from '《组件路径》';
import {
  listAllStateWorkorder,
  imWorkOrderSelectDefaultField
} from '@/api/mySheetManage';
import { adapterList } from '@/utils/tools.js';
import Preview from '~/Fields/preview';
import addorder from '@/views/work-sheet/new-sheet';
export default {
  components: { Preview, addorder },
  data () {
    return {
      activeName: 'first',
      orderState: '0',
      orderStateMap: ['', '0,1,3,4', '2,5'], // 工单状态对应code
      list: [],
      listQuery: {
        // 分页数
        page: 1,
        pageSize: 10,
        total: 0
      },
      col: [
        // 列配置
        {
          index: '1164724692221825024',
          name: '问题分类',
          width: 150,
          slot: true
        },
        {
          index: 'currentState',
          name: '当前状态',
          width: 90,
          slot: true
        },
        {
          index: 'gmtCreate',
          name: '工单发起时间',
          width: 200,
          slot: true,
          sortable: 'custom'
        }
      ],
      pagination: {
        // 分页配置
        background: true,
        layout: 'prev, pager, next',
        pageSizes: [10, 20, 30, 40, 50, 100],
        small: true,
        pagerCount: 5
      },
      propField: {
        prop: 'gmt_create', //排序字段 gmtCreate(工单发起时间)
        orderBy: undefined
      }, //排序字段
      containerid: '',
      conditionsOptions: [
        // {
        //   // 选择组
        //   value: 'workorder_num',
        //   label: '工单编号'
        // },
        // {
        //   value: 's1164456058157142016',
        //   label: '客户名称'
        // },
        // {
        //   value: 's1176037234466492416',
        //   label: '订单编号'
        // }
      ],
      searchvalHint: '请输入',
      searchval: '',
      searchtype: '',
      isCheck: true, //点击查询按钮，是否传入im_client_id
      isQuery: false
    };
  },
  // 方法集合
  methods: {
    // 点击查询按钮事件
    query () {
      if (this.searchtype === '') {
        this.$XyyMessage.warning(`请选择查询条件`);
        return;
      }
      if (this.searchval === '') {
        this.$XyyMessage.warning(`请输入查询内容`);
        return;
      }
      this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      };
      this.isCheck = false;
      this.isQuery = true;
      this.getListAllStateWorkorder(this.listQuery);
    },
    // 重置
    reset () {
      this.resetForm();
      this.getListAllStateWorkorder(this.listQuery);
    },
    resetForm () {
      (this.searchval = ''),
        (this.searchtype = ''),
        (this.searchvalHint = '请输入');
      this.isQuery = false;
      this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      };
      this.isCheck = true;
    },
    // 搜索条件选择角色时
    selectEvents (val) {
      this.conditionsOptions.forEach((item, index) => {
        if (item.field === val) {
          this.searchvalHint = '请输入' + item.value;
        }
      });
    },
    init (newval) {
      this.resetForm();
      //获取历史工单查询条件
      this.getImWorkOrderSelectDefaultField();
      // 获取历史工单数据
      this.getListAllStateWorkorder(this.listQuery);

      const SheetItem = this.$refs.workOrderManage.getSheetItem(); // 获取现有表单内容

      // 缓存表单内容到store
      if (
        this.containerid &&
        SheetItem &&
        SheetItem.sheetTypeVal &&
        SheetItem.sheetPre &&
        SheetItem.sheetPre.length
      ) {
        this.$store.commit('imProperty/SET_IMSheetItem', {
          name: this.containerid,
          value: Object.assign({}, SheetItem)
        });
      }

      const Store_SheetItem = this.$store.getters.sheetItem(newval); // 获取store表单内容
      if (
        Store_SheetItem &&
        Store_SheetItem.sheetTypeVal &&
        Store_SheetItem.sheetPre &&
        Store_SheetItem.sheetPre.length
      ) {
        // 有缓存 --获取store 数据到表单
        this.$refs.workOrderManage.setSheetItem(
          Object.assign({}, Store_SheetItem)
        );
      } else {
        // 无缓存 --关联工单地域模板
        const data = {
          areaName: this.$store.getters.chat.ipprovince // 客户地域关联工单模板地域
        };
        this.$refs.workOrderManage.resetTypeVal(data); //  重置 关联工单地域模板
      }
      this.containerid = newval;
    },

    /**
     * 获取历史工单数据
     */
    getListAllStateWorkorder (listQuery) {
      const that = this;
      if (!that.$store.getters.khid) {
        return;
      }
      let id = 'im_client_id';
      if (Number(that.$store.getters.chat.appId) === 1001) {
        id = 'im_sale_id';
      }
      const { page, pageSize } = listQuery || that.listQuery;

      let preParam = {
        pageNum: page,
        pageSize: pageSize,
        queryParameterJson: {
          current_state: that.orderStateMap[that.orderState]
          // [id]: Number(that.$store.getters.khid) || undefined
          // im_app_id: Number(that.$store.getters.chat.appId) || undefined
          // [that.searchtype]: that.searchval
        }
      };

      if (that.searchtype) {
        preParam.queryParameterJson[that.searchtype] = that.searchval;
      }

      if (that.isCheck && !that.isQuery) {
        preParam.queryParameterJson[id] =
          Number(that.$store.getters.khid) || undefined;
      }

      if (that.propField.orderBy) {
        preParam.prop = that.propField.prop;
        preParam.order = that.propField.orderBy;
      }

      listAllStateWorkorder(preParam)
        .then(response => {
          if (response.msg === 'success') {
            const { list, total, pageSize, pageNum } = response.data;
            if (list) {
              that.list = adapterList(list);
              that.listQuery = {
                page: pageNum,
                pageSize: pageSize,
                total: total
              };
            }
          } else {
            that.$XyyMessage.error(response.msg);
          }
        })
        .catch(function (error) {
          that.$XyyMessage.error('数据异常：' + error.message);
        });
    },
    changeListAllStateWorkorder () {
      this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      };
      this.isCheck = true;
      this.getListAllStateWorkorder(this.listQuery);
    },
    /**
     * 历史工单查询条件
     */
    getImWorkOrderSelectDefaultField () {
      imWorkOrderSelectDefaultField()
        .then(response => {
          if (response.msg === 'success') {
            if (response.data) {
              this.conditionsOptions = response.data;
            }
          } else {
            this.$XyyMessage.error(response.msg);
            // this.loading.close();
          }
        })
        .catch(function (error) {
          this.$XyyMessage.error(error);
        });
    },

    /**
     * 切换tabs事件
     */
    tabsClick (e) {
      if (e.name == 'second') {
        // this.isCheck = true;
        this.getListAllStateWorkorder(this.listQuery);
      }
    },

    /**
     * 去工单详情
     */
    operationClick (row) {
      if (row.seeDetails === false) {
        this.$XyyMessage.warning('您无权查看该订单');
        return;
      }
      this.$router.push({
        path: '/workStatus' + row.formTypeId + '/sheetDetail/' + row.id,
        query: {
          id: row.id,
          type: 'wordSheet',
          currentUrl: this.currentUrl
        }
      });
    },

    /**
     * 自定义排序
     * 重新请求列表
     */
    sortChange ({ column, prop, order }) {
      //排序方式
      switch (order) {
        case 'ascending':
          this.propField.orderBy = 'asc';
          break;
        case 'descending':
          this.propField.orderBy = 'desc';
          break;
        default:
          this.propField.orderBy = '';
          break;
      }
      // this.isCheck = ture;
      //请求数据
      this.getListAllStateWorkorder(this.listQuery);
    },

    /**
     * 时间格式转换
     */
    getFormatDate (row, column, cellValue, index) {
      if (!cellValue) {
        return '无';
      } else {
        return new Date(cellValue + 8 * 3600 * 1000)
          .toJSON()
          .substr(0, 19)
          .replace('T', ' ');
      }
    }
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted () {
    // this.getListAllStateWorkorder(); // 获取全部工单列表
    this.containerid = this.$store.getters.containerid;
    this.init(this.$store.getters.containerid);
  },
  beforeCreate () { }, // 生命周期 - 创建之前
  beforeMount () { }, // 生命周期 - 挂载之前
  beforeUpdate () { }, // 生命周期 - 更新之前
  updated () { }, // 生命周期 - 更新之后
  beforeDestroy () { }, // 生命周期 - 销毁之前
  destroyed () { }, // 生命周期 - 销毁完成
  activated () { } // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang='scss' scoped>
/deep/.el-radio-button__inner {
  padding: 12px 30px;
}
.table-containter {
  margin-top: 10px;
}

.selectItem {
  margin-bottom: 10px;
}

/deep/.preview .single {
  margin: 10px;
}

.orderno-search-box {
  width: 100%;
  margin: 0 0 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .el-select {
    min-width: 105px;
  }

  .el-input {
    margin-left: 5px;
  }

  .el-button {
    margin-left: 5px;
  }
}
</style>