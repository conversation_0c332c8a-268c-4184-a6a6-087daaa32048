<template></template>

<script>
export default {
  name: 'Loading',
  data() {
    return {
      loading: null
    };
  },
  methods: {
    openLoading() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0)'
      });
      return loading;
    },
    closeLoading(loading) {
      loading.close();
    }
  }
};
</script>

<style scoped>
</style>
