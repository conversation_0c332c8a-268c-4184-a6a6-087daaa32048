
export function sayingMessage () {
  return [
    {
      direction: 2,
      id: 1,
      type: 1,
      content:
        '啊实阿松大阿松大阿松大阿萨大厦大厦大厦大厦大厦大厦大厦大稍等阿松大阿松大阿松大阿松大稍等打实<img src="/static/plugins/emoticons/images/13.gif" border="0" alt="" /><img src="/static/plugins/emoticons/images/31.gif" border="0" alt="" />',
      ctime: new Date().toLocaleString()
    },
    {
      direction: 1,
      id: 2,
      type: 1,
      content:
        '啊实阿松大阿松大阿松大阿萨大厦大厦大厦大厦大厦大厦大厦大稍等阿松大阿松大阿松大阿松大稍等打实<img src="/static/plugins/emoticons/images/13.gif" border="0" alt="" /><img src="/static/plugins/emoticons/images/31.gif" border="0" alt="" />',
      ctime: new Date().toLocaleString()
    },
    {
      direction: 2,
      id: 3,
      type: 1,
      content:
        '啊实阿松大阿松大阿松大阿萨大厦大厦大厦大厦大厦大厦大厦大稍等阿松大阿松大阿松大阿松大稍等打实<img src="/static/plugins/emoticons/images/13.gif" border="0" alt="" /><img src="/static/plugins/emoticons/images/31.gif" border="0" alt="" />',
      ctime: new Date().toLocaleString()
    },
    {
      direction: 2,
      id: 4,
      type: 2,
      content:
        '这是谁？<img src="https://dss0.bdstatic.com/70cFuHSh_Q1YnxGkpoWK1HF6hhy/it/u=3209370120,2008812818&fm=26&gp=0.jpg" border="0" alt="" />',
      ctime: new Date().toLocaleString()
    },
    {
      direction: 1,
      id: 5,
      type: 1,
      content:
        '啊实阿松大阿松大阿松大阿萨大厦大厦大厦大厦大厦大厦大厦大稍等阿松大阿松大阿松大阿松大稍等打实<img src="/static/plugins/emoticons/images/13.gif" border="0" alt="" /><img src="/static/plugins/emoticons/images/31.gif" border="0" alt="" />',
      ctime: new Date().toLocaleString()
    }
  ]
}

export function userdataAction () {
  return [
    {
      avatar:
        'https://dss0.bdstatic.com/70cFuHSh_Q1YnxGkpoWK1HF6hhy/it/u=3209370120,2008812818&fm=26&gp=0.jpg',
      name: '赵磊',
      time: '10-12 11:23',
      content: '你好，我想咨询一下药品什么鬼的东西',
      state: '会话中',
      select: false
    },
    {
      avatar:
        'https://dss0.bdstatic.com/70cFuHSh_Q1YnxGkpoWK1HF6hhy/it/u=3209370120,2008812818&fm=26&gp=0.jpg',
      name: '赵大宝',
      time: '10-12 11:23',
      content: '好的',
      state: '会话中',
      select: false
    },
    {
      avatar:
        'https://dss0.bdstatic.com/70cFuHSh_Q1YnxGkpoWK1HF6hhy/it/u=3209370120,2008812818&fm=26&gp=0.jpg',
      name: '赵磊',
      time: '10-12 11:23',
      content: '你好，来瓶敌敌畏',
      state: '方可已结束绘画',
      select: false
    },
    {
      avatar:
        'https://dss0.bdstatic.com/70cFuHSh_Q1YnxGkpoWK1HF6hhy/it/u=3209370120,2008812818&fm=26&gp=0.jpg',
      name: '赵磊',
      time: '10-12 11:23',
      content: '你好，我想咨询一下药品什么鬼的东西',
      state: '会话中',
      select: false
    },
    {
      avatar:
        'https://dss0.bdstatic.com/70cFuHSh_Q1YnxGkpoWK1HF6hhy/it/u=3209370120,2008812818&fm=26&gp=0.jpg',
      name: '赵大宝',
      time: '10-12 11:23',
      content: '好的',
      state: '会话中',
      select: false
    },
    {
      avatar:
        'https://dss0.bdstatic.com/70cFuHSh_Q1YnxGkpoWK1HF6hhy/it/u=3209370120,2008812818&fm=26&gp=0.jpg',
      name: '赵磊',
      time: '10-12 11:23',
      content: '你好，来瓶敌敌畏',
      state: '方可已结束绘画',
      select: false
    },
    {
      avatar:
        'https://dss0.bdstatic.com/70cFuHSh_Q1YnxGkpoWK1HF6hhy/it/u=3209370120,2008812818&fm=26&gp=0.jpg',
      name: '赵磊',
      time: '10-12 11:23',
      content: '你好，我想咨询一下药品什么鬼的东西',
      state: '会话中',
      select: false
    },
    {
      avatar:
        'https://dss0.bdstatic.com/70cFuHSh_Q1YnxGkpoWK1HF6hhy/it/u=3209370120,2008812818&fm=26&gp=0.jpg',
      name: '赵大宝',
      time: '10-12 11:23',
      content: '好的',
      state: '会话中',
      select: false
    },
    {
      avatar:
        'https://dss0.bdstatic.com/70cFuHSh_Q1YnxGkpoWK1HF6hhy/it/u=3209370120,2008812818&fm=26&gp=0.jpg',
      name: '赵磊',
      time: '10-12 11:23',
      content: '你好，来瓶敌敌畏',
      state: '方可已结束绘画',
      select: false
    },
    {
      avatar:
        'https://dss0.bdstatic.com/70cFuHSh_Q1YnxGkpoWK1HF6hhy/it/u=3209370120,2008812818&fm=26&gp=0.jpg',
      name: '赵磊',
      time: '10-12 11:23',
      content: '你好，我想咨询一下药品什么鬼的东西',
      state: '会话中',
      select: false
    },
    {
      avatar:
        'https://dss0.bdstatic.com/70cFuHSh_Q1YnxGkpoWK1HF6hhy/it/u=3209370120,2008812818&fm=26&gp=0.jpg',
      name: '赵大宝',
      time: '10-12 11:23',
      content: '好的',
      state: '会话中',
      select: false
    },
    {
      avatar:
        'https://dss0.bdstatic.com/70cFuHSh_Q1YnxGkpoWK1HF6hhy/it/u=3209370120,2008812818&fm=26&gp=0.jpg',
      name: '赵磊',
      time: '10-12 11:23',
      content: '你好，来瓶敌敌畏',
      state: '方可已结束绘画',
      select: false
    },
    {
      avatar:
        'https://dss0.bdstatic.com/70cFuHSh_Q1YnxGkpoWK1HF6hhy/it/u=3209370120,2008812818&fm=26&gp=0.jpg',
      name: '赵磊',
      time: '10-12 11:23',
      content: '你好，我想咨询一下药品什么鬼的东西',
      state: '会话中',
      select: false
    },
    {
      avatar:
        'https://dss0.bdstatic.com/70cFuHSh_Q1YnxGkpoWK1HF6hhy/it/u=3209370120,2008812818&fm=26&gp=0.jpg',
      name: '赵大宝',
      time: '10-12 11:23',
      content: '好的',
      state: '会话中',
      select: false
    },
    {
      avatar:
        'https://dss0.bdstatic.com/70cFuHSh_Q1YnxGkpoWK1HF6hhy/it/u=3209370120,2008812818&fm=26&gp=0.jpg',
      name: '赵磊',
      time: '10-12 11:23',
      content: '你好，来瓶敌敌畏',
      state: '方可已结束绘画',
      select: false
    }
  ];
}


/**
     * 1.客户往客服发送消息
     * 101 普通消息
     * 102 临时消息
     * 2.客服往客户发送消息
     * 201 普通消息
     * 202 对话转移提示
     * 203 特殊欢迎语
     * 3.系统发往客户消息
     * 301 . 在排队
     * 302 . 没有消息
     * 303 无客服
     * 304 操作成功（比如发送消息等）
     * 305 加入对话提示
     * 306 长时间没有对话，是否曳继续等待
     * 307 顾客选择继续等待
     * 4.系统发往客服消息
     * 401 未登录
     * 402 客服处于非在线状态
     * 403 新对话
     * 404 无任何类型的新消息
     * 405 无权限
     * 406操作成功
     * 407 非法输入
     * 408 对话转移请求
     * 409 转移的对话
     * 410 接受方接受转移请求
     * 411 接受方拒绝转移请求
     * 412有新的通知，content中的内容为 未读消息的数目
     * 413 对话被接管通知
     * 414被踢出登陆
     * 415对话转移成功后的话术
     * 5.客户关闭
     * 501 客户关闭
     * 6.客服关闭（不要求评价）
     * 601
     * 8.客服要求评价
     */
export function kfcontainerInfo () {
  return {
    dialoginfo: {
      accepttime: "2019-11-20 10:37:35",
      advice: "测试内容l6e8",
      appId: 1000,
      cmsgCount: 28186,
      corpid: 10000,
      createtime: "2019-11-20 10:36:44",
      currentkefuid: 2029,
      customLastMessageTime: "11-20 10:36",
      dialogType: 1,
      endtime: "2019-11-20 10:43:30",
      estimateStr: "测试内容hvim",
      groupName: "11",
      groupid: 130,
      id: 1757659262486536584,
      ipcity: "未知",
      ipprovince: "未知",
      kefuLastMessageTime: 1,
      kefuName: 1,
      kefuid: 2029,
      kmsgCount: 1,
      lastMsg: 1,
      lastMsgTime: 1,
      messagenum: 1,
      nickname: '大宝',
      privilegegroup: -1,
      referer: "Windows电脑浏览器",
      sourceip: "**************",
      timeToLocal: 1,
      timeused: 1,
      uid: 0,
      whoclose: -1
    },
    messages: [
      {
        content: '您好，欢迎来到小药药在线客户系统！我是小药药人工客服，很高兴为您服务！！！',
        createtime: '2019-11-20 10:37:35',
        dialogid: '1757659262486536584',
        kefuid: 2029,
        msgStatus: 0,
        type: 101,
        uid: 0
      }
    ]
  }
}

export function usercontainerInfo () {
  return {
    dialoginfo: {
      accepttime: "2019-11-20 10:37:35",
      advice: "测试内容l6e8",
      appId: 1000,
      cmsgCount: 28186,
      corpid: 10000,
      createtime: "2019-11-20 10:36:44",
      currentkefuid: 2029,
      customLastMessageTime: "11-20 10:36",
      dialogType: 1,
      endtime: "2019-11-20 10:43:30",
      estimateStr: "测试内容hvim",
      groupName: "11",
      groupid: 130,
      id: 175765926248657594,
      ipcity: "未知",
      ipprovince: "未知",
      kefuLastMessageTime: 1,
      kefuName: 1,
      kefuid: 2029,
      kmsgCount: 1,
      lastMsg: 1,
      lastMsgTime: 1,
      messagenum: 1,
      nickname: 'daboas11',
      privilegegroup: -1,
      referer: "Windows电脑浏览器",
      sourceip: "**************",
      timeToLocal: 1,
      timeused: 1,
      uid: 0,
      whoclose: -1
    },
    messages: [
      {
        content: '您好，欢迎来到小药药在线客户系统！我是小药药人工客服，很高兴为您服务！！！',
        createtime: '2019-11-20 10:37:35',
        dialogid: '175765926248657594',
        kefuid: 2029,
        msgStatus: 0,
        type: 201,
        uid: 0
      }
    ]
  }
}

export function usercontainerInfoING () {
  return {
    dialoginfo: {
      accepttime: "2019-11-20 10:37:35",
      advice: "测试内容l6e8",
      appId: 1000,
      cmsgCount: 28186,
      corpid: 10000,
      createtime: "2019-11-20 10:36:44",
      currentkefuid: 2029,
      customLastMessageTime: "11-20 10:36",
      dialogType: 1,
      endtime: "2019-11-20 10:43:30",
      estimateStr: "测试内容hvim",
      groupName: "11",
      groupid: 130,
      id: 175765926948657594,
      ipcity: "未知",
      ipprovince: "未知",
      kefuLastMessageTime: 1,
      kefuName: 1,
      kefuid: 2029,
      kmsgCount: 1,
      lastMsg: 1,
      lastMsgTime: 1,
      messagenum: 1,
      nickname: 'daboas222',
      privilegegroup: -1,
      referer: "Windows电脑浏览器",
      sourceip: "**************",
      timeToLocal: 1,
      timeused: 1,
      uid: 0,
      whoclose: -1
    },
    messages: [
      {
        content: '您好，欢迎来到小药药在线客户系统！我是小药药人工客服，很高兴为您服务！！！',
        createtime: '2019-11-20 10:37:35',
        dialogid: '175765926948657594',
        kefuid: 2029,
        msgStatus: 0,
        type: 201,
        uid: 0
      }
    ]
  }
}


export function usercontainerInfoED () {
  return {
    dialoginfo: {
      accepttime: "2019-11-20 10:37:35",
      advice: "测试内容l6e8",
      appId: 1000,
      cmsgCount: 28186,
      corpid: 10000,
      createtime: "2019-11-20 10:36:44",
      currentkefuid: 2029,
      customLastMessageTime: "11-20 10:36",
      dialogType: 2,
      endtime: "2019-11-20 10:43:30",
      estimateStr: "测试内容hvim",
      groupName: "11",
      groupid: 130,
      id: 175765946248657594,
      ipcity: "未知",
      ipprovince: "未知",
      kefuLastMessageTime: 1,
      kefuName: 1,
      kefuid: 2029,
      kmsgCount: 1,
      lastMsg: 1,
      lastMsgTime: 1,
      messagenum: 1,
      nickname: 'daboas333',
      privilegegroup: -1,
      referer: "Windows电脑浏览器",
      sourceip: "**************",
      timeToLocal: 1,
      timeused: 1,
      uid: 0,
      whoclose: -1
    },
    messages: [
      {
        content: '您好，欢迎来到小药药在线客户系统！我是小药药人工客服，很高兴为您服务！！！',
        createtime: '2019-11-20 10:37:35',
        dialogid: '175765946248657594',
        kefuid: 2029,
        msgStatus: 0,
        type: 201,
        uid: 0
      }
    ]
  }
}

export function usercontainerInfoWILL () {
  return {
    dialoginfo: {
      accepttime: "2019-11-20 10:37:35",
      advice: "测试内容l6e8",
      appId: 1000,
      cmsgCount: 28186,
      corpid: 10000,
      createtime: "2019-11-20 10:36:44",
      currentkefuid: 2029,
      customLastMessageTime: "11-20 10:36",
      dialogType: 3,
      endtime: "2019-11-20 10:43:30",
      estimateStr: "测试内容hvim",
      groupName: "11",
      groupid: 130,
      id: 175765926241657594,
      ipcity: "未知",
      ipprovince: "未知",
      kefuLastMessageTime: 1,
      kefuName: 1,
      kefuid: 2029,
      kmsgCount: 1,
      lastMsg: 1,
      lastMsgTime: 1,
      messagenum: 1,
      nickname: 'daboas444',
      privilegegroup: -1,
      referer: "Windows电脑浏览器",
      sourceip: "**************",
      timeToLocal: 1,
      timeused: 1,
      uid: 0,
      whoclose: -1
    },
    messages: [
      {
        content: '您好，欢迎来到小药药在线客户系统！我是小药药人工客服，很高兴为您服务！！！',
        createtime: '2019-11-20 10:37:35',
        dialogid: '175765926241657594',
        kefuid: 2029,
        msgStatus: 0,
        type: 201,
        uid: 0
      }
    ]
  }
}

export function topviewData () {
  return {
    avatar: 'https://dss0.bdstatic.com/70cFuHSh_Q1YnxGkpoWK1HF6hhy/it/u=3209370120,2008812818&fm=26&gp=0.jpg',
    textData: ['客户昵称：赵大宝', '来源地区：湖北', '来源渠道：微信', '对话次数：5次', '当天对话数：0次', '等候接入用时：1分40秒', '对话时长：1小时18分40秒']
  }
}

export function transData () {
  return [{
    name: "在线im1",
    id: 1,
    type: "group",
    kefus: [{
      name: "陈楠",
      type: "kefu",
      id: 1,
      code: "1001",
      numrotio: "80%",
      icon: 'el-icon-user'
    }]
  }]
}