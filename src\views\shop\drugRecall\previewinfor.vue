<template>
  <div class="previewinfor">
    <div class="drug-information">
      <div class="drug-title">{{drugParam.drugName}}药品召回信息登记</div>
      <div class="drug-text">{{drugParam.description}}</div>
      <div class="drug-row">
        <div class="row-title">
          <span>*</span>1.药店名称
        </div>
        <div class="drug-line"></div>
      </div>
      <div class="drug-row">
        <div class="row-title">
          <span>*</span>2.注册手机号码
        </div>
        <div class="drug-line"></div>
      </div>
      <div class="drug-row">
        <div class="row-title">
          <span>*</span>3.批号：123456(请填写剩余数量)
        </div>
        <div class="drug-content-row">
          <ul>
            <li v-for="(item,index) in drugParam.batchInfos" :key="index">
              <span>{{item.batchNumber}}</span>
              <div class="content-index">请填写数量</div>
            </li>
          </ul>
        </div>
      </div>
      <div class="drug-row">
        <div class="row-title">
          <span>*</span>4.发票类型(纸质发票必须随货退回，否则会影响您的退款)
        </div>
        <div class="drug-content">
          <ul>
            <li>
              <span></span>电子发票
            </li>
            <li>
              <span></span>纸质发票
            </li>
            <li>
              <span></span>专票
            </li>
          </ul>
        </div>
      </div>
      <div class="drug-row">
        <div class="row-title">
          <span>*</span>5.邮寄快递
        </div>
        <div class="drug-line"></div>
      </div>
      <div class="drug-row">
        <div class="row-title">
          <span>*</span>2.物流单号
        </div>
        <div class="drug-line"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { postDrugList } from "@/api/shop/index";
export default {
  name: "",
  components: {},
  filters: {},
  props: {
    goodsData: Object
  },
  data() {
    return {
      drugName: "",
      description: "",
      batchInfos: [],
      number: [],
      drugParam: {},
      radio: ""
    };
  },
  computed: {},
  mounted() {
    this.drugParam = this.goodsData;
  },
  methods: {},
  watch: {
    deep: true,
    goodsData(val) {
      this.drugParam = val;
    }
  },
  created() {}
};
</script>

<style lang="scss" scoped>
.previewinfor {
  .drug-information {
    width: 300px;
    padding: 20px 12px;
    .drug-title {
      color: #333;
      font-size: 18px;
      margin-bottom: 5px;
      font-weight: 700;
    }
    .drug-text {
      margin-bottom: 20px;
      color: #666;
    }
    .drug-row {
      .row-title {
        margin-bottom: 5px;
        span {
          color: rgb(226, 17, 17);
        }
        font-weight: 700;
        color: #333;
      }
      .drug-line {
        width: 100%;
        height: 150px;
        border: 1px solid #333;
      }
      margin-bottom: 20px;
    }
    .drug-content {
      ul {
        padding-left: 0px;
        border-bottom: 1px solid #666;
        li {
          border: 1px solid #666;
          height: 26px;
          line-height: 26px;
          border-bottom: 0;
          padding-left: 5px;
          span {
            width: 10px;
            height: 10px;
            display: inline-block;
            border-radius: 50%;
            border: 1px solid #666;
            margin-right: 5px;
          }
        }
      }
    }
    .drug-content-row {
      ul {
        padding-left: 0px;
        li {
          padding-left: 5px;
          height: 30px;
          line-height: 30px;
          margin-bottom: 5px;
          span {
            margin-right: 5px;
            width: 84px;
            overflow: hidden;
            display: inline-block;
            white-space: nowrap;
          }
          div {
            display: inline-block;
            width: 180px;
            padding-left: 3px;
            float: right;
            border: 1px solid #666;
            height: 30px;
          }
        }
      }
    }
  }
}
</style>