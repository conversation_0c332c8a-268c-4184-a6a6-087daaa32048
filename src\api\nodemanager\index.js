import request from "@/utils/request";

/* 节点列表查询 */
export function nodePoolGet(data) {
  return request({
    url: "/node/pool",
    method: "get",
    params: data
  });
}

/* 新建节点 */
export function nodePoolPost(data) {
  return request({
    url: "/node/pool",
    method: "post",
    params: data
  });
}

/* 更新节点 */
export function nodePoolPut(data) {
  return request({
    url: "/node/pool",
    method: "put",
    params: data
  });
}

/**
 *删除节点
 * @param
 */
export function nodePoolDelete(data) {
  return request({
    url: "/node/pool",
    method: "delete",
    params: data
  });
}

/* 节点复制 */
export function copyNodePoolByNodePoolId(data) {
  return request({
    url: "/node/pool/copyNodePoolByNodePoolId",
    method: "post",
    params: data
  });
}

/* 根据id查询列表 */
export function getNodeById(data) {
  return request({
    url: "/node/pool/getNodeById",
    method: "get",
    params: data
  });
}

/* 开启关闭节点 */
export function disableNodePool(data) {
  return request({
    url: "/node/pool/disableNodePool",
    method: "put",
    params: data
  });
}

/**
 * 工单报表相关
 * *
 * */

/* 查询工单量报表 */
export function selectWorkorderList(data) {
  return request({
    url: "/workorderReport/selectWorkorderList",
    method: "post",
    params: data
  });
}

/* 获取客户来源所在地列表 */
export function getCustomerSourceList(data) {
  return request({
    url: "/workorderReport/getCustomerSourceList",
    method: "get",
    params: data
  });
}

/* 获取工单类型下拉列表 */
export function getWorkorderTypeList(data) {
  return request({
    url: "/workorderReport/getWorkorderTypeList",
    method: "get",
    params: data
  });
}

/* 节点更新前验证 */
export function checkBeforeUpdate(data) {
  return request({
    url: "/node/pool/checkBeforeUpdate",
    method: "get",
    params: data
  });
}




/**
 * 员工话务报表相关
 * * 
 * */




/* 获取员工组 */
export function getTrafficList(data) {
  return request({
    url: '/imapi/imKefuGroup/all',
    method: 'get',
    params: data
  });
}
/* 获取会话结果 */
export function getOrderTypeList(data) {
  return request({
    url: '/imapi/report/getDialogResultList',
    method: 'get',
    params: data
  });
}

/* 获取坐席 */
export function getTraffic(data) {
  return request({
    url: '/imapi/imKefuGroup/selectById?id=' + data,
    method: 'get',
    // params: data
  });
}
/* 获取坐席搜索 */
export function getRoleTra(data) {
  return request({
    url: '/imapi/staff/list',
    method: 'get',
    params: data
  });
}

/* 查询客服状态变更报表 */
export function selectTrafficList(data) {
  return request({
    url: '/imapi/report/statusChangeReport',
    method: 'get',
    params: data
  });
}

/* 查询客服状态变更导出报表 */
export function exportData(data) {
  return request({
    url: '/imapi/report/statusChangeReport/export',
    method: 'get',
    params: data
  });
}

/* 查询对话明细报表报表 */
export function dialogueTrafficList(data) {
  return request({
    url: '/imapi/report/dialogInfoReport',
    method: 'get',
    params: data
  });
}

/* 导出对话明细报表报表 */
export function exportTrafficList(data) {
  return request({
    url: '/imapi/report/dialogInfoReport/export',
    method: 'get',
    params: data
  });
}

/* 查询员工话务报表 */
export function TrafficReport(data) {
  return request({
    url: '/imapi/sessionReport/queryPage',
    method: 'get',
    params: data
  });
}
/* 导出员工话务报表 */
export function exportReport(data) {
  return request({
    url: '/imapi/sessionReport/export',
    method: 'get',
    params: data
  });
}

/* 查询访客信息报表 */
export function VisitorReport(data) {
  return request({
    url: '/imapi/visitorSourceReport/get',
    method: 'get',
    params: data
  });
}

/* 查询在线平台报表 */
export function onlineReport(data) {
  return request({
    url: '/imapi/platformReport/queryPage',
    method: 'get',
    params: data
  });
}

/**
 *所属渠道
 * @param {Object}查询参数
 */
export function getSources(params) {
  return request({
    url: '/imapi/staff/merchants',
    method: 'get',
  });
}