<template>
  <div>
    <el-dialog :visible="status" :title="title" custom-class="transfer-box" top="0" @close="close">
      <div class="left-content">
        <div class="content-title">待添加坐席</div>
        <div class="content-body">
          <el-input
            v-model.trim="leftSearch"
            suffix-icon="el-icon-search"
            size="small"
            placeholder="请输入坐席名称/工号"
          ></el-input>
          <el-checkbox-group v-model="leftCheckedDatas" @change="handleLeftChange">
            <el-checkbox
              v-for="el in leftFilterDatas"
              :label="el"
              :key="el.id"
            >{{ `${el.name}-${el.code}` }}</el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="btn-box">
          <el-checkbox
            :indeterminate="leftIndeterminate"
            v-model="leftCheckAll"
            @change="handleLeftCheckAll"
          >{{ leftCheckAll?'全不选':'全选' }}</el-checkbox>
        </div>
      </div>
      <div class="center-content">
        <el-button
          :disabled="!leftCheckedDatas.length"
          icon="el-icon-back"
          class="btn-right"
          type="primary"
          @click="leftToRight"
        ></el-button>
        <el-button
          :disabled="!rightCheckedDatas.length"
          icon="el-icon-back"
          class="btn-left"
          type="primary"
          @click="rightToLeft"
        ></el-button>
      </div>
      <div class="right-content">
        <div class="content-title">已选择坐席{{ rightDatas.length?`(${rightDatas.length})`:'' }}</div>
        <div class="content-body">
          <el-input
            v-model.trim="rightSearch"
            suffix-icon="el-icon-search"
            size="small"
            placeholder="请输入坐席名称/工号"
          ></el-input>
          <el-checkbox-group v-model="rightCheckedDatas" @change="handleRightChange">
            <el-checkbox
              v-for="el in rightFilterDatas"
              :label="el"
              :key="el.id"
            >{{ `${el.name}-${el.code}` }}</el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="btn-box">
          <el-checkbox
            :indeterminate="rightIndeterminate"
            v-model="rightCheckAll"
            @change="handleRightCheckAll"
          >{{ rightCheckAll?'全不选':'全选' }}</el-checkbox>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    status: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '提示'
    },
    checked: {
      type: Array,
      default: function() {
        return [];
      }
    },
    leftDatas: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      leftCheckedDatas: [], // 左侧选中用户数据
      rightDatas: [], // 右侧用户数据
      rightCheckedDatas: [], // 右侧选中用户数据
      disabledIds: [], // 禁用的用户id
      leftCheckAll: false,
      leftIndeterminate: false,
      leftSearch: '',
      rightCheckAll: false,
      rightIndeterminate: false,
      rightSearch: ''
    };
  },
  computed: {
    leftFilterDatas() {
      return this.leftSearch
        ? this.leftDatas.filter(
            el =>
              String(el.code).indexOf(this.leftSearch) >= 0 ||
              String(el.name).indexOf(this.leftSearch) >= 0,
            this
          )
        : this.leftDatas;
    },
    rightFilterDatas() {
      return this.rightSearch
        ? this.rightDatas.filter(
            el =>
              String(el.code).indexOf(this.rightSearch) >= 0 ||
              String(el.name).indexOf(this.rightSearch) >= 0,
            this
          )
        : this.rightDatas;
    }
  },
  watch: {
    status(val, old) {
      if (val) {
        this.initDatas();
      }
    },
    leftSearch(val, old) {
      this.handleLeftChange(this.leftCheckedDatas);
    },
    rightSearch(val, old) {
      this.handleRightChange(this.rightCheckedDatas);
    }
  },
  mounted() {
    this.initDatas();
  },
  methods: {
    /**
     * 初始化选中数据
     */
    initDatas() {
      this.rightDatas = this.checked;
      this.leftCheckedDatas = this.leftDatas.filter(el =>
        this.rightDatas.map(_el => _el.id).includes(el.id)
      );
      this.rightCheckedDatas = [];
    },
    /**
     * 关闭回调
     */
    close() {
      this.$emit('update:status', false);
    },
    /**
     * 保存回调
     */
    save() {
      if (!this.rightDatas.length) {
        this.$XyyMessage.error('未有选中项，请重新选择');
        return;
      }
      this.$emit('callback', this.rightDatas);
      this.close();
    },
    /**
     * 选择右侧表单数据
     */
    leftToRight() {
      this.rightDatas = JSON.parse(JSON.stringify(this.leftCheckedDatas));
    },
    /**
     * 取消右侧表单数据
     */
    rightToLeft() {
      if (this.rightCheckedDatas.length) {
        const ids = this.rightCheckedDatas.map(el => el.id);
        this.rightDatas = this.rightDatas.filter(el => !ids.includes(el.id));
        this.leftCheckedDatas = this.leftDatas.filter(el =>
          this.rightDatas.map(_el => _el.id).includes(el.id)
        );
        this.rightCheckedDatas = [];
      }
    },
    handleLeftChange(val) {
      const checkedCount = val.length;
      this.leftCheckAll =
        this.leftFilterDatas.length &&
        checkedCount === this.leftFilterDatas.length;
      this.leftIndeterminate =
        checkedCount > 0 && checkedCount < this.leftFilterDatas.length;
    },
    /**
     * 全选操作
     */
    handleLeftCheckAll(val) {
      this.leftCheckedDatas = val ? this.leftFilterDatas : [];
      this.leftIndeterminate = false;
    },
    handleRightChange(val) {
      const checkedCount = val.length;
      this.rightCheckAll =
        this.rightFilterDatas.length &&
        checkedCount === this.rightFilterDatas.length;
      this.rightIndeterminate =
        checkedCount > 0 && checkedCount < this.rightFilterDatas.length;
    },
    handleRightCheckAll(val) {
      this.rightCheckedDatas = val ? this.rightFilterDatas : [];
      this.rightIndeterminate = false;
    }
  }
};
</script>

<style lang="scss">
.el-dialog.transfer-box {
  width: 850px;
  height: 502px;
  transform: translateY(-50%);
  top: 50%;
  .el-dialog__header {
    height: 52px;
    padding: 15px 20px;
    .el-dialog__title {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(41, 41, 51, 1);
      height: 22px;
      line-height: 22px;
    }
    .el-dialog__headerbtn {
      top: 18px;
    }
  }
  .el-dialog__body {
    height: calc(100% - 122px);
    padding: 6px 20px 0;
    > div {
      float: left;
      height: 100%;
      box-sizing: border-box;
      overflow: hidden;
      position: relative;
      .content-title {
        height: 55px;
        background: rgba(240, 242, 245, 1);
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(41, 41, 51, 1);
        padding: 17px 0 18px 20px;
      }
      .content-body {
        height: calc(100% - 55px);
        padding: 15px 20px 32px;
        overflow-y: auto;
        .el-input {
          margin-bottom: 12px;
          .el-input__inner {
            padding-left: 12px;
          }
        }
        .el-checkbox-group {
          .el-checkbox {
            display: block;
            margin-bottom: 12px;
            font-weight: normal;
          }
        }
      }
      .btn-box {
        position: absolute;
        bottom: 0;
        padding-left: 20px;
        width: 100%;
        height: 32px;
        background: #fff;
        z-index: 9999;
        .el-button {
          padding: 0;
          height: 20px;
          line-height: 20px;
          font-weight: normal;
        }
      }
    }
    .left-content {
      width: 346px;
      border-radius: 2px;
      border: 1px solid rgba(228, 228, 235, 1);
    }
    .center-content {
      width: calc(100% - 738px);
      position: relative;
      .el-button {
        padding: 0;
        width: 28px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        border-radius: 50%;
        position: absolute;
        top: 50%;
        &.btn-left {
          left: 50%;
          transform: translateX(-50%) translateY(80%);
          margin-left: 0;
        }
        &.btn-right {
          right: 50%;
          transform: rotate(180deg) translateX(-50%) translateY(80%);
        }
        &.is-disabled {
          background: rgba(220, 223, 230, 1);
          border-color: rgba(220, 223, 230, 1);
        }
      }
    }
    .right-content {
      width: 392px;
      border-radius: 2px;
      border: 1px solid rgba(228, 228, 235, 1);
    }
  }
  .el-dialog__footer {
    padding: 20px;
    .el-button {
      height: 36px;
      line-height: 36px;
      padding: 0 19px;
    }
  }
}
</style>
