<template>
  <div class="wxchat-container" :style="{backgroundColor: wrapBg}">
    <div
      class="window"
      id="window-view-container"
      :style="{height: maxHeight + 'px', width: width-20 +'px'}"
    >
      <!-- data is empty -->
      <div class="loading" v-if="dataArray && dataArray.length==0">
        <div style="margin-top: 200px;text-align:center; font-size: 16px;">
          <span>未查找到聊天记录</span>
        </div>
      </div>

      <!-- loading -->
      <!-- <div class="loading" v-if="dataArray.length==0">
        <div style="margin-top: 200px;">
        </div>
      </div>-->

      <!-- <div class="title" v-if="dataArray && dataArray.length>0">
        <p v-text="contactNickname"></p>
      </div>-->
      <!-- main -->
      <ScrollLoader
        :minHeight="maxHeight-30"
        @scroll-to-top="refresh"
        class="container-main"
        v-if="dataArray && dataArray.length>0"
        :style="{maxHeight: maxHeight + 'px'}"
        :needEvent="needEvent"
        ref="scroll_loder"
      >
        <div class="message">
          <ul>
            <li
              v-for="message in dataArray"
              :key="message.id"
              v-if="message.content"
              :class="message.direction==2?'an-move-right':'an-move-left'"
            >
              <div v-if="message.msgtype==='endmessage'" class="time">- 该会话已关闭 -</div>
              <div v-else-if="message.msgtype==='system'" class="systemmsg">
                <div class="systemstyle">
                  <div class="el-icon-warning" style="margin-right:10px;"></div>
                  <span
                    v-html="newcontent(message.content)"
                    style="white-space: normal; word-break: break-all; "
                  ></span>
                </div>
              </div>

              <div :class="'main' + (message.direction==2?' self':'')" v-else>
                <p :class="message.direction==2?'timerows_right':'timerows_left'">
                  <span v-text="message.ctime"></span>
                </p>
                <img
                  class="avatar"
                  width="50"
                  height="50"
                  style="border-radius: 25px;"
                  :src="message.direction==2? ownerAvatarUrl: contactAvatarUrl"
                />
                <div
                  v-html="newcontent(message.content)"
                  class="text"
                  id="msg"
                  @click="showRaw($event)"
                ></div>
              </div>
            </li>
          </ul>
        </div>
      </ScrollLoader>
    </div>
    <imgAlert ref="imgAlert"></imgAlert>
  </div>
</template>

<script>
import ScrollLoader from './scroll_loader.vue';
import imgAlert from './im_messageImgAlert';
import Vue from 'vue';
export default {
  name: 'wxChat',

  components: {
    ScrollLoader,
    imgAlert
  },

  props: {
    contactNickname: {
      type: String,
      default: 'Mystic Faces'
    },
    needEvent: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      required: true
    },

    width: {
      type: Number,
      default: 450
    },

    wrapBg: {
      type: String,
      default: '#fff'
    },

    maxHeight: {
      type: Number
    },

    contactAvatarUrl: {
      type: String
    },

    ownerAvatarUrl: {
      type: String
    },

    getUpperData: {
      type: Function
      // required: true
    },

    getUnderData: {
      type: Function
      // required: true
    },

    sectionName: {
      type: String
    }
  },

  data() {
    return {
      isUpperLaoding: false,
      isUnderLaoding: false,
      isRefreshedAll: false,
      isLoadedAll: false,
      max_height: this.maxHeight,
      minHeight: 100,
      dataArray: [],
      images: ''
    };
  },
  watch: {
    data(newData, oldData) {
      this.dataArray = [];
      this.data = newData;
      this.dataArray = this.dataArray.concat(this.data);

      this.$nextTick(() => {
        if (newData.length === 0) {
          return;
        }
        this.$refs.scroll_loder.scrollALLTimeToBottom();
      });
    }
  },

  mounted() {
    this.minHeight = document.getElementById(
      'window-view-container'
    ).offsetHeight;
    this.max_height = document.getElementById(
      'window-view-container'
    ).offsetHeight;
  },
  methods: {
    newcontent(content) {
      if (!content) {
        return;
      }
      let cont = content;
      if (cont.indexOf('xyyignore') === -1 && cont.indexOf('onclick') !== -1) {
        let contArr = cont.split('onclick');
        cont = contArr.join('@click');
      }
      return cont;
    },

    refreshMessage(message) {
      var me = this;
      me.dataArray = me.dataArray.concat(message);
      this.scrollerToBottom();
    },

    scrollerToBottom() {
      this.$nextTick(() => {
        this.$refs.scroll_loder.scrollToBottom();
      });
    },

    //向上拉刷新
    refresh(done) {
      var me = this;
      if (me.isUpperLaoding) {
        return;
      }

      if (me.isRefreshedAll) {
        done(true);
        me.isUpperLaoding = false;
        return;
      }

      try {
        this.getUpperData().then(function(data) {
          if (data.length == 0) {
            me.isRefreshedAll = true;
            done(true);
          } else {
            me.dataArray = data.reverse().concat(me.dataArray); //倒序合并
            done();
          }
        });
      } catch (error) {
        console.error(
          'wxChat: props "getUpperData" must return a promise object!'
        );
      }
      me.isUpperLaoding = false;
    },
    showRaw(event) {
      if (event.target.tagName === 'IMG') {
        let realimgsrc = event.target.attributes['@click'].value.split("'");
        this.images = realimgsrc[1];
        this.$refs.imgAlert.openView(realimgsrc[1]);
      }
    }

    //向下拉加载
    // infinite(done) {
    //   var me = this;
    //   if (me.isUnderLaoding) {
    //     return;
    //   }
    //   if (me.isLoadedAll) {
    //     done(true);
    //     me.isUnderLaoding = false;
    //     return;
    //   }

    //   try {
    //     this.getUnderData().then(function(data) {
    //       if (data == 0) {
    //         me.isLoadedAll = true;
    //         done(true);
    //       } else {
    //         done();
    //         me.dataArray = me.dataArray.concat(data); //直接合并
    //       }
    //     });
    //   } catch (error) {
    //     console.error(
    //       'wxChat: props "getUnderData" must return a promise object!'
    //     );
    //   }
    //   me.isUnderLaoding = false;
    // }
  }
};
</script>
<style scoped>
.wxchat-container {
  width: 100%;
  height: 100%;
  /* background-color: white; */
  /* z-index: 100; */
  /* position: fixed; */
  left: 0;
  top: 0;
  overflow: hidden;
}
.shadow {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 50;
  width: 100%;
  height: 100%;
  /* background: #000; */
  opacity: 0.2;
}
.window {
  /* box-shadow: 1px 1px 20px -5px #000; max-width: 450px; */
  min-width: 300px;
  background: #fff;
  margin: 0 auto;
  overflow: hidden;
  padding: 0;
  position: relative;
  z-index: 51;
}
button {
  border: 0;
  background: none;
  border-radius: 0;
  text-align: center;
}
button {
  outline: none;
}
.w100 {
  width: 100%;
}
.mt5 {
  margin-top: 5px;
}
.mt10 {
  margin-top: 10px;
}
.mt20 {
  margin-top: 20px;
}
.mb10 {
  margin-bottom: 10px;
}
.mb20 {
  margin-bottom: 20px;
}
.fs0 {
  font-size: 0;
}
.title {
  background: #000;
  text-align: center;
  color: #fff;
  width: 100%;
  height: 50px;
  line-height: 50px;
  font-size: 14px;
}
.loading,
.no-more {
  text-align: center;
  color: #b0b0b0;
  line-height: 100px;
}
.no-more {
  line-height: 60px;
}
.pull-right {
  float: right;
}
.link-line {
  text-decoration: underline;
}
.message {
  /* height: 100%; */
  padding: 10px 15px;
  overflow-y: auto;
  background-color: #fff;
}
.message ul {
  overflow-y: auto;
}
.message li {
  margin-bottom: 15px;
  left: 0;
  position: relative;
  display: block;
}
.message .time {
  margin: 10px 0;
  text-align: center;
  font-size: 12px;
  color: #909399;
}

.systemmsg {
  margin: 10px 0;
  text-align: center;
  font-size: 12px;
  width: 100%;
  margin-top: 20px;
  /* padding-top: 10px; */
  padding: 10px;
  color: #909399;
  border-radius: 4px;
  background-color: #dae9ec;
}

.systemstyle {
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.message .timerows_right {
  font-size: 12px;
  margin-right: 60px;
  color: #909399;
  text-align: right;
}
.message .timerows_left {
  font-size: 12px;
  margin-left: 60px;
  color: #909399;
  text-align: left;
}
.message .text {
  display: inline-block;
  position: relative;
  padding: 0 10px;
  /* max-width: calc(100% - 75px);
   */
  max-width: 50%;
  min-height: 35px;
  line-height: 2.1;
  font-size: 15px;
  padding: 6px 10px;
  text-align: left;
  word-break: break-all;
  background-color: #f5f7fa;
  color: #000;
  border-radius: 4px;
  box-shadow: 0px 1px 7px -5px #000;
}
.message .avatar {
  float: left;
  margin: 0 10px 0 0;
  border-radius: 3px;
  background: #fff;
}
.message .time > span {
  display: inline-block;
  padding: 0 5px;
  font-size: 12px;
  color: #fff;
  border-radius: 2px;
  background-color: #dadada;
}

.message .system > span {
  padding: 4px 9px;
  text-align: left;
}
.message .text:before {
  content: ' ';
  position: absolute;
  top: 9px;
  right: 100%;
  border: 6px solid transparent;
  border-right-color: #fff;
}
.message .self {
  text-align: right;
}
.message .self .avatar {
  float: right;
  margin: 0 0 0 10px;
}
.message .self .text {
  background-color: #dae9ec;
}

.message .self .text:before {
  right: inherit;
  left: 100%;
  border-right-color: transparent;
  border-left-color: #dae9ec;
}
.message .image {
  max-width: 200px;
}
img.static-emotion-gif,
img.static-emotion {
  vertical-align: middle !important;
}

.an-move-left {
  left: 0;
  animation: moveLeft 0.7s ease;
  -webkit-animation: moveLeft 0.7s ease;
}
.an-move-right {
  left: 0;
  animation: moveRight 0.7s ease;
  -webkit-animation: moveRight 0.7s ease;
}
.bgnone {
  background: none;
}
.message ::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 0 #ffffff;
  background: #fff;
}
@keyframes moveRight {
  0% {
    left: -20px;
    opacity: 0;
  }
  100% {
    left: 0;
    opacity: 1;
  }
}

@-webkit-keyframes moveRight {
  0% {
    left: -20px;
    opacity: 0;
  }
  100% {
    left: 0px;
    opacity: 1;
  }
}
@keyframes moveLeft {
  0% {
    left: 20px;
    opacity: 0;
  }
  100% {
    left: 0px;
    opacity: 1;
  }
}

@-webkit-keyframes moveLeft {
  0% {
    left: 20px;
    opacity: 0;
  }
  100% {
    left: 0px;
    opacity: 1;
  }
}

@media (max-width: 367px) {
  .fzDInfo {
    width: 82%;
  }
}
</style>
