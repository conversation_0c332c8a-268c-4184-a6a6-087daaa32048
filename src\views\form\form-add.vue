<template>
  <div class="page-container">
    <div class="content">
      <!-- 步骤条 start -->
      <steps :steps="steps"
             :active="active" />
      <!-- 步骤条 end -->

      <!-- 基础配置 -->
      <base-info v-if="active === 0"
                 ref="baseInfo"
                 :form="baseForm" />

      <!-- 流程配置 -->
      <flow-info v-if="active === 1"
                 ref="flowInfo"
                 :form="flowForm"
                 :form-id="id"
                 @reloadNodeList="reloadNodeList" />
    </div>

    <!-- footer start -->
    <footer class="footer-fix">
      <el-button v-if="active>0"
                 @click="checkTimer(goStep,'timer')(-1)">上一步</el-button>
      <el-button v-if="active !== steps.length-1"
                 type="primary"
                 @click="checkTimer(goStep,'timer1')(1)">下一步</el-button>
      <el-button v-if="active == steps.length-1 && !readOnly"
                 type="primary"
                 @click="complete">完成</el-button>
    </footer>
    <!-- footer end -->
  </div>
</template>

<script>
import {
  getFormById,
  addBaseForm,
  updateBaseForm,
  getListNodesByFormId
} from '@/api/formManage';
import Steps from './components/steps';
import BaseInfo from './components/base-info';
import FlowInfo from './components/flow-info';
import utils from '@/utils/form';
export default {
  name: 'FormAdd',
  components: { Steps, BaseInfo, FlowInfo },
  directives: {},
  data () {
    return {
      id: '', // 表单id
      steps: [{ name: '基础配置' }, { name: '流程配置' }], // 流程数据
      active: 0, // 当前流程
      // 基础配置
      baseForm: {
        name: '',
        formTypeId: '',
        labels: '',
        description: '',
        areaCode: ''
      },
      timer: null,
      timer1: null,
      // 流程节点
      flowForm: [
        // {
        //   active: true,
        //   nodeName: '发起节点',
        //   nodeType: 0, // 节点类型 0-发起节点 1-处理节点 2-抄送节点 3-关闭节点
        //   nodeUserList: [], // 参与人
        //   templateId: 0, // 模板id
        //   toNext: 0, // 下一步是否有多个节点 0-否 1-是
        // },
        // {
        //   active: false,
        //   nodeName: '关闭节点',
        //   nodeType: 3,
        //   nodeUserList: [],
        //   templateId: 0,
        //   toNext: 0,
        //   processTimeout: 1, // 处理时限
        //   closeWay: 0 // 关闭方式
        // }
      ],
      readOnly: false // 只读状态
    };
  },
  mounted () {
    // 基础设置回显
    if (this.$route.query.id !== 'false') {
      this.id = this.$route.query.id;
      this.getFormById(this.id);
    }
    this.readOnly = this.$route.query.readOnly
      ? Boolean(this.$route.query.readOnly)
      : false;
    this.$store.dispatch('tagsView/updateVisitedView', {
      ...this.$route,
      meta: {
        title: this.readOnly ? '查看表单应用' : '编辑表单应用'
      }
    });
  },
  methods: {
    // 上一步&&下一步
    goStep (step) {
      if (!this.readOnly) {
        if (this.active === 0) {
          if (this.$refs.baseInfo.validateForm()) {
            if (this.id) {
              this.updateBaseForm(step);
            } else {
              this.addBaseForm(step);
            }
          }
        } else if (this.active === 1) {
          if (
            this.flowForm.length &&
            !this.$refs.flowInfo.$refs.nodeInfo.editable
          ) {
            // 校验节点信息是否保存
            const _this = this;
            this.$XyyMsg({
              title: '提示',
              content: '是否保存当前节点的设置？', // html代码串
              closeBtnClass: 'left',
              okValue: '保存',
              onSuccess: function () {
                const temp = {};
                _this.flowForm.forEach((el, index) => {
                  if (el.active) {
                    temp.index = index;
                    temp.currentNodeId = el.id;
                  }
                });
                _this.$refs.flowInfo.$refs.nodeInfo.saveNode(
                  temp.index,
                  temp.currentNodeId,
                  step
                );
              },
              button: [
                {
                  value: '不保存',
                  callback: function () {
                    _this.active = _this.active + step;
                  }
                }
              ]
            });
          } else {
            this.active = this.active + step;
          }
        }
      } else {
        this.active = this.active + step;
      }
    },
    // 完成操作
    complete () {
      if (
        this.flowForm.length &&
        !this.$refs.flowInfo.$refs.nodeInfo.editable &&
        !this.readOnly
      ) {
        // 校验节点信息是否保存
        this.$XyyMsg({
          title: '提示',
          content: '是否保存当前节点的设置？', // html代码串
          closeBtnClass: 'left',
          okValue: '保存',
          onSuccess: () => {
            const temp = {};
            this.flowForm.forEach((el, index) => {
              if (el.active) {
                temp.index = index;
                temp.currentNodeId = el.id;
              }
            });
            this.$refs.flowInfo.$refs.nodeInfo.saveNode(
              temp.index,
              temp.currentNodeId
            );
            this.$store
              .dispatch('tagsView/delView', this.$route)
              .then(({ visitedViews }) => {
                this.$router.push({ name: 'formList' });
              });
          },
          button: [
            {
              value: '不保存',
              callback: () => {
                this.$store
                  .dispatch('tagsView/delView', this.$route)
                  .then(({ visitedViews }) => {
                    this.$router.push({ name: 'formList' });
                  });
              }
            }
          ]
        });
      } else {
        this.$store
          .dispatch('tagsView/delView', this.$route)
          .then(({ visitedViews }) => {
            this.$router.push({ name: 'formList' });
          });
      }
    },
    // 根据id获取应用信息
    getFormById (id) {
      getFormById(id).then(res => {
        if (res.code === 1) {
          const { name, labels, description, formTypeId, id, areaCode } = res.data;
          this.baseForm.id = id;
          this.baseForm.name = name;
          this.baseForm.labels = labels;
          this.baseForm.description = description;
          this.baseForm.formTypeId = formTypeId;
          this.baseForm.areaCode = areaCode || '';
          this.getListNodesByFormId(id);
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 新增基础信息
    addBaseForm (step) {
      const params = Object.assign({}, this.baseForm);
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      addBaseForm(params).then(res => {
        loading.close();
        if (res.code === 1) {
          this.$XyyMessage.success('保存成功');
          this.id = res.data;
          this.baseForm.id = res.data;
          this.getListNodesByFormId(this.id);
          this.active = this.active + step;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 更新基础信息
    updateBaseForm (step) {
      const params = Object.assign({}, this.baseForm);
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      updateBaseForm(params).then(res => {
        loading.close();
        if (res.code === 1) {
          this.active = this.active + step;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 重新加载节点列表
    reloadNodeList (params) {
      this.refreshFlowNodes(this.id, params.id);
    },
    // 获取节点列表
    getListNodesByFormId (formId, nodeId) {
      getListNodesByFormId(formId).then(res => {
        if (res.code === 1) {
          this.flowForm = res.data;
          // 校验节点数据完整性
          if (this.flowForm.length) {
            this.flowForm.forEach(el => {
              const res = utils.validateFormData(el);
              el.require = !res.valid;
              el.editable = res.valid;
              if (el.id === nodeId) {
                el.active = true;
                el.editable = true;
              }
            });
            if (nodeId) {
              this.id = formId;
              this.baseForm.id = formId;
            } else {
              this.flowForm[0].active = true;
            }
          }
        }
      });
    },
    /**
     * 刷新节点数据
     */
    refreshFlowNodes (formId, nodeId) {
      getListNodesByFormId(formId).then(res => {
        if (res.code === 1) {
          this.flowForm = res.data;
          // 校验节点数据完整性
          this.flowForm.forEach(el => {
            const res = utils.validateFormData(el);
            el.require = !res.valid;
            el.editable = res.valid;
            if (el.id === nodeId) {
              el.active = true;
            }
          });
        } else {
          this.$XyyMessage.error(res.msg);
        }
        if (
          this.flowForm.length &&
          !this.flowForm.some(el => el.id === nodeId)
        ) {
          this.flowForm[0].active = true;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.page-container {
  position: relative;
  width: 100%;
  height: 100;
  padding: 36px 20px;
  .content {
    height: 100%;
    overflow: auto;
  }
}
.footer-fix {
  position: fixed;
  width: 100%;
  right: 0px;
  bottom: 0px;
  height: 56px;
  line-height: 56px;
  text-align: right;
  z-index: 1;
  background: rgb(255, 255, 255);
  box-shadow: 0px 0px 0px 1px rgba(0, 0, 0, 0.12);
  .el-button {
    height: 36px;
    line-height: 36px;
    padding: 0 20px;
    &:last-child {
      margin-right: 20px;
    }
  }
}
</style>

