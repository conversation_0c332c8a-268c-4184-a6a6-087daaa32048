<template>
  <xyy-list-page>
    <template slot="header">
      <el-row class="herader-title" type="flex" justify="space-between" align="middle">
        <span style="font-size:16px">用户组</span>
        <!-- <el-button icon="el-icon-upload2"
                   class="export-excel"
        @click="exportData">导出Excel</el-button>-->
      </el-row>
      <div class="box row-between btn-group-filter">
        <div class="left">
          <xyy-button icon-class="btn-add" class-name="btn-add-icon" @click="goAddPage">新增用户组</xyy-button>
          <xyy-button icon-class="enable" type="normal" @click="batchStart">批量启用</xyy-button>
          <xyy-button icon-class="disable" type="normal" @click="batchForbidden">批量禁用</xyy-button>
        </div>
        <div class="right">
          <el-input v-model.trim="listQuery.userGroupName" placeholder="请输入内容" maxlength="20"></el-input>
          <el-button type="primary" @click="checkTimer(handleSearch,'timer')()">搜索</el-button>
        </div>
      </div>
    </template>
    <template slot="body">
      <div class="info-container">
        <info :info="info"></info>
      </div>
      <xyy-table
        :data="list"
        :list-query="listQuery"
        :col="col"
        :operation="operation"
        :has-selection="true"
        @get-data="getList"
        @operation-click="operationClick"
        @selectionCallback="selectionChange"
      >
        <template slot="gmtModified" slot-scope="{col}">
          <el-table-column
            :key="col.index"
            :prop="col.index"
            :label="col.name"
            :width="col.width"
            :formatter="getFormatDate"
          />
        </template>
        <template slot="status" slot-scope="{col}">
          <el-table-column :key="col.index" :label="col.name" :width="90" :resizable="false">
            <template slot-scope="{row}">
              <span :class="'status-tip '+(row.status === 1?'open':'close')"></span>
              {{ row.status === 1?'启用':'禁用' }}
            </template>
          </el-table-column>
        </template>
      </xyy-table>
    </template>
  </xyy-list-page>
</template>

<script>
import {
  getUserGroupList,
  disableUserGroup,
  disableBatchUserGroup,
  removeUserGroup,
  copyUserGroup
} from '@/api/user-group';
/* 查询关联 */
// import { listTemplate } from '../../api/fields/fields-comment';

export default {
  name: 'UserGroupList',
  data() {
    return {
      info: [
        {
          title: '用户组可由一人或多人组成，即新建工单、处理工单的用户群体',
          info: ''
        },
        {
          title: '启用/禁用',
          info:
            '禁用即失效状态，禁用时会判断当前用户组是否被节点引用，如果是则不能禁用；启用即正常可用状态，启用状态的用户组才可以引用到节点'
        },
        { title: '编辑', info: '可修改用户组的配置，可增删组内人员。' },
        {
          title: '复制',
          info:
            '对当前用户组进行复制，生成一条新用户组，用户组设置与原用户组一致。'
        },
        {
          title: '删除',
          info: '与禁用的判断逻辑一致。删除后，用户组从页面消失。'
        }
      ],
      list: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0,
        userGroupName: ''
      },

      selectArr: [],
      col: [
        {
          index: 'userGroupName',
          name: '用户组名称',
          width: 300,
          ellipsis: true,
          resizable: true
        },
        { index: 'total', name: '人数', resizable: true, width: 50 },
        {
          index: 'description',
          name: '说明',
          ellipsis: true,
          resizable: true
        },
        {
          index: 'gmtModified',
          name: '更新时间',
          width: 180,
          slot: true,
          resizable: true
        },
        { index: 'editorName', name: '更新人', width: 130, resizable: true },
        {
          index: 'status',
          name: '状态',
          width: 50,
          slot: true,
          resizable: true
        },
        {
          index: 'operation',
          name: '操作',
          width: 220,
          operation: true,
          resizable: true
        }
      ],
      operation: [
        {
          name: '启用',
          type: 0,
          format: function(row) {
            return ['启用', '禁用'][row.status];
          }
        },
        {
          name: '编辑',
          type: 1
        },
        {
          name: '复制',
          type: 2
        },
        {
          name: '删除',
          type: 3,
          disabled: function(row) {
            return !!row.status;
          }
        }
      ],
      timer: null,
      versionCodes: []
    };
  },
  methods: {
    handleSearch() {
      this.listQuery.page = 1;
      const { page, pageSize, userGroupName } = this.listQuery;
      getUserGroupList(page, pageSize, userGroupName).then(res => {
        const { list, pageNum, pageSize, total } = res.data;
        this.list = list;
        this.listQuery = {
          ...this.listQuery,
          page: pageNum,
          pageSize,
          total
        };
      });
    },
    getList: function(listQuery) {
      const { page, pageSize } = listQuery;
      getUserGroupList(page, pageSize, this.listQuery.userGroupName).then(
        res => {
          const { list, pageNum, pageSize, total } = res.data;
          this.list = list;
          this.listQuery = {
            ...this.listQuery,
            page: pageNum,
            pageSize,
            total
          };
        }
      );
    },
    goAddPage: function() {
      this.$router.push({
        path: '/worksheet/userGroupAdd/' + new Date().getTime()
      });
    },
    goEditPage: function(row) {
      const { id, description, userGroupName, versionCode } = row;
      this.$router.push({
        path: '/worksheet/userGroupEdit/' + row.id,
        query: {
          description,
          userGroupName,
          userGroupId: row.id,
          versionCode: versionCode
        }
      });
    },
    operationClick: function(type, row) {
      const { id, userGroupName, versionCode } = row;
      const options = [
        row => {
          const msgs = ['启用', '禁用'];
          this.$XyyMsg({
            title: '提示',
            content: '确定' + msgs[row.status] + '此用户组吗？', // html代码串
            onSuccess: () => {
              disableUserGroup({ id, versionCode }).then(res => {
                if (res.code === 1) {
                  this.$XyyMessage.success('用户组已' + msgs[row.status]);
                  this.getList(this.listQuery);
                } else {
                  this.$XyyMessage.warning(res.msg);
                }
              });
            }
          });
        },
        row => {
          this.goEditPage(row);
        },
        row => {
          copyUserGroup(id).then(res => {
            if (res.code === 1) {
              this.$XyyMessage.success({
                message: '复制成功'
              });
              this.getList(this.listQuery);
            }
          });
        },
        row => {
          this.$XyyMsg({
            title: '提示',
            content: '确定删除此用户组吗?',
            onSuccess: () => {
              removeUserGroup(id, versionCode).then(res => {
                if (res.code === 1) {
                  this.$XyyMessage.success({
                    message: '删除成功'
                  });
                  this.getList(this.listQuery);
                } else if (res.code === 9) {
                  // 查询字段是否被关联
                  let html =
                    '当前正有表单应用内节点使用此用户组，请先修改表单应用节点( ';
                  html += `<span style="color: red"> ${res.msg}</span>`;
                  html += ' )';
                  this.$XyyMsg({
                    title: '提示',
                    content: html,
                    closeBtn: false,
                    onSuccess: function() {}
                  });
                } else {
                  this.$XyyMsg({
                    title: '提示',
                    closeBtn: false,
                    content: res.msg, // html代码串
                    onSuccess: () => {}
                  });
                }
              });
            }
          });
        }
      ];
      options[type](row);
    },
    // operationStatus: function(row) {},
    getFormatDate: function(row, column, cellValue, index) {
      return new Date(parseInt(cellValue) + 8 * 3600 * 1000)
        .toJSON()
        .substr(0, 19)
        .replace('T', ' ');
    },
    selectionChange(val) {
      this.selectArr.splice(0, this.selectArr.length);
      this.versionCodes.splice(0, this.versionCodes.length);
      val.forEach(item => {
        this.selectArr.push(item.id);
        this.versionCodes.push(item.versionCode);
      });
    },
    batchStart() {
      if (this.selectArr.length === 0) {
        this.$XyyMessage.warning('无选中数据');
        return;
      }
      this.$XyyMsg({
        title: '提示',
        content: '确定批量启用这些用户组吗',
        onSuccess: () => {
          disableBatchUserGroup(this.selectArr, 1, this.versionCodes).then(
            res => {
              if (res.code === 1) {
                this.$XyyMessage.success(res.msg);
                this.getList(this.listQuery);
              } else {
                this.$XyyMessage.warning(res.msg);
              }
            }
          );
        }
      });
    },
    batchForbidden() {
      if (this.selectArr.length === 0) {
        this.$XyyMessage.warning('无选中数据');
        return;
      }
      this.$XyyMsg({
        title: '提示',
        content: '确定批量禁用这些用户组吗',
        onSuccess: () => {
          disableBatchUserGroup(this.selectArr, 0, this.versionCodes).then(
            res => {
              if (res.code === 1) {
                this.$XyyMessage.success(res.msg);
                this.getList(this.listQuery);
              } else {
                this.$XyyMessage.warning(res.msg);
              }
            }
          );
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
/deep/.page-header {
  padding-bottom: 8px !important;
}
.herader-title {
  font-size: 14px;
  color: #393943;
  padding-bottom: 20px;
  border-bottom: 1px dashed #e4e4eb;
  margin-bottom: 20px;
}
.box {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}
.row-between {
  -webkit-box-pack: justify;
  -moz-justify-content: space-between;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}
.status-tip {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 2px 2px 0;
  &.open {
    background: #67c23a;
  }
  &.close {
    background: #ff3024;
  }
}
.btn-group-filter {
  .left {
    width: 50%;
    .batch-start-btn,
    .batch-forbidden-btn {
      width: 101px;
      height: 36px;
      border-radius: 2px;
      border: 1px solid rgba(228, 228, 235, 1);
      font-size: 14px;
      color: rgba(87, 87, 102, 1);
      outline: none;
    }
    .batch-start-btn {
      background: url('/static/start_icon.png') no-repeat 5% center;
      background-size: 13px;
    }
    .batch-forbidden-btn {
      background: url('/static/forbidden_icon.png') no-repeat 5% center;
      background-size: 13px;
    }
  }
  .right {
    width: 50%;
    text-align: right;
    .el-input {
      margin-right: 8px;
      width: 268px !important;
      height: 36px !important;
      /deep/ .el-input__inner {
        height: 36px !important;
        border-radius: 2px !important;
        border: 1px solid rgba(228, 228, 235, 1);
      }
    }
    button {
      width: 68px;
      height: 36px;
    }
  }
}
</style>

