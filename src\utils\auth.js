import Cookies from 'js-cookie';

const TokenKey = 'TGC';
const cookieKeys = ['TGC', 'sid', 'uid'];

export function getToken() {
  return Cookies.get(TokenKey);
}

export function setToken(token) {
  // return Cookies.set(TokenKey, token);
  return cookieKeys.forEach(cookie => {
    Cookies.set(cookie, token);
  });
}

export function removeToken() {
  // return Cookies.remove(TokenKey);
  return cookieKeys.forEach(cookie => Cookies.remove(cookie));
}
