/**
 * 工单标签配置相关接口
 */
import request from '@/utils/request';
import { formData } from '@/utils/index';

/**
 * 查询自定义标签列表
 * @param {是否展示停用标签} params 
 */
export function tagsGet(params) {
  return request({
    url: '/sys/tags/get',
    method: 'get',
    params
  });
}

/**
 * 自定义标签名称重复性校验
 * @param {标签名称} params
 */
export function tagsValidate(params) {
  return request({
    url: '/sys/tags/validate',
    method: 'get',
    params
  });
}

/**
 * 保存自定义标签
 * @param {标签名称} data
 */
export function tagsSave(data) {
  return request({
    url: '/sys/tags/save',
    method: 'post',
    data: formData(data)
  });
}

/**
 * 停用自定义标签
 * @param {标签id} data
 */
export function tagsStop(data) {
  return request({
    url: '/sys/tags/stop',
    method: 'put',
    data: formData(data)
  });
}

/**
 * 启用自定义标签
 * @param {标签id} data 
 */
export function tagsEnable(data) {
  return request({
    url: '/sys/tags/enable',
    method: 'put',
    data: formData(data)
  });
}


/**
 * 删除自定义标签
 * @param {标签id} data
 */
export function tagsDelete(data) {
  return request({
    url: '/sys/tags/delete',
    method: 'delete',
    data: formData(data)
  });
}

