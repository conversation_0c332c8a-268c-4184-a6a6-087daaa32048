<template>
  <div class="gdnumclass">
    <xyy-list-page>
      <template slot="header">
        <el-row class="herader-title" type="flex" justify="space-between" align="middle">
          <span style="font-size:16px">客服状态变更表</span>
        </el-row>
        <el-form
          ref="listQuery"
          :rules="rules"
          :inline="true"
          :model="listQuery"
          label-position="right"
          class="search-form"
        >
          <el-row type="flex" class="row-bg" justify="space-between">
            <!-- 日期 -->
            <el-form-item label="日期" prop="dataRange">
              <el-date-picker
                :picker-options="pickerOptions"
                v-model="listQuery.dataRange"
                type="daterange"
                range-separator="-"
                size="small"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                text="erdf"
                prefix-icon="el-icon-date"
                class="innerSelTime"
                @change="queryTime()"
                @focus="dateTimeFocus()"
              />
            </el-form-item>
            <!--员工类型-->
            <el-form-item label="员工组" prop="sheetType">
              <el-select v-model="listQuery.sheetType" placeholder="全部" @change="changeList">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in oderCityData"
                  :key="item.id"
                  :label="item.groupName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <!--坐席号-->
            <el-form-item label="坐席" prop="CustLocation">
              <!-- <el-select v-model="listQuery.CustLocation" placeholder="全部">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in ortoCityData"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>-->
              <el-select
                v-model="listQuery.CustLocation"
                :remote-method="remoteMethod"
                filterable
                remote
                clearable
                placeholder="请输入坐席"
              >
                <el-option
                  v-for="item in options2"
                  :key="item.id"
                  :label="item.name+'('+item.code+')'"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row>
            <!--变更状态前-->
            <el-form-item label="变更前状态" prop="timeType" class="biangeng">
              <el-select v-model="listQuery.timeType" placeholder="全部">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in oderTypeDta"
                  :key="item.status"
                  :label="item.remark"
                  :value="item.status"
                ></el-option>
              </el-select>
            </el-form-item>
            <!--变更状态后-->
            <el-form-item label="变更后状态" prop="timeBian" class="biangeng">
              <el-select v-model="listQuery.timeBian" placeholder="全部">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in houTypeDta"
                  :key="item.status"
                  :label="item.remark"
                  :value="item.status"
                ></el-option>
              </el-select>
            </el-form-item>
            <!--变更原因-->
            <el-form-item label="变更原因" prop="timeYuan" class="biangeng">
              <el-select v-model="listQuery.timeYuan" placeholder="全部">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in yuanTypeDta"
                  :key="item.status"
                  :label="item.remark"
                  :value="item.status"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row style="text-align:right;float:right;margin-top: -65px;">
            <el-form-item>
              <div class="searchTwoButton">
                <el-button
                  type="primary"
                  size="small"
                  @click="checkTimer(handerSearch('listQuery'))"
                >查询</el-button>
                <!-- <el-button size="small" @click="checkTimer(oldSearch('listQuery'))">重置</el-button> -->
                <el-button class="export-excel" style="padding-top: 9px;" @click="exportGet">导出</el-button>
              </div>
            </el-form-item>
          </el-row>
        </el-form>
      </template>
      <template slot="body">
        <div class="info-container">
          <info v-if="wenhao" :info="infodata()"></info>
        </div>
        <xyy-table
          v-loading="tabLoading"
          :is-stripe="false"
          :data="list"
          :col="col"
          :list-query="queryList"
          :offset-top="240"
          @get-data="getList"
        ></xyy-table>
      </template>
    </xyy-list-page>
    <expor-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    ></expor-tip>
  </div>
</template>

<script>
import axios from 'axios';
import exporTip from '@/views/work-sheet/components/exportTip';
import { mockGDType, mockGDUser } from './components/mock';
import {
  selectTrafficList,
  getTrafficList,
  getTraffic,
  exportData,
  getRoleTra
} from '@/api/nodemanager';
import chosseTypeAlert from './components/commonDialog';
import formTypeListVue from '../form/form-type-list.vue';
import { ONLINE_STATUS_MAP } from '@/utils/var.js';
export default {
  name: 'statusChange',
  components: {
    chosseTypeAlert,
    exporTip
  },
  data() {
    return {
      options2: [],
      tabLoading: false,
      ortoCityData: [],
      queryList: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      wenhao: true,
      changeExport: false,
      loading: false,
      mocktype: this.APIMockGDType(),
      mockCity: this.APICityGDType(),
      oderTypeDta: ONLINE_STATUS_MAP, // 变更状态前
      houTypeDta: ONLINE_STATUS_MAP, // 变更状态后
      yuanTypeDta: [
        { status: 1, remark: '坐席手动' },
        { status: 2, remark: '管理员强制' },
        { status: 3, remark: '系统强制'}
      ],
      oderCityData: [],
      dataRange: '',
      formAdressId: '',
      formTypeId: '',
      created: false,
      list: [],
      listQuery: {
        dataRange: [], // 日期
        sheetType: '',
        CustLocation: '',
        timeType: '',
        timeBian: '',
        timeYuan: ''
      },
      radio: 3,
      rules: {
        dataRange: [
          { required: false, message: '日期为必填项', trigger: 'blur' }
        ]
      },
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.minDate = minDate;
          this.maxDate = maxDate;
        },
        disabledDate: time => {
          const curDate = new Date().getTime();
          const two = 365 * 2 * 24 * 3600 * 1000;
          const twoyear = curDate - two;
          const three = 92 * 24 * 3600 * 1000;
          if (this.minDate) {
            return (
              time.getTime() > Date.now() ||
              time.getTime() < twoyear ||
              time > new Date(this.minDate.getTime() + three) ||
              time < new Date(this.minDate.getTime() - three)
            );
          }
          return time.getTime() > Date.now() || time.getTime() < twoyear;
        }
      },
      col: [
        {
          index: 'reportDate',
          name: '序号',
          width: 50,
          fixed: true,
          resizable: true
        },
        {
          index: 'userCode',
          name: '坐席工号',
          width: 100,
          fixed: true,
          resizable: true
        },
        {
          index: 'userName',
          name: '坐席名称',
          fixed: true,
          // width: 95,
          resizable: true
        },
        {
          index: 'kefuGroupName',
          name: '员工组',
          fixed: true,
          // width: 100,
          resizable: true
        },
        {
          index: 'beforeChangeStatus',
          name: '变更前状态',
          // width: 100,
          resizable: true
        },
        {
          index: 'afterChangeStatus',
          name: '变更后状态',
          // width: 100,
          resizable: true
        },
        {
          index: 'intervalsStr',
          name: '变更前状态时长(s)',
          width: 170,
          resizable: true
        },
        {
          index: 'changeReason',
          name: '变更原因',
          // width: 120,
          resizable: true
        },
        {
          index: 'endtime',
          name: '操作时间',
          width: 189,
          resizable: true
        }
      ],
      timelist: {
        1: '坐席手动',
        2: '管理员强制',
        3: '系统强制'
      },
      param: {},
      url: ``
    };
  },
  created() {
    this.getNowTimeDate();
    this.APIGetTrafficList();
    this.created = true;
  },
  activated() {
    // if (!this.created) {
    //   this.APISelectWorkorderList();
    // }
    this.created = false;
    this.getList(this.queryList);
  },
  methods: {
    // 坐席搜索
    async remoteMethod(query) {
      if (query !== '') {
        var params = {
          page: 1,
          pagesize: 1000,
          searchtype: 'name',
          searchval: query
        };
        await getRoleTra(params).then(res => {
          this.options2 = res.data.page.datas;
          console.log(this.options2);
          return res.data;
        });
      } else {
        this.options2 = [];
      }
    },
    getList: function(queryList) {
      const { page, pageSize } = queryList;
      const name = this.queryList.creatorName;
      // Object.keys(this.listQuery).forEach(k => {})
      const {
        dataRange,
        timeType,
        timeYuan,
        timeBian,
        sheetType,
        CustLocation
      } = this.listQuery;
      const param = {
        pageNum: page,
        pageSize,
        starttime: dataRange[0],
        endtime: dataRange[1],
        type: timeType,
        changeReason: timeYuan,
        changeStatus: timeBian,
        kefuGroupId: sheetType || null,
        uid: CustLocation || null
      };
      // if (this.listQuery.CustLocation == '') {
      //   this.param = {
      //     pageNum: page,
      //     pageSize,
      //     starttime: this.listQuery.dataRange[0],
      //     endtime: this.listQuery.dataRange[1],
      //     type: this.listQuery.timeType,
      //     changeReason: this.listQuery.timeYuan,
      //     changeStatus: this.listQuery.timeBian,
      //     userGroupId: this.listQuery.sheetType
      //   };
      // } else {
      //   this.param = {
      //     pageNum: page,
      //     pageSize,
      //     starttime: this.listQuery.dataRange[0],
      //     endtime: this.listQuery.dataRange[1],
      //     type: this.listQuery.timeType,
      //     changeReason: this.listQuery.timeYuan,
      //     changeStatus: this.listQuery.timeBian,
      //     userGroupId: this.listQuery.sheetType,
      //     uid: this.listQuery.CustLocation
      //   };
      // }
      this.tabLoading = true;
      selectTrafficList(param)
        .then(res => {
          // console.log(res);
          this.tabLoading = false;
          const { total } = res.data;
          this.list = res.data.records;
          let reportDate = {};
          this.list.forEach((item, index) => {
            reportDate = {};
            item.changeReason = this.timelist[item.changeReason];
            this.list[index] = Object.assign({}, this.list[index], {
              reportDate: index + 1
            });
          });
          this.queryList = {
            ...this.queryList,
            total: Number(total)
          };
        })
        .catch(err => {
          this.tabLoading = false;
        });
    },
    handleChangeExport(info) {
      const that = this;
      this.changeExport = false;
      if (info === 'go') {
        that.$router.push({
          path: '/other/index/'
        });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    exportGet() {
      const that = this;
      if (!that.list || !that.list.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      if (this.listQuery.CustLocation == '') {
        this.url = `${process.env.BASE_API_IM}/report/statusChangeReport/export?endtime=${that.listQuery.dataRange[1]}&starttime=${that.listQuery.dataRange[0]}&type=${this.listQuery.timeType}&changeReason=${this.listQuery.timeYuan}&changeStatus=${this.listQuery.timeBian}&userGroupId=${this.listQuery.sheetType}`;
      } else {
        this.url = `${process.env.BASE_API_IM}/report/statusChangeReport/export?endtime=${that.listQuery.dataRange[1]}&starttime=${that.listQuery.dataRange[0]}&type=${this.listQuery.timeType}&changeReason=${this.listQuery.timeYuan}&changeStatus=${this.listQuery.timeBian}&userGroupId=${this.listQuery.sheetType}&uid=${this.listQuery.CustLocation}`;
      }
      if (
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
      ) {
        this.url =
          this.url +
          `&businessPartCode=${this.$store.getters.channel.businessPartCode}`;
      }
      // const url = `${process.env.BASE_API_IM}/report/statusChangeReport/export?endtime=${that.listQuery.dataRange[1]}&starttime=${that.listQuery.dataRange[0]}&type=${this.listQuery.timeType}&changeReason=${this.listQuery.timeYuan}&changeStatus=${this.listQuery.timeBian}&userGroupId=${this.listQuery.sheetType}&uid=${this.listQuery.CustLocation}`;
      const a = document.createElement('a');
      a.href = this.url;
      a.click();

      // console.log(params);
      // exportData(params).then(res => {
      //   this.$XyyMessage.success('导出成功！');
      // });
    },
    // oldSearch() {
    //   this.getNowTimeDate();
    //   this.listQuery.sheetType = '';
    //   this.listQuery.CustLocation = '';
    //   this.APISelectWorkorderList();
    // },
    handerSearch() {
      this.getList(this.queryList);
    },
    // 当前时间
    getNowTimeDate() {
      const time = new Date(8 * 3600 * 1000 + +new Date())
        .toJSON()
        .substr(0, 10);

      this.listQuery.dataRange = [time, time];
    },
    dateTimeFocus() {
      this.minDate = undefined;
      // 条件时间查询
      const that = this;
      that.$nextTick(function() {
        document
          .getElementsByClassName('el-picker-panel')[0]
          .addEventListener('click', function() {
            // that.$refs.messageDrop.show();
          });
      });
    },
    queryTime() {
      // this.$refs.messageDrop.show();
    },
    getSummaries() {
      const sums = [];
      this.col.forEach((column, index) => {
        if (index === 0) {
          sums[column.index] = '合计';
          return;
        }
        const values = this.list.map(item => Number(item[column.index]));
        if (!values.every(value => isNaN(value))) {
          sums[column.index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          // sums[index] += ' 元';
        } else {
          sums[column.index] = '';
        }
      });

      this.list.push(sums);
    },

    changeToNumber(data) {
      const newdata = {};
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        for (const key in element) {
          if (element.hasOwnProperty(key)) {
            const objelement = element[key];
            newdata;
          }
        }
        newdata[element];
      }
    },
    infodata() {
      return [
        {
          title: '变更前状态',
          info: '坐席变更工作状态前的状态'
        },
        {
          title: '变更后状态',
          info: '坐席变更工作状态后的状态'
        },
        {
          title: '变更前状态时长',
          info: '变更状态前的工作状态持续时长'
        },
        {
          title: '变更原因',
          info: '包括坐席手动、管理员强制'
        },
        {
          title: '操作时间',
          info: '变更状态的操作时间'
        },
        {
          title: '注意',
          info:
            '坐席登录进入系统默认为离线状态。当坐席未变更状态此时退出系统（退出系统后，坐席状态为离线）那么状态则会记录为：离线-离线'
        }
      ];
    },

    /**
     * 工单量报表API
     */

    APISelectWorkorderList() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      selectWorkorderList({
        customerSource: this.listQuery.CustLocation,
        endTime: this.listQuery.dataRange[1],
        formTypeId: this.listQuery.sheetType,
        startTime: this.listQuery.dataRange[0]
      }).then(res => {
        this.loading.close();
        const dataListList = res.data;
        if (
          !dataListList &&
          typeof dataListList !== 'undefined' &&
          dataListList != 0
        ) {
          this.$XyyMessage.success('暂时无数据');
          this.list = [];
          return;
        }
        if (res.code === 1) {
          if (res.data.length === 0) {
            this.$XyyMessage.success('暂时无数据');
          }
          this.list = res.data;
        } else {
          this.$XyyMessage.error(res.msg);
        }
        // this.getSummaries();
      });
    },
    APIGetTrafficList() {
      getTrafficList().then(res => {
        if (res.code === 1) {
          this.oderCityData = res.data;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    changeList() {
      var parm = this.listQuery.sheetType;
      if (parm) {
        getTraffic(parm).then(res => {
          if (res.code == 0) {
            this.$XyyMessage.error(res.msg);
          } else if (res.code == 1) {
            this.ortoCityData = res.data.userKefuList;
          }
        });
      }
    },
    APIMockGDType() {
      return mockGDType();
    },
    APICityGDType() {
      return mockGDUser();
    }
  }
};
</script>
<style scoped lang="scss">
/deep/.el-date-editor {
  /*width: 490px;*/
}
/deep/.page-header {
  padding-bottom: 0 !important;
}
</style>
<style>
.el-form--inline .biangeng {
  width: 25%;
}
.el-form--inline .biangeng .el-form-item__content {
  width: 65%;
}
.kuaijie {
  font-size: 14px;
  color: #606266;
  padding-right: 15px;
}
.danxaun {
  margin-top: 12px;
}
.search-form {
  border-bottom: 1px dashed #e4e4eb;
  overflow: hidden;
}
.herader-title {
  margin-bottom: 20px;
  padding: 10px 16px;
  font-size: 14px;
  color: #393943;
  background: #e4e4e4;
}

.gdnumclass .el-table th > .cell {
  position: relative;
  word-wrap: normal;
  text-overflow: ellipsis;
  vertical-align: middle;
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  /* display: flex;
    flex-direction: column;
    align-items: flex-start; */
  padding-top: 10px;
  padding-bottom: 10px;
}

.gdnumclass .el-table th > .cell p {
  display: block;
  margin-block-start: 0px;
  margin-block-end: 0px;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  text-align: right;
}

.gdnumclass .el-table th > .cell small {
  font-size: 12px;
  color: #909399;
  display: block;
  margin-block-start: 0px;
  margin-block-end: 0px;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  text-align: right;
}

.gdnumclass .el-table tr td .cell {
  height: 100%;
  line-height: 30px;
  overflow-x: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  word-break: normal;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  padding-left: 10px;
  padding-right: 10px;
  white-space: normal;
  display: block;
}

.gdnum_input_group .el-form-item__error {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  margin-left: 15px;
}
</style>

<style scoped lang="scss">
/deep/.el-table {
  /deep/.el-table__fixed {
    height: auto !important;
    bottom: 16px;
    margin-bottom: 0 !important;
  }
  /deep/.el-table__fixed-body-wrapper {
    top: 44px !important;
  }
}
/deep/.el-table__body tr td {
  border-bottom: 1px solid #ebeef5;
  background: #fff;
}
.el-menu-item.is-active {
  color: #3b95a8 !important;
}
/deep/.el-input__inner {
  height: 36px !important;
  line-height: 36px !important;
}
/deep/.el-button {
  height: 36px;
}
.el-menu.el-menu--horizontal {
  border-bottom: 1px dashed #e4e4eb;
  padding-bottom: 20px;
}
.search-box {
  width: 344px;
  float: right;
  .el-input {
    width: calc(100% - 76px);
    /deep/ .el-input__inner {
      height: 36px;
      line-height: 36px;
    }
  }
  button {
    width: 68px;
    height: 36px;
    float: right;
  }
}
.status-tip {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 2px 2px 0;
  &.open {
    background: #67c23a;
  }
  &.close {
    background: #ff3024;
  }
}

.toprow {
  display: flex;
  flex-direction: row;
}

.timediv {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 14px;
  margin-right: 30px;
}

/*.innerSelTime {*/
/*margin-left: 15px;*/
/*}*/

.gdnumheader {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.gdnum_input_mainheader {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.gdnum_input_group {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.gdnum_button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}

.searchTwoButton {
  width: 100%;
}
.el-form--inline .biangeng {
  width: 25%;
}
</style>
