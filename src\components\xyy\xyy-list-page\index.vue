<template>
  <div class="page">
    <div class="page-header">
      <slot name="header"></slot>
    </div>
    <div class="page-body">
      <slot name="body"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'XyyListPage',
  data() {
    return {};
  },
  methods: {}
};
</script>

<style scoped lang="scss">
.page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .page-header {
    width: 100%;
    padding: 20px;
  }
  .page-body {
    flex: 1;
    height: 665px;
    width: 100%;
    padding: 0 20px 33px;
    box-sizing: border-box;
  }
}
</style>
