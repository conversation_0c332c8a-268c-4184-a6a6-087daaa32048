<template>
  <div class="categories-lv1-board-component-container">
    <div class="category-lv1-title">全部分类</div>
    <div class="category-lv1-content">
      <template v-if="categoryLv1List.length">
        <div class="category-lv1-wrapper">
          <template v-for="item in categoryLv1List">
            <template v-if="item.visible">
              <div class="lv1-item" :key="item.id">
                <el-link @click="toQueryCategoryLv1(item)">{{ item.name }}</el-link>
              </div>
            </template>
          </template>
        </div>
        <template v-if="categoryLv1List.length > 10">
          <el-link
            class="category-lv1-switch"
            :underline="false"
            @click="btnCategoryLv1SwitchClick"
          >
            {{ categoryLv1SwitchON?'收起':'展开' }}
            <i
              :class="categoryLv1SwitchON?'el-icon-arrow-up':'el-icon-arrow-down'"
            ></i>
          </el-link>
        </template>
      </template>
      <template v-else>
        <div class="category-lv1-empty">暂无数据</div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'categoriesLv1Board',
  components: {},
  filters: {},
  props: {
    category: {
      type: Array,
      require: true,
      default: () => [],
    },
  },
  inject: ['knowledgeHall'],
  data() {
    return {
      categoryLv1List: [], //所有的一级分类
      categoryLv1SwitchON: false, //true:展开状态,false:关闭状态
    };
  },
  computed: {},
  watch: {
    category: {
      immediate: true,
      deep: true,
      handler(list) {
        this.getCategoryLv1All(list);
      },
    },
  },
  created() {},
  mounted() {},
  methods: {
    /**
     * 拆解并展示所有的一级分类
     */
    getCategoryLv1All(list) {
      if (!(list && list.length)) {
        return false;
      }
      this.categoryLv1List = [];
      list.map((item, index) => {
        this.categoryLv1List.push({
          id: item.id,
          name: item.name,
          level: item.level,
          visible: index < 10 ? true : false,
        });
      });
    },

    /**
     * 展开/收起点击
     */
    btnCategoryLv1SwitchClick() {
      if (this.categoryLv1SwitchON) {
        //展开状态
        this.categoryLv1List.map((item, index) => {
          if (index >= 10) {
            item.visible = false;
          }
        });
      } else {
        //关闭状态
        this.categoryLv1List.map((item) => {
          item.visible = true;
        });
      }
      this.categoryLv1SwitchON = !this.categoryLv1SwitchON;
    },

    /**
     * 点击一级分类
     */
    toQueryCategoryLv1(categoryLv1) {
      this.knowledgeHall.toQueryCategoryLv1(categoryLv1);
    },
  },
};
</script>

<style lang="scss" scoped>
.categories-lv1-board-component-container {
  width: 100%;
  padding: 20px;
  background-color: #fff;

  .category-lv1-title {
    font-weight: 600;
    color: #303133;
    font-size: 16px;
    margin-bottom: 10px;
  }

  .category-lv1-content {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    background-color: #f5f7fa;
    padding: 20px 20px 0;

    .category-lv1-empty {
      width: 100%;
      margin-bottom: 20px;
      text-align: center;
      font-size: 14px;
      color: #aeaebf;
    }

    .category-lv1-wrapper {
      flex-grow: 1;
      width: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: wrap;

      .lv1-item {
        width: 20%;
        margin-bottom: 20px;
        text-align: left;

        .el-link {
          font-size: 14px;
        }
      }
    }

    .category-lv1-switch {
      width: 80px;
      height: 100%;
      margin-bottom: 20px;
      text-align: center;
      font-size: 14px;
    }
  }
}
</style>