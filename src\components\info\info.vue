<template>
  <div class="explain-question">
    <el-popover
      placement="bottom"
      width="360"
      trigger="hover"
      effect="light"
      :popper-class="['m-pop', popClassList].join(' ')"
    >
      <div v-for="(item,index) in info" :key="index">
        <header>{{ item.title }}</header>
        <div v-if="item.mark" class="line"></div>
        <p>{{ item.info }}</p>
      </div>
      <div slot="reference">
        <svg-icon class="icon-info" icon-class="info"></svg-icon>
      </div>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'Info',
  props: {
    info: {
      type: Array,
      default: function() {
        return [];
      }
    },
    popClassList: {
      type: String
    }
  }
};
</script>

<style scoped>
.line {
  border-top: 1px dashed #dedee5;
  height: 1px;
  margin-bottom: 12px;
}
</style>
