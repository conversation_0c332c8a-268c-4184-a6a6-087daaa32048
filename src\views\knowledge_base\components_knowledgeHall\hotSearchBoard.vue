<template>
  <div class="hot-search-board-component-container">
    <div class="hot-search-head">
      <div class="head-title">热词TOP10</div>
      <div class="head-btns">
        <el-link :underline="false"
                 :class="[activeTab===0?'active':'']"
                 @click="btnTabClick(0)">历史</el-link>
        <el-link :underline="false"
                 :class="[activeTab===1?'active':'']"
                 @click="btnTabClick(1)">当天</el-link>
        <el-link :underline="false"
                 :class="[activeTab===2?'active':'']"
                 @click="btnTabClick(2)">当周</el-link>
        <el-link :underline="false"
                 :class="[activeTab===3?'active':'']"
                 @click="btnTabClick(3)">当月</el-link>
      </div>
    </div>
    <div class="hot-search-body"
         v-loading="loading"
         element-loading-spinner="el-icon-loading"
         element-loading-background="rgba(255,255,255, 0.8)"
         element-loading-text="拼命加载中">
      <template v-if="hotSearchList.length">
        <template v-for="(item,index) in hotSearchList">
          <div :class="['body-item',{'active':item.keyword === highlightKeyword}]"
               :key="index"
               @click="keywordClick(item)">
            <div :class="['item-index',hotSearchRankClass(index)]">{{ index | hotSearchRankNumber }}</div>
            <div class="item-keyword">{{ item.keyword }}</div>
            <div class="item-amount">{{ item.searchCount }}</div>
          </div>
        </template>
      </template>
      <template v-else>
        <div class="hot-search-empty">暂无数据</div>
      </template>
    </div>
    <div class="hot-search-foot"
         @click="gotoCollectionPage()">
      <div class="foot-left">
        <img :src="require('@/assets/work_sheet/icon-collect.png')" />
        我的收藏(
        <span>{{ collectionCount }}</span>)
      </div>
      <i class="el-icon-arrow-right"></i>
    </div>
  </div>
</template>

<script>
import { queryTopKeywords, queryFavoriteCount } from '@/api/knowledge_base';
export default {
  name: 'hotSearchBoard',
  components: {},
  filters: {
    /**
     * 处理排行序号
     */
    hotSearchRankNumber (index) {
      return (index + 1).toString().padStart(2, 0);
    },
  },
  props: {},
  inject: ['knowledgeHall'],
  data () {
    return {
      activeTab: 0,
      sourceList: {}, //请求回来的源数据，包含历史、当日、当周、当月
      hotSearchList: [], //视图绑定的数据
      collectionCount: 0, //收藏总数
      loading: false
    };
  },
  computed: {
    //高亮的关键词
    highlightKeyword: function () {
      return this.knowledgeHall.keyword;
    },
  },
  watch: {},
  created () { },
  mounted () {
    this.queryTopKeywords();
    this.queryFavoriteCount();
  },
  methods: {
    /**
     * 获取top10数据
     */
    queryTopKeywords () {
      return new Promise((resolve, reject) => {
        queryTopKeywords()
          .then((resp) => {
            if (resp.code === 1) {
              this.sourceList = resp.data;
              //初始加载历史
              if (this.sourceList.history && this.sourceList.history.length) {
                this.hotSearchList = this.sourceList.history;
              }
              resolve();
            } else {
              reject();
              this.$XyyMessage.error(resp.msg);
            }
          })
          .catch(() => {
            reject();
            this.$XyyMessage.error('获取TOP10数据失败，请重试！');
          });
      })

    },

    /**
     * 获取收藏总数
     */
    queryFavoriteCount () {
      queryFavoriteCount().then((resp) => {
        if (resp.code === 1) {
          this.collectionCount = resp.data;
        }
      });
    },

    /**
     * 关键词点击
     */
    keywordClick (keyword) {
      this.knowledgeHall.toQueryKnowledgeByKeyword(keyword.keyword);
    },

    /**
     * tab切换
     */
    btnTabClick (index) {
      if (this.activeTab === index) {
        return false;
      }
      this.loading = true;
      this.queryTopKeywords().then(() => {
        // 点击切换切换拉取
        switch (index) {
          case 0:
            //历史
            if (this.sourceList.history && this.sourceList.history.length) {
              this.hotSearchList = this.sourceList.history;
            } else {
              this.hotSearchList = [];
            }
            break;
          case 1:
            //当天
            if (this.sourceList.day && this.sourceList.day.length) {
              this.hotSearchList = this.sourceList.day;
            } else {
              this.hotSearchList = [];
            }
            break;
          case 2:
            //当周
            if (this.sourceList.week && this.sourceList.week.length) {
              this.hotSearchList = this.sourceList.week;
            } else {
              this.hotSearchList = [];
            }
            break;
          case 3:
            //当月
            if (this.sourceList.month && this.sourceList.month.length) {
              this.hotSearchList = this.sourceList.month;
            } else {
              this.hotSearchList = [];
            }
            break;
        }
        this.loading = false;
      });
      this.activeTab = index;
    },

    /**
     * 获取排序样式
     */
    hotSearchRankClass (index) {
      let retClass = '';
      switch (index) {
        case 0:
          retClass = 'first';
          break;
        case 1:
          retClass = 'second';
          break;
        case 2:
          retClass = 'third';
          break;
      }
      return retClass;
    },

    /**
     * 跳转收藏页面
     */
    gotoCollectionPage () {
      this.$router.replace({
        path: `/knowledge_base/knowledgeCollection`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.hot-search-board-component-container {
  width: 400px;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .hot-search-head {
    width: 100%;
    height: 60px;
    padding: 0 20px;
    font-size: 0;
    border-bottom: 1px solid #e4e4eb;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .head-title {
      font-size: 16px;
      font-weight: 600;
      color: #292933;
    }

    .head-btns {
      height: 100%;
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      align-items: stretch;

      .el-link {
        margin-left: 20px;
        box-sizing: border-box;

        &.active {
          color: #3b95a8;
          position: relative;

          :before {
            content: '';
            width: 100%;
            height: 2px;
            background-color: #3b95a8;
            position: absolute;
            top: 0;
            left: 0;
          }
        }
      }
    }
  }

  .hot-search-body {
    flex-grow: 1;
    width: 100%;
    min-height: 454px;
    padding: 0 8px;
    position: relative;

    .body-item {
      width: 100%;
      margin: 6px 0;
      padding: 8px 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &:hover,
      &.active {
        background-color: #f4f9fa;
        cursor: pointer;

        .item-keyword {
          color: #3b95a8;
          font-weight: 500;
        }
      }

      .item-index {
        width: 18px;
        height: 18px;
        line-height: 18px;
        margin-right: 12px;
        font-size: 12px;
        text-align: center;
        background-color: #aeaebf;
        color: #fff;
        border-radius: 2px;

        &.first {
          background-color: #df3d3e;
        }

        &.second {
          background-color: #fa7d3c;
        }

        &.third {
          background-color: #faa90f;
        }
      }

      .item-keyword {
        flex-grow: 1;
        width: calc(100% - 18px - 12px - 64px - 10px);
        font-size: 14px;
        color: #292933;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .item-amount {
        width: 64px;
        margin-left: 10px;
        text-align: right;
        color: #909399;
        font-size: 14px;
      }
    }

    .hot-search-empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #ccc;
    }
  }

  .hot-search-foot {
    width: 100%;
    height: 60px;
    padding: 0 20px;
    border-top: 1px solid #e4e4eb;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:hover {
      .foot-left {
        color: #3b95a8;
      }

      .el-icon-arrow-right {
        color: #3b95a8;
      }
    }

    .foot-left {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      color: #575766;
      font-size: 14px;

      img {
        width: 16px;
        height: 16px;
        margin-right: 10px;
      }

      span {
        color: #3b95a8;
      }
    }

    .el-icon-arrow-right {
      color: #aeaebf;
    }
  }
}
</style>