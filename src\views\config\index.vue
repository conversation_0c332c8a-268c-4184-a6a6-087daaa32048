<template>
  <div>
    <xyy-button
      icon-class="configbtn"
      class-name="configbtn"
      class="configBtn"
      @click="config"
    >重复工单校验</xyy-button>
    <xyy-button
      icon-class="classificationIcon"
      class-name="configbtn"
      class="configBtn configBtn1"
      @click="configTem"
    >问题分类-模板配置</xyy-button>
    <xyy-button
      icon-class="config_OrderNum"
      class-name="configbtn"
      class="configBtn configBtn1"
      @click="configOrderNum"
    >领取工单数配置</xyy-button>
    <xyy-button
      icon-class="workSheetTagIcon"
      class-name="configbtn"
      class="configBtn configBtn1"
      @click="configOrderTag"
    >工单标签配置</xyy-button>
  </div>
</template>

<script>
export default {
  name: 'ConfigIndx',
  data() {
    return {};
  },
  methods: {
    config() {
      this.$router.push({
        path: '/worksheet/config-start'
      });
    },
    configTem() {
      this.$router.push({
        path: '/worksheet/configuration'
      });
    },
    configOrderNum() {
      this.$router.push({
        path: '/worksheet/configOrderNum'
      });
    },
    configOrderTag() {
      this.$router.push({
        path: '/worksheet/configOrderTag'
      });
    }
  }
};
</script>
<style lang="scss">
.el-button.configBtn.el-button--primary.is-plain {
  margin: 20px 0 0 20px;
  width: 196px;
  height: 78px;
  color: #292933;
  border: 1px solid #e4e4eb;
  padding: 8px 23px;
}
.el-button.configBtn.el-button--primary.configBtn1 {
  width: auto;
}
.el-button.configBtn.el-button--primary.is-plain:hover {
  border: 1px solid #40a3b8;
  color: #40a3b8;
  background-color: #fff;
}
.svg-icon.configbtn {
  width: 50px;
  height: 50px;
  vertical-align: middle;
}
</style>
<style scoped lang="scss">
</style>

