<template >
  <div class="customer-management">
    <xyy-list-page>
      <template slot="header">
        <div>
          <el-form ref="queryList" :model="queryList" class="customer-management-box">
            <el-row class="innerEl" type="flex" justify="space-between" align="middle">
              <el-form-item class="serverNum" label="客户ID" label-width="60px" prop="id">
                <el-input
                  v-model="queryList.id"
                  class="serverName"
                  size="small"
                  clearable
                  placeholder="请输入ID"
                />
              </el-form-item>

              <el-form-item class="serverNum" label="药店名称" label-width="80px" prop="realName">
                <el-input
                  v-model="queryList.realName"
                  class="serverName"
                  size="small"
                  clearable
                  placeholder="请输入名称"
                />
              </el-form-item>

              <el-form-item class="serverNum" label="地址" label-width="40px" prop="address">
                <el-input
                  v-model="queryList.address"
                  class="serverName"
                  size="small"
                  clearable
                  placeholder="请输入地址"
                />
              </el-form-item>

              <el-form-item class="serverNum" label="手机号" label-width="60px" prop="mobile">
                <el-input
                  v-model="queryList.mobile"
                  class="serverName"
                  size="small"
                  clearable
                  placeholder="请输入手机号"
                />
              </el-form-item>
            </el-row>

            <el-row type="flex" class="innerEl top_20" justify="space-between" align="middle">
              <el-form-item class="serverNum" label="行政域" label-width="60px" prop="registerBranch">
                <el-select v-model="queryList.registerBranch" placeholder="全国" clearable>
                  <el-option
                    v-for="item in registerBranchData"
                    :key="item.branchCode"
                    :label="item.branchName"
                    :value="item.branchCode"
                  ></el-option>
                </el-select>
              </el-form-item>

              <el-form-item class="serverNum" label="绑定CA" label-width="80px" prop="ca">
                <el-input
                  v-model="queryList.ca"
                  class="serverName"
                  size="small"
                  clearable
                  placeholder="请输入CA名称"
                />
              </el-form-item>

              <el-form-item
                class="serverNum"
                label="是否添加企业微信"
                label-width="130px"
                prop="isAddWechat"
              >
                <el-select
                  v-model="queryList.isAddWechat"
                  placeholder="全部"
                  clearable
                  style="width: 100px;"
                >
                  <el-option label="全部" value></el-option>
                  <el-option
                    v-for="item in isAddWechatList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>

              <el-form-item>
                <xyy-button size="medium" @click="bulkBindCA">批量绑定CA</xyy-button>
                <xyy-button size="medium" @click="handerImport">导入</xyy-button>
              </el-form-item>
              <el-form-item>
                <el-button
                  plain
                  type="primary"
                  size="medium"
                  class="searchCondition"
                  @click="checkTimer(handerSearch('queryList'))"
                >查询</el-button>
                <el-button plain size="medium" @click="resetForm">重置</el-button>
              </el-form-item>
            </el-row>
          </el-form>
        </div>
      </template>

      <template slot="body">
        <el-row type="flex" justify="space-between" align="top" class="customer-management-table">
          <xyy-table
            v-loading="tabLoading"
            ref="orderTable"
            :data="list"
            :list-query="listQuery"
            :col="col"
            :operation="operation"
            :offset-top="240"
            :has-selection="true"
            @get-data="getList"
            @selectionCallback="setCheckedDatas"
          >
            <!-- @operation-click="operationClick" -->
            <!-- 是否添加企业微信 -->
            <template slot="isAddWechat" slot-scope="{ col }">
              <el-table-column :key="col.index" :label="col.name" :width="col.width">
                <template slot-scope="{ row }">
                  {{ row.isAddWechat === 1?'是':'否' }}
                  <el-button
                    type="text"
                    size="medium"
                    class="tag-btn"
                    icon="el-icon-edit"
                    circle
                    @click="editWechat(row)"
                  ></el-button>
                </template>
              </el-table-column>
            </template>
            <!--操作-->
            <template slot="operation" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :label="col.name"
                :prop="col.index"
                :render-header="renderHeader"
                :width="col.width"
                fixed="right"
              >
                <template slot-scope="scope">
                  <el-row>
                    <el-button type="text" @click="handerBind(scope.$index, scope.row)">绑定CA</el-button>
                    <el-button
                      v-if="scope.row.kefuName !== '无'"
                      type="text"
                      @click="handerCheck(scope.$index, scope.row)"
                    >查看CA信息</el-button>
                  </el-row>
                </template>
              </el-table-column>
            </template>
          </xyy-table>
        </el-row>
      </template>
    </xyy-list-page>

    <!-- 点击导入按钮弹出框-开始-->
    <el-dialog
      :visible="centerDialogImport"
      :close-on-click-modal="false"
      :show-close="!uploading"
      title="提示"
      custom-class="import-box"
      top="0"
      @close="close"
    >
      <div class="content-body">
        <span ref="content" class="import-content" v-html="content"></span>
        <el-upload
          ref="importFile"
          :before-upload="beforeUpload"
          :on-progress="handleProgress"
          :on-success="handleSuccess"
          :on-error="handleError"
          :action="url"
          :headers="header"
          :with-credentials="true"
          accept=".xls, .xlsx"
        >
          <el-button v-show="!uploading && !completed" size="small" type="primary">上传文件</el-button>
          <div slot="file" slot-scope="{file}">
            <el-progress :percentage="percent"></el-progress>
            <span class="file-name">{{ file.name }}</span>
          </div>
        </el-upload>
      </div>
      <span class="dialog-footer" slot="footer">
        <el-button v-show="completederr" size="small" type="primary" @click="abortFile()">下载</el-button>
        <el-button v-show="completed" size="small" type="primary" @click="done">确定</el-button>
      </span>
    </el-dialog>

    <!-- 批量绑定CA -->
    <el-dialog :visible.sync="groupOpen" title="绑定CA-选择人员" width="400px" custom-class="user-box">
      <div class="content-body">
        <div>
          <el-input
            v-model="searchKey"
            placeholder="搜索人员名称"
            maxlength="20"
            suffix-icon="el-icon-search"
          />
        </div>
        <div>
          <el-radio-group v-if="filterUserList.length" v-model="radioCA">
            <el-radio
              v-for="(item, index)  in filterUserList"
              :label="item.id +'-'+item.stuffNumAndName"
              :key="index"
            >{{ `${item.name}-${item.code}` }}</el-radio>
          </el-radio-group>
        </div>
      </div>

      <span class="dialog-footer" slot="footer">
        <el-button @click="dialogCancelCA">取 消</el-button>
        <el-button type="primary" @click="dialogDetermineCA">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 是否添加企业微信 -->
    <el-dialog :visible.sync="isWechat" title="是否添加企业微信" class="dialogInner" width="400px">
      <el-radio-group v-model="radio" prop="radio" style="line-height: 30px;">
        <el-radio :label="1">是</el-radio>
        <el-radio :label="0">否</el-radio>
      </el-radio-group>

      <span class="dialog-footer" slot="footer">
        <el-button @click="dialogCancelWechat">取 消</el-button>
        <el-button type="primary" @click="dialogDetermineWechat">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 二维码弹窗 -->
    <el-dialog
      :visible="qrCodeDialogVisible"
      custom-class="qrCodeDialog"
      :show-close="false"
      title="关联CA企业微信"
      class="dialogInner"
      width="40%"
    >
      <div class="qrtips">图片已生成，可截图分享或右键保存到本地</div>
      <img id="avatar" :src="avatar?avatar:defaultAvatar" shape="square" />
      <img id="qrcode" :src="qrCodeText?qrCodeText:defaultAvatar" shape="square" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="qrCodeDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  findCAExclusiveMerchantInfoList,
  findAllBranchList,
  customCAInfoExport,
  importCustomer,
  cAExclusiveKeFuBind,
  isAddWechat,
  getEmployeeList
} from '@/api/crm';
import QRCode from 'qrcodejs2';
import _avatar from '@/assets/common/user-avatar.png';
export default {
  name: 'customerManagement',
  data() {
    return {
      created: false,
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      queryList: {
        realName: '',
        mobile: '',
        id: '',
        registerBranch: '',
        address: '',
        ca: '',
        isAddWechat: ''
      },
      registerBranchData: [],
      list: [],
      datas: {},
      col: [
        { index: 'id', name: '客户id', width: 160 },
        { index: 'realName', name: '药店名称', width: 220 },
        { index: 'businessTypeName', name: '客户类型', width: 120 },
        { index: 'defaultContactor', name: '会员姓名', width: 200 },
        { index: 'mobile', name: '手机号', width: 140 },
        { index: 'city', name: '市', width: 120 },
        { index: 'district', name: '区', width: 220 },
        { index: 'defaultAddress', name: '地址', width: 200 },
        { index: 'registerBranch', name: '行政域', width: 120 },
        { index: 'status', name: '状态', width: 120 },
        { index: 'licenseStatus', name: '资质状态', width: 120 },
        { index: 'sysUserName', name: '销售人员', width: 120 },
        { index: 'createTime', name: '注册时间', width: 220 },
        { index: 'kefuName', name: '绑定CA', width: 160 },
        {
          index: 'isAddWechat',
          name: '是否添加企业微信',
          slot: true,
          width: 140
        },
        {
          index: 'operation',
          name: '操作',
          slot: true,
          width: 200
        }
      ],
      operation: [
        {
          name: '绑定CA',
          type: 0
        },
        {
          name: '查看CA信息',
          type: 1
        }
      ],
      options: [],
      optionProps: {
        value: 'id',
        label: 'name',
        checkStrictly: true
      },
      isAddWechatList: [
        { id: 1, name: '是' },
        { id: 2, name: '否' }
      ],
      centerDialogImport: false, // 点击导入按钮弹出框
      uploading: false, // 上传标识
      content: '',
      completed: false, // 导入完成
      completederr: false, // 部分成功部分失败
      result: {
        total: 0,
        success: 0,
        fail: 0
      }, // 导入结果
      datas: [], // 导入数据
      downloadUrl: '', // 失败数据地址
      url: process.env.BASE_API_IM + '/cAExclusiveKeFu/importCustomer',
      searchKey: '',
      employeeDatas: [], // 员工数据
      radioCA: '',
      customers: [],
      groupOpen: false, // 弹框状态
      radio: 0,
      customerId: '',
      isWechat: false,
      qrCodeDialogVisible: false,
      qrCodeText: '', // 绘制二维码文本
      originPath: '', //源路径
      defaultAvatar: _avatar,
      avatar: '',
      tabLoading: false,
      header: { businessPartCode: this.getBusinessPartCode() },
      code: ''
    };
  },
  created() {
    this.created = true;
    this.APIfindAllBranchList();
    this.getEmployeeDatas();
  },
  activated() {
    if (!this.created) this.getList(this.listQuery);
    this.created = false;
  },
  computed: {
    filterUserList() {
      return this.employeeDatas.filter(
        el =>
          this.formatStr(el.name).search(this.formatStr(this.searchKey)) >= 0 ||
          this.formatStr(el.code).search(this.formatStr(this.searchKey)) >= 0,
        this
      );
    }
  },
  mounted() {},
  methods: {
    getBusinessPartCode() {
      let code = '';
      if (
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
      ) {
        code = this.$store.getters.channel.businessPartCode;
      }
      return code;
    },
    formatStr(val) {
      val = val.replace(/[\'\"\\\/\b\f\n\r\t]/g, '');
      val = val.replace(/[\@\#\$\%\^\&\*\(\)\{\}\:\"\L\<\>\?\[\]]/g, '');
      return val;
    },
    handerSearch(formName) {
      (this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      }),
        this.getList(this.listQuery);
    },
    getList: function(listQuery) {
      const { page, pageSize } = listQuery;
      let params = {
        pageNum: page,
        pageSize,
        id: this.queryList.id, // 客户id
        realName: this.queryList.realName, // 客户名称
        mobile: this.queryList.mobile, // 手机号
        registerCode: this.queryList.registerBranch // 行政域编号
      };
      this.tabLoading = true;
      findCAExclusiveMerchantInfoList(params)
        .then(res => {
          this.tabLoading = false;
          const { total } = res.data;
          this.list = res.data.list;

          let status = {};
          let licenseStatus = {};
          let kefuName = {};
          let isAddWechat = {};
          this.list.forEach((item, index) => {
            status = {};
            licenseStatus = {};
            kefuName = {};
            isAddWechat = {};
            // 1.激活，2.未激活，3.冻结
            if (item.status == 1) {
              status = '激活';
            } else if (item.status == 2) {
              status = '未激活';
            } else if (item.status == 3) {
              status = '冻结';
            } else {
              status = '-';
            }
            // 1.资质未提及，2.资质已提交，3.资质已过期，4.资质已通过, 5首营资质审核中,6首营资质一审通过
            if (item.licenseStatus == 1) {
              licenseStatus = '资质未提及';
            } else if (item.licenseStatus == 2) {
              licenseStatus = '资质已提交';
            } else if (item.licenseStatus == 3) {
              licenseStatus = '资质已过期';
            } else if (item.licenseStatus == 4) {
              licenseStatus = '资质已通过';
            } else if (item.licenseStatus == 5) {
              licenseStatus = '首营资质审核中';
            } else if (item.licenseStatus == 6) {
              licenseStatus = '首营资质一审通过';
            } else {
              licenseStatus = '-';
            }

            if (item.kefuName) {
              kefuName = item.kefuName;
            } else {
              kefuName = '无';
            }

            if (item.isAddWechat == 1) {
              isAddWechat = 1;
            } else {
              isAddWechat = 0;
            }

            this.list[index] = Object.assign({}, this.list[index], {
              status: status,
              licenseStatus: licenseStatus,
              kefuName: kefuName,
              isAddWechat: isAddWechat
            });
          });

          // 根据地址搜索条件查询
          if (this.queryList.address) {
            let that = this;
            let listFiltered = [];
            this.list.forEach((item, index) => {
              if (
                item.defaultAddress &&
                item.defaultAddress.includes(that.queryList.address)
              ) {
                listFiltered.push(item);
              }
            });
            this.list = listFiltered;
          }

          // 根据绑定CA名称搜索条件查询
          if (this.queryList.ca) {
            let that = this;
            let listFiltered = [];
            this.list.forEach((item, index) => {
              if (item.kefuName && item.kefuName.includes(that.queryList.ca)) {
                listFiltered.push(item);
              }
            });
            this.list = listFiltered;
          }

          // 根据是否添加企业微信搜索条件查询
          if (this.queryList.isAddWechat) {
            let that = this;
            let listFiltered = [];
            this.list.forEach((item, index) => {
              if (
                item.isAddWechat == (that.queryList.isAddWechat == 2 ? 0 : 1)
              ) {
                listFiltered.push(item);
              }
            });
            this.list = listFiltered;
          }

          this.listQuery = {
            ...this.listQuery,
            page: Number(res.data.pageNum),
            pageSize: Number(res.data.pageSize),
            total: Number(res.data.total)
          };
        })
        .catch(() => {
          this.tabLoading = false;
        });
    },

    // 获取员工
    getEmployeeDatas() {
      getEmployeeList({ id: '-1' })
        .then(res => {
          if (res.code === 1) {
            this.employeeDatas = res.data;
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },

    // 获取行政域
    APIfindAllBranchList() {
      findAllBranchList().then(res => {
        if (res.code === 1) {
          this.registerBranchData = res.data;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    /**
     * 设置选中数据
     */
    setCheckedDatas(datas) {
      // this.customers = datas.map(el => el.id);
      this.customers = datas.map(el => {
        return {
          customerId: el.id
        };
      });
    },

    /**
     * 绑定CA
     */
    handerBind(index, row) {
      this.customers = [];
      this.radioCA = '';

      this.customers.push({ customerId: row.id });
      this.groupOpen = true;
    },

    // 批量绑定CA
    bulkBindCA() {
      if (this.customers.length <= 0) {
        this.$XyyMessage.warning('请选择客户');
        return;
      }
      this.groupOpen = true;
    },

    // 绑定caAPI
    dialogDetermineCA() {
      this.groupOpen = false;
      this.radio = this.radioCA.split('-');
      let params = {
        customers: this.customers, // 客户id
        kefuId: this.radio[0], // 客服id
        kefuName: this.radio[1] // 客服id
      };
      cAExclusiveKeFuBind(params).then(res => {
        if (res.code === 1) {
          this.$XyyMessage.success('绑定成功');
          this.$refs.orderTable.clearSelection();
          this.getList(this.listQuery);
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    dialogCancelCA() {
      this.radioCA = '';
      this.customers = [];
      this.groupOpen = false;
    },

    // 是否添加企业微信
    editWechat(row) {
      this.radio = 0;
      this.customerId = '';

      this.radio = row.isAddWechat;
      this.customerId = row.id;
      this.isWechat = true;
    },

    // 是否添加企业微信API
    dialogDetermineWechat() {
      this.isWechat = false;
      let params = {
        customerId: this.customerId, // 客户id
        isAddWechat: this.radio // 是否绑定微信
      };
      isAddWechat(params).then(res => {
        if (res.code === 1) {
          this.getList(this.listQuery);
          // this.list = this.list.map(item => {
          //   if (row.id === item.id) {
          //     item.isAddWechat = this.radio;
          //   }
          //   return item;
          // });
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    dialogCancelWechat() {
      this.radio = 0;
      this.customerId = '';
      this.isWechat = false;
    },

    // 查看CA信息
    handerCheck(index, row) {
      // 查看二维码
      // document.getElementById('qrcode') &&
      //   (document.getElementById('qrcode').innerHTML = '');
      this.avatar = row.avatar;
      this.qrCodeText = row.qrCode;
      this.qrCodeDialogVisible = true;
    },

    /**
     * 二维码dialog打开
     */
    qrCodeDialogOpened() {
      // 查看二维码
      this.$nextTick(() => {
        new QRCode(this.$refs.qrCodeUrl, {
          text: this.qrCodeText, // 需要转换为二维码的内容
          width: 160,
          height: 160,
          colorDark: '#000000',
          colorLight: '#ffffff',
          correctLevel: QRCode.CorrectLevel.H
        });
      });
    },

    // 点击导入按钮事件
    handerImport() {
      this.centerDialogImport = true;
      // this.content = `请先<a class="DownloadTem">下载导入模板</a>，再点击上传按钮进行数据上传`;
      this.content = `请点击上传按钮进行数据上传`;
      this.uploading = false;
      this.percent = 0;
      this.completed = false;
      this.completederr = false;
      // this.result = {
      //   total: 0,
      //   success: 0,
      //   fail: 0
      // };
      this.datas = [];
      this.downloadUrl = '';
    },

    /**
     * 上传中回调
     */
    handleProgress(event, file) {
      if (file) {
        this.uploading = true;
        this.percent = Number(event.percent.toFixed(0));
        if (this.percent > 99) this.percent = 99;
      }
    },
    /**
     * 下载失败数据回调
     */
    abortFile() {
      if (this.downloadUrl) {
        // 下载操作
        const url = this.downloadUrl;
        this.download(url);
        this.close();
      }
      // const url = `${process.env.BASE_IMG_URL}/${this.downloadUrl}`;
      // this.$refs['importFile'].abort(file);
      // this.$refs['importFile'].uploadFiles = [];
      // this.uploading = false;
    },
    /**
     * 上传成功回调
     */
    handleSuccess(res, file) {
      if (res.code === 1) {
        this.percent = 100;
        this.completed = true;
        this.content = '上传完成';
        this.$refs['importFile'].uploadFiles = [];
        // this.result = {
        //   total: res.data.totalNum,
        //   success: res.data.okNum,
        //   fail: res.data.failNum
        // };
        // this.datas = res.data.options;
        // if (res.data.failNum) {
        //   this.downloadUrl = res.data.failUrl;
        // }
      } else {
        if (res.data && res.data.downExcelUrl) {
          this.completederr = true;
          this.completed = true;
          this.content = '请点击"下载"按钮下载失败数据';
          this.downloadUrl = res.data.downExcelUrl;
        }
        this.$XyyMessage.error(res.msg);
        this.$refs['importFile'].uploadFiles = [];
      }
      this.uploading = false;
    },
    /**
     * 上传失败回调
     */
    handleError(res) {
      this.$refs['importFile'].uploadFiles = [];
      this.uploading = false;
      this.$XyyMessage.error(`文件上传失败`);
    },
    /**
     * 上传之前
     */
    beforeUpload(file) {
      if (file.type) {
        if (
          ![
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          ].includes(file.type)
        ) {
          this.$XyyMessage.error(`只能选择.xls或.xlsx格式的文件`);
          return false;
        }
      }
    },

    /**
     * 关闭回调
     */
    close() {
      this.centerDialogImport = false;
    },

    /**
     * 确定操作
     */
    done() {
      // if (this.downloadUrl) {
      //   // 下载操作
      //   const url = `${process.env.BASE_IMG_URL}/${this.downloadUrl}`;
      //   this.download(url);
      //   this.close();
      // } else {
      //   // 保存数据
      //   this.$emit('importCallback', this.datas);
      //   this.close();
      // }
      // 保存数据
      this.$emit('importCallback', this.datas);
      this.close();
    },

    /**
     * 下载方法
     */
    download(url) {
      const a = document.createElement('a');
      a.href = url;
      a.click();
    },

    // 表头插入导入按钮
    renderHeader() {
      return (
        <div>
          操作
          <el-button size="small" on-click={() => this.exportExcel()}>
            {' '}
            <span class="el-icon-upload2"></span> 导出
          </el-button>
        </div>
      );
    },
    // 导出表格事件
    exportExcel() {
      // 查询列表数据
      const that = this;
      if (!that.list || !that.list.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      const param = {
        id: this.queryList.id, // 客户id
        realName: this.queryList.realName, // 客户名称
        mobile: this.queryList.mobile, // 手机号
        registerCode: this.queryList.registerBranch // 行政域编号
      };
      let url = ``;
      customCAInfoExport(param).then(res => {
        if (res.hasOwnProperty('code')) {
          that.$XyyMessage.error(res.msg);
        } else {
          // 申请接口带入参数查询数据
          url = `${process.env.BASE_API_IM}/cAExclusiveKeFu/customCAInfo/export?id=${this.queryList.id}&realName=${this.queryList.realName}&mobile=${this.queryList.mobile}&registerCode=${this.queryList.registerBranch}`;
          const a = document.createElement('a');
          a.href = url;
          a.click();
        }
      });
    },

    resetForm() {
      (this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      }),
        (this.queryList = {
          realName: '',
          mobile: '',
          id: '',
          registerBranch: '',
          address: '',
          ca: '',
          isAddWechat: ''
        });
      this.getList(this.listQuery);
    }
  }
};
</script>
<style scoped lang="scss">
.customer-management {
  /deep/.page-body {
    width: 100%;
  }
  /deep/.table-containter {
    width: 100%;
    min-height: 400px;
  }
  /deep/.el-table__empty-block {
    width: 100 !important;
  }
  .DownloadTem {
    color: rgba(64, 163, 184, 1);
  }
  .customer-management-box {
    padding-bottom: 20px;
    margin-bottom: 10px;
    border-bottom: 1px dotted #e4e4eb;
    .innerEl {
      height: 38px;
      /deep/.el-input__inner {
        width: 100%;
        height: 36px;
        line-height: 36px;
      }
      /deep/.el-form-item {
        margin: 0;
      }
      .timeSel {
        width: 300px;
      }
      /deep/input[type='number']::-webkit-inner-spin-button,
      /deep/input[type='number']::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      .btnNew {
        color: #fff;
        background-color: rgba(59, 149, 168, 1);
      }
      .serverName {
        width: 200px;
      }
      .serverNum {
        margin-right: 20px;
      }
      .searchCondition.is-plain {
        background: rgba(59, 149, 168, 1);
        color: #fff;
      }
      &.top_20 {
        margin-top: 20px;
      }
    }
  }

  /deep/.el-dialog.import-box {
    width: 400px;
    height: 240px;
    top: 50%;
    transform: translateY(-50%);
    .el-dialog__header {
      height: 50px;
      padding: 14px 20px;
      box-sizing: border-box;
      border-bottom: 1px solid rgba(238, 238, 238, 1);
      .el-dialog__title {
        height: 22px;
        font-size: 16px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: rgba(48, 49, 51, 1);
        line-height: 22px;
        float: left;
      }
      .el-dialog__headerbtn {
        top: 16px;
      }
    }
    .el-dialog__body {
      height: 130px;
      padding: 20px;
      position: relative;
      .import-content {
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(41, 41, 51, 1);
        a {
          color: #3b95a8;
        }
      }
      .import-result {
        margin: 0;
        padding: 0;
        overflow: hidden;
        dt {
          float: left;
          margin: 0 30px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(144, 147, 153, 1);
          &:first-child {
            margin-left: 0;
          }
          &:last-child {
            margin-right: 0;
          }
        }
      }
      > div {
        position: relative;
        height: 113px;
        .el-button {
          height: 36px;
          line-height: 36px;
          padding: 0 11px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(255, 255, 255, 1);
          position: absolute;
          bottom: 0px;
          left: 50%;
          transform: translateX(-50%);
        }
        .el-upload-list {
          position: absolute;
          top: 18px;
          width: 100%;
          li {
            margin: 0;
            padding-top: 16px;
            height: 95px;
            .el-progress {
              height: 6px;
              top: 0px;
              .el-progress-bar {
                border-radius: 3px;
                height: 100%;
                width: calc(100% - 40px);
                float: left;
                .el-progress-bar__outer {
                  height: 100% !important;
                }
              }
              .el-progress__text {
                top: 50%;
                transform: translateY(-50%);
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: rgba(144, 147, 153, 1);
              }
            }
            &:hover {
              background: none;
              .el-progress__text {
                display: inline;
              }
            }
            &:focus {
              outline: none;
            }
            span.file-name {
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: rgba(144, 147, 153, 1);
            }
          }
        }
      }
      > .el-button {
        height: 36px;
        line-height: 36px;
        padding: 0 19px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        position: absolute;
        bottom: 20px;
        right: 20px;
      }
    }
  }

  /deep/.el-dialog.user-box {
    width: 750px;
    height: 500px;
    transform: translateY(-50%);
    top: 30%;
    .el-dialog__header {
      height: 50px;
      padding: 15px 20px;
      .el-dialog__title {
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(41, 41, 51, 1);
        height: 22px;
        line-height: 22px;
      }
      .el-dialog__headerbtn {
        top: 18px;
      }
    }

    .el-dialog__body {
      padding: 5px 20px 0;
      height: calc(100% - 126px);

      .content-body {
        height: calc(100% - 55px);
        padding: 15px 20px 5px;
        overflow-y: auto;
        .el-input {
          margin-bottom: 12px;
          .el-input__inner {
            padding-left: 12px;
          }
        }
        .el-radio-group {
          .el-radio {
            display: block;
            margin-bottom: 12px;
            font-weight: normal;
          }
        }
      }
    }
  }

  /* 表格内 icon 样式 */
  .el-icon-plus:before {
    background-color: transparent;
  }
  /* 标签按钮 */
  .tag-btn {
    height: auto !important;
    margin: 2px 0;
  }

  .customer-management-table {
    /deep/ {
      .el-table__fixed-header-wrapper {
        button {
          background: rgba(0, 0, 0, 0);
          border: 0;
          color: rgba(59, 149, 168, 1);
          .el-icon-upload2 {
            &:before {
              @extend button;
            }
          }
        }
      }
    }
  }

  /deep/.qrCodeDialog {
    .qrtips {
      padding: 0 20px 30px;
      font-size: 16px;
    }

    #avatar {
      margin: 0 20px;
      width: 160px;
      height: 160px;
    }

    #qrcode {
      width: 160px;
      height: 160px;
      display: inline-block;
      /deep/ img {
        margin: 0 auto;
      }
    }
  }
}
</style>
