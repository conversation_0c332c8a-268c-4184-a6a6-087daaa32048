<template>
  <div class="im_kindeditor">
    <!-- http://kindeditor.net/docs/option.html -->
    <editor
      id="editor_msg"
      height="170px"
      width="100%"
      :pasteType="2"
      :htmlTags="{
        span: [],
        img : ['src', 'width', 'height', 'border', 'alt', 'title', 'align', '.width', '.height', '.border'],
        'br,tbody,tr,strong,b,sub,sup,em,i,u,strike,s,del': [],
      }"
      ref="kindeditor"
      :content="editorText"
      :loadStyleMode="false"
      :pluginsPath="'/pc/static/plugins/'"
      :items="['emoticonss', 'imageinsert','fileinsert', 'transfer','fontsize','forecolor','history','sendcard']"
      @on-content-change="onContentChange"
      style="color:green;"
      :style="autoHeight"
    ></editor>
    <!-- 自动回复浮窗 :designMode ='false'-->
    <div class="autoreply" v-show="searchFlag">
      <div v-if="!getDatelist.length">
        <div
          v-for="(item,index) in replyKeyWorkds.alltypes"
          :name="index"
          :key="index"
          class="auto-box"
        >
          <div
            v-for="(info,sIndex) in replyKeyWorkds.allinfos"
            v-if="item.id==info.typeid"
            :key="sIndex"
            class="common-list"
            @click="handleListClick(info.answer)"
          >
            <p>
              <span>{{ (item.name + '：' + info.answer).substr(0, (item.name + '：' + info.answer).indexOf(searchVal)) }}</span>
              <span style="color:#3B95A8">{{ searchVal }}</span>
              <span>{{ (item.name + '：' + info.answer).substr((item.name + '：' + info.answer).indexOf(searchVal) + searchVal.length) }}</span>
            </p>
          </div>
        </div>
      </div>
      <div v-else>
        <div class="auto-box">
          <div
            v-for="(info,sIndex) in getDatelist"
            :key="sIndex"
            class="common-list"
            @click="handleListClick(info)"
          >
            <p>
              <span>{{ (info).substr(0, (info).indexOf(searchVal)) }}</span>
              <span style="color:#3B95A8">{{ searchVal }}</span>
              <span>{{ (info).substr((info).indexOf(searchVal) + searchVal.length) }}</span>
            </p>
          </div>
        </div>
      </div>
    </div>
    <!-- imageinsert -->
    <el-upload
      :action="uploadurl"
      ref="elupload"
      :before-upload="beforeHandleAvatarSuccess"
      :show-file-list="true"
      :hidden="imglistShow"
    >
      <input slot="trigger" id="filemanage" hidden="true"/>
    </el-upload>

    <!-- fileinsert -->
    <el-upload
      :action="fileuploadurl"
      ref="elFileupload"
      :before-upload="beforeUploadFile"
      :show-file-list="false"
      :hidden="filelistShow"
    >
      <input slot="trigger" id="fileinsertbtn" hidden="true"/>
    </el-upload>

    <!-- transfer -->
    <input type="button" id="transferbtn" hidden="true" @click="transferAction()"/>
    <!-- history -->
    <input type="button" id="historybtn" hidden="true" @click="historyAction()"/>
    <!-- 评价 -->
    <input type="button" id="assessbtn" hidden="true" @click="assessAction()"/>
    <!-- 发送名片 -->
    <input type="button" id="sendcardbtn" hidden="true" @click="sendcardAction()"/>
    <transdialog
      :data="transdata"
      :dialogFormVisible="transAlerShow"
      @treedialog="transdialogAction"
    ></transdialog>
  </div>
</template>

<script>
import {uploadimg, onlinekefu} from '@/api/im_view';
import {getReplyKeyWords} from '@/api/im_view/commonUse';
import {drawimg} from '../../im_view/components/im_container_tool/message_tool';
import transdialog from './im_center_chatinput_treedialog';
import {sendMessage} from '@/api/im_view/index';
// import { transData } from './mockdata';
import {resolve} from 'q';
import axios from 'axios';

export default {
  components: {
    transdialog
  },
  props: [
    'allcontent',
    'uploadurl',
    'fileuploadurl',
    'containerId',
    'contentEdit'
  ],
  inject: ['compIMViewMain'],
  data() {
    return {
      editorText: '',
      transAlerShow: false,
      imglistShow: false,
      filelistShow: false,
      transdata: [],
      fv: process.env.BASE_API,
      kindetor: undefined,
      searchVal: '', //搜索框的值
      searchFlag: true, //自动回复框显隐
      cloneReplyKeyWorkds: {}, //自动回复数据克隆
      replyKeyWorkds: {
        allinfos: [],
        alltypes: []
      }, //自动回复数据
      numActive: 0, //当前选中list值
      allLength: 0, //搜索框的值
      editorElement: null, //编辑器的绑定元素
      getDatelist: [],
      autoHeight: '170px'
    };
  },
  created() {
    const that = this;
    this.emits(this, 'register', {
      type: 'kuaijiehuifu_kfim',
      uid: this._uid,
      fn: val => {
        that.editorText = that.editorText || '';
        if (val.hasOwnProperty('answer')) {
          let qa = val.answer;
          this.onContentChange(qa, 'kjhf');
        } else if (val.hasOwnProperty('smallurl')) {
          // let changeIMG =
          //   '<img ' +
          //   ' data-ke-src="' +
          //   val.smallurl +
          //   '" ' +
          //   'onclick="showRaw(\'' +
          //   val.bigurl +
          //   '\')" ' +
          //   'src="' +
          //   val.smallurl +
          //   '" />';
          // this.onContentChange(changeIMG, 'kjhf');
          var imgsrc = '<img ' + 'src="' + val.smallurl + '" />';
          var changeIMG =
            '<img ' +
            'onclick="showRaw(\'' +
            val.bigurl +
            '\')" ' +
            'src="' +
            val.smallurl +
            '" />';
          this.onContentChange((that.editorText += imgsrc));
          this.$emit('clickInputTool', 'changeSrc', {
            newvalue: changeIMG,
            oldvalue: imgsrc
          });
        } else if (val.hasOwnProperty('link')) {
          let qa =
            val.title +
            '<br>' +
            val.des +
            '<a href="' +
            val.link +
            '">' +
            val.link +
            '</a>';
          this.onContentChange(qa, 'kjhf');
        }
      }
    });
  },
  activated() {
    window.downloadMedia = function (url) {
      window.open(url);
    }
  },
  mounted() {
    // this.afterChange();
    // console.log(this.$refs.kindeditor.editor.edit.doc);
    // this.apiOnlinekefu();
    this.removeComposition();
  },
  methods: {
    // initKindeditor() {
    //   var _this = this;
    //   // 默认参数
    //   var options = {
    //     uploadJson: 'upload/image',
    //     width: '100%',
    //     afterChange() {
    //       _this.localValue = this.html();
    //     }
    //   };
    //   // 调用外部参数
    //   if (_this.options) {
    //     for (var i in _this.options) {
    //       options[i] = _this.options[i];
    //     }
    //   }
    //   KindEditor.create(_this.$refs.kindeditor, options);
    // },
    // readyfunction(ke) {
    //   alert(ke);
    // },

    pasteFunction(ev) {
      alert(ev);
    },
    afterChange() {
      let doc = this.$refs.kindeditor.editor.edit.doc;
      let cmd = this.$refs.kindeditor.editor.edit.cmd;
      let _this = this;

      this.$refs.kindeditor.editor.edit.textarea.bind('paste', function (event) {
        binding.value(event);
      });
      // doc.body.bind('onpaste', null, function(event) {
      //   alert('111111111');
      //   binding.value(event);
      // });
      console.log(this.$refs.kindeditor);
      return;
      doc.addEventListener('onpaste', null, function (event) {
        binding.value(event);
      });
      return;
      doc.body['paste'] = function (ev) {
        var dataItem = ev.originalEvent.clipboardData.items[0];
        //判断文件类型
        if (dataItem.kind == 'file' && dataItem.type.indexOf('image/') !== -1) {
          let file = dataItem.getAsFile();

          if (!/\.(jpg|png|JPG|PNG|bmp|BMP|gif|GIF)$/.test(file.name)) {
            alert('不支持上传的文件类型');
            return;
          }
          let srcg = file.size;
          if (srcg / 1024 / 1024 > 2) {
            alert('图片超过2M不能上传');
            return;
          }
        }
      };

      return;
      doc.body.bind('paste', function (ev) {
        var dataItem = ev.originalEvent.clipboardData.items[0];
        //判断文件类型
        if (dataItem.kind == 'file' && dataItem.type.indexOf('image/') !== -1) {
          let file = dataItem.getAsFile();

          if (!/\.(jpg|png|JPG|PNG|bmp|BMP|gif|GIF)$/.test(file.name)) {
            alert('不支持上传的文件类型');
            return;
          }
          let srcg = file.size;
          if (srcg / 1024 / 1024 > 2) {
            alert('图片超过2M不能上传');
            return;
          }

          _this.beforeHandleAvatarSuccess(file);
        }
      });
    },
    afterCreate() {
      try {
        let doc = this.$refs.kindeditor.editor.edit.doc;
        let cmd = this.$refs.kindeditor.editor.edit.cmd;
        doc.addEventListener(
          'paste',
          function (e) {
            console.log(e);
          },
          false
        );
      } catch (e) {
      }
    },
    //内容改变时触发
    onContentChange(val, type) {
      this.editorElement = this.$refs.kindeditor.editor.cmd.doc.body;
      if (this.$refs.kindeditor.editor.cmd.sel.anchorNode) {
        if (
          this.$refs.kindeditor.editor.cmd.sel.anchorNode.data == '/' &&
          this.searchFlag
        ) {
          this.numActive = 1;
          this.numActive = 0;
          this.searchVal = '';
          this.applyClass();
          this.getReplyKeyWords();
          this.removeComposition();
          this.addComposition();
        }
      }
      this.keyDown();

      //添加了粘贴图片的逻辑
      if (this.hasBase64Image(val)) {
        //替换掉粘贴进去的base64图片
        let div = document.createElement('div');
        div.style.display = 'none';
        div.innerHTML = val;
        document.body.append(div);

        //获取到粘贴的base64图片
        let pastedImage = null;
        div.querySelectorAll('img').forEach(item => {
          if (this.hasBase64Image(item.src)) {
            pastedImage = item;
          }
        });

        const loading = this.$loading({
          target: document.querySelector(
            '.im-view-center-chat-input-container'
          ),
          lock: true,
          spinner: 'el-icon-loading',
          text: '上传中...',
          background: 'rgba(255,255,255, 0.8)'
        });

        //上传图片
        this.uploadPastedBase64Image(pastedImage.src)
          .then(resp => {
            if (resp.code === 1) {
              //this.editorText = val.replace(pastedImage.outerHTML, resp.data);
              let img = document.createElement('img');
              img.src = resp.path.thumbnailPath;
              img.setAttribute(
                'onclick',
                `showRaw("${resp.path.originalPath}")`
              );

              //图片替换成上传后的链接
              //div.insertBefore(img, pastedImage);
              //div.removeChild(pastedImage);
              if (div.hasChildNodes(pastedImage)) {
                div.querySelectorAll('img').forEach(item => {
                  if (item === pastedImage) {
                    div.insertBefore(img, item);
                    item.remove();
                  }
                });
              }

              this.editorElement.innerHTML = this.editorText = div.innerHTML;

              //普通消息
              this.$emit(
                'clickInputTool',
                'changeContent',
                this.editorText,
                type
              );

              //emit,store
              var imgsrc = '<img ' + 'src="' + resp.path.thumbnailPath + '" />';
              var changeIMG =
                '<img ' +
                'onclick="showRaw(\'' +
                resp.path.originalPath +
                '\')" ' +
                'src="' +
                resp.path.thumbnailPath +
                '" />';
              this.$emit('clickInputTool', 'changeSrc', {
                newvalue: changeIMG,
                oldvalue: imgsrc
              });
            }
          })
          .catch(err => {
            this.$XyyMessage.error(err.message);

            //上传失败，移除输入框中粘贴的base64图片
            //div.removeChild(pastedImage);
            if (div.hasChildNodes(pastedImage)) {
              div.querySelectorAll('img').forEach(item => {
                if (item === pastedImage) {
                  item.remove();
                }
              });
            }

            this.editorElement.innerHTML = this.editorText = div.innerHTML;
            //普通消息
            this.$emit(
              'clickInputTool',
              'changeContent',
              this.editorText,
              type
            );
          })
          .finally(() => {
            //记录id key是会话id，value是编辑文案
            this.$store.commit('updataEditContentData', {
              key: this.containerId,
              value: this.editorText
            });

            // 解决设置内容后光标不在最后的问题
            this.$nextTick(() => {
              this.$refs.kindeditor.editor.focus();
              this.$refs.kindeditor.editor.appendHtml('');
            });

            div.remove();

            setTimeout(() => {
              loading.close();
            }, 100);
          });
      } else {
        this.editorText = val;
        this.$emit('clickInputTool', 'changeContent', this.editorText, type); //普通消息
        //记录id key是会话id，value是编辑文案
        this.$store.commit('updataEditContentData', {
          key: this.containerId,
          value: val
        });
      }
    },

    //判断是否包含图片base64串
    hasBase64Image(str) {
      // if (str === '' || str.trim() === '') {
      //   return false;
      // }
      // try {
      //   return btoa(atob(str)) == str;
      // } catch (err) {
      //   return false;
      // }
      if (str.indexOf('data:image/') !== -1 && str.indexOf(';base64,') !== -1) {
        return true;
      } else {
        return false;
      }
    },

    //将base64图片转成file对象后上传
    uploadPastedBase64Image(imageBase64Str) {
      return new Promise((resolve, reject) => {
        try {
          let suffixName = imageBase64Str.substring(
            imageBase64Str.indexOf('/') + 1,
            imageBase64Str.indexOf(';base64,')
          ); //后缀名
          let imageFile = this.blobToFile(
            this.base64ToBlob(imageBase64Str),
            new Date().getTime().toString() + '.' + suffixName
          ); //base64转成file对象

          //上传图片
          //校验图片格式
          if (
            !['jpg', 'jpeg', 'png', 'bmp', 'gif'].includes(
              imageFile.name
                .substring(imageFile.name.lastIndexOf('.') + 1)
                .toLowerCase()
            )
          ) {
            reject({code: 0, message: '文件格式不正确，请重新选择！'});
          }

          //校验附件大小,单位字节,限制20M
          if (imageFile.size > 20 * 1024 * 1024) {
            reject({code: 0, message: '文件过大，限制20M，请重新选择！'});
          }

          drawimg(imageFile, val => {
            let formData = new FormData();
            formData.append('imgFile', val);
            axios.defaults.withCredentials = true;
            axios
              .post(this.uploadurl, formData, {
                headers: {
                  'Content-Type': 'multipart/form-data'
                }
              })
              .then(res => {
                if (res.data.code === 1) {
                  resolve({
                    code: 1,
                    path: {
                      originalPath: res.data.data.originalPath,
                      thumbnailPath: res.data.data.thumbnailPath
                    }
                  });
                } else {
                  reject({code: 0, message: res.msg || '服务异常！'});
                }
              })
              .catch(err => {
                reject({code: 0, message: '图片上传失败，请重试！'});
              });
          });
        } catch (e) {
          reject({code: 0, message: '图片上传失败，请重试！'});
        }
      });
    },

    //粘贴图片为base64,将base64转换成file对象
    base64ToBlob(base64Data) {
      let arr = base64Data.split(','),
        fileType = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        l = bstr.length,
        u8Arr = new Uint8Array(l);

      while (l--) {
        u8Arr[l] = bstr.charCodeAt(l);
      }
      return new Blob([u8Arr], {
        type: fileType
      });
    },

    //将blob转file
    blobToFile(newBlob, fileName) {
      newBlob.lastModifiedDate = new Date();
      newBlob.name = fileName;
      return newBlob;
    },

    //上传之前图片处理
    beforeHandleAvatarSuccess(file) {
      console.log(file, 'file');
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG或PNG 格式!');
        return false;
      }
      this.imglistShow = true;
      var that = this;
      // drawimg(file, function(val) {
      let formData = new FormData();
      formData.append('imgFile', file);
      formData.append("type", "image");
      axios.defaults.withCredentials = true;
      axios
        .post(that.uploadurl, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        .then(res => {
          if (res.data.code === 1) {
            var imgsrc =
              '<img ' + 'src="' + res.data.data.thumbnailPath + '" />';
            var changeIMG =
              '<img ' +
              'onclick="showRaw(\'' +
              res.data.data.originalPath +
              '\')" ' +
              'src="' +
              res.data.data.thumbnailPath +
              '" />';
            //debug IM-450
            that.editorText = that.editorText || '';
            that.onContentChange((that.editorText += imgsrc));
            that.$emit('clickInputTool', 'changeSrc', {
              newvalue: changeIMG,
              oldvalue: imgsrc
            });
          } else {
            that.$message(res.msg || '服务异常');
          }
        })
        .catch(err => {
          console.log(err);
        });
      // });
    },

    //上传附件
    beforeUploadFile(file) {
      //校验附件格式
      if (
        ['mov', 'mp4', 'avi', 'pdf', 'xls', 'xlsx', 'doc', 'docx'].includes(
          file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
        )
      ) {
        // 视频文件校验附件大小,单位字节,限制500M
        if (
          ['mov', 'mp4', 'avi'].includes(
            file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
          )
        ) {
          if (file.size > 200 * 1024 * 1024) {
            this.$XyyMessage.error('文件过大，限制200M！');
            return false;
          }
        }
        // 文件文件校验附件大小,单位字节,限制20M
        if (
          ['pdf', 'xls', 'xlsx', 'doc', 'docx'].includes(
            file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
          )
        ) {
          // 校验附件大小,单位字节,限制20M
          if (file.size > 20 * 1024 * 1024) {
            this.$XyyMessage.error('文件过大，限制20M！');
            return false;
          }
        }
      } else {
        this.$XyyMessage.error('文件格式不正确！');
        return false;
      }

      let oldContainerId = this.containerId;

      let chatlist_ing = this.compIMViewMain.$refs['im_view_left'].chatlist_ing;
      let oldPreviewMsg = '';
      chatlist_ing.forEach(item => {
        if (oldContainerId == item.id) {
          oldPreviewMsg = item.assignLastMessage;
        }
      });

      this.compIMViewMain.exchangeChatingListLastMsgPreview(
        oldContainerId,
        '消息发送中'
      );

      let formData = new FormData();
      formData.append('file', file);
      axios.defaults.withCredentials = true;
      axios
        .post(this.fileuploadurl, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        .then(resp => {
          if (resp.data.code === 1) {
            let filePath = resp.data.data; //filePath 文件路径
            let messageTxt = ''; //拼接消息体标签
            switch (
              filePath.substring(filePath.lastIndexOf('.') + 1).toLowerCase()
              ) {
              case 'mov':
              case 'mp4':
              case 'avi':
                messageTxt = `<video src="${filePath}" x5-video-player-type="h5" controls="controls" height="250px" width="150px"></video>
                              <div  class="btn xyyignore" style="width:56px;line-height:26px">
                                  <div style="background-color: #00c675;border: 1px solid #00c675;outline: none;color: #fff;padding: 0 12px;border-radius: 2px;-webkit-appearance: none;display: inline-block;font-size:12px;"
                                      onclick="downloadMedia('${filePath}')"
                                >下载</div>
                              </div>`;
                break;
              case 'pdf':
                messageTxt = `<div class="msg-card-file" style="background: #ffffff; padding: 4px;">
                  <div
                    style="word-break: break-all;display: flex;justify-content: flex-start;align-items: center;"
                  >
                    <div class="file-info" style="flex-grow: 1;width: 195px">
                      <div style="font-size: 13px; font-weight: 500 ;color: #333333;display: inline-block;">
                        ${file.name}
                      </div>
                      <div
                        class="file-size"
                        style="font-size: 12px; font-weight: 400; color: #999999; display: inline-block;"
                      >${this.previewSize(file)}</div>
                      <div class="btn" style="width:56px;line-height:26px">
                        <div
                          style="background-color: #00c675;border: 1px solid #00c675;outline: none;color: #fff;padding: 0 12px;border-radius: 2px;-webkit-appearance: none;display: inline-block;font-size:12px;"
                          onclick="showRaw('${filePath}')"
                          data-filename="${file.name}"
                        >预览</div>
                      </div>
                    </div>
                  </div>
                  <img style="display: none;" data-filename = "${
                  file.name
                }" src="${filePath}">
                </div>`;
                break;
              case 'xlsx':
              case 'xls':
              case 'doc':
              case 'docx':
                // messageTxt = `<img onclick="showRaw('${filePath}')" src="https://upload.test.ybm100.com/G1/M00/1B/DD/Cgoz02Bz4SyAKW9KAAASuxwBeMs091.jpg" />`;
                filePath = filePath + '?filename=' + file.name;
                messageTxt = `<div class="msg-card-file" style="background: #ffffff; padding: 4px;">
                  <div
                    style="word-break: break-all;display: flex;justify-content: flex-start;align-items: center;"
                  >
                    <div class="file-info" style="flex-grow: 1;width: 195px">
                      <div style="font-size: 13px; font-weight: 500 ;color: #333333;display: inline-block;">
                        ${file.name}
                      </div>
                      <div
                        class="file-size"
                        style="font-size: 12px; font-weight: 400; color: #999999; display: inline-block;"
                      >${this.previewSize(file)}</div>
                      <div class="btn" style="width:56px;line-height:26px">
                        <div
                          style="background-color: #00c675;border: 1px solid #00c675;outline: none;color: #fff;padding: 0 12px;border-radius: 2px;-webkit-appearance: none;display: inline-block;font-size:12px;"
                          onclick="showRaw('${filePath}')"
                          data-filename="${file.name}"
                        >下载</div>
                      </div>
                    </div>
                  </div>
                  <img style="display: none;" data-filename = "${
                  file.name
                }" src="${filePath}">
                </div>`;
                break;
              default:
                messageTxt = `<object classid="" width="150" height="250" border="0"><param name="src" value="${filePath}"></object>`;
                break;
            }
            //附件直接发送消息
            // this.$emit('sendFileMessage', messageTxt);
            this.sendFileMessage(messageTxt, oldContainerId, oldPreviewMsg);
          } else {
            this.$XyyMessage.error('文件上传失败，请稍后重试！');
            this.compIMViewMain.exchangeChatingListLastMsgPreview(
              oldContainerId,
              oldPreviewMsg
            );
          }
        })
        .catch(err => {
          this.$XyyMessage.error(err.msg);
          this.compIMViewMain.exchangeChatingListLastMsgPreview(
            oldContainerId,
            oldPreviewMsg
          );
        });
    },

    //上传附件，直接发送消息
    sendFileMessage(messageTxt, oldContainerId, oldPreviewMsg) {
      sendMessage(oldContainerId, {
        dialogid: oldContainerId,
        msgText: messageTxt
      })
        .then(res => {
          if (res.code === 1) {
          } else {
            this.$message.error(res.msg);
            this.compIMViewMain.exchangeChatingListLastMsgPreview(
              oldContainerId,
              oldPreviewMsg
            );
          }
        })
        .catch(() => {
          this.compIMViewMain.exchangeChatingListLastMsgPreview(
            oldContainerId,
            oldPreviewMsg
          );
        });
    },

    previewSize(file) {
      return file.size < 1000
        ? file.size + 'B'
        : file.size < 1024000
          ? (file.size / 1000).toFixed(2) + 'KB'
          : (file.size / 1024000).toFixed(2) + 'MB';
    },

    // 转接弹窗弹出
    transferAction() {
      this.apiOnlinekefu();
    },

    // 转接弹窗的方法回调
    transdialogAction(type, value) {
      if (type === 'hiddenAction') {
        //是否隐藏
        this.transAlerShow = value;
      } else if (type === 'clickNode') {
        //点击跳转的节点
        this.$emit('clickInputTool', 'transfera', value);
      }
    },

    // 历史消息
    historyAction() {
      this.$emit('clickInputTool', 'history', '');
    },

    // 发送名片
    sendcardAction() {
      this.$emit('clickInputTool', 'sendcard', '');
    },

    // 要求用户评论
    assessAction() {
      this.$emit('clickInputTool', 'assess', '');
    },

    cleanInput() {
      this.editorText = '';
    },

    apiOnlinekefu() {
      this.transAlerShow = true;
      onlinekefu({dialogid: this.containerId}).then(res => {
        var kefuData = res.data;
        for (let index = 0; index < kefuData.length; index++) {
          const element = kefuData[index];
          element.kefus.splice(0, 0, {
            id: element.id,
            name: element.name,
            type: element.type
          });
          for (let index1 = 0; index1 < element.kefus.length; index1++) {
            const element1 = element.kefus[index1];
            element1['numrotio'] = '80%';
            if (element1.type === 'group') {
              element1.icon = 'el-icon-transfer-group';
            } else if (element1.type === 'kefu') {
              element1.icon = 'el-icon-transfer-user';
            }
          }
        }

        this.transdata = kefuData;
      });
    },

    compositionListener(event) {
      var e = window.event || event;
      this.searchVal += e.data;
      this.searchResult(this.searchVal);
    },

    // 绑定输入事件
    addComposition() {
      this.searchFlag = true;
      this.editorElement.addEventListener(
        'compositionend',
        this.compositionListener,
        false
      );
    },
    // 移除输入事件
    removeComposition() {
      this.searchFlag = false;
      this.getDatelist = [];
      if (this.editorElement)
        this.editorElement.removeEventListener(
          'compositionend',
          this.compositionListener,
          false
        );
    },
    //获取接口数据
    getReplyKeyWords() {
      getReplyKeyWords().then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.cloneReplyKeyWorkds = JSON.parse(JSON.stringify(res.data));
        this.replyKeyWorkds = JSON.parse(JSON.stringify(res.data));
      });
    },
    //传入匹配关键字
    searchResult(keyWorkds) {
      if (!keyWorkds) {
        this.replyKeyWorkds = JSON.parse(
          JSON.stringify(this.cloneReplyKeyWorkds)
        );
        this.cloneList();
        return;
      }
      var arr = [];
      this.replyKeyWorkds.allinfos.splice(
        0,
        this.replyKeyWorkds.allinfos.length
      );
      this.replyKeyWorkds.alltypes.splice(
        0,
        this.replyKeyWorkds.alltypes.length
      );
      this.cloneReplyKeyWorkds.alltypes.forEach(item => {
        if (item.name.indexOf(keyWorkds) >= 0) {
          this.replyKeyWorkds.alltypes.push(item);
          if (arr.indexOf(item.id) < 0) {
            arr.push(item.id);
            this.cloneReplyKeyWorkds.allinfos.forEach(typeItem => {
              if (item.id == typeItem.typeid) {
                this.replyKeyWorkds.allinfos.push(typeItem);
              }
            });
          }
        }
      });
      this.cloneReplyKeyWorkds.allinfos.forEach(item => {
        if (item.answer.indexOf(keyWorkds) >= 0) {
          this.replyKeyWorkds.allinfos.push(item);
          if (arr.indexOf(item.typeid) < 0) {
            arr.push(item.typeid);
            this.cloneReplyKeyWorkds.alltypes.forEach(typeItem => {
              if (item.typeid == typeItem.id) {
                this.replyKeyWorkds.alltypes.push(typeItem);
              }
            });
          }
        }
      });
      this.cloneList();
    },
    cloneList() {
      this.replyKeyWorkds.alltypes.forEach(item => {
        this.replyKeyWorkds.allinfos.forEach(typeItem => {
          if (typeItem.typeid == item.id) {
            this.getDatelist.push(item.name + '：' + typeItem.answer);
          }
        });
      });
      var arrList = [];
      for (var i = 0; i < this.getDatelist.length; i++) {
        if (arrList.indexOf(this.getDatelist[i]) == -1) {
          arrList.push(this.getDatelist[i]);
        }
      }
      this.getDatelist = arrList;
    },
    //选中后替换
    handleListClick(item) {
      item = item.substring(item.indexOf('：') + 1, item.length);
      var oldData = this.$refs.kindeditor.editor.cmd.sel.anchorNode.data.substring(
        0,
        this.$refs.kindeditor.editor.cmd.sel.baseOffset -
        this.searchVal.length -
        1
      );
      var newData = this.$refs.kindeditor.editor.cmd.sel.anchorNode.data.substring(
        this.$refs.kindeditor.editor.cmd.sel.baseOffset -
        this.searchVal.length -
        1
      );
      newData = newData.replace('/' + this.searchVal, item);
      this.$refs.kindeditor.editor.cmd.sel.anchorNode.data = oldData + newData;
      this.editorText = this.$refs.kindeditor.editor.cmd.doc.body.innerHTML;
      // 解决设置内容后光标不在最后的问题
      this.$nextTick(() => {
        this.$refs.kindeditor.editor.focus();
        this.$refs.kindeditor.editor.appendHtml('');
      });
      this.removeComposition();
    },
    //绑定编辑器事件
    keyDown() {
      this.editorElement.removeEventListener(
        'keydown',
        this.editorKeydown,
        false
      );
      this.editorElement.addEventListener('keydown', this.editorKeydown, false);
      this.editorElement.removeEventListener('keyup', this.editorKeyup, false);
      this.editorElement.addEventListener('keyup', this.editorKeyup, false);
      this.editorElement.addEventListener('click', this.editorFocus, false);
    },
    //键盘按键事件
    editorKeydown(event) {
      var e = window.event || event;
      if (e.keyCode == 191) {
        this.numActive = 0;
        this.searchVal = '';
        this.applyClass();
        this.getReplyKeyWords();
        this.removeComposition();
        this.addComposition(e);
      } else if (e.keyCode == 8 || e.keyCode == 37 || e.keyCode == 39) {
        this.removeComposition();
      } else if (e.keyCode == 229) {
        if (this.searchFlag) {
          this.removeComposition();
          this.addComposition(e);
        } else {
        }
      } else if (e.keyCode == 38) {
        if (this.searchFlag) {
          e.preventDefault();
          if (this.numActive == 0) {
            this.numActive = 0;
          } else {
            this.numActive = this.numActive - 1;
          }
          document.querySelector('.autoreply').scrollTop = 36 * this.numActive;
        }
      } else if (e.keyCode == 40) {
        if (this.searchFlag) {
          e.preventDefault();
          if (this.numActive == this.allLength - 1) {
            this.numActive == this.allLength - 1;
          } else {
            this.numActive = this.numActive + 1;
          }
          document.querySelector('.autoreply').scrollTop = 36 * this.numActive;
        }
      } else if (e.keyCode == 13) {
        e.preventDefault();
        e.stopPropagation();
        if (this.searchFlag) {
          document
            .querySelectorAll('.autoreply .common-list')
            [this.numActive].click();
        }
      } else if (e.keyCode == 16) {
      } else {
        this.removeComposition();
      }
    },
    //键盘上下事件 194
    editorKeyup(event) {
      var e = window.event || event;
      let sendMsgBtn = document.querySelector('.send-chat-msg');
      if (sendMsgBtn && e.keyCode == 13) {
        e.preventDefault();
        e.stopPropagation();
        this.removeComposition();
        sendMsgBtn.click();
      }
      if (e.keyCode == 38 || e.keyCode == 40) {
      } else {
        this.numActive = 1;
        this.numActive = 0;
        document.querySelector('.autoreply').scrollTop = 0;
      }
    },
    applyClass() {
      console.log(this.$refs.kindeditor.editor);
      console.log(
        this.$refs.kindeditor.editor.cmd.sel.anchorNode.parentElement.offsetTop
      );
      var numScroll = 0;
      numScroll =
        this.$refs.kindeditor.editor.cmd.sel.anchorNode.parentElement
          .offsetTop -
        this.editorElement.scrollTop +
        70;
      // +  this.$refs.kindeditor.editor.cmd.sel.anchorNode.parentElement.offsetHeight
      document
        .querySelector('.autoreply')
        .setAttribute('style', 'min-width:300px');
      if (numScroll >= 180) {
        document.querySelector('.autoreply').style.top = '180px';
      } else {
        document.querySelector('.autoreply').style.top = numScroll + 'px';
      }
    },
    editorFocus() {
      this.removeComposition();
    }
  },
  watch: {
    numActive: {
      handler(val, oldval) {
        this.allLength = document.querySelectorAll(
          '.autoreply .common-list'
        ).length;
        if (this.allLength >= 0) {
          for (var i = 0; i < this.allLength; i++) {
            document.querySelectorAll('.autoreply .common-list')[
              i
              ].style.background = '#fff';
          }
          if (document.querySelectorAll('.autoreply .common-list').length)
            document.querySelectorAll('.autoreply .common-list')[
              val
              ].style.background = '#dae9ec';
        }
      },
      deep: true
    },
    contentEdit: {
      handler(newData, oldData) {
        this.editorText = '';
        if (newData) {
          this.editorText = newData.editorTextIn;
        }
      },
      deep: true
    }
  }
};
</script>

<style lang='scss'>
.im_kindeditor {
  height: 170px;

  .ke-content {
    width: 100%;
    height: 100%;
  }

  #editor_msg {
    border: none;
    resize: none;
  }
}

.im_kindeditor .ke-icon-imageinsert {
  background-image: url('/pc/static/plugins/imageinsert/images/imageinset.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.im_kindeditor .ke-icon-fileinsert {
  background-image: url('/pc/static/plugins/fileinsert/images/fileinsert.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.im_kindeditor .ke-icon-transfer {
  background-image: url('/pc/static/plugins/transfer/image/zhuanyi.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  /* background-color: green; */
  width: 18px;
  height: 18px;
}

.im_kindeditor .ke-icon-history {
  background-image: url('/pc/static/plugins/history/images/history.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.im_kindeditor .ke-icon-assess {
  background-image: url('/pc/static/plugins/assess/images/assess.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.im_kindeditor .ke-icon-emoticonss {
  background-image: url('/pc/static/plugins/emoticonss/images/emotion.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.im_kindeditor .ke-icon-fontsize {
  background-image: url('/pc/static/plugins/fontsize/images/fontsize.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.im_kindeditor .ke-icon-forecolor {
  background-image: url('/pc/static/plugins/fontcolor/images/fontcolor.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.im_kindeditor .ke-icon-sendcard {
  background-image: url('/pc/static/plugins/sendcard/images/sendcard.png');
  background-position: 0px 0px;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
}

.im_kindeditor .ke-toolbar {
  border-bottom: 0px;
  background-color: #fff;
  padding: 2px 5px;
  text-align: left;
  overflow: hidden;
  zoom: 1;
}

.im_kindeditor .ke-statusbar {
  position: relative;
  background-color: #fff;
  border-top: 0px solid #cccccc;
  font-size: 0;
  line-height: 0;
  height: 0px;
  overflow: hidden;
  text-align: center;
  cursor: s-resize;
}

.im_kindeditor .ke-container {
  display: block;
  border: 0px;
  border-top: 1px solid #dcdee3;
  background-color: #fff;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

// 回复悬浮窗样式
.im_kindeditor {
  position: relative;

  .autoreply {
    position: absolute;
    left: 0;
    top: 176px;
    z-index: 1000;
    max-width: 600px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
    border-radius: 2px;
    border: 1px solid rgba(220, 222, 227, 1);
    overflow: auto;
    max-height: 150px;

    .auto-box {
      cursor: pointer;

      .common-list {
        padding-left: 10px;

        p {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          height: 36px;
          line-height: 36px;
          font-size: 14px;
          color: #60606e;
        }

        &:hover {
          background-color: #dae9ec !important;
        }
      }

      &:nth-of-type(1) {
        .common-list:nth-of-type(1) {
          background-color: #dae9ec;
        }
      }
    }
  }

  .autoreply::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }

  .autoreply::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #f5f7fa;
  }

  .autoreply::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    background: #fff;
  }
}
</style>
