<?xml version="1.0" encoding="UTF-8"?>
<svg width="50px" height="50px" viewBox="0 0 50 50" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 60.1 (88133) - https://sketch.com -->
    <title>编组</title>
    <desc>Created with Sketch.</desc>
    <g id="1.7" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="参数配置" transform="translate(-1142.000000, -153.000000)">
            <g id="编组-2备份-3" transform="translate(1142.000000, 153.000000)">
                <g id="编组" transform="translate(2.000000, 2.000000)">
                    <circle id="椭圆形" fill="#E9F3F6" cx="23" cy="23" r="23"></circle>
                    <path d="M12,9.5 C11.1715729,9.5 10.5,10.1715729 10.5,11 L10.5,34.3776629 C10.5,34.6604464 10.5799358,34.9374701 10.7305877,35.1767826 C11.1719293,35.8778597 12.0980426,36.0884169 12.7991197,35.6470753 L22,29.8549426 L31.2008803,35.6470753 C31.4401928,35.7977272 31.7172165,35.8776629 32,35.8776629 C32.8284271,35.8776629 33.5,35.2060901 33.5,34.3776629 L33.5,11 C33.5,10.1715729 32.8284271,9.5 32,9.5 L12,9.5 Z" id="矩形" stroke="#40A3B8" fill="#FFFFFF"></path>
                    <polygon id="多边形" fill="#3B95A8" transform="translate(33.000000, 32.000000) rotate(90.000000) translate(-33.000000, -32.000000) " points="33 25 39.0621778 28.5 39.0621778 35.5 33 39 26.9378222 35.5 26.9378222 28.5"></polygon>
                    <polygon id="多边形" fill="#40A3B8" transform="translate(34.000000, 32.000000) rotate(90.000000) translate(-34.000000, -32.000000) " points="34 25 40.0621778 28.5 40.0621778 35.5 34 39 27.9378222 35.5 27.9378222 28.5"></polygon>
                    <circle id="椭圆形" fill="#FFFFFF" cx="34" cy="32" r="3"></circle>
                    <rect id="矩形" fill="#D4EBF2" x="15" y="14" width="14" height="3" rx="1"></rect>
                    <rect id="矩形" fill="#C0E5F0" x="15" y="20" width="14" height="3" rx="1"></rect>
                    <path d="M36.3983007,20.5785353 L38.4469024,16.4889741 C38.6942606,15.9951803 39.2950831,15.7954048 39.7888769,16.0427631 C39.8652109,16.0810014 39.9363338,16.1288578 40.0005101,16.1851645 L41.4850016,17.4876196 C41.9001493,17.8518596 41.9414181,18.4836782 41.577178,18.898826 C41.5369827,18.9446392 41.4926832,18.9866819 41.4448327,19.0244291 L37.9117396,21.8115351 C37.4781309,22.1535905 36.8493308,22.0793719 36.5072754,21.6457631 C36.2670618,21.3412548 36.2245896,20.9253094 36.3983007,20.5785353 Z" id="路径-5" fill="#FFB557" transform="translate(38.388310, 19.961011) rotate(-5.000000) translate(-38.388310, -19.961011) "></path>
                    <path d="M39.0941081,23.89881 L40.2866618,21.4993015 C40.532463,21.0047308 41.1326531,20.8030633 41.6272238,21.0488645 C41.705795,21.0879143 41.7788852,21.1371348 41.8446117,21.1952583 L42.4951709,21.7705617 C42.9088905,22.136423 42.9476875,22.7683982 42.5818262,23.1821179 C42.5431668,23.2258344 42.5007547,23.2660829 42.4550756,23.3024025 L40.3883105,24.9456944 C40.0197152,25.2387657 39.4833284,25.1775415 39.1902571,24.8089462 C38.9843114,24.5499286 38.946831,24.1951429 39.0941081,23.89881 Z" id="路径-5备份" fill="#FFDEB3" transform="translate(40.388310, 23.831004) rotate(27.000000) translate(-40.388310, -23.831004) "></path>
                </g>
            </g>
        </g>
    </g>
</svg>