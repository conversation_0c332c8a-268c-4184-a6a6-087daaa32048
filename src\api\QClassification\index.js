import request from '@/utils/request';
import { formData } from '@/utils/index';

/**
 *问题分类相关报表
 * @param {params}查询参数
 */
export function getReport (params) {
  return request({
    url: '/workorderReport/selectQuestionTypeList',
    method: 'post',
    params: params
  });
}

/**
 *问题分类相关报表
 * @param {params}查询参数
 */
export function exportData (params) {
  return request({
    url: '/workOrder/export/getProblemClassificationExport',
    method: 'get',
    params: params
  });
}

/**
 *问题分类-配置模板：问题分类树
 * @param {params}查询参数
 */
export function getProblemClassTree (params) {
  return request({
    url: '/businessType/tree',
    method: 'get',
    params: params
  });
}

/**
 *问题分类-配置模板：保存（删除、修改）问题分类树
 * @param {params}查询参数
 */
export function deleteSaved (data, params) {
  return request({
    url: `/businessType/save?formTypeId=${params}`,
    method: 'post',
    data
  });
}

/**
 *问题分类-配置模板：模板列表
 * @param {params}查询参数  judgeProcessClass
 */
export function getTemplate (params) {
  return request({
    url: '/template/base/listTemplate?status=1&type=0',
    method: 'get',
    params: params
  });
}

/**
 *问题分类-配置模板：判断是否流程条件
 * @param {params}查询参数
 */
export function judgeProcessClass (params) {
  return request({
    url: '/businessType/checkIsConditionField',
    method: 'get',
    params: params
  });
}

/**
 *问题分类-配置模板：上传文件接口
 * @param {params}查询参数
 */
export function UploadFile (params) {
  return request({
    url: '/businessType/analysisExcel',
    method: 'post',
    data: params
  });
}

/**
 *问题分类-配置模板：问题分类删除引用校验
 * @param {params}查询参数
 */
export function getCheck (params) {
  return request({
    url: 'businessType/checkIsConditionField',
    method: 'get',
    params: params
  });
}

/**
 * 下载管理：列表
 * @param {params}查询参数
 */
export function getDownManList (params) {
  return request({
    url: '/workOrder/export/page',
    method: 'get',
    params: params
  });
}

/**
 * 下载管理：下载
 * @param {params}查询参数
 */
export function getDownload (params) {
  return request({
    url: '/workOrder/export/download/' + params,
    method: 'get'
  });
}

/**
 *  问题分类-工单类型
 * @param {params}查询参数
 */
export function getWorkType (params) {
  return request({
    url: '/form/type/listFormType',
    method: 'get',
    params: params
  });
}

/**
 *  问题分类-添加问题分类
 * @param {params}查询参数
 */
export function addProblemType (params) {
  return request({
    url: 'businessType/addOne',
    method: 'post',
    data: formData(params)
  });
}

/**
 *  问题分类-编辑问题分类
 * @param {params}查询参数
 */
export function modifyProblemType (params) {
  return request({
    url: 'businessType/update',
    method: 'put',
    data: formData(params)
  });
}

/**
 *  问题分类-删除问题分类
 * @param {params}查询参数
 */
export function deleteProblemType (params) {
  return request({
    url: 'businessType/delete/'+ params,
    method: 'delete',
  });
}
