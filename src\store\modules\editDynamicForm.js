const editDynamicForm = {
  state: {
    editDynamicFormData: {}
  },
  mutations: {
    updataEditDynamicFormData(state, payload) {
      const { key, value } = payload;
      state.editDynamicFormData[key] = value;
      // console.log(state.editDynamicFormData);
    },
    deleteDynamicFormData(state, payload) {
      const { key } = payload;
      // delete state.editDynamicFormData[key];
      state.editDynamicFormData[key] = "";
      // console.log(state.editDynamicFormData);
    }
  }
};
export default editDynamicForm;
