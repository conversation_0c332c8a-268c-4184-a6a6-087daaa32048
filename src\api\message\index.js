import request from '@/utils/request';
import { data } from 'autoprefixer';

/* 分页查询通知消息列表 */
export function getMessageList(pageNum, pageSize) {
  return request({
    url: '/message/listMessageForPage',
    method: 'get',
    params: {
      pageNum,
      pageSize
    }
  });
}
/* 获取未读信息 */
export function getUnreadList() {
  return request({
    url: '/message/listUnreadMessage ',
    method: 'get'
  });
}

/* 将用户全部未读消息标记已读 */
export function updateAllMessageStatus() {
  return request({
    url: '/message/updateAllMessageStatus',
    method: 'get'
  });
}

/* 将用户全部未读消息标记已读 */
export function updateSingleMessageStatus(data) {
  return request({
    url: '/message/updateMessageStatus',
    method: 'get',
    params: data
  });
}

/* 获取用户组信息 */
export function getUserGroupInfo(data) {
  return request({
    url: '/user/group/getUserGroupByUserGroupId',
    method: 'get',
    params: data
  });
}

/**
 * 
 */
export function batchUpdateMessagePopOut(data){
  return request({
    url:'/message/batchUpdateMessagePopOut',
    method:'post',
    data
  })
}
