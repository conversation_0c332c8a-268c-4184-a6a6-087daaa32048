<template>
  <div class="tabs-box">
    <header class="tabs-box-header">
      <xyy-button icon-class="btn-add" class-name="btn-add-icon" @click="addNewClick(1)">新建字段</xyy-button>
      <xyy-button icon-class="enable" type="normal" @click="enableDatas">批量启用</xyy-button>
      <xyy-button icon-class="disable" type="normal" @click="disableDatas">批量禁用</xyy-button>
      <div class="search-box">
        <el-input v-model="listQuery.fieldName" placeholder="请输入字段名称" maxlength="20" />
        <el-button type="primary" @click="getList(listQuery)">搜索</el-button>
      </div>
    </header>
    <div class="info-container">
      <info :info="info"></info>
    </div>
    <xyy-table
      :data="list"
      :col="col"
      :operation="operation"
      :list-query="listQuery"
      :has-selection="true"
      @get-data="getList"
      @operation-click="operationClick"
      @selectionCallback="setCheckedDatas"
    >
      <template slot="fieldType" slot-scope="{col}">
        <el-table-column
          :key="col.index"
          :prop="col.index"
          :label="col.name"
          :width="col.width"
          :formatter="getField"
        />
      </template>
      <template slot="gmtModified" slot-scope="{col}">
        <el-table-column
          :key="col.index"
          :prop="col.index"
          :label="col.name"
          :width="col.width"
          :formatter="getFormatDate"
        />
      </template>
      <template slot="currentState" slot-scope="{col}">
        <el-table-column :key="col.index" :label="col.name" :width="col.width">
          <template slot-scope="item">
            <span :class="'status-tip '+(item.row.currentState === 0?'open':'close')"></span>
            {{ item.row.currentState === 0?'启用':'禁用' }}
          </template>
        </el-table-column>
      </template>
      <!-- <template slot="operation" slot-scope="{col}">
        <el-table-column
          :label="col.name"
          :width="col.width"
          fixed="right"
          class-name="operation-box"
        >
          <template slot-scope="{row}">
            <template>
              <el-button
                v-for="item in operation"
                :key="item.type"
                :disabled="row.editState === 1"
                type="text"
                @click="operationClick(item.type,row)"
              >{{ item.type === 1?item.name[row.currentState]:item.name }}</el-button>
            </template>
          </template>
        </el-table-column>
      </template>-->
    </xyy-table>
  </div>
</template>

<script>
/* 查询列表 保存 删除 启用 禁用 */
import {
  deleteFieldById,
  enableSystemField,
  getCustomFieldsList,
  prohibitSystemField,
  saveCopy,
  enableFieldDatas,
  disableFieldDatas
} from '@/api/fields/index';
/* 查询关联 */
import { listTemplate } from '../../api/fields/fields-comment';
/* 字段类型 时间格式化 */
import { getFieldType, getFormatDate } from '../../assets/filter/commpent';

export default {
  name: 'CustomFields',
  data() {
    return {
      info: [
        {
          title: '用户可按需要自定义字段，自定义字段需引用到模板才能使用。',
          info: ''
        },
        {
          title: '启用/禁用',
          info:
            '禁用即失效，禁用时系统判断当前字段是否被模板引用，如果为使用中则不可禁用；启用即可用状态，已启用状态字段才可以引用到新建模板'
        },
        { title: '编辑', info: '可修改字段的配置项，但不能修改字段类型' },
        {
          title: '复制',
          info: '对当前字段进行复制，生成一条新字段，字段设置与原字段一致。'
        },
        {
          title: '删除',
          info: '与禁用的判断逻辑一致。删除后，字段不可用。（逻辑删除）'
        }
      ],
      list: [],
      listQuery: {
        page: 0,
        pageSize: 10,
        total: 0,
        fieldName: ''
      },
      col: [
        { index: 'fieldName', name: '字段名', resizable: true },
        {
          index: 'fieldType',
          name: '字段类型',
          slot: true,
          resizable: true,
          width: 150
        },
        {
          index: 'remarks',
          name: '备注说明',
          width: 200,
          ellipsis: true,
          resizable: true
        },
        {
          index: 'gmtModified',
          name: '更新时间',
          slot: true,
          resizable: true
        },
        { index: 'editorName', name: '更新人', resizable: true, width: 130 },
        {
          index: 'currentState',
          name: '状态',
          width: 90,
          slot: true,
          resizable: true
        },
        {
          index: 'operation',
          name: '操作',
          width: 220,
          operation: true,
          resizable: true
        }
      ],
      operation: [
        {
          name: '启用',
          type: 1,
          format: function(row) {
            return ['禁用', '启用'][row.currentState];
          }
        },
        {
          name: '编辑',
          type: 2
        },
        {
          name: '复制',
          type: 3
        },
        {
          name: '删除',
          type: 4,
          disabled: function(row) {
            return row.currentState === 0;
          }
        }
      ],
      selections: [],
      timer: null
    };
  },
  activated() {
    // this.listQuery.fieldName = '';
    this.getList(this.listQuery);
  },
  methods: {
    /**
     * 设置选中数据
     */
    setCheckedDatas(datas) {
      this.selections = datas.map(el => el.id);
    },
    /* 获取列表数据 */
    getList: function(params) {
      const { page, pageSize } = params;
      getCustomFieldsList(page, pageSize, this.listQuery.fieldName)
        .then(res => {
          const { list, total } = res.data;
          this.list = list;
          this.listQuery = {
            ...this.listQuery,
            total: Number(total)
          };
        })
        .catch(() => {});
    },
    // 操作按钮
    operationClick: function(type, row) {
      console.log(type, row);
      const that = this;
      if (type === 1) {
        // 禁用状态 要启用
        if (row.currentState === 1) {
          this.$XyyMsg({
            content: '确定要启用此字段吗？',
            onSuccess: function() {
              that.enableSystem(row, that);
            }
          });
        } else {
          // 启用状态 -- > 禁用
          listTemplate({ fieldId: row.id }).then(datas => {
            if (datas.data && datas.data.length > 0) {
              let html = '当前有表单应用正在使用此字段，请先修改表单应用 ( ';
              datas.data.map(function(item, index) {
                html += `<span style="color: red">${index === 0 ? '' : '、'} ${
                  item.name
                }</span>`;
              });
              html += ' )';
              that.$XyyMsg({
                title: '提示',
                content: html,
                closeBtn: false,
                onSuccess: function() {}
              });
            } else {
              that.$XyyMsg({
                title: '提示',
                content: '确定禁用此字段吗？',
                onSuccess: function() {
                  that.prohibitSystem(row, that);
                }
              });
            }
          });
        }
      }

      // 编辑
      if (type === 2) {
        that.addNewClick(2, row.id);
      }
      // 复制
      if (type === 3) {
        saveCopy({ fieldId: row.id }).then(res => {
          if (res.code === 1) {
            that.$XyyMessage.success('复制成功');
            that.getList(that.listQuery);
          } else {
            that.$XyyMessage.error(res.msg);
          }
        });
      }
      // 删除
      if (type === 4) {
        console.log('删除');
        // 查询字段是否被关联
        listTemplate({ fieldId: row.id }).then(datas => {
          if (datas.data && datas.data.length > 0) {
            let html = '当前有模板正在使用此字段，请先修改模板( ';
            datas.data.map(function(item, index) {
              html += `<span style="color: red">${index === 0 ? '' : '、'} ${
                item.name
              }</span>`;
            });
            html += ' )';
            that.$XyyMsg({
              title: '提示',
              content: html,
              closeBtn: false,
              onSuccess: function() {}
            });
          } else {
            that.$XyyMsg({
              title: '提示',
              content: '确定删除此字段吗？',
              onSuccess: function() {
                that.deleteField(row, that);
              }
            });
          }
        });
      }
    },
    // 启用字段
    enableSystem(row, that) {
      enableSystemField({ fieldId: row.id }).then(res => {
        if (res.code === 1) {
          that.$XyyMessage.success('字段已启用');
          that.getList(that.listQuery);
        } else {
          that.$XyyMessage.error(res.msg);
        }
      });
    },
    // 禁用字段
    prohibitSystem(row, that) {
      prohibitSystemField({ fieldId: row.id }).then(res => {
        if (res.code === 1) {
          that.$XyyMessage.success('字段已禁用');
          that.getList(that.listQuery);
        } else {
          that.$XyyMessage.error(res.msg);
        }
      });
    },
    // 确定删除
    deleteField(row, that) {
      deleteFieldById({ fieldId: row.id }).then(res => {
        if (res.code === 1) {
          that.$XyyMessage.success('删除成功');
          that.getList(that.listQuery);
        } else {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: res.msg, // html代码串
            onSuccess: () => {}
          });
        }
      });
    },
    // 新建or编辑字段 status=2编辑 params -row的参数
    addNewClick(status, id) {
      const str =
        status === 1
          ? '/worksheet/newAddFields/' + new Date().getTime()
          : '/worksheet/editFields/' + id;
      this.$router.push({
        path: str,
        query: {
          type: 'custom',
          state: status,
          id: id
        }
      });
    },
    // 字段类型文本
    getField: function(row) {
      return getFieldType(row.fieldType);
    },
    // 时间格式化
    getFormatDate: function(row) {
      return getFormatDate(row.gmtModified);
    },
    /**
     * 批量启用
     */
    enableDatas() {
      if (this.selections.length) {
        this.$XyyMsg({
          title: '提示',
          content: `确定批量启用这些字段吗？`,
          onSuccess: () => {
            enableFieldDatas(this.selections).then(res => {
              if (res.code === 1) {
                // 刷新列表
                this.getList(this.listQuery);
                this.$XyyMsg({
                  title: '提示',
                  closeBtn: false,
                  content: `共${res.data.total}条数据，成功${
                    res.data.successNum
                  }条，失败${res.data.failNum}条${
                    res.data.failNames ? '（' + res.data.failNames + '）' : ''
                  }`, // html代码串
                  onSuccess: () => {}
                });
              } else {
                this.$XyyMsg({
                  title: '提示',
                  closeBtn: false,
                  content: res.msg, // html代码串
                  onSuccess: () => {}
                });
              }
            });
          }
        });
      } else {
        this.$XyyMessage.warning('无选中数据');
      }
    },
    /**
     * 批量禁用
     */
    disableDatas() {
      if (this.selections.length) {
        this.$XyyMsg({
          title: '提示',
          content: `确定批量禁用这些字段吗？`,
          onSuccess: () => {
            disableFieldDatas(this.selections).then(res => {
              if (res.code === 1) {
                // 刷新列表
                this.getList(this.listQuery);
                this.$XyyMsg({
                  title: '提示',
                  closeBtn: false,
                  content: `共${res.data.total}条数据，成功${
                    res.data.successNum
                  }条，失败${res.data.failNum}条${
                    res.data.failNames ? '（' + res.data.failNames + '）' : ''
                  }`, // html代码串
                  onSuccess: () => {}
                });
              } else {
                this.$XyyMsg({
                  title: '提示',
                  closeBtn: false,
                  content: res.msg, // html代码串
                  onSuccess: () => {}
                });
              }
            });
          }
        });
      } else {
        this.$XyyMessage.warning('无选中数据');
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.tabs-box-header {
  padding-bottom: 8px !important;
}
.search-box {
  width: 344px;
  float: right;
  .el-input {
    width: calc(100% - 76px);
    /deep/ .el-input__inner {
      height: 36px;
      line-height: 36px;
    }
  }
  button {
    width: 68px;
    height: 36px;
    float: right;
  }
}
</style>
