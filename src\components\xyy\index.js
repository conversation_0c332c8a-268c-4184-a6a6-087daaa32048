import XyyButton from './xyy-button';
import XyyTable from './xyy-table';
import XyyListPage from './xyy-list-page';
import XyyMessage from './xyy-message/main';
import XyyMessageBox from './xyy-message-box/main';
import Info from '../info/info';

const components = [XyyButton, XyyTable, XyyListPage, Info];
const install = function(Vue, opts = {}) {
  components.forEach(component => {
    Vue.component(component.name, component);
  });
  Vue.prototype.$XyyMsg = XyyMessageBox;
  Vue.prototype.$XyyMessage = XyyMessage;
};

if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
}

export default {
  version: '1.0.0',
  install,
  XyyMessage
};
