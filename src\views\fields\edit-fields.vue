<template>
  <div class="tabs-box">
    <!--右侧配置-->
    <fieldsBox class="tabs-box-left">
      <!-- 选择类型 -->
      <div slot="header"
           class="tabs-box-left-header">
        <span>字段类型</span>
        <el-select :disabled="true"
                   v-model="optionsModel.fieldType"
                   style="width: 220px"
                   placeholder="请选择">
          <el-option v-for="item in options"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value" />
        </el-select>
      </div>
      <div v-if="optionsModel.fieldType === null"
           slot="main"
           class="choose-img">
        <p class="desc-select">
          <img src="../../assets/work_sheet/notSheet.png"
               alt />
          <span>请先选择字段</span>
        </p>
      </div>

      <div v-loading="loading"
           v-else
           slot="main">
        <configuration ref="optionsModel"
                       :keys="optionsModel.fieldType"
                       :disabled-props="disabledProps"
                       v-model="optionsModel" />
      </div>
    </fieldsBox>
    <!--左侧预览-->
    <fieldsBox class="tabs-box-right">
      <span slot="header">预览</span>

      <preview v-if="optionsModel.fieldType !== null"
               slot="main"
               :keys="optionsModel.fieldType"
               :preview="optionsModel"
               :read-only="([10].includes(optionsModel.fieldType))"
               :preview-edit="true" />
    </fieldsBox>
    <div class="tabs-footer">
      <el-button :disabled-props="disabledProps"
                 type="primary"
                 @click="checkTimer(saveClick,'timer')()">保存</el-button>
    </div>
  </div>
</template>

<script>
/* 新增自定义字段保存  新增系统字段保存  更新系统字段  更新自定义字段  查询字段信息*/
import {
  getFieldByFieldId,
  // saveCustomField,
  // saveSystemField,
  updateCustomerField,
  updateSystemField
} from '@/api/fields/index';
/* 查询关联字段信息 */
import { listTemplate } from '../../api/fields/fields-comment';
import fieldsBox from './components/fields-box';
import configuration from './components/configuration';
import preview from '../../components/Fields/preview';

export default {
  name: 'EditFields',
  components: {
    fieldsBox,
    configuration,
    preview
  },
  data () {
    return {
      disabledProps: false, // 保存后禁用表单
      loading: false,
      state: 2, // 2 -编辑 1-新建
      type: '', // system -系统字段 custom-自定义
      options: [
        {
          label: '单行输入',
          value: 0
        },
        {
          label: '多行输入',
          value: 1
        },
        {
          label: '下拉列表',
          value: 2
        },
        {
          label: '单选',
          value: 3
        },
        {
          label: '多选',
          value: 4
        },
        {
          label: '日期',
          value: 5
        },
        {
          label: '级联',
          value: 6
        },
        {
          label: '省市区',
          value: 7
        },
        {
          label: '电话',
          value: 8
        },
        {
          label: '邮箱',
          value: 9
        },
        {
          label: '附件',
          value: 10
        }
      ],
      optionsModelOld: {},
      optionsModel: {
        systemFieldFlag: 0, // 是否是系统字段,’0’:’是’,’1’:’否’
        fieldName: '', // 字段名
        fieldText: '', // 字段文本
        fieldType: null, // 字段类型 0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
        limitNumberCharacters: '', // 限制字符数
        defaultValue: '', // 默认值
        tips: '', // 提示信息
        remarks: '', // 备注描述
        optionSettings: {
          // 选项设置卡
        }
      },
      timer: null
    };
  },
  mounted () {
    this.routerClick(this.$route);
  },
  methods: {
    // 路径切换时的数据问题
    routerClick (router) {
      this.state = Number(router.query.state);
      this.type = this.$route.query.type;
      this.optionsModel.systemFieldFlag =
        router.query.type === 'system' ? 0 : 1;
      this.optionsModel.id = router.query.id;
      this.loading = true;
      this.getFieldByFieldId();
      this.$store.dispatch('tagsView/addVisitedView', router);
      this.disabledProps = false;
    },
    // 编辑时获取初始数据
    getFieldByFieldId () {
      const that = this;
      getFieldByFieldId({ fieldId: that.optionsModel.id })
        .then(res => {
          if (res.code === 1) {
            Object.keys(that.optionsModel).forEach(key => {
              that.optionsModel[key] = res.data[key];
            });
            that.optionsModelOld = JSON.parse(
              JSON.stringify(that.optionsModel)
            );
            const rest = res.data.optionSettings.replace(/(&quot;)/g, '"');
            that.optionsModel.optionSettings = JSON.parse(rest);
            if (that.optionsModel.fieldType === 10) {
              if (
                !that.optionsModel.optionSettings.fileObj ||
                !that.optionsModel.optionSettings.fileObj.optionsValue
              ) {
                that.optionsModel.optionSettings.fileObj = {
                  optionsValue: [],
                  optionsArray: []
                };
              }
            } else if (
              that.optionsModel.fieldType === 2 ||
              that.optionsModel.fieldType === 3
            ) {
              const type =
                that.optionsModel.fieldType === 2
                  ? 'selectOptions'
                  : 'radioOptions';
              if (that.optionsModel.optionSettings[type].optionsArray.length) {
                that.optionsModel.optionSettings[type].optionsArray.forEach(
                  (el, i) => {
                    if (!el.val) {
                      el.val = new Date().getTime() + i;
                    }
                    if (
                      that.optionsModel.optionSettings[type].optionsValue === i
                    ) {
                      that.optionsModel.optionSettings[type].optionsValue =
                        el.val;
                    }
                  }
                );
              }
            } else if (that.optionsModel.fieldType === 4) {
              if (
                that.optionsModel.optionSettings.checkedOptions.optionsArray
                  .length
              ) {
                that.optionsModel.optionSettings.checkedOptions.optionsArray.forEach(
                  (el, i) => {
                    if (!el.val) {
                      el.val = new Date().getTime() + i;
                    }
                    const _i = that.optionsModel.optionSettings.checkedOptions.optionsValue.indexOf(
                      i
                    );
                    if (_i > -1) {
                      that.optionsModel.optionSettings.checkedOptions.optionsValue[
                        _i
                      ] = el.val;
                    }
                  }
                );
              }
            }
            that.loading = false;
          }
        })
        .catch(e => {
          that.loading = false;
        });
    },
    // 保存时进行验证 // 级联的回调判断
    getMenuBtnList (menuTreeList, menuList) {
      for (const item of menuTreeList) {
        if (item.label === '') {
          menuList.push(true);
          break;
        }
        if (item.children && item.children.length > 0) {
          this.getMenuBtnList(item.children, menuList);
        }
      }
    },
    // 保存方法
    saveClick () {
      this.loading = true;
      if (this.optionsModel.fieldType === null) {
        this.$XyyMessage.error('请配置字段后保存');
        this.loading = false;
        return false;
      }
      // 级联选项判断
      if (this.optionsModel.fieldType === 6) {
        const tree = [];
        this.getMenuBtnList(
          this.optionsModel.optionSettings.treeOptions.optionsArray,
          tree
        );
        if (
          this.optionsModel.optionSettings.treeOptions.optionsArray.length ===
          0 ||
          tree.length > 0
        ) {
          this.$XyyMessage.error('请配置选项设置后保存');
          this.loading = false;
          return false;
        }
      } else if (this.optionsModel.fieldType === 5) {
        // 如果是日期清空选中值
        this.optionsModel.optionSettings.dateOptions.dateValue = '';
      }
      this.$refs['optionsModel'].submitForm().then(
        valid => {
          const datas = this.optionsSettings(this.optionsModel.fieldType);
          // 自定义保存 / 系统字段保存
          this.saveField(datas);
        },
        () => {
          this.loading = false;
        }
      );
    },
    // 提交时 optionSetting的数据
    optionsSettings (type) {
      if ([2, 3, 4, 5, 6, 7, 10].indexOf(type) === -1) {
        return '{}';
      } else {
        return JSON.stringify(this.optionsModel.optionSettings);
      }
    },
    /* 校验成功后的保存 */
    saveField (datas) {
      const params = JSON.parse(JSON.stringify(this.optionsModel));
      const that = this;
      params.optionSettings = datas;
      delete params.gmtCreate;
      delete params.gmtModified;
      for (var keys in params) {
        if (keys !== 'id' && params[keys] === that.optionsModelOld[keys]) {
          delete params[keys];
        }
      }
      if (Object.keys(params).length > 1) {
        that.editListTemplate(params, that);
      } else {
        this.loading = false;
        this.$XyyMessage.info('数据无更改');
      }
    },
    // 编辑保存前验证字段
    editListTemplate (params, that) {
      listTemplate({ fieldId: this.optionsModel.id })
        .then(res => {
          if (res.data && res.data.length > 0) {
            let html =
              '<p style="margin:0 0 20px; 0">确认保存修改吗？</p><span style="color: red">修改后，所有使用此字段的模板将同步更新。<br/>涉及的模板';
            res.data.map(function (item, index) {
              html += `${index === 0 ? '：' : '、'} ${item.name}`;
            });
            html += '</span>';
            this.loading = false;
            that.$XyyMsg({
              title: '提示',
              content: html,
              onSuccess: function () {
                that.loading = true;
                that[that.type + 'Update'](params, that);
              }
            });
          } else {
            that[that.type + 'Update'](params, that);
          }
        })
        .catch(e => {
          this.loading = false;
          that.$XyyMsg({
            content: e.msg,
            closeBtn: false,
            onSuccess: function () { }
          });
        });
    },
    // 更新字段
    systemUpdate (params, that) {
      updateSystemField(params).then(res => {
        if (res.code === 1) {
          that.$XyyMessage.success('更新字段成功');
          that.disabledProps = true;
          that.dispatchPath();
        } else {
          this.loading = false;
          that.$XyyMsg({
            content: res.msg,
            closeBtn: false,
            onSuccess: function () { }
          });
        }
      });
    },
    customUpdate (params, that) {
      updateCustomerField(params).then(res => {
        if (res.code === 1) {
          that.$XyyMessage.success('更新字段成功');
          that.disabledProps = true;
          that.dispatchPath();
        } else {
          this.loading = false;
          that.$XyyMsg({
            closeBtn: false,
            content: res.msg,
            onSuccess: function () { }
          });
        }
      });
    },
    /* 保存跳转 标签*/
    dispatchPath () {
      const view = this.$route;
      this.$store
        .dispatch('tagsView/delView', view)
        .then(({ visitedViews }) => {
          if (this.type === 'system') {
            this.$router.push({ name: 'systemFields' });
          } else if (this.type === 'custom') {
            this.$router.push({ name: 'customFields' });
          }
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.tabs-box {
  position: relative;
  padding-bottom: 50px;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  &-left {
    min-width: 415px;
    width: 49%;
    min-height: 400px;
    height: 100%;
    overflow-y: hidden;
    padding-bottom: 60px;
    .choose-img {
      width: 30%;
      left: 50%;
      top: 50%;
      transform: translateX(-50%) translateY(-50%);
      position: absolute;
      img {
        width: 100%;
        // margin-bottom: 10px;
      }
      .desc-select {
        width: 100%;
        height: auto;
        color: #aeaebf;
        font-size: 14px;
        text-align: center;
        // margin-top: 100px;
        margin: 0;
      }
      span {
        position: relative;
        top: 40px;
      }
    }
    &-header {
      span {
        display: inline-block;
        width: 70px;
        text-align: right;
        margin-right: 10px;
      }
    }
  }
  &-right {
    min-width: 100px;
    width: 49%;
  }
  /deep/.el-input--suffix {
    height: 40px;
  }
  /deep/ .el-input__inner {
    height: 36px !important;
    line-height: 36px;
  }
  .tabs-footer {
    position: absolute;
    bottom: 10px;
    right: 20px;
    background: #fff;
    text-align: right;
    .el-button {
      height: 36px;
    }
  }
}
</style>
