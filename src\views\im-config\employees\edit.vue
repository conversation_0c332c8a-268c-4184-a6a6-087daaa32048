<template>
  <div class="im-employee-box">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="员工组名称" prop="groupName">
        <el-input v-model.trim="form.groupName" maxlength="10" />
      </el-form-item>
      <el-form-item label="选择员工" class="group-form-item">
        <el-button v-show="!groupDatas.length" icon="el-icon-plus" @click="groupOpen=true">添加</el-button>
        <div v-show="groupDatas.length" class="group-box">
          <el-tag
            v-for="data in groupDatas"
            :key="data.id"
            closable
            type="info"
            @close="delGroupData(data.id)"
          >{{ `${data.name}-${data.code}` }}</el-tag>
          <el-button icon="el-icon-plus" @click="groupOpen=true">添加</el-button>
        </div>
      </el-form-item>
      <el-form-item label=" ">
        <el-button type="primary" @click="checkTimer(save,'timer')()">保存</el-button>
      </el-form-item>
    </el-form>
    <transfer
      :status.sync="groupOpen"
      :left-datas="employeeDatas"
      :checked="groupDatas"
      title="选择员工"
      @callback="setGroupDatas"
    ></transfer>
  </div>
</template>
<script>
import transfer from './components/transfer';
import {
  getEmployeeList,
  saveGroupData,
  getGroupData
} from '@/api/im-config/employees';
export default {
  components: {
    transfer
  },
  data() {
    return {
      employees: [], // 员工数据
      form: {
        groupName: '',
        kefuList: [],
        id: ''
      },
      groupOpen: false, // 弹框状态
      groupDatas: [], // 选中员工数据
      employeeDatas: [], // 员工数据
      rules: {
        groupName: [
          { required: true, message: '请输入员工组名称', trigger: 'blur' },
          { max: 10, message: '不超过10个字', trigger: 'blur' }
        ]
      },
      timer: null
    };
  },
  activated() {
    if (this.$route.query.id) {
      this.$store.dispatch('tagsView/updateVisitedView', {
        ...this.$route,
        meta: {
          title: '编辑员工组'
        }
      });
      this.form.id = this.$route.query.id;
      this.initGroupDatas(this.$route.query.id);
    } else {
      this.initGroupDatas();
    }
  },
  methods: {
    setGroupDatas(datas) {
      this.groupDatas = datas;
    },
    delGroupData(id) {
      this.groupDatas = this.groupDatas.filter(el => el.id !== id);
    },
    initGroupDatas(id) {
      if (id) {
        this.getEmployeeDatas(id, datas => {
          getGroupData(id).then(res => {
            if (res.code === 1) {
              this.form = {
                id: res.data.id,
                groupName: res.data.groupName,
                kefuList: res.data.userKefuList
                  ? res.data.userKefuList.map(el => el.id)
                  : []
              };
              this.groupDatas = datas.filter(
                el => this.form.kefuList.includes(Number(el.id)),
                this
              );
            } else {
              this.$XyyMessage.error(res.msg);
            }
          });
        });
      } else {
        this.getEmployeeDatas();
      }
    },
    getEmployeeDatas(id, cb) {
      getEmployeeList(id ? { id: id } : {})
        .then(res => {
          if (res.code === 1) {
            this.employeeDatas = res.data;
            if (cb) {
              cb(res.data);
            }
          }
        })
        .catch(() => {});
    },
    save() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.form.kefuList = this.groupDatas.map(el => Number(el.id));
          saveGroupData(this.form)
            .then(res => {
              if (res.code === 1) {
                this.$XyyMessage.success(
                  this.form.id ? '修改成功' : '保存成功'
                );
                this.$store.dispatch('tagsView/delView', this.$route);
                this.$router.replace({
                  name: 'employeeList'
                });
              } else {
                this.$XyyMessage.error(res.msg);
              }
            })
            .catch(() => {});
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.im-employee-box {
  .el-form {
    width: 800px;
    margin: 20px auto;
    .el-form-item {
      margin-bottom: 20px;

      /deep/.el-form-item__label {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(41, 41, 51, 1);
      }
      .el-input {
        /deep/.el-input__inner {
          height: 36px;
          line-height: 36px;
          border: 1px solid rgba(228, 228, 235, 1);
        }
      }

      &.group-form-item {
        .el-button {
          margin-bottom: 12px;
        }
        margin-bottom: 8px;
      }
      .el-button {
        height: 36px;
        padding: 0 12px;
        line-height: 36px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
        border-radius: 2px;
        border: 1px solid rgba(228, 228, 235, 1);
        &:focus,
        &:hover {
          background: #fff;
          border-color: rgba(228, 228, 235, 1);
        }
        &.el-button--primary {
          color: rgba(255, 255, 255, 1);
          padding: 0 20px;
          border: none;
        }
        &.el-button--primary:focus,
        &.el-button--primary:hover {
          background: #3b95a8;
          border-color: #3b95a8;
        }
      }
      /deep/ .el-form-item__content {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
      }
      .group-box {
        .el-tag {
          margin: 0 12px 12px 0;
          height: 36px;
          line-height: 36px;
          background: rgba(245, 247, 250, 1);
          border-radius: 2px;
          border: 1px solid rgba(228, 228, 235, 1);
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(87, 87, 102, 1);
          /deep/.el-tag__close.el-icon-close:hover {
            background: rgba(245, 247, 250, 1);
            color: rgba(87, 87, 102, 1);
          }
        }
      }
    }
  }
}
</style>
