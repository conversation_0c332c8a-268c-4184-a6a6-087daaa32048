<template >
  <div class="announcement-management-box">
    <el-form ref="from" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="公告标题" prop="name" id="ming">
        <el-input
          v-model.trim="form.name"
          placeholder="请输入标题"
          style="width: 480px;"
          maxlength="50"
        />
      </el-form-item>

      <el-form-item class="marginTopList" prop="editorText" label="内容">
        <!-- 编辑器 -->
        <editor-bar
          class="editor"
          v-model="form.editorText"
          :is-clear="isClear"
          :index="2"
          @change="change"
        ></editor-bar>
      </el-form-item>

      <el-form-item>
        <el-button plain size="medium" @click="preview">预览</el-button>
        <el-button
          plain
          type="primary"
          size="medium"
          class="searchCondition"
          @click="seveAnnouncement(form.id)"
        >保存</el-button>
        <el-button plain size="medium" @click="cancel">取消</el-button>
      </el-form-item>
    </el-form>

    <!-- 添加弹出框-开始-->
    <el-dialog :visible.sync="dialogTableVisible" class="dialogInner2" title="预览">
      <div class="header">
        <div class="title">
          <div>
            <h3>{{form.name}}</h3>
          </div>
        </div>
      </div>
      <div class="announcement-managementEdit-wrap announcement-managementEdit-wrap_2">
        <div class="message" v-html="form.editorText"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import EditorBar from '../knowledge_base/richTextEditor2/wangeditor2';
import {
  findKnowledgeNoticeDetail,
  updateKnowledgeNotice,
  saveKnowledgeNotice
} from '@/api/knowledge_base';
export default {
  components: {
    EditorBar
  },
  name: 'AnnouncementManagementEdit',
  data() {
    return {
      form: {
        name: '',
        editorText: '',
        id: ''
      },
      rules: {
        name: [
          { required: true, message: '选项名称为必填项', trigger: 'blur' }
        ],
        editorText: [
          { required: true, message: '选项名称为必填项', trigger: 'blur' }
        ]
      },
      dialogTableVisible: false,
      isClear: false
    };
  },
  activated() {},
  mounted() {
    this.form.id = this.$route.query.templateId
      ? this.$route.query.templateId
      : '';
    if (this.form.id) {
      this.$store.dispatch('tagsView/updateVisitedView', {
        ...this.$route,
        meta: {
          title: '编辑公告'
        }
      });
      findKnowledgeNoticeDetail({ id: this.form.id, onclick: false }).then(
        res => {
          if (res.code === 1) {
            this.form.name = res.data.title;
            this.form.editorText = res.data.context;
          } else {
            this.$XyyMessage.error(res.msg);
          }
        }
      );
    } else {
      this.form.name = '';
      this.form.editorText = '';
    }
  },
  methods: {
    // 点击添加事件
    async preview() {
      this.dialogTableVisible = true;
    },
    makeNo() {
      this.dialogTableVisible = false;
    },
    // 关联问题弹窗确定
    makeSure() {
      this.dialogTableVisible = false;
    },
    change() {},
    // 保存
    seveAnnouncement(id) {
      if (!this.form.name) {
        this.$XyyMessage.error('请输入标题');
        return;
      }

      if (!this.form.editorText) {
        this.$XyyMessage.error('请输入内容');
        return;
      }

      var code = this.form.editorText;
      //替换a标签的href，添加点击事件
      let contains = document.getElementById('wangEditerDivContainer');
      if (contains) {
        document.body.removeChild(contains);
      }
      let wangEditerDivContainer = document.createElement('DIV');
      wangEditerDivContainer.id = 'wangEditerDivContainer';
      wangEditerDivContainer.style.display = 'none';
      wangEditerDivContainer.innerHTML = code;
      document.body.appendChild(wangEditerDivContainer);
      let wangEditerDivContainer_a_all = document
        .getElementById('wangEditerDivContainer')
        .querySelectorAll('a');

      if (wangEditerDivContainer_a_all && wangEditerDivContainer_a_all.length) {
        wangEditerDivContainer_a_all.forEach(item => {
          if (item.href.includes('/#/knowledge_base/')) {
            item.setAttribute('onclick', `wangEditerAClick("${item.href}")`);
            item.href = 'javascript:;';
            item.target = '';
          }
        });
      }

      this.form.editorText = document.getElementById(
        'wangEditerDivContainer'
      ).innerHTML;

      if (id) {
        updateKnowledgeNotice({
          id: this.form.id ? this.form.id : '',
          title: this.form.name,
          context: this.form.editorText
        })
          .then(res => {
            if (res.code === 1) {
              this.$XyyMessage.success('保存成功');
              this.$store.dispatch('tagsView/delView', this.$route);
              this.$router.push({
                path: '/knowledge_base/announcementManagement'
              });
            } else {
              this.$XyyMessage.error(res.msg);
            }
          })
          .catch(() => {});
      } else {
        saveKnowledgeNotice({
          title: this.form.name,
          context: this.form.editorText
        })
          .then(res => {
            if (res.code === 1) {
              this.$XyyMessage.success('保存成功');
              this.$store.dispatch('tagsView/delView', this.$route);
              this.$router.push({
                path: '/knowledge_base/announcementManagement'
              });
            } else {
              this.$XyyMessage.error(res.msg);
            }
          })
          .catch(() => {});
      }
    },
    /**
     * 取消
     */
    cancel() {
      this.$store.dispatch('tagsView/delView', this.$route);
      this.$router.push({
        path: '/knowledge_base/announcementManagement'
      });
    }
  }
};
</script>

<style lang="scss">
.announcement-management-box {
  .el-form {
    width: calc(100% - 280px);
    margin: 20px 20px;
    .el-form-item {
      margin-bottom: 20px;

      /deep/.el-form-item__label {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(41, 41, 51, 1);
      }
      .el-input {
        /deep/.el-input__inner {
          height: 36px;
          line-height: 36px;
          border: 1px solid rgba(228, 228, 235, 1);
        }
      }
      .el-textarea {
        /deep/.el-textarea__inner {
          height: 211px;
          border: 1px solid rgba(228, 228, 235, 1);
        }
      }
      // .el-button {
      //   height: 36px;
      //   padding: 0 12px;
      //   line-height: 36px;
      //   font-size: 14px;
      //   font-family: PingFangSC-Regular, PingFang SC;
      //   font-weight: 400;
      //   color: rgba(87, 87, 102, 1);
      //   border-radius: 2px;
      //   border: 1px solid rgba(228, 228, 235, 1);
      //   &:focus,
      //   &:hover {
      //     background: #fff;
      //     border-color: rgba(228, 228, 235, 1);
      //   }
      //   &.el-button--primary {
      //     color: rgba(255, 255, 255, 1);
      //     padding: 0 20px;
      //     border: none;
      //   }
      //   &.el-button--primary:focus,
      //   &.el-button--primary:hover {
      //     background: #3b95a8;
      //     border-color: #3b95a8;
      //   }
      // }
      /deep/ .el-form-item__content {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
      }
    }
  }

  .searchCondition.is-plain {
    background: rgba(59, 149, 168, 1);
    color: #fff;
  }
  .dialogInner2 {
    .shezhiyin {
      position: absolute;
      top: 5px;
      left: 20px;
      /deep/.el-form-item__content {
        margin-left: 0 !important;
      }
    }
    .line {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(41, 41, 51, 1);
      margin-top: 0;
      margin-bottom: 0;
      span {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(174, 174, 191, 1);
        margin-left: 8px;
      }
    }
    .header {
      margin: 0 auto;
      .title {
        text-align: center;
        // margin: 0 auto;
        // display: flex;
        // position: absolute;
        // left: 50%;
        // -webkit-transform: translateX(-50%);
        // transform: translateX(-50%);
        .title-info {
          margin-left: 20px;
        }
      }
    }
    .announcement-managementEdit-wrap_2 {
      background: #fff;
      /deep/.elIconDelete {
        display: none;
      }
    }
    .announcement-managementEdit-wrap {
      .message {
        min-height: 400px;
        width: 100%;
        height: auto;
        /* table 样式 */
        /deep/ {
          table {
            border-top: 1px solid #ccc;
            border-left: 1px solid #ccc;
          }
          table td,
          table th {
            border-bottom: 1px solid #ccc;
            border-right: 1px solid #ccc;
            // padding: 3px 5px;
          }
          table th {
            background-color: #eee;
            // border-bottom: 2px solid #ccc;
            text-align: center;
          }

          /* blockquote 样式 */
          blockquote {
            display: block;
            border-left: 8px solid #d0e5f2;
            padding: 5px 10px;
            margin: 10px 0;
            line-height: 1.4;
            font-size: 100%;
            background-color: #f1f1f1;
          }

          /* code 样式 */
          code {
            display: inline-block;
            *display: inline;
            *zoom: 1;
            background-color: #f1f1f1;
            border-radius: 3px;
            padding: 3px 5px;
            margin: 0 3px;
          }
          pre code {
            display: block;
          }

          /* ul ol 样式 */
          ul,
          ol {
            margin: 0;
          }

          ul,
          dl {
            list-style-type: disc;
          }
          a {
            color: #428bca;
            text-decoration: none;
          }
          a:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}
</style>