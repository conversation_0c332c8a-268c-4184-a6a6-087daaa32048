<template>
  <div class="tree-group">
    <el-checkbox
      :indeterminate="isIndeterminate"
      v-model="checkAll"
      class="check-all"
      @change="handleCheckAllChange"
    >{{ data.typeName }}</el-checkbox>
    <el-checkbox-group v-model="checkedDatas" @change="handleCheckedChange">
      <el-checkbox
        v-for="el in data.base"
        :label="el"
        :key="el.id"
        :disabled="disabled.includes(el.id)"
      >{{ el.name }}</el-checkbox>
    </el-checkbox-group>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {
          base: []
        };
      }
    },
    checked: {
      type: Array,
      default() {
        return [];
      }
    }, // 选中数据
    disabled: {
      type: Array,
      default() {
        return [];
      }
    } // 禁用数据
  },
  data() {
    return {
      checkAll: false,
      isIndeterminate: false,
      checkedDatas: []
    };
  },
  methods: {
    /**
     * 全选change回调
     */
    handleCheckAllChange(val) {
      this.checkedDatas = val
        ? this.data.base.filter(el => !this.disabled.includes(el.id))
        : [];
      this.isIndeterminate = false;
      this.$emit('changeCallback');
    },
    /**
     * change回调
     */
    handleCheckedChange(value) {
      const checkedCount = value.length;
      const datas = this.data.base.filter(el => !this.disabled.includes(el.id));
      this.checkAll = datas.length && checkedCount === datas.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < datas.length;
      this.$emit('changeCallback');
    },
    /**
     * 获取选中数据
     */
    getCheckedDatas() {
      return this.checkedDatas;
    },
    /**
     * 设置选中数据
     */
    setCheckedDatas() {
      if (this.checked.length) {
        const ids = this.checked.map(el => el.id);
        this.checkedDatas = this.data.base.filter(
          el => ids.includes(el.id),
          this
        );
      } else {
        this.checkedDatas = [];
      }
      const datas = this.data.base.filter(el => !this.disabled.includes(el.id));
      this.checkAll = datas.length && this.checkedDatas.length === datas.length;
      this.isIndeterminate =
        this.checkedDatas.length > 0 && this.checkedDatas.length < datas.length;
    },
    /**
     * 触发全选、全不选
     */
    setAllChecked(val) {
      this.checkAll = val;
      this.handleCheckAllChange(val);
    }
  }
};
</script>

<style lang="scss" scoped>
.tree-group {
  .el-checkbox.check-all {
    margin-bottom: 12px;
  }
  .el-checkbox-group {
    padding-left: 25px;
    .el-checkbox {
      display: block;
      margin-bottom: 12px;
    }
  }
}
</style>
