import Layout from '@/views/layout/Layout';
export default [
  {
    path: '/monitor',
    component: Layout,
    name: 'monitor',
    // redirect: '/monitor/list',
    meta: {
      title: '工单监控',
      icon: 'monitor',
      affix: true,
      code: 'menu:cs:workordermonitor',
      mark: 'monitor'
    },
    children: [
      {
        path: 'list',
        name: 'monitorList',
        meta: {
          title: '工单监控',
          code: 'menu:cs:workordermonitor',
          mark: 'monitor',
          componentName: 'List'
        },
        component: () => import('@/views/monitor/list')
      }
    ]
  },
  {
    path: '/whole',
    component: Layout,
    redirect: 'monitor/whole',
    meta: {
      title: '全员监控',
      affix: true,
      code: 'menu:im:overall',
      mark: 'monitor'
    },
    children: [
      {
        path: 'whole',
        name: 'IMMonitorWhole',
        meta: {
          title: '全员监控',
          code: 'menu:im:overall',
          mark: 'monitor',
          componentName: 'IMMonitorWhole'
        },
        component: () => import('@/views/monitor/index')
      },
      {
        path: 'errorchat',
        name: 'errorchat',
        hidden: true,
        meta: {
          title: '监控转接',
          code: 'menu:im:overall',
          mark: 'monitor'
        },
        component: () => import('@/views/monitor/errorchat')
      }
    ]
  },
  {
    path: '/waitingsession',
    component: Layout,
    redirect: '/waitingsession/waitingsessionlist',
    meta: {
      title: '等待会话',
      affix: true,
      code: 'menu:im:waitingsessionmonitor',
      mark: 'monitor'
    },
    children: [
      {
        path: 'waitingsessionlist',
        name: 'waitingsessionlist',
        meta: {
          title: '等待会话',
          code: 'menu:im:waitingsessionmonitor',
          mark: 'monitor',
          componentName: 'Waitingsession'
        },
        component: () => import('@/views/monitor/waitingsession')
      }
    ]
  }
];
