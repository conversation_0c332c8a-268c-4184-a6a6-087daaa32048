<template>
  <div>
    <xyy-list-page>
      <template slot="header">
        <el-form :inline="true" class="search-box">
          <el-row>
            <el-col :span="9">
              <el-form-item label="时间范围">
                <el-date-picker
                  v-model="listQuery.dateRange"
                  type="datetimerange"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="是否指派">
                <el-select v-model="listQuery.haveAssign">
                  <el-option :value="true" label="已指派"></el-option>
                  <el-option :value="false" label="未指派"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="是否处理">
                <el-select v-model="listQuery.state">
                  <el-option :value="0" label="未处理"></el-option>
                  <el-option :value="1" label="已处理"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="访客名称">
                <el-input
                  v-model="listQuery.customName"
                  placeholder="请输入内容"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="btns-group">
            <el-col :span="24">
              <el-form-item>
                <el-button type="primary" @click="getList(listQuery, true)"
                  >查询</el-button
                >
                <el-button @click="reset">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="btn-box">
          <xyy-button
            v-show="selections.length && assignAcross"
            icon-class="assign"
            @click="toAssign"
            >批量指派</xyy-button
          >
          <!-- 
          <div class="switch-box">
            <el-button :class="{active:!listQuery.state}" plain @click="setState(0)">未处理</el-button>
            <el-button :class="{active:listQuery.state}" plain @click="setState(1)">已处理</el-button>
          </div>
          -->
        </div>
      </template>
      <template slot="body">
        <xyy-table
          ref="tableRef"
          :data="list"
          :list-query="listQuery"
          :col="col"
          :has-selection="true"
          @get-data="getList"
          @operation-click="operationClick"
          @selectionCallback="setCheckedDatas"
        >
          <template slot="haveAssign" slot-scope="{ col }">
            <el-table-column
              :key="col.index"
              :prop="col.index"
              :label="col.name"
              :formatter="formatAssign"
            />
          </template>
          <template slot="state" slot-scope="{ col }">
            <el-table-column
              :key="col.index"
              :prop="col.index"
              :label="col.name"
              :formatter="formatState"
            />
          </template>
          <template slot="operation" slot-scope="{ col }">
            <el-table-column
              :label="col.name"
              :width="col.width"
              fixed="right"
              class-name="operation-box"
            >
              <template slot-scope="{ row }">
                <el-row>
                  <el-button
                    v-for="item in filterOper(row)"
                    :key="item.type"
                    type="text"
                    @click="operationClick(item.type, row)"
                    >{{ item.name }}</el-button
                  >
                </el-row>
              </template>
            </el-table-column>
          </template>
        </xyy-table>
      </template>
    </xyy-list-page>
    <assign
      :status.sync="assignOpen"
      :datas="groupDatas"
      title="指派"
      @callback="setAssignDatas"
    ></assign>
    <detail :status.sync="detailOpen" :data="detailData"></detail>
  </div>
</template>

<script>
import assign from '@/components/im/assign';
import detail from './components/detail';
import {
  getCommentList,
  getGroupDatas,
  getAllKefuGroupDatas,
  setAssignData,
  setAssignDatas,
  getCommentData,
  getServiceData,
  getAssignAcross
} from '@/api/im-config/comment';
export default {
  name: 'compCommentList',
  components: {
    assign,
    detail
  },
  data() {
    return {
      list: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0,
        dateRange: [],
        haveAssign: '',
        state: '', // 0 未处理 1 已处理
        customName: '' //访客名称
      },
      col: [],
      operation: [
        {
          name: '查看',
          type: 1
        },
        {
          name: '指派',
          type: 0
        },
        {
          name: '重新指派',
          type: 2
        }
      ],

      assignOpen: false,
      detailOpen: false,
      selections: [], // 选中数据
      assignId: '', // 当前指派数据id
      groupDatas: [],
      detailData: {
        messages: [],
        customerData: '',
        serviceData: ''
      },
      assignAcross: true // 手动指派权限
    };
  },
  computed: {},
  activated() {
    this.getList(this.listQuery);
    this.getAssignAcross();
  },
  mounted() {
    const start = new Date(new Date(new Date().toLocaleDateString()).getTime());
    const end = new Date(
      new Date(new Date().toLocaleDateString()).getTime() +
        24 * 60 * 60 * 1000 -
        1
    );
    // const end = new Date();
    this.listQuery.dateRange = [start, end];
  },
  methods: {
    filterOper(row) {
      // return !row.haveAssign && this.assignAcross
      //   ? this.operation
      //   : this.operation.filter((el) => el.type);

      //state 0:未处理,1:已处理
      if (row.haveAssign) {
        // 已指派
        // return this.operation.filter(el => el.type === 1);
        if (row.state === 0) {
          // 未处理
          if (this.assignAcross) {
            return this.operation.filter(el => el.type === 1 || el.type === 2);
          } else {
            return this.operation.filter(el => el.type === 1);
          }
        } else {
          // 已处理
          return this.operation.filter(el => el.type === 1);
        }
      } else {
        //未指派
        if (row.state === 0) {
          //未处理
          if (this.assignAcross) {
            //手动指派权限
            return this.operation.filter(el => el.type === 1 || el.type === 0);
          } else {
            return this.operation.filter(el => el.type === 1);
          }
        } else {
          //已处理
          return this.operation.filter(el => el.type === 1);
        }
      }
    },
    getAssignAcross() {
      getAssignAcross()
        .then(res => {
          if (res.code === 1) {
            this.assignAcross = res.data;
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },
    /**
     * 设置选中数据
     */
    setCheckedDatas(datas) {
      this.selections = datas.map(el => el.dialogId);
    },

    //处理table列
    getCol() {
      return this.listQuery.state
        ? [
            { index: 'userName', name: '访客名称', width: 180, ellipsis: true },
            { index: 'haveAssign', name: '是否指派', slot: true },
            { index: 'state', name: '状态', slot: true },
            { index: 'referer', name: '来源渠道', width: 180, ellipsis: true },
            { index: 'appName', name: '来源应用' },
            {
              index: 'createTime',
              name: '留言时间',
              width: 180,
              ellipsis: true
            },
            {
              index: 'handleTime',
              name: '处理时间',
              width: 180,
              ellipsis: true
            },
            { index: 'kefuNameAndCode', name: '负责人' },
            { index: 'operation', name: '操作', width: 150, slot: true }
          ]
        : [
            { index: 'userName', name: '访客名称', width: 180, ellipsis: true },
            { index: 'haveAssign', name: '是否指派', slot: true },
            { index: 'state', name: '状态', slot: true },
            { index: 'referer', name: '来源渠道', width: 180, ellipsis: true },
            { index: 'appName', name: '来源应用' },
            {
              index: 'createTime',
              name: '留言时间',
              width: 180,
              ellipsis: true
            },
            { index: 'kefuNameAndCode', name: '负责人' },
            { index: 'operation', name: '操作', width: 150, slot: true }
          ];
    },

    getList: function(listQuery, reset) {
      this.col = this.getCol();
      const { page, pageSize } = listQuery;
      const state = this.listQuery.state;
      const haveAssign = this.listQuery.haveAssign;
      const dateRange = this.listQuery.dateRange;
      const createTimeStart =
        dateRange && dateRange.length
          ? this.getFormatDate('', '', dateRange[0].getTime())
          : '';
      const createTimeEnd =
        dateRange && dateRange.length
          ? this.getFormatDate('', '', dateRange[1].getTime())
          : '';
      const customName = this.listQuery.customName;
      getCommentList({
        page: reset ? 1 : page,
        size: pageSize,
        state,
        haveAssign,
        createTimeStart,
        createTimeEnd,
        customName
      })
        .then(res => {
          if (res.code === 1) {
            const { records, total, current } = res.data;
            //添加是否显示指派按钮标识
            //this.list = records;
            let datalist = [];
            records.forEach((item, index) => {
              datalist.push(
                Object.assign({}, item, {
                  selectable: this.selectableComputed(item)
                })
              );
            });
            this.list = datalist;
            this.listQuery = {
              ...this.listQuery,
              total: Number(total),
              page: Number(current)
            };
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {})
        .finally(() => {
          this.$refs.tableRef.clearSelection();
        });
    },
    /**
     * column的checkbox是否可被选中
     */
    selectableComputed(row) {
      //state 0:未处理,1:已处理
      if (row.haveAssign) {
        //已指派
        // return false;
        if (row.state === 0) {
          // 未处理
          if (this.assignAcross) {
            return true;
          } else {
            return false;
          }
        } else {
          // 已处理
          return false;
        }
      } else {
        //未指派
        if (row.state === 0) {
          //未处理
          if (this.assignAcross) {
            //手动指派权限
            return true;
          } else {
            return false;
          }
        } else {
          //已处理
          return false;
        }
      }
    },

    operationClick: function(type, row) {
      switch (type) {
        case 0: // 指派
        case 2: // 重新指派
          this.toAssign(row.dialogId);
          break;
        case 1: // 查看
          this.toDetail(row.dialogId, row);
          break;
      }
    },
    formatAssign(row, column, cellValue, index) {
      return cellValue ? '已指派' : '未指派';
    },
    formatState(row, column, cellValue, index) {
      return cellValue ? '已处理' : '未处理';
    },
    toAssign(id) {
      getAllKefuGroupDatas().then(res => {
        if (res.code === 1) {
          this.groupDatas = res.data;
        }
      });
      if (id) {
        // 单数据指派
        this.assignId = id;
      } else {
        // 多数据指派
        if (!this.selections.length) {
          this.$XyyMessage.error('请先选择留言');
          return;
        }
      }
      this.assignOpen = true;
    },
    /**
     * 设置指派数据
     */
    setAssignDatas(data) {
      if (this.assignId) {
        setAssignData({
          dialogId: this.assignId,
          id: data.id,
          type: data.type
        })
          .then(res => {
            if (res.code === 1) {
              this.assignId = '';
              this.assignOpen = false;
              this.getList(this.listQuery);

              // this.$XyyMessage.success('指派成功');
              let msgTxt = ''
              if(res.data == 0) {
                msgTxt = `指派失败，留言已被指派给其他人`
              } else {
                msgTxt = `成功指派${res.data}条留言`
              }
              this.$XyyMsg({
                title: '提示',
                content: msgTxt,
                closeBtn: false,
                onSuccess: () => { },
              });
            } else {
              this.$XyyMessage.error(res.msg);
            }
          })
          .catch(() => {});
      } else {
        setAssignDatas({
          dialogIds: this.selections,
          id: data.id,
          type: data.type
        })
          .then(res => {
            if (res.code === 1) {
              this.assignOpen = false;
              this.getList(this.listQuery);
              // this.selections = [];

              // this.$XyyMessage.success('指派成功');
              let msgTxt = ''
              if(res.data == 0) {
                msgTxt = `指派失败，留言已被指派给其他人`
              } else {
                msgTxt = `成功指派${res.data}条留言` + (res.data != this.selections.length ? `，剩余留言已被指派给其他人`: ``)
              }
              this.$XyyMsg({
                title: '提示',
                content: msgTxt,
                closeBtn: false,
                onSuccess: () => { },
              });
              this.selections = [];
            } else {
              this.$XyyMessage.error(res.msg);
            }
          })
          .catch(() => {});
      }
    },
    setState(val) {
      this.listQuery.state = val;
      this.getList(this.listQuery, true);
    },
    reset() {
      this.listQuery.dateRange = '';
      this.listQuery.haveAssign = '';
      this.listQuery.state = '';
      this.listQuery.customName = '';
      // this.getList(this.listQuery);
    },
    toDetail(id, row) {
      this.detailData = {
        messages: [],
        customerData: '',
        serviceData: ''
      };
      getCommentData({ dialogId: id })
        .then(res => {
          if (res.code === 1) {
            if (JSON.stringify(res.data) == '{}') {
              this.detailData = {
                messages: [],
                customerData: {
                  referer: row.referer,
                  customName: row.creatorName,
                  createTime: row.createTime
                },
                serviceData: ''
              };
            } else {
              this.detailData.messages = res.data.messages;
              this.detailData.customerData = {
                referer: res.data.referer,
                nickName: res.data.customNickName,
                customName: res.data.customName,
                city: `${res.data.ipprovince} ${res.data.ipcity}`,
                createTime: res.data.toLeaveMessageTime,
                customHeadImg: res.data.customHeadImg
              };
            }
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
      getServiceData({ dialogId: id }).then(res => {
        if (res.code === 1) {
          this.detailData.serviceData = res.data.imFormDataExtend;
        } else {
          this.detailData.serviceData = [];
          // this.$XyyMessage.error(res.msg);
        }
      });
      this.detailOpen = true;
    },
    getFormatDate: function(row, column, cellValue, index) {
      return new Date(cellValue + 8 * 3600 * 1000)
        .toJSON()
        .substr(0, 19)
        .replace('T', ' ');
    }
  }
};
</script>

<style lang="scss" scoped>
.search-box {
  border-bottom: 1px dashed #e4e4eb;
  padding-bottom: 20px;

  .el-row {
    .el-col:last-child {
      /deep/.el-form-item {
        margin-right: 0;
      }
    }
  }

  .btns-group {
    .el-col {
      text-align: right;

      /deep/.el-form-item {
        margin-bottom: 0;
      }
    }
  }

  /deep/.el-form-item {
    margin-right: 15px;
    //margin-bottom: 0;
    margin-bottom: 15px;
    display: flex;

    /deep/.el-form-item__label {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(41, 41, 51, 1);
      padding-right: 6px;
    }

    /deep/.el-form-item__content {
      flex-grow: 1;

      .el-date-editor,
      .el-select,
      .el-input {
        width: 100%;
      }
    }

    .el-date-editor.el-input__inner {
      height: 36px;
      line-height: 36px;

      /deep/.el-range__icon,
      /deep/.el-range-separator,
      /deep/.el-range__close-icon {
        line-height: 28px;
      }
    }

    .el-select {
      /deep/.el-input__inner {
        height: 36px;
        line-height: 36px;
      }
    }

    button {
      width: 68px;
      height: 36px;
      line-height: 36px;
      padding: 0;

      &.el-button--default {
        border: 1px solid rgba(228, 228, 235, 1);
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);

        &:hover,
        &:focus {
          border: 1px solid rgba(228, 228, 235, 1);
          background: #fff;
          color: rgba(87, 87, 102, 1);
        }
      }
    }
  }
}

.btn-box {
  padding-top: 20px;

  .switch-box {
    float: right;
    border-radius: 2px;
    overflow: hidden;

    .el-button {
      margin: 0;
      float: left;
      border-radius: 0;
      height: 36px;
      line-height: 36px;
      padding: 0 16px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(144, 147, 153, 1);
      border: 1px solid rgba(228, 228, 235, 1);

      &:hover,
      &:focus {
        border: 1px solid rgba(228, 228, 235, 1);
        color: rgba(144, 147, 153, 1);
      }

      &.active {
        color: rgba(59, 149, 168, 1);
        border-color: rgba(59, 149, 168, 1);
      }
    }

    .el-button:first-child {
      border-right-width: 0;

      &.active {
        border-right-width: 1px;
      }
    }

    .el-button:last-child {
      border-left-width: 0;

      &.active {
        border-left-width: 1px;
      }
    }
  }
}

.operation-box {
  width: auto;
  white-space: nowrap;
  letter-spacing: 0;
  margin: 0 -10px;

  .el-button {
    position: relative;
    margin: 0 10px;
  }

  .el-button::before {
    position: absolute;
    top: 14px;
    right: -10px;
    content: '';
    display: block;
    width: 1px;
    height: 12px;
    background: #dcdfe6;
  }

  .el-button:first-child {
    margin-left: 0;
  }

  .el-button:last-child {
    margin-right: 0;
  }

  .el-button:last-child::before {
    display: none;
  }
}
</style>
