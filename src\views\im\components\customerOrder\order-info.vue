<template>
  <div class="sss-order-info-wrap">
    <ul class="order-list">
      <li v-for="item in orderData" :key="item.orderNo + Date.now()">
        <header class="order-header">
          <span>
            订单编号:
            &nbsp; {{ item.orderNo }}
          </span>
          <span :class="'status-'+item.status" class="status-name">{{ item.statusName }}</span>
        </header>
        <div class="box row-start order-list-wrapper">
          <div class="row-center img-box">
            <img :src="item.imageUrl ? item.imageUrl : ''" :alt="item.merchantName" />
          </div>
          <div class="detail">
            <header class="box row-between column-start">
              <h2 class="ellipsis">
                <el-tooltip
                  :content="item.merchantName"
                  class="item"
                  effect="dark"
                  placement="top-start"
                >
                  <span style="font-size: 14px; color: #000;">{{ item.merchantName }}</span>
                </el-tooltip>
              </h2>
              <el-button
                :disabled="!item.orderNo"
                type="text"
                style="font-size: 12px;"
                @click="viewOrderDetail(item)"
              >
                查看
                <i class="el-icon-arrow-right"></i>
              </el-button>
            </header>
            <time>{{ item.createTime|formatTime }}</time>
            <p class="box row-start">
              <span>共{{ item.varietyNum }}种商品</span>
              <span>
                实付总额：¥
                <i>{{ item.money }}</i>
              </span>
            </p>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>
<script>
export default (function () {
  return {
    props: {
      orderData: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {};
    },
    watch: {},
    methods: {
      viewOrderDetail(item) {
        window.open(item.orderDetailUrl, '_blank');
      },
    },
  };
})();
</script>
<style lang="scss" scoped>
@import './style/order-info.scss';
.sss-order-info-wrap {
  padding-bottom: 0 !important;

  .order-list {
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 0;

    li:first-child {
      margin-top: 0;
    }

    li:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
