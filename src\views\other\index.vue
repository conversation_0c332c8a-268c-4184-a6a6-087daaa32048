<template>
  <div class="DownloadManager">
    <xyy-list-page>
      <template slot="header">
        <el-row>
          <el-row type="flex"
                  class="row-bg"
                  justify="space-between">
            <el-col :span="9"
                    style="text-align: left;font-size:18px;">
              <el-row type="flex"
                      class="row-bg">
                <el-col>
                  <span>下载管理</span>
                  <el-popover placement="bottom"
                              width="359"
                              popper-class="popoverAll"
                              trigger="hover">
                    <p>
                      <span>导出生成的文件在本页面统一查看和下载。生成较大文件时需花费较长时间，通过状态查看文件是否可下载</span>
                    </p>
                    <p>
                      <span>状态可分为</span>
                      <br />未执行 / 生成中 / 已完成 / 执行出现未知错误 / 文件持久化失败 / 数据超出大小限制 / 其中生成中和已完成是正常状态
                    </p>
                    <p>
                      <span>异常状态解释</span>
                      <br />1.未执行:如系统正在生成超大文件，会导致其他待生成的文件排队，排队时状态为未执行。
                      <br />2.文件持久化失败：生成超大文件，可能导致服务器内存溢出，此时生成文件失败
                      <br />3.数据超出大小限制：一次最多到处10w条数据，一次最多1000个sheet，最多可以10000列
                    </p>
                    <div slot="reference"
                         style="cursor:pointer;">
                      <svg-icon class="icon-info"
                                icon-class="info"></svg-icon>
                    </div>
                  </el-popover>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="9">
              <span class="sheetMainBtn"
                    @click="handerRefresh">
                刷新
                <span class="el-icon-refresh-left"></span>
              </span>
            </el-col>
          </el-row>
        </el-row>
      </template>
      <template slot="body">
        <div class="xyyTable xyyTable1">
          <el-table :data="list"
                    stripe
                    style="width: 100%">
            <el-table-column prop="fileName"
                             width="350"
                             label="文件名"></el-table-column>
            <el-table-column :formatter="formatState"
                             prop="state"
                             label="状态"
                             width="180"></el-table-column>
            <el-table-column :formatter="getFormatDate"
                             prop="exportTime"
                             label="操作时间"></el-table-column>
            <el-table-column prop="creatorName"
                             label="操作人"></el-table-column>
            <el-table-column prop="state"
                             label="操作">
              <template slot-scope="scope">
                <span @click="handleDownload(scope.row)"
                      v-if="scope.row.state === 2"
                      class="downLoadText">下载</span>
                <span v-else-if="scope.row.state !== 2"
                      style="color:rgba(64,163,184,1)">-</span>
              </template>
            </el-table-column>
            <div slot="empty"
                 class="empty-data-container">
              <img :src="emptyDataImgUrl" />
            </div>
          </el-table>
          <div class="pagination-container"
               v-if="list.length">
            <el-pagination :current-page="listQuery.page"
                           :page-sizes="listQuery.pageSizes"
                           :page-size="listQuery.pageSize"
                           :total="listQuery.total"
                           layout="total, sizes, prev, pager, next, jumper"
                           @size-change="handleSizeChange"
                           @current-change="handleCurrentChange"></el-pagination>
          </div>
        </div>
      </template>
    </xyy-list-page>
  </div>
</template>

<script>
import emptyDataImgUrl from '@/assets/common/empty-data-table.png';
import { getDownManList } from '@/api/QClassification/index'; //getDownload
import XyyTable from '../../components/xyy/xyy-table/index';
// import moment from 'moment'// 导入文件
export default {
  name: 'OtherIndex',
  components: { XyyTable },
  inject: ['reload'],
  data () {
    return {
      list: [],
      listQuery: {
        page: 1, // 当前显示页数（第几页）
        pageSizes: [10, 20, 30, 40, 50, 100], // 当前显示页数
        pageSize: 10, // 每页显示多少条
        total: 0, // 总条目数(一共多少条)
      },
      isHover: 0,
      emptyDataImgUrl,
    };
  },
  created () {
    this.getList(this.listQuery);
  },
  activated: function () {
    this.getList(this.listQuery);
  },
  methods: {
    // 点击下载按钮事件
    handleDownload (row) {
      let url = process.env.BASE_API + '/workOrder/export/download/' + row.id;
      if (
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
      ) {
        url += `?businessPartCode=${this.$store.getters.channel.businessPartCode}`;
      }
      window.open(url);
    },
    // 刷新按钮
    handerRefresh () {
      this.reload();
    },
    // 分页pagesize(每页条数)改变时触发
    handleSizeChange (val) {
      this.listQuery.pageSize = val;
      this.getList(this.listQuery);
    },
    // page (当前页)改变时会触发
    handleCurrentChange (val) {
      this.listQuery.page = val;
      this.getList(this.listQuery);
    },
    // 操作时间转换
    getFormatDate (row, column, cellValue) {
      return this.getMyDate(parseInt(cellValue));
    },
    getMyDate (str) {
      var oDate = new Date(str),
        oYear = oDate.getFullYear(),
        oMonth = oDate.getMonth() + 1,
        oDay = oDate.getDate(),
        oHour = oDate.getHours(),
        oMin = oDate.getMinutes(),
        oSen = oDate.getSeconds(),
        oTime =
          oYear +
          '-' +
          this.getzf(oMonth) +
          '-' +
          this.getzf(oDay) +
          ' ' +
          this.getzf(oHour) +
          ':' +
          this.getzf(oMin) +
          ':' +
          this.getzf(oSen); // 最后拼接时间
      return oTime;
    },
    // 补0操作
    getzf (testnum) {
      if (parseInt(testnum) < 10) {
        testnum = '0' + testnum;
      }
      return testnum;
    },
    // 判断状态显示数据
    formatState (row, column, cellValue) {
      if (cellValue === 0) {
        return '未执行';
      } else if (cellValue === 1) {
        return '生成中';
      } else if (cellValue === 2) {
        return '已完成';
      } else if (cellValue === -1) {
        return '出现未知错误';
      } else if (cellValue === -2) {
        return '文件持久化失败';
      } else if (cellValue === -3) {
        return '数据超出大小限制';
      }
    },
    // 获取数据
    getList (listQuery) {
      const { page, pageSize } = this.listQuery || listQuery;
      const params = {
        pageNum: page,
        pageSize: pageSize,
      };
      getDownManList(params).then((res) => {
        const { list, pageNum, pageSize, total } = res.data;
        this.list = list;
        this.listQuery = {
          ...this.listQuery,
          page: pageNum,
          pageSize,
          total,
        };
      });
    },
  },
};
</script>
<style>
.el-pager li {
  margin: 0 5px;
}
/*.xyyTable1 .el-table tr td .cell{*/
/*white-space: normal;*/
/*}*/
</style>
<style lang="scss" scoped>
.popoverAll {
  p {
    color: rgba(144, 147, 153, 1);
    font-size: 12px;
    span {
      font-size: 14px;
      color: rgba(41, 41, 51, 1);
    }
  }
}
.DownloadManager {
  /deep/.page-header {
    text-align: right;
  }
  .el-popover__reference {
    width: 160px;
    display: inline-block;
  }
  .sheetMainBtn {
    padding: 10px 12px;
    font-size: 12px;
    color: rgba(64, 163, 184, 1);
    cursor: pointer;
  }

  .downLoadText {
    color: rgba(64, 163, 184, 1);
    cursor: pointer;
  }
  .search-box {
    overflow: hidden;
    padding-bottom: 20px;
    border-bottom: 1px dotted #e4e4eb;
    margin-bottom: 15px;
    label {
      float: left;
      padding-right: 8px;
      font-size: 14px;
      font-weight: 400;
      color: rgba(41, 41, 51, 1);
      line-height: 36px;
    }
    .el-input {
      width: 334px;
      float: left;
      margin-right: 15px;
      /deep/ .el-input__inner {
        height: 36px;
        line-height: 36px;
      }
    }
    button {
      width: 68px;
      height: 36px;
      float: left;
    }
  }
  .operation-box {
    width: auto;
    white-space: nowrap;
    letter-spacing: 0;
    margin: 0 -10px;
    .el-button {
      position: relative;
      margin: 0 10px;
    }
    .el-button::before {
      position: absolute;
      top: 14px;
      right: -10px;
      content: '';
      display: block;
      width: 1px;
      height: 12px;
      background: #dcdfe6;
    }
    .el-button:first-child {
      margin-left: 0;
    }
    .el-button:last-child {
      margin-right: 0;
    }
    .el-button:last-child::before {
      display: none;
    }
  }
}
</style>
