import Layout from '@/views/layout/Layout';
export default [
  {
    path: '/imcontainer',
    component: Layout,
    redirect: 'imcontainer/list',
    meta: {
      title: '在线会话',
      affix: true,
      code: 'menu:csim:talk',
      mark: 'session'
    },
    children: [
      {
        path: 'list',
        name: 'IMViewMain',
        meta: {
          title: '在线会话',
          code: 'menu:csim:talk',
          mark: 'session',
          componentName:'compIMViewMain'//IM关闭页面需执行回调,得主动销毁
        },
        component: () => import('@/views/im/im_view_main')
      }
      // {
      //   path: "list",
      //   name: "imcontainerList",
      //   meta: {
      //     title: "在线会话",
      //     code: "menu:csim:talk",
      //     mark: "session"
      //   },
      //   component: () => import("//@/views/im_view/im_container ")
      // }
    ]
  },
  {
    path: '/imSeach',
    component: Layout,
    redirect: 'imSeach/list',
    meta: {
      title: '对话搜索',
      affix: true,
      code: 'menu:csim:searchdialog',
      mark: 'session'
    },
    children: [
      {
        path: 'list',
        name: 'imSeachList',
        meta: {
          title: '对话搜索',
          code: 'menu:csim:searchdialog',
          mark: 'session',
          componentName:'compIMSearchList'
        },
        component: () => import('@/views/im_view/im_seach')
      }
    ]
  },
  {
    path: '/imServerTotal',
    component: Layout,
    redirect: 'imServerTotal/list',
    meta: {
      title: '服务总结',
      affix: true,
      code: 'menu:csim:summaryQuery',
      mark: 'session'
    },
    children: [
      {
        path: 'list',
        name: 'serveTotal',
        meta: {
          title: '服务总结',
          code: 'menu:csim:summaryQuery',
          mark: 'session',
          componentName:'compServeTotal'
        },
        component: () => import('@/views/im_view/im_serverTotal')
      },
      {
        path: 'imseachdetail/:t',
        name: 'imseachdetail',
        hidden: true,
        meta: {
          title: '服务总结详情 ',
          code: 'menu:csim:summaryQuery',
          mark: 'session'
        },
        component: () => import('@/views/im_view/im_seachDetail')
      },
      {
        path: 'imseachedite/:t',
        name: 'imseachedite',
        hidden: true,
        meta: {
          title: '编辑服务总结 ',
          code: 'menu:csim:summaryQuery',
          mark: 'session'
        },
        component: () => import('@/views/im_view/im_seachEdite')
      }
    ]
  },
  {
    path: '/commentList',
    component: Layout,
    redirect: 'commentList/list',
    meta: {
      affix: true,
      code: 'menu:csim:leavemsg',
      mark: 'session'
    },
    children: [
      {
        path: 'list',
        name: 'commentList',
        meta: {
          title: '留言管理',
          code: 'menu:csim:leavemsg',
          mark: 'session',
          componentName:'compCommentList'
        },
        component: () => import('@/views/im-config/comment/list')
      }
    ]
  }
];
