<?xml version="1.0" encoding="UTF-8"?>
<svg width="50px" height="50px" viewBox="0 0 50 50" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 61.2 (89653) - https://sketch.com -->
    <title>编组</title>
    <desc>Created with Sketch.</desc>
    <g id="1.7" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="参数配置" transform="translate(-552.000000, -153.000000)">
            <rect fill="#FFFFFF" x="0" y="0" width="1440" height="900"></rect>
            <rect id="矩形" fill="#F0F2F5" x="200" y="64" width="1240" height="852"></rect>
            <rect id="Rectangle" fill="#FFFFFF" x="219" y="118" width="1200" height="758"></rect>
            <rect id="矩形备份" stroke="#40A3B8" x="534.5" y="139.5" width="274" height="77" rx="2"></rect>
            <g id="编组-2备份" transform="translate(552.000000, 153.000000)">
                <g id="编组" transform="translate(2.000000, 2.000000)">
                    <circle id="椭圆形" fill="#E9F3F6" cx="23" cy="23" r="23"></circle>
                    <path d="M12,10 L32,10 C33.1045695,10 34,10.8954305 34,12 L34,34 C34,35.6568542 32.6568542,37 31,37 L13,37 C11.3431458,37 10,35.6568542 10,34 L10,12 C10,10.8954305 10.8954305,10 12,10 Z" id="矩形" stroke="#40A3B8" fill="#FFFFFF"></path>
                    <rect id="矩形" fill="#D4EBF2" x="13" y="14" width="8" height="12"></rect>
                    <rect id="矩形" fill="#D4EBF2" x="23" y="21" width="8" height="2"></rect>
                    <rect id="矩形" fill="#D4EBF2" x="23" y="25" width="8" height="2"></rect>
                    <rect id="矩形" fill="#D4EBF2" x="23" y="29" width="8" height="2"></rect>
                    <rect id="矩形" fill="#E9F3F6" x="13" y="28" width="8" height="5"></rect>
                    <rect id="矩形备份-2" fill="#E9F3F6" x="23" y="14" width="8" height="5"></rect>
                    <circle id="椭圆形" fill="#3B95A8" cx="31" cy="31" r="7"></circle>
                    <circle id="椭圆形" fill="#40A3B8" cx="32" cy="31" r="7"></circle>
                    <text id="?" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" fill="#FFFFFF">
                        <tspan x="28" y="36">?</tspan>
                    </text>
                    <path d="M36.3983007,21.5785353 L38.4469024,17.4889741 C38.6942606,16.9951803 39.2950831,16.7954048 39.7888769,17.0427631 C39.8652109,17.0810014 39.9363338,17.1288578 40.0005101,17.1851645 L41.4850016,18.4876196 C41.9001493,18.8518596 41.9414181,19.4836782 41.577178,19.898826 C41.5369827,19.9446392 41.4926832,19.9866819 41.4448327,20.0244291 L37.9117396,22.8115351 C37.4781309,23.1535905 36.8493308,23.0793719 36.5072754,22.6457631 C36.2670618,22.3412548 36.2245896,21.9253094 36.3983007,21.5785353 Z" id="路径-5" fill="#FFB557" transform="translate(38.388310, 20.961011) rotate(-5.000000) translate(-38.388310, -20.961011) "></path>
                    <path d="M39.0941081,24.89881 L40.2866618,22.4993015 C40.532463,22.0047308 41.1326531,21.8030633 41.6272238,22.0488645 C41.705795,22.0879143 41.7788852,22.1371348 41.8446117,22.1952583 L42.4951709,22.7705617 C42.9088905,23.136423 42.9476875,23.7683982 42.5818262,24.1821179 C42.5431668,24.2258344 42.5007547,24.2660829 42.4550756,24.3024025 L40.3883105,25.9456944 C40.0197152,26.2387657 39.4833284,26.1775415 39.1902571,25.8089462 C38.9843114,25.5499286 38.946831,25.1951429 39.0941081,24.89881 Z" id="路径-5备份" fill="#FFDEB3" transform="translate(40.388310, 24.831004) rotate(27.000000) translate(-40.388310, -24.831004) "></path>
                </g>
            </g>
        </g>
    </g>
</svg>