<template>
  <div>
    <el-dialog
      :title="data.title"
      :visible.sync="dialogFormVisible"
      width="800px"
      :before-close="befoClose"
    >
      <div class="im_dialog_transfer_header">
        <el-input :placeholder="data.placeholder" v-model="searchdata" style="width:220px">
          <el-button slot="prepend" icon="el-icon-search" @click="searchAction()"></el-button>
        </el-input>
      </div>
      <div class="im_dialog_transfer_body">
        <div
          v-for="(item,index) in data.tabledata"
          :key="index"
          style="width: 100%; height: 400px;"
        >
          <el-table :data="item" :row-click="rowclick" class="im_dialog_transfer_table">
            <el-table-column :label="item[0].title" :prop="'name'"></el-table-column>
          </el-table>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: ['data', 'dialogFormVisible'],
  data() {
    return {
      searchdata: ''
    };
  },
  methods: {
    searchAction() {},
    rowclick(row, column, event) {
      console.log(row, column, event);
      this.$emit('clickrow', row);
    },
    befoClose(done) {
      this.$emit('im_transfer_dialog', '', false);
    }
  }
};
</script>

<style>
.im_dialog_transfer_body {
  width: 100%;
  display: flex;
  flex-direction: row;
}

.im_dialog_transfer_header {
  width: 100%;
  height: 50px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}

.im_dialog_transfer_table {
  width: 100%;
  height: 100%;
}
</style>