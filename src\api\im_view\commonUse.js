import request from '@/utils/request-im';

/* 获取快捷回复关键词*/
export function getReplyKeyWords(params) {
  return request({
    url: '/quickreply/faq/all',
    method: 'get',
    params: params
  });
}

/* 获取常用图片*/
export function getCommonPic(params) {
  return request({
    url: '/picture/list',
    method: 'get',
    params: params
  });
}
/* 获取常用连接*/
export function getCommonLink(params) {
  return request({
    url: '/link/ajax/list',
    method: 'get',
    params: params
  });
}
/* 获取FAQ*/
export function getFAQ(params) {
  return request({
    url: '/faqcustom/init_tree',
    method: 'get',
    params: params
  });
}
