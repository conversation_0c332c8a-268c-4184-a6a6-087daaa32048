import request from "@/utils/request";
import { formData } from "@/utils/index";

/* 获取全部已开的区域信息列表*/
export function getAllBranchs() {
  return request({
    url: "/api/cs/ec/getAllBranchs",
    method: "get"
  });
}
/* 查询商品列表数据*/
export function getPageInfo(params) {
  return request({
    url: "/api/cs/ec/pageCsuInfos",
    method: "get",
    params
  });
}

/* 查询不同级别的商品分类*/
/* 级别（1：一级分类2：二级分类3：三级分类）:level(必填)*/
export function findCategoryListByLevel(params) {
  return request({
    url: "/api/cs/ec/findCategoryListByLevel",
    method: "get",
    params
  });
}

/* 通过父分类ID查询所有子分类*/
/* 参数名：id(必填)，父id*/
export function getListCategoryByParentId(params) {
  return request({
    url: "/api/cs/ec/listCategoryByParentId",
    method: "get",
    params
  });
}
/* 根据branchcode 获取店铺列表*/
export function getShopList(params) {
  return request({
    url: "/api/cs/ec/queryInfoByBranchCode",
    method: "get",
    params
  });
}

/**
 * 以下药品召回接口
 */
/**
 * 召回单列表查询
 * @param {*} data
 */
export function workOrderRecallGetList(data) {
  return request({
    url: "/workOrderRecall/getList",
    method: "post",
    data: formData(data)
  });
}

/**
 * 召回单删除
 * @param {*} data
 */
export function workOrderRecallDel(data) {
  return request({
    url: "/workOrderRecall/workOrderRecallDel",
    method: "post",
    data: formData(data)
  });
}

/**
 * 超回单启用禁用
 */
export function workOrderRecallEnableAndDisable(data) {
  return request({
    url: "/workOrderRecall/workOrderRecallEnableAndDisable",
    method: "post",
    data: formData(data)
  });
}

/* 药品召回信息登记*/
export function getPreview(params) {
  return request({
    url: "recallInfo/submit",
    method: "post",
    params
  });
}

/* 客户信息登记列表查询 */
export function getCustomInfoList(data) {
  return request({
    url: "/recallInfos/getList",
    method: "post",
    data
  });
}
// 导出
export function exportCustomInfoList(data) {
  return request({
    url: "/recallInfos/exports",
    method: "post",
    data
  });
}
/**
 * 新建编辑提交
 * params 提交数据
 */
export function postDrugList(params) {
  return request({
    url: "/workOrderRecall/submitRecall",
    method: "post",
    data: params
  });
}
