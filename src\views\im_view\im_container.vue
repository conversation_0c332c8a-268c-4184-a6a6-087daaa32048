<template>
  <div style="background: #f0f2f5;">
    <div class="im_container_mainview">
      <chatleftview ref="container_main_leftview"
                    :owner-avatar-url="ownerAvatarUrl"
                    :kefu-info-data="kefuInfoData"
                    :select-number="messageType"
                    :user-data-array="userdata"
                    :ing-number="ingNumber"
                    :table-height="797"
                    class="im_container_main_leftview"
                    @im_container_left_clickrow="chatLeftClickRow"
                    @im_container_left_clicktype="chatLeftClickType"
                    @im_container_left_scroll="chatmoreAction"
                    @im_container_left_searchFunction="searchHisFuction"
                    @im_container_left_searchMessage="getCurrentMessageList"></chatleftview>
      <div :style="{ height: 797 + 'px' }"
           :hidden="refreshChatView"
           class="im_container_midview">
        <div :style="{ height: 797 - 40 + 'px' }"
             style="border: 1px solid #dcdee3;">
          <topview ref="container_main_topview"
                   :view-data="currentUserData"
                   :black-list-auth="kefuInfoData.blackListAuth"
                   :viewtype="topvuewType"
                   :container-id="currentContainerID"
                   class="im_container_chat_top_view"
                   @user_top_view="userTopView"></topview>
          <chatcontent ref="newMessage"
                       :style="{ height: contentMaxHeight + 'px' }"
                       :owner-avatar-url="ownerAvatarUrl"
                       :contact-avatar-url="contactAvatarUrl"
                       :chatdata="chatdata"
                       :section-name="sectionName"
                       :need-event="chatTopScroll"
                       :max-height="contentMaxHeight"
                       class="im_container_chat_content"></chatcontent>
          <chatinput v-if="topvuewType === 'ing'"
                     ref="contentinput"
                     :container-id="currentContainerID"
                     :uploadurl="imgUploadUrl"
                     :contentEdit="editorText"
                     @clickInputTool="clickInputTool"></chatinput>
          <div v-if="topvuewType === 'ing'"
               style="width:100%; background-color:white; height:70px;display: flex;justify-content: flex-end;align-items: center;">
            <el-button style="margin-right:20px;"
                       size="small"
                       type="success"
                       class="send-chat-msg"
                       @click="apisendmessage()">发送</el-button>
          </div>
        </div>
      </div>
      <div :style="{ height: 797 + 'px' }"
           class="im_container_rightview">
        <div v-if="topvuewType != 'ing'"
             style="overflow-y: auto;">
          <userdetail :prohieght="797 - 40 + 'px'"
                      :user-detail-array="userDetailArray"
                      :type="topvuewType"
                      :current-id="currentContainerID"></userdetail>
        </div>
        <div v-else
             :style="{ height: 797 - 40 + 'px' }"
             style="overflow-y: auto;">
          <rightview ref="rightview"></rightview>
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="zhuanjiealert"
               :show-close="false"
               :close-on-press-escape="false"
               :close-on-click-modal="false"
               title="提示"
               width="30%">
      <span>您有新的转接信息，是否同意转接？</span>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="zhuanjieRe()">拒 绝</el-button>
        <el-button type="primary"
                   @click="zhuanjieSuc()">确 定</el-button>
      </span>
    </el-dialog>
    <!-- <imagealert ref="imgAlert"></imagealert> -->
  </div>
</template>

<script>
import chatinput from './components/container_message/im_container_chatinput';
import chatleftview from './components/container_message/im_container_leftview';
import chatcontent from './components/container_message/im_container_content';
import topview from './components/container_message/im_container_topview';
import userdetail from './components/container_message/im_container_userdatail';
import rightview from './message-config';
import utils from '@/utils/form';
import imagealert from './components/container_message/im_messageImgAlert';
// 获取到消息之后的处理方法
import {
  historyState,
  containerState,
  containerType,
  containerMessage,
  userDatas,
  kfStatesKeyChange,
  historyUserData,
  historyMessageData,
  topData,
  userDatasSec
} from './components/im_container_tool/message_tool';

// 接口
import {
  readMessageList,
  sendMessage,
  kfUserInfo,
  currentlist,
  Kefustatus,
  currentMessageList,
  readHistoryMessageList,
  historyMessage,
  startChart,
  closeContainer,
  addblack,
  kfAdvice,
  transferSend,
  transferReject,
  transferAccept,
  dialogsDetail,
  sayIngMessage,
  settodooo,
  liuyanDialogsDetail
} from '@/api/im_view/index';

export default {
  components: {
    chatinput,
    chatleftview,
    chatcontent,
    topview,
    rightview,
    userdetail,
    imagealert
  },

  data () {
    return {
      lock: false,
      lockPageid: false,
      reading: true,
      creatrun: true,
      pageid: '',
      im_reults: [], // 已填写完服务总结的对话
      zhuanjiealert: false,
      zhuanjiedata: {},
      kefuInfoData: {},
      chatContentData: [],
      screenHeight: window.screen.availHeight,
      refreshChatView: false,
      containerDatas: {}, // 消息总数居
      sectionName: 'im_container_main',
      ownerAvatarUrl: '/pc/static/user_kf.png',
      contactAvatarUrl: '/pc/static/user_kh.png',
      chatdata: [], // 消息数据
      userdata: [], // 左侧列表数据
      userdataIng: [], // 会话中
      userdataWill: [], // 待跟进
      userdataHis: [], // 历史会话
      userdataEd: [], // 留言
      currentUserData: { avatar: '', textData: [] },
      contentMaxHeight: 470,
      messageType: 0, // 当前列表类型
      currentContainerID: '0', // 当前会话id
      chatTopScroll: false, // 是否需要上拉加载更多
      topvuewType: 'ing', // 顶部客户信息简介类型
      imgUploadUrl: process.env.BASE_API_IM + '/im-file/uploadimg',
      historyPageNum: 1, // 历史消息页码
      chatContente: '', // 输入框的内容
      userDetailArray: [], // 访客信息
      isRunAndRun: true, // 消息轮询开关
      tabFlag: false,
      ingNumber: 0, //实际服务人数，废弃userdataIng.length用法
      pollingResponseFlag: false, //轮询是否返回标识
      editorText: {
        editorTextIn: '',
        editorTextIndex: 1
      },
      editorFlag: false
    };
  },
  watch: {
    $route (to, from) {
      if (from.path === '/imcontainer/list') {
        this.isRunAndRun = false;
      }
      if (to.path === '/imcontainer/list') {
        // if (this.lockPageid === true) {
        //   this.isRunAndRun = true;
        // }
        this.$vnode.parent.componentInstance.cache = {};
        this.$vnode.parent.componentInstance.key = [];
      }
    },
    isRunAndRun (val) {
      if (val) {
        this.getmessage(this.$store.getters.imcreate);
      }
    }
  },
  created () {
    this.apikfUserInfo();
    this.emits(this, 'register', {
      type: 'tagclose_imcontainer',
      uid: this._uid,
      fn: () => {
        this.isRunAndRun = false;
      }
    });
    this.emits(this, 'register', {
      type: 'im_result',
      uid: this._uid,
      fn: containerid => {
        this.im_reults.push(containerid);
      }
    });
    this.emits(this, 'register', {
      type: 'leftview_data_tosort',
      uid: this._uid,
      fn: data => {
        this.userdata = [];
        this.userdata = data;
      }
    });
    if (this.$store.getters.imcreate === '') {
      this.$store.commit(
        'imProperty/SET_IMProperty_containercreate',
        Math.ceil(Math.random() * 1000000).toString()
      );
    }
    this.lockPageid = true;
    // }
    //  else {
    //   this.isRunAndRun = false;
    //   this.apikfUserInfo1();
    // }
  },
  methods: {
    conntentHeight (type) {
      if (type === 'other') {
        return 797 - 40 - 83;
      }
      if (type === 'history') {
        return 797 - 40 - 83;
      } else {
        return type === 'show'
          ? 797 - 40 - 70 - 170 - 47
          : 797 - 40 - 70 - 170 - 136;
      }
    },
    clickInputTool (type, data, contenttype) {
      switch (type) {
        case 'transfera': {
          this.transfera(data);
          break;
        }
        case 'history': {
          this.history();
          break;
        }
        case 'assess': {
          this.apikfAdvice();
          break;
        }
        case 'changeContent': {
          this.chatContente = data;
          if (contenttype === 'kjhf') {
            return;
          }
          // const str = this.chatContente.substring(
          //   this.chatContente.length - 17,
          //   this.chatContente.length
          // );

          // if (str.indexOf('<br />') !== -1) {
          //   this.chatContente = this.chatContente.substring(
          //     0,
          //     this.chatContente.length - 17
          //   );
          //   this.chatContente = this.haveAndDeletebr(this.chatContente);
          //   if (this.chatContente === '') {
          //     return;
          //   }
          //   // this.apisendmessage();
          // }

          break;
        }
        case 'changeSrc': {
          this.chatContentData.push(data);
          break;
        }
      }
    },
    //  dealString (str) {
    //     let result = '';
    //     let flag = false;
    //     for (let char of str) {
    //       if(char === '>') {
    //         flag = true;
    //       }
    //       if(char === '<') {
    //         flag = false
    //       }
    //       if(flag && char !== '>') {
    //         result = result + char;
    //       }
    //     }
    //     return result;
    //   },
    haveAndDeletebr (str) {
      const str1 = str.substring(str.length - 17, str.length);

      if (str1.indexOf('<br />') !== -1) {
        str = str.substring(0, str - 17);
      }
      return str;
    },

    transfera (content) {
      transferSend({
        did: this.currentContainerID,
        id: content.id,
        type: content.type
      }).then(res => {
        if (res.code === 1) {
          this.$XyyMessage.success('转接请求发送成功');
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    history (content) {
      this.$refs.rightview.tohistory();
      // alert('history');
    },

    // 加载更多
    chatmoreAction () {
      if (
        this.userdata.length !== 0 &&
        typeof this.userdata[this.userdata.length - 1] === 'string'
      ) {
        return;
      }
      if (this.historyPageNum === '已无数据') {
        this.userdata.push('到底了...');
        return;
      }

      this.getHistoryMessageList();
    },
    // 发送消息
    apisendmessage () {
      // this.$refs.imgAlert.openView();
      // return;
      if (this.currentContainerID === '0') {
        this.$XyyMessage.error('请选择会话');
        return;
      }
      if (!this.chatContente.length) {
        this.$XyyMessage.error('请不要发送空信息');
        return;
      }

      let chatcopy = this.chatContente;
      for (let index = 0; index < this.chatContentData.length; index++) {
        const element = this.chatContentData[index];
        if (chatcopy.indexOf(element.oldvalue) !== -1) {
          const copycontent = chatcopy.replace(
            element.oldvalue,
            element.newvalue
          );
          chatcopy = copycontent;
        }
      }

      sendMessage(this.currentContainerID, {
        dialogid: this.currentContainerID,
        msgText: chatcopy
      }).then(res => {
        if (res.code === 1) {
          this.chatContente = '';
          this.$refs.contentinput.cleanInput();
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    // 点击左侧人员列表刷新消息
    chatLeftClickRow (val) {
      this.$refs.container_main_topview.resetRestartBtnDisabled();
      if (val.row) {
        this.currentContainerID = val.row.containerId || '';
        this.contactAvatarUrl = val.row.avatar || '';
        this.refreshEditContent(this.currentContainerID);
        this.$store.commit(
          'imProperty/SET_IMProperty_containerid',
          this.currentContainerID
        );
        this.$store.commit('imProperty/SET_IMProperty_kfid', val.row.kfid);
        this.$store.commit('imProperty/SET_IMProperty_khid', val.row.khid);

        // 会话中 0 // 待跟进  1 // 历史会话 2 // 留言 3

        switch (this.messageType) {
          case 0: {
            this.refreshMessage(this.currentContainerID);
            this.topvuewType = 'ing';
            this.chatTopScroll = false;
            this.contentMaxHeight = this.conntentHeight('show');
            this.deleteRedPop(this.currentContainerID, this.userdataIng, val);
            //this.getMessageAlone(this.currentContainerID);
            break;
          }
          case 1: {
            this.refreshMessage(this.currentContainerID);
            this.topvuewType = 'other';
            this.chatTopScroll = false;
            this.contentMaxHeight = this.conntentHeight('other');
            this.userInfomation(val.row);
            this.deleteRedPop(this.currentContainerID, this.userdataWill, val);
            //this.getMessageAlone(this.currentContainerID);
            break;
          }
          case 2: {
            this.topvuewType = 'history';
            this.chatTopScroll = false;
            this.contentMaxHeight = this.conntentHeight('history');
            this.getHistoryMessage(this.currentContainerID);
            this.userInfomation(val.row);
            break;
          }
          case 3: {
            this.refreshMessage(this.currentContainerID);
            this.topvuewType = 'other';
            this.chatTopScroll = false;
            this.contentMaxHeight = this.conntentHeight('other');
            this.userInfomation(val.row);
            this.deleteRedPop(this.currentContainerID, this.userdataEd, val);
            //this.getMessageAlone(this.currentContainerID);
            break;
          }
          default:
            break;
        }
      }
    },
    // 刷新消息
    refreshMessage (currentContainerID) {
      this.refreshChatView = true;
      const containerId = currentContainerID || this.currentContainerID;
      this.$nextTick(() => {
        this.chatdata = [];
        debugger;
        this.chatdata = this.containerDatas[containerId].messages;
        this.refreshChatView = false;
      });
    },
    // 更新编辑器内容
    refreshEditContent (currentContainerID) {
      // if (!this.editorFlag) {
      //   this.editorText = undefined;
      //   this.editorFlag = true;
      //   return;
      // }
      this.$nextTick(() => {
        this.editorText.editorTextIn = '';
        this.editorText.editorTextIn = this.$store.getters.editContentData(
          currentContainerID
        );

        this.editorText.editorTextIndex++;

        // if (!this.editorText) {
        //   this.editorText = '';
        // }
      });
    },
    chatLeftClickTypeING (type) {
      this.messageType = type;
      switch (type) {
        case 0:
          this.userdata = this.userdataIng;
          // this.closeChat(this.userdata);
          break;
        case 1:
          this.userdata = this.userdataWill;
          break;
        case 3:
          this.userdata = this.userdataEd;
          break;

        default:
          break;
      }
    },

    // 人员列表类型切换
    chatLeftClickType (type, searchstr) {
      this.historyPageNum = 1;
      this.messageType = type;
      this.userdata = [];
      this.$nextTick(() => {
        this.$refs.container_main_leftview.pushToBottom(false);
        switch (type) {
          case 0: {
            this.userdata = this.userdataIng;
            this.initChat();
            break;
          }
          case 1: {
            this.userdata = this.userdataWill;
            break;
          }
          case 2: {
            this.getHistoryMessageList(searchstr);
            break;
          }
          case 3: {
            this.userdata = this.userdataEd;
            break;
          }
        }
      });
    },
    // 对象去重
    unique (arr) {
      let name = 'containerId';
      let map = new Map();
      for (let item of arr) {
        if (!map.has(item.name)) {
          map.set(item.name, item);
        }
      }
      return [...map.values()];
    },

    // 消除红点
    deleteRedPop (containerId, userdata, val) {
      if (containerId) {
        this.containerDatas[containerId].dialoginfo.unreadNum = 0;
        userdata[val.$index].unreadNum = 0;
      }
    },

    // 获取顶部数据
    topViewVelue () {
      const _this = this;
      if (_this.currentContainerID !== '0' && _this.messageType === 0) {
        var containerData = _this.containerDatas[_this.currentContainerID];
        if (containerData === undefined) {
          return;
        }
        _this.currentUserData = topData(containerData);
      }
    },

    userInfomation (userdata) {
      this.currentUserData = {
        textData: ['客户昵称：' + userdata.name],
        avatar: userdata.avatar
      };
      if (userdata.channelId === 1001) {
        this.userDetailArray = [
          { title: '渠道类型：', detail: userdata.referer },
          { title: '微信昵称：', detail: userdata.wxName },
          { title: '客户名称：', detail: userdata.name },
          { title: '所在地区：', detail: userdata.adress }
        ];
      } else {
        this.userDetailArray = [
          { title: '渠道类型：', detail: userdata.referer },
          { title: '客户名称：', detail: userdata.name },
          { title: '所在地区：', detail: userdata.adress }
        ];
      }
    },
    // 初始化聊天页面
    initContent () {
      this.chatLeftClickType(0);
      this.currentContainerID = '0';
      this.chatdata = [];
      this.currentUserData = { avatar: '', textData: [] };
    },

    // 会话中的第一个聊天会话
    nextContent () {
      this.chatLeftClickTypeING(0);
      this.chatLeftClickRow({ $index: 0, row: this.userdataIng[0] });
      this.$nextTick(() => {
        this.$refs.container_main_leftview.tosayIng();
      });
    },

    // 消息处理
    containerMassage (msg) {
      let data = containerMessage(msg); //每一条消息添加direction,msgtype,ctime
      if (data) {
        //消息加入总数据
        const oldAndNew = containerType(
          this.containerDatas,
          data,
          this.currentContainerID === data.dialoginfo.id //当前会话id
        );
        this.containerDatas[data.dialoginfo.id] = oldAndNew.newmsg;
        // 用户列表分类
        userDatasSec(
          oldAndNew.newmsg,
          oldAndNew.oldmsg,
          this.userdataIng,
          this.userdataWill,
          this.userdataEd
        );
      }
    },
    // 聊天页上方的工具栏方法
    userTopView (type, data) {
      debugger;
      if (this.currentContainerID === '0') {
        this.$XyyMessage.error('请选择会话');
        return;
      }
      if (type === 'addblacklist') {
        this.apiAddblack(data);
      } else if (type === 'closeContainer') {
        this.apiCloseTalking(this.currentContainerID);
      } else if (type === 'show') {
        this.contentMaxHeight = this.conntentHeight('close');
        this.$refs.newMessage.scrollowToBottom();
      } else if (type === 'close') {
        this.contentMaxHeight = this.conntentHeight('show');
      } else if (type === 'startContainer') {
        this.apiStartTalking();
      }
    },

    zhuanjieRe () {
      transferReject(this.zhuanjiedata.dialoginfo.id).then(res => {
        this.zhuanjiealert = false;
        if (res.code === 1) {
          this.$XyyMessage.success('成功');
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    zhuanjieSuc () {
      transferAccept(this.zhuanjiedata.dialoginfo.id).then(res => {
        this.zhuanjiealert = false;
        if (res.code === 1) {
          if (res.data.type === 404) {
            this.$XyyMessage.error('转接会话错误');
            return;
          }
          this.apiDialogsDetail(res.data.dialogid);
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    stopReadMsg () {
      this.isRunAndRun = false;
      // this.lock = true;
    },
    startReadMsg () {
      this.isRunAndRun = true;
      // this.lock = false;
    },

    /**
     * 请求API
     */

    // 获取客服的信息
    apikfUserInfo (type = '', cb) {
      kfUserInfo().then(res => {
        if (res.code === 1) {
          this.kefuInfoData = res.data;
          if (
            this.kefuInfoData.avatar !== null &&
            this.kefuInfoData.avatar !== undefined &&
            this.kefuInfoData.avatar !== ''
          ) {
            this.ownerAvatarUrl = this.kefuInfoData.avatar;
          }
          // 如果子组件调用则不执行
          !type && this.getCurrentMessageList();
          cb && cb();
        } else {
          this.$XyyMessage.error(res.msg || '接口超时');
        }
      });
    },

    // 初始加载左侧会话列表数据,  dialogType: 1, 会话中, 2: 留言, 3: 待跟进;
    getCurrentMessageList () {
      this.$store.commit('imProperty/SET_IMProperty_containerid', undefined);
      this.$store.commit('imProperty/SET_IMProperty_kfid', undefined);
      this.$store.commit('imProperty/SET_IMProperty_khid', undefined);

      //初始加载未关闭对话
      currentMessageList(this.$store.getters.imcreate).then(res => {
        if (res.code !== 1) {
          return;
        }
        if (res.data !== null) {
          this.containerDatas = {}; //消息总数据
          this.chatdata = []; // 消息数据
          this.userdataIng = []; // 会话中
          this.userdataWill = []; // 待跟进
          this.userdataHis = []; // 历史会话
          this.userdataEd = []; // 留言

          res.data.forEach((dataElement, dataIndex) => {
            if (dataElement.messages.length > 0) {
              for (
                let len = dataElement.messages.length, i = len - 1;
                i >= 0;
                i--
              ) {
                //310 订单详情页进入客服聊天界面发送的系统消息
                if (dataElement.messages[i].type == 310) {
                  res.data[dataIndex].messages.splice(i, 1);
                }
              }
            }
          });
          for (let index = 0; index < res.data.length; index++) {
            const element = res.data[index];
            this.containerMassage(element);
          }

          //服务人数赋值
          this.ingNumber = this.userdataIng.length;
          this.$refs.container_main_leftview.KFInfoData(this.ingNumber);
        }

        //tabs切换到会话中
        this.chatLeftClickType(this.messageType);
        if (this.$store.getters.imcreate) {
        }

        //定时器获取消息
        this.getmessage(this.$store.getters.imcreate);
        // this.$store.commit('imProperty/SET_IMProperty_containercreate', '1');
      });
    },

    getMessageAlone (containerId) {
      readMessageList({
        pageId: this.$store.getters.imcreate,
        dialogid:
          this.currentContainerID === '0'
            ? ''
            : containerId || this.currentContainerID,
        t: Math.ceil(Math.random() * 1000000).toString()
      }).then(res => {
        if (res.code === 10001) {
          this.$alert('此账号已从别处登录，已停止消息读取', '提示', {
            confirmButtonText: '确定',
            callback: action => { }
          });
          this.isRunAndRun = false;
          return;
        }
        if (res.code !== 1) {
          return this.$XyyMessage.error(res.msg || '接口错误');
        }
        console.log(res.data.messages[0], 'type');
        //this.removeChatingToHis(res.data.messages[0]); // 处理用户结束会话;
        if (res.data && res.data.dialoginfo) {
          if (
            res.data.messages.length !== 0 &&
            res.data.messages[0].type === 408
          ) {
            this.zhuanjiedata = res.data;
            this.zhuanjiealert = true;
          } else if (
            res.data.messages.length !== 0 &&
            res.data.messages[0].type === 310
          ) {
          } else if (
            res.data.messages.length !== 0 &&
            res.data.messages[0].type === 410
          ) {
            const msgarr = this.userdataIng.filter(
              item => item.containerId === this.currentContainerID
            );
            const index = this.userdataIng.indexOf(msgarr[0]);
            this.userdataIng.splice(index, 1);
            delete this.containerDatas[this.currentContainerID];
            if (this.userdataIng.length === 0) {
              this.initContent();
            } else {
              this.nextContent();
            }
            this.$XyyMessage.success('对方已接受转接');
          } else if (
            res.data.messages.length !== 0 &&
            res.data.messages[0].type === 411
          ) {
            this.$XyyMessage.warning('对方拒绝了你的转接');
          } else {
            if (!this.containerDatas.hasOwnProperty(res.data.dialoginfo.id)) {
              this.containerMassage(res.data);
              //this.apisayIngMessage(res.data.dialoginfo.id);
              this.recomputedChatList(res.data);
            } else {
              this.containerMassage(res.data);
              if (this.currentContainerID === res.data.dialoginfo.id) {
                const messageLength = this.containerDatas[
                  this.currentContainerID
                ].messages.length;
                const containerData = this.containerDatas[
                  this.currentContainerID
                ];
                var newmessage = containerData.messages[messageLength - 1];
                this.$refs.newMessage.newMessage(newmessage);
              }
              this.recomputedChatList(res.data);
            }
          }
        }
      });
    },
    // 获取消息并刷新
    getmessage () {
      // 解决首次进入会话页面状态和时间延迟显示的问题
      if (this.$refs.container_main_leftview) {
        this.$refs.container_main_leftview.KFInfoData(this.ingNumber);
        this.$refs.container_main_leftview.KFSortNumber();
      }
      //setInterval(() => {
      // console.log('readmessage request');
      if (this.isRunAndRun) {
        readMessageList({
          pageId: this.$store.getters.imcreate,
          dialogid:
            this.currentContainerID === '0' ? '' : this.currentContainerID, //当前会话id
          t: Math.ceil(Math.random() * 1000000).toString()
        })
          .then(res => {
            // console.log('readmessage response');
            if (res.code === 1 && res.data) {
              //this.removeChatingToHis(res.data.messages[0]); // 处理用户结束会话;
              if (res.data.dialoginfo) {
                const dialogType = res.data.messages[0].type;
                if (dialogType === 408) {
                  //408 对话转移请求
                  console.log('408');
                  this.zhuanjiedata = res.data;
                  this.zhuanjiealert = true;
                } else if (dialogType === 310) {
                  //310 订单详情页进入客服聊天界面发送的系统消息
                  console.log('310');
                } else if (dialogType === 410) {
                  //410 接受方接受转移请求
                  console.log('410');
                  const msgarr = this.userdataIng.filter(
                    item => item.containerId === this.currentContainerID
                  );
                  const index = this.userdataIng.indexOf(msgarr[0]);
                  this.userdataIng.splice(index, 1);
                  this.ingNumber--;
                  this.$refs.container_main_leftview.KFInfoData(this.ingNumber);
                  delete this.containerDatas[this.currentContainerID];
                  // if (this.userdataIng.length === 0) {
                  //   this.initContent();
                  // } else {
                  //   this.nextContent();
                  // }
                  this.userdataIng.length
                    ? this.nextContent()
                    : this.initContent();
                  // 再次调用（改动一行）
                  //this.getCurrentMessageList();
                  this.$XyyMessage.success('对方已接受转接');
                } else if (dialogType === 411) {
                  //411 接受方拒绝转移请求
                  this.$XyyMessage.warning('对方拒绝了你的转接');
                } else {
                  console.log('dialoginfo is not null');
                  console.log('初始数据不包含当前会话');
                  console.log(
                    'containerDatas不包含当前会话',
                    !this.containerDatas.hasOwnProperty(res.data.dialoginfo.id)
                  );
                  console.log(
                    'userdataIng不包含当前会话',
                    !this.userdataIng.hasOwnProperty(res.data.dialoginfo.id)
                  );

                  if (
                    !this.containerDatas.hasOwnProperty(res.data.dialoginfo.id)
                  ) {
                    this.containerMassage(res.data);
                    this.isRunAndRun = false; //暂停轮询
                    this.apisayIngMessage(
                      res.data.dialoginfo.id,
                      undefined,
                      () => {
                        this.recomputedChatList(res.data);
                      }
                    );
                    //this.recomputedChatList(res.data);
                  } else {
                    console.log('初始数据包含当前会话');
                    this.containerMassage(res.data);
                    if (this.currentContainerID === res.data.dialoginfo.id) {
                      const messageLength = this.containerDatas[
                        this.currentContainerID
                      ].messages.length;
                      const containerData = this.containerDatas[
                        this.currentContainerID
                      ];
                      const newmessage =
                        containerData.messages[messageLength - 1];
                      this.$refs.newMessage.newMessage(newmessage);
                    }

                    this.recomputedChatList(res.data);
                  }
                }
              } else {
                //没有消息体
                // console.log('dialoginfo is null');
                this.recomputedChatList(res.data);
              }
              //2020.7.4,rl,处理会话人数超上限等问题,begin
              this.getmessage(this.$store.getters.imcreate);
              //2020.7.4,rl,处理会话人数超上限等问题,end
            } else if (res.code === 10001) {
              this.$alert('此账号已从别处登录，已停止消息读取', '提示', {
                confirmButtonText: '确定',
                callback: action => { }
              });
              this.isRunAndRun = false;
              return;
            } else {
              this.$XyyMessage.error(res.msg);
              setTimeout(() => {
                this.getmessage(this.$store.getters.imcreate);
              }, 1000);
            }
          })
          .catch(err => {
            console.log(err, 'error');
            setTimeout(() => {
              this.getmessage(this.$store.getters.imcreate);
            }, 1000);
          });
        this.topViewVelue();
        //this.$refs.container_main_leftview.KFInfoData();
        this.$refs.container_main_leftview.KFSortNumber();
      }
      // if (this.isRunAndRun) {
      //   this.getmessage();
      // }

      //}, 1000);
    },
    // 搜索
    searchHisFuction (searchStr) {
      this.historyPageNum = 1;
      this.getHistoryMessageList(searchStr);
    },
    // 加载历史会话信息
    getHistoryMessageList (searchStr) {
      readHistoryMessageList({
        page: this.historyPageNum,
        pagesize: 20,
        queryType: '3',
        keyword: searchStr
      }).then(res => {
        if (res.code !== 1)
          return this.$XyyMessage.error(res.msg || '接口超时');
        if (this.historyPageNum === 1) {
          this.historyPageNum += 1;
          this.userdataHis = [];

          for (let index = 0; index < res.data.datas.length; index++) {
            const element = res.data.datas[index];
            this.userdataHis.push(historyUserData(element));
          }
        } else {
          this.historyPageNum += 1;
          for (let index = 0; index < res.data.datas.length; index++) {
            const element = res.data.datas[index];
            this.userdataHis.push(historyUserData(element));
          }
          if (res.data.datas.length < 20) {
            if (this.userdataHis.length > 7) {
              this.$refs.container_main_leftview.pushToBottom(true);
            }
          }
        }
        if (this.messageType === 2) {
          this.userdata = this.userdataHis;
        }
      });
    },

    apiDialogsDetail (containerID) {
      dialogsDetail({ containerid: containerID }).then(res => {
        if (res.code === 1) {
          if (!res.data.messages) {
            return;
          }
          this.containerMassage({
            dialoginfo: res.data,
            messages: res.data.messages
          });
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    // 加载会话中的一些消息
    apisayIngMessage (containerID, type, cb) {
      sayIngMessage({ containerid: containerID })
        .then(res => {
          if (res.code !== 1) {
            this.$XyyMessage.error(res.msg);
            return;
          }
          let dataArr = res.data;
          if (dataArr && dataArr.messages && dataArr.messages.length) {
            for (let len = dataArr.messages.length, i = len - 1; i >= 0; i--) {
              //310 订单详情页进入客服聊天界面发送的系统消息
              if (dataArr.messages[i].type == 310) {
                dataArr.messages.splice(i, 1);
              }
            }
          }
          //留言转会话
          if (type === 309) {
            this.containerMassage(dataArr);
            this.chatLeftClickType(0);
          }
          this.containerMassage(dataArr);

          cb && cb();
        })
        .finally(() => {
          console.log('finally');
          this.isRunAndRun = true; //开启轮询
        });
    },

    // 根据containerID获取历史消息
    getHistoryMessage (containerID) {
      debugger;
      historyMessage({ containerid: containerID }).then(res => {
        if (res.data === null || res.data.length === 0) {
          return;
        }
        for (let len = res.data.length, i = len - 1; i >= 0; i--) {
          if (res.data[i].type == 310) {
            res.data.splice(i, 1);
          }
        }
        var messageArr = historyMessageData(res.data);
        this.chatdata = messageArr;
      });
    },
    // 开始会话
    apiStartTalking () {
      const that = this;
      this.stopReadMsg();
      const data = that.containerDatas[that.currentContainerID];
      const dataType = { dataType: data.dialoginfo.dialogType };
      startChart({ dialogid: that.currentContainerID })
        .then(res => {
          if (res.code === 1) {
            if (dataType.dataType === 2) {
              /*
              that.containerDatas[
                that.currentContainerID
              ].dialoginfo.dialogType = 1;
              const msgarr = that.userdataEd.filter(
                item => item.containerId === that.currentContainerID
              );
              if (msgarr.length) {
                const index = that.userdataEd.indexOf(msgarr[0]);
                that.userdataEd.splice(index, 1);
                that.userdataIng.splice(0, 0, msgarr[0]);

                //that.nextContent();
                //that.loadLeftMessageList();
              }
              */
              that.$XyyMessage.success('发起会话成功');
              that.reloadCurrentMessageList(() => {
                //this.userdataIng.length ? this.nextContent() : this.initContent();
                this.chatLeftClickTypeING(0);
                this.chatLeftClickRow({
                  $index: 0,
                  row: this.userdataIng[0]
                });
                this.$refs.container_main_leftview.tosayIng();
                this.isRunAndRun = true;
              });
            } else {
              /*
              that.containerDatas[
                that.currentContainerID
              ].dialoginfo.dialogType = 1;
              const item = that.userdataWill.filter(
                e => e.containerId === that.currentContainerID
              )[0];
              const index = that.userdataWill.indexOf(item);
              if (item) {
                item.state =
                  containerState(item.type) === ''
                    ? historyState(item.type)
                    : containerState(item.type);
                that.userdataWill.splice(index, 1);
                that.userdataIng.splice(0, 0, item);
                //that.nextContent();
                //that.loadLeftMessageList();
              }
              */
              that.$XyyMessage.success('发起会话成功');
              that.reloadCurrentMessageList(() => {
                //this.userdataIng.length ? this.nextContent() : this.initContent();
                this.chatLeftClickTypeING(0);
                this.chatLeftClickRow({
                  $index: 0,
                  row: this.userdataIng[0]
                });
                this.$refs.container_main_leftview.tosayIng();
                this.isRunAndRun = true;
              });
            }
          } else {
            that.$XyyMessage.error(res.msg);
            that.startReadMsg();
          }
        })
        .catch(err => {
          console.log(err, 'err');
          that.startReadMsg();
        });
    },

    // 留言信息不全的问题解决
    apiliuyanDialogsDetail () {
      liuyanDialogsDetail({ containerid: this.currentContainerID }).then(
        res => {
          if (res.code === 1) {
            // this.containerDatas[this.currentContainerID].messages = [];
            this.containerMassage({
              dialoginfo: res.data,
              messages: res.data.messages
            });
            this.nextContent();
          } else {
            this.$XyyMessage.error(res.msg);
          }
        }
      );
    },
    // 结束会话
    apiCloseTalking (ccId) {
      this.isRunAndRun = false;
      const containerId = ccId || this.currentContainerID;
      this.$confirm('确定结束对话？', '提示', {
        distinguishCancelAndClose: true,
        customClass: 'im-confirm-msg-box',
        cancelButtonClass: 'cancel-button-class',
        confirmButtonText: '会话转至待跟进',
        cancelButtonText: '已处理结束会话',
        type: 'warning'
      })
        .then(() => {
          settodooo({ dialogid: containerId })
            .then(res => {
              if (res.code === 1) {
                /*
                const item = this.userdataIng.filter(
                  t => t.containerId === containerId
                )[0];
                item.state = '访客关闭会话';
                const index = this.userdataIng.indexOf(item);
                this.userdataIng.splice(index, 1);
                this.ingNumber--;
                this.$refs.container_main_leftview.KFInfoData(this.ingNumber);
                this.containerDatas[
                  this.currentContainerID
                ].dialoginfo.dialogType = 3;
                this.userdataWill.splice(0, 0, item);
                */

                this.$XyyMessage.success('成功');
                this.reloadCurrentMessageList(() => {
                  this.chatLeftClickTypeING(1);
                  this.chatLeftClickRow({
                    $index: 0,
                    row: this.userdataWill[0]
                  });
                  this.$refs.container_main_leftview.tosayIng();
                  this.isRunAndRun = true;
                });
              } else {
                this.$XyyMessage.error(res.msg);
                this.isRunAndRun = true;
              }
            })
            .catch(error => {
              console.log(error);
              this.isRunAndRun = true;
            });
        })
        .catch(error => {
          if (error === 'cancel') {
            // if (this.im_reults.indexOf(this.currentContainerID) === -1) {
            //   this.$message.error('请先填写服务小结');
            //   return;
            // }
            closeContainer({ dialogid: containerId })
              .then(res => {
                if (res.code === 1) {
                  /*
                  const msgItem = this.userdataIng.filter(
                    item => item.containerId === containerId
                  )[0];
                  const index = this.userdataIng.indexOf(msgItem);
                  this.userdataIng.splice(index, 1);
                  this.ingNumber--;
                  this.$refs.container_main_leftview.KFInfoData(this.ingNumber);
                  delete this.containerDatas[containerId];
                  

                  // this.userdataIng.length
                  //   ? this.nextContent()
                  //   : this.initContent();
                  */
                  this.$XyyMessage.success('成功');
                  this.reloadCurrentMessageList(() => {
                    //this.userdataIng.length ? this.nextContent() : this.initContent();
                    this.chatLeftClickTypeING(0);
                    this.chatLeftClickRow({
                      $index: 0,
                      row: this.userdataIng[0]
                    });
                    this.$refs.container_main_leftview.tosayIng();
                    this.isRunAndRun = true;
                  });
                } else {
                  this.$XyyMessage.error(res.msg || '接口错误');
                  this.isRunAndRun = true;
                }
              })
              .catch(error => {
                console.log(error, 'closeContainer');
                this.isRunAndRun = true;
              });
          } else {
            console.log(error, 'error');
            this.isRunAndRun = true;
          }
        });
    },

    //结束会话后，重新加载列表
    reloadCurrentMessageList (cb) {
      this.$store.commit('imProperty/SET_IMProperty_containerid', undefined);
      this.$store.commit('imProperty/SET_IMProperty_kfid', undefined);
      this.$store.commit('imProperty/SET_IMProperty_khid', undefined);
      currentMessageList(this.$store.getters.imcreate).then(res => {
        if (res.code !== 1) {
          return;
        }
        if (res.data !== null) {
          this.containerDatas = {}; //消息总数据
          this.chatdata = []; // 消息数据
          this.userdataIng = []; // 会话中
          this.userdataWill = []; // 待跟进
          this.userdataHis = []; // 历史会话
          this.userdataEd = []; // 留言

          res.data.forEach((dataElement, dataIndex) => {
            if (dataElement.messages.length > 0) {
              for (
                let len = dataElement.messages.length, i = len - 1;
                i >= 0;
                i--
              ) {
                //310 订单详情页进入客服聊天界面发送的系统消息
                if (dataElement.messages[i].type == 310) {
                  res.data[dataIndex].messages.splice(i, 1);
                }
              }
            }
          });
          for (let index = 0; index < res.data.length; index++) {
            const element = res.data[index];
            this.containerMassage(element);
          }

          this.userdata = this.userdataIng;

          //服务人数赋值
          this.ingNumber = this.userdataIng.length;
          this.$refs.container_main_leftview.KFInfoData(this.ingNumber);
        }

        //tabs切换到会话中
        //this.chatLeftClickType(this.messageType);
        if (this.$store.getters.imcreate) {
        }

        cb && cb();
      });
    },

    // 添加黑名单
    apiAddblack (value) {
      addblack({
        dialogId: this.currentContainerID,
        remark: value.remark,
        timeLimit: value.time
      }).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.error(res.msg);
        } else {
          this.$XyyMessage.success('添加黑名单成功');
        }
      });
    },
    // 评价  评价消息判断是否需要接口
    apikfAdvice () {
      kfAdvice({ dialogid: this.currentContainerID }).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 进入会话后判断会话类型type是否为501(访客关闭会话), 502(访客超时关闭会话),601(客服关闭会话)
    // 701 用户关闭 重新发起新会话;
    removeChatingToHis (data) {
      // const lists = JSON.parse(JSON.stringify(data));
      // let newArr = this.userdataIng.filter(obj => data.every(item => item.containerId !== obj.containerId));
      if ([501, 502, 601].includes(data.type) && data.dialogid) {
        const filterItem = this.userdataIng.filter(
          t => t.containerId == data.dialogid
        )[0];
        if (filterItem) {
          const index = this.userdataIng.indexOf(filterItem);
          this.userdataIng.splice(index, 1);
          //服务人数赋值
          this.ingNumber = this.userdataIng.length;
          this.$refs.container_main_leftview.KFInfoData(this.ingNumber);
          this.containerDatas[filterItem.containerId].dialoginfo.dialogType = 3;
          this.userdataHis.splice(0, 0, filterItem);
          delete this.containerDatas[filterItem.containerId]; // containerDatas 缓存
        }
        // 判断当前选中id 是否和结束会话的id相等, 相等则清空当前会话内容
        if (data.dialogid === this.currentContainerID) {
          this.initChat();
        } else {
          this.userdataIng.length
            ? this.currentSelectRow(this.userdataIng)
            : this.initChat();
        }
      }
      if (data.type === 701 && data.dialogid) {
        const containerItem = this.containerDatas[data.dialogid];
        containerItem && (containerItem.dialoginfo.dialogType = 1);
        const filterItem = this.userdataWill.filter(
          e => e.containerId === data.dialogid
        )[0];
        const index = this.userdataWill.indexOf(filterItem);
        if (filterItem) {
          this.userdataWill.splice(index, 1);
          // this.containerDatas[filterItem.containerId].dialoginfo.dialogType = 1;
          this.userdataIng.splice(0, 0, filterItem);
          this.messageType = 0;
          filterItem.state = '会话中';
          delete this.containerDatas[data.dialogid];
          if (data.dialogid === this.currentContainerID) {
            this.initChat();
          } else {
            // 如果会话列表点击的行不是用户端要关闭的会话
            this.userdataIng.length
              ? this.currentSelectRow(this.userdataIng)
              : this.initChat();
          }
        }
      }
      // console.log(type, 'type');
      // 解决会话在待跟进情况下, 用户发起会话后会话中也出现对话的问题
    },
    initChat () {
      this.currentContainerID = '0';
      this.chatdata = []; // 消息数据
      // this.messageType = 0;
      this.topvuewType = 'ing'; // 顶部客户信息简介类型
      this.currentUserData = { avatar: '', textData: [] };
    },
    // 结束会话后判断会话中当前列表数据containerId是否与当前选中的containerId是否相等, 然后选中
    currentSelectRow (arr) {
      const index = arr.findIndex(
        d => d.containerId === this.currentContainerID
      );
      // console.log(index, 'indexindexindex');
      this.chatLeftClickRow({ $index: index, row: arr[index] });
      // this.$refs.container_main_leftview.tosayIng();
    },
    // 更新左侧列表数据
    loadLeftMessageList () {
      this.$store.commit('imProperty/SET_IMProperty_containerid', undefined);
      this.$store.commit('imProperty/SET_IMProperty_kfid', undefined);
      this.$store.commit('imProperty/SET_IMProperty_khid', undefined);
      currentMessageList(this.$store.getters.imcreate)
        .then(res => {
          this.isRunAndRun = true;
          if (res.code !== 1) return;
          if (res.data) {
            this.containerDatas = {};
            this.chatdata = []; // 消息数据
            this.userdataIng = []; // 会话中
            this.userdataWill = []; // 待跟进
            this.userdataHis = []; // 历史会话
            this.userdataEd = []; // 留言
            (res.data || []).forEach((dataElement, dataIndex) => {
              if (dataElement.messages.length > 0) {
                for (
                  let len = dataElement.messages.length, i = len - 1;
                  i >= 0;
                  i--
                ) {
                  if (dataElement.messages[i].type == 310) {
                    res.data[dataIndex].messages.splice(i, 1);
                  }
                }
              }
              const obj = res.data[dataIndex];
              this.containerMassage(obj);
            });
            this.userdata = this.userdataIng;
            // this.nextContent();
          }
        })
        .catch(error => {
          this.isRunAndRun = true;
          // this.nextContent();
          console.log(error);
        });
    },
    // 排序
    compare (property) {
      return function (a, b) {
        return a[property] - b[property];
      };
    },

    //处理会话人数超上限等问题
    recomputedChatList (data) {
      //根据接口返回，重新处理会话中，待跟进，留言三个array
      /**
       * 会话中
       */
      //本地数据无，服务返回有,添加本地会话
      //本地数据有,服务返回无,移除本地会话
      let dataIngSpliceLocal = []; //本地需要移除的会话
      this.userdataIng.forEach((itemCurrent, indexCurrent) => {
        if (data.chatingDialogIdList.indexOf(itemCurrent.containerId) === -1) {
          dataIngSpliceLocal.push(itemCurrent.containerId);
        }
      });
      //移除本地会话中多余的数据
      if (dataIngSpliceLocal.length) {
        dataIngSpliceLocal.forEach(itemContainerId => {
          this.userdataIng.forEach((item, index) => {
            if (itemContainerId === item.containerId) {
              this.userdataIng.splice(index, 1);
            }
          });
        });
      }
      //服务人数赋值
      this.ingNumber = this.userdataIng.length;
      this.$refs.container_main_leftview.KFInfoData(this.ingNumber);

      /**
       * 待跟进
       */
      let dataWillSpliceLocal = []; //待跟进，本地需要移除的会话
      this.userdataWill.forEach((itemCurrent, indexCurrent) => {
        if (data.todoDialogIdList.indexOf(itemCurrent.containerId) === -1) {
          dataWillSpliceLocal.push(itemCurrent.containerId);
        }
      });
      //移除本地待跟进中多余的数据
      if (dataWillSpliceLocal.length) {
        dataWillSpliceLocal.forEach(itemContainerId => {
          this.userdataWill.forEach((item, index) => {
            if (itemContainerId === item.containerId) {
              this.userdataWill.splice(index, 1);
            }
          });
        });
      }

      /**
       * 留言
       */
      let dataLeaveMessageSpliceLocal = []; //留言，本地需要移除的会话
      this.userdataEd.forEach((itemCurrent, indexCurrent) => {
        if (
          data.leaveMessageDialogIdList.indexOf(itemCurrent.containerId) === -1
        ) {
          dataLeaveMessageSpliceLocal.push(itemCurrent.containerId);
        }
      });
      //移除本地留言中多余的数据
      if (dataLeaveMessageSpliceLocal.length) {
        dataLeaveMessageSpliceLocal.forEach(itemContainerId => {
          this.userdataEd.forEach((item, index) => {
            if (itemContainerId === item.containerId) {
              this.userdataEd.splice(index, 1);
            }
          });
        });
      }
    }
  },
  beforeRouteLeave (to, from, next) {
    // this.$vnode.parent.componentInstance.cache = {};
    // this.$vnode.parent.componentInstance.key = [];
    next();
  }
};
</script>

<style scoped>
.im_container_mainview {
  background: #f0f2f5;
  display: flex;
  flex-direction: row;
}

.im_container_main_leftview {
  /* width: 200px; */
  height: 100%;
  margin-right: 20px;
}

.im_container_midview {
  display: flex;
  flex-direction: column;
  width: 650px;
  padding-top: 20px;
  padding-bottom: 20px;
  padding-left: 20px;
  /* height: 775px; */
  background-color: #fff;
}

.im_container_rightview {
  background-color: #fff;
  /* border: 1px solid #dcdee3; */
}

.im_container_chat_content {
  background-color: white;
}

.im_container_mainview .el-button--success {
  color: #ffffff;
  background-color: #3b95a8;
  border-color: #3b95a8;
}
</style>
