const state = {
  containerid: undefined,
  kfid: undefined,
  khid: undefined,
  containercreat: '',
  returnResult: {},
  chat: {}, //会话信息
  sheetItem: {} // im新建工单缓存
};
const mutations = {
  SET_IMProperty_containerid: (state, containerid) => {
    state.containerid = containerid;
  },
  SET_IMProperty_kfid: (state, kfid) => {
    state.kfid = kfid;
  },
  SET_IMProperty_khid: (state, khid) => {
    state.khid = khid;
  },
  SET_IMProperty_containercreate: (state, containercreat) => {
    state.containercreat = containercreat;
  },
  SET_RETURN_RESULT: (state, returnResult) => {
    state.returnResult = returnResult;
  },
  SET_IMProperty_chat: (state, chat) => {
    state.chat = chat;
  },
  DEL_IMProperty_chat: state => {
    state.chat = {};
    state.containerid = undefined;
    state.kfid = undefined;
    state.khid = undefined;
  },
  SET_IMSheetItem: (state, sheetItem) => {
    const { name, value } = sheetItem;
    state.sheetItem[name] = value;
  },
  DEL_IMSheetItem(state, payload) {
    if (payload && payload.name) {
      const { name } = payload;
      state.sheetItem[name] = '';
    } else {
      state.sheetItem = {};
    }
  }
};
export default {
  namespaced: true,
  state,
  mutations
};
