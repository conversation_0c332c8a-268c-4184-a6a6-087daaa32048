<template>
  <div>
    <div v-for="(item, index) in getDataList"
         :key="index">
      <template v-if="item.type===101||item.type===102||item.type===201||item.type===203">
        <p :class="item.type===101 || item.type===102?'visitors':'customerService'">
          <!-- {{ item.kefuName }} -->
          {{ item.talkName?item.talkName:item.talkId }}
          {{ item.createTimeStr?item.createTimeStr:item.createtime }}
        </p>
        <span v-html="item.content">{{ item.content }}</span>
      </template>
    </div>
    <div v-if="!getDataList.length">
      <div class="empty-data-container">
        <img :src="emptyDataImgUrl" />
      </div>
    </div>
  </div>
</template>

<script>
import emptyDataImgUrl from '@/assets/common/empty-data-table.png';
export default {
  name: 'Chat',
  props: {
    getDataList: {
      type: Array,
      default: ''
    }
  },
  data () {
    return {
      showOn: true,
      emptyDataImgUrl
    };
  },
  created () {
    this.getInfo();
  },
  methods: {
    getInfo (val) { }
  }
};
</script>

<style scoped lang="scss">
.customerService {
  color: rgba(59, 149, 168, 1);
}
.visitors {
  color: rgba(230, 162, 60, 1);
}

//无数据样式
.empty-data-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 305px;
    height: 219px;
  }
}
</style>
