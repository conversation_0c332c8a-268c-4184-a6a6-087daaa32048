import request from '@/utils/request';
// import { formData } from '@/utils/index';

/* 列表页 */

/* 用户组列表查询 */
export function getMonitorList(data) {
  return request({
    url: 'workorder/monitor/listMonitorWorkorder',
    method: 'get',
    params: data
  });
}
/* 获取总的数据以前*/
export function getTotal(data) {
  return request({
    url: 'workorder/monitor/listMonitorWorkorderCount',
    method: 'get',
    params: data
  });
}
/* 获取总的node数据*/
export function getNodeTotal(data) {
  return request({
    url: 'workorder/monitor/listMonitorNodeTotal',
    method: 'get',
    params: data
  });
}
/* 获取总的状态数据*/
export function getStateTotal(data) {
  return request({
    url: 'workorder/monitor/listMonitorStateTotal',
    method: 'get',
    params: data
  });
}
/* 获取节点下的组数据*/
export function getNodesUser(data) {
  return request({
    url: 'workorder/monitor/listGroupTotalByNodeId',
    method: 'get',
    params: data
  });
}
/* 获取组内用户工单数*/
export function getNodesOrder(data) {
  return request({
    url: 'workorder/monitor/listUserTotalByGroupId',
    method: 'get',
    params: data
  });
}

/* 获取业务监控数据*/
// export function getBusniessData() {
//   return request({
//     url: 'imapi/overall/alldata',
//     method: 'get'
//   });
// }

/* 获取人工接启率数据*/
export function getPersonCount() {
  return request({
    url: 'imapi/overall/connectionRate',
    method: 'get'
  });
}

/* 获取渠道监控数据*/
export function getChannelMonitorinData() {
  return request({
    url: 'imapi/overall/channelMonitoring',
    method: 'get'
  });
}

/* 获取状态人数*/
export function getStateNumber() {
  return request({
    url: 'imapi/overall/stateNumber',
    method: 'get'
  });
}

/* 获取正常状态 坐席组监控情况*/
export function getSeatGroupr() {
  return request({
    url: 'imapi/overall/seatGroup',
    method: 'get'
  });
}

/* 获取预警状态 坐席组监控情况*/
export function getForbiddenSeatGroup() {
  return request({
    url: 'imapi/overall/forbiddenSeatGroup',
    method: 'get'
  });
}

/* 切换状态 传客服id 和 状态值 */
export function changeState(data) {
  return request({
    url: 'imapi/agent/changestate',
    method: 'post',
    data: data
  });
}

/* 用户休假状态查询 */
export function findCsUserState() {
  return request({
    url: '/csUserVacation/findCsUserVacationStatus',
    method: 'get'
  });
}

/* 用户休假更新状态接口 */
export function updateUserState(data) {
  return request({
    url: '/csUserVacation/updateUserVacationStatus?status=' + data,
    method: 'get'
  });
}

/* 用户休假保存接口 */
export function saveUserState(data) {
  return request({
    url: '/csUserVacation/saveUserVacationStatus?vacationNum=' + data,
    method: 'get'
  });
}

/* 查询排队中未处理的会话 */
export function findDialogQueueList(data) {
  return request({
    url: 'imapi/dialogQueue/list',
    method: 'get',
    params: data
  });
}

/* 客服在等待中列表点击发起会话 */
export function dialogQueueStart(data) {
  return request({
    url: 'imapi/dialogQueue/start',
    method: 'post',
    params: data
  });
}

/* 当日创建和当日待领取数*/
export function getOrderMonitorinData() {
  return request({
    url: 'imapi/overall/channelMonitoring',
    method: 'get'
  });
}

/* 人员监控显示配置字段*/
export function getFindPersonnelMonitoringShowFieldData(data) {
  return request({
    url: 'workorderReport/findPersonnelMonitoringShowField?formTypeId=' + data,
    method: 'get'
  });
}

/* 人员监控设置配置字段*/
export function getSettingPersonnelMonitoringShowFieldData(data) {
  return request({
    url: 'workorderReport/settingPersonnelMonitoringShowField',
    method: 'post',
    data: data
  });
}

/* 工单监控整合数据*/
export function getMonitorAllData(data) {
  return request({
    url: 'workorder/monitor/getMonitorAllData',
    method: 'get',
    params: data
  });
}

/* 客户来源所在地*/
export function getCustomerSourceList() {
  return request({
    url: "/workorderReport/getCustomerSourceList",
    method: "get"
  });
}

/**
 *添加人员-所属渠道
 * @param {Object}查询参数
 */
export function getSources(params) {
  return request({
    url: '/imapi/staff/merchants',
    method: 'get',
  });
}

/**
 * 查询有效公告列表
 */
export function findEffectiveKnowledgeNoticeList(){
  return request({
    url:'/imapi/knowledgeNotice/findEffectiveKnowledgeNoticeList',
    method:'get',
  })
}
