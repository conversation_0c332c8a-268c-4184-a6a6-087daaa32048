<template>
  <div class="customer-info-component-container">
    <template v-if="activeComponent">
      <component
        :is="activeComponent"
        ref="activeComponent"
        :prop="{containerid,khid,appId,businessPartCode,channelId,appIdAdapter}"
        @bindRefreshChatInfo="bindRefreshChatInfo"
      ></component>
    </template>
    <template v-else>
      <div class="content-empty">
        <img src="../../../../assets/common/no_custome_info.png" alt="暂无客户信息" />
      </div>
    </template>
  </div>
</template>

<script>
import compInfoYBM from './compInfoYBM';
import compInfoCRM from './compInfoCRM';
import compInfoSAAS from './compInfoSAAS';
import compInfoYKQ from './compInfoYKQ';
export default {
  name: '',
  components: {
    compInfoYBM,
    compInfoCRM,
    compInfoSAAS,
    compInfoYKQ,
  },
  filters: {},
  props: {},
  data() {
    return {
      activeComponent: '', //动态子组件

      //props
      containerid: '', //会话id
      khid: '', //客户id
      appId: '', //appId
      businessPartCode: '', //业务线编码
      channelId: '', //渠道id
      appIdAdapter: '', //匹配渠道
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    /**
     * 重置组件
     */
    reset() {
      if (this.$refs.activeComponent) {
        this.$refs.activeComponent.reset();
      }
    },

    /**
     * 查询初始化信息,ref传递参数
     * 弃用watch
     */
    init({ chat, userId }) {
      this.containerid = chat.id;
      this.khid = userId;
      this.appId = chat.appId;
      this.businessPartCode = chat.businessPartCode;
      this.channelId = chat.channelId;

      //转换的appId
      this.appIdAdapter = this.appIdAdapterFormatter({
        appId: chat.appId,
        businessPartCode: chat.businessPartCode,
        channelId: chat.channelId,
      });

      switch (this.appIdAdapter) {
        case 10: //药帮忙
          this.activeComponent = 'compInfoYBM';
          break;
        case 11: //豆芽
          this.activeComponent = 'compInfoCRM';
          break;
        case 20: //智慧脸PC
        case 21: //智慧脸APP
        case 22: //微信H5
          this.activeComponent = 'compInfoSAAS';
          break;
        case 30: //微信小程序
        case 31: //灵芝问诊APP
        case 32: //您健康APP
          this.activeComponent = 'compInfoYKQ';
          break;
        case 0: //重置组件
          this.activeComponent = '';
          break;
      }
      this.$nextTick(() => {
        if (this.$refs.activeComponent) {
          this.$refs.activeComponent.init();
        }
      });
    },

    /**
     * 区分业务线来源
    
      应用      appId   businessPartCode
      药帮忙    1000    S00009999
      豆芽      1001    S00009999
      微信      1002
      智慧脸    1003    S00009998
      灵芝问诊   1004   S00009997
      宜块钱    1005    S00009997
      您健康    1006    S00009997

      渠道          channelId
      PC              1000
      微信H5           1002
      APP             1003
      客服工作台        1004
      微信小程序        1005
     */
    appIdAdapterFormatter({ businessPartCode, appId, channelId }) {
      let retAppId = 0;
      switch (businessPartCode) {
        //药帮忙
        case 'S00009999': {
          switch (appId) {
            case 1000: //药帮忙
            case 1002: //微信
              retAppId = 10;
              break;
            case 1001:
              retAppId = 11; //豆芽
              break;
          }
          break;
        }
        //智慧脸
        case 'S00009998': {
          switch (appId) {
            case 1003:
              if (channelId === 1000 || channelId === 1004) {
                //智慧脸PC
                retAppId = 20;
              } else if (channelId === 1003) {
                //智慧脸APP
                retAppId = 21;
              }
              break;
            case 1002:
              if (channelId === 1002) {
                //微信H5
                retAppId = 22;
              } else if (channelId === 1005) {
                //微信小程序
                //客户信息显示无
              }
              break;
          }
          break;
        }
        //宜贰叁
        case 'S00009997': {
          switch (appId) {
            case 1002:
              if (channelId === 1005 || channelId === 1004) {
                //微信小程序
                retAppId = 30;
              }
              break;
            case 1004: //灵芝问诊APP
              retAppId = 31;
              break;
            case 1006: //您健康APP
              retAppId = 32;
              break;
          }
          break;
        }
      }
      return retAppId;
    },

    /**
     * 绑定药店后刷新会话信息
     */
    bindRefreshChatInfo(params) {
      this.$emit('refreshChatInfo', params);
    },
  },
};
</script>

<style lang="scss" scoped>
.customer-info-component-container {
  width: 100%;
  height: 100%;

  .content-empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    img {
      width: 200px;
    }
  }
}
</style>