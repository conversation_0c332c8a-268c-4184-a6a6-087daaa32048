<template>
  <div class="im_container_view">
    <div class="im_container_header">
      <el-dropdown placement="bottom-end" trigger="click" size="medium" @command="handleCommand">
        <div class="im_container_header_avatar">
          <img :src="ownerAvatarUrl" class="im_container_header_img" />
          <div :style="{backgroundColor: brigeColor}" class="littleBrage"></div>
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="(item,index) in stateAlertData"
            :key="index"
            :command="item.statusCode"
          >{{ item.name }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <div class="header_time">{{ offlineStatus }} {{ onLineTimes }}</div>
      <div class="header_twotext">
        <div>{{ userNumber }}</div>
        <div>排队人数:{{ sortNumber }}</div>
      </div>
    </div>
    <!-- 搜索 -->
    <div class="im_container_search">
      <el-input
        v-model="searchContent"
        class="my-autocomplete-search"
        placeholder="请输入用户名称/手机号搜索"
        size="small"
        clearable
        @keyup.enter.native="searchSlove"
        @focus="searchFocus"
      >
        <i slot="suffix" class="el-input__icon el-icon-search" @click="handleIconClick"></i>
      </el-input>
      <div v-if="searchFlag" class="im_search_top">
        <ul>
          <li @click="handleSelect(restaurants.id, restaurants.realName)">
            <div class="img">
              <img src="../../../../../static/user_kh.png" alt />
            </div>
            <div class="name">
              <span>{{ restaurants.realName }}</span>
            </div>
            <div class="name">
              <span>{{ restaurants.mobile }}</span>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <!-- <div v-loading="loading"> -->
    <!-- 内容 -->
    <div class="im_container_body">
      <!-- 会话选择按钮 [会话中, 待跟进, 历史, 留言] -->
      <div class="im_container_buttons">
        <div v-for="(item, index) in buttonData" :key="index" class="button_items">
          <el-button
            :class="[selectNumber === index ? 'select_button' : 'no_select_button']"
            type="text"
            class="select_button"
            size="mini"
            @click="selectItem(item, index)"
          >
            <i :class="classes[index]"></i>
            <span class="select_button_suspended">{{ item }}</span>
          </el-button>
          <div class="border-line" :class="{'active': selectNumber === index}"></div>
        </div>
      </div>

      <!-- 选择顺序 -->
      <div
        v-if="selectNumber === 0 || selectNumber === 2"
        :style="{borderBottom: selectNumber !== 2?'0.5px solid #e4e4eb':'', marginTop:selectNumber !== 2?'':'10px', marginBottom:selectNumber !== 2?'':'10px'}"
        class="im_container_chooseview"
      >
        <el-input
          v-if="selectNumber === 2"
          :size="'mini'"
          :maxlength="100"
          v-model="searchStr"
          placeholder="请输入访客名称"
          @change="searchAction"
        >
          <el-button slot="append" icon="el-icon-search" @click="searchAction()"></el-button>
        </el-input>
        <el-dropdown
          v-else
          placement="right"
          trigger="click"
          size="medium"
          @command="handleCommandpx"
        >
          <el-button type="text" icon="el-icon-s-operation"></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(item,index) in paixuAlertData"
              :key="index"
              :command="item.value"
            >{{ item.name }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>

      <!-- 消息列表 -->
      <div
        ref="lefttable"
        :style="{height:tableHeight-200+'px'}"
        class="im_container_leftview_usertable"
      >
        <div>
          <el-table
            :data="userDatas"
            :show-header="false"
            :border="false"
            :row-class-name="'im_container_leftview_table_row'"
            highlight-current-row
          >
            <el-table-column>
              <template slot-scope="scope">
                <div
                  :class="[scope.row.select === true ? 'im_container_leftview_userlist_select' : 'im_container_leftview_userlist']"
                  @click="rowclickAction(scope)"
                >
                  <img :src="scope.row.avatar" class="im_container_leftview_useravatar" />
                  <div
                    :class="selectNumber === 1?'im_container_leftview_userlist_right_1':'im_container_leftview_userlist_right'"
                  >
                    <div class="im_container_leftview_userlist_nametime">
                      <div
                        class="im_container_leftview_userlist_nametimeval"
                        style="display:none;"
                      >{{ scope.row.name }}</div>
                      <el-tooltip :content="scope.row.name" placement="top">
                        <div
                          class="im_container_leftview_userlist_name"
                        >{{ getSubStr(scope.row.name) }}</div>
                      </el-tooltip>
                      <div
                        class="im_container_leftview_userlist_time"
                      >{{ timechange(selectNumber === 1?scope.row.starttime:scope.row.lastMsgTime) }}</div>
                    </div>
                    <div
                      v-if="selectNumber !== 1"
                      class="im_container_leftview_userlist_content"
                    >{{ getMessageText(scope.row.content) }}</div>
                    <div class="im_container_leftview_userlist_state">{{ scope.row.state }}</div>
                  </div>
                  <div
                    v-if="scope.row.unreadNum!==0"
                    class="im_container_leftview_badge"
                  >{{ scope.row.unreadNum>99?'99+':scope.row.unreadNum }}</div>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div v-if="bottonLoading" class="loading">
            <div>加载完毕</div>
          </div>
        </div>
      </div>
    </div>
    <!-- </div> -->
  </div>
</template>

<script>
import utils from '@/utils/form';
import {
  kfStatesKeyChange,
  YYYYMMHHToTimeStr,
  TimeToYDHMStr
} from '../im_container_tool/message_tool';
// 接口
import {
  Kefustatus,
  kfChangeState,
  kfCurrentState,
  kfdialogQueueSize,
  searchKeyword,
  startConversation,
  startChart,
  // kfUserInfo,
  // getCurrentState,
  currentMessageList
} from '@/api/im_view/index';

export default {
  props: {
    paixuAlertData: {
      type: Array,
      default: () => {
        return [
          { name: '按对话接入时间排序', value: '0' },
          { name: '按任意方新消息排序', value: '1' },
          { name: '按访客方新消息排序', value: '2' }
        ];
      }
    },
    userDataArray: {
      type: Array
    },
    tableHeight: {
      type: Number
    },
    ingNumber: {
      type: Number
    },
    selectNumber: {
      type: Number,
      default: 0
    },
    kefuInfoData: {
      type: Object
    },
    ownerAvatarUrl: {
      type: String
    }
  },
  data() {
    return {
      userDatas: [],
      lastclick: null,
      activeName: '',
      buttonData: ['会话中', '待跟进', '历史会话', '留言'],
      classes: [
        'el-icon-chat-dot-round',
        'el-icon-time',
        'el-icon-date',
        'el-icon-edit-outline'
      ],
      sortType: '', // 排序方式
      alertListHidden: true, // 排序弹窗是否隐藏
      stateListHidden: true, // 状态弹窗是否隐藏
      stateAlertData: [],
      brigeColor: '#999',
      onlineStatus: 0,
      searchStr: '',
      tabledom: {},
      onLineTimes: ' 0:0:0',
      offlineStatus: '离线',
      sortNumber: 0,
      userNumber: '服务人数 0/0',
      bottonLoading: false,
      restaurants: {},
      searchContent: '', // 会话搜索值
      hoverFlag: true, // 会话svg图标
      searchFlag: false, // 搜索下拉判断,
      searchVal: {
        $index: 0,
        row: {}
      },
      chatStatus: {
        1: '在线',
        2: '培训',
        3: '忙碌',
        4: '就餐',
        5: '离线',
        7: '小休',
        8: '会议'
      },
      timerId: '', //计时器标识
      editContent: {},
      currentContainerID: ''
    };
  },
  watch: {
    userDataArray(newvalue, oldvalue) {
      this.userDatas = newvalue;
    },
    deep: true
  },
  created() {
    this.getStateApi();
  },
  mounted() {
    // this.initKfState();
    const that = this;

    this.$refs.lefttable.onscroll = function(res) {
      if (
        res.target.scrollTop + res.target.clientHeight ===
        res.target.scrollHeight
      ) {
        setTimeout(function() {
          if (that.selectNumber !== 2 && that.userDatas.length > 7) {
            that.pushToBottom(true);
            return;
          }
          res.target.scrollTop -= 2;
          that.$emit('im_container_left_scroll');
        }, 100);
      }
    };

    //2020.7.7,rl,计算计时器
    this.timerId = setInterval(() => {
      this.KFInfoTimer();
    }, 1000);
  },
  methods: {
    initKfState() {
      const status = this.kefuInfoData && this.kefuInfoData.status;
      this.brigeColor = this.swichColor(status);
      this.offlineStatus = this.chatStatus[status];
    },
    pushToBottom(type) {
      this.bottonLoading = type;
    },
    timechange(str) {
      return TimeToYDHMStr(str);
    },
    bgcolorChange() {},
    updateKfState(command) {
      // changeReason: 1 变更原因 1.坐席手动, 2.管理员强制;
      kfChangeState({ state: command, changeReason: 1 }).then(res => {
        if (res.code === 1) {
          this.kefuInfoData.status = res.data;
          this.brigeColor = this.swichColor(res.data);
          this.offlineStatus = this.chatStatus[res.data];

          // 切换状态后更新状态时间
          this.$nextTick(() =>
            this.$parent.apikfUserInfo('kfChangeState', () => {
              //重置计时器
              clearInterval(this.timerId);
              this.timerId = null;
              this.onLineTimes = ' 0:0:0';
              this.timerId = setInterval(() => {
                this.KFInfoTimer();
              }, 1000);
            })
          );
        } else {
          this.$XyyMessage.error(res.msg || '接口超时');
        }
      });
    },
    // 状态切换
    handleCommand(command) {
      if (this.kefuInfoData.status === command) return;
      if (this.sortNumber === 0) {
        // 排队人数0的时候执行
        this.updateKfState(command);
        return false;
      } else {
        const stateElement = this.stateAlertData.filter(
          item => item.statusCode === command
        );
        const stateStr = stateElement[0].name;
        const tishiStr =
          '当前排队' +
          this.sortNumber +
          '人，确定切换为' +
          stateStr +
          '状态吗？';
        this.$confirm(tishiStr, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.updateKfState(command);
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消切换'
            });
          });
      }
    },
    handleCommandpx(command) {
      // const arr = utils.deepCopy(this.userDatas);
      if (command === '0') {
        this.userDatas.sort(this.compare('starttime'));
      } else if (command === '1') {
        this.userDatas.sort(this.compare('lastMsgTime'));
      } else {
        this.userDatas.sort(this.compare('fklastTime'));
      }
      // this.emits(this, 'send', 'leftview_data_tosort', arr);
    },
    compare(property) {
      return function(a, b) {
        var value1 = YYYYMMHHToTimeStr(a[property]);
        var value2 = YYYYMMHHToTimeStr(b[property]);
        // console.log(value1, value2);
        return value2 - value1;
      };
    },
    // 选择列表类型
    selectItem(item, index) {
      this.$emit('im_container_left_clicktype', index, this.searchStr);
    },

    getSubStr(str) {
      if (!str || str.length <= 4) {
        return str;
      }
      var subStr1 = str.substr(0, 1);
      var subStr2 = str.substr(str.length - 1, 1);
      var subStr = subStr1 + '...' + subStr2;
      return subStr;
    },

    // 列表跳转过来
    tosayIng() {
      this.$nextTick(() => {
        this.userDatas = this.userDatas.map(item => {
          item.select = false;
          return item;
        });
        const item = this.userDatas[0];
        if (item) item.select = true;
      });
    },

    // 点击列表
    rowclickAction(val) {
      this.userDatas = this.userDatas.map(item => {
        item.select = false;
        return item;
      });
      const item = this.userDatas[val.$index];
      if (item) item.select = true;
      this.$emit('im_container_left_clickrow', val);
    },

    // 获取状态列表接口
    getStateApi() {
      Kefustatus().then(res => {
        this.stateAlertData = kfStatesKeyChange(res.data);
      });
    },

    // 获取客服状态接口
    kfCurrentStateApi() {
      kfCurrentState().then(res => {
        if (res.code === 1) {
          this.brigeColor = this.swichColor(res.data);
        } else {
          this.$message(res.msg);
        }
      });
    },

    swichColor(type) {
      const colors = [
        '#67C23A',
        '#67C23A',
        '#60B1FF',
        '#F08557',
        '#F0AC4E',
        '#999',
        '',
        '#C6C6C6',
        '#6098FF'
      ];
      return colors[type];
      // switch (type) {
      //   case 1:
      //     return "#67C23A";
      //     break;
      //   case 2:
      //     return "#60B1FF";
      //     break;
      //   case 3:
      //     return "#F08557";
      //     break;
      //   case 4:
      //     return "#F0AC4E";
      //     break;
      //   case 5:
      //     return "#999999";
      //     break;
      //   case 6:
      //     return "";
      //     break;
      //   case 7:
      //     return "#C6C6C6";
      //     break;
      //   case 8:
      //     return "#6098FF";
      //     break;

      //   default:
      //     break;
      // }
    },

    // 客服信息的处理
    //计算服务人数
    KFInfoData(ingNumber) {
      if (this.kefuInfoData === {}) {
        return;
      }
      this.userNumber =
        '服务人数:' + ingNumber + '/' + this.kefuInfoData.maxdialog || 0;
      const status = this.kefuInfoData.status || 5;
      this.brigeColor = this.swichColor(status);
      this.offlineStatus = this.chatStatus[status];
    },

    //计算计时器
    KFInfoTimer() {
      if (this.kefuInfoData === {}) {
        return;
      }
      var currentTime =
        parseInt(Date.parse(new Date()) / 1000) -
        parseInt(this.kefuInfoData.statustime / 1000);
      if (currentTime < 0) {
        currentTime = 0;
      }
      var timeStr =
        parseInt(currentTime / 3600) +
        ':' +
        parseInt((currentTime % 3600) / 60) +
        ':' +
        (currentTime % 60);
      // let stateElement = this.stateAlertData.filter(
      //   item => item.statusCode === this.kefuInfoData.status
      // );
      // let stateStr = stateElement[0].name;
      // this.onLineTimes = stateStr + " " + timeStr;
      // var statu = this.stateAlertData[this.kefuInfoData.state].statusName;
      // this.onLineTimes = statu + ' ' + timeStr;
      this.onLineTimes = ' ' + timeStr;
    },

    // 获取排队数量
    KFSortNumber() {
      kfdialogQueueSize({
        t: Math.ceil(Math.random() * 1000000).toString()
      }).then(res => {
        if (res.code !== 1) {
          this.$message(res.msg);
          return;
        }
        this.sortNumber = res.data;
      });
    },

    // 当前服务人数
    searchAction(val) {
      this.$emit('im_container_left_searchFunction', this.searchStr);
    },

    // 获取文本消息中的文字
    getMessageText(value) {
      if (!value || value === '') {
        return '';
      }
      if (value.indexOf('<img') != -1) {
        return '图片消息';
      }
      var con = value.replace(/\s*/g, ''); // 去掉空格
      var res = con.replace(/<[^>]+>/g, ''); // 去掉所有的html标记
      var res1 = res.replace(/↵/g, ''); // 去掉所有的↵符号
      var text = res1.replace(/[\r\n]/g, ''); // 去掉回车换行
      // var text = value.slice(start + 1, end);
      return text;
    },

    // 搜索数据
    loadAll(val) {
      let errorMsg = '';
      debugger;
      searchKeyword(val)
        .then(response => {
          if (response.code === 1) {
            this.searchFlag = true;
            this.restaurants = response.data;
          } else {
            this.searchFlag = false;
            errorMsg = response.msg;
            this.$message(response.msg);
          }
        })
        .catch(e => {
          this.searchFlag = false;
          this.$message(errorMsg);
        });
    },
    // 搜索选中事件
    handleSelect(id, name) {
      this.searchFlag = false;
      this.selectItem('会话中', 0);
      setTimeout(e => {
        this.searchVal.$index = 0;
        var searchArrtwo = [];
        searchArrtwo = this.userDatas;
        for (var j = 0; j < searchArrtwo.length; j++) {
          if (name == searchArrtwo[j].name) {
            this.hoverFlag = false;
            searchArrtwo.unshift(searchArrtwo[j]);
            searchArrtwo.splice(j + 1, 1);
            break;
          } else {
            this.hoverFlag = true;
          }
        }
        this.userDatas = searchArrtwo;
        var searchArr = [];
        searchArr = this.userDatas;
        for (var i = 0; i < searchArr.length; i++) {
          if (name == searchArr[i].name) {
            this.searchVal.$index = i;
            this.searchVal.row = this.userDatas[i];
            break;
          } else {
            this.searchVal.$index = 0;
          }
        }
        startConversation(id)
          .then(response => {
            if (response.code === 1) {
              this.$emit('im_container_left_searchMessage', '');
              this.rowclickAction(this.searchVal);
              setTimeout(e => {
                // if (this.hoverFlag) {
                document
                  .querySelectorAll('.el-table .el-table__row .cell>div')[0]
                  .click();
                // }
              }, 1000);
            } else {
              this.$message(response.msg);
            }
          })
          .catch(e => {
            this.$message(e);
          });
      }, 1000);
    },
    // 搜索点击按钮事件
    handleIconClick() {
      this.loadAll(this.searchContent);
    },
    // 搜索回车事件
    searchSlove() {
      this.loadAll(this.searchContent);
    },
    // 搜索点击输入框事件
    searchFocus() {
      this.searchFlag = false;
    }
  }
};
</script>

<style lang='scss'>
.im_container_view .loading {
  width: 100%;
  height: 40px;
  position: relative;
  overflow: hidden;
  text-align: center;
  margin: 5px 0;
  color: #999;
  font-size: 13px;
}
.im_container_view .el-table tr td .cell {
  height: auto;
  padding-left: 0px;
  padding-right: 0px;
}

.im_container_view .el-table tr td {
  height: auto;
  padding: 0;
  border-bottom: none;
}

.im_container_view .el-table {
  border: 0px;
}

.im_container_view {
  /* width: 200px; */
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fff;
  padding-bottom: 20px;
}
/* 搜索 */

.im_container_search {
  text-align: center;
  margin-bottom: 10px;
  position: relative;
  .my-autocomplete-search {
    width: 190px;
  }
  .el-icon-search {
    float: right;
  }
  .el-input__inner {
    font-size: 12px;
    padding-left: 8px;
  }
  .im_search_top {
    position: absolute;
    top: 46px;
    left: 0;
    box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.13);
    border: 1px solid rgba(240, 242, 245, 1);
    z-index: 99;
    background-color: #fff;
    width: 100%;
    ul {
      max-height: 200px;
      overflow: auto;
      li {
        line-height: normal;
        padding: 10px 15px 10px 13px;
        &:hover {
          background-color: rgba(218, 233, 236, 1);
          cursor: pointer;
        }
        .im_line {
          width: 98%;
          height: 1px;
          background-color: #e4e4eb;
          margin: 0 auto;
          margin-top: 7px;
        }
        .img {
          width: 32px;
          height: 32px;
          float: left;
          border-radius: 50%;
          margin-right: 8px;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .name {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          font-size: 12px;
          text-align: left;
          color: #292933;
        }
        .addr {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          font-size: 12px;
          color: #292933;
          text-align: left;
        }

        .highlighted .addr {
          color: #ddd;
        }
      }
    }
  }
}
.im_search_top ul::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
.im_search_top ul::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #f5f7fa;
}
.im_search_top ul::-webkit-scrollbar-track {
  /*滚动条里面轨道*/

  background: #fff;
}
.button_items {
  width: 25%;
  .el-button {
    position: relative;
  }
  .select_button_suspended {
    position: absolute;
    top: 33px;
    left: -15px;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    padding: 2px 4px;
    font-size: 10px;
    display: none;
    margin-left: 0;
    z-index: 999;
  }
  &:hover {
    .select_button_suspended {
      display: block;
    }
    .el-button {
      span {
        i {
          color: #0097ac;
        }
      }
    }
  }
  &:nth-of-type(3) {
    .select_button_suspended {
      left: -23px;
    }
  }
  &:nth-of-type(4) {
    .select_button_suspended {
      left: -12px;
    }
  }
  // .select_button {
  //   .select_button_suspended {
  //     display: block;
  //   }
  // }
  .el-button {
    i {
      font-size: 16px;
    }
    svg {
      font-size: 16px;
    }
  }
}
</style>

<style scoped lang="scss">
.im_container_header {
  width: 200px;
  height: 147px;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* background-color: red; */
}
.im_container_header_avatar {
  width: 50px;
  height: 50px;
  margin-top: 20px;
}
.im_container_header_img {
  width: 50px;
  height: 50px;
  /* margin-top: 20px; */
  border-radius: 25px;
}

.header_time {
  font-size: 12px;
  color: #9b9bab;
  margin-top: 10px;
}

.header_twotext {
  width: 80%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  color: #9b9bab;
  margin-top: 20px;
}

.im_container_body {
  width: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.im_container_buttons {
  width: 95%;
  height: 35px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  background-color: #f5f7fa;
  padding-left: 5px;
  padding-right: 5px;
  align-items: flex-end;
}

.select_button {
  font-size: 8px;
}

.no_select_button {
  font-size: 8px;
  color: #909399;
}

.border-line {
  width: 40%;
  height: 2px;
}
.border-line.active {
  background-color: #3b95a8;
}

.button_items {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.im_container_chooseview {
  width: 100%;
  height: 35px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  padding-right: 5px;
  padding-left: 5px;
  align-items: center;
}

.im_container_leftview_userlist {
  display: flex;
  flex-direction: row;
  width: 100%;
  background-color: white;
  padding-left: 10px;
  padding-right: 10px;
}
.im_container_leftview_userlist.active {
  background-color: #dae9ec;
}
.im_container_leftview_userlist_select {
  display: flex;
  flex-direction: row;
  width: 100%;
  background-color: #dae9ec;
  padding-left: 10px;
  padding-right: 10px;
}

.im_container_leftview_useravatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-top: 14px;
  margin-right: 5px;
}

.im_container_leftview_userlist_right {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100px;
  border-bottom: 0.5px solid #e4e4eb;
}

.im_container_leftview_userlist_right_1 {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 80px;
  border-bottom: 0.5px solid #e4e4eb;
}

.im_container_leftview_userlist_nametime {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 8px;
  padding-right: 5px;
}

.im_container_leftview_userlist_name {
  font-size: 12px;
  width: 50px;
  color: #292933;
  line-height: 20px;
  /* height: 20px; */
  /* display: flex;
  flex-direction: row;
  align-items: center; */
  /* white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; */
  /* word-break: break-all; */
}

.im_container_leftview_userlist_time {
  display: flex;
  flex-direction: row;
  align-items: center;
  color: #909399;
  padding-right: 5px;
  height: 20px;
  font-size: 12px;
}

.im_container_leftview_userlist_content {
  /* display: flex;
  flex-direction: row;
  align-items: center; */
  font-size: 12px;
  color: #575766;
  margin-top: 5px;
  width: 100px;
  line-height: 20px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.im_container_leftview_userlist_content p {
  font-size: 12px;
  color: #575766;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.im_container_leftview_userlist_state {
  height: 30px;
  display: flex;
  margin-top: 10px;
  flex-direction: row;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.im_container_leftview_usertable {
  width: 100%;
  /* height: 540px; */
  background-color: white;
  overflow: auto;
}

.im_container_leftview_table_row {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100px;
}

.im_container_leftview_table_row_1 {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 60px;
}

.im_container_leftview_badge {
  position: absolute;
  right: 10px;
  top: 40%;
  color: white;
  background-color: red;
  font-size: 12px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 6px;
  padding-right: 6px;
  border-radius: 9px;
}

.im_container_header_avatar .littleBrage {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  position: relative;
  left: 40px;
  bottom: 10px;
}

.im_container_chooseview .im_container_chooseview_alertlist {
  position: absolute;
  left: 220px;
  top: 170px;
  z-index: 60;
}
.im_container_view .im_container_chooseview_alert {
  position: absolute;
  left: 160px;
  top: 180px;
  z-index: 60;
}

.im_container_leftview_usertable::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
.im_container_leftview_usertable::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #f5f7fa;
}
.im_container_leftview_usertable::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  background: #fff;
}
</style>
