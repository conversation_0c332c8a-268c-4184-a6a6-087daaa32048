<template>
  <div>
    <div v-if="info">
      <el-row
        v-for="(item,index) in systemArr"
        :key="index"
        :class="{'empty-row':clearEmpty && isEmptyVal(item)}"
        class="instrLs"
      >
        <!-- 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件 -->
        <span
          v-if="!clearEmpty || !isEmptyVal(item)"
          v-show="getAuthTypeStatus(item)"
          class="lsItem"
        >
          <span class="lsTtl">{{ item.fieldText +":" }}</span>
          <span
            v-if="item.fieldType===0||item.fieldType===1||item.fieldType===5||item.fieldType===7||item.fieldType===8||item.fieldType===9"
            class="lsCon"
          >
            <!-- {{ item.fieldSingleValue }} -->
            <template v-if="item.fieldType===1 || item.fieldType===7">
              <pre>{{ item.fieldSingleValue }}</pre>
            </template>
            <template v-else>{{ item.fieldSingleValue }}</template>
          </span>
          <span v-else-if="item.fieldType===2" class="lsCon">{{ item.optionName }}</span>
          <span
            v-else-if="item.fieldType===3&&item.fieldText!=='优先级'"
            class="lsCon"            
          >{{ item.optionName }}</span>
          <!-- 优先级 -->
          <span
            v-else-if="item.fieldType===3&&item.fieldText==='优先级'"
            :class="item.optionName === '普通'?'priority0':item.optionName === '紧急'?'priority1':'priority2'"
            class="lsCon"
          >{{ item.optionName === '普通'?'普通':item.optionName === '紧急'?'紧急':'加急' }}</span>
          <!-- 多选级联 -->
          <span
            v-else-if="item.fieldType===4||item.fieldType===6"
            :key="index"
            class="lsCon"
          >{{ handleMultip(item.fieldMultipleValue) }}</span>
          <!-- 附件 -->
          <span
            v-else-if="item.fieldType===10&&item.fieldMultipleValue&&item.fieldMultipleValue.length>0"
            class="lsCon"
          >
            <el-row>
              <fileprev
                v-for="(i,index) in handleAppendix(item.fieldMultipleValue)"
                :file="i"
                :percent="100"
                :key="index"
              ></fileprev>
            </el-row>
          </span>
        </span>
      </el-row>
      <p class="line"></p>
      <layout :lay-datas="layDatas" :col-datas="customeArr">
        <template slot="col" slot-scope="{ arr,row,col }">
          <div 
            class="instrLs template-col"
            :class="{ 'empty-col': !(arr[row][col]&&(!clearEmpty || !isEmptyVal(arr[row][col])) && getAuthTypeStatus(arr[row][col])) }"
          >
            <span
              v-if="arr[row][col]&&(!clearEmpty || !isEmptyVal(arr[row][col])) && getAuthTypeStatus(arr[row][col])"
              class="lsItem"
            >
              <span class="lsTtl">{{ arr[row][col].fieldText +":" }}</span>
              <span
                v-if="getAuthTypeStatus(arr[row][col]) && (arr[row][col].fieldType===0||arr[row][col].fieldType===1||arr[row][col].fieldType===5||arr[row][col].fieldType===7||arr[row][col].fieldType===8||arr[row][col].fieldType===9)"
                class="lsCon"
              >
                <!-- {{ arr[row][col].fieldSingleValue }} -->
                <template v-if="arr[row][col].fieldType===1  || arr[row][col].fieldType===7">
                  <pre>{{ arr[row][col].fieldSingleValue }}</pre>
                </template>
                <template v-else>{{ arr[row][col].fieldSingleValue }}</template>
              </span>
              <span
                v-else-if="arr[row][col].fieldType===2 && getAuthTypeStatus(arr[row][col])"
                class="lsCon"
              >{{ arr[row][col].optionName }}</span>
              <span
                v-else-if="arr[row][col].fieldType===3&&arr[row][col].fieldText!=='优先级' && getAuthTypeStatus(arr[row][col])"
                class="lsCon"
              >{{ arr[row][col].optionName }}</span>
              <!-- 优先级 -->
              <span
                v-else-if="arr[row][col].fieldType===3&&arr[row][col].fieldText==='优先级' && getAuthTypeStatus(arr[row][col])"
                :class="arr[row][col].optionName === '普通'?'priority0':arr[row][col].optionName === '紧急'?'priority1':'priority2'"
                class="lsCon"
              >{{ arr[row][col].optionName === '普通'?'普通':item.optionName === '紧急'?'紧急':'加急' }}</span>
              <!-- 多选级联 -->
              <span
                v-else-if="(arr[row][col].fieldType===4||arr[row][col].fieldType===6) && getAuthTypeStatus(arr[row][col])"
                :key="arr[row][col].fieldCode"
                class="lsCon"
              >{{ handleMultip(arr[row][col].fieldMultipleValue) }}</span>
              <!-- 附件 -->
              <span
                v-else-if="arr[row][col].fieldType===10&&arr[row][col].fieldMultipleValue&&arr[row][col].fieldMultipleValue.length>0 && getAuthTypeStatus(arr[row][col])"
                class="lsCon"
              >
                <el-row>
                  <fileprev
                    v-for="(i,index) in handleAppendix(arr[row][col].fieldMultipleValue)"
                    :file="i"
                    :percent="100"
                    :key="index"
                  ></fileprev>
                </el-row>
              </span>
            </span>
          </div>
        </template>
      </layout>
    </div>
    <div v-else>
      <el-row
        v-for="(item,index) in dataInfo"
        :key="index"
        :class="{'empty-row':clearEmpty && isEmptyVal(item)}"
        class="instrLs"
      >
        <!-- 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件 -->
        <span v-if="!clearEmpty || !isEmptyVal(item)" class="lsItem">
          <span class="lsTtl">{{ item.fieldText +":" }}</span>
          <span
            v-if="item.fieldType===0||item.fieldType===1||item.fieldType===5||item.fieldType===7||item.fieldType===8||item.fieldType===9"
            class="lsCon"
          >
            <!-- {{ item.fieldSingleValue }} -->
            <template v-if="item.fieldType===1 || item.fieldType===7">
              <pre>{{ item.fieldSingleValue }}</pre>
            </template>
            <template v-else>{{ item.fieldSingleValue }}</template>
          </span>
          <span v-else-if="item.fieldType===2" class="lsCon">{{ item.optionName }}</span>
          <span
            v-else-if="item.fieldType===3&&item.fieldText!=='优先级'"
            class="lsCon"
          >{{ item.optionName }}</span>
          <!-- 优先级 -->
          <span
            v-else-if="item.fieldType===3&&item.fieldText==='优先级'"
            :class="item.optionName === '普通'?'priority0':item.optionName === '紧急'?'priority1':'priority2'"
            class="lsCon"
          >{{ item.optionName === '普通'?'普通':item.optionName === '紧急'?'紧急':'加急' }}</span>
          <!-- 多选级联 -->
          <span
            v-else-if="item.fieldType===4||item.fieldType===6"
            :key="index"
            class="lsCon"
          >{{ handleMultip(item.fieldMultipleValue) }}</span>
          <!-- 附件 -->
          <span
            v-else-if="item.fieldType===10&&item.fieldMultipleValue&&item.fieldMultipleValue.length>0"
            class="lsCon"
          >
            <el-row>
              <fileprev
                v-for="(i,index) in handleAppendix(item.fieldMultipleValue)"
                :file="i"
                :percent="100"
                :key="index"
              ></fileprev>
            </el-row>
          </span>
        </span>
      </el-row>
    </div>
  </div>
</template>

<script>
import layout from '@/components/templates/layout';

import fileprev from '~/Fields/file-preview';
import { initFileDatas } from '@/utils/tools.js';
export default {
  name: 'Draft',
  components: {
    fileprev,
    layout,
  },
  props: {
    dataInfo: {
      type: Array,
      default: function () {
        return [];
      },
    },
    layDatas: {
      type: String,
      default: function () {
        return '';
      },
    },
    clearEmpty: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      systemArr: [],
      customeArr: [],
    };
  },
  watch: {
    dataInfo: function (val) {
      this.splitArr(val);
    },
  },
  created() {
      console.log('dataInfo:', this.dataInfo);
      console.log('info prop:', this.info);
      this.splitArr(this.dataInfo);
      console.log('systemArr:', this.systemArr);
      console.log('customeArr:', this.customeArr);
    },
  methods: {
    splitArr(val) {
      if (val && val.length > 0) {
        this.systemArr.splice(0, this.systemArr.length);
        this.customeArr.splice(0, this.customeArr.length);
        val.forEach((item) => {
          if (item.systemFieldFlag == 0) {
            this.systemArr.push(item);
          } else {
            this.customeArr.push(item);
          }
        });
      }
    },
    /**
     * 是否是空值
     */
    isEmptyVal(item) {
      let empty = true;
      if ([0, 1, 5, 7, 8, 9].includes(item.fieldType)) {
        empty = !item.fieldSingleValue || !String(item.fieldSingleValue).trim();
      } else if ([2, 3].includes(item.fieldType)) {
        empty = !item.optionName;
      } else if ([4, 6, 10].includes(item.fieldType)) {
        empty =
          !item.fieldMultipleValue ||
          !Object.keys(item.fieldMultipleValue).length;
      }
      return empty;
    },
    // 附件数据处理
    handleAppendix(item) {
      return initFileDatas(item);
    },
    handleMultip(item) {
      if (typeof item === 'string') {
        item = JSON.parse(item.replace(/(&quot;)/g, '"'));
      }
      return Object.values(item).join('/');
    },
    // 获取显隐状态
    getAuthTypeStatus(item, a, b) {
      const condition = this.conditionAA(item);
      return condition !== 0;
    },
    // 是否只读？
    conditionAA(obj) {
      // authType: 0, // 权限类型 0-隐藏 1-只读 2-编辑
      // authTypeCondition: 2, // 满足条件的权限类型 0-隐藏 1-只读 2-编辑

      const that = this;
      let authTypeCondition = 1;
      if (!obj.conditionList) {
        obj.conditionList = [];
      }
      if (obj.conditionList[0]) {
        this.dataInfo.forEach(function (item, index) {
          if (item.fieldCode === obj.conditionList[0].conditionFieldCode) {
            let optionsValue;
            if (
              item.optionSettings &&
              typeof item.optionSettings === 'string'
            ) {
              const rest = item.optionSettings.replace(/(&quot;)/g, '"');
              optionsValue = JSON.parse(rest);
            } else {
              optionsValue = item.fieldSingleValue;
            }
            // fieldType: 1, // 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
            let symbolSign = false;
            switch (item.fieldType) {
              case 0:
              case 1:
              case 5:
              case 8:
              case 9:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  item.defaultValue
                );
                break;
              case 2:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  optionsValue
                );
                break;
              case 3:
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  optionsValue
                );
                break;
              // eslint-disable-next-line no-case-declarations
              case 4:
                let value = [...optionsValue.checkedOptions.optionsValue];
                value = value.sort();
                symbolSign = that.getConditionSymbol(
                  obj.conditionList[0],
                  value.join()
                );
                break;
            }
            if (symbolSign) {
              // 满足条件的权限类型 0-隐藏 1-只读 2-编辑
              authTypeCondition = obj.authTypeCondition;
            }
          }
        });
      }

      return authTypeCondition;
    },
    getConditionSymbol(obj, defaultValue) {
      // defaultValue:输入值 valueFirst:条件值
      // 条件符号 0-等于 1-大于 2-小于 3-大于等于 4-小于等于 5-介于 6-无
      const value = obj.valueFirst
        .split(',')
        .sort((a, b) => a - b)
        .join(',');
      if (Object.prototype.toString.call(defaultValue) === '[object Array]') {
        defaultValue = defaultValue.join(',');
      }
      // console.log('obj,defaultValue', obj, defaultValue);

      switch (obj.conditionSymbol) {
        case 0:
          return defaultValue == value;
        case 1:
          return defaultValue != value;
        case 2:
          if (!defaultValue) {
            return false;
          } else {
            return defaultValue.indexOf(value) !== -1;
          }

        // eslint-disable-next-line no-case-declarations
        case 3:
          const valArr = value.split(',');
          for (let i = 0; i <= valArr.length; i++) {
            if (String(defaultValue).indexOf(valArr[i]) != -1) {
              return true;
            }
          }
          return false;
        case 4:
          return defaultValue === value;
        case 5:
          return defaultValue !== value;
      }
    },
  },
};
</script>
<style lang='scss' scoped>
.line {
  height: 0;
  border-top: 1px dashed #e4e4eb;
}
.instrLs {
  margin-bottom: 16px;
  &.empty-row {
    margin-bottom: 0;
  }
  &.empty-col {
    margin-bottom: 0;
  }
  .lsItem {
    color: #292933;
    text-align: right;

    // textarea 保存格式
    display: flex;
    // white-space: nowrap !important;
    justify-content: flex-start;
    align-items: flex-start;
  }
  .lsTtl {
    flex-shrink: 0;
  }
  .lsCon {
    color: #575766;
    word-wrap: break-word;
    margin-left: 5px;
    text-align: left;

    // textarea 保存格式
    pre {
      margin-top: 0;
      margin-bottom: 0;
      white-space: pre-wrap !important;
      font-family: inherit !important;
      word-break: break-all;
    }
  }
  .priority0 {
    width: auto;
    padding: 3px 4px;
    color: #909399;
    background: #ededf0;
  }
  .priority1 {
    width: auto;
    padding: 3px 4px;
    color: #fb720c;
    background: #fce5d4;
  }
  .priority2 {
    width: auto;
    padding: 3px 4px;
    color: #ff3024;
    background: #fbe6e6;
  }
}
</style>
