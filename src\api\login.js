import request from '@/utils/request';
import { formData } from '@/utils/index';

export function getInfo() {
  return request({
    url: '/system/index',
    method: 'get'
  });
}

export function logout() {
  return request({
    url: '/logout',
    method: 'get'
  });
}

export function loginChangeState() {
  // 将切换的im api中
  return request({
    url: '/imapi/agent/loginChangeState',
    method: 'get'
  });
}

/* pc更改业务线 */
export function saveLineOfBusinessSwitchingChange(businessPartCode) {
  return request({
    url: '/system/saveLineOfBusinessSwitchingChange',
    method: 'post',
    data: businessPartCode
  });
}
