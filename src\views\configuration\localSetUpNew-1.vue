<template>
  <div class="localSetUpNew">
    <xyy-list-page>
      <template slot="body">
        <el-form ref="form" :model="form" label-width="100px">
          <el-form-item label="组合名称">
            <el-input v-model="form.combinaName"></el-input>
          </el-form-item>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="form.groupDes" maxlength="100" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="包含省市">
            <el-button v-for="(item, index) in form.checkProvince">{{ item }}<span class="el-icon-close" @click="deleteDataP(index)"></span></el-button>
            <el-button v-for="(item, index) in form.checkCityList">{{ item }}<span class="el-icon-close" @click="deleteData(item,index)"></span></el-button>
            <el-button @click="AddProvCities"><span class="el-icon-plus">添加</span></el-button>
            <!-- 添加弹出框-开始-->
            <el-dialog title="选择省市" :visible.sync="dialogTableVisible">
              <div class="levelPanel">
                <!--<el-cascader-panel ref="cascaderAddr" :options="options" emitPath='false' :props="props"  @change="chooseCascader" v-model="form.ap"></el-cascader-panel>-->
                <el-tree
                  :data="data"
                  node-key="id"
                  show-checkbox
                  ref="tree"
                  :accordion="accordion"
                  :default-expanded-keys = "nideKey"
                  :props="defaultProps">
                </el-tree>
              </div>
              <div slot="footer" class="dialog-footer">
                <el-button @click="dialogTableVisible = false">取 消</el-button>
                <el-button type="primary" @click="makeSure">确 定</el-button>
              </div>
            </el-dialog>
            <!-- 添加弹出框结束-->
          </el-form-item>
          <el-form-item label="对应员工组">
            <el-select v-model="form.groups" multiple placeholder="请接入员工组">
              <el-option
                v-for="item in employeGroups"
                :key="item.id"
                :label="item.groupName"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handerSave">保存</el-button>
          </el-form-item>
        </el-form>
      </template>
    </xyy-list-page>
  </div>
</template>

<script>
import XyyListPage from '../../components/xyy/xyy-list-page/index';
import XyyButton from "../../components/xyy/xyy-button/index";
import { provProvince, provCity, newEditor, EmployeeGroups } from '@/api/configuration/RegionalMenu';
export default {
  name: 'localSetUpNew-1',
  components: { XyyButton, XyyListPage },
  data() {
    return {
      data: [], // zTree的数据
      defaultProps: {
        children: 'nextLevelArea',
        label: 'areaName'
      },
      nideKey: [], // 默认展开的节点的 key 的数组
      accordion: true, // 控制子节点不同时显示
      selectedChildAll: [], // 获取所有选中的节点
      halfSelectedChildAll: [], // 获取半选数据的节点
      selectedAll: [], // 获取所有选中后过滤的节点
      form: {
        combinaName: '', // 组合名称
        groupDes: '', // 备注
        groups: '', // 对应员工组
        checkProvince: [], // 选择的省
        checkCityList: [], // 选择的市
        checkCity: [] // 每次选择的市存储的值
      },
      employeGroups: [],
      dialogTableVisible: false, // 控制弹出框
    }
  },
  created() {
    this.getValue()
  },
  methods: {
    // 点击添加事件
    AddProvCities() {
      let that = this
      this.dialogTableVisible = true
      // 获取省市
    },
    handerSave(){

    },
    // 确定按钮
    makeSure() {
      var that = this;
      this.checkCity = []
      this.dialogTableVisible = false
      this.selectedChildAll = this.$refs.tree.getCheckedNodes()
      this.halfSelectedChildAll = this.$refs.tree.getHalfCheckedNodes()
      // 所有节点里 去掉半选节点即可
      this.selectedChildAll.forEach(function(item, index) {
        that.halfSelectedChildAll.forEach(function(itemL, indexL) {
          if (item.areaName !== itemL.areaName) {
            that.selectedAll.push(item)
          }
        })
      })

    },
    getValue() {
      //   获取员工组
      EmployeeGroups().then(response => {
        this.employeGroups = response.data
      }).catch(function(error) {
        console.log(error);
      });
      this.valueTo = this.$route.query.obj
      // 获取省市
      provProvince().then(response => {
        this.data = response.data
        this.nideKey.push(this.data[0].id)
      }).catch(function(error) {
        console.log(error);
      });

    }
  }
}
</script>

<style lang="scss">
  .localSetUpNew{
    .el-dialog{
      width: 400px;
      height: 602px;
    }
    .page-body{
      display: flex;
      justify-content: center;
      /*align-items: center;*/
      .levelPanel{
        @extend .page-body
      }
      .el-dialog__body{
        height: 76%;
        overflow-y: auto;
        .levelPanel{
          justify-content: left;
          .el-tree-node__children{
            position: absolute;
            top: 0;
            left: 150%;
            background: #fff;
          }
        }
      }
      .el-form{
        width: 50%;
        .el-select{
          width: 100%;
        }
      }
    }

  }
</style>
