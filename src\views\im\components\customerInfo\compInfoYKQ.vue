<template>
  <div class="comp-info-YKQ-component-container">
    <div class="search-customer-content">
      <!-- 微信小程序 -->
      <template v-if="prop.appIdAdapter === 30">
        <template v-if="customerInfo.id">
          <div class="detail-section">
            <div class="item-info whole-line">
              <span class="info-field">微信昵称：</span>
              <span class="info-value">{{ customerInfo.nickname }}</span>
            </div>
            <div class="item-info whole-line">
              <span class="info-field">手机号：</span>
              <span class="info-value">{{ customerInfo.mobile }}</span>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="content-empty">
            <img src="../../../../assets/common/no_custome_info.png" alt="暂无客户信息" />
          </div>
        </template>
      </template>
      <!-- 灵芝问诊APP -->
      <template v-else-if="prop.appIdAdapter === 31">
        <template v-if="customerInfo.id">
          <!-- 药店名 -->
          <!-- 
          <div class="header-content">
            <span class="shop-name">{{ customerInfo.realName }}</span>
          </div> 
          -->
          <!-- 详情信息 -->
          <div class="detail-section">
            <div class="item-info whole-line">
              <span class="info-field">药店名称：</span>
              <span class="info-value">{{ customerInfo.realName }}</span>
            </div>
            <div class="item-info whole-line">
              <span class="info-field">地址：</span>
              <span class="info-value">{{ customerInfo.registAddress }}</span>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="content-empty">
            <img src="../../../../assets/common/no_custome_info.png" alt="暂无客户信息" />
          </div>
        </template>
      </template>
      <!-- 您健康APP -->
      <template v-else-if="prop.appIdAdapter === 32">
        <template v-if="customerInfo.id">
          <div class="detail-section">
            <div class="item-info whole-line">
              <span class="info-field">用户昵称：</span>
              <span class="info-value">{{ customerInfo.nickname }}</span>
            </div>
            <div class="item-info whole-line">
              <span class="info-field">手机号：</span>
              <span class="info-value">{{ customerInfo.mobile }}</span>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="content-empty">
            <img src="../../../../assets/common/no_custome_info.png" alt="暂无客户信息" />
          </div>
        </template>
      </template>
    </div>
  </div>
</template>

<script>
import { getCustomeDetails } from '@/api/im_view/customeInfo';
export default {
  name: 'compInfoYKQ',
  components: {},
  filters: {},
  props: {
    prop: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  data() {
    return {
      customerInfo: {}, //客户信息
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    /**
     * 重置组件
     */
    reset() {
      Object.assign(this.$data, this.$options.data()); // 重置data数据
    },

    /**
     * 初始化数据
     */
    init() {
      this.reset();
      this.queryCustomerInfo();
    },

    /**
     * 查询客户信息
     */
    queryCustomerInfo() {
      const loading = this.$loading({
        target: document.querySelector('.im-config'),
        lock: true,
        spinner: 'el-icon-loading',
        text: '加载中...',
        background: 'rgba(255,255,255, 0.8)',
      });
      getCustomeDetails({
        id: this.prop.khid,
        appId: this.prop.appId,
        businessPartCode: this.prop.businessPartCode,
        channelId: this.prop.channelId,
      })
        .then((resp) => {
          if (resp.code === 1) {
            if (resp.data && resp.data.id) {
              this.customerInfo = resp.data;
            }
          } else {
            this.$XyyMessage.error(resp.msg);
          }
        })
        .finally(() => {
          loading.close();
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.comp-info-YKQ-component-container {
  width: 100%;
  height: 100%;

  .search-customer-content {
    width: 100%;
    height: 100%;
    padding: 20px;
    position: relative;

    .detail-section {
      width: 100%;
      padding: 20px 0 0;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      flex-wrap: wrap;

      .item-info {
        width: 49%;
        line-height: 20px;
        margin-bottom: 10px;
        flex-grow: 0;
        flex-shrink: 0;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;

        &:nth-child(even) {
          margin-left: 2%;
        }

        .info-field {
          width: 70px;
          color: #292933;
          font-size: 14px;
          font-weight: 500;
        }

        .info-value {
          width: calc(100% - 70px);
          color: #575766;
          font-size: 14px;
          font-weight: 500;
        }

        .light {
          color: #67c23a;
        }

        &.whole-line {
          width: 100%;
          &:nth-child(even) {
            margin-left: 0;
          }
        }
      }
    }

    .content-empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

      img {
        width: 200px;
      }
    }
  }
}
</style>