import utils from '../../../../utils/form';
import store from '../../../../store';
/**
 * https://wiki.int.ybm100.com/pages/viewpage.action?pageId=383230752
 * 1.客户往客服发送消息
 * 101 普通消息
 * 102 临时消息
 * 103 客户自动回复问题
 * 2.客服往客户发送消息
 * 201 普通消息
 * 202 对话转移提示
 * 203 特殊欢迎语
 * 204 客服自动回复答案
 * 205 客服消息撤回
 * 3.系统发往客户消息
 * 301 . 在排队
 * 302 . 没有消息
 * 303 无客服
 * 304 操作成功（比如发送消息等）
 * 305 加入对话提示
 * 306 长时间没有对话，是否曳继续等待
 * 307 顾客选择继续等待
 * 310 订单详情页进入客服聊天界面发送的系统消息
 * 331 客服无应答超时自动回复消息
 * 332 用户应答超时自动回复消息
 * 4.系统发往客服消息
 * 401 未登录
 * 402 客服处于非在线状态
 * 403 新对话
 * 404 无任何类型的新消息
 * 405 无权限
 * 406 操作成功
 * 407 非法输入
 * 408 对话转移请求
 * 409 转移的对话
 * 410 接受方接受转移请求
 * 411 接受方拒绝转移请求
 * 412 有新的通知，content中的内容为 未读消息的数目
 * 413 对话被接管通知
 * 414 被踢出登陆
 * 415 对话转移成功后的话术
 * 416 对话转待跟进
 * 417 用户新留言
 * 502 访客超时关闭对话
 * 5.客户关闭
 * 501 客户关闭
 * 6.客服关闭（不要求评价）
 * 601 客服关闭
 * 602 客服超时关闭
 * 603 系统24H关闭
 * 8.客服要求评价
 */

// 会话状态
export function containerState(type) {
  switch (type) {
    case 501: {
      return '访客关闭会话';
    }
    case 502: {
      return '访客超时关闭会话';
    }
    case 601: {
      return '客服关闭会话';
    }
    default: {
      return '会话中';
    }
  }
}

// 消息加入总数据
export function containerType(containerdata, data, isUnread) {
  let newData;
  let oldData;
  if (containerdata[data.dialoginfo.id] === undefined) {
    newData = data;
    newData.dialoginfo['unreadNum'] = data.dialoginfo.unRead;
    // containerdata[data.dialoginfo.id] = data;
    // containerdata[data.dialoginfo.id].dialoginfo['unreadNum'] = data.messages.length;
  } else {
    oldData = containerdata[data.dialoginfo.id];
    var unreadNum = containerdata[data.dialoginfo.id].dialoginfo.unreadNum;
    const unreadArr = data.messages.filter(item => item.direction === 1);
    var newdialog = data.dialoginfo;
    newdialog['unreadNum'] =
      isUnread !== true ? (unreadNum += unreadArr.length) : (unreadNum = 0);
    var oldmss = containerdata[data.dialoginfo.id].messages;
    var marr = oldmss.concat(data.messages);
    newData = {
      dialoginfo: newdialog,
      messages: marr
    };
    // containerdata[data.dialoginfo.id].dialoginfo = newdialog;
    // containerdata[data.dialoginfo.id].messages = marr;
  }
  return {
    oldmsg: oldData,
    newmsg: newData
  };
}

// 用户的拆分（更好用的）
export function userDatasSec(
  newMsg,
  oldMsg,
  userdataIng,
  userdataWill,
  userdataEd
) {
  var data = {
    avatar:
      newMsg.dialoginfo.customHeadImg === ''
        ? '/pc/static/user_kh.png'
        : newMsg.dialoginfo.customHeadImg,
    name:
      newMsg.dialoginfo.nickname === null
        ? newMsg.dialoginfo.customNickName
        : newMsg.dialoginfo.nickname,
    fklastTime: newMsg.dialoginfo.customLastMessageTime,
    creatTime: newMsg.dialoginfo.createTime,
    starttime: newMsg.dialoginfo.starttime,
    lastMsgTime: newMsg.dialoginfo.lastMsgTime,
    joinQueueTime: newMsg.dialoginfo.joinQueueTime,
    content:
      newMsg.dialoginfo.lastMsg === null ? '' : newMsg.dialoginfo.lastMsg,
    state:
      newMsg.messages.length === 0
        ? historyState(newMsg.dialoginfo.whoclose)
        : containerState(newMsg.messages[newMsg.messages.length - 1].type),
    type:
      newMsg.messages.length === 0
        ? newMsg.dialoginfo.whoclose
        : newMsg.messages[newMsg.messages.length - 1].type,
    select: false,
    containerId: newMsg.dialoginfo.id,
    kfid: newMsg.dialoginfo.kefuid,
    khid: newMsg.dialoginfo.uid,
    unreadNum: newMsg.dialoginfo.unreadNum,
    referer: newMsg.dialoginfo.referer,
    channelId: newMsg.dialoginfo.channelId,
    wxName: newMsg.dialoginfo.customNickName,
    adress: newMsg.dialoginfo.ipprovince + newMsg.dialoginfo.ipcity,
    accepttime: YYYYMMHHToTimeStr(newMsg.dialoginfo.accepttime)
  };

  if (!oldMsg) {
    switch (newMsg.dialoginfo.dialogType) {
      case 1:
        userdataIng.splice(0, 0, data);
        break;
      case 3:
        data.state = '访客关闭会话';
        userdataWill.splice(0, 0, data);
        break;
      case 2:
        if (data.state !== '会话中') {
          data.state = '访客关闭会话';
        }
        // debugger
        userdataEd.splice(0, 0, data);
        break;

      default:
        break;
    }
    return;
  }
  switch (oldMsg.dialoginfo.dialogType) {
    case 1:
      {
        const user = userdataIng.filter(
          item => item.containerId === oldMsg.dialoginfo.id
        );
        if (user.length !== 0) {
          data.select = user[0].select;
          const index = userdataIng.indexOf(user[0]);
          userdataIng.splice(index, 1);
          if (newMsg.dialoginfo.dialogType === 1) {
            userdataIng.splice(0, 0, data);
          }
        } else {
          userdataIng.push(data);
        }
      }
      break;

    case 2:
      {
        // debugger
        const user = userdataEd.filter(
          item => item.containerId === oldMsg.dialoginfo.id
        );
        if (data.state !== '会话中') {
          data.state = '访客关闭会话';
        }
        if (user.length !== 0) {
          data.select = user[0].select;
          const index = userdataEd.indexOf(user[0]);
          userdataEd.splice(index, 1);
          if (newMsg.dialoginfo.dialogType === 2) {
            userdataEd.splice(0, 0, data);
          }
        } else {
          userdataEd.push(data);
        }
      }
      break;

    case 3:
      {
        data.state = '访客关闭会话';
        const user = userdataWill.filter(
          item => item.containerId === oldMsg.dialoginfo.id
        );
        if (user.length !== 0) {
          data.select = user[0].select;
          const index = userdataWill.indexOf(user[0]);
          userdataWill.splice(index, 1);
          if (newMsg.dialoginfo.dialogType === 3) {
            userdataWill.splice(0, 0, data);
          }
        } else {
          userdataWill.push(data);
        }
      }
      break;

    default:
      break;
  }
}

// 消息处理聊天方向
export function containerMessage(data) {
  if (data && data.messages) {
    for (let index = 0; index < data.messages.length; index++) {
      const element = data.messages[index];
      if (
        element.type === 101 ||
        element.type === 102 ||
        element.type === 103
      ) {
        element.direction = 1;
        element.msgtype = 1;
        element.ctime = element.createTime;
      } else if (element.type === 201 || element.type === 204) {
        element.direction = 2;
        element.msgtype = 1;
        element.ctime = element.createTime;
      } else if (element.type === 205) {
        element.direction = 2;
        element.msgtype = 'recall';
        element.ctime = element.createTime;
        if (store.getters.kfid == element.kefuId) {
          element.content = '你撤回了一条消息';
        }
      } else if (element.type === 203 || element.type === 202) {
        element.direction = 2;
        element.msgtype = 'system';
        element.ctime = element.createTime;
      } else if (element.type === 301) {
        element.direction = 2;
        element.msgtype = 'system';
        element.ctime = element.createTime;
      } else if (element.type === 305) {
        element.direction = 2;
        element.msgtype = 'system';
        element.ctime = element.createTime;
      } else if (element.type === 309) {
        element.direction = 2;
        element.msgtype = 'system';
        element.ctime = element.createTime;
      } else if (element.type === 331) {
        element.direction = 2;
        element.msgtype = 'system';
        element.ctime = element.createTime;
      } else if (element.type === 332) {
        element.direction = 2;
        element.msgtype = 'system';
        element.ctime = element.createTime;
      } else if (
        element.type === 413 ||
        element.type === 415 ||
        element.type === 411 ||
        element.type === 410
      ) {
        element.direction = 2;
        element.msgtype = 'system';
        element.ctime = element.createTime;
      } else if (
        element.type === 416 ||
        element.type === 403 ||
        element.type === 418
      ) {
        element.direction = 2;
        element.msgtype = 'system';
        element.ctime = element.createTime;
      } else if (element.type === 501) {
        element.direction = 1;
        element.msgtype = 'endmessage';
        element.ctime = element.createTime;
      } else if (element.type === 502) {
        element.direction = 1;
        element.msgtype = 'endmessage';
        element.ctime = element.createTime;
      } else if (
        element.type === 601 ||
        element.type === 602 ||
        element.type === 603
      ) {
        element.direction = 2;
        element.msgtype = 'endmessage';
        element.ctime = element.createTime;
      } else {
        console.log('消息类型异常！', element ? element.type : '空对象');
      }
    }
  } else {
    console.log('消息异常！');
  }
  return data;
}

// 历史对话列表处理
export function historyUserData(data) {
  var newdata = {
    avatar:
      data.customHeadImg === '' ? '/pc/static/user_kh.png' : data.customHeadImg,
    name: data.nickname === null ? data.customNickName : data.nickname,
    kfname: data.kefuName,
    fklastTime: data.customLastMessageTime,
    lastMsgTime: data.endtime,
    creatTime: data.createTime,
    joinQueueTime: data.joinQueueTime,
    content: data.lastMsg,
    state: historyState(data.whoclose),
    select: false,
    containerId: data.id,
    kfid: data.kefuid,
    khid: data.uid,
    unreadNum: 0,
    referer: data.referer,
    channelId: data.channelId,
    wxName: data.customNickName,
    adress: data.ipprovince + data.ipcity
  };

  return newdata;
}

// 历史列表的状态
export function historyState(type) {
  switch (type) {
    case -1:
      return '访客关闭会话';
    case -2:
      return '访客超时关闭';
    default:
      return '客服关闭会话';
  }
}

// 历史消息处理
export function historyMessageData(messagedata) {
  var messageArr = [];
  for (let index = 0; index < messagedata.length; index++) {
    const element = messagedata[index];
    if (element.type === 101 || element.type === 102 || element.type === 103) {
      element.direction = 1;
      element.msgtype = 1;
      element.ctime = element.createTime;
    } else if (element.type === 201 || element.type === 204) {
      element.direction = 2;
      element.msgtype = 1;
      element.ctime = element.createTime;
    } else if (element.type === 203) {
      element.direction = 2;
      element.msgtype = 'system';
      element.ctime = element.createTime;
    } else if (element.type === 301) {
      element.direction = 2;
      element.msgtype = 'system';
      element.ctime = element.createTime;
    } else if (element.type === 305) {
      element.direction = 2;
      element.msgtype = 'system';
      element.ctime = element.createTime;
    } else if (element.type === 309) {
      element.direction = 2;
      element.msgtype = 'system';
      element.ctime = element.createTime;
    } else if (element.type === 331) {
      element.direction = 2;
      element.msgtype = 'system';
      element.ctime = element.createTime;
    } else if (element.type === 332) {
      element.direction = 2;
      element.msgtype = 'system';
      element.ctime = element.createTime;
    } else if (element.type === 415) {
      element.direction = 2;
      element.msgtype = 1;
      element.ctime = element.createTime;
    } else if (element.type === 416) {
      element.direction = 2;
      element.msgtype = 1;
      element.ctime = element.createTime;
    } else if (element.type === 501) {
      element.direction = 1;
      element.msgtype = 'endmessage';
      element.ctime = element.createTime;
    } else if (element.type === 502) {
      element.direction = 1;
      element.msgtype = 'endmessage';
      element.ctime = element.createTime;
    } else if (element.type === 601) {
      element.direction = 2;
      element.msgtype = 'endmessage';
      element.ctime = element.createTime;
    }

    messageArr.push(element);
  }
  return messageArr;
}

// 图片压缩
export function drawimg(fileObj, callback) {
  try {
    const image = new Image();
    image.src = URL.createObjectURL(fileObj);
    image.onload = function () {
      const that = this;
      // 默认按比例压缩
      let w = that.width;
      let h = that.height;
      const scale = w / h;
      w = fileObj.width || w;
      h = fileObj.height || w / scale;
      let quality = 1.0; // 默认图片质量为0.7
      // 生成canvas
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      // 创建属性节点
      const anw = document.createAttribute('width');
      anw.nodeValue = w;
      const anh = document.createAttribute('height');
      anh.nodeValue = h;
      canvas.setAttributeNode(anw);
      canvas.setAttributeNode(anh);
      ctx.drawImage(that, 0, 0, w, h);
      // 图像质量
      if (fileObj.quality && fileObj.quality <= 1 && fileObj.quality > 0) {
        quality = fileObj.quality;
      }
      // quality值越小，所绘制出的图像越模糊
      const data = canvas.toDataURL('image/jpeg', quality);
      // 压缩完成执行回调
      const newFile = convertBase64UrlToBlob(data);
      callback(newFile);
    };
  } catch (e) {
    console.log('压缩失败!');
  }
}

function convertBase64UrlToBlob(urlData) {
  const bytes = window.atob(urlData.split(',')[1]); // 去掉url的头，并转换为byte
  // 处理异常,将ascii码小于0的转换为大于0
  const ab = new ArrayBuffer(bytes.length);
  const ia = new Uint8Array(ab);
  for (let i = 0; i < bytes.length; i++) {
    ia[i] = bytes.charCodeAt(i);
  }
  return new Blob([ab], {
    type: 'image/png'
  });
}

// 增加或修改Key值
export function kfStatesKeyChange(kfData) {
  for (let index = 0; index < kfData.length; index++) {
    const element = kfData[index];
    element['name'] = element.statusName;
  }
  return kfData;
}

// 顶部工具栏的数据处理
export function topData(data) {
  const time = YYYYMMHHToTimeStr(data.dialoginfo.accepttime);
  var currentTime = parseInt(Date.parse(new Date()) / 1000) - time;
  var timeStr =
    parseInt(currentTime / 3600) +
    '小时' +
    parseInt((currentTime % 3600) / 60) +
    '分' +
    (currentTime % 60) +
    '秒';

  return {
    avatar:
      data.dialoginfo.customHeadImg === ''
        ? '/pc/static/user_kh.png'
        : data.dialoginfo.customHeadImg,
    textData: [
      '客户昵称：' +
      (data.dialoginfo.customNickName || data.dialoginfo.realName),
      '来源地区：' + data.dialoginfo.ipprovince + data.dialoginfo.ipcity,
      '来源渠道：' + data.dialoginfo.referer,
      '对话次数：' + (data.dialoginfo.dialogCount || '0') + '次',
      '当天对话数:' + (data.dialoginfo.todayDialogCount || '0') + '次',
      '等候接入用时:' + utils.secToHMS(data.dialoginfo.queueUseTime),
      '对话时长：' + timeStr
    ]
  };
}

// 时间戳转换时1间
export function timeStrToYYYYMMHH(timeStr) {
  var date = new Date(timeStr + 8 * 3600 * 1000); // 增加8小时
  return date
    .toJSON()
    .substr(0, 19)
    .replace('T', ' ');
}

// 时间转换时间戳
export function YYYYMMHHToTimeStr(timeStr) {
  var date = parseInt(Date.parse(timeStr) / 1000);
  return date;
}

// 时间格式转换
export function TimeToYDHMStr(timeStr) {
  if (!timeStr) {
    return '未知';
  }
  const timeDRe = parseInt(Date.parse(timeStr));
  var date = new Date(timeDRe);
  const mon = date.getMonth() + 1; // 获取月份(0-11,0代表1月,用的时候记得加上1)
  const day = date.getDate(); // 获取日(1-31)
  const hours = date.getHours(); // 获取小时数(0-23)
  let min = date.getMinutes(); // 获取分钟数(0-59)
  if (min.toString().length === 1) {
    min = '0' + min;
  }
  const str = mon + '-' + day + ' ' + hours + ':' + min;
  // alert(str);
  return str;
}
