<template>
  <div>
    <el-form :model="previewModal" :rules="rules" label-width="100px" class="single-attr-form">
      <!-- 单行预览 -->
      <template v-if="keys == 0">
        <el-form-item
          :label="previewModal.fieldLable"
          :prop="previewModal.required==1?'defaultValue' : ''"
          class="single"
        >
          <el-input :placeholder="previewModal.placeholder" v-model="previewModal.defaultValue" />
        </el-form-item>
      </template>
      <!-- 多行输入 -->
      <template v-else-if="keys == 1">
        <el-form-item
          :label="previewModal.fieldLable"
          :prop="previewModal.required==1? 'defaultValue' : ''"
          class="single"
        >
          <el-input
            :placeholder="previewModal.placeholder"
            v-model="previewModal.defaultValue"
            type="textarea"
          />
        </el-form-item>
      </template>
      <!-- 下拉列表 -->
      <template v-else-if="keys == 2">
        <el-form-item
          :label="previewModal.fieldLable"
          :prop="previewModal.required==1? 'defaultValue' : ''"
          class="single"
        >
          <el-select v-model="previewModal.defaultValue" :placeholder="previewModal.placeholder">
            <el-option
              v-for="(item, index) in previewModal.optionSettings"
              :key="index"
              :label="item.fieldLable"
              :value="item"
            />
          </el-select>
        </el-form-item>
      </template>
      <!-- 单选 -->
      <template v-else-if="keys == 3">
        <el-form-item
          :label="previewModal.fieldLable"
          :prop="previewModal.required==1? 'defaultValue' : ''"
          class="single"
        >
          <el-radio-group v-model="previewModal.defaultValue">
            <el-radio
              v-for="(item, index) in previewModal.optionSettings"
              :key="index"
              :label="item"
            ></el-radio>
          </el-radio-group>
        </el-form-item>
      </template>
      <!-- 多选 -->
      <template v-else-if="keys == 4">
        <el-form-item
          :label="previewModal.fieldLable"
          :prop="previewModal.required==1? 'defaultValue' : ''"
          class="single"
        >
          <el-checkbox-group v-model="previewModal.defaultValue">
            <el-checkbox
              v-for="(item,index) in previewModal.optionSettings"
              :label="item"
              :key="index"
            ></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script>
// 0:单行输入,1:多行输入,2:下拉,3:单选,4:多选--
export default {
  name: 'DynimicTemplate',
  props: ['preview', 'keys'],
  data: function() {
    return {
      previewModal: {},
      rules: {
        defaultValue: [
          { required: true, message: '请输入必填项', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    preview: {
      handler: function(newVal, oldVal) {
        if (!oldVal || !oldVal.id) return;
        const json = JSON.parse(JSON.stringify(newVal));
        Object.keys(newVal).forEach(item => {
          const isNext =
            JSON.stringify(newVal[item]) !=
              JSON.stringify(this.previewModal[item]) && item != 'defaultValue';
          if (isNext) {
            this.previewModal[item] = json[item];
          }
        });
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.previewModal = JSON.parse(JSON.stringify(this.preview));
    if (this.previewModal.fieldType == 4) {
      this.previewModal.defaultValue = [];
    }
  }
};
</script>

<style scoped>
</style>
