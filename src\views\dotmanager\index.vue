<template>
  <xyy-list-page>
    <template slot="header">
      <el-dropdown placement="bottom"
                   trigger="click"
                   @command="handleCommand">
        <span class="el-dropdown-link">
          <xyy-button icon-class="btn-add"
                      class-name="btn-add-icon">新建节点</xyy-button>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :command="{ title: '发起节点', type: '0' }">
            <el-popover placement="right"
                        trigger="hover">
              <div>发起节点：即工单发起的节点，对应新建工单</div>
              <div slot="reference">发起节点</div>
            </el-popover>
          </el-dropdown-item>
          <el-dropdown-item :command="{ title: '处理节点', type: '1' }">
            <el-popover placement="right"
                        trigger="hover">
              <div>处理节点：即工单处理的节点，对应处理回复工单</div>
              <div slot="reference">处理节点</div>
            </el-popover>
          </el-dropdown-item>
          <el-dropdown-item :command="{ title: '抄送节点', type: '2' }">
            <el-popover placement="right"
                        trigger="hover">
              <div>抄送节点：流程中非处理职责，被抄送的用户可查看工单</div>
              <div slot="reference">抄送节点</div>
            </el-popover>
          </el-dropdown-item>
          <el-dropdown-item :command="{ title: '关闭节点', type: '3' }">
            <el-popover placement="right"
                        trigger="hover">
              <div>关闭节点：即工单关闭的节点，对应关闭工单</div>
              <div slot="reference">关闭节点</div>
            </el-popover>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <div class="search-box">
        <el-input v-model="searchStr"
                  placeholder="请输入节点名称"
                  maxlength="20"
                  @input="changeString" />
        <el-button type="primary"
                   @click="checkTimer(searchAction())">搜索</el-button>
      </div>
      <!-- <chosseTypeAlert ref="chosseAlert" :dialogVisible="true" @commondialog="alertValue"></chosseTypeAlert> -->
    </template>
    <template slot="body">
      <xyy-table v-loading="loading"
                 :data="list"
                 :list-query="listQuery"
                 :col="col"
                 :operation="operation"
                 element-loading-text="loading"
                 element-loading-spinner="el-icon-loading"
                 @get-data="ApiNodePoolGet"
                 @operation-click="operationClick">
        <template slot="description"
                  slot-scope="{col}">
          <el-table-column :key="col.index"
                           :prop="col.index"
                           :label="col.name"
                           show-overflow-tooltip />
        </template>
        <template slot="gmtModified"
                  slot-scope="{col}">
          <el-table-column :key="col.index"
                           :prop="col.index"
                           :label="col.name"
                           :formatter="getFormatDate"
                           show-overflow-tooltip />
        </template>
        <template slot="status"
                  slot-scope="{col}">
          <el-table-column :key="col.index"
                           :label="col.name"
                           :width="col.width">
            <template slot-scope="{row}">
              <span :class="'status-tip '+(row.status === 1?'open':'close')"></span>
              {{ row.status === 1?'启用':'禁用' }}
            </template>
          </el-table-column>
        </template>
      </xyy-table>
    </template>
  </xyy-list-page>
</template>

<script>
import { getFormList, changeStatus, delForm, copyForm } from '@/api/formManage';
import {
  nodePoolGet,
  nodePoolPost,
  nodePoolDelete,
  copyNodePoolByNodePoolId,
  disableNodePool
} from '@/api/nodemanager';
import chosseTypeAlert from './components/commonDialog';
/* 查询关联 */
import { listTemplate } from '../../api/fields/fields-comment';
export default {
  name: 'Dotmanagerindex',
  components: {
    chosseTypeAlert
  },
  data () {
    return {
      loading: false,
      created: false,
      searchStr: '',
      list: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0,
        formName: ''
      },
      col: [
        {
          index: 'nodeName',
          name: '节点名称',
          width: 300,
          ellipsis: true,
          resizable: true
        },
        { index: 'typeString', name: '节点类型', width: 100, resizable: true },
        // { index: 'description', name: '备注说明', width: 200, ellipsis: true },
        {
          index: 'gmtModified',
          name: '更新时间',
          width: 180,
          slot: true,
          resizable: true
        },
        { index: 'editorName', name: '更新人', width: 130, resizable: true },
        {
          index: 'status',
          name: '状态',
          width: 90,
          slot: true,
          resizable: true
        },
        {
          index: 'operation',
          name: '操作',
          width: 220,
          operation: true,
          resizable: true
        }
      ],
      operation: [
        {
          name: '启用',
          type: 0,
          format: function (row) {
            return ['启用', '禁用'][row.status];
          }
        },
        {
          name: '编辑',
          type: 1,
          disabled: function (row) {
            return false;
          }
        },
        {
          name: '复制',
          type: 2
        },
        {
          name: '删除',
          type: 3,
          disabled: function (row) {
            return !!row.status;
          }
        }
      ]
    };
  },
  created () {
    this.created = true;
  },
  activated () {
    if (!this.created) this.ApiNodePoolGet();
    this.created = false;
  },
  methods: {
    handleCommand (value) {
      this.addNewForm('', value);
    },
    // 获取表格数据
    getList: function (listQuery) {
      // pageNum, pageSize
      const { page, pageSize, formName } = listQuery;
      getFormList({ pageNum: page, pageSize, formName }).then(res => {
        const { list, total } = res.data;
        this.list = list;
        this.listQuery = {
          ...this.listQuery,
          total
        };
      });
    },
    // 操作回调
    operationClick: function (type, row) {
      const { id, status } = row;
      const options = [
        row => {
          this.checkTimer(this.setStates(id, status), 'timer')();
          //  this.setStates(id, status);
        },
        row => {
          this.checkTimer(this.addNewForm(id), 'timer')();
        },
        row => {
          this.checkTimer(this.ApiCopyNodePoolByNodePoolId(id), 'timer')();
          // this.ApiCopyNodePoolByNodePoolId(id);
        },
        row => {
          this.checkTimer(this.deleteAction(id), 'timer')();
          // this.deleteAction(id);
        }
      ];
      options[type](row);
    },

    deleteAction (id) {
      this.$XyyMsg({
        title: '提示',
        content: '确定删除此节点吗？',
        onSuccess: () => {
          this.ApiNodePoolDelete(id);
        }
      });
    },

    // 改变状态
    setStates (id, status) {
      status = status === 0 ? 1 : 0;
      const msgs = ['禁用', '启用'];
      this.$XyyMsg({
        title: '提示',
        content: '确定' + msgs[status] + '此节点应用吗？', // html代码串
        onSuccess: () => {
          this.APIDisableNodePool(id, status);
          // changeStatus({ id, status }).then(res => {
          //   if (res && res.code) {
          //     this.$XyyMessage.success('节点应用已' + msgs[status]);
          //     this.getList(this.listQuery);
          //   } else {
          //     this.$XyyMessage.error(res.msg);
          //   }
          // });
        }
      });
    },
    // 删除节点
    delForm (id) {
      this.$XyyMsg({
        title: '提示',
        content: '确定删除此节点应用吗？',
        onSuccess: () => {
          delForm(id).then(res => {
            if (res && res.code) {
              this.$XyyMessage.success('删除成功');
              this.getList(this.listQuery);
            } else {
              this.$XyyMessage.error(res.msg);
            }
          });
        }
      });
    },
    // 复制节点
    copyForm (id) {
      copyForm(id).then(res => {
        if (res && res.code) {
          this.$XyyMessage.success('复制成功');
          this.getList(this.listQuery);
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 新建节点
    addNewForm (id, type) {
      if (type !== undefined) {
        this.$router.push({
          path: '/worksheet/dotmanagernewdot/' + new Date().getTime(),
          query: {
            type: 'createdot',
            dotType: type.type
          }
        });
      } else {
        this.$router.push({
          path: '/worksheet/dotmanagerupdate/' + id,
          query: { type: 'updatedot', formid: id }
        });
      }
    },
    // 时间格式化
    getFormatDate: function (row, column, cellValue, index) {
      return new Date(cellValue + 8 * 3600 * 1000)
        .toJSON()
        .substr(0, 19)
        .replace('T', ' ');
    },

    searchAction () {
      this.listQuery.page = 1;
      this.listQuery.formName = this.searchStr;
      this.ApiNodePoolGet();
    },

    changeString (value) {
      this.checkTimer(this.searchAction());
    },

    /**
     * API 请求
     */
    ApiNodePoolGet () {
      this.loading = true;
      // this.$loading({
      //   lock: true,
      //   text: 'Loading',
      //   spinner: 'el-icon-loading',
      //   background: 'rgba(255,255,255, 0.8)'
      // });
      nodePoolGet({
        NodeName: this.listQuery.formName,
        pageNum: this.listQuery.page,
        pageSize: this.listQuery.pageSize
      }).then(res => {
        this.loading = false;
        if (res.code !== 1) {
          this.$XyyMessage.error(res.msg);
          return;
        }
        const { list, total } = res.data;
        this.listQuery.total = total;
        var list1 = [];
        for (let index = 0; index < list.length; index++) {
          const element = list[index];
          if (element.nodeType === 0) {
            element.typeString = '发起节点';
          } else if (element.nodeType === 1) {
            element.typeString = '处理节点';
          } else if (element.nodeType === 2) {
            element.typeString = '抄送节点';
          } else {
            element.typeString = '关闭节点';
          }
          list1.push(element);
        }
        this.list = [];
        this.list = list1;
      });
    },
    ApiNodePoolDelete (val) {
      nodePoolDelete({ id: val }).then(res => {
        if (res.code === 1) {
          this.$XyyMessage.success('节点已删除');
          this.ApiNodePoolGet();
        } else if (res.code === 0) {
          this.$XyyMsg({
            title: '提示',
            content: res.msg,
            closeBtn: false,
            onSuccess: () => { }
          });
        } else if (res.code === 9) {
          // this.$XyyMessage.error(res.msg);
          // 查询字段是否被关联
          let html = '当前有表单应用正在使用此节点,请先修改表单应用( ';
          html += `<span style="color: red"> ${res.msg}</span>`;
          html += ' )';
          this.$XyyMsg({
            title: '提示',
            content: html,
            closeBtn: false,
            onSuccess: function () { }
          });
        } else {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: res.msg, // html代码串
            onSuccess: () => { }
          });
        }
      });
    },
    ApiCopyNodePoolByNodePoolId (val) {
      copyNodePoolByNodePoolId({ id: val }).then(res => {
        if (res.code === 1) {
          this.$XyyMessage.success('复制成功');
          this.ApiNodePoolGet();
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    APIDisableNodePool (id, status) {
      disableNodePool({ id: id }).then(res => {
        if (res.code === 1) {
          const msgs = ['禁用', '启用'];
          this.$XyyMessage.success('节点已' + msgs[status]);
          this.ApiNodePoolGet();
        } else if (res.code === 0) {
          this.$XyyMsg({
            title: '提示',
            content: res.msg,
            closeBtn: false,
            onSuccess: () => { }
          });
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.search-box {
  width: 344px;
  float: right;
  .el-input {
    width: calc(100% - 76px);
    /deep/ .el-input__inner {
      height: 36px;
      line-height: 36px;
    }
  }
  button {
    width: 68px;
    height: 36px;
    float: right;
  }
}
.status-tip {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 2px 2px 0;
  &.open {
    background: #67c23a;
  }
  &.close {
    background: #ff3024;
  }
}
</style>

