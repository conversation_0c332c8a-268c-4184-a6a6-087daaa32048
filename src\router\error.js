import Layout from '@/views/layout/Layout';
export default [
  {
    path: '/',
    component: Layout,
    redirect: '/empty',
    hidden: true
  },
  /* 空白页 */
  {
    path: '/empty',
    name: 'empty',
    component: Layout,
    hidden: true
  },
  /* error */
  {
    path: '/error',
    name: 'error',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '500',
        name: '500',
        component: require('@/views/common/500').default
      },
      {
        path: 'NoNetwork',
        name: 'NoNetwork',
        component: require('@/views/common/no-network').default
      }
    ]
  },
  {
    path: '/403',
    name: '403',
    hidden: true,
    component: () => import('@/views/common/403')
  }
]
