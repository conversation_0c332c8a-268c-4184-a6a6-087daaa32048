import request from '@/utils/request-im';

/* 根据订单编号获取订单详情接口/crmapi/getOrderInfoByNo/{orderNo}*/
export function getOrderInfoByNo(params) {
  return request({
    url: '/crm/orderInfoByNo/' + params.orderNo,
    method: 'get'
    // params: params
  });
}
/* 根据退款单编号获取退款单详情接口/crmapi/getRefundOrderInfoByNo/{orderNo}*/
export function getRefundOrderInfoByNo(params) {
  return request({
    url: '/crm/refundOrderInfoByNo/' + params.orderNo,
    method: 'get'
    // params: params
  });
}

/* 根据订单编号获取订单详情不带商品信息/crmapi/{merchantId}/getOrderBaseByNo/{orderNo}*/
export function getOrderBaseByNo(params) {
  const url = `/crm/${params.memberId}/orderBaseByNo/${params.orderNo}`;
  return request({
    url: url,
    method: 'get'
    // params: params
  });
}
/* 获取订单列表接口*/
export function listOrdersLast(params) {
  return request({
    url: '/crm/listOrdersLast/',
    method: 'get',
    params: params
  });
}
/* 获取退款单列表接口*/
export function listRefundOrdersLast(params) {
  return request({
    url: '/crm/listRefundOrdersLast',
    method: 'get',
    params: params
  });
}

/* 订单信息查询 */
export function queryOrderInfoPage(data) {
  return request({
    url: '/imorder/queryOrderInfoPage',
    method: 'post',
    data
  });
}

/* 获取退款单列表接口*/
export function queryRefundOrderInfoPage(data) {
  return request({
    url: '/imorder/queryRefundOrderInfoPage',
    method: 'post',
    data
  });
}

/* 查询订单商品列表信息 */
export function queryOrderDetailList(params) {
  return request({
    url: '/imorder/queryOrderDetailList',
    method: 'get',
    params
  });
}

// 查询订单物流信息
export function queryLogisticsInfo(params) {
  return request({
    url: '/imorder/queryLogisticsInfo',
    method: 'get',
    params
  });
}

// 订单备注快捷语备注下拉
export function querySelectRemarkDict(params) {
  return request({
    url: '/imorder/selectRemarkDict',
    method: 'get',
    params
  });
}

// 编辑订单备注
export function submitAddRemark(data) {
  return request({
    url: '/imorder/addRemark',
    method: 'put',
    data
  });
}

// 查询订单备注
export function queryRemark(params) {
  return request({
    url: '/imorder/queryRemark',
    method: 'get',
    params
  });
}

// 查询订单发票信息
export function queryOrderInvoice(params) {
  return request({
    url: '/imorder/getOrderInvoice',
    method: 'get',
    params
  });
}

// 发送发票
export function sendPdfMessage(params) {
  return request({
    url: `/chat/sendPdfMessage/${params.dialogId}?url=${params.url}`,
    method: 'post'
  });
}

// 发送订单
export function sendOrderInfoMessage(params) {
  return request({
    url: `/chat/sendOrderInfoMessage/${params.dialogId}?orderNo=${
      params.orderNo
    }`,
    method: 'post'
  });
}

// 发送商品
export function sendProductMessage(params) {
  return request({
    url: `/chat/sendProductMessage/${params.dialogId}?orderNo=${
      params.orderNo
    }&skuId=${params.skuId}`,
    method: 'post'
  });
}

// 查询买家开票信息
export function queryInvoiceInfo(params) {
  return request({
    url: '/imorder/invoice',
    method: 'get',
    params
  });
}

// 查询订单跳转链接
export function queryOrderDetailUrl(params) {
  return request({
    url: '/imorder/queryOrderDetailUrl',
    method: 'get',
    params
  });
}

// 查询商品跳转链接
export function queryProductDetailUrl(params) {
  return request({
    url: '/imorder/queryProductDetailUrl',
    method: 'get',
    params
  });
}
