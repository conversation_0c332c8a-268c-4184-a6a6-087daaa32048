<template>
  <div>
    <!--客户信息-->
    <div id="customeInfo" class="bind-custome">
      <div v-if="!hasInfo||!isBind" class="ele-searchbox">
        <!--@keyup.enter.native="handleIconClick"-->

        <el-autocomplete
          ref="searchref"
          v-model="shopName"
          :fetch-suggestions="querySearchAsync"
          placeholder="检索客户进行绑定"
          popper-class="select-list-box"
          @select="handleShopNameSelect"
        >
          <svg-icon
            slot="suffix"
            ref="searchIcon"
            class="svg-icon"
            icon-class="search"
            style="top:12px;position: relative"
            @click="handleIconClick"
          ></svg-icon>
        </el-autocomplete>
      </div>
      <div v-if="!customeInfo.id" class="center">
        <img
          src="../../../../assets/common/no_custome_info.png"
          style="max-width:400px"
          alt="暂无客户信息"
        />
      </div>
      <div v-if="hasInfo&&customeInfo.id" class="ele-info">
        <header>基本信息</header>
        <p v-if="customeInfo.id" class="box row-between column-center shop-name">
          <em>{{ customeInfo.realName }}</em>
          <input
            v-if="!isBind"
            :value="isBind?&quot;取消绑定&quot;:&quot;绑定&quot;"
            type="button"
            @click="handleBind"
          />
        </p>
        <p v-if="hasInfo" class="line"></p>
        <div class="info">
          <!--药店详情   -->
          <ul v-if="customeInfo.id" class="box row-wrap row-start basic">
            <li>
              <i>药店编码：</i>
              <span>{{ customeInfo.id }}</span>
            </li>
            <li>
              <i>会员姓名：</i>
              <span>{{ customeInfo.nickname }}</span>
            </li>
            <li>
              <i>手机号码：</i>
              <span>{{ customeInfo.mobile }}</span>
            </li>
            <li>
              <i>客户类型：</i>
              <span>{{ customeInfo.businessType }}</span>
            </li>
            <li>
              <i>状态：</i>
              <span v-if="customeInfo.status!==1">{{ customeInfo.statusStr }}</span>
              <span v-else>
                <em>{{ customeInfo.statusStr }}</em>
              </span>
            </li>
            <li>
              <i>资质状态：</i>
              <span>{{ customeInfo.licenseStatusStr }}</span>
            </li>
            <li>
              <i>当前等级：</i>
              <span>{{ customeInfo.currentLevel }}</span>
            </li>
            <li>
              <i>普药销售：</i>
              <span>{{ customeInfo.sysRealName }}</span>
            </li>
            <li>
              <i>邀请码：</i>
              <span>{{ customeInfo.authCode }}</span>
            </li>
            <li>
              <i>销售电话：</i>
              <span>{{ customeInfo.sysJobNumber }}</span>
            </li>
            <li>
              <i>注册时间：</i>
              <span>{{ customeInfo.activeTime|formatTime }}</span>
            </li>
            <li>
              <i>注册地区：</i>
              <span>{{ customeInfo.registAddress }}</span>
            </li>
            <li>
              <i>地址：</i>
              <span>{{ customeInfo.ckaddress }}</span>
            </li>
            <li>
              <i>经营范围：</i>
              <span>{{ customeInfo.businesscont }}</span>
            </li>
          </ul>
          <ul
            v-for="(item ,index) in bankInfo"
            v-if="bankInfo&&bankInfo.length>0"
            :key="index"
            class="card"
          >
            <li>
              <i>开户银行：</i>
              <span>{{ item.bankName }}</span>
            </li>
            <li>
              <i>开户户名：</i>
              <span>{{ item.accountName }}</span>
            </li>
            <li>
              <i>银行卡号：</i>
              <span>{{ item.cardNo }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  bindOrCancelUser,
  getCustomeDetails,
  getBankInfo,
  getListMerchantByName
} from '@/api/im_view/customeInfo';
export default {
  name: 'Index',
  data: function() {
    return {
      hasInfo: false,
      isBind: false,
      shopName: null,
      customeInfo: {},
      bankInfo: [],
      options: [],
      timeout: null,
      shopId: '' // 客户ID
    };
  },
  computed: {
    customId: function() {
      return this.$store.getters.khid;
    },
    dialogId: function() {
      return this.$store.getters.containerid;
    }
  },
  watch: {
    customId: function(newVal, oldVal) {
      this.customeInfo = {};
      this.bankInfo.splice(0, this.bankInfo.length);
      this.shopId = newVal || this.customId;
      if (this.shopId) this.checkIsBind(this.shopId);
      this.clear();
    }
  },
  mounted() {
    this.shopId = this.customId;
    // this.shopId = 58496;
    window.customeTabObj = this;
    if (this.shopId) this.checkIsBind(this.shopId);
  },
  methods: {
    checkIsBind(memberId) {
      getCustomeDetails({ memberId: memberId }).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        if (res.data && res.data.id) {
          this.customeInfo = Object.assign({}, res.data);
          this.hasInfo = true;
          this.isBind = true;
          this.getBankInfo(memberId);
        }
      });
    },
    handleIconClick() {
      this.$refs.searchref.focus();
      const shopName = this.shopName;
      if (!shopName) {
        return;
      }
      this.shopName = '' + shopName;
    },
    clear() {
      this.isBind = false;
    },
    handleBind() {
      if (this.isBind) {
        this.$confirm('确定取消此绑定?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'notice-box',
          cancelButtonClass: 'cancel-button',
          confirmButtonClass: 'confirm-button'
        })
          .then(() => {
            bindOrCancelUser({ dialogId: this.dialogId, uid: 0 }).then(res => {
              if (res.code !== 1) {
                this.$XyyMessage.warning(res.msg);
                return;
              }
              this.isBind = false;
              this.$XyyMessage.success('解绑成功');
            });
          })
          .catch(() => {
            this.$XyyMessage({
              type: 'info',
              message: '已取消'
            });
          });
      } else {
        if (!this.dialogId || !this.shopId) {
          this.$XyyMessage.error('请选择会话');
          return;
        }
        bindOrCancelUser({ dialogId: this.dialogId, uid: this.shopId }).then(
          res => {
            if (res.code !== 1) {
              this.$XyyMessage.warning(res.msg);
              return;
            }
            this.isBind = true;
            this.$store.commit('imProperty/SET_IMProperty_khid', this.shopId);
            this.$XyyMessage.success('绑定成功');
          }
        );
      }
    },
    getInfo(shopId) {
      this.getCustomeInfo()
        .then(data => {
          this.customeInfo = Object.assign({}, data);
          this.hasInfo = true;
          // 再绑定情况下，再获取银行卡信息
          this.getBankInfo(shopId);
        })
        .catch(err => {
          this.customeInfo = {};
          this.clear();
          this.hasInfo = false;
          this.$XyyMessage.warning(err.msg);
        });
    },
    getCustomeInfo() {
      return new Promise((resolve, reject) => {
        if (!this.shopId) {
          resolve({});
        }
        // 根据药店Id 查询基本信息
        getCustomeDetails({ memberId: this.shopId }).then(res => {
          if (res.code !== 1) {
            reject(res.msg);
          }
          res.data && res.data.id ? resolve(res.data) : reject(res.msg);
        });
      });
    },
    getBankInfo(memberId) {
      getBankInfo({ memberId: memberId }).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.error(res.msg || '');
          return;
        }
        this.bankInfo = res.data;
      });
    },
    querySearchAsync(queryString, cb) {
      if (!queryString) {
        cb([]);
        return;
      }
      getListMerchantByName({ name: this.shopName }).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        if (res.data && res.data.length > 0) {
          this.options = res.data.map(item => {
            return { value: item['realName'], id: item.id };
          });
        } else {
          this.options = [];
        }
        cb(this.options);
      });
    },
    handleShopNameSelect(item) {
      this.shopId = item.id;
      item.id && this.getInfo(this.shopId);
    }
  }
};
</script>

<style scoped>
@import '../../../../styles/custome-order.css';
.center {
  text-align: center;
}
.center img {
  width: 220px;
}
</style>
