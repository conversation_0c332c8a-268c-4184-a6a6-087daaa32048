<template>
  <div class="navbar">
    <div class="navbar-left">
      <div class="sidebar-header-item">
        <span>客服系统</span>
      </div>
      <div class="sidebar-header-notice">
        <!-- 知识公告走马灯 -->
        <template v-if="knowledgeNoticeList.length">
          <el-carousel
            height="20px"
            direction="vertical"
            :interval="5000"
            indicator-position="none"
          >
            <el-carousel-item v-for="item in knowledgeNoticeList" :key="item.id">
              <img class="el-carousel-icon" :src="require('@/assets/icons/icon-notice.png')" />
              <el-link
                @click="btnKnowledgeNoticeDetailClick(item)"
              >【{{ item.modifyTime | newDateFormatter }}】{{ item.title | noticeTitleFormatter }}</el-link>
              <el-link
                :underline="false"
                class="btn-knowledge-more"
                @click="btnKnowledgeNoticeMoreClick"
              >查看更多</el-link>
            </el-carousel-item>
          </el-carousel>
        </template>
      </div>
    </div>
    <div class="navbar-container" @click="closeMessage">
      <span class="title">所属业务</span>
      <span class="dividerBox">
        <el-divider direction="vertical"></el-divider>
      </span>
      <el-dropdown @command="handleCommand" trigger="click">
        <span class="el-dropdown-link">
          <img :src="curenntChannel.url" class="channelIcon" />
          {{curenntChannel.businessPartName}}
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown" class="channelBox">
          <el-dropdown-item
            v-for="item in channelList"
            :class="item.businessPartCode == curenntChannel.businessPartCode ? 'active' :''"
            :key="item.businessPartCode"
            :command="item"
          >
            <img :src="item.url" />
            {{item.businessPartName}}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-divider direction="vertical"></el-divider>
      <span class="user-wrapper">
        <img class="user-avatar" src="../../../assets/common/user-avatar.png" alt="头像" />
        <span class="user-name">{{ name }}</span>
        <span class="user-status" v-if="restStatic" @click="loginRest">休</span>
      </span>
      <el-divider direction="vertical"></el-divider>
      <div class="operation">
        <el-row>
          <el-col :span="12">
            <div class="message-box">
              <svg-icon
                class="logout message-btn"
                icon-class="message"
                @click.stop="notifyMessage"
              />
              <span v-if="isDot" class="dot"></span>
            </div>
          </el-col>
          <el-col :span="12">
            <svg-icon class="logout" icon-class="icon-logout" @click="logout" />
          </el-col>
        </el-row>
        <!-- <div class="switch-system" @click="switchSystem">
          <svg-icon class="system-icon" icon-class="switch-system" />选择系统
        </div>-->
      </div>
    </div>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      :close-on-click-modal="false"
      custom-class="dialoglogin"
      width="30%"
    >
      <span>
        <span style="color:#292933;font-size:16px;">确定退出登录？</span>
        <p>
          <span>次日休班</span>
          <el-radio v-model="radio" label="1">是</el-radio>
          <el-radio v-model="radio" label="2">否</el-radio>
        </p>
        <p v-if="radio=='1'">
          <span>休班天数</span>
          <el-select v-model="restValue" size="small" placeholder="请选择">
            <el-option v-for="item in options" :key="item" :value="item"></el-option>
          </el-select>
        </p>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="loginConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="提示"
      :visible.sync="newdialogVisible"
      :append-to-body="true"
      :close-on-click-modal="false"
      width="30%"
      top="28vh"
    >
      <span>未来{{restValue}}天您将休班</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="newdialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="openPrompt">确 定</el-button>
      </span>
    </el-dialog>
    <transition name="fade-transform">
      <div v-if="status" class="message-list">
        <ul v-if="list.length>0">
          <li v-for="item in list" :key="item.id">
            <p class="box row-start column-center" @click="toWorkSheet(item)">
              <span>
                <i v-show="item.status==0" class="notice"></i>
              </span>
              <i v-if="item.linkTab === 9" class="icon-warning"></i>
              <el-popover
                placement="left"
                trigger="hover"
                effect="light ml"
                popper-class="pop-style"
              >
                {{ item.content }}
                <em slot="reference" class="message">{{ item.content }}</em>
              </el-popover>
            </p>
            <p class="box row-around column-center">
              <time>{{ item.gmtCreate|formatTime }}</time>
              <span @click="updateSingleMessageStatus(item)">
                <b v-show="item.status==0">知道了</b>
              </span>
            </p>
          </li>
        </ul>
        <div v-else class="box row-center column-center" style="height:244px;">
          <img src="../../../assets/message/no-message.png" alt style="width:166px" />
        </div>
        <p class="box row-around column-center btn-group">
          <el-button plain @click="closeMessage">关闭</el-button>
          <el-button :disabled="isDisabled" plain @click="updateAllMessageStatus">全部标记为已读</el-button>
          <el-button class="active-btn" @click="lookAll">查看</el-button>
        </p>
      </div>
    </transition>
  </div>
</template>

<script>
import {
  changeState,
  findCsUserState,
  updateUserState,
  saveUserState,
  findEffectiveKnowledgeNoticeList
} from '@/api/monitor/index.js';
import { getPersonalData } from '@/api/im-config/personal-setting';
import {
  getUnreadList,
  updateAllMessageStatus,
  updateSingleMessageStatus,
  batchUpdateMessagePopOut
} from '@/api/message';
import {
  loginChangeState,
  saveLineOfBusinessSwitchingChange
} from '@/api/login';
import { getWorkorderInfoAndReplyInfo } from '@/api/mySheetManage';
import Vue from 'vue';
import VueNativeSock from 'vue-native-websocket';
let reconnecTimeId = null;
export default {
  filters: {
    //知识公告title超出50字显示省略号
    noticeTitleFormatter: function(title) {
      let retTitle = '';
      if (title && title.length >= 50) {
        retTitle = title.substr(0, 50) + '...';
      } else {
        retTitle = title;
      }
      return retTitle;
    },

    //格式化时间到秒
    newDateFormatter: function(timeStr) {
      let retTimeStr = '';
      try {
        if (timeStr) {
          const d = new Date(timeStr);
          retTimeStr = `${d.getFullYear()}-${(d.getMonth() + 1)
            .toString()
            .padStart(2, 0)}-${d
            .getDate()
            .toString()
            .padStart(2, 0)} ${d
            .getHours()
            .toString()
            .padStart(2, 0)}:${d
            .getMinutes()
            .toString()
            .padStart(2, 0)}:${d
            .getSeconds()
            .toString()
            .padStart(2, 0)}`;
        } else {
          retTimeStr = '';
        }
      } catch (err) {
        retTimeStr = '';
      }
      return retTimeStr;
    }
  },
  data() {
    return {
      list: [], // 未读消息列表

      isDisabled: false,
      dialogVisible: false, //退出框
      newdialogVisible: false, //休息天数再次确认框
      loginObj: {}, //
      restStatic: true,
      radio: '2',
      restValue: '1',
      options: ['1', '2', '3', '4', '5', '6', '7'],

      //websocket相关
      ws: null, //im的websocket连接对象
      wsHeartBeatCheckTimeout: null, //socket心跳超时时间计时器
      wsHeartBeatCount: 0, //socket心跳发送次数的计数
      wsReconnectLock: false, //防止socket重复重连
      wsReconnectTimeout: null, //socket重连计时器

      notification: null, //浏览器系统通知对象
      notificationCloseTimer: null, //浏览器系统通知对象关闭计时器

      // channelList: [], // [{ businessPartName: '药帮忙', businessPartCode: 'S00009999', url: '../../../../static/channel/logo_YBM.png' }, { businessPartName: '智慧脸', businessPartCode: 'S00009998', url: '../../../../static/channel/logo_ZL.png' }, { businessPartName: '宜贰叁', businessPartCode: 'S00009997', url: '../../../../static/channel/logo_YKQ.png' }],
      // curenntChannel: {}

      knowledgeNoticeList: [] //知识公告滚动数据list
    };
  },
  computed: {
    name: function() {
      return this.$store.getters.name;
    },
    isDot: function() {
      return this.$store.getters.isDot;
    },
    status: function() {
      return this.$store.getters.box_status;
    },
    channelList: function() {
      return this.$store.getters.channelList;
    },
    curenntChannel: function() {
      return this.$store.getters.channel || {};
    }
  },
  watch: {
    //socket超时时间30s
    wsHeartBeatCount: function(val) {
      if (val >= 7) {
        this.$parent.wsKFOnlineStatusRefrest = true;
      } else {
        this.$parent.wsKFOnlineStatusRefrest = false;
      }
    }
  },
  created() {
    const _this = this;

    this.initWebSocket();

    window.onbeforeunload = function() {
      this.closeWebSocket();
    };
  },
  mounted() {
    this.getListStart('whitNotice');
    this.changeLoginState();
    this.getUserState();
    this.findEffectiveKnowledgeNoticeList();
  },
  methods: {
    /**
     * 主动关闭websocket
     */
    closeWebSocket() {
      this.wsHeartBeatReset();
      this.ws && this.ws.close();
      this.wsReconnectLock = false;
    },

    /**
     * 初始化WebSocket
     */
    initWebSocket() {
      this.closeWebSocket();

      let socket_api =
        process.env.BASE_API.replace('http', 'ws') + '/websocket';
      if (
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
      ) {
        // socket新增渠道参数
        socket_api +=
          '?businessPartCode=' + this.$store.getters.channel.businessPartCode;
      }
      if ('WebSocket' in window) {
        this.ws = new WebSocket(socket_api);
      } else if ('MozWebSocket' in window) {
        this.ws = new MozWebSocket(socket_api);
      } else {
        this.ws = null;
        this.$XyyMessage.warning(
          '您的浏览器不支持WebSocket协议,建议使用新版谷歌浏览器！'
        );
      }

      if (this.ws) {
        this.ws.onopen = this.wsOnOpen;
        this.ws.onmessage = this.wsOnMessage;
        this.ws.onclose = this.wsOnClose;
        this.ws.onerror = this.wsOnError;
      }
    },

    wsOnOpen() {
      this.wsReconnectLock = false;
      this.wsHeartBeatStart();
    },

    wsOnMessage(e) {
      //收到socket消息,重置心跳发送次数的计数
      this.wsHeartBeatCount = 0;
      try {
        let socketData = JSON.parse(e.data);
        if (socketData.id === '00000') {
          //心跳检测的响应体
        } else if (socketData.id === '00001') {
          //有新的知识公告
          if (socketData.message && socketData.message.length) {
            this.knowledgeNoticeList = socketData.message;
          } else {
            this.knowledgeNoticeList = [];
          }
        } else {
          this.getList('whitNotice', () => {});
        }
      } catch (err) {}
    },

    wsOnClose() {
      this.closeWebSocket();
    },

    wsOnError() {
      //当前WebSocket发生错误,重连
      this.wsReconnect();
    },

    //心跳检测重置
    wsHeartBeatReset() {
      this.wsHeartBeatCount = 0;
      clearInterval(this.wsHeartBeatCheckTimeout);
    },

    //开启心跳
    wsHeartBeatStart() {
      this.wsHeartBeatCheckTimeout = setInterval(() => {
        if (this.ws.readyState === 1) {
          //当前WebSocket连接正常
          this.wsHeartBeatCount++;
          if (this.wsHeartBeatCount >= 7) {
            //当前WebSocket超时,重连
            this.wsReconnect();
          } else {
            this.ws.send(JSON.stringify({ id: '00000', message: 'HeartBeat' }));
          }
        } else {
          //当前WebSocket已处于关闭态,重连
          this.wsReconnect();
        }
      }, 5000);
    },

    //重连
    wsReconnect() {
      if (this.wsReconnectLock) {
        return;
      }
      this.wsReconnectLock = true;
      this.wsReconnectTimeout && clearTimeout(this.wsReconnectTimeout);
      this.wsReconnectTimeout = setTimeout(() => {
        this.initWebSocket();
      }, 1000);
    },

    /**
     *  展开或收起消息列表
     */
    notifyMessage() {
      console.log(this.status);
      !this.status
        ? this.$store.commit('message/SET_Message_status', 1)
        : this.$store.commit('message/SET_Message_status', 0);
      console.log(this.status);
      if (this.status) {
        console.log('获取消息列表');
        this.getListStart('whitoutNotice');
      }
    },

    logout: function() {
      // 退出获取当前客服ID
      const obj = {};
      obj['state'] = 6;
      obj['changeReason'] = 1;
      getPersonalData().then(res => {
        obj['kefuId'] = res.data.id;
        this.loginObj = obj;
      });
      this.dialogVisible = true;
    },

    loginConfirm() {
      if (this.radio == '1') {
        this.newdialogVisible = true;
      } else {
        this.openPrompt();
      }
    },

    openPrompt() {
      changeState(this.loginObj).then(res => {
        if (this.radio == '1') {
          //传参，休假天数
          saveUserState(this.restValue).then(res => {
            if (res.code === 1) {
              this.restStatic = true;
              this.$store.commit('message/SET_User_status', 1);
              this.retrunKf();
            } else {
              this.dialogVisible = false;
              this.newdialogVisible = false;
              this.$XyyMessage.error(res.msg);
            }
          });
        } else {
          this.retrunKf();
        }
      });
    },

    /**
     * 用户休假更新状态
     */
    loginRest() {
      this.$XyyMsg({
        title: '提示',
        content: '确定取消休班？', // html代码串
        onSuccess: () => {
          // 0 取消休假
          updateUserState('0').then(res => {
            if (res.code === 1) {
              this.$XyyMessage.success('取消成功！');
              this.$store.commit('message/SET_User_status', 0);
              this.restStatic = false;
            } else {
              this.$XyyMessage.error(res.msg);
            }
          });
        }
      });
    },

    /**
     * 休假状态查询，0-无休假 1-休假中
     */
    getUserState() {
      findCsUserState().then(res => {
        if (res.code === 1 && res.data) {
          if (res.data.status == 0) {
            this.$store.commit('message/SET_User_status', 0);
            this.restStatic = false;
          } else if (res.data.status == 1) {
            this.$store.commit('message/SET_User_status', 1);
            this.restStatic = true;
            this.restValue = res.data.vacationNum;
          }
        }
      });
    },

    retrunKf() {
      this.$store.dispatch('LogOut').then(res => {
        // 调用修改状态接口
        this.dialogVisible = false;
        this.newdialogVisible = false;
        this.$XyyMessage.success('退出成功！');
        //delete this.$options.sockets.onmessage;
        this.restValue = '1';
        this.ws && this.ws.close();
      });
    },

    /**
     * 获取未读消息列表
     */
    getListStart(sendNotice) {
      // 获取未读消息列表
      getUnreadList().then(res => {
        if (res.code === 1 && res.data) {
          if (res.data.length > 0) {
            this.$store.commit('message/SET_Message_isDot', true);
            // this.$store.commit('message/SET_Message_status', 1);
            this.isDisabled = false;
            this.list = res.data;
            if (sendNotice === 'whitNotice') {
              this.queryWorkOrderAppendExplanationNotice();
            }
          } else {
            this.$store.commit('message/SET_Message_isDot', false);
            // this.$store.commit('message/SET_Message_status', 0);
            this.isDisabled = true;
            this.list = [];
          }
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    /**
     * 获取未读消息列表
     */
    getList: function(sendNotice, cb) {
      // 获取未读消息列表
      getUnreadList()
        .then(res => {
          if (res.code === 1) {
            if (res.data.length > 0) {
              this.$store.commit('message/SET_Message_isDot', true);
              this.$store.commit('message/SET_Message_status', 1);
              this.isDisabled = false;
              this.list = res.data;
              if (sendNotice === 'whitNotice') {
                this.queryWorkOrderAppendExplanationNotice();
              }
            } else {
              this.$store.commit('message/SET_Message_isDot', false);
              this.$store.commit('message/SET_Message_status', 0);
              this.isDisabled = true;
              this.list = [];
            }
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .finally(() => {
          cb && cb();
        });
    },

    /**
     * 判断消息列表中是否有未读的追加说明的通知，有则浏览器系统通知
     */
    queryWorkOrderAppendExplanationNotice() {
      if (this.list.length) {
        let unreadWorkOrderAppendExplanationList = []; //消息列表中，未读的追加说明的通知list
        this.list.forEach((item, index) => {
          //16:追加说明的通知
          //hasPopOut 消息是否已弹出 0-未弹出 1-已弹出
          if (item.noticeType === 16 && item.hasPopOut === 0) {
            unreadWorkOrderAppendExplanationList.push(item);
          }
        });
        if (unreadWorkOrderAppendExplanationList.length > 0) {
          this.sendSystemNotice(unreadWorkOrderAppendExplanationList[0]); //发浏览器系统通知
        }
      }
    },

    //发送浏览器通系统通知
    sendSystemNotice(notice) {
      /**
       * Notification.permission属性，用于读取用户给予的权限，它是一个只读属性，它有三种状态
       * default：用户还没有做出任何许可，因此不会弹出通知。
       * granted：用户明确同意接收通知。
       * denied：用户明确拒绝接收通知。
       */
      if (window.Notification) {
        Notification.requestPermission(status => {
          if (status === 'granted') {
            if (this.notification) {
              this.notification.close();
              clearTimeout(this.notificationCloseTimer);
            }
            this.notification = new Notification('消息提醒', {
              body: notice.content,
              icon: '../../pc/static/user_kh.png'
            });

            //播放音频
            this.audioPlay();

            //关闭通知
            this.notificationCloseTimer = setTimeout(() => {
              this.notification.close();
            }, 3000);

            //设置弹出标识，下次请求到该通知，则不推浏览器系统通知
            this.updateHasPopOut(notice);
          }
        });
      }
    },

    /**
     * 播放音频
     * https://developers.google.com/web/updates/2017/09/autoplay-policy-changes
     */
    audioPlay() {
      let elAudio = document.getElementById('audioPlay');
      elAudio.volume = 0.5;
      elAudio.currentTime = 0;
      let playPromise = elAudio.play();
      if (playPromise !== undefined) {
        playPromise
          .then(_ => {
            // Autoplay started!
            console.log('Autoplay started');
          })
          .catch(error => {
            // Autoplay was prevented.
            // Show a "Play" button so that user can start playback.
            console.log('Autoplay was prevented');
          });
      }
    },

    /**
     * 设置消息弹出标识
     */
    updateHasPopOut(notice) {
      batchUpdateMessagePopOut([notice.id]).then(resp => {});
    },
    /**
     * 全部标记为已读
     */
    updateAllMessageStatus() {
      // 全部标为已读
      updateAllMessageStatus().then(res => {
        if (res.code === 1) {
          this.$XyyMessage.success('通知全部已读！');
          this.$store.commit('message/SET_Message_isDot', false);
          this.$store.commit('message/SET_Message_status', 0);
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    /**
     * 更新单一消息为已读
     */
    updateSingleMessageStatus(item) {
      // 更新单一消息为已读
      updateSingleMessageStatus({
        id: !item.parentId ? item.id : item.parentId,
        replyId: item.parentId ? item.id : null
      }).then(res => {
        if (res.code === 1) {
          // this.$XyyMessage.success('更改成功！');
          this.getList('withoutNotice', undefined);
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    /**
     * 关闭消息列表弹窗
     */
    closeMessage() {
      // 关闭消息列表弹窗
      this.$store.commit('message/SET_Message_status', 0);
    },

    /**
     *  查看全部通知
     */
    lookAll() {
      this.$store.commit('message/SET_Message_status', 0);
      this.$store.dispatch('tagsView/addVisitedView', {
        path: '/message/list',
        meta: {
          title: '全部通知'
        }
      });
      this.$router.push({
        path: '/message/list?t=' + new Date().getTime()
      });
    },

    /**
     * 去工单详情
     */
    toWorkSheet(notice) {
      // 0-已超时 1-即将超时 2-我处理中的 3-待领取 4-我发起的 5-抄送给我的，
      // const noticeType = item.linkTab + 1;
      // let url = '';
      // const arr = this.$route.path.split('/');
      // url = `/${arr[1]}/list?t=${new Date().getTime()}&noticeType=${noticeType}`;
      // this.$router.push({
      //   path: url
      // });

      //16:追加说明的通知
      //先查询是否有工单权限，有权限跳详情，无权限弹提示
      if (notice.noticeType === 16) {
        getWorkorderInfoAndReplyInfo({
          workorderId: notice.workorderId
        })
          .then(resp => {
            if (resp.code === 1) {
              //有权限
              this.$router.push({
                path: `/workStatus${resp.data.base_field.form_type_id}/sheetDetail/${notice.workorderId}`,
                query: {
                  id: notice.workorderId,
                  type: 'notice',
                  currentUrl: this.$route.path
                }
              });
            } else {
              //无权限
              this.$XyyMessage.error(resp.msg);
            }
          })
          .finally(() => {
            this.$store.commit('message/SET_Message_status', 0);
          });
      } else {
        this.$store.commit('message/SET_Message_status', 0);
      }
    },

    switchSystem: function() {},

    async changeLoginState() {
      try {
        await loginChangeState();
      } catch (error) {
        console.log(error);
      }
    },

    /**
     * 切换业务线操作
     */
    handleCommand(command) {
      if (command.businessPartCode !== this.curenntChannel.businessPartCode) {
        let loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(255,255,255, 0.8)'
        });
        saveLineOfBusinessSwitchingChange({
          businessPartCode: command.businessPartCode
        })
          .then(res => {
            if (res && res.code === 1) {
              // this.$message('click on item ' + JSON.stringify(command));
              this.curenntChannel = command;
              this.$router.push('/');
              location.reload();
            } else {
              this.$XyyMessage.error(res.msg || '服务异常，操作失败');
            }
          })
          .catch(err => {
            this.$XyyMessage.error('服务异常，操作失败');
            console.log(err);
          })
          .finally(() => {
            loading.close();
          });
      }
    },

    /**
     * 查询所有有效的公告
     */
    findEffectiveKnowledgeNoticeList() {
      findEffectiveKnowledgeNoticeList().then(resp => {
        if (resp.code === 1) {
          if (resp.data && resp.data.length) {
            this.knowledgeNoticeList = resp.data;
          } else {
            this.knowledgeNoticeList = [];
          }
        }
      });
    },

    /**
     * 查看公告详情
     */
    btnKnowledgeNoticeDetailClick(notice) {
      this.$router.replace({
        path: `/knowledge_base/announcementDetails/${notice.id}?templateId=${notice.id}`
      });
    },

    /**
     * 查看更多，跳转到公告大厅页面
     */
    btnKnowledgeNoticeMoreClick() {
      this.$router.replace({
        path: `/knowledge_base/announcementHall`
      });
    }
  }
};
</script>
<style  lang="scss">
.pop-style {
  width: 300px;
  box-shadow: none !important;
  font-size: 14px;
}
.el-dialog.dialoglogin {
  p {
    margin: 20px 0;
    & > span {
      margin-right: 12px;
    }
  }
}
.el-dialog__body {
  padding: 20px;
}
.el-dialog .el-dialog__header {
  border-bottom: 1px solid #ddd;
}
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
@import '@/styles/variables.scss';
.sidebar-header-item {
  height: $navBarHeight;
  background: $navLogoBg;
  // position: absolute;
  // top: 0;
  // left: 0;
  width: 200px;
  line-height: $navBarHeight;
  font-weight: 600;
  color: $navLogoText;
  text-align: center;
  font-size: 22px;
  font-family: PingFangSC;
}
.navbar {
  background-color: $navBarBg;
  height: $navBarHeight;
  // line-height: $navBarHeight;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
  font-family: PingFangSC;

  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  .navbar-left {
    flex-grow: 1;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .sidebar-header-notice {
      flex-grow: 1;

      .el-carousel {
        .el-carousel__item {
          display: flex;
          justify-content: flex-start;
          align-items: center;

          .el-carousel-icon {
            width: 20px;
            height: 20px;
            margin: 0 0 0 12px;
          }

          .el-link {
            height: 20px;
            color: #fff;
            white-space: nowrap;

            &.is-underline:hover:after {
              border-bottom: 1px solid #fff;
            }

            &.btn-knowledge-more {
              position: relative;
              margin-left: 12px;

              &:after {
                content: '';
                width: 100%;
                height: 0;
                border-bottom: 1px solid #fff;
                position: absolute;
                bottom: 0;
                left: 0;
              }
            }
          }
        }
      }
    }
  }

  .navbar-container {
    flex-shrink: 0;
    flex-grow: 0;
    width: 600px;

    text-align: right;
    height: $navBarHeight;
    line-height: $navBarHeight;
    .user-wrapper {
      // margin-right: 30px;
      position: relative;
      .user-avatar {
        display: inline-block;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        vertical-align: middle;
        margin-right: 9px;
      }
      .user-name {
        color: #fff;
      }
      .user-status {
        cursor: pointer;
        position: absolute;
        top: 2px;
        right: -19px;
        background: #f39800;
        width: 18px;
        height: 18px;
        text-align: center;
        line-height: 17px;
        font-size: 12px;
        color: #ffffff;
        border-radius: 50%;
      }
    }
    .operation {
      display: inline-block;
      vertical-align: middle;
      height: 24px;
      line-height: 24px;
      // border-left: 1px solid rgba(255, 255, 255, 0.4);
      .message-box {
        position: relative;
      }
      .logout {
        width: 24px;
        height: 24px;
        vertical-align: middle;
        margin: 0 42px 0 24px;
        cursor: pointer;
      }
      .message-btn {
        margin: 0;
      }
      .dot {
        width: 8px;
        height: 8px;
        position: absolute;
        right: -3px;
        top: 0;
        border-radius: 50%;
        background: #ff3024;
      }
      .switch-system {
        display: inline-block;
        color: #fff;
        margin-right: 20px;
        cursor: pointer;
        .system-icon {
          width: 24px;
          height: 24px;
          vertical-align: middle;
          margin-right: 4px;
        }
      }
      .el-col-12 {
        width: auto;
      }
    }
    .title {
      color: #fff;
      font-size: 14px;
      opacity: 0.5;
    }

    .el-divider--vertical {
      height: 18px;
      opacity: 0.6;
      margin: 0 18px 0 26px;
    }
    .dividerBox {
      .el-divider {
        height: 10px;
        opacity: 0.5;
        margin: 0 6px;
      }
    }
  }

  .message-list {
    position: absolute;
    right: 30px;
    top: $navBarHeight;

    width: 310px;
    // float: right;
    // margin-right: 30px;
    background: #fff;
    box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
    border: 1px solid $appMainBg;
    ul,
    li {
      list-style: none;
      margin: 0;
      padding: 0;
    }
    b {
      font-weight: normal;
    }
    ul {
      height: 205px;
      margin-top: 16px;
      overflow-y: auto;
    }
    li {
      margin-bottom: 28px;
      p {
        margin: 0;
        padding: 0;
        line-height: 20px;
      }
      p:first-child {
        cursor: pointer;
      }
      p:first-child > span {
        margin-left: 9px;
        margin-right: 6px;
        display: inline-block;
        width: 10px;
        // height: 10px;
        // text-align: center;
        line-height: 5px;
      }
      .notice {
        margin-left: 5px;
      }
      p:last-child {
        margin-top: 8px;
        padding-left: 33px;
        padding-right: 20px;
        time {
          display: inline-block;
          width: 80%;
          font-size: 14px;
          color: rgba(144, 147, 153, 1);
          text-align: left;
        }
        span {
          display: inline-block;
          width: 30%;
          font-size: 14px;
          color: $navBarBg;
          text-align: right;
          b {
            cursor: pointer;
          }
        }
      }
      em {
        display: inline-block;
        width: 238px;
        line-height: 20px;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-style: normal;
        overflow: hidden;
        font-size: 14px;
      }
    }
    .btn-group {
      padding-left: 15px;
      padding-right: 15px;
      .el-button {
        padding: 12px 12px 8px 12px;
        height: 36px;
        border-radius: 2px;
      }
      .active-btn {
        border: 1px solid $navBarBg;
        color: #40a3b8;
      }
    }
    .icon-warning {
      display: inline-block;
      width: 14px;
      height: 14px;
      margin-left: 5px;
      background: url('./images/<EMAIL>') center;
      background-size: cover;
    }
    .icon-warning + span {
      margin-left: 4px !important;
    }
  }
  .el-dropdown-link {
    cursor: pointer;
    color: #ffffff;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
  .demonstration {
    display: block;
    color: #8492a6;
    font-size: 14px;
    margin-bottom: 20px;
  }
}
.el-dropdown-link img.channelIcon {
  height: 20px;
  width: 20px;
  display: inline-block;
  margin: -1px 5px 0 0;
  vertical-align: middle;
  position: relative;
}
ul.channelBox {
  margin-top: -10px;
  margin-left: 10px;
  padding: 4px 0;
  li.el-dropdown-menu__item {
    padding: 0 50px 0 8px;
    line-height: 33px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    color: #3a95a8;
    img {
      height: 20px;
      width: 20px;
      display: inline-block;
      margin: -2px 5px 0 0;
      vertical-align: middle;
      position: relative;
    }
  }
  .active {
    background: #ebf4f6;
  }
}

// 去掉下拉菜单的背景框
.el-dropdown-link:focus {
  outline: 0;
}
</style>

