<template>
  <div class="localSetUpNew localSetUpNewList">
    <xyy-list-page>
      <template slot="body">
        <el-form ref="form" :model="form" label-width="100px" :rules="rules">
          <el-form-item label="组合名称" prop="groupName">
            <el-input v-model="form.groupName" maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="form.groupDes" maxlength="100" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="包含省市" prop="checkProvince" class="shengshi">
            <el-button v-for="(item, index) in form.checkProvince" class="shegnshiji">{{ item.label }}<span class="el-icon-close" @click="deleteDataP(item,index)"></span></el-button>
            <el-button v-for="(item, index) in form.checkCityList" class="shegnshiji">{{ item.label }}<span class="el-icon-close" @click="deleteData(item,index)"></span></el-button>
            <el-button @click="AddProvCities"><span class="el-icon-plus">添加</span></el-button>
            <div class="el-form-item__error" v-if="addShow">省市不能为空</div>
            <!-- 添加弹出框-开始-->
            <el-dialog title="选择省市" :visible.sync="dialogTableVisible" class="choiceSS">
              <div class="levelPanel">
                <el-cascader-panel ref="cascaderAddr"  :options="data"  :emitPath = "false" :props="props" v-model="selectedValue"></el-cascader-panel>
              </div>
              <div slot="footer" class="dialog-footer">
                <el-button @click="dialogTableVisible = false">取 消</el-button>
                <el-button type="primary" @click="makeSure">确 定</el-button>
              </div>
            </el-dialog>
            <!-- 添加弹出框结束-->
          </el-form-item>
          <el-form-item label="对应员工组" prop="groups">
            <el-select v-model="form.groups" multiple placeholder="请接入员工组">
              <el-option
                v-for="item in employeGroups"
                :key="item.id"
                :label="item.groupName"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="checkTimer(handerSave('form'),'timer')()">保存</el-button>
          </el-form-item>
        </el-form>
      </template>
    </xyy-list-page>
  </div>
</template>

<script>
import XyyListPage from '../../components/xyy/xyy-list-page/index';
import XyyButton from "../../components/xyy/xyy-button/index";
import { provProvince, provCity, newEditor, getGroups, EmployeeGroups, getRegionalInfo, getSetUpProvince } from '@/api/configuration/RegionalMenu';
import message from "../../store/modules/message";
export default {
  name: 'localSetUpNew',
  components: { XyyButton, XyyListPage },
  data() {
    return {
      addShow: false, // 控制提示显示隐藏
      data: [], // 级联的数据
      props: {
        value: 'id',
        label: 'areaName',
        children: 'nextLevelArea',
        multiple: true
      },
      timer: null,
      rules: {
        groupName: [
          { required: true, message: '组合名称必填', trigger: 'blur' },
        ],
        groups: [
          { required: true, message: '对应员工组必填', trigger: 'change' },
        ]
      },
      selectedValue: [], // 选中的省市值
      selectedValueAll: [], // 选中的所有的省市值
      checkCityListAll: [], // 选中的所以市
      citys: [], // 传递给后台的地区值
      form: {
        groupName: '', // 组合名称
        groupDes: '', // 备注
        groups: [], // 对应员工组
        checkProvince: [], // 选择的省
        checkCityList: [] // 选择的市
      },
      valueTo: {}, // 点击编辑进入后的内容存储值
      saveId: '', // 保存时的id
      employeGroups: [],
      dialogTableVisible: false, // 控制弹出框
      provinceCitySelected: [], // 当前获取的不能选择的省市
    }
  },
  created() {
    this.getValue()
  },
  methods: {

    // 点击添加事件
    async AddProvCities() {
      let that = this
      // 获取省市
      await provProvince().then(response => {
        //  获取已经选择的省市
        let valueToId;
        if(this.valueTo){
          valueToId = this.valueTo.id
        }else {
          valueToId = ''
        }

         getSetUpProvince({ areaId: valueToId }).then(responseP => {
           this.provinceCitySelected = responseP.data
          // 数据为空处理
          if (responseP.data.length === 0) {
            that.data = response.data
            that.dialogTableVisible = true
            return;
          }
         // 循环所有的省
          response.data.forEach(function(item, index){
          //  循环所有的已经选择的省
            responseP.data.forEach(function(itemL, indexL) {
              if(itemL.id === item.areaCode ){
                item.disabled = true
                that.data = response.data
              }
            })
         // 循环所有的市
            if(item.nextLevelArea !== null){
              item.nextLevelArea.forEach(function(item, indexY){
                //  循环所有的已经选择的省
                responseP.data.forEach(function(itemL, indexL) {
                  if(itemL.id === item.areaCode ){
                    item.disabled = true
                    that.data = response.data
                  }
                })
              })
            }
          })
           this.dialogTableVisible = true
        })

      }).catch(function(error) {
        console.log(error);
      });

    },
    // 删除省
    deleteDataP(item, index) {
      this.form.checkProvince.splice(index, 1)
      // 回显数据 删除
      let newArray = []
      this.selectedValue.forEach((itemP, indexP) => {
        newArray[indexP] = itemP.filter((itemL) => {
          return itemL !== item.value;
        })
      })
      this.selectedValue = newArray;
    },
    // 删除市
    deleteData(item, index) {
      this.form.checkCityList.splice(index, 1)
      let newArray = []
      this.selectedValue.forEach((itemP, indexP) => {
        newArray[indexP] = itemP.filter((itemL) => {
          return itemL !== item.value;
        });
      })
      this.selectedValue = newArray;
    },
    // 页面保存事件
    handerSave(formName){
      let that = this;
      let arrProvince = [] // 省的
      let arrCity = [] // 市的
      this.$refs[formName].validate((valid) => {
        if (that.form.checkProvince.length === 0 && that.form.checkCityList.length === 0) {
          this.addShow = true
          return;
        }
        if (valid) {
          that.form.checkProvince.forEach((item) => {
            arrProvince.push({ 'id': item.value, 'name': item.label })
          })
          that.form.checkCityList.forEach((item) => {
            arrCity.push({ 'id': item.value, 'name': item.label })
          })

          // 省市合并
          this.citys = arrProvince.concat(arrCity)
          console.log(this.citys.length)
          if (this.valueTo === undefined) {
            // 不是从编辑进入的 不存在id
            that.saveId = 0
          } else {
            // 是从编辑进入的 存在 id
            that.saveId = that.valueTo.id
          }
          // 后台传参
          let params = {
            groupName: that.form.groupName,
            groups: that.form.groups,
            id: that.saveId,
            groupDes: that.form.groupDes,
            citys: this.citys
          }
          newEditor(params).then(response => {
            if (response.code === 1) {
              this.$message('保存成功')
              this.$store.dispatch('tagsView/delView', this.$route);
              this.$router.replace({
                name: 'localSetUp'
              })
            } else {
              this.$message(response.msg)
            }
          }).catch(function(error) {
            this.$message(error)
          });
        } else {
          return false;
        }
      });

    },
    // 省市确定按钮
    makeSure() {
      let that = this;
      let obj = []; // 被选中的省的，下级的市
      // 确定的时候清空 否则值会重复
      that.form.checkProvince = []
      that.form.checkCityList = []
      // 获取所有选中的省市；
      this.selectedValueAll = this.$refs.cascaderAddr.getCheckedNodes()
      // 循环选中的值判断是否有children值 有的话就是全部选中  没有的话 就是没有全部选中
      // -------如果选择省 但是省中有不能选择的市 选择省的时候就不能全部勾选，去掉这个省--开始
      // 获取选中的省中有不能选中的市
      let obj1 = []
      this.selectedValueAll.forEach((itemK, indexK)=>{
        // 获取省
        if (itemK.hasChildren === true) {
          itemK.children.forEach((itemL, indexL) => {
            // 调用已经选择的接口
            that.provinceCitySelected.forEach((itemJ, indexJ)=>{
              if (itemL.label === itemJ.name) {
                obj1.push(itemL.parent.label)
              }
            })
          })

        }
      })
      obj1 = this.uniq(obj1)

      obj1.forEach((em, eml)=>{
        this.selectedValueAll.forEach((num, numl)=>{
          if (num.label === em){
            this.selectedValueAll.splice(numl, 1)
          }
        })
      })
      // -------如果选择省 但是省中有不能选择的市 选择省的时候就不能全部勾选，去掉这个省--结束
      this.selectedValueAll.forEach((item, index) => {
        //provinceCitySelected
        if (item.hasChildren === true) {
          // that.provinceCitySelected.forEach((itemJ, indexJ)=>{
          //   console.log(itemJ)
          // })
          // 全部的省 省的hasChildren因为有下级 所以为true
          that.form.checkProvince.push(item);
          // 将选择的所有省的市放在obj里面
          item.children.forEach((itemL, indexL) => {
            obj.push(itemL)
          })
        } else if (item.hasChildren === false) {
          // 选中的全部的市，市的hasChildren因为没有下级 所以为false
          that.form.checkCityList.push(item);
        }
      })

      this.form.checkCityList = this.array_diff(that.form.checkCityList, obj)

      if (this.selectedValueAll.length !== 0) {
          this.addShow = false;
      }
      this.dialogTableVisible = false
    },
    // 去掉重复的;
    array_diff(a, b) {
      for (var i = 0; i < b.length; i++) {
        for (var j = 0; j < a.length; j++) {
          if (a[j] == b[i]) {
            a.splice(j, 1);
            j = j - 1;
          }
        }
      }
      return a;
    },
    //数组去重
    uniq(array){
      var temp = []; //一个新的临时数组
      for(var i = 0; i < array.length; i++){
        if(temp.indexOf(array[i]) == -1){
          temp.push(array[i]);
        }
      }
      return temp;
    },
    // 获取省市和员工组
    async getValue() {
      let that = this;
      that.selectedValue = []
      //   获取员工组
      await getGroups().then(response => {
        this.employeGroups = response.data
      }).catch(function(error) {
        console.log(error);
      });

      // 获取点击编辑后整行的内容
      this.valueTo = this.$route.query.obj

      if(this.valueTo){
      //点击编辑进入的页面-存在this.valueTo
        await getRegionalInfo(this.valueTo.id).then(response => {
          response.data.citys.forEach((item, index) => {
            item["label"] = item.name;
            item["value"] = item.id;
            delete item.name;
            delete item.id;
            that.form.checkCityList.push(item)
          })

//         获取省市
           provProvince().then(responseL => {
          let newArr = [];// 所有返回的省
          let newArrS=[] // 所有返回的市
        // 获取省市(当获得的数据是省的时候)
        responseL.data.forEach(function(itemP, indexP){
          response.data.citys.forEach(function(itemL, indexL) {
            if(itemP.id === itemL.value){
              newArr.push(itemP);
            }
          })

          if (itemP.nextLevelArea !== null) {
            itemP.nextLevelArea.forEach((itemK, indexK) => {
              response.data.citys.forEach(function(itemL, indexL) {
                if(itemK.id === itemL.value){
                  newArrS.push(itemK);
                }
              })
            })
          }
        })
        //   前提是市存在如果是市，就把他的省的id，和市的id添加到一起
        if(newArrS.length>0){
          newArrS.forEach(function(itemO, indexO){
            that.selectedValue.push([itemO.parentId,itemO.id])
          })
        }
        // 前提是存在省, 如果是省的话,把省的下边的市的id添加到一起
        if(newArr.length>0){
          newArr.forEach(function(itemO, indexO){
            itemO.nextLevelArea.forEach(function(itemJ, indexJ){
              that.selectedValue.push([itemJ.parentId,itemJ.id])
            })
          })
        }

        })



          that.form = {
            checkProvince: that.form.checkProvince,
            checkCityList: that.form.checkCityList,
            ...response.data
           }
         }).catch(function(error) {
          console.log(error);
        });
      } else {
      // 点击新建地理组合进入的页面-不存在this.valueTo

      }


    }
  }
}
</script>
<style lang="scss" scoped>
  .localSetUpNewList {
    /deep/.shegnshiji{
      margin-left: 0 !important;
      margin-right: 10px;
      margin-bottom: 10px;
      padding: 12px 10px !important;
      background: rgba(245, 247, 250, 1);
    }
  }
  /deep/.shengshi{
    /deep/.el-form-item__label:before{
      content: '*';
      color: #F56C6C;
      margin-right: 4px;
    }
  }
  /deep/.choiceSS{
    /deep/.el-dialog__header{
      padding-top: 15px;
    }
    /deep/.el-dialog__body{
      padding-top: 10px !important;
    }
  }

</style>

<style lang="scss">
  .localSetUpNew {
    .el-cascader-menu{
      min-width: 163px;
    }
    .levelPanel{
      height:100%;
    }
    .el-cascader-menu__wrap{
      height:100%;
      overflow: auto;
    }
  }

  .localSetUpNew{
    .el-dialog{
      width: 400px;
      height: 602px;
    }
    .page-body{
      display: flex;
      justify-content: center;
      /*align-items: center;*/
      .levelPanel{
        @extend .page-body
      }
      .el-dialog__body{
        height: 76%;
        overflow-y: auto;
        .levelPanel{
          justify-content: left;
          .el-tree-node__children{
            position: absolute;
            top: 0;
            left: 150%;
            background: #fff;
          }
        }
      }
      .el-form{
        width: 50%;
        .el-select{
          width: 100%;
        }
      }
    }

  }
</style>
