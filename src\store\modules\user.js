import { getInfo } from '@/api/login';
import { removeToken } from '@/utils/auth';

const user = {
  state: {
    name: '',
    userId: '',
    avatar: '',
    roles: [],
    // 菜单权限
    menu: [],
    validBtn: [],
    channelList: [],
    channel: undefined
  },

  mutations: {
    SET_NAME: (state, name) => {
      state.name = name;
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar;
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles;
    },
    SET_MENU: (state, menu) => {
      state.menu = menu;
    },
    SET_VALID_BTN: (state, validBtn) => {
      state.validBtn = validBtn;
    },
    SET_USERID: (state, id) => {
      state.userId = id;
    },
    SET_CHANNEL: (state, channel) => {
      state.channel = channel;
    },
    SET_CHANNELLIST: (state, channelList) => {
      state.channelList = channelList;
    }
  },

  actions: {
    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo()
          .then(response => {
            const data = response.data;
            const menu = JSON.parse(data.attributes.menu);
            const validBtnArr = JSON.parse(data.attributes.permission);
            const channelList = data.attributes.roleVos.list || [];
            // 跟后端确认并不需要判断
            const channelInfo = (data.attributes.roleVos.list || []).find(
              item => {
                return item.businessPartCode === data.attributes.roleVos.code;
              }
            );
            if (menu && menu.length > 0 && channelInfo) {
              // commit('SET_ROLES', data.roles);
              // const arr = [...menu[0].children];
              const arr = [...menu];
              arr.push({
                title: '全部通知',
                url: '/message/list'
              });
              // [{"parent":66,"funType":3,"code":"btn:cs:workorderrestart","resType":2,"checked":false,"id":179,"title":"工单重启","lev":10,"open":false,"seq":1,"spread":false},
              commit('SET_USERID', data.id);
              commit('SET_NAME', data.attributes.realname);
              commit('SET_AVATAR', data.avatar ? data.avatar : '');
              commit('SET_MENU', arr);
              commit('SET_VALID_BTN', validBtnArr);
              commit('SET_CHANNELLIST', channelList);
              commit('SET_CHANNEL', channelInfo);
              resolve(response);
            } else {
              let message = '当前用户没有系统权限';
              if (!menu && !menu.length) {
                message = '当前用户没有系统权限';
              } else if (!channelInfo) {
                message = '无业务线数据访问权限，请联系管理员授权！';
              }

              reject({
                type: '403',
                message,
                channelInfo
              });
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    // 登出
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        commit('SET_ROLES', []);
        removeToken();
        window.localStorage.removeItem('defRoutes');
        resolve();
        window.location = process.env.BASE_API + '/logout';
        // logout(state.token)
        //   .then(() => {
        //     commit('SET_ROLES', []);
        //     removeToken();
        //     resolve();
        //   })
        //   .catch(error => {
        //     reject(error);
        //   });
      });
    }
  }
};

export default user;
