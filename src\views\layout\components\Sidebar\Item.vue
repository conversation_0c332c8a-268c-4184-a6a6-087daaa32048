<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    meta: {
      type: Object,
      default: () => {
        return {
          title: '',
          icon: ''
        };
      }
    },
    isHover: {
      type: Boolean,
      default: false
    }
  },
  render(h, context) {
    const { icon, title } = context.props.meta;
    const vnodes = [];
//工单1.7改版
    // if (icon) {
    //   if (context.props.isHover) {
    //     vnodes.push(<svg-icon icon-class={icon + '_hover'} />);
    //   } else {
    //     vnodes.push(<svg-icon icon-class={icon} />);
    //   }
    // }

    if (title) {
      vnodes.push(<span slot='title'>{title}</span>);
    }
    return vnodes;
  }
};
</script>
