<template>
  <xyy-list-page>
    <template slot="header">
      <xyy-button icon-class="btn-add" class-name="btn-add-icon" @click="addNewForm(false)">新建表单应用</xyy-button>
      <div class="search-box">
        <el-input v-model="listQuery.formName" placeholder="请输入模板名称" maxlength="20" />
        <el-button type="primary" @click="getList(listQuery)">搜索</el-button>
      </div>
    </template>
    <template slot="body">
      <xyy-table
        :data="list"
        :list-query="listQuery"
        :col="col"
        :operation="operation"
        @get-data="getList"
        @operation-click="operationClick"
      >
        <template slot="gmtModified" slot-scope="{col}">
          <el-table-column
            :key="col.index"
            :prop="col.index"
            :label="col.name"
            :width="col.width"
            :formatter="getFormatDate"
            show-overflow-tooltip
          />
        </template>
        <template slot="status" slot-scope="{col}">
          <el-table-column :key="col.index" :label="col.name" :width="col.width">
            <template slot-scope="{row}">
              <span :class="'status-tip '+(row.status === 1?'open':'close')"></span>
              {{ row.status === 1?'启用':'禁用' }}
            </template>
          </el-table-column>
        </template>
      </xyy-table>
    </template>
  </xyy-list-page>
</template>

<script>
import { getFormList, changeStatus, delForm, copyForm } from '@/api/formManage';

export default {
  name: 'FormList',
  data() {
    return {
      created: false,
      list: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0,
        formName: ''
      },
      col: [
        {
          index: 'name',
          name: '表单名称',
          width: 300,
          ellipsis: true,
          resizable: true
        },
        {
          index: 'formTypeName',
          name: '表单类型',
          width: 120,
          resizable: true
        },
        {
          index: 'description',
          name: '备注说明',
          width: 200,
          ellipsis: true,
          resizable: true
        },
        { index: 'gmtModified', name: '更新时间', slot: true, resizable: true },
        { index: 'editorName', name: '更新人', width: 130, resizable: true },
        {
          index: 'status',
          name: '状态',
          width: 90,
          slot: true,
          resizable: true
        },
        {
          index: 'operation',
          name: '操作',
          width: 270,
          operation: true,
          resizable: true
        }
      ],
      operation: [
        {
          name: '启用',
          type: 0,
          format: function(row) {
            return ['启用', '禁用'][row.status];
          }
        },
        {
          name: '编辑',
          type: 1,
          disabled: function(row) {
            return !!row.status;
          }
        },
        {
          name: '查看',
          type: 2
        },
        {
          name: '复制',
          type: 3
        },
        {
          name: '删除',
          type: 4,
          disabled: function(row) {
            return !!row.status;
          }
        }
      ]
    };
  },
  created() {
    this.created = true;
  },
  activated() {
    if (!this.created) this.getList(this.listQuery);
    this.created = false;
  },
  methods: {
    // 获取表格数据
    getList: function(listQuery) {
      // pageNum, pageSize
      const { page, pageSize } = listQuery;

      getFormList({
        pageNum: page,
        pageSize,
        formName: this.listQuery.formName
      }).then(res => {
        if (res.code === 1) {
          const { list, total } = res.data;
          this.list = list;
          this.listQuery = {
            ...this.listQuery,
            total
          };
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 操作回调
    operationClick: function(type, row) {
      const { id, status, name } = row;
      const options = [
        row => {
          this.setStates(id, status, name);
        },
        row => {
          this.addNewForm(id);
        },
        row => {
          this.showDetail(id);
        },
        row => {
          this.copyForm(id);
        },
        row => {
          this.delForm(id);
        }
      ];
      options[type](row);
    },
    // 改变状态
    setStates(id, status, name) {
      status = status === 0 ? 1 : 0;
      if (status === 1 && String(name).length > 20) {
        this.$XyyMessage.error('名称过长,请先修改表单名称');
        return;
      }
      const msgs = ['禁用', '启用'];
      this.$XyyMsg({
        title: '提示',
        content: '确定' + msgs[status] + '此表单应用吗？', // html代码串
        onSuccess: () => {
          changeStatus({ id, status }).then(res => {
            if (res.code === 1) {
              this.$XyyMessage.success('表单应用已' + msgs[status]);
              this.getList(this.listQuery);
            } else {
              this.$XyyMessage.error(res.msg);
            }
          });
        }
      });
    },
    /**
     * 查看详情
     */
    showDetail(id) {
      this.$router.push({
        path: '/worksheet/editForm/' + id,
        query: { id, readOnly: true }
      });
    },
    // 删除表单
    delForm(id) {
      this.$XyyMsg({
        title: '提示',
        content: '确定删除此表单应用吗？',
        onSuccess: () => {
          delForm(id).then(res => {
            if (res.code === 1) {
              this.$XyyMessage.success('删除成功');
              this.getList(this.listQuery);
            } else {
              this.$XyyMsg({
                title: '提示',
                closeBtn: false,
                content: res.msg, // html代码串
                onSuccess: () => {}
              });
            }
          });
        }
      });
    },
    // 复制表单
    copyForm(id) {
      copyForm(id).then(res => {
        if (res.code === 1) {
          this.$XyyMessage.success('复制成功');
          this.getList(this.listQuery);
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 新建表单
    addNewForm(id) {
      let path;
      if (!id) {
        id = 'false';
        path = '/worksheet/newForm/' + new Date().getTime();
      } else {
        path = '/worksheet/editForm/' + id;
      }

      this.$router.push({
        path: path,
        query: { id }
      });
    },
    // 时间格式化
    getFormatDate: function(row, column, cellValue, index) {
      return new Date(cellValue + 8 * 3600 * 1000)
        .toJSON()
        .substr(0, 19)
        .replace('T', ' ');
    }
  }
};
</script>

<style scoped lang="scss">
.search-box {
  width: 344px;
  float: right;
  .el-input {
    width: calc(100% - 76px);
    /deep/ .el-input__inner {
      height: 36px;
      line-height: 36px;
    }
  }
  button {
    width: 68px;
    height: 36px;
    float: right;
  }
}
.status-tip {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 2px 2px 0;
  &.open {
    background: #67c23a;
  }
  &.close {
    background: #ff3024;
  }
}
</style>

