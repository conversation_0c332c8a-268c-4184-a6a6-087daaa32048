<template>
  <el-dialog
    :visible="status"
    title="商品信息查看"
    custom-class="product-detail-box"
    top="0"
    @close="close"
  >
    <el-form :disabled="true" :model="form" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="商品添加区域">
            <el-select v-model="form.branchCode" placeholder>
              <el-option
                v-for="area in areas"
                :key="area.id"
                :label="area.branchName"
                :value="area.branchCode"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="销售渠道">
            <el-input v-model="form.channelCode"></el-input>
          </el-form-item>-->
          <el-form-item label="商品编码">
            <el-input v-model="form.barcode"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="商品条码">
            <el-input v-model="form.code"></el-input>
          </el-form-item>
          <el-form-item label="商品推荐分类">
            <el-input v-model="form.categoryName"></el-input>
          </el-form-item>
          <el-form-item label="规格">
            <el-input v-model="form.spec"></el-input>
          </el-form-item>
          <el-form-item label="批准文号">
            <el-input v-model="form.approvalNumber"></el-input>
          </el-form-item>
          <el-form-item label="存储条件">
            <el-input v-model="form.storageCondition"></el-input>
          </el-form-item>
          <el-form-item label="件装量">
            <el-input v-model="form.pieceLoading"></el-input>
          </el-form-item>
          <el-form-item label="展示名称">
            <el-input v-model="form.showName"></el-input>
          </el-form-item>
          <el-form-item label="商品名称">
            <el-input v-model="form.productName"></el-input>
          </el-form-item>
          <el-form-item label="商品助记码">
            <el-input v-model="form.zjm"></el-input>
          </el-form-item>
          <el-form-item label="库存">
            <el-input v-model="form.availableQty"></el-input>
            <el-radio-group v-model="form.availableQtyType">
              <el-radio :label="1">真实库存</el-radio>
              <el-radio :label="2">虚拟库存</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="有效期">
            <el-input v-model="form.term"></el-input>
          </el-form-item>
          <el-form-item label="商品产地">
            <el-input v-model="form.producer"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生产厂家">
            <el-input v-model="form.manufacturer"></el-input>
          </el-form-item>
          <el-form-item label="活动类型">
            <el-select v-model="form.activityType" placeholder>
              <el-option :value="0" label="默认"></el-option>
              <el-option :value="2" label="双十活动"></el-option>
              <el-option :value="3" label="双十二活动"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="单位">
            <el-input v-model="form.productUnit"></el-input>
          </el-form-item>
          <el-form-item label="剂型">
            <el-input v-model="form.dosageForm"></el-input>
          </el-form-item>
          <el-form-item label="药品分类">
            <el-radio-group v-model="form.drugClassification">
              <el-radio :label="0">无</el-radio>
              <el-radio :label="1">甲类OTC</el-radio>
              <el-radio :label="2">乙类OTC</el-radio>
              <el-radio :label="3">处方药</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="中包装">
            <el-input v-model="form.mediumPackageNum"></el-input>
          </el-form-item>
          <el-form-item label="是否易碎品">
            <el-radio-group v-model="form.isFragileGoods">
              <el-radio :label="0">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="通用名称">
            <el-input v-model="form.commonName"></el-input>
          </el-form-item>
          <el-form-item label="是否可拆零">
            <el-radio-group v-model="form.isSplit">
              <el-radio :label="1">可拆零</el-radio>
              <el-radio :label="0">不可拆零</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="生产日期">
            <el-input v-model="form.productionDate"></el-input>
          </el-form-item>
          <el-form-item label="安全库存">
            <el-input v-model="form.safeQty"></el-input>
          </el-form-item>
          <el-form-item label="保质期">
            <el-input v-model="form.shelfLife"></el-input>
          </el-form-item>
          <el-form-item label="是否赠品">
            <el-radio-group v-model="form.isGive">
              <el-radio :label="0">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="商品定位">
            <el-input v-model="form.productPosition"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <!-- <el-form-item label="价格类型">
            <el-radio-group v-model="form.priceType">
              <el-radio :label="1">统一价格</el-radio>
              <el-radio :label="2">价格区间</el-radio>
            </el-radio-group>
          </el-form-item>-->
          <el-form-item label="药帮忙价">
            <el-input v-model="form.fob"></el-input>
          </el-form-item>
          <el-form-item label="个人限购数量">
            <el-input v-model="form.limitedQty"></el-input>
          </el-form-item>
          <el-form-item label="发布类目" label-width="100px"></el-form-item>
          <el-form-item label="一级类目">
            <el-input v-model="form.erpFirstCategoryName"></el-input>
          </el-form-item>
          <el-form-item label="二级类目">
            <el-input v-model="form.erpSecondCategoryName"></el-input>
          </el-form-item>
          <el-form-item label="三级类目">
            <el-input v-model="form.erpThirdCategoryName"></el-input>
          </el-form-item>
          <el-form-item label="零售价">
            <el-input v-model="priceComputed"></el-input>
            <el-radio-group v-model="priceTypeComputed">
              <el-radio :label="1">建议零售价</el-radio>
              <el-radio :label="2">控销零售价</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="商品类型">
            <el-radio-group v-model="form.oemType">
              <el-radio :label="1">普通商品</el-radio>
              <el-radio :label="2">OEM商品</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="限购时间">
            <el-date-picker
              v-model="limitDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="关联分类">
            <el-input v-model="form.skuRelationCategoryName"></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="form.status" placeholder>
              <el-option :value="1" label="销售中"></el-option>
              <el-option :value="2" label="已售罄"></el-option>
              <!-- <el-option :value="3" label="特惠中"></el-option> -->
              <el-option :value="4" label="下架"></el-option>
              <!-- <el-option :value="5" label="秒杀中"></el-option> -->
              <el-option :value="6" label="待上架"></el-option>
              <el-option :value="7" label="已录入"></el-option>
              <el-option :value="8" label="待审核"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否可用医保">
            <el-radio-group v-model="form.isUsableMedicalStr">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否一致性评价">
            <el-radio-group v-model="form.isConsistency">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否保健品">
            <el-radio-group v-model="form.isHealth">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否高毛">
            <el-radio-group v-model="form.highGross">
              <el-radio :label="2">是</el-radio>
              <el-radio :label="1">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="商品原图">
            <img :src="`${basePath}ybm/product/min/${form.imageUrl}`" />
            <img
              v-for="(img,i) in form.imagesList"
              :key="i"
              :src="`${basePath}ybm/product/pic/min/${img}`"
            />
          </el-form-item>
          <el-form-item label="说明书">
            <img
              v-for="(img,i) in form.skuInstructionImageList"
              :key="i"
              :src="`${basePath}ybm/product/desc/min/${img.instrutionImageUrl}`"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="22">
          <el-form-item label="备注">
            <el-input v-model="form.bz" type="textarea" rows="4" resize="none"></el-input>
          </el-form-item>
          <el-form-item label="商品描述">
            <el-input v-model="form.description" type="textarea" rows="4" resize="none"></el-input>
          </el-form-item>
          <el-form-item label="不良反应">
            <el-input v-model="form.untowardEffect" type="textarea" rows="4" resize="none"></el-input>
          </el-form-item>
          <el-form-item label="适应症/功能主治">
            <el-input v-model="form.indication" type="textarea" rows="4" resize="none"></el-input>
          </el-form-item>
          <el-form-item label="用法与用量">
            <el-input v-model="form.usageAndDosage" type="textarea" rows="4" resize="none"></el-input>
          </el-form-item>
          <el-form-item label="成分">
            <el-input v-model="form.component" type="textarea" rows="4" resize="none"></el-input>
          </el-form-item>
          <el-form-item label="禁忌">
            <el-input v-model="form.abstain" type="textarea" rows="4" resize="none"></el-input>
          </el-form-item>
          <el-form-item label="注意事项">
            <el-input v-model="form.considerations" type="textarea" rows="4" resize="none"></el-input>
          </el-form-item>
          <el-form-item label="药物互相作用">
            <el-input v-model="form.interaction" type="textarea" rows="4" resize="none"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button plain @click="close">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getProductData, getAreaDatas } from '@/api/ec/product-detail';
import { BASE_URL } from '@/utils/tools.js';
debugger
export default {
  props: {
    status: {
      type: Boolean,
      default: false
    },
    csuId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {
        branchCode: '', // 商品区域编码
        // channelCode: '', // 销售渠道
        barcode: '', // 商品编码
        code: '', // 商品条码
        categoryName: '', // 商品推荐分类
        spec: '', // 规格
        approvalNumber: '', // 批准文号
        storageCondition: '', // 存储条件
        pieceLoading: '', // 件装量
        showName: '', // 展示名称
        productName: '', // 商品名称
        zjm: '', // 商品助记码
        availableQty: '', // 库存
        availableQtyType: 1, // 库存类型 1-真是库存 2-虚拟库存
        term: '', // 有效期
        producer: '', // 商品产地
        manufacturer: '', // 生产厂家
        activityType: '', // 活动类型
        productUnit: '', // 商品单位
        dosageForm: '', // 剂型
        drugClassification: 0, // 药品分类 1-甲类 2-乙类 3处方药
        mediumPackageNum: '', // 中包装
        isFragileGoods: 0, // 是否易碎品 0-否 1-是
        commonName: '', // 通用名称
        isSplit: 0, // 是否可拆零 0-否 1-是
        productionDate: '', // 生产日期
        safeQty: '', // 安全库存
        shelfLife: '', // 保质期
        isGive: 0, // 是否赠品 0-否 1-是
        productPosition: '', // 商品定位
        // priceType: 1, // 价格类型 1-统一价格 2-价格区间
        fob: '', // 药帮忙价
        limitedQty: '', // 个人限购数量
        erpFirstCategoryName: '', // 一级类目名称
        erpSecondCategoryName: '', // 二级类目名称
        erpThirdCategoryName: '', // 三级类目名称
        suggestPrice: '', // 建议零售价
        uniformPrice: '', // 控销零售价
        oemType: 1, // 商品类型 1-普通商品 2-oem商品
        purchaseTimeStart: '', // 限购起始时间
        purchaseTimeEnd: '', // 限购结束时间
        skuRelationCategoryName: '', // 关联分类名称
        status: 1, // 状态 1-销售中 2-已售罄 3-特惠中 4-下架 5-秒杀中 6-待上架
        isUsableMedicalStr: 1, // 是否可用医保 0-否 1-是
        isConsistency: 0, // 是否一致性评价 0-否 1-是
        isHealth: 0, // 是否保健品 0-否 1-是
        highGross: 1, // 是否高毛 1-否 2-是
        imageUrl: '', // 商品主图
        imagesList: [], // 商品原图
        skuInstructionImageList: [], // 说明书原图
        bz: '', // 备注
        description: '', // 商品说明
        untowardEffect: '', // 不良反应
        indication: '', // 适应症
        usageAndDosage: '', // 用法与用量
        component: '', // 成分
        abstain: '', // 禁忌
        considerations: '', // 注意事项
        interaction: '' // 药物互相作用
      },
      areas: [],
      basePath: BASE_URL
    };
  },
  computed: {
    priceComputed() {
      return this.form.suggestPrice || this.form.uniformPrice;
    },
    priceTypeComputed() {
      return this.form.suggestPrice ? 1 : this.form.uniformPrice ? 2 : '';
    },
    limitDate() {
      return [
        this.form.purchaseTimeStart ? this.form.purchaseTimeStart : '',
        this.form.purchaseTimeEnd ? this.form.purchaseTimeEnd : ''
      ];
    }
  },
  watch: {
    status(val) {
      if (val) {
        this.getAreaDatas();
        this.getProductData();
      }
    }
  },
  methods: {
    close() {
      this.$emit('update:status', false);
    },
    getProductData() {
      getProductData({ csuId: this.csuId }).then(res => {
        if (res.code === 1) {
          for (const key in this.form) {
            this.form[key] = res.data[key];
          }
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    getAreaDatas() {
      getAreaDatas().then(res => {
        if (res.code === 1) {
          this.areas = res.data;
        }
      });
    }
  }
};
</script>

<style lang="scss">
.el-dialog.product-detail-box {
  width: 1200px;
  top: 50%;
  transform: translateY(-50%);
  .el-dialog__header {
    padding: 15px 20px;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: rgba(41, 41, 51, 1);
    .el-dialog__headerbtn {
      top: 17px;
    }
  }
  .el-dialog__body {
    padding: 20px;
    height: 395px;
    overflow-y: auto;
    .el-row {
      border-top: 1px solid rgba(228, 228, 235, 1);
      padding: 10px 0;
      .el-form-item__label {
        font-weight: normal;
      }
      .el-input {
        width: 220px;
      }
      .el-date-editor {
        /deep/.el-range-separator {
          width: 20px;
          padding: 0;
        }
      }
      img {
        width: 120px;
        height: 120px;
        margin: 0 5px 10px;
        float: left;
      }
    }
  }
  .el-dialog__footer {
    height: 56px;
    padding: 0 20px;
    box-sizing: border-box;
    .el-button {
      padding: 0 20px;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      font-weight: 400;
      font-family: PingFangSC-Regular, PingFang SC;
      &.el-button--default {
        border: 1px solid rgba(228, 228, 235, 1);
        color: rgba(87, 87, 102, 1);
        &:hover,
        &:focus {
          border: 1px solid rgba(228, 228, 235, 1);
          color: rgba(87, 87, 102, 1);
        }
      }
    }
  }
}
</style>
