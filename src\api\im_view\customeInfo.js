import request from '@/utils/request-im';

/* 绑定或取消绑定客户 uid:0为取消绑定 */
export function bindOrCancelUser(data) {
  return request({
    url: '/crm/bindUser',
    method: 'post',
    data: data
  });
}

/* 根据药店Id 查询基本信息 /crm/customerDetails/{memberId}*/

export function getCustomeDetails(params) {
  const url = '/crm/customerDetails';
  return request({
    url: url,
    method: 'get',
    params: params
  });
}
/*  根据药店Id 查询银行卡信息 */
export function getBankInfo(params) {
  return request({
    url: '/crm/bankinfoDetails/' + params.memberId,
    method: 'get'
    // params: params
  });
}
/*  根据药店名称模糊查询药店 */
export function getListMerchantByName(params) {
  return request({
    url: '/crm/listMerchantByName',
    method: 'get',
    params: params
  });
}

/**
 * 客服工作台豆芽销售信息查询
 */
export function getCrmSaleInfo(params) {
  return request({
    url: '/crm/getCrmSaleInfo',
    method: 'get',
    params
  })
}

/**
 * 客服端豆芽私海用户查询
 */
export function privateCustomerQuery(params) {
  return request({
    url: '/crm/privateCustomerQuery',
    method: 'get',
    params
  })
}

/**
 * 查询ca客户信息
 */
export function getExclusiveKeFuByCustomId(params) {
  return request({
    url: '/cAExclusiveKeFu/getExclusiveKeFuByCustomId',
    method: 'get',
    params
  })
}



