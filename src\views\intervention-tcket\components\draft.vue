<template>
  <el-dropdown :hide-on-click="false" trigger="click" @visible-change="visibleChange">
    <el-button plain class="sheetMainBtn">
      草稿箱
      <i class="el-icon-arrow-down el-icon--right"></i>
    </el-button>
    <el-dropdown-menu ref="draftMenu" slot="dropdown" class="dropDownBox">
      <el-dropdown-item v-if="tableData.length===0" divided command="a" class="dropDownItem">
        <el-row v-if="tableData.length===0" class="noData">暂无工单草稿</el-row>
      </el-dropdown-item>
      <el-dropdown-item
        v-for="(item,index) in tableData"
        v-else
        :key="index"
        divided
        command="a"
        class="dropDownItem"
      >
        <el-row class="content">{{ item.formTypeName }}-{{ item.formName }}</el-row>
        <el-row type="flex" justify="space-between" align="middle">
          <span class="timeTemp">{{ item.gmtModified| formatTime }}</span>
          <el-row type="flex" align="middle" class="btn">
            <el-button type="text" @click="handleEdit(item)">编辑</el-button>
            <span class="division">|</span>
            <el-button type="text" @click="handleDelDraft(item.id)">删除</el-button>
          </el-row>
        </el-row>
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  name: 'Draft',
  props: {
    tableData: {
      type: Array,
      default: function() {
        return [];
      }
    }
  },
  created() {
    const that = this;
    that.$nextTick(() => {
      const draftMenu = that.$refs.draftMenu.$el;
      draftMenu.onscroll = () => {
        const menuOffHig = draftMenu.offsetHeight;
        const menuScroTop = draftMenu.scrollTop;
        const menuScroHig = draftMenu.scrollHeight;
        if (menuOffHig + menuScroTop - menuScroHig > 0) {
          that.$emit('draftMenuLoad');
        }
      };
    });
  },
  methods: {
    visibleChange() {
      this.$emit('visibleChange');
    },
    handleEdit(id) {
      this.$emit('handleEdit', id);
    },
    handleDelDraft(itemId) {
      this.$emit('delDraft', itemId);
    }
  }
};
</script>
<style lang='scss' scoped>
.el-icon-arrow-down {
  color: #c0c4cc;
}
.sheetMainBtn {
  padding: 12px 12px;
}
.noData {
  text-align: center;
  font-size: 14px;
  color: #aeaebf;
}
/deep/.el-button {
  height: 36px;
}
.dropDownBox {
  width: 346px;
  min-height: 50px;
  max-height: 231px;
  overflow-y: auto;
}
.dropDownItem {
  margin: 0px 20px 5px;

  .content {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: rgba(41, 41, 51, 1);
    line-height: 20px;
  }
  .division {
    margin: 0 5px;
  }
  .timeTemp {
    color: #909399;
  }
  .btn {
    color: #3b95a8;
  }
}
.el-dropdown-menu__item:hover {
  background: none;
}
.el-dropdown-menu__item--divided:nth-child(1) {
  border-top: none;
}
</style>
