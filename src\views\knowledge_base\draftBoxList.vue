<template >
  <xyy-list-page class="draftBox-list">
    <template slot="header">
      <div>
        <el-form ref="queryList" :model="queryList" class="draftBox-list-box">
          <el-row type="flex" class="innerEl top_20" justify="space-between" align="middle">
            <el-form-item class="serverNum" label="知识标题" label-width="80px" prop="serveNum">
              <el-input
                v-model="queryList.title"
                class="serverName"
                size="small"
                placeholder="请输入标题"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                plain
                type="primary"
                size="medium"
                class="searchCondition"
                @click="checkTimer(handerSearch('queryList'))"
              >查询</el-button>
              <el-button plain size="medium" @click="resetForm">重置</el-button>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
    </template>

    <template slot="body">
      <el-row type="flex" justify="space-between" align="top">
        <xyy-table
          :data="list"
          :list-query="listQuery"
          :col="col"
          :operation="operation"
          @get-data="getList"
          @operation-click="operationClick"
        ></xyy-table>
      </el-row>
    </template>
  </xyy-list-page>
</template>

<script>
import {
  listKnowledgeManagementDraftBox,
  deleteKnowledgeManagementDraftBox,
  reportKnowledgeManagement,
  saveOrUpdateKnowledgeManagementDraftBox
} from '@/api/knowledge_base';
import utils from '@/utils/filter';
export default {
  name: 'DraftBoxList',
  data() {
    return {
      queryList: {
        title: ''
      },
      list: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      datas: {},
      col: [
        { index: 'title', name: '知识标题', resizable: true },
        { index: 'modifyTime', name: '修改时间', resizable: true },
        { index: 'modifyName', name: '修改人', resizable: true },
        {
          index: 'operation',
          name: '操作',
          width: 250,
          operation: true,
          resizable: true
        }
      ],
      operation: [
        {
          name: '存为知识',
          type: 0
        },
        {
          name: '编辑',
          type: 1
        },
        {
          name: '删除',
          type: 2
        }
      ],
      created: false
    };
  },
  created() {
    this.created = true;
  },
  activated() {
    if (!this.created) this.getList(this.listQuery);
    this.created = false;
  },
  mounted() {},
  methods: {
    handerSearch(formName) {
      this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      };
      this.getList(this.listQuery);
    },
    getList: function(listQuery) {
      const { page, pageSize } = listQuery;
      const params = {
        pageNum: page,
        pageSize,
        title: this.queryList.title
      };
      listKnowledgeManagementDraftBox(params)
        .then(res => {
          const { total } = res.data;
          this.list = res.data.records;
          let modifyTime = {};
          this.list.forEach((item, index) => {
            modifyTime = {};

            if (item.modifyTime == null) {
              modifyTime = '-';
            } else {
              modifyTime = utils.dataTime(item.modifyTime, 'yy-mm-dd HH:ss:nn');
            }
            this.list[index] = Object.assign({}, this.list[index], {
              modifyTime: modifyTime
            });
          });

          this.listQuery = {
            ...this.listQuery,
            page: Number(res.data.current),
            pageSize: Number(res.data.size),
            total: Number(res.data.total)
          };
          // this.listQuery = {
          //   ...this.listQuery,
          //   total: Number(total)
          // };
        })
        .catch(() => {});
    },
    operationClick: function(type, row) {
      switch (type) {
        case 0:
          this.reportKnowledgeManagement(row);
          break;
        case 1:
          this.toEdit(row);
          break;
        case 2:
          this.$XyyMsg({
            title: '提示',
            content: '确定要删除草稿吗?',
            onSuccess: () => {
              this.delData(row);
            }
          });
          break;
      }
    },
    // 删除草稿
    delData(row) {
      // 保存后
      deleteKnowledgeManagementDraftBox({ id: row.id })
        .then(res => {
          if (res.code === 1) {
            this.$XyyMessage.success('删除成功');
            this.getList(this.listQuery);
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(function(error) {});
    },
    // 存为知识
    reportKnowledgeManagement(row) {
      reportKnowledgeManagement({ id: row.id })
        .then(res => {
          if (res.code === 1) {
            this.$XyyMessage.success('保存成功');
            this.getList(this.listQuery);
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(function(error) {});
    },
    /**
     * 跳转到模板编辑页
     */
    toEdit(data) {
      if (data) {
        this.$router.replace({
          path: `/knowledge_base/knowledgeManagementEdit/${data.id}`,
          query: { templateId: data.id, isDraftBox: true }
        });
      } else {
        this.$router.replace({
          path: `/knowledge_base/knowledgeManagementEdit/${new Date().getTime()}`
        });
      }
    },

    resetForm() {
      (this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      }),
        (this.queryList = {
          title: ''
        });
      this.getList(this.listQuery);
    }
  }
};
</script>

<style lang="scss">
.draftBox-list {
  /deep/.page-body {
    width: 100%;
  }
  /deep/.table-containter {
    width: 100%;
    min-height: 400px;
  }
  /deep/.el-table__empty-block {
    width: 100 !important;
  }
  .draftBox-list-box {
    padding-bottom: 20px;
    margin-bottom: 10px;
    border-bottom: 1px dotted #e4e4eb;
    .innerEl {
      height: 38px;
      /deep/.el-input__inner {
        width: 100%;
        height: 36px;
        line-height: 36px;
      }
      /deep/.el-form-item {
        margin: 0;
      }
      .timeSel {
        width: 300px;
      }
      /deep/input[type='number']::-webkit-inner-spin-button,
      /deep/input[type='number']::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      .btnNew {
        color: #fff;
        background-color: rgba(59, 149, 168, 1);
      }
      .serverName {
        width: 200px;
      }
      .serverNum {
        margin-right: 20px;
      }
      .searchCondition.is-plain {
        background: rgba(59, 149, 168, 1);
        color: #fff;
      }
      &.top_20 {
        margin-top: 20px;
      }
    }
  }
}
</style>
