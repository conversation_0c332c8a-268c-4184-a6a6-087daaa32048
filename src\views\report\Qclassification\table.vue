<template>
  <el-table :data="data" @cell-click="onCellClick">
    <el-table-column
      prop="questionName"
      label="问题分类"
      width="180"
      show-overflow-tooltip
      class-name="info"
    >
    </el-table-column>
    <el-table-column prop="newlyAddNum" width="100">
      <template slot="header" slot-scope="{ row }">
        <p class="title">创建工单数</p>
        <p class="sub-title">当日新增</p>
      </template>
    </el-table-column>
    <el-table-column prop="newlyAddClosedNum" width="100">
      <template slot="header" slot-scope="{ row }">
        <p class="title">完成工单数</p>
        <p class="sub-title">当日新增</p>
      </template>
    </el-table-column>
    <el-table-column prop="historyClosedNum" width="100">
      <template slot="header" slot-scope="{ row }">
        <p class="title">完成工单数</p>
        <p class="sub-title">历史工单</p>
      </template>
    </el-table-column>
    <el-table-column prop="allClosedNum" width="100">
      <template slot="header" slot-scope="{ row }">
        <p class="title">完成工单数</p>
        <p class="sub-title">所有工单</p>
      </template>
    </el-table-column>
    <el-table-column prop="newlyAddClosedTime" width="130">
      <template slot="header" slot-scope="{ row }">
        <p class="title">平均完成时长(h)</p>
        <p class="sub-title">当日新增</p>
      </template>
    </el-table-column>
    <el-table-column prop="historyClosedTime" width="130">
      <template slot="header" slot-scope="{ row }">
        <p class="title">平均完成时长(h)</p>
        <p class="sub-title">历史工单</p>
      </template>
    </el-table-column>
    <el-table-column prop="allClosedTime" width="130">
      <template slot="header" slot-scope="{ row }">
        <p class="title">平均完成时长(h)</p>
        <p class="sub-title">所有工单</p>
      </template>
    </el-table-column>
    <el-table-column
      prop="averageProcessTime"
      label="平均处理时长"
      width="110"
    ></el-table-column>
    <el-table-column
      prop="totalTimeoutDuration"
      label="工单处理总超时长(h)"
      width="160"
    ></el-table-column>
    <el-table-column
      prop="timeoutNum"
      label="超时工单数"
      width="100"
      class-name="info"
    ></el-table-column>
    <el-table-column
      prop="unClosedNum"
      label="未完结工单"
      width="100"
      class-name="info"
    ></el-table-column>
    <el-table-column
      prop="timeoutRate"
      label="超时率"
      width="100"
    ></el-table-column>
    <el-table-column
      prop="closedInTimeRate"
      label="及时结案率"
      width="100"
    ></el-table-column>
    <el-table-column prop="handle" label="操作" width="100" class-name="info">
      <el-button slot-scope="{ row }" type="text">查看工单</el-button>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'QuestionCatagoryTable',
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  methods: {
    onCellClick({ questionCode }, column) {
      this.$emit('clickCell', { name: column.property, questionCode });
    }
  }
};
</script>

<style lang="scss" scoped>
.title,
.sub-title {
  margin: 0;
  text-align: right;
}
.sub-title {
  font-size: 12px;
  color: #909399;
}
.el-table {
  /deep/ .el-table__body {
    .info {
      color: #3b95a8;
      cursor: pointer;
    }
  }
}
</style>
