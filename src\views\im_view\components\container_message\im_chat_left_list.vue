<template>
  <div class="im_chat_left_list">
    <el-table
      :data="userDatas"
      :show-header="false"
      :border="false"
      :row-class-name="'im_container_leftview_table_row'"
      highlight-current-row
    >
      <el-table-column>
        <template slot-scope="scope">
          <div
            v-if="scope.row.select === true"
            :class="[scope.row.select === true ? 'im_container_leftview_userlist_select' : 'im_container_leftview_userlist']"
            @click="rowclickAction(scope)"
          >
            <img :src="scope.row.avatar" class="im_container_leftview_useravatar" />
            <div
              :class="selectNumber === 1?'im_container_leftview_userlist_right_1':'im_container_leftview_userlist_right'"
              class="userlist_right"
            >
              <div class="im_container_leftview_userlist_nametime">
                <div
                  class="im_container_leftview_userlist_nametimeval"
                  style="display:none;"
                >{{ scope.row.name }}</div>
                <el-tooltip :content="scope.row.name" placement="top">
                  <div
                    class="im_container_leftview_userlist_name"
                  >{{ getSubStr(scope.row.name) }}</div>
                </el-tooltip>
                <div
                  class="im_container_leftview_userlist_time"
                >{{ timechange(selectNumber === 1?scope.row.starttime:scope.row.lastMsgTime) }}</div>
              </div>
              <div
                v-if="selectNumber !== 1"
                class="im_container_leftview_userlist_content"
              >{{ getMessageText(scope.row.content) }}</div>
              <div class="im_container_leftview_userlist_state">{{ scope.row.state }}</div>
            </div>
            <div
              v-if="scope.row.unreadNum!==0"
              class="im_container_leftview_badge"
            >{{ scope.row.unreadNum>99?'99+':scope.row.unreadNum }}
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="bottonLoading" class="loading">
      <div>加载完毕</div>
    </div>
  </div>
</template>
<script>
export default {
  inheritAttrs: false,
  props: [],
  data() {
    return {
      
    }
  },
}
</script>
<style lang="scss" scoped>
@mixin wh($width: 100%, $height: 100%) {
  width: $width;
  height: $height;
}
@mixin flexDir($display: flex, $flexDirection: row) {
  display: $display;
  flex-direction: $flexDirection;
}
@mixin textOverflow() {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
@mixin flexCenter() {
  display: nowrap;
  justify-content: center;
  align-items: center;
}
.im_chat_left_list {
  @include wh();
  .im_container_leftview_userlist {
    @include flexDir();
    width: 100%;
    background-color: white;
    padding-left: 10px;
    padding-right: 10px;
  }
  .im_container_leftview_userlist.active {
    background-color: #dae9ec;
  }
  .im_container_leftview_userlist_select {
    @include flexDir();
    width: 100%;
    background-color: #dae9ec;
    padding-left: 10px;
    padding-right: 10px;
  }

  .im_container_leftview_useravatar {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    margin-top: 14px;
    margin-right: 5px;
  }
  .userlist_right {
    @include flexDir($display: flex, $flexDirection: column);
    width: 100%;
    border-bottom: 0.5px solid #e4e4eb;
  }
  .im_container_leftview_userlist_right {
    height: 100px;
  }
  .im_container_leftview_userlist_right_1 {
    height: 80px;
  }

  .im_container_leftview_userlist_nametime {
    @include flexDir();
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-top: 8px;
    padding-right: 5px;
  }

  .im_container_leftview_userlist_name {
    font-size: 12px;
    width: 50px;
    color: #292933;
    line-height: 20px;
  }

  .im_container_leftview_userlist_time {
    @include flexDir;
    align-items: center;
    color: #909399;
    padding-right: 5px;
    height: 20px;
    font-size: 12px;
  }

  .im_container_leftview_userlist_content {
    width: 100px;
    margin-top: 5px;
    line-height: 20px;
    font-size: 12px;
    color: #575766;
    @include textOverflow();
  }

  .im_container_leftview_userlist_content p {
    font-size: 12px;
    color: #575766;
    @include textOverflow();
  }

  .im_container_leftview_userlist_state {
    @include flexDir();
    align-items: center;
    height: 30px;
    margin-top: 10px;
    font-size: 12px;
    color: #909399;
  }

  .im_container_leftview_usertable {
    width: 100%;
    /* height: 540px; */
    background-color: white;
    overflow: auto;
  }

  .im_container_leftview_table_row {
    @include flexDir($display: flex, $flexDirection: column);
    @include wh($width: 100%, $height: 10px);
  }

  .im_container_leftview_table_row_1 {
    @include flexDir($display: flex, $flexDirection: column);
    @include wh($width: 100%, $height: 60px);
  }

  .im_container_leftview_badge {
    position: absolute;
    right: 10px;
    top: 40%;
    color: white;
    background-color: red;
    font-size: 12px;
    height: 18px;
    @include flexCenter();
    padding-left: 6px;
    padding-right: 6px;
    border-radius: 9px;
  }

  .im_container_header_avatar .littleBrage {
    @include wh($width: 10px, $height: 10px);
    border-radius: 5px;
    position: relative;
    left: 40px;
    bottom: 10px;
  }

  .im_container_chooseview .im_container_chooseview_alertlist {
    position: absolute;
    left: 220px;
    top: 170px;
    z-index: 60;
  }
  .im_container_view .im_container_chooseview_alert {
    position: absolute;
    left: 160px;
    top: 180px;
    z-index: 60;
  }

  .im_container_leftview_usertable::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }
  .im_container_leftview_usertable::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #f5f7fa;
  }
  .im_container_leftview_usertable::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    background: #fff;
  }
}
</style>
