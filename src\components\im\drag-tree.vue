<template>
  <div class="drag-tree-box">
    <el-tree
      ref="drag-tree"
      :data="list"
      :render-content="renderContent"
      :expand-on-click-node="false"
      :props="props"
      :allow-drop="allowDrop"
      :default-expanded-keys="expandedKeys"
      node-key="id"
      draggable
      @node-drop="handleDrop"
      @node-click="selectNode"
    ></el-tree>
  </div>
</template>

<script>
export default {
  props: {
    props: {
      type: Object,
      default: () => {
        return {
          label: 'label',
          children: 'children'
        };
      }
    },
    list: {
      type: Array,
      default: () => []
    },
    expandedKeys: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    allowDrop(dragNode, dropNode, type) {
      let pass = false;
      if (dragNode.level === dropNode.level && type !== 'inner') {
        pass = true;
      } else if (dragNode.level < dropNode.level && type === 'inner') {
        pass = true;
      }
      return pass;
    },
    handleDrop(dragNode, dropNode, type, e) {
      // console.log(dragNode, dropNode);
      this.$emit('dropCallback', {
        dragNode: dragNode.data,
        dropNode: dropNode.data,
        list: this.list
      });
    },
    renderContent(h, { node, data, store }) {
      const min = 'el-icon-minus';
      const plus = 'el-icon-plus';
      const label = 'drag-tree-label';
      const minTitle = node.level === 1 ? '删除分类' : '删除问题';
      const plusTitle = '新增问题';
      if (node.level === 1) {
        return (
          <span>
            <span class={label}>{node.label}</span>
            <i
              class={min}
              title={minTitle}
              on-click={() => {
                this.$emit('delCallback', data);
              }}
            ></i>
            <i
              class={plus}
              title={plusTitle}
              on-click={e => {
                this.$emit('addChildCallback', data);
                e.cancelBubble = true;
              }}
            ></i>
          </span>
        );
      } else {
        return (
          <span>
            <span class={label}>{node.label}</span>
            <i
              class={min}
              title={minTitle}
              on-click={() => {
                this.$emit('delChildCallback', data);
              }}
            ></i>
          </span>
        );
      }
    },
    selectNode(data, node, obj) {
      this.$emit('clickCallback', { data, node });
    }
  }
};
</script>

<style lang="scss" scoped>
div.drag-tree-box {
  .el-tree {
    /deep/.drag-tree-label {
      margin-right: 20px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
    }
    /deep/.el-icon-minus,
    /deep/.el-icon-plus {
      border: 1px solid #3b95a8;
      font-size: 14px;
      color: #3b95a8;
      margin-right: 12px;
      display: none;
    }
    /deep/.el-tree-node__content:hover {
      background: #f5f7fa;
    }
    /deep/.el-tree-node:focus {
      > .el-tree-node__content {
        background: #dae9ec;
      }
    }
    /deep/.el-tree-node.is-current {
      > .el-tree-node__content {
        background: #dae9ec;
        .el-icon-minus,
        .el-icon-plus {
          display: inline;
        }
      }
    }
  }
}
</style>
