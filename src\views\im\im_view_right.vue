<template>
  <div class="im-config">
    <el-tabs v-model="activeTab" type="card" class="im-tabs">
      <el-tab-pane v-if="chatState == 1" label="常用" name="usual">
        <common-use></common-use>
      </el-tab-pane>
      <el-tab-pane label="客户信息" name="customer">
        <customer-info ref="customerInfo" @refreshChatInfo="refreshChatInfo"></customer-info>
      </el-tab-pane>
      <el-tab-pane label="订单信息" name="order" style="height: calc(100% + 15px);margin: -15px -20px 0;">
        <customer-order ref="customerOrder"></customer-order>
      </el-tab-pane>
      <el-tab-pane label="工单管理" name="workorder" :lazy="true">
        <workorder ref="workorder"></workorder>
      </el-tab-pane>
      <el-tab-pane label="聊天记录" name="record" :lazy="true">
        <chat-record></chat-record>
      </el-tab-pane>
      <el-tab-pane label="服务小结" name="service" :lazy="true">
        <service-config-form ref="serviceConfigForm"></service-config-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import commonUse from '../im_view/components/commonUse';
import CustomerInfo from './components/customerInfo';
import CustomerOrder from './components/customerOrder';
import chatRecord from './components/chatrecord';
import serviceConfigForm from './components/serviceConfigForm';
import workorder from './components/workOrderManage';
// import { getConversation } from '@/api/configuration/RegionalMenu';

// import { log } from 'util';
export default {
  components: {
    commonUse,
    CustomerInfo,
    CustomerOrder,
    chatRecord,
    serviceConfigForm,
    workorder,
  },
  provide() {
    return {
      compIMViewRight: this,
    };
  },
  data() {
    return {
      activeTab: 'usual',
      chatInfo: {},
      chatState: 0, // 1：会话中,2：其他
      type: '',
    };
  },
  mounted() { },
  methods: {
    tohistory() {
      this.activeTab = 'record';
    },
    /**
     * 会话选中事件
     */
    rightSelect({ type, userId, chat, kefuInfo }) {
      const that = this;
      that.type = type;
      that.chatInfo = chat;
      this.$refs.customerInfo.init({ chat, userId }); //初始化客户信息
      this.$refs.customerOrder.init({ type, chat, userId, kefuInfo }); //初始化订单信息
      if (that.$refs.workorder) {
        that.$refs.workorder.init(chat.id);
      }
      //服务小结
      if (that.$refs.serviceConfigForm) {
        //that.$refs.serviceConfigForm.serviceConfigFormSelect(chat.customNickName); // 选中会话更新服务小结的会话信息
        that.$refs.serviceConfigForm.init(chat.customNickName);
      }

      // 会话中
      if (type === '0' && that.chatInfo.dialogEndScene === 0) {
        that.chatState = 1;
      } else {
        // 其他
        that.chatState = 2;
        that.activeTab =
          that.activeTab === 'usual' ? 'customer' : that.activeTab;
      }
    },

    /**
     * 重置数据
     */
    resetRight() {
      Object.assign(this.$data, this.$options.data()); // 重置data数据
      this.$refs.customerInfo.reset(); //重置客户信息
      this.$refs.customerOrder.reset(); //重置订单信息
    },

    /**
     * 更新绑定客户后的会话信息
     */
    refreshChatInfo(params) {
      this.$parent.refreshChatInfo({ tab: this.type, chat: params }); // 更新绑定客户后的会话信息
      //this.$refs.serviceConfigForm.serviceConfigFormSelect(params.customName); // 更新绑定客户后的服务小结的会话信息
      that.$refs.serviceConfigForm.init(chat.customNickName);
    },
  },
};
</script>

<style lang="scss" scoped>
.im-config {
  padding: 16px 16px;
  height: 100%;

  .el-tabs {
    min-width: 468px;
    max-width: 534px;

    @media screen and (min-width: 1024px) and (max-width: 1919px) {
      max-width: 468px;
    }

    width: 100%;
    height: 100%;
    margin: 0 auto;
  }
}
</style>
<style lang="scss">
.im-tabs>.el-tabs__header {
  margin-bottom: 0 !important;
}

.im-tabs>.el-tabs__header .el-tabs__nav {
  width: 100%;
  border-radius: 0;
  display: flex;

  overflow-x: auto;

  //滚动条样式
  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 1px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 5px;
  }

  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #f5f7fa;
  }

  &::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    background: #fff;
  }
}

.im-tabs>.el-tabs__header .el-tabs__nav .el-tabs__item {
  background: rgba(245, 247, 250, 1);
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(144, 147, 153, 1);
  border-color: #dcdee3;
  padding: 0 !important;
  min-width: 82px;
  box-sizing: border-box;
  text-align: center;
  flex-grow: 1;

  &.is-active {
    color: rgba(41, 41, 51, 1);
    background: #fff;
    border-bottom: none;
  }
}

.im-tabs>.el-tabs__content {
  height: calc(100% - 40px);
  border: 1px solid rgba(220, 222, 227, 1);
  border-top: 0;
  padding: 15px 20px 0;
  overflow-y: auto;

  .el-tab-pane {
    height: 100%;
  }
}
</style>
