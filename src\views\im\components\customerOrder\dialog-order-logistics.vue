<template>
  <!-- 订单物流信息弹窗 -->
  <el-dialog class="dialog-order-logistics-container" width="900px" append-to-body destroy-on-close :show-close="false"
    :visible.sync="dialogVisible" v-if="dialogVisible">
    <div class="dialog-body">
      <div class="dialog-title">
        <span class="title-txt">物流信息</span>
        <i class="el-icon-close" @click="handleCloseDialog"></i>
      </div>
      <div class="dialog-content">
        <div class="content-box" v-if="packages.length">
          <div class="logistics-info">
            <span class="log-inf-txt">订单编号：{{ orderNo }}</span>
            <span class="log-inf-txt">配送方式：{{ company }}</span>
            <span class="log-inf-txt">包裹数：{{ packages.length }}</span>
          </div>
          <div class="logistics-package">
            <div class="package-tab">
              <template v-for="(item, index) in packages">
                <span :class="['tab-item', index == packageIndex ? 'active' : '']"
                  @click="handleChangePackage(index)">包裹{{
                    index + 1 }}</span>
              </template>
            </div>
            <div class="package-content">
              <span class="package-num">运单号：{{ packages[packageIndex].logisticsNo }}</span>
              <el-timeline>
                <el-timeline-item v-for="(item, index) in packages[packageIndex].logisticsNodeList" :key="index">
                  <div class="item-timeline-content">
                    <span class="timeline-timestamp">{{ formatDatetime(item.time) }}</span>
                    <span class="timeline-content-txt">{{ item.description }}</span>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </div>
        <div class="empty-box" v-else>
          <img class="empty-icon" src="@/assets/common/icon-empty.png" />
          <span class="empty-txt">暂无物流信息</span>
        </div>
      </div>
      <div class="dialog-footer">
        <el-button class="btn-primary" @click="handleCloseDialog">关闭</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { queryLogisticsInfo } from '@/api/im_view/customeOrder';
export default {
  name: "dialog-order-logistics",
  data() {
    return {
      dialogVisible: false,
      orderNo: '',
      company: '',
      packageIndex: 0, // 包裹index
      packages: [],
    }
  },
  methods: {
    init({ orderNo }) {
      this.company = ""
      this.packageIndex = 0
      this.packages = []
      this.queryOrderLogistics({ orderNo })
      this.$nextTick(() => {
        this.dialogVisible = true;
      })
    },

    // 查询物流信息
    async queryOrderLogistics({ orderNo }) {
      try {
        const { code, data, msg } = await queryLogisticsInfo({ orderNo })
        if (code == 1) {
          if (data) {
            this.orderNo = data.orderNo
            this.company = data.logisticsCompany
            this.packageIndex = 0
            if (Array.isArray(data.logisticsPackages) && data.logisticsPackages.length) {
              this.packages = data.logisticsPackages
            } else {
              this.packages = []
            }
          }
        } else {
          throw new Error(msg)
        }
      } catch (e) {
        this.$XyyMessage.error(e.message);
      } finally { }
    },

    // 关闭弹窗
    handleCloseDialog() {
      this.dialogVisible = false;
    },

    // 切换包裹
    handleChangePackage(index) {
      this.packageIndex = index
    },

    // 转时间戳
    formatDatetime(timestamp) {
      if (!isNaN(timestamp)) {
        const timestampDate = new Date(timestamp)
        const year = timestampDate.getFullYear()
        const month = (timestampDate.getMonth() + 1).toString().padStart(2, '0')
        const date = timestampDate.getDate().toString().padStart(2, '0')
        const hour = timestampDate.getHours().toString().padStart(2, '0')
        const minute = timestampDate.getMinutes().toString().padStart(2, '0')
        const second = timestampDate.getSeconds().toString().padStart(2, '0')
        return `${year}-${month}-${date} ${hour}:${minute}:${second}`
      } else {
        return timestamp
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-order-logistics-container {
  /deep/ {
    .el-dialog {
      border-radius: 4px;

      .el-dialog__header {
        display: none;
      }

      .el-dialog__body {
        width: 100%;
        padding: 20px;
        box-sizing: border-box;

        .dialog-body {
          width: 100%;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: stretch;

          .dialog-title {
            width: 100%;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .title-txt {
              flex-grow: 1;
              color: #222222;
              font-size: 16px;
              font-weight: 400;
            }

            .el-icon-close {
              flex-shrink: 0;
              cursor: pointer;
              font-size: 16px;
              font-weight: bold;

              &:hover {
                opacity: 0.6;
              }
            }
          }

          .dialog-content {
            width: 100%;
            margin-bottom: 20px;

            .content-box {
              width: 100%;
              display: flex;
              flex-direction: column;
              justify-content: flex-start;
              align-items: stretch;

              .logistics-info {
                width: 100%;
                margin-bottom: 16px;
                display: flex;
                justify-content: flex-start;
                align-items: center;

                .log-inf-txt {
                  margin-right: 40px;
                  color: #222222;
                  font-size: 14px;
                  font-weight: 400;
                }
              }

              .logistics-package {
                width: 100%;
                min-height: 322px;
                max-height: 460px;
                border: 1px solid #d9dee3;
                border-radius: 2px;
                overflow: hidden;
                display: flex;
                justify-content: flex-start;
                align-items: stretch;

                .package-tab {
                  flex-shrink: 0;
                  width: 88px;
                  border-right: 1px solid #d9dee3;
                  overflow-y: auto;
                  display: flex;
                  flex-direction: column;
                  justify-content: flex-start;
                  align-items: stretch;

                  .tab-item {
                    width: 100%;
                    padding: 13px 0;
                    box-sizing: border-box;
                    text-align: center;
                    line-height: 1;
                    cursor: pointer;
                    background-color: #fff;
                    color: #666666;
                    font-size: 14px;
                    font-weight: 400;

                    &.active {
                      background-color: #dae9ec;
                      color: #3B95A8;
                      font-weight: 500;
                    }
                  }
                }

                .package-content {
                  flex-grow: 1;
                  overflow-y: auto;
                  padding: 16px;
                  box-sizing: border-box;
                  display: flex;
                  flex-direction: column;
                  justify-content: flex-start;
                  align-items: stretch;

                  .package-num {
                    width: 100%;
                    line-height: 1;
                    margin-bottom: 16px;
                    color: #222222;
                    font-size: 14px;
                    font-weight: 400;
                  }

                  .el-timeline {
                    width: 100%;
                    padding: 0;

                    .el-timeline-item {
                      width: 100%;
                      padding: 0 0 20px 0;

                      .el-timeline-item__node {
                        background-color: #fff;
                        border: 1px solid #C9D1DA;
                        width: 8px;
                        height: 8px;
                        left: 1px;
                      }

                      &:first-child {
                        .el-timeline-item__node {
                          background-color: #3B95A8;
                          border: 1px solid #3B95A8;
                        }

                        &::before {
                          content: '';
                          width: 14px;
                          height: 14px;
                          background-color: #dae9ec;
                          border-radius: 50%;
                          position: absolute;
                          top: -3px;
                          left: -2px;
                        }
                      }

                      .el-timeline-item__wrapper {
                        padding-left: 20px;

                        .el-timeline-item__content {
                          .item-timeline-content {
                            width: 100%;
                            display: flex;
                            justify-content: flex-start;
                            align-items: flex-start;

                            .timeline-timestamp {
                              flex-shrink: 0;
                              color: #888888;
                              font-size: 14px;
                              font-weight: 400;
                            }

                            .timeline-content-txt {
                              margin-left: 32px;
                              color: #222222;
                              font-size: 14px;
                              font-weight: 400;
                            }
                          }
                        }

                        .el-timeline-item__timestamp {
                          display: none;
                        }
                      }
                    }
                  }
                }
              }
            }

            .empty-box {
              width: 100%;
              padding: 60px 0;
              display: flex;
              flex-direction: column;
              justify-content: flex-start;
              align-items: center;

              .empty-icon {
                width: 180px;
                height: 150px;
                margin-bottom: 20px;
              }

              .empty-txt {
                color: #666666;
                font-size: 16px;
                font-weight: 400;
              }
            }
          }

          .dialog-footer {
            width: 100%;
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .el-button {
              padding: 9px 18px;
              border-radius: 4px;
              font-size: 14px;
              font-weight: 500;

              &:hover {
                opacity: 0.6;
              }

              &:active {
                opacity: 1;
              }

              +.el-button {
                margin-left: 16px;
              }

              &.btn-primary {
                color: #ffffff;
                background-color: #3B95A8;
                border-color: #3B95A8;
              }
            }
          }
        }
      }
    }
  }
}
</style>