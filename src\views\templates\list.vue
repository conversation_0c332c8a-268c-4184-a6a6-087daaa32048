<template>
  <xyy-list-page>
    <template slot="header">
      <xyy-button icon-class="btn-add"
                  class-name="btn-add-icon"
                  @click="toBase">新建模板</xyy-button>
      <xyy-button icon-class="enable"
                  type="normal"
                  @click="enableDatas">批量启用</xyy-button>
      <xyy-button icon-class="disable"
                  type="normal"
                  @click="disableDatas">批量禁用</xyy-button>
      <div class="search-box">
        <el-input v-model="listQuery.templateName"
                  placeholder="请输入模板名称"
                  maxlength="20" />
        <el-button type="primary"
                   @click="getList(listQuery)">搜索</el-button>
      </div>
    </template>
    <template slot="body">
      <description :info="info"
                   class="help-msg-box"></description>
      <xyy-table :data="list"
                 :list-query="listQuery"
                 :col="col"
                 :operation="operation"
                 :has-selection="true"
                 @get-data="getList"
                 @operation-click="operationClick"
                 @selectionCallback="setCheckedDatas">
        <template slot="type"
                  slot-scope="{col}">
          <el-table-column :key="col.index"
                           :prop="col.index"
                           :label="col.name"
                           :width="col.width"
                           :formatter="typeFormat" />
        </template>
        <template slot="gmtModified"
                  slot-scope="{col}">
          <el-table-column :key="col.index"
                           :prop="col.index"
                           :label="col.name"
                           :width="col.width"
                           :formatter="getFormatDate" />
        </template>
        <template slot="status"
                  slot-scope="{col}">
          <el-table-column :key="col.index"
                           :label="col.name"
                           :width="col.width">
            <template slot-scope="{row}">
              <span :class="'status-tip '+(row.status === 1?'open':'close')"></span>
              {{ row.status === 1?'启用':'禁用' }}
            </template>
          </el-table-column>
        </template>
      </xyy-table>
    </template>
  </xyy-list-page>
</template>

<script>
import {
  getTemplateList,
  changeTemplateStatus,
  copyTemplateData,
  delTemplateData,
  enableTemplateDatas,
  disableTemplateDatas
} from '@/api/templates';

import description from '@/components/tools/description.vue';
/* 查询关联 */
// import { listTemplate } from '../../api/fields/fields-comment';

export default {
  name: 'TemplateList',
  components: {
    description
  },
  data () {
    return {
      info: [
        { title: '', info: '模板即发起工单/处理工单填写的表单' },
        {
          title: '启用/禁用',
          info:
            '禁用即失效状态，禁用时会判断当前模板是否被节点或问题分类引用，如果使用中则不能禁用；启用即可用状态，启用状态的模板才可以引用到节点'
        },
        { title: '编辑', info: '可修改模板的配置，但不能修改模板类型' },
        {
          title: '复制',
          info: '对当前模板进行复制，生成一条新模板，模板设置与原模板一致'
        },
        {
          title: '删除',
          info: '与禁用的判断逻辑一致。删除后，模板从页面消失'
        }
      ],
      list: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0,
        templateName: ''
      },
      col: [
        {
          index: 'name',
          name: '模板名称',
          ellipsis: true,
          resizable: true,
          width: 300
        },
        {
          index: 'type',
          name: '模板类型',
          width: 130,
          slot: true,
          resizable: true
        },
        {
          index: 'description',
          name: '备注说明',
          width: 200,
          ellipsis: true,
          resizable: true
        },
        { index: 'gmtModified', name: '更新时间', slot: true, resizable: true },
        { index: 'editorName', name: '更新人', width: 130, resizable: true },
        {
          index: 'status',
          name: '状态',
          width: 90,
          slot: true,
          resizable: true
        },
        {
          index: 'operation',
          name: '操作',
          width: 220,
          operation: true,
          resizable: true
        }
      ],
      operation: [
        {
          name: '启用',
          type: 0,
          format: function (row) {
            return ['启用', '禁用'][row.status];
          }
        },
        {
          name: '编辑',
          type: 1
        },
        {
          name: '复制',
          type: 2
        },
        {
          name: '删除',
          type: 3,
          disabled: function (row) {
            return !!row.status;
          }
        }
      ],
      // 选中行
      selections: []
    };
  },
  activated () {
    this.getList(this.listQuery);
  },
  methods: {
    /**
     * 设置选中数据
     */
    setCheckedDatas (datas) {
      this.selections = datas.map(el => el.id);
    },
    /**
     * 跳转到模板基础信息页
     */
    toBase (data) {
      if (data) {
        const old = this.$store.state.tagsView.visitedViews.filter(
          el =>
            (el.name === 'templateEditor' || el.name === 'templateConfig') &&
            el.params.id === data.id
        )[0]; // 同一模板 处于第二步、第三步 覆盖当前模板
        const view = this.$router.options.routes
          .filter(el => el.name === 'worksheet')[0]
          .children.filter(el => el.name === 'templateBase')[0];
        if (old) {
          const path = `/worksheet/templateBase/${data.id}`;
          const query = { templateId: data.id, isUpdate: true };
          this.updateTab(
            {
              ...view,
              path,
              query,
              fullPath: this.getFullPath(path, query),
              meta: {
                title: '编辑模板'
              }
            },
            old
          );
        } else {
          this.$router.replace({
            path: `/worksheet/templateBase/${data.id}`,
            query: { templateId: data.id, isUpdate: true }
          });
        }
      } else {
        this.$router.replace({
          path: `/worksheet/templateBase/${new Date().getTime()}`
        });
      }
    },
    updateTab (view, old) {
      this.$store
        .dispatch('tagsView/updateCurrentView', {
          old: old,
          view
        })
        .then(() => {
          const { fullPath } = view;
          this.$nextTick(() => {
            this.$router.replace({
              path: fullPath
            });
          });
        });
    },
    getFullPath (path, query) {
      const params = [];
      for (const key in query) {
        params.push(key + '=' + query[key]);
      }
      return path + '?' + params.join('&');
    },
    getList: function (listQuery) {
      const { page, pageSize } = listQuery;
      getTemplateList({
        pageNum: page,
        pageSize,
        templateName: this.listQuery.templateName
      })
        .then(res => {
          if (res.code === 1) {
            const { list, total } = res.data;
            this.list = list;
            this.listQuery = {
              ...this.listQuery,
              total
            };
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => { });
    },
    operationClick: function (type, row) {
      switch (type) {
        case 0:
          if (row.status === 0 && String(row.name).length > 20) {
            this.$XyyMessage.error('名称过长,请先修改模板名称');
            return;
          }
          this.$XyyMsg({
            title: '提示',
            content: `是否${row.status === 0 ? '启用' : '禁用'}?`,
            onSuccess: () => {
              this.changeStatus(row);
            }
          });
          break;
        case 1:
          this.toBase(row);
          break;
        case 2:
          this.copyData(row);
          break;
        case 3:
          this.$XyyMsg({
            title: '提示',
            content: '确定要删除模板吗?',
            onSuccess: () => {
              this.delData(row);
            }
          });
          break;
      }
    },
    /**
     * 更改模板状态
     */
    changeStatus (data) {
      changeTemplateStatus({
        id: data.id,
        status: data.status === 0 ? 1 : 0
      }).then(res => {
        if (res.code === 1) {
          const msg = data.status === 0 ? '启用' : '禁用';
          this.$XyyMessage.success(`模板已${msg}`);
          // 刷新列表
          this.getList(this.listQuery);
        } else {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: res.msg, // html代码串
            onSuccess: () => { }
          });
        }
      });
    },
    /**
     * 复制模板数据
     */
    copyData (data) {
      copyTemplateData(data.id).then(res => {
        if (res.code === 1) {
          this.$XyyMessage.success('复制成功');
          // 刷新列表
          this.getList(this.listQuery);
        } else {
          this.$XyyMessage.error(res.msg);
          // this.$XyyMsg({
          //   title: '提示',
          //   closeBtn: false,
          //   content: res.msg, // html代码串
          //   onSuccess: () => {}
          // });
        }
      });
    },
    /**
     * 删除模板数据
     */
    delData (data) {
      delTemplateData(data.id).then(res => {
        if (res.code === 1) {
          this.$XyyMessage.success('删除成功');
          // 刷新列表
          this.getList(this.listQuery);
        } else if (res.code === 9) {
          // this.$XyyMessage.error(res.msg);

          // 查询字段是否被关联
          let html = '当前正有表单应用使用此模板，请先修改表单应用( ';
          html += `<span style="color: red"> ${res.msg}</span>`;
          html += ' )';
          this.$XyyMsg({
            title: '提示',
            content: html,
            closeBtn: false,
            onSuccess: function () { }
          });
        } else {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: res.msg, // html代码串
            onSuccess: () => { }
          });
        }
      });
    },
    getFormatDate (row, column, cellValue, index) {
      return new Date(cellValue + 8 * 3600 * 1000)
        .toJSON()
        .substr(0, 19)
        .replace('T', ' ');
    },
    typeFormat (row, column, cellValue, index) {
      return cellValue === 0 ? '发起' : '处理或关闭';
    },
    /**
     * 批量启用
     */
    enableDatas () {
      if (this.selections.length) {
        this.$XyyMsg({
          title: '提示',
          content: `确定批量启用这些模板吗？`,
          onSuccess: () => {
            enableTemplateDatas(this.selections).then(res => {
              if (res.code === 1) {
                // 刷新列表
                this.getList(this.listQuery);
                this.$XyyMsg({
                  title: '提示',
                  closeBtn: false,
                  content: res.msg, // html代码串
                  onSuccess: () => { }
                });
              } else {
                this.$XyyMsg({
                  title: '提示',
                  closeBtn: false,
                  content: res.msg, // html代码串
                  onSuccess: () => { }
                });
              }
            });
          }
        });
      } else {
        this.$XyyMessage.warning('无选中数据');
      }
    },
    /**
     * 批量禁用
     */
    disableDatas () {
      if (this.selections.length) {
        this.$XyyMsg({
          title: '提示',
          content: `确定批量禁用这些模板吗？`,
          onSuccess: () => {
            disableTemplateDatas(this.selections).then(res => {
              if (res.code === 1) {
                // 刷新列表
                this.getList(this.listQuery);
                this.$XyyMsg({
                  title: '提示',
                  closeBtn: false,
                  content: res.msg, // html代码串
                  onSuccess: () => { }
                });
              } else {
                this.$XyyMsg({
                  title: '提示',
                  closeBtn: false,
                  content: res.msg, // html代码串
                  onSuccess: () => { }
                });
              }
            });
          }
        });
      } else {
        this.$XyyMessage.warning('无选中数据');
      }
    }
  }
};
</script>

<style scoped lang="scss">
/deep/.page-header {
  padding-bottom: 8px !important;
}
.search-box {
  width: 344px;
  float: right;
  .el-input {
    width: calc(100% - 76px);
    /deep/ .el-input__inner {
      height: 36px;
      line-height: 36px;
    }
  }
  button {
    width: 68px;
    height: 36px;
    float: right;
  }
}

.status-tip {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 2px 2px 0;
  &.open {
    background: #67c23a;
  }
  &.close {
    background: #ff3024;
  }
}
.operation-box {
  width: auto;
  white-space: nowrap;
  letter-spacing: 0;
  margin: 0 -10px;
  .el-button {
    position: relative;
    margin: 0 10px;
  }
  .el-button::before {
    position: absolute;
    top: 14px;
    right: -10px;
    content: '';
    display: block;
    width: 1px;
    height: 12px;
    background: #dcdfe6;
  }
  .el-button:first-child {
    margin-left: 0;
  }
  .el-button:last-child {
    margin-right: 0;
  }
  .el-button:last-child::before {
    display: none;
  }
}
.help-msg-box {
  float: right;
  margin-bottom: 5px;
  &::after {
    content: '';
    clear: both;
  }
}
</style>

