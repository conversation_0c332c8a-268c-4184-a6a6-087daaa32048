<template>
  <xyy-list-page>
    <template slot="header">
      <el-form ref="searchForm" :model="queryTitle" class="search-box">
        <el-row type="flex" class="innerEl" justify="space-between" align="middle">
          <el-form-item label="发起时间" label-width="80px" prop="dateRange">
            <el-date-picker
              v-model="queryTitle.dateRange"
              type="daterange"
              start-placeholder="请选择"
              end-placeholder="请选择"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              class="timeSel"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="坐席" label-width="80px" prop="selectAllUserId">
            <el-select
              v-model="queryTitle.selectAllUserId"
              prop="selectAllUserId"
              placeholder="请选择"
            >
              <el-option
                v-for="item in selectAllUsers"
                :label="item.name"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="坐席组" label-width="80px" prop="groupNameId">
            <el-select
              v-model="queryTitle.groupNameId"
              prop="groupNameId"
              placeholder="请选择"
              @change="selectAllUser"
            >
              <el-option
                v-for="item in imKefuGroups"
                :label="item.groupName"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row class="innerElOther top_20">
          <div :class="[styleNomal?'rowWrapNomal':'rowWrap']">
            <el-form-item
              v-for="queryInfo in beQueryiedFields"
              v-if="queryInfo.fieldType == 2||queryInfo.fieldType == 3||queryInfo.fieldType == 4"
              :key="queryInfo.id"
              :label="queryInfo.fieldLable"
              :prop="queryInfo.fieldKey"
              class="serverNum"
              label-width="80px"
            >
              <el-select
                v-model="query[queryInfo.fieldKey]"
                :placeholder="'请输入'+queryInfo.fieldLable"
                :multiple="queryInfo.fieldType == 4"
                :collapse-tags="queryInfo.fieldType == 4"
              >
                <el-option
                  v-for="settings in arrformting(queryInfo.optionSettings)"
                  :label="settings"
                  :value="settings"
                  :key="settings"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item
              v-else
              :label="queryInfo.fieldLable"
              :prop="query[queryInfo.fieldKey]"
              label-width="80px"
            >
              <el-input
                v-model="query[queryInfo.fieldKey]"
                :placeholder="'请输入'+queryInfo.fieldLable"
                class="serverName"
                size="small"
              />
            </el-form-item>

            <el-form-item class="queryMole">
              <el-button type="text" @click="handleShowMore">
                {{ showMoreSearch?'收起':'展开' }}
                <i v-if="!showMoreSearch" class="el-icon-arrow-up"></i>
                <i v-if="showMoreSearch" class="el-icon-arrow-down"></i>
              </el-button>
              <el-button
                plain
                type="primary"
                size="medium"
                class="searchCondition"
                @click="handleCondition"
              >查询</el-button>
              <el-button plain size="medium" @click="resetForm('searchForm')">重置</el-button>
            </el-form-item>
          </div>
        </el-row>
      </el-form>
    </template>
    <template slot="body">
      <div class="xyyTable">
        <el-button type="text" icon="el-icon-upload2" class="exportExcel" @click="exportExcel">导出</el-button>
        <xyy-table
          :data="list"
          :list-query="listQuery"
          :col="col"
          :operation="operation"
          @get-data="getList"
          @operation-click="operationClick"
        >
          <!-- <template slot="gmtModified" slot-scope="{col}">
          <el-table-column
            :key="col.index"
            :prop="col.index"
            :label="col.name"
            :formatter="getFormatDate"
          />
          </template>-->
        </xyy-table>
      </div>
    </template>
  </xyy-list-page>
</template>

<script>
import {
  serviceDataLists,
  beQueryiedFields,
  imKefuGroup,
  selectAllUser
} from '@/api/im_view/serviceConfigForm';
import historyList from './components/history/list';
import historyEdit from './components/history/edit';
import customeInfo from './components/customeInfo';
import commonUse from './components/commonUse';
export default {
  name: 'compServeTotal',
  components: {
    historyList,
    historyEdit,
    customeInfo,
    commonUse
  },
  filters: {},
  data() {
    return {
      styleNomal: false,
      showMoreSearch: false,
      imKefuGroups: [],
      selectAllUsers: [],
      allQuerTitle: [
        // {
        //   fieldDescribe: '客户名称介绍',
        //   fieldKey: '643415673527074845',
        //   fieldLable: '客户名称',
        //   fieldLocked: 1,
        //   fieldType: 0,
        //   optionSettings: '',
        //   placeholder: '请输入客户名称',
        //   sort: 1
        // },
        // {
        //   id: 17,
        //   formId: 1,
        //   fieldLable: '身份类型',
        //   fieldKey: '644190457668042753',
        //   optionSettings: '["客户","业务员","司机","其他"]',
        //   fieldType: 2,
        //   fieldDescribe: '',
        //   placeholder: '',
        //   defaultValue: '',
        //   rowNumber: '1',
        //   required: 0,
        //   sort: 3,
        //   gmtFormat: '',
        //   currentState: 1,
        //   editable: 1,
        //   fieldLocked: 1,
        //   deleted: 0,
        //   createTime: 1573628573000,
        //   creatorId: 2033,
        //   creatorName: 'fanlejiao',
        //   modifyTime: 1573628573000,
        //   modifyId: 2032,
        //   modifyName: '钟晓霞',
        //   bequeried: 1,
        //   showInlist: 0
        // },
        // {
        //   id: 18,
        //   formId: 1,
        //   fieldLable: '身份类型',
        //   fieldKey: '644190457668042752',
        //   optionSettings: '["客户","业务员","司机","其他"]',
        //   fieldType: 3,
        //   fieldDescribe: '',
        //   placeholder: '',
        //   defaultValue: '',
        //   rowNumber: '1',
        //   required: 0,
        //   sort: 3,
        //   gmtFormat: '',
        //   currentState: 1,
        //   editable: 1,
        //   fieldLocked: 1,
        //   deleted: 0,
        //   createTime: 1573628573000,
        //   creatorId: 2033,
        //   creatorName: 'fanlejiao',
        //   modifyTime: 1573628573000,
        //   modifyId: 2032,
        //   modifyName: '钟晓霞',
        //   bequeried: 1,
        //   showInlist: 0
        // },
        // {
        //   id: 40,
        //   formId: 1,
        //   fieldLable: '身份类型',
        //   fieldKey: '644190457668042759',
        //   optionSettings: '["客户","业务员","司机","其他"]',
        //   fieldType: 4,
        //   fieldDescribe: '',
        //   placeholder: '',
        //   defaultValue: '',
        //   rowNumber: '1',
        //   required: 0,
        //   sort: 3,
        //   gmtFormat: '',
        //   currentState: 1,
        //   editable: 1,
        //   fieldLocked: 1,
        //   deleted: 0,
        //   createTime: 1573628573000,
        //   creatorId: 2033,
        //   creatorName: 'fanlejiao',
        //   modifyTime: 1573628573000,
        //   modifyId: 2032,
        //   modifyName: '钟晓霞',
        //   bequeried: 1,
        //   showInlist: 0
        // },
        // {
        //   fieldDescribe: '客户名称介绍',
        //   fieldKey: '643415673527074856',
        //   fieldLable: '客户名称',
        //   fieldLocked: 1,
        //   fieldType: 0,
        //   optionSettings: '',
        //   placeholder: '请输入客户名称',
        //   sort: 1
        // },
        // {
        //   fieldDescribe: '客户名称介绍',
        //   fieldKey: '643415673527074857',
        //   fieldLable: '客户名称',
        //   fieldLocked: 1,
        //   fieldType: 0,
        //   optionSettings: '',
        //   placeholder: '请输入客户名称',
        //   sort: 1
        // }
      ],
      beQueryiedFields: [],
      queryTitle: { dateRange: [], groupNameId: '', selectAllUserId: '' },
      query: {},
      list: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      col: [
        // { index: '33', name: '创建时间' },
        // { index: '44', name: '备注' },
        // { index: 'operation', name: '操作', width: 220, operation: true }
      ],
      operation: [
        {
          name: '查看',
          type: 1
        },
        {
          name: '编辑',
          type: 2
        }
      ]
    };
  },
  mounted() {
    this.beQueryiedField(); // 获取搜索配置字段
    this.imKefuGroup(); // 获取作息组
    this.selectAllUser(); // 获取作息
  },

  methods: {
    imKefuGroup() {
      const that = this;
      imKefuGroup().then(res => {
        // console.log('555', res);
        if (res.code === 1) {
          that.imKefuGroups = res.data;
        }
      });
    },
    selectAllUser() {
      // 员工组id 传-1 代表全部
      const that = this;
      selectAllUser(-1).then(res => {
        console.log('666', res);
        if (res.code === 1) {
          that.selectAllUsers = res.data;
        }
      });
    },
    arrformting(val) {
      return JSON.parse(val);
    },
    beQueryiedField() {
      const that = this;
      beQueryiedFields().then(res => {
        console.log('resres:', res);

        if (res.code === 1) {
          that.allQuerTitle = res.data;
          that.allQuerTitle.forEach(item => {
            if (item.fieldType == 4) {
              that.$set(that.query, item.fieldKey, []); // v-model
            } else {
              that.$set(that.query, item.fieldKey, '');
            }
          });
          if (that.allQuerTitle.length > 0) {
            that.styleNomal = false;
            if (that.allQuerTitle.length > 3) {
              that.beQueryiedFields = that.allQuerTitle.filter((item, i) => {
                return i < 3;
              });
            } else {
              that.beQueryiedFields = that.allQuerTitle;
            }
          } else {
            that.styleNomal = true;
          }
          console.log('query333:', that.query);

          that.$nextTick(() => {
            if (document.querySelector('.queryMole').offsetLeft === 0) {
              console.log('00000');
              document.querySelector('.queryMole').style.marginLeft = '86%';
            }
          });
        } else {
          //that.$XyyMsg(res.msg);
        }
        console.log('333:', that.query);
      });
    },
    resetForm(formName) {
      const that = this;
      console.log(formName);

      for (const key in that.query) {
        if (that.query.hasOwnProperty(key)) {
          if (typeof key === 'string') {
            that.$set(that.query, key, '');
          } else {
            that.$set(that.query, key, []);
          }
        }
      }

      that.$refs[formName].resetFields();
    },
    handleShowMore() {
      const that = this;
      this.showMoreSearch = !this.showMoreSearch;
      if (this.showMoreSearch) {
        that.beQueryiedFields = that.allQuerTitle;
        console.log('that.beQueryiedFields:', that.beQueryiedFields);

        console.log('query55:', that.query);

        document.querySelector('.innerElOther').style.height = 'auto';
        that.$nextTick(() => {
          if (document.querySelector('.queryMole').offsetLeft === 0) {
            console.log('00000');
            document.querySelector('.queryMole').style.marginLeft = '74%';
          }
        });
      } else {
        that.beQueryiedFields = that.allQuerTitle.filter((item, i) => {
          return i < 3;
        });
        console.log('query55:', that.query);
        that.$nextTick(() => {
          document.querySelector('.queryMole').style.marginLeft = '0px';
        });
        document.querySelector('.innerElOther').style.height = '61px';
      }
    },
    operationClick: function(type, row) {
      console.log('000222333', type, row);
      const that = this;
      switch (type) {
        case 1:
          that.$router.push({
            path: 'imseachdetail/' + new Date().getTime(),
            query: {
              dataId: row.id
            }
          });
          break;
        case 2:
          that.$router.push({
            path: 'imseachedite/' + new Date().getTime(),
            query: {
              dataId: row.id
            }
          });
          break;
      }
    },
    getList(listQuery) {
      // 查询列表数据
      const that = this;
      const { page, pageSize } = listQuery || this.listQuery;
      const param = {
        createTime: {
          start: this.queryTitle.dateRange[0],
          end: this.queryTitle.dateRange[1]
        },
        groupId: this.queryTitle.groupNameId,
        kefuId: this.queryTitle.selectAllUserId,
        ...this.query
      };
      serviceDataLists(page, pageSize, param).then(res => {
        if (res.code === 1) {
          that.list = res.data.data;

          that.col = [
            ...res.data.title,
            { index: 'operation', name: '操作', width: 220, operation: true }
          ];
          that.listQuery = {
            page: Number(res.data.pageInfo.currentPage),
            pageSize: Number(res.data.pageInfo.pageSize),
            total: Number(res.data.pageInfo.totalCount)
          };
        } else {
          // this.$XyyMsg({
          //   title: '提示',
          //   closeBtn: false,
          //   content: `${res.msg}`, // html代码串
          //   onSuccess: () => {},
          // });
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    handleCondition() {
      this.getList();
    },
    exportExcel() {
      // 导出列表
      const that = this;
      if (!that.list || !that.list.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      const businessPart =
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
          ? this.$store.getters.channel.businessPartCode
          : '';
      const preParam = {
        formId: 1,
        queryStr: {
          createTime: {
            start: this.queryTitle.dateRange[0],
            end: this.queryTitle.dateRange[1]
          },
          businessPartCode: businessPart,
          groupId: this.queryTitle.groupNameId,
          kefuId: this.queryTitle.selectAllUserId,
          ...this.query
        }
      };
      preParam.queryStr = JSON.stringify(preParam.queryStr);

      function httpPost(URL, PARAMS) {
        var temp = document.createElement('form');
        temp.action = URL;
        temp.method = 'get';
        temp.style.display = 'none';

        for (var x in PARAMS) {
          var opt = document.createElement('textarea');
          opt.name = x;
          opt.value = PARAMS[x];
          temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();
      }
      // let domain = document.domain;
      // if (domain === 'zhou.dev.ybm100.com') {
      //   domain = 'api-im.dev.ybm100.com';
      // }
      httpPost(
        `${process.env.BASE_API_IM}/serviceSummary/dataListsExcel`,
        preParam
      );
    }
  }
};
</script>

<style lang="scss" scoped>
.search-box {
  padding: 20px;
  .innerEl {
    height: 38px;
    /deep/.el-input__inner {
      width: 100%;
      height: 36px;
      line-height: 36px;
    }
    /deep/.el-form-item {
      margin: 0;
    }
    .timeSel {
      width: 500px;
    }
    .seachItme {
      min-width: 580px;
    }

    .serverName {
      width: 95%;
    }
    .serverNum {
      margin-right: 20px;
    }
    .searchCondition.is-plain {
      background: rgba(59, 149, 168, 1);
      color: #fff;
    }
    &.top_20 {
      margin-top: 20px;
    }
  }
  .innerElOther {
    height: 61px;
    overflow: hidden;
    /deep/.el-input__inner {
      width: 100%;
      height: 36px;
      line-height: 36px;
    }
    /deep/.el-form-item {
      margin: 0;
    }
    /deep/.el-select {
      width: 95%;
    }
    .timeSel {
      width: 500px;
    }
    .seachItme {
      min-width: 580px;
    }

    .serverName {
      width: 95%;
    }
    .serverNum {
    }
    .searchCondition.is-plain {
      background: rgba(59, 149, 168, 1);
      color: #fff;
    }
    &.top_20 {
      margin-top: 20px;
    }
    .rowWrap {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      flex-wrap: wrap;
      /deep/.el-form-item {
        margin-bottom: 20px;
        width: 25%;
      }
    }
    .rowWrapNomal {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: flex-end;
      flex-wrap: wrap;
      /deep/.el-form-item {
        margin-bottom: 20px;
      }
    }
    .rowBtn {
      width: 230px;
    }
  }
}
.queryMole {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.xyyTable {
  position: relative;
  .exportExcel {
    position: absolute;
    right: 20px;
    top: 0px;
    z-index: 10;
  }
}
</style>
