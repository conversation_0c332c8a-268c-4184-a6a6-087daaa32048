<!-- 全局 im聊天记录组件 -->
<template>
  <div>
    <div v-for="(item, index) in getDataListComputed" :key="index">
      <template>
        <p :class="item.type === 101 || item.type === 102 ? 'visitors' : 'customerService'">
          <!-- IM2.1.5：除101，102，201外都是系统消息且不显示发送者昵称 -->
          <span v-if="item.type === 101 || item.type === 102">【访客】</span>
          <span v-else-if="item.type === 201">【坐席】</span>
          <span v-else>【系统消息】</span>
          <span v-if="item.type === 101 || item.type === 102">{{ item.talkName || item.customerName }}</span>
          <span v-if="item.type === 201">{{ item.talkName || item.kefuName }}</span>
          {{ item.createTimeStr ? item.createTimeStr : item.createtime || item.createTime }}
        </p>
        <span class="message" v-html="item.content" @click="showRaw($event)"></span>
      </template>
    </div>
    <div v-if="!getDataListComputed.length">
      <div class="empty-data-container">
        <img :src="emptyDataImgUrl" />
      </div>
    </div>
    <fileprev ref="imgDialog"></fileprev>
    <filepreview ref="fileDialog"></filepreview>
  </div>
</template>

<script>
// 例如：import 《组件名称》 from '《组件路径》';
import emptyDataImgUrl from '@/assets/common/empty-data-table.png';
import fileprev from '~/Fields/img-preview';
import filepreview from '~/Fields/pdf-preview';
import { log } from 'util';
export default {
  components: { fileprev, filepreview },
  props: {
    getDataList: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      showOn: true,
      emptyDataImgUrl
    };
  },
  // 监听属性 类似于data概念
  computed: {
    getDataListComputed: function () {
      let dataList = [];
      if (this.getDataList.length) {
        dataList = this.cardMsgType201Formatter(this.getDataList);
      }
      return dataList;
    }
  },
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    // 展示图片
    showRaw(src) {
      if (event.target.attributes['onclick']) {
        let realimgsrc = event.target.attributes['onclick'].value.split("'");
        switch (
        realimgsrc[1]
          .substring(realimgsrc[1].lastIndexOf('.') + 1)
          .toLowerCase()
        ) {
          case 'xls':
          case 'xlsx':
          case 'doc':
          case 'docx':
          case 'pdf':
            // this.$refs.fileDialog.initPdf('');
            this.$nextTick(() => {
              this.$refs.fileDialog.initPdf(realimgsrc[1]);
            });
            break;
          case 'png':
          case 'jpg':
          default:
            this.$refs.imgDialog.initImage('');
            this.$nextTick(() => {
              this.$refs.imgDialog.initImage(realimgsrc[1]);
            });
            break;
        }
        // this.$refs.imgDialog.initImage(realimgsrc[1]);
      }
    },
    cardMsgType201Formatter(msgList) {
      if (msgList && msgList.length && msgList instanceof Array) {
        //名片消息，type:201
        for (let i = 0, l = msgList.length; i < l; i++) {
          if (msgList[i].type === 201) {
            //去掉名片消息中按钮的点击事件
            let isDivContains = document.getElementById('msgContentDiv');
            if (isDivContains) {
              document.body.removeChild(isDivContains);
            }

            let msgContentDiv = document.createElement('div');
            msgContentDiv.setAttribute('id', 'msgContentDiv');
            msgContentDiv.innerHTML = msgList[i].content;
            document.body.appendChild(msgContentDiv);

            let btns = document.querySelectorAll('#msgContentDiv button');
            if (btns && btns.length) {
              btns.forEach(item => {
                item.removeAttribute('onclick');
              });
            }

            msgList[i].content = document.getElementById(
              'msgContentDiv'
            ).innerHTML;

            document.body.removeChild(msgContentDiv);
          }
        }
        return msgList;
      }
    }
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {
    window.showRaw = function (url) { }; // 防止图片自带的showRaw 调用报错
  },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() { },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeDestroy() { }, // 生命周期 - 销毁之前
  destroyed() { }, // 生命周期 - 销毁完成
  activated() { } // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang='scss' scoped>
.customerService {
  color: rgba(59, 149, 168, 1);
}

.visitors {
  color: rgba(230, 162, 60, 1);
}

.message {
  word-wrap: break-word;

  /deep/img {
    max-width: 100%;
  }

  // 订单消息样式 begin
  /deep/ .order-msg-bubble {
    width: 100%;
    // background-color: #ffffff;
    padding: 4px 0;
    box-sizing: border-box;
    border-radius: 8px;
    cursor: pointer;

    .order-msg-content {
      width: 100%;
      padding-bottom: 10px;
      box-sizing: border-box;
      overflow: hidden;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;

      .order-msg-img {
        flex-shrink: 0;
        width: 54px;
        height: 54px;
        margin-top: 0 !important;
        border: 1px solid #eeeeee;
        box-sizing: border-box;
        border-radius: 6px;
      }

      .order-msg-info {
        flex-grow: 1;
        padding: 0 0 0 8px;
        box-sizing: border-box;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;

        .order-msg-info-sec {
          width: 100%;
          margin-bottom: 10px;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;

          &:last-child {
            margin-bottom: 0;
          }

          .order-msg-sec-ttl {
            flex-shrink: 0;
            width: 75px;
            line-height: 1;
            color: #888888;
            font-size: 14px;
            font-weight: 400;
            font-family: PingFangSC;
          }

          .order-msg-sec-cnt {
            flex-grow: 1;
            line-height: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #222222;
            font-size: 14px;
            font-weight: 400;
            font-family: PingFangSC;

            &.t-light {
              color: #fc3c17;
            }
          }
        }
      }
    }

    .order-msg-no {
      width: 100%;
      padding-top: 10px;
      box-sizing: border-box;
      border-top: 1px solid #ebe7e7;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;

      .order-msg-no-ttl {
        flex-shrink: 0;
        width: 75px;
        line-height: 1;
        color: #888888;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFangSC;
      }

      .order-msg-no-cnt {
        flex-grow: 1;
        line-height: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #222222;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFangSC;
      }
    }
  }

  // 订单消息样式 end

  // 商品消息样式 begin
  /deep/ .product-msg-bubble {
    width: 100%;
    // background-color: #ffffff;
    padding: 4px 0;
    box-sizing: border-box;
    border-radius: 8px;
    cursor: pointer;

    .product-msg-content {
      width: 100%;
      overflow: hidden;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;

      .product-msg-img {
        flex-shrink: 0;
        width: 54px;
        height: 54px;
        margin-top: 0 !important;
        border: 1px solid #eeeeee;
        box-sizing: border-box;
        border-radius: 6px;
      }

      .product-msg-info {
        flex-grow: 1;
        padding: 0 0 0 8px;
        box-sizing: border-box;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;

        .product-msg-info-sec {
          width: 100%;
          margin-bottom: 10px;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;

          &:last-child {
            margin-bottom: 0;
          }

          .product-msg-sec-ttl {
            flex-shrink: 0;
            width: 75px;
            line-height: 1;
            color: #888888;
            font-size: 14px;
            font-weight: 400;
            font-family: PingFangSC;
          }

          .product-msg-sec-cnt {
            flex-grow: 1;
            line-height: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #222222;
            font-size: 14px;
            font-weight: 400;
            font-family: PingFangSC;

            &.t-light {
              color: #fc3c17;
            }
          }
        }
      }
    }
  }

  // 商品消息样式 end
}

//无数据样式
.empty-data-container {
  padding-top: 150px;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 305px;
    height: 219px;
  }
}
</style>