<template>
  <div class="blacklistMange">
    <xyy-list-page>
      <template slot="header">
        <el-form
          :inline="true"
          :model="listQuery"
          class="demo-form-inline search-box"
          ref="listQuery"
        >
          <el-form-item label="时间范围" prop="dataRange">
            <el-date-picker
              v-model="listQuery.dataRange"
              type="datetimerange"
              range-separator="-"
              size="small"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              text="erdf"
              prefix-icon="el-icon-date"
              class="innerSelTime"
            />
          </el-form-item>
          <el-form-item label="来源渠道" prop="channelId">
            <el-select placeholder="请选择" v-model="listQuery.channelId">
              <el-option
                :key="item.value"
                :label="item.channelName"
                :value="item.channelId"
                v-for="item in formMangesources"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="query" class="searchCondition" plain size="small" type="primary">查询</el-button>
            <el-button @click="resetForm('listQuery')" plain size="small">重置</el-button>
          </el-form-item>
        </el-form>
      </template>
      <template slot="body">
        <div class="xyyTable">
          <el-button class="batchDelete" icon="el-icon-delete" @click="allRemove">批量移除</el-button>
          <xyy-table
            :col="col"
            :data="tableData"
            :has-selection="true"
            :hasIndex="true"
            :list-query="listQuery"
            :offset-top="240"
            @get-data="seachDataList"
            @selectionCallback="setCheckedDatas"
          >
            <template>
              <el-table-column type="index" width="50"></el-table-column>
            </template>
            <!--访客姓名-->
            <template slot="userName" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :label="col.name"
                :prop="col.index"
                :width="col.width"
              ></el-table-column>
            </template>
            <!--来源渠道-->
            <template slot="channelName" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :width="col.width"
                :label="col.name"
                show-overflow-tooltip
              ></el-table-column>
            </template>
            <!--创建时间-->
            <template slot="createTime" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :label="col.name"
                :prop="col.index"
                :width="col.width"
                show-overflow-tooltip
              ></el-table-column>
            </template>
            <!-- 创建人-->
            <template slot="creatorName" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :label="col.name"
                :prop="col.index"
                :width="col.width"
              ></el-table-column>
            </template>
            <!--修改时间-->
            <template slot="modifyTime" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :label="col.name"
                :prop="col.index"
                :width="col.width"
                show-overflow-tooltip
              ></el-table-column>
            </template>
            <!--修改人-->
            <template slot="modifyName" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :label="col.name"
                :prop="col.index"
                :width="col.width"
              ></el-table-column>
            </template>
            <!--有效时长-->
            <template slot="validTime" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :label="col.name"
                :prop="col.index"
                :width="col.width"
              ></el-table-column>
            </template>
            <!--开始时间-->
            <template slot="startTime" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :label="col.name"
                :prop="col.index"
                :width="col.width"
                show-overflow-tooltip
              ></el-table-column>
            </template>
            <!--结束时间-->
            <template slot="endTime" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :label="col.name"
                :prop="col.index"
                :width="col.width"
                show-overflow-tooltip
              ></el-table-column>
            </template>
            <!--备注-->
            <template slot="remark" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :label="col.name"
                :prop="col.index"
                :ellipsis="col.ellipsis"
                :width="col.width"
                show-overflow-tooltip
              ></el-table-column>
            </template>
            <template slot="operation" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :label="col.name"
                :prop="col.index"
                :fixed="col.fixed"
                :width="col.width"
              >
                <template slot-scope="scope">
                  <el-row>
                    <el-button @click="handerEditor(scope.$index, scope.row)" type="text">编辑 |</el-button>
                    <el-button @click="handerDelete(scope.$index, scope.row)" type="text">移除</el-button>
                  </el-row>
                </template>
              </el-table-column>
            </template>
          </xyy-table>
        </div>
      </template>
    </xyy-list-page>
    <!--弹出的黑名单编辑框-开始-->
    <el-dialog :visible.sync="dialogEditor" title="黑名单编辑" class="dialogInner" width="50%">
      <el-form :model="listQuery">
        <el-form-item>
          <el-row>
            <el-col :span="2">
              <span class="asterisk">*</span>时限
            </el-col>
            <el-col :span="18">
              <el-row type="flex" class="row-bg" justify="space-between">
                <el-col :span="2">
                  <div class="grid-content bg-purple bg-purple1">
                    <el-button
                      :class="[{'input_1_active': whichBtn==0 },'input_1']"
                      @click="hours"
                    >24h</el-button>
                  </div>
                </el-col>
                <el-col :span="2">
                  <div class="grid-content bg-purple-light bg-purple1">
                    <el-button
                      :class="[{'input_1_active': whichBtn==1 },'input_2']"
                      @click="permanent"
                    >永久</el-button>
                  </div>
                </el-col>
                <el-col :span="2">
                  <div class="grid-content bg-purple-light bg-purple1">
                    <el-button
                      :class="[{'input_1_active': whichBtn==2 },'input_2']"
                      @click="custom"
                    >自定义</el-button>
                  </div>
                </el-col>
                <el-col :span="11" style="margin-top:-2px">
                  <el-input
                    class="inputK"
                    type="number"
                    v-model.number="timeLimitOurse"
                    v-if="CustomDisplay"
                    @input="handerInput"
                  ></el-input>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2">
              <span style="color:#fff">biaozhu</span>
            </el-col>
            <el-col :span="8">点击保存后，开始计时</el-col>
          </el-row>
          <el-row>
            <el-col :span="2">备注说明</el-col>
            <el-col :span="18">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                maxlength="100"
                show-word-limit
                v-model="remark"
                class="input_3"
              ></el-input>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <span class="dialog-footer" slot="footer">
        <el-button @click="dialogCancel">取 消</el-button>
        <el-button @click="dialogDetermine" type="primary">确 定</el-button>
      </span>
    </el-dialog>
    <!--弹出的黑名单编辑框-结束-->
  </div>
</template>

<script>
import XyyListPage from '../../components/xyy/xyy-list-page/index';
import XyyTable from '../../components/xyy/xyy-table/index';
import {
  blacklistMange,
  removeBlacklist,
  addBlacklist,
  getSources,
} from '@/api/configuration/BlacklistManage';
export default {
  name: 'compBlacklistManage',
  components: { XyyTable, XyyListPage },
  data() {
    return {
      formMangesources: [], // 来源渠道
      // 表格显示的数据
      tableData: [],
      listQuery: {
        page: 1, // 当前页数(页码)
        pageSize: 10, // 每页显示条目个数
        total: 0, // 总条目数
        dataRange: [], // 开始时间 结束时间
        channelId: '', // 来源渠道
        remark: '', // 备注说明
        timeLimit: '', // 时限
      },
      remark: '', //  编辑里面的备注
      timeLimitOurse: '',
      whichBtn: -1, // 获取点击的是哪个按钮
      CustomDisplay: false, // 控制编辑弹框显示隐藏
      editorRow: [], // 点击编辑存储的每行的内容
      col: [
        { index: 'userName', name: '访客姓名', ellipsis: true, width: 250 },
        { index: 'channelName', name: '来源渠道', slot: true, ellipsis: true },
        {
          index: 'createTime',
          name: '创建时间',
          slot: true,
          ellipsis: true,
          width: 200,
        },
        {
          index: 'creatorName',
          name: '创建人',
          slot: true,
          ellipsis: true,
          width: 100,
        },
        {
          index: 'modifyTime',
          name: '修改时间',
          slot: true,
          ellipsis: true,
          width: 200,
        },
        {
          index: 'modifyName',
          name: '修改人',
          slot: true,
          ellipsis: true,
          width: 100,
        },
        {
          index: 'validTime',
          name: '有效时长(h)',
          slot: true,
          ellipsis: true,
          width: 100,
        },
        {
          index: 'startTime',
          name: '开始时间',
          slot: true,
          ellipsis: true,
          width: 200,
        },
        {
          index: 'endTime',
          name: '结束时间',
          slot: true,
          ellipsis: true,
          width: 200,
        },
        {
          index: 'remark',
          name: '备注',
          slot: true,
          ellipsis: true,
          width: 200,
        },
        {
          index: 'operation',
          name: '操作',
          slot: true,
          ellipsis: true,
          width: 150,
          fixed: 'right',
        },
      ],
      dialogEditor: false, // 控制弹出的编辑框
      selections: [], // 表格选中的所有数据的id
    };
  },
  mounted: function () {
    this.seachDataList();
    this.getSourcesList();
  },
  methods: {
    // 重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    // 表单查询；
    query() {
      let listQuery = this.listQuery;
      this.listQuery.page = 1;
      this.seachDataList(listQuery);
    },
    // 表格编辑按钮事件
    handerEditor(index, row) {
      this.editorRow = row;
      console.log('row');
      console.log(row);
      if (this.editorRow.timeLimit === 24) {
        this.whichBtn = 0;
        this.CustomDisplay = false;
        this.timeLimitOurse = '';
      }
      if (this.editorRow.timeLimit === 0) {
        this.whichBtn = 1;
        this.CustomDisplay = false;
        this.timeLimitOurse = '';
      }
      if (this.editorRow.timeLimit !== 0 && this.editorRow.timeLimit !== 24) {
        this.whichBtn = 2;
        this.timeLimitOurse = this.editorRow.timeLimit;
        this.CustomDisplay = true;
      }
      this.remark = row.remark;
      this.dialogEditor = true;
    },
    // 判断自定义输入事件
    handerInput() {
      // 判断不能输入小数点
      let str = '' + this.timeLimitOurse;
      if (str.indexOf('.') !== -1) {
        let arr = str.split('');
        arr.splice(arr.length - 1);
        let str2 = arr.join('');
        this.timeLimitOurse = +str2;
      }
      // 时长最多输入9999999，超过则不能输入
      if (this.timeLimitOurse > 99999) {
        let aaa = this.timeLimitOurse.toString();
        let bbb = aaa.slice(0, 5);
        this.timeLimitOurse = Number(bbb);
      }
    },
    // 点击编辑里边的 24h
    hours() {
      this.CustomDisplay = false;
      this.whichBtn = 0;
    },
    // 点击永久时
    permanent() {
      this.CustomDisplay = false;
      this.whichBtn = 1;
    },
    // 自定义事件
    custom() {
      this.whichBtn = 2;
      this.CustomDisplay = true;
    },
    // 获取来源渠道数据
    getSourcesList() {
      getSources()
        .then((response) => {
          this.formMangesources = response.data;
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    // 查询列表的数据
    seachDataList(listQuery) {
      // 查询列表数据
      const that = this;
      const { page, pageSize } = listQuery || this.listQuery;
      const dataListList = this.listQuery.dataRange;
      if (
        !dataListList &&
        typeof dataListList !== 'undefined' &&
        dataListList !== 0
      ) {
        listQuery = {
          page: page,
          size: pageSize,
          channelId: this.listQuery.channelId,
          timeLimit: '',
        };
      } else {
        listQuery = {
          page: page,
          size: pageSize,
          channelId: this.listQuery.channelId,
          endTime: this.listQuery.dataRange[1],
          startTime: this.listQuery.dataRange[0],
          timeLimit: '',
        };
      }

      //   申请接口带入参数查询数据
      blacklistMange(listQuery)
        .then((response) => {
          const { current, size, total } = response.data;
          response.data.records.forEach((item, index) => {
            if (item.validTime === 0) {
              item.validTime = '永久';
            }
          });
          this.tableData = response.data.records;
          this.listQuery = {
            ...this.listQuery,
            page: Number(current),
            pageSize: Number(size),
            total: Number(total),
          };
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    // 设置选中的数据id
    setCheckedDatas(datas) {
      this.selections = datas.map((el) => el.id);
    },

    // 编辑弹框确定事件
    dialogDetermine() {
      if (this.whichBtn === '') {
        this.$message('请设置黑名单时限');
        return false;
      }
      console.log('this.timeLimitOurse');
      console.log(this.timeLimitOurse);
      // 做判断
      this.dialogEditor = false;
      if (this.whichBtn === 0) {
        this.listQuery.timeLimit = 24;
      } else if (this.whichBtn === 1) {
        this.listQuery.timeLimit = 0;
      } else if (this.whichBtn === 2) {
        this.listQuery.timeLimit = this.timeLimitOurse;
      }
      let preParam = {
        id: this.editorRow.id,
        remark: this.remark,
        timeLimit: this.listQuery.timeLimit,
      };

      addBlacklist(preParam)
        .then((response) => {
          if (response.code === 1) {
            this.$message('修改成功');
            this.seachDataList();
            this.whichBtn = -1;
          } else {
            this.$message(response.msg);
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    // 弹出框取消事件
    dialogCancel() {
      this.dialogEditor = false;
    },
    // 批量移除
    allRemove() {
      if (this.selections.length === 0) {
        this.$message('请选择要删除的数据');
        return;
      }
      // 获取选中的
      this.$XyyMsg({
        title: '提示',
        content: '确定从黑名单中移除此访客',
        onSuccess: () => {
          removeBlacklist(this.selections)
            .then((response) => {
              if (response.code === 1) {
                this.$message('移除成功');
                this.seachDataList();
              }
            })
            .catch(function (error) {
              console.log(error);
            });
        },
      });
    },
    // 表格移除按钮事件
    handerDelete(index, row) {
      this.$XyyMsg({
        title: '提示',
        content: '确定从黑名单中移除此访客',
        onSuccess: () => {
          //   同意删除
          let arrList = [];
          arrList.push(row.id);
          //   申请接口带入参数查询数据  传递id
          removeBlacklist(arrList)
            .then((response) => {
              if (response.code === 1) {
                this.$message('移除成功');
                this.seachDataList();
              }
            })
            .catch(function (error) {
              console.log(error);
            });
        },
      });
    },
  },
};
</script>
<style lang="scss">
.search-box {
  border-bottom: 1px dashed #e4e4eb;
}
.dialogInner {
  .el-col-11 .el-input__inner {
    width: 170px;
    padding-right: 0;
  }
  .bg-purple1 .input_1_active {
    border: 1px solid rgba(59, 149, 168, 1);
    color: 1px solid rgba(59, 149, 168, 1);
  }
  .bg-purple1 .el-button:visited,
  .el-button:active,
  .el-button:hover {
    background: #fff !important;
  }
  .bg-purple1 .input_1_active:visited {
    background: #fff !important;
  }

  .bg-purple1 .el-button.el-button--primary.is-plain {
    border: 1px solid rgba(228, 228, 235, 1);
    color: rgba(87, 87, 102, 1);
  }
  .input_1,
  .input_2 {
    width: 80px;
    height: 36px;
    .el-input__inner {
      height: 36px;
    }
  }
  .input_3 {
    textarea {
      height: 211px;
      border-radius: 2px;
    }
  }
}
</style>
<style scoped lang="scss">
.inputK {
  /deep/.el-input__inner {
    height: 36px;
  }
}
.searchCondition {
  background: #3b95a8;
  color: #fff;
}
.blacklistMange {
  .batchDelete {
    margin-bottom: 20px;
  }
}
.asterisk {
  color: red;
}
.elCol_6 {
  width: 61% !important;
}
</style>
