<template>
  <el-table :data="tableData" class="tabs-box-tables" border style="width: 100%">
    <el-table-column prop="name" label="姓名" />
    <el-table-column prop="key" label="字段类型" />
    <el-table-column prop="desc" label="备注说明" />
    <el-table-column prop="time" width="229" label="更新时间" />
    <el-table-column prop="user" label="更新人" />
    <el-table-column width="84" label="状态">
      <template slot-scope="scope">
        <span
          :class="[scope.row.state === 1 ? 'tabs-box-tag-enable' : 'tabs-box-tag-disable']"
        >{{ scope.row.state === 1 ? '启用':'禁用' }}</span>
      </template>
    </el-table-column>
    <el-table-column width="125" label="操作">
      <template slot-scope="scope">
        <el-button
          v-if="scope.row.stateJin === 1"
          type="text"
          size="small"
          @click="handleClick(scope.row)"
        >{{ scope.row.state === 1 ? '禁用':'启用' }}</el-button>
        <el-button type="text" size="small">编辑</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'TableModule',
  props: {
    tabledata: {
      type: Array,
      default: function() {
        return [];
      }
    }
  },
  methods: {
    handleClick(row) {
      this.$emit('handleClick', row);
    }
  }
};
</script>
