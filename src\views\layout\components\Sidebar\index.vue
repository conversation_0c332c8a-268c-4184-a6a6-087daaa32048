<template>
  <el-scrollbar wrap-class="scrollbar-wrapper" class="sidebar-header">
    <div class="box row-start" style="min-height:100%; background-color: #F0F2F5;">
      <ul class="out-menu" @mouseenter="handleMenuEnter" @mouseleave="mouseLeave">
        <li
          v-for="(item,index) in menu"
          :key="index"
          :class="(currentIndex==index||hoverIndex==index)?'menu-hover':''"
          @click="handleMenuClick(item.mark)"
          @mouseenter="mouseEnter(index)"
        >
          <p>
            <svg-icon
              :icon-class="(currentIndex==index||hoverIndex==index)?item.icon+'_hover':item.icon"
            ></svg-icon>
          </p>
          {{ item.name }}
        </li>
      </ul>
      <transition name="slide-fade">
        <div v-if="isChild" style="margin-left:50px;" @mouseleave="handleMenuLeave">
          <el-menu
            :default-active="$route.path"
            :background-color="variables.menuBg"
            :text-color="variables.menuText"
            :active-text-color="variables.menuActiveText"
            :collapse-transition="false"
            :default-openeds="openeds"
            mode="vertical"
          >
            <sidebar-item
              v-for="route in routes"
              :key="route.path"
              :item="route"
              :base-path="route.path"
            />
          </el-menu>
        </div>
      </transition>
    </div>
  </el-scrollbar>
</template>

<script>
import variables from '@/styles/variables.scss';
import SidebarItem from './SidebarItem';
export default {
  components: { SidebarItem },
  data() {
    return {
      list: [
        {
          name: '会话',
          icon: 'session_7',
          code: 'menu:csim:dialogmgr',
          mark: 'session'
        },
        // { name: '会话', icon: 'session_7', code: 'menu:cs:workorder', mark: 'session' },
        {
          name: '工单',
          icon: 'workSheet_7',
          code: 'menu:cs:workorder',
          mark: 'workSheet'
        },
        // { name: '质检', icon: 'qs_7', code: 'menu:csim:qa', mark: 'qs' },
        {
          name: '监控',
          icon: 'monitor',
          code: 'menu:cs:monitor',
          mark: 'monitor'
        },
        {
          name: '统计',
          icon: 'statistics_7',
          code: 'menu:cs:total',
          mark: 'statistics'
        },
        {
          name: '知识库',
          icon: 'knowledge_base_7',
          code: 'menu:cs:knowledge_base',
          mark: 'knowledge_base'
        },
        {
          name: 'CRM',
          icon: 'crm_7',
          code: 'menu:cs:crm',
          mark: 'crm'
        },
        {
          name: '业务',
          icon: 'shop_7',
          code: 'menu:cs:business',
          mark: 'shop'
        },
        {
          name: '配置',
          icon: 'config_7',
          code: 'menu:cs:config',
          mark: 'config'
        }
      ],
      mark: 'workSheet',
      isChild: true,
      allRoutes: JSON.parse(JSON.stringify(this.$router.options.routes)),
      hoverIndex: null,
      currentIndex: 0
    };
  },
  computed: {
    menu() {
      let arr = [];
      const menu = this.$store.getters.menu;
      arr = this.list.filter(item => {
        return menu.some(val => {
          return item.code && item.code === val.code;
        });
      });
      return arr;
    },
    routes() {
      // 权限过滤
      const menu = this.$store.getters.menu;
      const whiteMenu = ['/empty', '/error', '/customerService'];
      let routes = [];
      const menuCode = [];
      menu.forEach(item => {
        if (item.code) {
          menuCode.push(item.code);
        }
      });
      if (menu && menu.length) {
        routes = this.allRoutes.filter(item => {
          if (
            (item.meta &&
              menuCode.indexOf(item.meta.code) > -1 &&
              item.meta.mark === this.mark) ||
            whiteMenu.indexOf(item.path) > -1
          ) {
            if (item.children && item.children.length > 0) {
              item.children = item.children.filter(val => {
                return (
                  (val.meta &&
                    menuCode.indexOf(val.meta.code) > -1 &&
                    val.meta.mark === this.mark) ||
                  whiteMenu.indexOf(item.path) > -1
                );
              });
            }
            return true;
          }
        });
      } else {
        routes = this.allRoutes;
      }
      return routes;
    },
    openeds() {
      const arr = [];
      this.routes.forEach(item => {
        if (!item.hidden && item.children && item.children.length > 1) {
          const cArr = item.children.filter(c => {
            return !c.hidden;
          });
          if (cArr.length > 1) {
            arr.push(item.path);
          }
        }
      });
      return arr;
    },
    variables() {
      return variables;
    }
  },
  watch: {
    '$store.getters.workType': {
      deep: true,
      handler(val) {
        this.allRoutes = JSON.parse(
          JSON.stringify(this.$router.options.routes)
        );
      }
    }
  },
  created() {
    this.emits(this, 'register', {
      type: 'nav_session',
      uid: this._uid,
      fn: m => {
        console.log('--nav---', m);
        this.handleMenuClick(m);
      }
    });
  },
  mounted() {
    this.$store.commit('page/SET_IS_CHILD', true);
    this.mark = this.$route.meta.mark ? this.$route.meta.mark : 'workSheet';
    // DT优化：默认菜单不在权限中，默认为第一个菜单的
    if (this.menu.length)
      this.mark = this.menu.some(item => {
        return item.mark === 'workSheet';
      })
        ? 'workSheet'
        : this.menu[0].mark;
    this.handleMenuClick(this.mark);
  },
  methods: {
    mouseEnter(index) {
      this.hoverIndex = index;
    },
    mouseLeave() {
      this.hoverIndex = null;
    },
    handleMenuClick(mark) {
      this.menu.map((item, index) => {
        if (item.mark === mark) {
          this.currentIndex = index;
        }
      });
      this.mark = mark;
    },
    handleMenuLeave() {
      this.isChild = true;
      this.$store.commit('page/SET_IS_CHILD', true);
    },
    handleMenuEnter() {
      this.isChild = true;
      this.$store.commit('page/SET_IS_CHILD', true);
    }
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/variables.scss';
.menu-hover {
  color: #3b95a8 !important;
}
.out-menu {
  padding-left: 0;
  width: 50px;
  background-color: #f0f2f5;
  position: fixed;
  top: 66px;
  li {
    cursor: pointer;
    p {
      margin: 0;
      height: 20px;
      margin-top: 10px;
      .svg-icon {
        margin: 0 !important;
        width: 20px;
        height: 20px;
      }
    }
    text-align: center;
    line-height: 30px;
    font-size: 12px;
    color: #979797;
    &:hover {
      color: #3b95a8 !important;
    }
  }
}
.el-menu {
  width: 150px !important;
}
.sidebar-header {
  padding-top: 66px !important;
}
</style>

