<template>
  <!-- 订单物流信息弹窗 -->
  <el-dialog class="dialog-order-invoice-container" width="900px" append-to-body destroy-on-close :show-close="false"
    :visible.sync="dialogVisible" v-if="dialogVisible">
    <div class="dialog-body">
      <div class="dialog-title">
        <span class="title-txt">发票信息</span>
        <i class="el-icon-close" @click="handleCloseDialog"></i>
      </div>
      <div class="dialog-content">
        <div class="content-box" v-if="invoiceList.length">
          <div class="invoice-package">
            <div class="package-tab">
              <template v-for="(item, index) in invoiceList">
                <span :class="['tab-item', index == invoiceIndex ? 'active' : '']"
                  @click="handleChangePackage(index)">发票{{
                    index + 1 }}</span>
              </template>
            </div>
            <div class="package-content">
              <!-- PDF预览 -->
              <object :data="invoiceList[invoiceIndex]" type="application/pdf"></object>
            </div>
          </div>
        </div>
        <div class="empty-box" v-else>
          <img class="empty-icon" src="@/assets/common/icon-empty.png" />
          <span class="empty-txt">暂无发票信息</span>
        </div>
      </div>
      <div class="dialog-footer">
        <template v-if="invoiceList.length">
          <el-button class="btn-default" @click="handleCopyInvoiceUrl">复制链接</el-button>
          <el-button class="btn-primary" v-if="operable" @click="handleSendInvoice">发送</el-button>
        </template>
        <template v-else>
          <el-button class="btn-primary" @click="handleCloseDialog">关闭</el-button>
        </template>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { queryOrderInvoice } from '@/api/im_view/customeOrder';
export default {
  name: "dialog-order-invoice",
  props: {
    operable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,

      orderNo: '',

      invoiceIndex: 0, // 发票index
      invoiceList: [],
    }
  },
  methods: {
    init({ orderNo }) {
      this.orderNo = orderNo

      this.invoiceIndex = 0
      this.invoiceList = []

      this.fetchOrderinvoice()

      this.$nextTick(() => {
        this.dialogVisible = true;
      })
    },

    // 查询发票信息
    async fetchOrderinvoice() {
      try {
        const { code, data, msg } = await queryOrderInvoice({
          orderNo: this.orderNo
        })
        if (code == 1) {
          if (Array.isArray(data) && data.length) {
            this.invoiceList = data
          }
        } else {
          throw new Error(msg)
        }
      } catch (e) {
        this.$XyyMessage.error(e.message);
      } finally { }
    },

    // 切换发票
    handleChangePackage(index) {
      this.invoiceIndex = index
    },

    // 复制链接
    handleCopyInvoiceUrl() {
      try {
        let input = document.createElement('textarea');
        input.value = this.invoiceList[this.invoiceIndex];
        document.body.appendChild(input);
        input.select();
        document.execCommand("copy");
        document.body.removeChild(input);
        this.$XyyMessage.success("操作成功");
      } catch (e) {
        console.log(e)
      }
    },

    // 发送发票
    handleSendInvoice() {
      this.dialogVisible = false;
      this.$emit('send', this.invoiceList[this.invoiceIndex])
    },

    // 关闭弹窗
    handleCloseDialog() {
      this.dialogVisible = false;
    },
  }
}
</script>

<style lang="scss" scoped>
.dialog-order-invoice-container {
  /deep/ {
    .el-dialog {
      border-radius: 4px;

      .el-dialog__header {
        display: none;
      }

      .el-dialog__body {
        width: 100%;
        padding: 20px;
        box-sizing: border-box;

        .dialog-body {
          width: 100%;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: stretch;

          .dialog-title {
            width: 100%;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .title-txt {
              flex-grow: 1;
              color: #222222;
              font-size: 16px;
              font-weight: 400;
            }

            .el-icon-close {
              flex-shrink: 0;
              cursor: pointer;
              font-size: 16px;
              font-weight: bold;

              &:hover {
                opacity: 0.6;
              }
            }
          }

          .dialog-content {
            width: 100%;
            margin-bottom: 20px;

            .content-box {
              width: 100%;
              display: flex;
              flex-direction: column;
              justify-content: flex-start;
              align-items: stretch;


              .invoice-package {
                width: 100%;
                height: 560px;
                border: 1px solid #d9dee3;
                border-radius: 2px;
                overflow: hidden;
                display: flex;
                justify-content: flex-start;
                align-items: stretch;

                .package-tab {
                  flex-shrink: 0;
                  width: 88px;
                  border-right: 1px solid #d9dee3;
                  overflow-y: auto;
                  display: flex;
                  flex-direction: column;
                  justify-content: flex-start;
                  align-items: stretch;

                  .tab-item {
                    width: 100%;
                    padding: 13px 0;
                    box-sizing: border-box;
                    text-align: center;
                    line-height: 1;
                    cursor: pointer;
                    background-color: #fff;
                    color: #666666;
                    font-size: 14px;
                    font-weight: 400;

                    &.active {
                      background-color: #dae9ec;
                      color: #3B95A8;
                      font-weight: 500;
                    }
                  }
                }

                .package-content {
                  flex-grow: 1;
                  overflow-y: auto;
                  // padding: 16px;
                  box-sizing: border-box;
                  display: flex;
                  flex-direction: column;
                  justify-content: flex-start;
                  align-items: stretch;

                  object {
                    width: 100%;
                    flex-grow: 1;
                  }
                }
              }
            }

            .empty-box {
              width: 100%;
              padding: 60px 0;
              display: flex;
              flex-direction: column;
              justify-content: flex-start;
              align-items: center;

              .empty-icon {
                width: 180px;
                height: 150px;
                margin-bottom: 20px;
              }

              .empty-txt {
                color: #666666;
                font-size: 16px;
                font-weight: 400;
              }
            }
          }

          .dialog-footer {
            width: 100%;
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .el-button {
              padding: 9px 18px;
              border-radius: 4px;
              font-size: 14px;
              font-weight: 500;

              &:hover {
                opacity: 0.6;
              }

              &:active {
                opacity: 1;
              }

              +.el-button {
                margin-left: 16px;
              }

              &.btn-primary {
                color: #ffffff;
                background-color: #3B95A8;
                border-color: #3B95A8;
              }

              &.btn-default {
                color: #222222;
                background-color: #fff;
                border-color: #D4D9DD;
              }
            }
          }
        }
      }
    }
  }
}
</style>