<template>
  <div>
    <el-dialog
      :visible="status"
      :title="title"
      custom-class="node-transfer-box"
      top="0"
      @close="close"
    >
      <div class="left-content">
        <el-tabs
          v-model="nodeType"
          :class="Number(nodeType)===0?'first-active':''"
          @tab-click="handleChange"
        >
          <el-tab-pane
            v-for="(label,index) in nodeLabels"
            :class="index===0 || index===3?'radio-node-box':''"
            :key="index"
            :name="String(index)"
            :label="label"
          >
            <el-checkbox-group
              v-model="leftCheckedDatas[index]"
              :disabled="(index===0 && startable) || (index===3 && endable)"
              @change="leftToRight"
            >
              <el-checkbox
                v-for="el in leftDatas[index]"
                :key="el.id"
                :label="el"
                :disabled="disabledCodes.includes(el.versionCode)"
              >
                <span v-if="index===0 || index===3" class="radio-icon-box">
                  <i class="radio-icon"></i>
                </span>
                {{ el.nodeName }}
              </el-checkbox>
            </el-checkbox-group>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="right-content">
        <div class="content-title">已选择{{ rightDatas.length?`(${rightDatas.length})`:'' }}</div>
        <div class="content-body">
          <el-checkbox-group v-model="rightCheckedDatas">
            <el-checkbox v-for="el in rightDatas" :key="el.id" :label="el">
              {{ el.nodeName }}
              <i class="el-icon-minus" @click="delRightData(el.id)"></i>
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="checkTimer(save,'timer')()">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getNodesByType } from '@/api/formManage';
export default {
  props: {
    status: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '选择节点'
    },
    checked: {
      type: Array,
      default: function() {
        return [];
      }
    },
    startable: {
      type: Boolean,
      default: false
    },
    endable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      leftDatas: [[], [], [], []], // 左侧表单数据 0-发起节点 1-处理节点 2-抄送节点 3-关闭节点
      leftCheckedDatas: [[], [], [], []], // 左侧选中表单数据
      rightDatas: [], // 右侧表单数据
      rightCheckedDatas: [], // 右侧选中表单数据
      nodeType: '0',
      nodeLabels: ['发起节点', '处理节点', '抄送节点', '关闭节点'],
      timer: null
    };
  },
  computed: {
    disabledCodes() {
      return this.checked.map(_el => _el.refNodeCode);
    }
  },
  watch: {
    status(val) {
      if (val) {
        this.getNodesByType(0, this.initDatas);
      }
    },
    leftCheckedDatas: {
      handler(val) {
        let arr = val[this.nodeType];
        if ([0, 3].includes(Number(this.nodeType)) && arr.length > 1) {
          arr = arr.splice(arr.length - 1, 1);
          this.leftCheckedDatas.splice(this.nodeType, 1, arr);
        }
      },
      deep: true
    }
  },
  methods: {
    initDatas() {
      this.rightCheckedDatas = [];
      this.rightDatas = [];
      this.leftCheckedDatas = [[], [], [], []];
      this.nodeType = '0';
    },
    /**
     * 关闭回调
     */
    close() {
      this.$emit('update:status', false);
    },
    save() {
      if (!this.rightDatas.length) {
        this.$XyyMessage.error('未有选中项，请重新选择');
        return;
      }
      this.$emit('callback', Array.from(new Set(this.rightDatas)));
      this.close();
    },
    /**
     * 选择右侧表单数据
     */
    leftToRight() {
      let datas = [];
      this.leftCheckedDatas.forEach(el => {
        datas = datas.concat(el);
      });
      this.rightDatas = JSON.parse(JSON.stringify(datas));
    },
    /**
     * 删除右侧数据
     */
    delRightData(id) {
      this.rightDatas = this.rightDatas.filter(el => el.id !== id);
      this.setLeftCheckedDatas(this.rightDatas);
    },
    /**
     * 设置左侧数据回显
     */
    setLeftCheckedDatas(datas) {
      const _datas = JSON.parse(JSON.stringify(datas));
      const arr = [[], [], [], []];
      _datas.forEach(el => {
        const data = this.leftDatas[Number(el.nodeType)].filter(
          _el => _el.id === el.id
        )[0];
        arr[Number(el.nodeType)].push(data);
      }, this);
      this.leftCheckedDatas = arr;
    },
    /**
     * 根据类型 获取引用节点
     */
    getNodesByType(type, cb) {
      getNodesByType({ nodeType: type }).then(res => {
        if (res.code === 1) {
          this.leftDatas.splice(type, 1, res.data);
          if (cb) {
            cb();
          }
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    /**
     * 选项卡变更回调
     */
    handleChange(tab, event) {
      if (!this.leftDatas[Number(tab.name)].length) {
        this.getNodesByType(Number(tab.name));
      }
    }
  }
};
</script>

<style lang="scss">
.el-dialog.node-transfer-box {
  width: 740px;
  height: 502px;
  transform: translateY(-50%);
  top: 50%;
  .el-dialog__header {
    height: 52px;
    padding: 15px 20px;
    .el-dialog__title {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(41, 41, 51, 1);
      height: 22px;
      line-height: 22px;
    }
    .el-dialog__headerbtn {
      top: 18px;
    }
  }
  .el-dialog__body {
    height: calc(100% - 122px);
    padding: 6px 20px 0;
    > div {
      float: left;
      height: 100%;
      box-sizing: border-box;
      overflow: hidden;
      position: relative;
      .content-title {
        height: 55px;
        background: rgba(240, 242, 245, 1);
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(41, 41, 51, 1);
        padding: 0 20px;
        line-height: 54px;
      }
      .content-body {
        height: calc(100% - 54px);
        padding: 15px 20px 12px;
        overflow-y: auto;
      }
    }
    .left-content {
      width: 50%;
      border-radius: 2px;
      border: 1px solid rgba(228, 228, 235, 1);
      border-right: none;
      .el-tabs {
        height: 100%;
        /deep/.el-tabs__header {
          margin-bottom: 0;
        }
        /deep/.el-tabs__nav {
          width: 100%;
          background: rgba(240, 242, 245, 1);
          .el-tabs__active-bar {
            width: 56px !important;
            margin-left: -5px;
            bottom: 8px;
          }
          .el-tabs__item {
            width: 25%;
            padding: 0;
            margin-left: 0;
            text-align: center;
            height: 55px;
            line-height: 55px;
          }
        }
        &.first-active {
          /deep/.el-tabs__active-bar {
            margin-left: 15px;
          }
        }
        /deep/.el-tabs__content {
          height: calc(100% - 54px);
          box-sizing: border-box;
          padding: 15px 20px 12px;
          overflow-y: auto;
          .el-checkbox-group {
            .el-checkbox {
              display: block;
              margin-bottom: 12px;
              font-weight: normal;
            }
          }
          .radio-node-box {
            .el-checkbox {
              .el-checkbox__input {
                display: none;
              }
              .el-checkbox__label {
                padding-left: 0;
              }
              .radio-icon-box {
                display: inline-block;
                width: 14px;
                height: 14px;
                border-radius: 100%;
                overflow: hidden;
                position: relative;
                top: 2.5px;
                border: 1px solid #e4e4eb;
                margin-right: 8px;
              }
              &.is-checked {
                .radio-icon-box {
                  border: 1px solid #3b95a8;
                  .radio-icon {
                    width: 8px;
                    height: 8px;
                    display: inline-block;
                    border-radius: 100%;
                    overflow: hidden;
                    background: #3b95a8;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translateX(-50%) translateY(-50%);
                  }
                }
              }
            }
          }
        }
      }
    }

    .right-content {
      width: 50%;
      border-radius: 2px;
      border: 1px solid rgba(228, 228, 235, 1);
      .el-checkbox-group {
        .el-checkbox {
          display: block;
          margin-bottom: 12px;
          font-weight: normal;
          .el-checkbox__input {
            display: none;
          }
          .el-checkbox__label {
            padding-left: 0;
          }
          .el-icon-minus {
            cursor: pointer;
            color: #3b95a8;
            border: 1px solid #3b95a8;
            font-size: 12px;
            position: relative;
            top: -1px;
            margin-left: 20px;
          }
        }
      }
    }
  }
  .el-dialog__footer {
    padding: 20px;
    .el-button {
      height: 36px;
      line-height: 36px;
      padding: 0 19px;
    }
  }
}
</style>
