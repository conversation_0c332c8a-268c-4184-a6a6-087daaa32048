<template>
  <div>
    <el-dialog :title="title" :visible="status" custom-class="assign-box" top="0" @close="close">
      <el-input v-model="keys" placeholder="请输入关键字" suffix-icon="el-icon-search"></el-input>
      <div class="assign-tree-box">
        <el-tree
          ref="assign-tree"
          :data="datas"
          :filter-node-method="filterNode"
          :render-content="renderContent"
          :expand-on-click-node="false"
          :props="{
            label:'name',
            children:'kefus'
          }"
          @node-click="selectNode"
        ></el-tree>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button plain @click="close">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '提示'
    },
    status: {
      type: Boolean,
      default: false
    },
    datas: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      keys: '',
      selectedKey: '',
      selectedType: ''
    };
  },
  watch: {
    keys(val) {
      this.$refs['assign-tree'].filter(val);
    },
    status(val) {
      if (val) {
        // 初始化选中数据
        this.selectedKey = '';
        this.selectedType = '';
        this.keys = '';
      }
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    renderContent(h, { node, data, store }) {
      const _class =
        data.id === this.selectedKey ? 'tree-node active' : 'tree-node';
      const _icon = data.type === 'kefu' ? 'im-user' : 'im-group';
      if (node.level === 2) {
        return (
          <span class={_class}>
            <svg-icon icon-class={_icon}></svg-icon>&nbsp;
            <span>{node.label}</span>
          </span>
        );
      } else {
        return (
          <span class={_class}>
            <span>{node.label}</span>
          </span>
        );
      }
    },
    selectNode(data, node, obj) {
      if (node.level === 2) {
        this.selectedKey = data.id;
        this.selectedType = data.type;
      }
    },
    close() {
      this.$emit('update:status', false);
    },
    save() {
      if (!this.selectedKey) {
        this.$XyyMessage.error('请选择指派对象');
        return;
      }
      this.$emit('callback', {
        id: this.selectedKey,
        type: this.selectedType
      });
      this.close();
    }
  }
};
</script>

<style lang="scss">
.el-dialog.assign-box {
  width: 400px;
  top: 50%;
  transform: translateY(-50%);
  /deep/.el-dialog__header {
    padding: 15px 20px;
    position: relative;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: rgba(41, 41, 51, 1);
    .el-dialog__headerbtn {
      top: 19px;
    }
  }
  /deep/.el-dialog__body {
    padding: 0 20px 20px;
    .assign-tree-box {
      height: 352px;
      border-radius: 2px;
      border: 1px solid rgba(228, 228, 235, 1);
      box-sizing: border-box;
      margin-top: 15px;
      padding: 15px;
      overflow-y: auto;
      .el-tree-node.is-current {
        .el-tree-node__content {
          background: #fff;
          .tree-node.active {
            color: rgba(59, 149, 168, 1);
          }
        }
      }
    }
  }
  .el-dialog__footer {
    height: 56px;
    padding: 0 20px;
    box-sizing: border-box;
    .el-button {
      padding: 0 20px;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      font-weight: 400;
      font-family: PingFangSC-Regular, PingFang SC;
      &.el-button--default {
        border: 1px solid rgba(228, 228, 235, 1);
        color: rgba(87, 87, 102, 1);
        &:hover,
        &:focus {
          border: 1px solid rgba(228, 228, 235, 1);
          color: rgba(87, 87, 102, 1);
        }
      }
    }
  }
}
</style>
