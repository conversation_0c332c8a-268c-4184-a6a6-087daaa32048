<template>
  <div class="PersonnelManagement">
    <xyy-list-page>
      <template slot="header">
        <el-form
          :inline="true"
          :model="listQuery"
          class="input_serch search-box"
          label-position="left"
          ref="listQuery"
        >
          <el-row>
            <el-form-item label="搜索条件" prop="searchtype">
              <el-select
                placeholder="请选择搜索条件"
                v-model="listQuery.searchtype"
                @change="selectEvents"
              >
                <el-option
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  v-for="item in conditionsOptions"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="searchval" v-if="!showOn">
              <el-input placeholder="请输入" v-model="listQuery.searchval" :disabled="disabledChoice"></el-input>
            </el-form-item>
            <el-form-item prop="searchval">
              <el-select v-model="listQuery.searchval" placeholder="请选择" v-if="showOn">
                <el-option
                  v-for="item in roleList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.roleNo"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="userstate">
              <el-select placeholder="请选择搜索条件" v-model="listQuery.userstate">
                <el-option
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  v-for="item in conditionsState"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button @click="query" type="primary">查询</el-button>
              <el-button @click="resetForm('listQuery')">重置</el-button>
            </el-form-item>
          </el-row>
        </el-form>
      </template>
      <template slot="body">
        <div class="xyyTable">
          <el-row type="flex" class="row-bg" justify="space-between">
            <el-col :span="8">
              <el-button @click="searchPersonnel" icon="el-icon-plus">新建人员</el-button>
              <el-select @change="sortChange" placeholder="请选择排序" v-model="listQuery.orderBy">
                <el-option
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  v-for="item in sortOptions"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="15" style="text-align: right">
              <div class="PersoManageTitle">
                <span>角色预览（人）：坐席{{ seats }}</span>
                <span>客服组长 {{ teamLeader }}</span>
                <span>主管 {{ charge }}</span>
                <span>经理 {{ manager }}</span>
                <span>管理员 {{ admin }}</span>
                <span>删除客服 {{ delete_1 }}</span>
                <span>禁止客服 {{ disabled_1 }}</span>
              </div>
            </el-col>
          </el-row>
          <xyy-table
            :data="Datelist"
            :col="col"
            :list-query="listQuery"
            :operation="operation"
            :offset-top="240"
            @get-data="seachDataList"
          >
            <!-- 用户名-->
            <template slot="username" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :width="col.width"
                :label="col.name"
              ></el-table-column>
            </template>
            <!--姓名-->
            <template slot="name" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :width="col.width"
                :label="col.name"
              ></el-table-column>
            </template>
            <!--昵称-->
            <template slot="nickname" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :width="col.width"
                :label="col.name"
              ></el-table-column>
            </template>
            <!--工号-->
            <template slot="code" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :width="col.width"
                :label="col.name"
              ></el-table-column>
            </template>
            <!--角色-->
            <template slot="roleName" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :width="col.width"
                :label="col.name"
              ></el-table-column>
            </template>
            <!--员工组-->
            <template slot="kefuGroupName" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :width="col.width"
                :label="col.name"
              ></el-table-column>
            </template>
            <!--是否禁用-->
            <template slot="state" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :width="col.width"
                :label="col.name"
              >
                <template slot-scope="{row}">
                  <span v-if="row.state === 0">正常</span>
                  <span v-else-if="row.state === 1">禁用</span>
                  <span v-else-if="row.state === 2">删除</span>
                </template>
              </el-table-column>
            </template>
            <!--服务数-->
            <template slot="maxdialog" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :width="col.width"
                :label="col.name"
              ></el-table-column>
            </template>
            <!--操作-->
            <template slot="operation" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :label="col.name"
                :prop="col.index"
                :render-header="renderHeader"
                :width="col.width"
              >
                <template slot-scope="scope">
                  <el-row>
                    <el-button type="text" @click="handerEditor(scope.$index, scope.row)">编辑</el-button>
                  </el-row>
                </template>
              </el-table-column>
            </template>
          </xyy-table>
        </div>
      </template>
    </xyy-list-page>
  </div>
</template>

<script>
import XyyListPage from '../../components/xyy/xyy-list-page/index';
import { getPerMan, getRole } from '@/api/configuration/PersManage';
export default {
  name: 'compPersonnelManage',
  components: { XyyListPage },
  data() {
    return {
      conditionsOptions: [
        {
          // 选择组
          value: 'name',
          label: '姓名'
        },
        {
          value: 'code',
          label: '工号'
        },
        {
          value: 'role',
          label: '角色'
        },
        {
          value: '员工组',
          label: '员工组'
        },
        {
          value: 'username',
          label: '用户名'
        },
        {
          value: 'nickname',
          label: '昵称'
        }
      ],
      conditionsState: [
        {
          value: '0',
          label: '正常'
        },
        {
          value: '1',
          label: '禁用'
        },
        {
          value: '2',
          label: '删除'
        }
      ],
      sortOptions: [
        {
          // 排序选择值
          value: '0',
          label: '服务数升值'
        },
        {
          value: '1',
          label: '服务数降值'
        }
      ],
      disabledChoice: true, // 控制搜索条件
      Datelist: [], // 表单数据
      seats: 0, // 坐席
      teamLeader: 0, // 客服组长
      charge: 0, // 主管
      manager: 0, // 经理
      admin: 0, // 管理员
      delete_1: 0, // 删除客服数量
      disabled_1: 0, // 禁用客服数量
      showOn: false, // 角色显示隐藏控制
      col: [
        { index: 'username', name: '用户名', slot: true },
        { index: 'name', name: '姓名', slot: true },
        { index: 'nickname', name: '昵称', slot: true },
        { index: 'code', name: '工号', slot: true },
        { index: 'roleName', name: '角色', slot: true },
        { index: 'kefuGroupName', name: '员工组', slot: true },
        { index: 'state', name: '是否禁用', slot: true },
        { index: 'maxdialog', name: '服务数', slot: true },
        { index: 'operation', name: '操作', slot: true, fixed: 'right' }
      ],
      listQuery: {
        // 表单分页
        page: 1,
        pageSize: 10,
        total: 1,
        searchtype: '', // 搜索条件
        searchval: '', // 搜索条件添加
        userstate: '', // 状态
        orderBy: '0', // 排序选择值
        fileType: 1,
        searchval: '' // 角色id
      },
      operation: [
        {
          name: '编辑',
          type: 1
        }
      ]
    };
  },
  created() {
    this.roleList();
  },
  activated() {
    this.seachDataList(this.listQuery);
  },
  methods: {
    // 搜索条件选择角色时
    selectEvents(val) {
      this.disabledChoice = false;
      // 选择角色 后边的角色框设置为空
      this.listQuery.searchval = '';
      if (val === 'role') {
        this.showOn = true;
      } else {
        this.showOn = false;
      }
    },
    // 点击搜索 跳转 添加人员页面
    searchPersonnel() {
      this.$router.replace({
        path: `/chat/addPersonnel/${new Date().getTime()}`
      });
    },
    // 表格编辑事件
    handerEditor(index, row) {
      this.$router.replace({
        path: `/chat/addPersonnel/${row.id}`,
        query: {
          obj: row
        }
      });
    },
    // 表单重置
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.disabledChoice = true;
    },
    // 排序选择
    sortChange(val) {
      this.seachDataList(this.listQuery);
    },
    // 点击查询按钮事件
    query() {
      this.seachDataList(this.listQuery);
    },
    // 查询列表数据
    seachDataList(listQuery) {
      // 查询列表数据
      const that = this;
      const { page, pageSize } = listQuery || this.listQuery;
      listQuery = {
        page: page,
        pagesize: pageSize,
        searchtype: this.listQuery.searchtype, // 搜索条件
        searchval: this.listQuery.searchval, // 搜索条件添加
        userstate: this.listQuery.userstate, // 状态
        orderBy: this.listQuery.orderBy, // 排序选择值
        fileType: 0
      };
      //   申请接口带入参数查询数据
      getPerMan(listQuery)
        .then(response => {
          this.listQuery.searchval = String(this.listQuery.searchval);
          const { curPage, pageSize, total } = response.data.page;
          this.Datelist = response.data.page.datas;
          this.seats = response.data.countRole.seats; // 坐席
          this.teamLeader = response.data.countRole.teamLeader; // 客服组长
          this.charge = response.data.countRole.charge; // 主管
          this.manager = response.data.countRole.manager; // 经理
          this.admin = response.data.countRole.admin; // 管理员
          this.delete_1 = response.data.countRole.delete; // 删除客服数量
          this.disabled_1 = response.data.countRole.disabled; // 禁用客服数量
          this.listQuery = {
            ...this.listQuery,
            page: Number(curPage),
            pageSize: Number(pageSize),
            total: Number(total)
          };
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    // 表头插入导入按钮
    renderHeader() {
      return (
        <div>
          操作
          <el-button size="small" on-click={() => this.exportExcel()}>
            {' '}
            <span class="el-icon-upload2"></span> 导出
          </el-button>
        </div>
      );
    },
    // 导出表格事件
    exportExcel() {
      // 传参 与人员管理列表接口一样
      // 查询列表数据
      const that = this;
      // const { page, pageSize } = listQuery || this.listQuery;
      //   申请接口带入参数查询数据
      let url = `${process.env.BASE_API_IM}/staff/list?page=1&pagesize=10&fileType=1&orderBy=${this.listQuery.orderBy}&searchtype=${this.listQuery.searchtype}&searchval=${this.listQuery.searchval}`;
      if (
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
      ) {
        url =
          url +
          `&businessPartCode=${this.$store.getters.channel.businessPartCode}`;
      }
      const a = document.createElement('a');
      a.href = url;
      a.click();
    },
    // 获取角色列表
    roleList() {
      getRole()
        .then(response => {
          response.data.forEach((item, index) => {
            item.roleNo = String(item.roleNo);
          });
          console.log(response.data);
          this.roleList = response.data;
        })
        .catch(function(error) {
          console.log(error);
        });
    }
  }
};
</script>

<style scoped lang="scss">
.search-box {
  border-bottom: 1px dashed #e4e4eb;
}
/deep/.w-e-text {
  height: 76%;
}
.PersonnelManagement {
  font-size: 14px;
  color: #292933;
  .table-containter {
    margin-top: 20px;
  }
  .PersoManageTitle {
    display: inline-block;
    height: 100%;
    background: rgba(245, 247, 250, 1);
    font-size: 12px;
    line-height: 40px;
    padding-left: 12px;
    span {
      &:after {
        content: '|';
        color: #eff0f5;
        margin: 0 10px 0 10px;
      }
    }
  }
  .el-table__header-wrapper {
    button {
      background: rgba(0, 0, 0, 0);
      border: 0;
      color: rgba(59, 149, 168, 1);
      .el-icon-upload2 {
        &:before {
          @extend button;
        }
      }
    }
  }
}
</style>
