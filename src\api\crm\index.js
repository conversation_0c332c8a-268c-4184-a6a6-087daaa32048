import request from '@/utils/request';

/* 查询客户管理列表 */
export function findCAExclusiveMerchantInfoList(data) {
  return request({
    url: '/imapi/cAExclusiveKeFu/findCAExclusiveMerchantInfoList',
    method: 'post',
    data: data
  });
}

/* 查询行政域 */
export function findAllBranchList(data) {
  return request({
    url: '/imapi/cAExclusiveKeFu/findAllBranchList',
    method: 'post',
    data: data
  });
}

/* 导出客户CA信息表 */
export function customCAInfoExport(data) {
  return request({
    url: '/imapi/cAExclusiveKeFu/customCAInfo/export',
    method: "get",
    params: data
  });
}

/* 导入客户信息绑定 */
export function importCustomer(data) {
  return request({
    url: '/imapi/cAExclusiveKeFu/importCustomer',
    method: 'post',
    data: data
  });
}

/* 单个、批量绑定 */
export function cAExclusiveKeFuBind(data) {
  return request({
    url: '/imapi/cAExclusiveKeFu/bind',
    method: 'post',
    data: data
  });
}

/* 是否添加企业微信 */
export function isAddWechat(data) {
  return request({
    url: '/imapi/cAExclusiveKeFu/isAddWechat',
    method: 'get',
    params: data
  });
}

/* 获取员工组 */
export function getEmployeeList(data) {
  return request({
    url: '/imapi/imKefuGroup/listKefu',
    method: 'get',
    params: data
  });
}

