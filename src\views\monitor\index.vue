<template>
  <div class="page-content">
    <el-row>
      <el-col :md="24" class="m-b">
        <statics-item ref="statics" />
      </el-col>
      <!-- <el-col :md="18" class="p-r m-b">
        <business-monitor ref="busniessRef" />
      </el-col>-->
      <el-col :md="18" class="p-r m-b">
        <el-card class="box-card" shadow="never" style="width: 100%;height:380px;">
          <div slot="header" class="clearfix">
            <span class="titlefont">状态人数</span>
          </div>
          <div
            style="font-size:14px;color:rgba(87,87,102,1)"
            class="m-b"
          >在线合计：{{ online }}人 系统合计：{{ total }}人</div>
          <div class="buttonlist">
            <template v-for="(item, index) in status">
              <el-button
                v-if=" item.status.length === 2"
                :key="item.status"
                :class="'status-'+ index"
                type="primary"
              >{{ item.status }} {{ item.count }}</el-button>
            </template>
          </div>
          <div class="buttonlist">
            <template v-for="(item, index) in status">
              <el-button
                v-if=" item.status.length !== 2 && index < 15"
                :key="item.status"
                :class="'status-'+ index"
                type="primary"
              >{{ item.status }} {{ item.count }}</el-button>
            </template>
          </div>
        </el-card>
      </el-col>
      <el-col :md="6" class="m-b">
        <channel-monitor ref="channelMonitor" />
      </el-col>

      <el-col :md="24" class="m-b">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            <span class="titlefont">正常状态 坐席组监控情况</span>
          </div>
          <div
            v-for="item in seatGroupList"
            :key="item.groupId"
            class="grouplist"
            @changeState="changeState"
          >
            <div class="grouptit">客服：{{ item.groupName }}</div>
            <monitor-item :list="item.userKefuList" :id="item.groupId" @eat="changeState" />
          </div>
        </el-card>
      </el-col>
      <el-col :md="24" class="m-b">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            <span class="titlefont">预警状态 坐席组监控情况</span>
          </div>
          <monitor-item :list="forbiddenSeatGroupList" :right="false" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import loading from '@/mixin/loading';
import StaticsItem from './components/statics';
import BusinessMonitor from './components/businessMonitor';
import ChannelMonitor from './components/channelMonitor';
import MonitorItem from './components/monitorItem';
import {
  getPersonCount,
  getChannelMonitorinData,
  getStateNumber,
  getSeatGroupr,
  getForbiddenSeatGroup
} from '@/api/monitor/index.js';
export default {
  name: 'IMMonitorWhole',
  components: { StaticsItem, BusinessMonitor, ChannelMonitor, MonitorItem },
  mixins: [loading],
  data() {
    return {
      status: [],
      seatGroupList: [], // 正常 坐席组监控情况
      forbiddenSeatGroupList: [], // 预警 坐席组监控情况
      online: 0, // 在线总人数
      total: 0, // 系统总人数
      isset: ''
    };
  },
  mounted() {
    // this.getBusniessData();
    this.getPersonCount();
    this.getChannelMonitorinData();
    this.getStateNumber();
    this.getSeatGroupr();
    this.getForbiddenSeatGroup();
    // 4秒刷新
    // this.isset = setInterval(() => {
    //   this.getBusniessData(); // 全员监控
    //   this.getPersonCount(); // 全员监控
    //   this.getChannelMonitorinData(); // 渠道进线监控
    //   this.getStateNumber(); // 状态人数
    //   this.getSeatGroupr(); // 坐席组监控情况
    //   this.getForbiddenSeatGroup();
    // }, 4000);
  },
  beforeDestroy() {
    // 注销前
    clearInterval(this.isset);
  },
  deactivated() {
    // 缓存组件停用
    clearInterval(this.isset);
  },
  activated() {
    // 缓存启用 4秒刷新
    this.isset = setInterval(() => {
      // this.getBusniessData(); // 全员监控
      this.getPersonCount(); // 全员监控
      this.getChannelMonitorinData(); // 渠道进线监控
      this.getStateNumber(); // 状态人数
      this.getSeatGroupr(); // 坐席组监控情况
      this.getForbiddenSeatGroup();
    }, 4000);
  },
  methods: {
    // getBusniessData() {
    //   getBusniessData().then(res => {
    //     this.$refs.statics.initData(res); // 初始化静态数据
    //     this.$refs.busniessRef.initChart(res); // 初始化业务监控柱状图
    //   });
    // },
    getPersonCount() {
      getPersonCount().then(res => {
        const { data } = res;
        if (data) {
          this.$refs.statics.setPersonCount(res); // 设置人工接启率
        }
      });
    },
    getChannelMonitorinData() {
      getChannelMonitorinData().then(res => {
        // if (res.hasOwnProperty('PC')) {
        this.$refs.channelMonitor.initChart(res); // 初始话渠道监控饼图
        // }
      });
    },
    getStateNumber() {
      getStateNumber().then(res => {
        // 1=在线，3=忙碌，2=培训，8=会议，4=就餐，7=小休，5=离线，0=登录，6=退出
        // 1000=在线有对话，1001=在线无对话，1002=忙碌有对话，1003=忙碌无对话，1004=培训有对话
        // 1005=培训无对话，1006=会议有对话，1007=会议无对话，1008=就餐有对话，1009=就餐无对话
        // 1010=小休有对话，1011=小休无对话，1012=离线有对话，1013=离线无对话
        if (res.hasOwnProperty('1')) {
          const arr = [];
          // let totalCount = 0;
          for (const key in res) {
            if (key === '1') {
              arr.push({ status: '在线', count: res[key] });
            } else if (key === '3') {
              arr.push({ status: '忙碌', count: res[key] });
            } else if (key === '2') {
              arr.push({ status: '培训', count: res[key] });
            } else if (key === '8') {
              arr.push({ status: '会议', count: res[key] });
            } else if (key === '4') {
              arr.push({ status: '就餐', count: res[key] });
            } else if (key === '7') {
              arr.push({ status: '小休', count: res[key] });
            } else if (key === '5') {
              arr.push({ status: '离线', count: res[key] });
            } else if (key === '0') {
              arr.push({ status: '登录', count: res[key] });
            } else if (key === '6') {
              arr.push({ status: '退出', count: res[key] });
            } else if (key === '1000') {
              arr.push({ status: '在线有对话', count: res[key] });
            } else if (key === '1001') {
              arr.push({ status: '在线无对话', count: res[key] });
            } else if (key === '1002') {
              arr.push({ status: '忙碌有对话', count: res[key] });
            } else if (key === '1003') {
              arr.push({ status: '忙碌无对话', count: res[key] });
            } else if (key === '1004') {
              arr.push({ status: '培训有对话', count: res[key] });
            } else if (key === '1005') {
              arr.push({ status: '培训无对话', count: res[key] });
            } else if (key === '1006') {
              arr.push({ status: '会议有对话', count: res[key] });
            } else if (key === '1007') {
              arr.push({ status: '会议无对话', count: res[key] });
            } else if (key === '1008') {
              arr.push({ status: '就餐有对话', count: res[key] });
            } else if (key === '1009') {
              arr.push({ status: '就餐无对话', count: res[key] });
            } else if (key === '1010') {
              arr.push({ status: '小休有对话', count: res[key] });
            } else if (key === '1011') {
              arr.push({ status: '小休无对话', count: res[key] });
            } else if (key === '1012') {
              arr.push({ status: '离线有对话', count: res[key] });
            } else if (key === '1013') {
              arr.push({ status: '离线无对话', count: res[key] });
            }
            // totalCount += res[key];
          }
          this.online = res['1'];
          // this.total = totalCount;
          this.total =
            res['1'] +
            res['3'] +
            res['2'] +
            res['8'] +
            res['4'] +
            res['7'] +
            res['5'];
          // this.total =
          //   res['在线'] +
          //   res['忙碌'] +
          //   res['培训'] +
          //   res['会议'] +
          //   res['就餐'] +
          //   res['小休'] +
          //   res['离线'];
          this.status = arr;
        }
      });
    },
    getSeatGroupr() {
      getSeatGroupr().then(res => {
        const { data } = res;
        if (data) {
          data.forEach(item => {
            if (item && item.userKefuList) {
              item.userKefuList.forEach(value => {
                value.lasttime = this.formatDate(value.lasttime);
              });
            }
          });
          this.seatGroupList = data;
        }
      });
    },
    getForbiddenSeatGroup() {
      getForbiddenSeatGroup().then(res => {
        let arr = [];
        let tem = {};
        const resArr = [];
        const { data } = res;
        if (data) {
          data.forEach(item => {
            if (item.userKefuList) {
              arr = [...arr, ...item.userKefuList];
            }
          });
        }
        arr.forEach(v => {
          v.lasttime = this.formatDate(v.lasttime);
          if (v.forbiddenWords && v.forbiddenWords.length > 0) {
            tem = Object.assign({}, v, {
              forbiddenWords: true,
              state: v.state
            });
            delete tem['forbidden'];
            resArr.push(tem);
          }

          if (v.forbidden) {
            tem = Object.assign({}, v, { state: v.state });
            delete tem['forbiddenWords'];
            resArr.push(tem);
          }
        });
        this.forbiddenSeatGroupList = arr;
      });
    },
    changeState() {
      // 重新加载列表
      this.getSeatGroupr();
    },
    formatDate(time) {
      // let { d, h, m } = {};
      if (time) {
        const dis = new Date().getTime() - time;
        return this.getDuration(dis);
      } else {
        return '0天0小时0分';
      }
      // console.log(time, dis, '=====');
      // d = Math.floor(dis / (3600 * 24));
      // h = parseInt((dis - d * 24 * 3600) / 3600);
      // m = parseInt((dis - d * 24 * 3600 - h * 3600) / 60);

      // return `${d}天${h}时${m}分`;
    },
    getDuration(my_time) {
      const days = my_time / 1000 / 60 / 60 / 24;
      const daysRound = Math.floor(days);
      const hours = my_time / 1000 / 60 / 60 - 24 * daysRound;
      const hoursRound = Math.floor(hours);
      const minutes =
        my_time / 1000 / 60 - 24 * 60 * daysRound - 60 * hoursRound;
      const minutesRound = Math.floor(minutes);
      // const seconds = my_time / 1000 - (24 * 60 * 60 * daysRound) - (60 * 60 * hoursRound) - (60 * minutesRound);
      // console.log('转换时间:', daysRound + '天', hoursRound + '时', minutesRound + '分', seconds + '秒');
      const time = daysRound + '天' + hoursRound + '小时' + minutesRound + '分';
      if (daysRound < 0 || hoursRound < 0 || minutesRound < 0) {
        return '0天0小时61分';
      }
      return time;
    }
  }
};
</script>
<style>
.el-card__header {
  border: 0;
  padding-bottom: 0;
}
.el-card {
  border: 0;
  border-radius: 0;
}
</style>
<style lang="scss" scoped>
.page-content {
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: rgba(87, 87, 102, 1);
  line-height: 20px;
  background: #f0f2f5 !important;
  .grouplist {
    border-bottom: 1px dashed #eef1f4;
  }
  .grouplist:last-of-type {
    padding-bottom: 0;
    border-bottom: 0px dashed #eef1f4;
  }
  .m-b {
    margin-bottom: 15px;
    button {
      height: 30px;
      line-height: 30px;
      padding-top: 0;
      padding-bottom: 0;
    }
  }
  .buttonlist {
    button {
      margin-right: 10px;
      margin-left: 0;
      margin-bottom: 10px;
    }
  }
  .m-b:last-of-type {
    margin-bottom: 0;
  }
  .p-r {
    padding-right: 15px;
  }

  .status-0 {
    border-color: #49ba6b;
    background: #49ba6b;
  }
  .status-1 {
    border-color: #00a1ff;
    background: #00a1ff;
  }
  .status-2 {
    border-color: #ff5724;
    background: #ff5724;
  }
  .status-3 {
    border-color: #ffa631;
    background: #ffa631;
  }
  .status-4 {
    border-color: #afafbf;
    background: #afafbf;
  }
  .status-5 {
    border-color: #9762eb;
    background: #9762eb;
  }
  .status-6 {
    border-color: #6098ff;
    background: #6098ff;
  }
  .status-7,
  .status-8 {
    color: #606266;
    border-color: rgba(103, 194, 58, 0.16);
    background: rgba(103, 194, 58, 0.16);
  }
  .status-9,
  .status-10 {
    color: #606266;
    border-color: rgba(246, 119, 65, 0.16);
    background: rgba(246, 119, 65, 0.16);
  }

  .status-11,
  .status-12 {
    color: #606266;
    border-color: rgba(96, 177, 255, 0.16);
    background: rgba(96, 177, 255, 0.16);
  }

  .status-13,
  .status-14 {
    color: #606266;
    border-color: rgba(96, 152, 255, 0.16);
    background: rgba(96, 152, 255, 0.16);
  }
  .grouptit {
    padding: 12px 0;
  }
  .titlefont {
    font-weight: bold;
    font-size: 16px;
  }
}
</style>
