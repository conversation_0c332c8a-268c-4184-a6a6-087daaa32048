<template>
  <div class="classificationAll">
    <xyy-list-page>
      <template slot="header">
        <el-row class="herader-title" type="flex" justify="space-between" align="middle">
          <span style="font-size:16px">问题分类报表</span>
          <el-button icon="el-icon-upload2" class="export-excel" @click="exportData">导出Excel</el-button>
        </el-row>
        <el-form
          ref="listQuery"
          :rules="rules"
          :inline="true"
          :model="listQuery"
          label-position="right"
          class="search-form"
        >
          <el-row :gutter="10" type="flex" class="row-bg" justify="space-between">
            <!-- 发起时间-->
            <el-form-item label="日期" prop="dataRange">
              <el-date-picker
                v-model="listQuery.dataRange"
                :picker-options="pickerOptions"
                type="daterange"
                range-separator="-"
                size="small"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                text="erdf"
                prefix-icon="el-icon-date"
                class="innerSelTime"
              />
            </el-form-item>
            <!--工单类型-->
            <el-form-item label="工单类型" prop="formTypeId">
              <el-select v-model="listQuery.formTypeId" placeholder="请选择">
                <!--                      <el-option label="全部" value></el-option>-->
                <el-option
                  v-for="item in workType"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <!--客户所在地-->
            <el-form-item label="客户所在地" prop="customerSource">
              <el-select v-model="listQuery.customerSource" placeholder="请选择">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in workLocation"
                  :key="item.id"
                  :label="item.sourceName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row type="flex" class="row-bg" justify="end">
            <el-form-item>
              <div class="searchTwoButton">
                <el-button
                  type="primary"
                  size="small"
                  class="searchCondition"
                  @click="handerSearch('listQuery')"
                >查询</el-button>
                <el-button plain size="small" @click="resetForm">重置</el-button>
              </div>
            </el-form-item>
          </el-row>
        </el-form>
      </template>
      <template slot="body">
        <div style="text-align:right;margin-bottom:10px;">
          <info :info="info"></info>
        </div>
        <xyy-table
          :data="list"
          :col="col"
          :list-query="listQuery"
          :offset-top="240"
          :is-pagination="false"
          :is-stripe="false"
        ></xyy-table>
      </template>
    </xyy-list-page>
    <expor-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    ></expor-tip>
  </div>
</template>

<script>
import exporTip from '@/views/work-sheet/components/exportTip';
import XyyListPage from '../../components/xyy/xyy-list-page/index';
import { getReport, exportData } from '@/api/QClassification/index';
import {
  getCustomerSourceList,
  getWorkorderTypeList
} from '@/api/report/index';
export default {
  name: 'Qclassification',
  components: { XyyListPage, exporTip },
  data() {
    return {
      changeExport: false,
      info: [
        { title: '工单问题分类名称', info: '信息展示项，非统计项 ' },
        {
          title: '创建工单数(当日新增)',
          info:
            '工单创建时间在所选时间内，工单类型为所选工单类型，计算各问题分类工单的总量 '
        },
        {
          title: '完成工单数（当日新增）',
          info:
            '工单创建时间在所选时间内，工单完结时间在所选时间内，计算各问题分类工单的总量'
        },
        {
          title: '完成工单数（历史工单）',
          info:
            '工单创建时间早于所选时间，工单完结时间在所选时间内，计算各问题分类工单的总量 '
        },
        {
          title: '完成工单数（所有工单）',
          info:
            '工单完结时间在当日内，不论工单创建时间是哪天，计算这些工单的总量结案工单数（所有工单）=结案工单数（当日新增）+结案工单数（历史工单） '
        },
        {
          title: '平均完结时长（当日新增）',
          info:
            '工单创建时间在所选时间内，工单完结时间在所选时间内，计算这些工单创建时间到完结时间的平均用时 '
        },
        {
          title: '平均完结时长（历史工单）',
          info:
            '工单创建时间早于所选时间，工单完结时间在所选时间内，计算这些工单创建时间到完结时间的平均用时'
        },
        {
          title: '平均完结时长（所有工单）',
          info:
            '工单完结时间在当日内，不论工单创建时间是哪天，计算这些工单创建时间到完结时间的平均用时 '
        }
      ],
      list: [], // 表单数据
      listQuery: {
        dataRange: [new Date(new Date().setHours(0, 0, 0, 0)), new Date()], // 日期
        formTypeId: '', // 工单类型
        customerSource: '' // 客户所在地
      },
      startTime: '',
      endTime: '',
      rules: {
        dataRange: [
          { required: true, message: '时间为必填项', trigger: 'change' }
        ]
      },
      col: [
        {
          index: 'questionName',
          name: '问题分类',
          ellipsis: true,
          resizable: true,
          width: 200,
          fixed: true
        },
        {
          index: 'newlyAddNum',
          name: '创建工单数',
          smallName: '当日新增',
          resizable: true
        },
        {
          index: 'newlyAddClosedNum',
          name: '完成工单数',
          smallName: '当日新增',
          resizable: true
        },
        {
          index: 'historyClosedNum',
          name: '完成工单数',
          smallName: '历史工单',
          resizable: true
        },
        {
          index: 'allClosedNum',
          name: '完成工单数',
          smallName: '所有工单',
          resizable: true
        },
        {
          index: 'newlyAddClosedTime',
          name: '平均完结时长(天)',
          smallName: '当日新增',
          width: 150,
          resizable: true
        },
        {
          index: 'historyClosedTime',
          name: '平均完结时长(天)',
          smallName: '历史工单',
          width: 150,
          resizable: true
        },
        {
          index: 'allClosedTime',
          name: '平均完结时长(天)',
          smallName: '所有工单',
          width: 150,
          resizable: true
        }
      ],
      workType: [], // 工单类型选项
      workLocation: [], // 客户所在地,
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.minDate = minDate;
          this.maxDate = maxDate;
        },
        disabledDate: time => {
          const curDate = new Date().getTime();
          const two = 365 * 2 * 24 * 3600 * 1000;
          const twoyear = curDate - two;
          const three = 92 * 24 * 3600 * 1000;
          if (this.minDate) {
            return (
              time.getTime() > Date.now() ||
              time.getTime() < twoyear ||
              time > new Date(this.minDate.getTime() + three) ||
              time < new Date(this.minDate.getTime() - three)
            );
          }
          return time.getTime() > Date.now() || time.getTime() < twoyear;
        }
      }
    };
  },
  created() {
    this.getDropDownList();
  },
  mounted() {
    // this.handerSearch('listQuery');
  },
  methods: {
    handleChangeExport(info) {
      const that = this;
      this.changeExport = false;
      if (info === 'go') {
        that.$router.push({
          path: '/other/index/'
        });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    exportData() {
      const that = this;
      if (!that.list || !that.list.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      const params = {
        endTime: new Date(that.listQuery.dataRange[1]).Format('yy-MM-dd'),
        startTime: new Date(that.listQuery.dataRange[0]).Format('yy-MM-dd'),
        formTypeId: that.listQuery.formTypeId,
        customerSource: that.listQuery.customerSource
      };
      exportData(params).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.changeExport = true;
      });
    },
    // 点击搜索事件
    handerSearch(formName) {
      const that = this;
      // 验证日期为必填项
      this.$refs[formName].validate(valid => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(255,255,255, 0.8)'
          });
          const params = {
            endTime: new Date(that.listQuery.dataRange[1]).Format('yy-MM-dd'),
            startTime: new Date(that.listQuery.dataRange[0]).Format('yy-MM-dd'),
            formTypeId: that.listQuery.formTypeId,
            customerSource: that.listQuery.customerSource
          };
          getReport(params).then(res => {
            if (res.code !== 1) {
              this.$XyyMessage.warning(res.msg);
              loading.close();
              return;
            }
            if (res.data && res.data.length === 0) {
              loading.close();
              this.$XyyMessage.success('暂时无数据');
              return;
            }
            setTimeout(() => {
              loading.close();
            }, 1000);
            this.list = res.data;
          });
        }
      });
    },
    // 获取表单数据
    seachDataList() {
      const params = {
        endTime: new Date(this.listQuery.dataRange[1]).Format('yy-MM-dd'),
        startTime: new Date(this.listQuery.dataRange[0]).Format('yy-MM-dd'),
        customerSource: this.listQuery.customerSource,
        formTypeId: this.listQuery.formTypeId
      };
      getReport(params).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.list = res.data;
      });
    },
    // 获取客户来源和工单类型数据
    getDropDownList() {
      // 获取工单类型数据
      getWorkorderTypeList().then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.listQuery.formTypeId = res.data[0].id;
        //      将工单类型存储本地 随时获取
        sessionStorage.setItem('formTypeId', res.data[0].id);
        this.workType = res.data;

        // listQuery.formTypeId
      });
      // 获取客户所在地数据
      getCustomerSourceList().then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.workLocation = res.data;
      });
    },
    resetForm() {
      this.listQuery = {
        dataRange: [new Date(new Date().setHours(0, 0, 0, 0)), new Date()], // 日期
        formTypeId: sessionStorage.getItem('formTypeId'), // 工单类型
        customerSource: '' // 客户所在地
      };
    }
  }
};
</script>
<style scoped lang="scss">
/deep/.el-form-item__label {
  font-weight: 500;
}
/deep/.el-table {
  /deep/.el-table__fixed {
    height: auto !important;
    bottom: 12px;
    margin-bottom: 0 !important;
  }
  /deep/.el-table__fixed-body-wrapper {
    top: 65px !important;
  }
}
/deep/.el-date-editor {
  /*width: 450px;*/
}
/deep/.page-header {
  padding-bottom: 0 !important;
}
.search-form {
  border-bottom: 1px dashed #e4e4eb;
}
.classificationAll {
  .table-header-row {
    .cell {
      p {
        white-space: nowrap;
      }
    }
  }
}
</style>
<style scoped lang="scss">
/deep/.el-table {
  /deep/.cell {
    white-space: nowrap;
  }
}
.herader-title {
  font-size: 14px;
  color: #393943;
  padding-bottom: 20px;
  border-bottom: 1px dashed #e4e4eb;
  margin-bottom: 20px;
}
.classificationAll {
  .el-col-1 {
    width: 1.16667%;
    padding-top: 13px;
  }
}
.popoverAll {
  p {
    color: rgba(144, 147, 153, 1);
    font-size: 12px;
    span {
      font-size: 14px;
      color: rgba(41, 41, 51, 1);
    }
  }
}
.gdnumheader {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.gdnum_input_group {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.searchTwoButton {
  width: 100%;
  margin-left: 30px;
}
.gdnum_input_group .el-form-item__error {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  margin-left: 15px;
}
/deep/.el-table__body tr td {
  border-bottom: 1px solid #ebeef5;
  background: #fff;
}
.el-menu-item.is-active {
  color: #3b95a8 !important;
}
/deep/.el-input__inner {
  height: 36px !important;
  line-height: 36px !important;
}
/deep/.el-button {
  height: 36px;
}
.el-menu.el-menu--horizontal {
  border-bottom: 1px dashed #e4e4eb;
  padding-bottom: 20px;
}
.searchCondition.is-plain {
  background: rgba(59, 149, 168, 1);
  color: #fff;
}
/deep/.searchCondition.is-plain:hover {
  background: rgba(40, 126, 144, 1);
}
/deep/.el-table th {
  padding-top: 10px;
  padding-bottom: 10px;
  text-align: right;
  padding-right: 19px;
  &:first-child {
    text-align: left;
  }
  p {
    margin: 0;
    height: 20px;
  }
  small {
    color: #909399;
    font-size: 12px;
    height: 17px;
  }
}
</style>
