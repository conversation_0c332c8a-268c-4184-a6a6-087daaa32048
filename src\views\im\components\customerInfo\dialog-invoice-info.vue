<template>
  <!-- 开票信息弹窗 -->
  <el-dialog class="dialog-invoice-info-container" width="560px" append-to-body destroy-on-close :show-close="false"
    :visible.sync="dialogVisible" v-if="dialogVisible">
    <div class="dialog-body">
      <div class="dialog-title">
        <span class="title-txt">开票信息</span>
        <i class="el-icon-close" @click="handleCloseDialog"></i>
      </div>
      <div class="dialog-content">
        <div class="content-box" v-if="viewDataList.length">
          <div class="invoice-info-box">
            <template v-for="(item, index) in viewDataList">
              <div class="info-item" :key="index">
                <div class="item-ttl">{{ item.label }}：</div>
                <div class="item-cnn">{{ item.value }}<span class="item-cnn-copy" v-if="item.copyVisible"
                    @click="handleCopy(item)">复制</span>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div class="empty-box" v-else>
          <img class="empty-icon" src="@/assets/common/icon-empty.png" />
          <span class="empty-txt">暂无开票信息</span>
        </div>
      </div>
      <div class="dialog-footer">
        <el-button class="btn-primary" @click="handleCloseDialog">关闭</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { queryInvoiceInfo } from '@/api/im_view/customeOrder';
export default {
  name: "dialog-invoice-info",
  data() {
    return {
      dialogVisible: false,

      viewDataList: [],
    }
  },
  methods: {
    init({ merchantId }) {

      this.viewDataList = []

      this.fetchOrderinvoice({ merchantId })

      this.$nextTick(() => {
        this.dialogVisible = true;
      })
    },

    // 查询开票信息
    async fetchOrderinvoice({ merchantId }) {
      try {
        const { code, data, msg } = await queryInvoiceInfo({ merchantId })
        if (code == 1) {
          if (data && Object.keys(data).length) {
            this.viewDataList = [
              {
                label: '发票类型',
                value: data.invoiceTypeName || '尚未登记',
                copyVisible: false
              },
              {
                label: '纳税人识别号',
                value: data.taxRegistryNumber || '尚未登记',
                copyVisible: !!data.taxRegistryNumber
              },
              {
                label: '开户银行',
                value: data.bankName || '尚未登记',
                copyVisible: !!data.bankName
              },
              {
                label: '银行账号',
                value: data.bankAccount || '尚未登记',
                copyVisible: !!data.bankAccount
              },
              {
                label: '公司名称',
                value: data.customerName || '尚未登记',
                copyVisible: !!data.customerName
              },
              {
                label: '注册地址',
                value: data.reAddr || '尚未登记',
                copyVisible: !!data.reAddr
              },
              {
                label: '营业执照地址',
                value: data.addr || '尚未登记',
                copyVisible: !!data.addr
              },
              {
                label: '电话',
                value: data.userMobile || '尚未登记',
                copyVisible: !!data.userMobile
              }
            ]
          }
        } else {
          throw new Error(msg)
        }
      } catch (e) {
        this.$XyyMessage.error(e.message);
      } finally { }
    },

    // 复制
    handleCopy(dateItem) {
      try {
        let input = document.createElement('textarea');
        input.value = dateItem.value;
        document.body.appendChild(input);
        input.select();
        document.execCommand("copy");
        document.body.removeChild(input);
        this.$XyyMessage.success("操作成功");
      } catch (e) {
        console.log(e)
      }
    },

    // 关闭弹窗
    handleCloseDialog() {
      this.dialogVisible = false;
    },
  }
}
</script>

<style lang="scss" scoped>
.dialog-invoice-info-container {
  /deep/ {
    .el-dialog {
      border-radius: 4px;

      .el-dialog__header {
        display: none;
      }

      .el-dialog__body {
        width: 100%;
        padding: 20px;
        box-sizing: border-box;

        .dialog-body {
          width: 100%;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: stretch;

          .dialog-title {
            width: 100%;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .title-txt {
              flex-grow: 1;
              color: #222222;
              font-size: 16px;
              font-weight: 400;
            }

            .el-icon-close {
              flex-shrink: 0;
              cursor: pointer;
              font-size: 16px;
              font-weight: bold;

              &:hover {
                opacity: 0.6;
              }
            }
          }

          .dialog-content {
            width: 100%;
            margin-bottom: 20px;

            .content-box {
              width: 100%;
              display: flex;
              flex-direction: column;
              justify-content: flex-start;
              align-items: stretch;

              .invoice-info-box {
                width: 100%;
                border: 1px solid #D9DEE3;
                border-radius: 2px;
                padding: 16px 12px;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: stretch;

                .info-item {
                  width: 100%;
                  margin-bottom: 12px;
                  display: flex;
                  justify-content: flex-start;
                  align-items: flex-start;

                  &:last-child {
                    margin-bottom: 0;
                  }

                  .item-ttl {
                    flex-shrink: 0;
                    width: 104px;
                    line-height: 1.4;
                    color: #222222;
                    font-size: 14px;
                    font-weight: 400;
                  }

                  .item-cnn {
                    flex-grow: 1;
                    line-height: 1.4;
                    color: #222222;
                    font-size: 14px;
                    font-weight: 400;

                    .item-cnn-copy {
                      width: fit-content;
                      display: inline-block;
                      line-height: 1;
                      background-color: #fff;
                      border: 1px solid #D4D9DD;
                      border-radius: 3px;
                      padding: 2px 6px;
                      margin-left: 12px;
                      box-sizing: border-box;
                      cursor: pointer;
                      user-select: none;
                      color: #222222;
                      font-size: 12px;
                      font-weight: 400;

                      &:hover {
                        background-color: #f1fff7;
                        border-color: #3B95A8;
                        color: #3B95A8;
                      }

                      &:active {
                        background-color: #3B95A8;
                        color: #ffffff;
                      }
                    }
                  }
                }
              }
            }

            .empty-box {
              width: 100%;
              padding: 60px 0;
              display: flex;
              flex-direction: column;
              justify-content: flex-start;
              align-items: center;

              .empty-icon {
                width: 180px;
                height: 150px;
                margin-bottom: 20px;
              }

              .empty-txt {
                color: #666666;
                font-size: 16px;
                font-weight: 400;
              }
            }
          }

          .dialog-footer {
            width: 100%;
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .el-button {
              padding: 9px 18px;
              border-radius: 4px;
              font-size: 14px;
              font-weight: 500;

              &:hover {
                opacity: 0.6;
              }

              &:active {
                opacity: 1;
              }

              +.el-button {
                margin-left: 16px;
              }

              &.btn-primary {
                color: #ffffff;
                background-color: #3B95A8;
                border-color: #3B95A8;
              }

              &.btn-default {
                color: #222222;
                background-color: #fff;
                border-color: #D4D9DD;
              }
            }
          }
        }
      }
    }
  }
}
</style>