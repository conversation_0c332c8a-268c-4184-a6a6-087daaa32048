<template>
  <div class="fields-box">
    <div class="fields-box-header">
      <slot name="header" />
    </div>
    <div class="fields-box-main">
      <slot name="main" />
    </div>
  </div>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON>'
};
</script>

<style lang="scss" scoped>
.fields-box {
  border: 1px solid #eee;
  &-header {
    padding: 0 20px;
    background: #eee;
    height: 50px;
    line-height: 50px;
  }
  &-main {
    overflow-y: auto;
    max-height: calc(100% - 50px);
    padding: 0 20px;
    position: relative;
    min-height: calc(100% - 50px);
  }
}
.select-box {
  .fields-box-header {
    height: 36px;
    line-height: 36px;
  }
}
</style>
