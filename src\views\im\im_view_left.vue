<template>
  <div class="im-view-left-container">
    <!-- 客服信息216 -->
    <div class="head-layout-216">
      <!-- 网络错误提示 -->
      <template v-if="isShowNetworkErrorTip">
        <el-alert class="network-error-tips" title="消息读取超时，请检查网络" type="error" :closable="false" show-icon></el-alert>
      </template>

      <!-- 详情信息 -->
      <div class="kf-info-wrap">
        <!-- 图像 -->
        <img :src="kfAvatar" class="kf-hearder-pic" />
        <div class="kf-detail-box">
          <!-- 名称 -->
          <div class="kf-info-name">{{ kfName }}</div>
          <div class="kf-info-status">
            <!-- 状态切换 -->
            <el-dropdown trigger="click" @command="commandKeFuStatusChange"
              @visible-change="kfStatusChangeDropdownVisibleChange">
              <div class="kf-online-status">
                <img class="status-icon" :src="onlineStatusList[kfOnlineStatus]['icon']" />
                <span class="status-label">{{
                  onlineStatusList[kfOnlineStatus]['label']
                }}</span>
                <img class="status-arrow" :class="[onlineStatusArrowDown ? '' : 'arrow-up']" :src="require('../../assets/im/kf_online_status/icon_arrow_down.png')
                  " />
              </div>
              <el-dropdown-menu slot="dropdown" class="kf-status-dropdown">
                <template v-for="(item, index) in onlineStatusList">
                  <template v-if="item['code'] !== '0'">
                    <el-dropdown-item :key="index" :command="item['code']" :attr="item['code']">
                      <img :src="item['icon']" />
                      <span>{{ item['label'] }}</span>
                    </el-dropdown-item>
                  </template>
                </template>
              </el-dropdown-menu>
            </el-dropdown>
            <!-- 时长 -->
            <div class="kf-status-timer">{{ kfOnlineTiming }}</div>
          </div>
        </div>
      </div>

      <!-- 接线数,排队数 -->
      <div class="kf-status-serve">
        <span>服务人数&nbsp;({{ currentDialogCount }}/{{ kfMaxDialogNumber }})</span>
        <span>排队人数&nbsp;({{ kfQueuingNumber }})</span>
      </div>
    </div>

    <!-- 客服信息旧版 -->
    <template v-if="false">
      <div class="head-layout">
        <!-- 客服图像,状态切换 -->
        <div class="kf-status-wrap">
          <el-dropdown trigger="click" size="small" @command="commandKeFuStatusChange">
            <div class="kf-header-box">
              <img :src="kfAvatar" class="kf-hearder-pic" />
              <span :style="{
                backgroundColor: onlineStatusList[kfOnlineStatus]['color']
              }" class="kf-header-status"></span>
            </div>
            <el-dropdown-menu slot="dropdown" class="kf-status-dropdown">
              <template v-for="(item, index) in onlineStatusList">
                <template v-if="item['code'] !== '0'">
                  <el-dropdown-item :key="index" :command="item['code']">{{
                    item['label']
                  }}</el-dropdown-item>
                </template>
              </template>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <!-- 时长 -->
        <div class="kf-status-timer">
          {{ onlineStatusList[kfOnlineStatus]['label'] }}&nbsp;&nbsp;{{
            kfOnlineTiming
          }}
        </div>
        <!-- 接线数,排队数 -->
        <div class="kf-status-serve">
          <span>服务人数:{{ chatlist_ing.length }}/{{ kfMaxDialogNumber }}</span>
          <span>排队人数:{{ kfQueuingNumber }}</span>
        </div>
      </div>
    </template>

    <!-- 搜索 -->
    <div class="search-popup">
      <el-input v-model="keywordSearch" placeholder="请输入用户名称/手机号搜索" size="small" clearable
        @keyup.enter.native="reqKeywordSearch" @focus="keywordSearchLayerShow = false">
        <i slot="suffix" class="el-input__icon el-icon-search" @click="reqKeywordSearch"></i>
      </el-input>
      <div v-if="keywordSearchLayerShow" class="search-popup-list">
        <template v-for="(item, index) in keywordSearchtList">
          <div :key="index" class="search-res-item" @click="keywordSearchSelected(item)">
            <img :src="require('../../../static/user_kh.png')" class="res-item-left" />
            <div class="res-item-right">
              <span>{{ item.realName }}</span>
              <span>{{ item.mobile }}</span>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 对话四个列表 -->
    <div class="chat-list">
      <!-- tab切换 -->
      <div class="chat-tabs-header">
        <div :class="{ 'is-active': activeTab === '0' }" class="tabs-header-item" @click="onClickTabChangeTo('0')">
          <!-- 
          <div class="chat-el-icon">
            <i class="el-icon-chat-dot-round"></i>
          </div>
          <span class="chat-tabs-tip">会话中</span> 
          -->
          <div class="header-item-label">会话中</div>
          <div class="header-item-label-bottom"></div>
        </div>
        <div :class="{ 'is-active': activeTab === '1' }" class="tabs-header-item" @click="onClickTabChangeTo('1')">
          <!-- 
          <div class="chat-el-icon">
            <i class="el-icon el-icon-time"></i>
          </div>
          <span class="chat-tabs-tip">待跟进</span>
          -->
          <div class="header-item-label">待跟进</div>
          <div class="header-item-label-bottom"></div>
          <span class="chat-el-badge" v-if="chatlist_followed_total">{{
            chatlist_followed_total
          }}</span>
        </div>
        <div :class="{ 'is-active': activeTab === '2' }" class="tabs-header-item" @click="onClickTabChangeTo('2')">
          <!-- 
          <div class="chat-el-icon">
            <i class="el-icon el-icon-date"></i>
          </div>
          <span class="chat-tabs-tip">历史会话</span>
          -->
          <div class="header-item-label">历史会话</div>
          <div class="header-item-label-bottom"></div>
        </div>
        <div :class="{ 'is-active': activeTab === '3' }" class="tabs-header-item" @click="onClickTabChangeTo('3')">
          <!-- 
          <div class="chat-el-icon">
            <i class="el-icon el-icon-edit-outline"></i>
          </div>
          <span class="chat-tabs-tip">留言</span>
          -->
          <div class="header-item-label">留言</div>
          <div class="header-item-label-bottom"></div>
          <span class="chat-el-badge" style="right:12px" v-if="chatlist_leaveMessage_total">{{
            chatlist_leaveMessage_total
          }}</span>
        </div>
      </div>
      <!-- tab内容 -->
      <div class="chat-tabs-content">
        <div :class="{ 'is-active': activeTab === '0' }" class="tabs-content-item">
          <div class="btn-sort-wrap">
            <el-dropdown trigger="click" size="medium" @command="commandChattingSort"
              @visible-change="sortDropdownVisibleChange">
              <el-button type="text">
                {{ chatlist_ing_sortType_list[chatlist_ing_sortType]['label'] }}
                <i :class="[
                  chatlist_ing_sortType_arrow_down
                    ? 'el-icon-caret-bottom'
                    : 'el-icon-caret-top'
                ]"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown" class="sort-dropdown">
                <template v-for="item in chatlist_ing_sortType_list">
                  <el-dropdown-item :key="item['type']" :command="item['type']">{{ item['label'] }}</el-dropdown-item>
                </template>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <left-chatlist tab="0" ref="ref_tab_0" @click="emitChatItemClick"
            @remove="emitChatItemRemove"></left-chatlist>

          <template v-if="chatlist_ing.concat(chatlist_closed).length">
            <div class="pager">
              <template v-if="isShowLoadMoreBtn_chatlist_ing">
                <el-button size="mini" type="text" :loading="isShowLoadMoreLoading_chatlist_ing"
                  @click="onClickLoadMore('0')">{{
                    isShowLoadMoreLoading_chatlist_ing
                      ? '加载中...'
                      : '加载更多'
                  }}</el-button>
              </template>
              <template v-else>
                <span>- 没有更多了 -</span>
              </template>
            </div>
          </template>
        </div>
        <div :class="{ 'is-active': activeTab === '1' }" class="tabs-content-item">
          <left-chatlist tab="1" ref="ref_tab_1" @click="emitChatItemClick"
            @remove="emitChatItemRemove"></left-chatlist>
          <template v-if="chatlist_followed.length">
            <div class="pager">
              <template v-if="isShowLoadMoreBtn_chatlist_followed">
                <el-button size="mini" type="text" :loading="isShowLoadMoreLoading_chatlist_followed"
                  @click="onClickLoadMore('1')">{{
                    isShowLoadMoreLoading_chatlist_followed
                      ? '加载中...'
                      : '加载更多'
                  }}</el-button>
              </template>
              <template v-else>
                <span>- 没有更多了 -</span>
              </template>
            </div>
          </template>
        </div>
        <div :class="{ 'is-active': activeTab === '2' }" class="tabs-content-item">
          <div class="form-search-wrap">
            <el-input v-model="keywordSearchHistorical" size="small" :maxlength="100" placeholder="请输入聊天记录/访客名称"
              clearable @keyup.enter.native="keywordSearchHistoricalClick">
              <i slot="suffix" class="el-input__icon el-icon-search" @click="keywordSearchHistoricalClick"></i>
            </el-input>
          </div>
          <left-chatlist tab="2" ref="ref_tab_2" @click="emitChatItemClick"
            @remove="emitChatItemRemove"></left-chatlist>
          <template v-if="chatlist_historical.length">
            <div class="pager">
              <template v-if="isShowLoadMoreBtn_chatlist_historical">
                <el-button size="mini" type="text" :loading="isShowLoadMoreLoading_chatlist_historical"
                  @click="onClickLoadMore('2')">{{
                    isShowLoadMoreLoading_chatlist_historical
                      ? '加载中...'
                      : '加载更多'
                  }}</el-button>
              </template>
              <template v-else>
                <span>- 没有更多了 -</span>
              </template>
            </div>
          </template>
        </div>
        <div :class="{ 'is-active': activeTab === '3' }" class="tabs-content-item">
          <left-chatlist tab="3" ref="ref_tab_3" @click="emitChatItemClick"
            @remove="emitChatItemRemove"></left-chatlist>
          <template v-if="chatlist_leaveMessage.length">
            <div class="pager">
              <template v-if="isShowLoadMoreBtn_chatlist_leaveMessage">
                <el-button size="mini" type="text" :loading="isShowLoadMoreLoading_chatlist_leaveMessage"
                  @click="onClickLoadMore('3')">{{
                    isShowLoadMoreLoading_chatlist_leaveMessage
                      ? '加载中...'
                      : '加载更多'
                  }}</el-button>
              </template>
              <template v-else>
                <span>- 没有更多了 -</span>
              </template>
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 转接提醒弹窗 -->
    <el-dialog :visible.sync="isShowTransferDialog" :show-close="false" :close-on-press-escape="false"
      :close-on-click-modal="false" :modal-append-to-body="true" :append-to-body="true" title="提示" width="30%">
      <span>您有新的转接信息，是否同意转接？</span>
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="transferRejectBtnDisabled" @click="transferReject">拒 绝</el-button>
        <el-button type="primary" :disabled="transferResolveBtnDisabled" @click="transferResolve">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  kfUserInfo,
  kfChangeState,
  searchKeyword,
  getDialogPageList,
  kfdialogQueueSize,
  readHistoryMessageList,
  readMessageList,
  transferReject,
  transferAccept,
  saveDialogSort,
  getDialogInfo,
  sendlog,
  getKefuFirstMsgTimeExcludeAutoMsg
} from '@/api/im_view/index';
import LeftChatlist from './components/left_chatlist';

export default {
  name: '',
  components: {
    LeftChatlist
  },
  filters: {},
  props: {},
  inject: ['compLayout', 'compIMViewMain'],
  data() {
    return {
      kfInfoDetail: {}, // 客服所有信息详情
      kfAvatar: '../../../pc/static/user_kf.png', // 头像
      kfName: '', //名称
      kfOnlineStatus: '5', // 在线状态,初始离线
      onlineStatusList: {
        0: {
          code: '0',
          label: '未知',
          color: '#999999',
          icon: require('../../assets/im/kf_online_status/<EMAIL>')
        },
        1: {
          code: '1',
          label: '在线',
          color: '#67C23A',
          icon: require('../../assets/im/kf_online_status/<EMAIL>')
        },
        2: {
          code: '2',
          label: '培训',
          color: '#60B1FF',
          icon: require('../../assets/im/kf_online_status/<EMAIL>')
        },
        3: {
          code: '3',
          label: '忙碌',
          color: '#F08557',
          icon: require('../../assets/im/kf_online_status/<EMAIL>')
        },
        4: {
          code: '4',
          label: '就餐',
          color: '#F0AC4E',
          icon: require('../../assets/im/kf_online_status/<EMAIL>')
        },
        5: {
          code: '5',
          label: '离线',
          color: '#999999',
          icon: require('../../assets/im/kf_online_status/<EMAIL>')
        },
        7: {
          code: '7',
          label: '小休',
          color: '#C6C6C6',
          icon: require('../../assets/im/kf_online_status/<EMAIL>')
        },
        8: {
          code: '8',
          label: '会议',
          color: '#6098FF',
          icon: require('../../assets/im/kf_online_status/<EMAIL>')
        }
      }, // 客服在线状态
      onlineStatusArrowDown: true, //客服状态下拉箭头方向
      kfOnlineTiming: '00:00:00', // 在线状态时长
      kfOnlineTimer: null, // 在线状态时长计时器

      kfMaxDialogNumber: '0', // 客服服务人数上限
      kfQueuingNumber: '0', // 排队人数

      keywordSearch: '', // 搜索框v-model
      keywordSearchtList: [], // 搜索结果list
      keywordSearchLayerShow: false, // 搜索结果浮层

      activeTab: '0', // 0:会话中,1:待跟进,2:历史会话,3:留言
      selectedChat: '', //当前选中会话对象，用于切换列表时，重新定位会话
      selectedChatTab: '', //当前选中了会话的那个tab，用于切换列表时，重新定位会话

      chatlist_ing: [], //会话中
      chatlist_closed: [], //会话中已关闭
      chatlist_ing_pageNum: 1,
      chatlist_ing_pageSize: 50,
      chatlist_ing_sortType: '0', //排序方式,0,1,2
      chatlist_ing_sortType_list: {
        0: {
          type: '0',
          label: '按对话接入时间排序'
        },
        1: {
          type: '1',
          label: '按任意方新消息排序'
        },
        2: {
          type: '2',
          label: '按访客方新消息排序'
        }
      },
      chatlist_ing_sortType_arrow_down: true, //会话中排序选择按钮下拉箭头方向
      isShowLoadMoreBtn_chatlist_ing: true, //会话中，是否显示加载更多按钮
      isShowLoadMoreLoading_chatlist_ing: false, //会话中，是否显示正在加载loading

      chatlist_followed: [], //待跟进
      chatlist_followed_pageNum: 1,
      chatlist_followed_pageSize: 100,
      chatlist_followed_total: 0, //待跟进总条数
      isShowLoadMoreBtn_chatlist_followed: true, //待跟进，是否显示加载更多按钮
      isShowLoadMoreLoading_chatlist_followed: false, //待跟进，是否显示正在加载loading

      chatlist_historical: [], //历史会话
      chatlist_historical_pageNum: 1,
      chatlist_historical_pageSize: 100,
      keywordSearchHistorical: '', //历史会话搜索框v-model
      isShowLoadMoreBtn_chatlist_historical: true, //历史会话，是否显示加载更多按钮
      isShowLoadMoreLoading_chatlist_historical: false, //历史会话，是否显示正在加载laoding

      chatlist_leaveMessage: [], //留言
      chatlist_leaveMessage_pageNum: 1,
      chatlist_leaveMessage_pageSize: 100,
      chatlist_leaveMessage_total: 0, //留言总条数
      isShowLoadMoreBtn_chatlist_leaveMessage: true, //留言，是否显示加载更多按钮
      isShowLoadMoreLoading_chatlist_leaveMessage: false, //留言，是否显示正在加载loading

      isRunPolling: false, //轮询开关,true:开启,false:终止
      pollingTimer: null, //轮询间隔计时器
      pollingTimeout: 0, //轮询间隔时间
      isFromCreatedHook: false, //activated钩子是否从created钩子顺序执行

      isShowTransferDialog: false, //转接弹窗是否显示
      transferInfo: {}, //转接信息对象

      isShowNetworkErrorTip: false, //是否显示轮询错误的提示语

      lastMessageId: '', //本次轮询到的消息Id
      readVersionId: '', //本次轮询到的版本号Id

      transferRejectBtnDisabled: false, //拒接转接按钮禁用
      transferResolveBtnDisabled: false, //确定转接按钮禁用

      sign: '', //本次轮询发送上一次轮询处理记录的 sign

      initLoading: null, //页面初始加载时的loading对象

      timerLeaveMessageCountdown: null, // 留言未回复倒计时
    };
  },
  computed: {
    hasIMView: function () {
      return this.$store.getters.visitedViews.find(item => {
        return item.path === '/imcontainer/list';
      })
        ? true
        : false;
    },

    //ws通知是否刷新客服状态
    wsKFOnlineStatusRefrest: function () {
      return this.compLayout.wsKFOnlineStatusRefrest;
    },

    // 服务人数计算器
    currentDialogCount: function () {
      // 会话中数量
      const chatingCount = this.chatlist_ing.filter(chat => chat.dialogEndScene === 0).length
      // 会话中,留言访客未回复
      const leaveCount = this.chatlist_ing.filter(chat => chat.dialogEndScene === 0 && chat.dialogStartScene == 2 && chat.customerUnReplyFromLeaveMessage === 0).length
      console.log(this.chatlist_ing)
      return chatingCount - leaveCount
    }
  },
  watch: {
    //监控轮询状态值改变
    // isRunPolling(newVal) {
    //   newVal && this.reqReadMessagePolling();
    // },

    //页面关闭，关闭轮询
    hasIMView: function (hasView) {
      if (!hasView) {
        this.isRunPolling = false;
        clearTimeout(this.pollingTimer);
        this.$parent.titleFlashClear();
        //this.$parent.destoryHandler();
        clearTimeout(this.timerLeaveMessageCountdown);
      }
    },

    //刷新客服状态
    wsKFOnlineStatusRefrest: function (val) {
      if (val) {
        this.reqKeFuUserInfo({ beginPolling: false });
      }
    },
  },
  created() {
    this.isFromCreatedHook = true;

    //生成pageId
    if (!this.$store.getters.imcreate) {
      this.$store.commit(
        'imProperty/SET_IMProperty_containercreate',
        new Date().getTime().toString()
      );
    }
  },
  mounted() {
    //添加初始加载全局loading
    this.initLoading = this.$loading({
      lock: true,
      text: '加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(255,255,255, 0.8)'
    });

    this.isRunPolling = true;
    this.reqKeFuUserInfo({ beginPolling: true });
  },
  activated() {
    //移除title闪烁
    this.$parent.titleFlashClear();

    // if (!this.isFromCreatedHook) {
    //   this.isRunPolling = true;
    // }

    if (!this.isRunPolling) {
      this.isRunPolling = true;
      this.reqReadMessagePolling();
    }
  },
  deactivated() {
    this.isFromCreatedHook = false;

    //新消息需要浏览器系统通知，保持轮询
    //this.isRunPolling = false;
    //clearTimeout(this.pollingTimer);//清除轮询定时器
    //this.pollingTimer = null;

    //移除动态生成的排序dropdown
    document.querySelectorAll('.sort-dropdown').forEach(item => {
      item.remove();
    });
    document.querySelectorAll('.kf-status-dropdown').forEach(item => {
      item.remove();
    });
  },
  beforeDestroy() {
    this.isRunPolling = false;
    clearTimeout(this.pollingTimer);
    this.$parent.titleFlashClear();
    //this.$parent.destoryHandler();
    clearTimeout(this.timerLeaveMessageCountdown);
  },
  methods: {
    /**
     * 获取当前客服信息
     */
    reqKeFuUserInfo(params) {
      kfUserInfo()
        .then(resp => {
          if (resp.code === 1) {
            // 客服所有信息详情
            this.kfInfoDetail = resp.data;
            // 头像
            if (this.kfInfoDetail.avatar) {
              this.kfAvatar = this.kfInfoDetail.avatar;
            }
            //名称
            this.kfName =
              this.kfInfoDetail.name ||
              this.kfInfoDetail.nickname ||
              this.kfInfoDetail.username;
            // 在线状态
            this.kfOnlineStatus = this.kfInfoDetail.status.toString();
            // 在线状态计时器
            this.computeKefuOnlineTiming(this.kfInfoDetail.statustime);
            // 客服服务人数上限
            this.kfMaxDialogNumber = this.kfInfoDetail.maxdialog.toString();
            // 请求列表数据
            this.reqInitMessage(params);
          } else {
            //客服信息加载失败，隐藏全局initloading
            this.initLoading && this.initLoading.close();

            this.compIMViewMain.closeCompIMViewMain(resp.msg);
          }
        })
        .catch(() => {
          //客服信息加载失败，隐藏全局initloading
          this.initLoading && this.initLoading.close();

          this.compIMViewMain.closeCompIMViewMain('客服信息加载失败，请重试！');
        });
    },

    /**
     * 计算在线状态计时器
     */
    computeKefuOnlineTiming(statustime) {
      clearInterval(this.kfOnlineTimer);
      this.kfOnlineTimer = null;
      this.kfOnlineTiming = '00:00:00';
      this.kfOnlineTimer = setInterval(() => {
        let currentTime =
          parseInt(Date.parse(new Date()) / 1000) - parseInt(statustime / 1000);
        if (currentTime < 0) {
          currentTime = 0;
        }
        const timeStr = `${parseInt(currentTime / 3600)
          .toString()
          .padStart(2, 0)}:${parseInt((currentTime % 3600) / 60)
            .toString()
            .padStart(2, 0)}:${(currentTime % 60).toString().padStart(2, 0)}`;
        this.kfOnlineTiming = timeStr;
      }, 1000);
    },

    /**
     * 初始请求列表数据
     * 会话中列表
     */
    reqInitMessage({ beginPolling }) {
      Promise.all([this.reqChatlistIngPromise()])
        .then(resp => {
          if (beginPolling) {
            this.initLoading && this.initLoading.close();
            this.reqReadMessagePolling(); //开始轮询获取消息
          }
        })
        .catch(() => {
          //在线会话列表加载失败，隐藏全局initLoading
          this.initLoading && this.initLoading.close();
        }); //catch勿删,否则报错
    },

    /**
     * 切换在线状态
     */
    commandKeFuStatusChange(statusCode) {
      if (this.kfOnlineStatus === statusCode) {
        return false;
      }

      if (Number(this.kfQueuingNumber) === 0) {
        // 排队人数为0
        this.reqUpdateKeFuStatus(statusCode);
      } else {
        // 有排队人数
        this.$confirm(
          `当前排队${this.kfQueuingNumber}人，确定切换为${this.onlineStatusList[statusCode]['label']
          }状态吗？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => {
            this.reqUpdateKeFuStatus(statusCode);
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消切换'
            });
          });
      }
    },

    /**
     * 请求切换在线状态
     */
    reqUpdateKeFuStatus(statusCode) {
      kfChangeState({
        state: statusCode,
        changeReason: 1 // 1.坐席手动, 2.管理员强制;
      }).then(resp => {
        if (resp.code === 1) {
          this.kfInfoDetail.status = this.kfOnlineStatus = statusCode.toString();
          this.kfInfoDetail.statustime = new Date().getTime();
          this.computeKefuOnlineTiming(this.kfInfoDetail.statustime);
        } else {
          this.$XyyMessage.error(resp.msg);
        }
      });
    },

    /**
     * 客服状态切换下拉框出现/隐藏时触发
     * 出现为 true，隐藏为 false
     */
    kfStatusChangeDropdownVisibleChange(status) {
      if (status) {
        this.onlineStatusArrowDown = false;
        this.$nextTick(() => {
          document
            .querySelectorAll('.kf-status-dropdown li')
            .forEach((item, index) => {
              if (
                item.getAttribute('attr').toString() ===
                this.kfOnlineStatus.toString()
              ) {
                if (item.className.indexOf(' selected') === -1) {
                  item.className = item.className + ' selected';
                }
              } else {
                if (item.className.indexOf(' selected') > -1) {
                  item.className = item.className.substring(
                    0,
                    item.className.indexOf(' selected')
                  );
                }
              }
            });
        });
      } else {
        this.onlineStatusArrowDown = true;
      }
    },

    /**
     * 关键词搜索
     */
    reqKeywordSearch() {
      this.keywordSearchtList = [];
      if (!this.keywordSearch) {
        return false;
      }
      searchKeyword(this.keywordSearch).then(resp => {
        if (resp.code === 1) {
          this.keywordSearchtList = [];
          if (Object.prototype.toString.call(resp.data) === '[object Object]') {
            this.keywordSearchtList.push(resp.data);
          } else if (
            Object.prototype.toString.call(resp.data) === '[object Array]'
          ) {
            this.keywordSearchtList = resp.data;
          }
          this.keywordSearchLayerShow = true;
        } else {
          // this.$XyyMessage.error(resp.msg);
          this.$message(resp.msg);
        }
      });
    },

    /**
     * 关键词搜索结果点击
     */
    keywordSearchSelected(user) {
      this.keywordSearchLayerShow = false;

      //四个列表中清除选中标识
      this.chatlist_ing.forEach(item => {
        item.assignChatItemSelected = false;
      });
      this.chatlist_closed.forEach(item => {
        item.assignChatItemSelected = false;
      });
      this.chatlist_followed.forEach(item => {
        item.assignChatItemSelected = false;
      });
      this.chatlist_historical.forEach(item => {
        item.assignChatItemSelected = false;
      });
      this.chatlist_leaveMessage.forEach(item => {
        item.assignChatItemSelected = false;
      });

      this.refUpdateChatListAll();

      //如果在会话列表中，则跳转到会话中并选中,否则展示历史会话
      let chatlistIngFormatChat = this.chatlist_ing.filter(item => {
        return (
          item.uid.toString() === user.id.toString() &&
          item.dialogEndScene === 0
        );
      });
      if (chatlistIngFormatChat.length) {
        //展示会话中
        this.$XyyMessage.warning('当前客户正在与您会话中！');
        this.chatlist_ing.forEach((item, index) => {
          if (item.uid.toString() === chatlistIngFormatChat[0].uid.toString()) {
            this.emitChatItemClick({ tab: '0', chat: item });
            this.onClickTabChangeTo('0');
          }
        });
      } else {
        this.selectedChat = '';
        //展示历史会话
        this.$emit(
          'select',
          {
            type: '4',
            userId: user.id,
            chat: user,
            kefuInfo: this.kfInfoDetail
          },
          { id: '007' }
        );
      }
    },

    /**
     * 会话中列表排序
     * 0:按对话接入时间排序,1:按任意方新消息排序,2:按访客方新消息排序
     * withoutSave true:不需要保存,false:需要保存
     */
    commandChattingSort(sortType, instances, withoutSave) {
      this.chatlist_ing_sortType = sortType;
      switch (sortType) {
        case '0':
          //this.chatlist_ing.sort(this.dateTimeCompare('starttime'));
          this.chatlist_ing = this.dateTimeSort(this.chatlist_ing, 'starttime');
          break;
        case '1':
          //this.chatlist_ing.sort(this.dateTimeCompare('assignLastMessageTime'));
          this.chatlist_ing = this.dateTimeSort(
            this.chatlist_ing,
            'assignLastMessageTime'
          );
          break;
        case '2':
          //this.chatlist_ing.sort(this.dateTimeCompare('customLastMessageTime'));
          this.chatlist_ing = this.dateTimeSort(
            this.chatlist_ing,
            'customLastMessageTime'
          );
          break;
      }

      this.refUpdateChatList({ tab: '0' });

      this.$forceUpdate();

      if (!withoutSave) {
        //保存排序到服务端
        saveDialogSort({
          sort: Number(sortType)
        })
          .then(resp => {
            if (resp.code === 1) {
            } else {
              this.$XyyMessage.error('排序方式保存失败！');
            }
          })
          .catch(() => {
            this.$XyyMessage.error('排序方式保存失败！');
          });
      }
    },

    /**
     * 会话中排序下拉框出现/隐藏时触发
     * 出现则为 true，隐藏则为 false
     */
    sortDropdownVisibleChange(status) {
      if (status) {
        this.chatlist_ing_sortType_arrow_down = false;
        if (this.chatlist_ing_sortType) {
          this.$nextTick(() => {
            document
              .querySelectorAll('.sort-dropdown li')
              .forEach((item, index) => {
                if (index.toString() === this.chatlist_ing_sortType) {
                  if (item.className.indexOf(' selected') === -1) {
                    item.className = item.className + ' selected';
                  }
                } else {
                  if (item.className.indexOf(' selected') > -1) {
                    item.className = item.className.substring(
                      0,
                      item.className.indexOf(' selected')
                    );
                  }
                }
              });
          });
        }
      } else {
        this.chatlist_ing_sortType_arrow_down = true;
      }
    },

    /**
     * 时间排序，将时间为空的排在最后面
     */
    dateTimeSort(chatlist, field) {
      if (!chatlist.length) {
        return [];
      }
      let list = chatlist;
      let dateList = []; //有时间值
      let dateEmptyList = []; //无时间值

      list.forEach((item, index) => {
        item[field] ? dateList.push(item) : dateEmptyList.push(item);
      });

      return dateList.sort(this.dateTimeCompare(field)).concat(dateEmptyList);
    },

    /**
     * 比较时间
     */
    dateTimeCompare(property) {
      return function (a, b) {
        try {
          let value1 = Date.parse(a[property].replace(/-/g, '/')); //replace(/-/g,"/")是兼容ie和火狐的方法，因为ie和火狐的默认日期格式是2020/08/04 23:22:11
          let value2 = Date.parse(b[property].replace(/-/g, '/'));
          return value2 - value1;
        } catch (err) {
          return undefined;
        }
      };
    },

    /**
     * tab切换
     */
    onClickTabChangeTo(tabIndex, reload) {
      if (this.activeTab === tabIndex && !reload) {
        return false;
      }
      this.activeTab = tabIndex;
      switch (this.activeTab) {
        case '0':
          //会话中
          this.chatlist_ing_pageNum = 1;
          this.reqChatlistIngPromise().catch(() => { });
          break;
        case '1':
          //待跟进
          this.chatlist_followed_pageNum = 1;
          this.reqChatlistFollowedPromise().catch(() => { });
          break;
        case '2':
          //历史会话
          this.chatlist_historical_pageNum = 1;
          this.reqChatlistHistoricalPromise().catch(() => { });
          break;
        case '3':
          //留言
          this.chatlist_leaveMessage_pageNum = 1;
          this.reqChatlistLeaveMessagePromise().catch(() => { });
          break;
      }
    },

    /**
     * 加载更多
     */
    onClickLoadMore(tab) {
      switch (tab) {
        case '0':
          this.chatlist_ing_pageNum += 1;
          this.reqChatlistIngPromise().catch(() => { });
          break;
        case '1':
          this.chatlist_followed_pageNum += 1;
          this.reqChatlistFollowedPromise().catch(() => { });
          break;
        case '2':
          this.chatlist_historical_pageNum += 1;
          this.reqChatlistHistoricalPromise().catch(() => { });
          break;
        case '3':
          this.chatlist_leaveMessage_pageNum += 1;
          this.reqChatlistLeaveMessagePromise().catch(() => { });
          break;
      }
    },

    /**
     * 查询会话中列表
     */
    reqChatlistIngPromise() {
      return new Promise((resolve, reject) => {
        this.isShowLoadMoreLoading_chatlist_ing = true;
        getDialogPageList({
          dialogType: 1,
          pageId: this.$store.getters.imcreate,
          pageNum: this.chatlist_ing_pageNum,
          pageSize: this.chatlist_ing_pageSize
        })
          .then(resp => {
            if (resp.code === 1) {
              //查询第一页时,清空列表
              if (this.chatlist_ing_pageNum === 1) {
                this.chatlist_ing = [];
                this.chatlist_closed = [];
              }

              let scrollTop = 0;
              try {
                scrollTop = document.querySelector('.chat-tabs-content')
                  .scrollTop; //滚动条位置
              } catch (err) { }

              let dialogInfo = null; //会话信息对象
              resp.data.datas.datas.forEach(item => {
                dialogInfo = Object.assign({}, item, {
                  assignCustomHeadImg: item.customHeadImg, //客户图像
                  assignCustomName: item.customName, //客户名称
                  assignLastMessageTime: item.lastMsgTime, //最后一条消息时间
                  assignLastMessage: item.lastMsg, //最后一条消息内容
                  assignUnreadMessageNum: Number(item.unRead) || 0, //未读数量
                  assignChatStatus: item.dialogEndScene, //当前会话状态
                  assignChatItemSelected: false, //是否选中
                  assignHasDialogInfo: true, //有会话对象
                });
                // dialogEndScene	会话关闭类型 0-未关闭，1-系统关闭,2-访客超时关闭,3-客户关闭,4-客服关闭,5-待跟进，6-抢接关闭,7-主动转接关闭
                this[
                  item.dialogEndScene === 0 ? 'chatlist_ing' : 'chatlist_closed'
                ]['push'](dialogInfo);
              });

              //列表加载数据时，重新选中列表加载前的选中项
              this.reSelectChatItem({ tab: '0' });

              this.refUpdateChatList({ tab: '0' });

              // 留言发起的会话进行倒计时
              this.setLeaveMessageCountdown();

              //加载更多按钮
              this.isShowLoadMoreBtn_chatlist_ing = Boolean(
                this.chatlist_closed.length >=
                this.chatlist_ing_pageNum * this.chatlist_ing_pageSize
              );
              this.isShowLoadMoreLoading_chatlist_ing = false;

              //排序方式赋值
              if (resp.data.sort) {
                this.chatlist_ing_sortType = resp.data.sort.toString();
              }

              //待跟进总数赋值
              this.chatlist_followed_total = Number(resp.data.todoDialogTotal) || 0;

              //滚动条重新定位
              this.$nextTick(_ => {
                try {
                  document.querySelector(
                    '.chat-tabs-content'
                  ).scrollTop = scrollTop;
                } catch (err) { }
              });

              this.$forceUpdate();

              resolve({ code: 1 });
            } else if (resp.code === 10002) {
              this.$XyyMessage.error(resp.msg);
              reject({ code: 10002, msg: resp.msg });
            } else {
              this.$XyyMessage.error(resp.msg);
              reject({ code: 0, msg: resp.msg });
            }
          })
          .catch(err => {
            this.$XyyMessage.error(err.message);
            reject({ code: 0, msg: err.message });
          });
      });
    },

    /**
     * 查询待跟进列表
     */
    reqChatlistFollowedPromise() {
      return new Promise((resolve, reject) => {
        this.isShowLoadMoreLoading_chatlist_followed = true;
        getDialogPageList({
          dialogType: 3,
          pageId: this.$store.getters.imcreate,
          pageNum: this.chatlist_followed_pageNum,
          pageSize: this.chatlist_followed_pageSize
        })
          .then(resp => {
            if (resp.code === 1) {
              //查询第一页时,清空列表
              if (this.chatlist_followed_pageNum === 1) {
                this.chatlist_followed = [];
              }

              let scrollTop = 0;
              try {
                scrollTop = document.querySelector('.chat-tabs-content')
                  .scrollTop; //滚动条位置
              } catch (err) { }

              let dialogInfo = null; //会话信息对象
              resp.data.datas.datas.forEach(item => {
                dialogInfo = Object.assign({}, item, {
                  assignCustomHeadImg: item.customHeadImg, //客户图像
                  assignCustomName: item.customName, //客户名称
                  assignLastMessageTime: item.lastMsgTime, //最后一条消息时间
                  assignLastMessage: item.lastMsg, //最后一条消息内容
                  assignUnreadMessageNum: Number(item.unRead) || 0, //未读数量
                  assignChatStatus: item.dialogEndScene, //当前会话状态
                  assignChatItemSelected: false, //是否选中
                  assignHasDialogInfo: true //有会话对象
                });
                this.chatlist_followed.push(dialogInfo);
              });

              //列表加载数据时，重新选中列表加载前的选中项
              this.reSelectChatItem({ tab: '1' });

              this.refUpdateChatList({ tab: '1' });

              //加载更多按钮
              this.isShowLoadMoreBtn_chatlist_followed = Boolean(
                this.chatlist_followed.length >=
                this.chatlist_followed_pageNum *
                this.chatlist_followed_pageSize
              );
              this.isShowLoadMoreLoading_chatlist_followed = false;

              //待跟进总条数赋值
              this.chatlist_followed_total = Number(resp.data.datas.total) || 0;

              //滚动条重新定位
              this.$nextTick(_ => {
                try {
                  document.querySelector(
                    '.chat-tabs-content'
                  ).scrollTop = scrollTop;
                } catch (err) { }
              });

              this.$forceUpdate();

              resolve({ code: 1 });
            } else {
              this.$XyyMessage.error(resp.msg);
              reject({ code: 0, msg: resp.msg });
            }
          })
          .catch(err => {
            this.$XyyMessage.error(err.message);
            reject({ code: 0, msg: err.message });
          });
      });
    },

    /**
     * 查询历史记录列表
     */
    reqChatlistHistoricalPromise() {
      return new Promise((resolve, reject) => {
        this.isShowLoadMoreLoading_chatlist_historical = true;
        readHistoryMessageList({
          //queryType: '3',
          keyword: this.keywordSearchHistorical,
          page: this.chatlist_historical_pageNum,
          pagesize: this.chatlist_historical_pageSize
        })
          .then(resp => {
            if (resp.code === 1) {
              //查询第一页时,清空列表
              if (this.chatlist_historical_pageNum === 1) {
                this.chatlist_historical = [];
              }

              let scrollTop = 0;
              try {
                scrollTop = document.querySelector('.chat-tabs-content')
                  .scrollTop; //滚动条位置
              } catch (err) { }

              let dialogInfo = null; //会话信息对象
              resp.data.datas.forEach(item => {
                dialogInfo = Object.assign({}, item, {
                  assignCustomHeadImg: item.customHeadImg, //客户图像
                  assignCustomName: item.nickname || item.customNickName, //客户名称
                  assignLastMessageTime: item.endtime, //最后一条消息时间
                  assignLastMessage: item.lastMsg, //最后一条消息内容
                  assignUnreadMessageNum: 0, //未读数量
                  assignChatStatus: item.dialogEndScene, //当前会话状态
                  assignChatItemSelected: false, //是否选中
                  assignHasDialogInfo: true //有会话对象
                });
                this.chatlist_historical.push(dialogInfo);
              });

              //列表加载数据时，重新选中列表加载前的选中项
              this.reSelectChatItem({ tab: '2' });

              this.refUpdateChatList({ tab: '2' });

              //加载更多按钮
              this.isShowLoadMoreBtn_chatlist_historical = Boolean(
                this.chatlist_historical.length >=
                this.chatlist_historical_pageNum *
                this.chatlist_historical_pageSize
              );
              this.isShowLoadMoreLoading_chatlist_historical = false;

              //滚动条重新定位
              this.$nextTick(_ => {
                try {
                  document.querySelector(
                    '.chat-tabs-content'
                  ).scrollTop = scrollTop;
                } catch (err) { }
              });

              this.$forceUpdate();

              resolve({ code: 1 });
            } else {
              this.$XyyMessage.error(resp.msg);
              reject({ code: 0, msg: resp.msg });
            }
          })
          .catch(err => {
            this.$XyyMessage.error(err.message);
            reject({ code: 0, msg: err.message });
          });
      });
    },

    /**
     * 历史会话搜索
     */
    keywordSearchHistoricalClick() {
      this.chatlist_historical_pageNum = 1;
      this.chatlist_historical = [];
      this.reqChatlistHistoricalPromise().catch(() => { });
    },

    /**
     * 查询留言列表
     */
    reqChatlistLeaveMessagePromise() {
      return new Promise((resolve, reject) => {
        this.isShowLoadMoreLoading_chatlist_leaveMessage = true;
        getDialogPageList({
          dialogType: 2,
          pageId: this.$store.getters.imcreate,
          pageNum: this.chatlist_leaveMessage_pageNum,
          pageSize: this.chatlist_leaveMessage_pageSize
        })
          .then(resp => {
            if (resp.code === 1) {
              //查询第一页时,清空列表
              if (this.chatlist_leaveMessage_pageNum === 1) {
                this.chatlist_leaveMessage = [];
              }

              let scrollTop = 0;
              try {
                scrollTop = document.querySelector('.chat-tabs-content')
                  .scrollTop; //滚动条位置
              } catch (err) { }

              let dialogInfo = null; //会话信息对象
              resp.data.datas.datas.forEach(item => {
                dialogInfo = Object.assign({}, item, {
                  assignCustomHeadImg: item.customHeadImg, //客户图像
                  assignCustomName: item.customName, //客户名称
                  assignLastMessageTime: item.lastMsgTime, //最后一条消息时间
                  assignLastMessage: item.lastMsg, //最后一条消息内容
                  assignUnreadMessageNum: Number(item.unRead) || 0, //未读数量
                  assignChatStatus: 'custom0', //item.dialogEndScene, //当前会话状态
                  assignChatItemSelected: false, //是否选中
                  assignHasDialogInfo: true //有会话对象
                });
                this.chatlist_leaveMessage.push(dialogInfo);
              });

              //列表加载数据时，重新选中列表加载前的选中项
              this.reSelectChatItem({ tab: '3' });

              this.refUpdateChatList({ tab: '3' });

              //加载更多按钮
              this.isShowLoadMoreBtn_chatlist_leaveMessage = Boolean(
                this.chatlist_leaveMessage.length >=
                this.chatlist_leaveMessage_pageNum *
                this.chatlist_leaveMessage_pageSize
              );
              this.isShowLoadMoreLoading_chatlist_leaveMessage = false;

              //留言总条数赋值
              this.chatlist_leaveMessage_total =
                Number(resp.data.datas.total) || 0;

              //滚动条重新定位
              this.$nextTick(_ => {
                try {
                  document.querySelector(
                    '.chat-tabs-content'
                  ).scrollTop = scrollTop;
                } catch (err) { }
              });

              this.$forceUpdate();

              resolve({ code: 1 });
            } else {
              this.$XyyMessage.error(resp.msg);
              reject({ code: 0, msg: resp.msg });
            }
          })
          .catch(err => {
            this.$XyyMessage.error(err.message);
            reject({ code: 0, msg: err.message });
          });
      });
    },

    // 留言未回复进行倒计时
    setLeaveMessageCountdown() {
      clearTimeout(this.timerLeaveMessageCountdown)
      this.timerLeaveMessageCountdown = null
      if(!this.chatlist_ing.length) {
        return
      }
      const now = new Date().getTime()
      console.time('LeaveMessageCountdownLoop')
      this.chatlist_ing.forEach(chat => {
        // dialogEndScene == 0 会话中
        // dialogStartScene == 2 留言发起
        // customerUnReplyFromLeaveMessage 0访客未回复，1访客已回复
        // kefuFirstMessageTime 客服第一次消息时间戳
        if (
          chat.dialogEndScene === 0 && 
          chat.dialogStartScene == 2 && 
          chat.customerUnReplyFromLeaveMessage === 0
        ) {
          chat.assignCustomerUnReplyFromLeaveMessageTips = true

          if (chat.kefuFirstMessageTime) {
            // 显示倒计时逻辑
            let diffInMilliseconds  = now - chat.kefuFirstMessageTime
            if (diffInMilliseconds >= 0) {
              if(diffInMilliseconds <= 15*60*1000) { // 15min
                // 将毫秒转换为秒
                diffInMilliseconds = 15*60*1000 - diffInMilliseconds
                let diffInSeconds = Math.floor(diffInMilliseconds / 1000); 
                // 计算分钟和剩余的秒
                let minutes = Math.floor(diffInSeconds / 60);
                let seconds = diffInSeconds % 60;

                chat.assignCountdown = `${minutes.toString().padStart(2,'0')}:${seconds.toString().padStart(2,'0')}`
              } else {
                chat.assignCountdown = '' // 00:00
              }
            } else {
              chat.assignCountdown = '' // 00:00
            }
          }

        } else {
          chat.assignCustomerUnReplyFromLeaveMessageTips = false
          chat.assignCountdown = ''
        }
      })
      this.refUpdateChatList({ tab: '0' });
      console.timeEnd('LeaveMessageCountdownLoop')
      this.timerLeaveMessageCountdown = setTimeout(() => {
        this.setLeaveMessageCountdown()
      }, 1000);
    },

    /**
     * 轮询逻辑，获取新消息
     */
    reqReadMessagePolling() {
      if (this.isRunPolling) {
        clearTimeout(this.pollingTimer);
        this.pollingTimeout = 0;
        Promise.all([this.reqReadMessagePromise()])
          .then(resp => {
            this.isShowNetworkErrorTip = false;
            this.pollingTimeout = 300;
          })
          .catch(err => {
            this.saveLocalStorage({
              key: 'read-message-catch',
              value: err,
              kefuInfo: this.kfInfoDetail
            });
            this.pollingTimeout = 2000;
          })
          .finally(() => {
            this.$nextTick(() => {
              this.pollingTimer = setTimeout(() => {
                this.reqReadMessagePolling();
              }, this.pollingTimeout);
            });
          });
      }
    },

    /**
     * 获取会话中列表，选中的未关闭的会话的会话id，
     */
    getChatlistIngSelectedDialogId() {
      let retId = '';
      let list_ing = this.chatlist_ing.filter(item => {
        return item.assignChatItemSelected === true;
      });
      if (list_ing.length) {
        retId = list_ing[0].id;
      } else {
        let list_closed = this.chatlist_closed.filter(item => {
          return item.assignChatItemSelected === true;
        });
        if (list_closed.length) {
          retId = list_closed[0].id;
        }
      }
      return retId;
    },

    /**
     * 轮询新消息
     */
    reqReadMessagePromise() {
      return new Promise((resolve, reject) => {
        readMessageList({
          pageId: this.$store.getters.imcreate,
          currentDialogId: this.getChatlistIngSelectedDialogId(),
          t: new Date().getTime().toString(),
          readVersionId: this.readVersionId || ''
          //sign: this.sign,
        })
          .then(resp => {
            if (resp.code === 1) {
              let respData = resp.data;

              /**
               * 其他字段处理
               */
              try {
                //本次轮询到的版本号Id赋值
                this.readVersionId = respData.readVersionId;

                //服务上限人数
                this.kfMaxDialogNumber = (
                  respData.kefuMaxDialog || 0
                ).toString();

                //排队人数
                this.kfQueuingNumber = (respData.queueNum || 0).toString();

                //留言总数
                if (
                  this.activeTab === '3' &&
                  this.chatlist_leaveMessage_total !==
                  (Number(respData.leaveMessageTotal) || 0)
                ) {
                  this.chatlist_leaveMessage_pageNum = 1;
                  this.reqChatlistLeaveMessagePromise().catch(() => { });
                } else {
                  this.chatlist_leaveMessage_total =
                    Number(respData.leaveMessageTotal) || 0; //留言总条数赋值
                }
              } catch (err) {
                //此处不使用reject
                this.saveLocalStorage({
                  key: 'read-message-catch',
                  value: { code: 0, id: '003', message: err.message },
                  kefuInfo: this.kfInfoDetail
                });
              }

              /**
               * 客服在线状态变更处理
               */
              try {
                if (respData.kefuStatus && respData.kefuStatus.status) {
                  if (
                    this.kfOnlineStatus !==
                    respData.kefuStatus.status.toString()
                  ) {
                    this.kfInfoDetail.status = this.kfOnlineStatus = respData.kefuStatus.status.toString();
                    this.kfInfoDetail.statustime = new Date().getTime();
                    this.computeKefuOnlineTiming(this.kfInfoDetail.statustime);
                    if (respData.kefuStatus.changeReason === 2) {
                      this.$XyyMessage.warning('您的状态已被管理员强制变更');
                    }
                  }
                }
              } catch (err) { }

              /**
               * 消息处理
               */
              if (respData.messages && respData.messages.length) {
                //存log日志
                try {
                  sendlog({
                    kefuInfo: this.kfInfoDetail,
                    response: resp
                  });
                } catch (err) { }

                //处理消息
                this.newMessageHandler(respData.messages).finally(() => {
                  resolve({ code: 1 });
                });
              } else {
                resolve({ code: 1 });
              }
            } else if (resp.code === 10001) {
              //停止轮询
              this.isRunPolling = false;

              //单点登录提示
              this.compIMViewMain.closeCompIMViewMain(
                '此账号已从别处登录，已停止消息读取！'
              );

              resolve({ code: 1 });
            } else {
              //网络错误提示
              if (!this.isShowNetworkErrorTip) {
                this.isShowNetworkErrorTip = true;
              }
              reject({ code: 0, id: '001', response: resp });
            }
          })
          .catch(err => {
            reject({ code: 0, id: '002', message: err.message });
          });
      });
    },

    /**
     * 处理轮询回来的新消息
     */
    newMessageHandler(messageList) {
      return new Promise((resolve, reject) => {
        //本地不记录storage,上传到雪地
        // console.log(process.env.NODE_ENV);
        // if (!['production'].includes(process.env.NODE_ENV)) {
        //   //消息存storage
        //   this.saveLocalStorage({
        //     key: 'read-message-save',
        //     value: { type: 'default', message: messageList },
        //   });
        // }
        let msg = null;
        for (let i = 0, il = messageList.length; i < il; i++) {
          try {
            msg = messageList[i];

            //无msgId，忽略处理，记录storage
            if (!msg.msgId) {
              // this.saveLocalStorage({
              //   key: 'read-message-save',
              //   value: { type: 'nullId', message: msg },
              // });
              continue;
            }

            //最后一条消息Id赋值
            this.lastMessageId = msg.msgId;

            //处理消息
            switch (msg.type) {
              case 408: {
                //408 对话转移请求
                //展示confirm弹窗
                this.transferInfo = msg;
                this.isShowTransferDialog = true;
                break;
              }
              case 410: {
                //410 接受方接受转移请求
                //移除本地会话中列表数据
                for (let j = 0, jl = this.chatlist_ing.length; j < jl; j++) {
                  if (
                    this.chatlist_ing[j].id.toString() ===
                    msg.dialogid.toString()
                  ) {
                    if (this.chatlist_ing[j].assignChatItemSelected) {
                      this.$parent.resetChat({ id: '004' });
                    }

                    //this.chatlist_ing.splice(j, 1);
                    let chatlist_ing_copy = Array.from(this.chatlist_ing);
                    chatlist_ing_copy.splice(j, 1);
                    this.chatlist_ing = chatlist_ing_copy;

                    this.refUpdateChatList({ tab: '0' });
                    this.$XyyMessage.success('对方已接受转接');
                    break;
                  }
                }
                break;
              }
              case 411: {
                //411 接受方拒绝转移请求
                this.$XyyMessage.warning('对方拒绝了你的转接');
                break;
              }
              case 413: {
                //413 会话被抢接
                //移除本地会话中列表数据
                for (let k = 0, kl = this.chatlist_ing.length; k < kl; k++) {
                  if (
                    this.chatlist_ing[k].id.toString() ===
                    msg.dialogid.toString()
                  ) {
                    if (this.chatlist_ing[k].assignChatItemSelected) {
                      this.$parent.resetChat({ id: '005' });
                    }

                    //this.chatlist_ing.splice(k, 1);
                    let chatlist_ing_copy = Array.from(this.chatlist_ing);
                    chatlist_ing_copy.splice(k, 1);
                    this.chatlist_ing = chatlist_ing_copy;

                    this.refUpdateChatList({ tab: '0' });
                    this.$XyyMessage.success('会话已被抢接');
                    break;
                  }
                }
                break;
              }
              case 417: {
                //访客留言分配给同一个坐席
                //客服留言后,关闭了页面,再次尝试进线,会产生新会话,没有客服接待则进留言,此时会返回417
                //需要用新的会话id替换老的会话id
                if (this.activeTab === '3') {
                  for (
                    let l = 0, ll = this.chatlist_leaveMessage.length;
                    l < ll;
                    l++
                  ) {
                    if (
                      this.chatlist_leaveMessage[l].uid === msg.uid &&
                      this.chatlist_leaveMessage[l].appId === msg.appId
                    ) {
                      //覆盖原字段值
                      let dialogInfo = Object.assign(
                        {},
                        this.chatlist_leaveMessage[l],
                        {
                          id: msg.dialogid //替换成新的会话id
                          //assignCustomHeadImg: msg.customHeader, //客户图像
                          //assignCustomName: msg.customerName, //客户名称
                          //assignLastMessageTime: msg.createTime, //最后一条消息时间
                          //assignLastMessage: msg.content, //最后一条消息内容
                          //assignUnreadMessageNum:
                          //(Number(
                          //this.chatlist_leaveMessage[l].assignUnreadMessageNum
                          //) || 0) + 1, //未读数量
                          //assignChatStatus: msg.dialogEndScene, //当前会话状态
                        }
                      );
                      this.$set(this.chatlist_leaveMessage, l, dialogInfo);

                      this.refUpdateChatList({ tab: '3' });

                      if (
                        this.chatlist_leaveMessage[l].assignChatItemSelected
                      ) {
                        //通知主组件，重载中间和右侧组件
                        this.$emit(
                          'select',
                          {
                            type: '3',
                            userId: msg.uid,
                            chat: this.chatlist_leaveMessage[l],
                            kefuInfo: this.kfInfoDetail
                          },
                          { id: '006' }
                        );
                      }
                      break;
                    }
                  }
                }
                break;
              }
              case 403: {
                //在线会话消息
                if (msg.dialogType.toString() === '1') {
                  //新进线会话
                  //判断会话中列表中是否有此会话
                  let isCurrentDialogInList = this.chatlist_ing.filter(item => {
                    return item.id.toString() === msg.dialogid.toString();
                  });

                  if (isCurrentDialogInList.length) {
                    //有此会话,异常提示
                    // this.$XyyMessage.error(
                    //   'type:403，会话中列表已存在当前新进线的会话，数据错误'
                    // );
                  } else {
                    //无此会话,创建新会话
                    let dialogInfoNew = Object.assign(
                      {},
                      {
                        id: msg.dialogid,
                        starttime: msg.createTime,
                        dialogEndScene: msg.dialogEndScene,
                        customLastMessageTime: null,
                        uid: msg.uid,
                        kefuid: msg.kefuid,
                        dialogStartScene: msg.dialogStartScene,
                        customerUnReplyFromLeaveMessage: msg.dialogStartScene == 2 ? 0 : null, // 留言发起会话新进线，访客一定是未回复状态
                        assignCustomerUnReplyFromLeaveMessageTips: msg.dialogStartScene == 2 ? true : false,  // 留言发起会话新进线，展示提示文案
                      },
                      {
                        assignCustomHeadImg: msg.customHeader, //客户图像
                        assignCustomName: msg.customerName, //客户名称
                        assignLastMessageTime: msg.createTime, //最后一条消息时间
                        assignLastMessage: msg.kefuNeedPreviewState
                          ? msg.content
                          : '', //最后一条消息内容
                        assignUnreadMessageNum: Number(msg.unReadMsgNum) || 0, //未读数量
                        assignChatStatus: msg.dialogEndScene, //当前会话状态
                        assignChatItemSelected: false, //是否选中
                        assignHasDialogInfo: false //有会话对象
                      }
                    );

                    //添加到对应列表中
                    if (msg.dialogEndScene === 0) {
                      // 会话列表以商家维度聚合,此处新增会话后需移除掉该商家的其他会话,uid
                      const uidIndexInChatListIng = this.chatlist_ing.findIndex(
                        chat => chat.uid === msg.uid
                      );
                      if (uidIndexInChatListIng > -1) {
                        this.chatlist_ing.splice(uidIndexInChatListIng, 1);
                      }
                      const uidIndexInChatListClosed = this.chatlist_closed.findIndex(
                        chat => chat.uid === msg.uid
                      );
                      if (uidIndexInChatListClosed > -1) {
                        this.chatlist_closed.splice(
                          uidIndexInChatListClosed,
                          1
                        );
                      }

                      this.chatlist_ing.unshift(dialogInfoNew);

                      this.refUpdateChatList({ tab: '0' });

                      //浏览器系统通知
                      this.$parent.sendSystemNotice();
                    } else {
                      //会话消息中，结束场景值有误，异常提示
                      this.$XyyMessage.error(
                        'type:403,dialogEndScene:!0，数据错误'
                      );
                    }
                  }
                } else {
                  this.$XyyMessage.error('type:403,dialogType:!1，数据错误');
                }
                break;
              }
              default: {
                if (msg.dialogType.toString() === '1') {
                  //在线会话消息

                  let newMsgToWhichList = 0; //消息信息更新到哪一个list中，0:会话中,1:会话中已经结束

                  //判断会话状态是否关闭
                  let currentClosedDialogInfo = null; //当前轮询到的关闭的会话对象
                  for (let m = 0, ml = this.chatlist_ing.length; m < ml; m++) {
                    if (
                      this.chatlist_ing[m].id.toString() ===
                      msg.dialogid.toString()
                    ) {
                      //此会话原来是会话中,轮询变成了关闭状态
                      if (msg.dialogEndScene !== 0) {
                        this.chatlist_ing[m].dialogEndScene = msg.dialogEndScene;
                        this.chatlist_ing[m].assignChatStatus = msg.dialogEndScene;

                        this.chatlist_ing[m].assignCustomerUnReplyFromLeaveMessageTips = false
                        this.chatlist_ing[m].assignCountdown = '';

                        //记录当前关闭会话对象
                        currentClosedDialogInfo = this.chatlist_ing[m];

                        //操作会话数组
                        //添加到已结束列表
                        this.chatlist_closed.unshift(this.chatlist_ing[m]);
                        //移除会话中
                        //this.chatlist_ing.splice(m, 1);
                        let chatlist_ing_copy = Array.from(this.chatlist_ing);
                        chatlist_ing_copy.splice(m, 1);
                        this.chatlist_ing = chatlist_ing_copy;

                        newMsgToWhichList = 1;

                        this.refUpdateChatList({ tab: '0' });
                      }

                      break;
                    }
                  }
                  if (currentClosedDialogInfo) {
                    // 留言发起的会话，15min倒计时结束，关闭会话，需从列表中移除
                    // dialogStartScene == 2 留言发起
                    // customerUnReplyFromLeaveMessage 0访客未回复，1访客已回复
                    if(
                      currentClosedDialogInfo.dialogStartScene == 2 && 
                      currentClosedDialogInfo.customerUnReplyFromLeaveMessage === 0
                    ) {
                      this.emitChatItemRemove({
                        tab: '0',
                        chat: { id: currentClosedDialogInfo.id }
                      });
                    } else {
                      //会话是选中状态
                      if (currentClosedDialogInfo.assignChatItemSelected) {
                        this.emitChatItemClick({
                          tab: '0',
                          chat: currentClosedDialogInfo
                        });
                      }
                    }
                  }

                  //处理消息
                  let targetList = this[
                    ['chatlist_ing', 'chatlist_closed'][newMsgToWhichList]
                  ];
                  for (let n = 0, nl = targetList.length; n < nl; n++) {
                    if (
                      targetList[n].id.toString() === msg.dialogid.toString()
                    ) {
                      //更新会话列表中最后一条消息，最后消息时间
                      //kefuNeedPreviewState客服端是否需要预览 true-需要 false-不需要
                      if (msg.kefuNeedPreviewState) {
                        targetList[n].assignLastMessage = msg.content; //最后一条消息内容
                        targetList[n].assignLastMessageTime = msg.createTime; //最后一条消息时间
                        //会话中列表,三个时间排序字段的赋值
                        if (newMsgToWhichList === 0) {
                          //1客户；2-客服，3系统消息
                          if (msg.msgFrom.toString() === '1') {
                            targetList[n].customLastMessageTime = msg.createTime;

                            // 更新customerUnReplyFromLeaveMessage字段
                            if(
                              targetList[n].dialogEndScene === 0 && 
                              targetList[n].dialogStartScene == 2 && 
                              targetList[n].customerUnReplyFromLeaveMessage === 0
                            ) {
                              targetList[n].customerUnReplyFromLeaveMessage = 1
                              targetList[n].assignCustomerUnReplyFromLeaveMessageTips = false
                              targetList[n].assignCountdown = ''
                            }
                          } else if(msg.msgFrom.toString() === '2') {
                            // 更新kefuFirstMessageTime字段
                            // dialogEndScene == 0 会话中
                            // dialogStartScene == 2 留言发起
                            // customerUnReplyFromLeaveMessage 0访客未回复，1访客已回复
                            // kefuFirstMessageTime 客服第一次消息时间戳
                            if(
                              targetList[n].dialogEndScene === 0 && 
                              targetList[n].dialogStartScene == 2 && 
                              targetList[n].customerUnReplyFromLeaveMessage === 0 && 
                              !targetList[n].kefuFirstMessageTime
                            ) {
                              this.updateKefuFirstMessageTime(msg.dialogid, (updateTimestamp) => {
                                targetList[n].kefuFirstMessageTime = updateTimestamp
                              })
                            }
                          }
                        }
                      }

                      //判断轮询回来的会话是否是当前选中的会话
                      //是,通知中间组件更新消息;否,未读数量加1
                      if (targetList[n].assignChatItemSelected) {
                        this.$emit('message', [msg], { id: '008' });
                      } else {
                        //kefuNeedReadState,是否需要客服读取 true-需要 false-不需要
                        if (msg.kefuNeedReadState) {
                          targetList[n].assignUnreadMessageNum += 1;
                        }
                      }

                      this.refUpdateChatList({ tab: '0' });

                      //浏览器系统通知
                      if (msg.kefuNeedReadState) {
                        this.$parent.sendSystemNotice();
                      }

                      break;
                    }
                  }

                  //按照所选规则排序
                  this.$nextTick(() => {
                    console.log('排序前');
                    console.log(this.chatlist_ing);
                    this.commandChattingSort(
                      this.chatlist_ing_sortType,
                      this,
                      true
                    );
                    console.log('排序后');
                    console.log(this.chatlist_ing);
                  });
                } else if (msg.dialogType.toString() === '2') {
                  //留言追加消息
                  for (
                    let o = 0, ol = this.chatlist_leaveMessage.length;
                    o < ol;
                    o++
                  ) {
                    if (
                      this.chatlist_leaveMessage[o].id.toString() ===
                      msg.dialogid.toString()
                    ) {
                      //变量赋值
                      this.chatlist_leaveMessage[o].assignLastMessageTime =
                        msg.createTime;
                      this.chatlist_leaveMessage[o].assignLastMessage =
                        msg.content;
                      this.chatlist_leaveMessage[
                        o
                      ].assignUnreadMessageNum = this.chatlist_leaveMessage[o]
                        .assignChatItemSelected
                          ? 0
                          : 1; //未读数量,只需要显示红点
                      this.chatlist_leaveMessage[o].assignChatStatus =
                        'custom0'; //当前会话状态

                      this.refUpdateChatList({ tab: '3' });

                      //选中状态,发消息
                      if (
                        this.chatlist_leaveMessage[o].assignChatItemSelected
                      ) {
                        this.$emit('message', [msg], { id: '009' });
                      }
                      break;
                    }
                  }
                }
                break;
              }
            }
            this.$forceUpdate();
          } catch (error) {
            this.saveLocalStorage({
              key: 'handler-message-catch',
              value: {
                error: error.stack,
                errorMessageId: msg.msgId,
                messageList: messageList
              },
              kefuInfo: this.kfInfoDetail
            });
          }
        }
        resolve();
      });
    },

    // 更新kefuFirstMessageTime字段
    async updateKefuFirstMessageTime(dialogid, callback) {
      try {
        const { code, data, msg }= await getKefuFirstMsgTimeExcludeAutoMsg({
          dialogId: dialogid
        })
        if(code == 1) {
          if(data) {
            callback(data)
          }
        } else {
          throw new Error(msg)
        }
      } catch(e) {
        console.log(e.message)
      }
    },

    /**
     * 转接拒接
     */
    transferReject() {
      this.transferRejectBtnDisabled = true;
      transferReject(this.transferInfo.dialogid)
        .then(resp => {
          if (resp.code === 1) {
            this.$XyyMessage.success('操作成功');
          } else {
            this.$XyyMessage.error(resp.msg);
          }
        })
        .catch(() => {
          this.$XyyMessage.error('操作失败!');
        })
        .finally(() => {
          this.isShowTransferDialog = false;
          setTimeout(() => {
            this.transferRejectBtnDisabled = false;
          }, 0);
        });
    },

    /**
     * 转接同意
     */
    transferResolve() {
      this.transferResolveBtnDisabled = true;
      transferAccept(this.transferInfo.dialogid)
        .then(resp => {
          if (resp.code === 1) {
            if (resp.data.type === 404) {
              this.$XyyMessage.error('转接会话错误');
            } else {
              this.$XyyMessage.success('操作成功');
            }
          } else {
            this.$XyyMessage.error(resp.msg);
          }
        })
        .catch(() => {
          this.$XyyMessage.error('操作失败!');
        })
        .finally(() => {
          this.isShowTransferDialog = false;
          setTimeout(() => {
            this.transferResolveBtnDisabled = false;
          }, 0);
        });
    },

    /**
     * 列表重新加载数据时，重新选中列表加载前的选中项
     */
    reSelectChatItem({ tab }) {
      if (tab === this.selectedChatTab && this.selectedChat) {
        let chatlistType = '';

        //会话中列表，区分会话中和已结束状态
        if (tab === '0') {
          let targetChat = this.chatlist_ing
            .concat(this.chatlist_closed)
            .filter(item => {
              return item.id.toString() === this.selectedChat.id.toString();
            });
          if (targetChat.length) {
            chatlistType =
              targetChat[0].dialogEndScene === 0
                ? 'chatlist_ing'
                : 'chatlist_closed';
          }
        } else {
          chatlistType = [
            '',
            'chatlist_followed',
            'chatlist_historical',
            'chatlist_leaveMessage'
          ][Number(tab)];
        }

        if (chatlistType) {
          this[chatlistType].forEach((item, index) => {
            if (item.id.toString() === this.selectedChat.id.toString()) {
              item.assignUnreadMessageNum = 0;
              item.assignChatItemSelected = true;
            }
          });
        }
      }
    },

    /**
     * 四个列表点击了某个会话的emit
     */
    emitChatItemClick({ tab, chat }) {
      //赋值全局选中会话及tab对象
      this.selectedChat = chat;
      this.selectedChatTab = tab;

      //四个列表中清除选中标识
      this.chatlist_ing.forEach(item => {
        item.assignChatItemSelected = false;
      });
      this.chatlist_closed.forEach(item => {
        item.assignChatItemSelected = false;
      });
      this.chatlist_followed.forEach(item => {
        item.assignChatItemSelected = false;
      });
      this.chatlist_historical.forEach(item => {
        item.assignChatItemSelected = false;
      });
      this.chatlist_leaveMessage.forEach(item => {
        item.assignChatItemSelected = false;
      });

      let chatInWhichList = null; //0:会话中,1:会话中已经结束
      switch (tab) {
        case '0':
          //会话中列表，当前会话未读数量清0
          this.chatlist_ing.forEach((item, index) => {
            if (chat.id.toString() === item.id.toString()) {
              item.assignUnreadMessageNum = 0;
              item.assignChatItemSelected = true;
              chatInWhichList = 0;
            }
          });
          this.chatlist_closed.forEach((item, index) => {
            if (chat.id.toString() === item.id.toString()) {
              item.assignUnreadMessageNum = 0;
              item.assignChatItemSelected = true;
              chatInWhichList = 1;
            }
          });
          break;
        case '1':
          //待跟进
          this.chatlist_followed.forEach(item => {
            if (chat.id.toString() === item.id.toString()) {
              item.assignChatItemSelected = true;
            }
          });
          break;
        case '2':
          //历史会话
          this.chatlist_historical.forEach(item => {
            if (chat.id.toString() === item.id.toString()) {
              item.assignChatItemSelected = true;
            }
          });
          break;
        case '3':
          //留言，数量清0，总数减掉
          this.chatlist_leaveMessage.forEach(item => {
            if (chat.id.toString() === item.id.toString()) {
              item.assignUnreadMessageNum = 0;
              item.assignChatItemSelected = true;
            }
          });
          break;
      }

      this.refUpdateChatListAll();

      //会话中列表，请求获取会话对象信息
      //无会话对象信息,去请求
      if (tab === '0' && !chat.assignHasDialogInfo) {
        getDialogInfo({ dialogId: chat.id })
          .then(resp => {
            if (resp.code === 1) {
              //组装新会话对象
              let newChat = null;
              //替换会话对象
              let targetList = this[
                ['chatlist_ing', 'chatlist_closed'][chatInWhichList]
              ];
              for (let i = 0, l = targetList.length; i < l; i++) {
                if (targetList[i].id.toString() === chat.id.toString()) {
                  newChat = Object.assign({}, resp.data, targetList[i], {
                    assignHasDialogInfo: true
                  });
                  //targetList[i] = newChat;
                  this.$set(targetList, i, newChat);
                  this.refUpdateChatList({ tab: '0' });
                  break;
                }
              }
              //通知主组件，重载中间和右侧组件
              this.$emit(
                'select',
                {
                  type: tab,
                  userId: newChat.uid,
                  chat: newChat,
                  kefuInfo: this.kfInfoDetail
                },
                { id: '011', type: tab, chat: newChat }
              );
            } else {
              this.$XyyMessage.error(resp.msg);
            }
          })
          .catch(() => {
            this.$XyyMessage.error('会话信息加载失败！');
          });
      } else {
        //通知主组件，重载中间和右侧组件
        this.$emit(
          'select',
          {
            type: tab,
            userId: chat.uid,
            chat,
            kefuInfo: this.kfInfoDetail
          },
          {
            id: '010',
            type: tab,
            chat: chat
          }
        );
      }
    },

    /**
     * 四个列表点击了移除会话的emit
     * by user:手动移除,system:系统移除
     */
    emitChatItemRemove({ tab, chat, by }) {
      let chatlistType = [
        'chatlist_closed',
        'chatlist_followed',
        'chatlist_historical',
        'chatlist_leaveMessage'
      ][Number(tab)];

      //关键字搜索结果,发起会话,不需移除列表
      if (chatlistType) {
        let index = -1;
        for (let i = 0, l = this[chatlistType].length; i < l; i++) {
          if (this[chatlistType][i].id.toString() === chat.id.toString()) {
            index = i;
            break;
          }
        }
        if (index > -1) {
          //如果当前移除的会话是选中状态
          if (this[chatlistType][index].assignChatItemSelected) {
            this.$parent.resetChat({ id: '012' });
            this.selectedChatTab = '';
            this.selectedChat = '';
          }

          //列表移除当前会话
          //this[chatlistType].splice(index, 1);
          let chatlistType_copy = Array.from(this[chatlistType]);
          chatlistType_copy.splice(index, 1);
          this[chatlistType] = chatlistType_copy;

          this.refUpdateChatListAll();

          //如果是待跟进列表，待跟进条数-1
          if (tab === '1') {
            this.chatlist_followed_total -= 1;
          }

          //如果是留言列表，留言条数-1
          if (tab === '3') {
            this.chatlist_leaveMessage_total -= 1;
          }

          this.$forceUpdate();

          //如果列表没有数据，则重新查询第一页数据
          if (!this[chatlistType].length) {
            this.onClickTabChangeTo(tab, true);
          }

          if (by === 'user') {
            this.$XyyMessage.success('会话移除成功');
          }
        }
      }
    },

    /**
     * 待跟进，留言列表中，发起会话，系统自动移除
     */
    reChatRemoveAuto(params) {
      this.emitChatItemRemove(params);
    },

    /**
     * 客服主动结束会话(移至待跟进或历史)，会话中列表需要移除本条记录
     */
    closeChatRemoveAuto({ tab, chat, removeTo }) {
      if (tab !== '0') {
        return false;
      }
      let index = -1;
      for (let i = 0, l = this.chatlist_ing.length; i < l; i++) {
        if (this.chatlist_ing[i].id.toString() === chat.id.toString()) {
          index = i;
          break;
        }
      }
      if (index > -1) {
        if (this.chatlist_ing[index].assignChatItemSelected) {
          this.$parent.resetChat({ id: '013' });
          this.selectedChatTab = '';
          this.selectedChat = '';
        }

        //this.chatlist_ing.splice(index, 1);
        let chatlist_ing_copy = Array.from(this.chatlist_ing);
        chatlist_ing_copy.splice(index, 1);
        this.chatlist_ing = chatlist_ing_copy;

        this.refUpdateChatList({ tab: '0' });
      }

      //结束会话移至待跟进，待跟进总数+1
      if (removeTo === 'followed') {
        this.chatlist_followed_total += 1;
        this.$forceUpdate();
      }
    },

    /**
     * 绑定游客后刷新会话信息
     */
    refreshChatInfo({ tab, chat }) {
      let chatlistType = '';

      //会话中列表，区分会话中和已结束状态
      if (tab === '0') {
        let targetChat = this.chatlist_ing
          .concat(this.chatlist_closed)
          .filter(item => {
            return item.id.toString() === chat.id.toString();
          });
        if (targetChat.length) {
          chatlistType =
            targetChat[0].dialogEndScene === 0
              ? 'chatlist_ing'
              : 'chatlist_closed';
        }
      } else {
        chatlistType = [
          '',
          'chatlist_followed',
          'chatlist_historical',
          'chatlist_leaveMessage'
        ][Number(tab)];
      }

      if (chatlistType) {
        this[chatlistType].forEach((item, index) => {
          if (item.id.toString() === chat.id.toString()) {
            let itemNew = Object.assign({}, item, chat, {
              assignCustomHeadImg: chat.customHeadImg, //客户图像
              assignCustomName: chat.customName //客户名称
            });
            this.$set(this[chatlistType], index, itemNew);
            this.refUpdateChatListAll();
            if (item.assignChatItemSelected) {
              this.emitChatItemClick({ tab, chat: itemNew });
            }
            this.$forceUpdate();
          }
        });
      }
    },

    /**
     * 错误信息存localStorage
     */
    saveLocalStorage(params) {
      this.$parent.saveLocalStorage(params);
    },

    /**
     * ref调方法更新子组件
     */
    refUpdateChatList({ tab }) {
      let refName = '';
      let dataSouce = [];
      switch (tab) {
        case '0':
          refName = 'ref_tab_0';
          dataSouce = this.chatlist_ing.concat(this.chatlist_closed);
          break;
        case '1':
          refName = 'ref_tab_1';
          dataSouce = this.chatlist_followed;
          break;
        case '2':
          refName = 'ref_tab_2';
          dataSouce = this.chatlist_historical;
          break;
        case '3':
          refName = 'ref_tab_3';
          dataSouce = this.chatlist_leaveMessage;
          break;
      }
      if (this.$refs[refName]) {
        this.$refs[refName].refUpdateChatList(dataSouce);
      }
    },

    /**
     * ref调方法更新所有会话列表子组件
     */
    refUpdateChatListAll() {
      this.refUpdateChatList({ tab: '0' });
      this.refUpdateChatList({ tab: '1' });
      this.refUpdateChatList({ tab: '2' });
      this.refUpdateChatList({ tab: '3' });
    },

    /**
     * 撤回消息
     */
    recallMessage(params) {
      let targetList = this.chatlist_ing; //会话中列表
      if (params && params.containerId) {
        for (let i = 0, l = targetList.length; i < l; i++) {
          if (targetList[i].id.toString() === params.containerId.toString()) {
            targetList[i].assignLastMessage = '你撤回了一条消息';
            targetList[i].assignLastMessageTime = new Date().getTime();
            this.refUpdateChatList({ tab: '0' });
            break;
          }
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.im-view-left-container {
  width: 268px;
  height: 100%;
  margin-right: 16px;
  background-color: #fff;
  flex-grow: 0;
  flex-shrink: 0;

  //客服信息
  .head-layout-216 {
    width: 100%;
    padding: 24px 16px 12px;
    overflow: hidden;
    position: relative;

    .network-error-tips {
      width: 100%;
      padding: 5px;
      z-index: 1;
      border-radius: 0;
      position: absolute;
      top: 0;
      left: 0;
    }

    .kf-info-wrap {
      width: 100%;
      word-break: break-all;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .kf-hearder-pic {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .kf-detail-box {
        min-height: 48px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;

        .kf-info-name {
          width: 100%;
          font-size: 14px;
          color: #292933;
          font-weight: 500;
        }

        .kf-info-status {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .kf-online-status {
            cursor: pointer;
            height: 24px;
            padding: 0 8px;
            border-radius: 12px;
            background-color: #f5f7fa;
            font-size: 12px;
            color: #292933;
            display: flex;
            justify-content: center;
            align-items: center;

            .status-icon {
              width: 12px;
              height: 12px;
              border-radius: 50%;
            }

            .status-label {
              font-size: 12px;
              color: #292933;
              margin: 0 4px;
            }

            .status-arrow {
              width: 12px;
              height: 12px;

              &.arrow-up {
                transform: rotate(180deg);
              }
            }
          }

          .kf-status-timer {
            font-size: 14px;
            color: #aeaebf;
            font-weight: 400;
          }
        }
      }
    }

    .kf-status-serve {
      width: 100%;
      margin-top: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: #575766;
    }
  }

  //客服信息旧版
  .head-layout {
    width: 100%;
    padding: 15px 0;
    position: relative;

    .kf-status-wrap {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      .kf-header-box {
        position: relative;
        font-size: 0;

        .kf-hearder-pic {
          width: 50px;
          height: 50px;
          border-radius: 50%;
        }

        .kf-header-status {
          width: 10px;
          height: 10px;
          border-radius: 50%;
          position: absolute;
          bottom: 0;
          right: 0;
        }
      }
    }

    .kf-status-timer {
      width: 100%;
      margin-top: 15px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      color: #9b9bab;
    }

    .kf-status-serve {
      width: 100%;
      margin-top: 15px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      font-size: 12px;
      color: #9b9bab;
    }
  }

  //搜索
  .search-popup {
    width: 100%;
    padding: 0 16px;
    position: relative;

    /deep/ {
      .el-input__inner {
        font-size: 12px;
        padding-left: 8px;
      }

      .el-icon-search {
        float: right;
      }
    }

    .search-popup-list {
      width: calc(100% - 32px);
      margin: 0 auto;
      max-height: 200px;
      overflow: auto;
      background-color: #fff;
      box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.13);
      border: 1px solid rgba(240, 242, 245, 1);
      z-index: 99;
      position: absolute;
      top: 32px;

      .search-res-item {
        width: 100%;
        padding: 10px;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        &:hover {
          background-color: rgba(218, 233, 236, 1);
          cursor: pointer;
        }

        .res-item-left {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          margin-right: 10px;
        }

        .res-item-right {
          flex-grow: 1;
          width: calc(100% - 32px - 10px);
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: flex-start;

          span {
            width: 100%;
            font-size: 12px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }

  //对话四个列表
  .chat-list {
    width: 100%;
    height: calc(100% - 113px - 32px - 8px);
    margin-top: 8px;

    //四个切换tab
    .chat-tabs-header {
      // width: calc(100% - 10px);
      width: 100%;
      height: 35px;
      margin: 0 auto;
      font-size: 0;
      // background-color: #f5f7fa;
      border-bottom: 1px solid #e4e4eb;
      display: flex;
      justify-content: space-around;
      align-items: center;

      .tabs-header-item {
        width: 25%;
        height: 100%;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;

        &.is-active {
          .chat-el-icon {
            border-bottom: 2px solid #3b95a8;

            i {
              color: #3b95a8;
            }
          }

          .header-item-label {
            color: #3c95a8;
            font-weight: 600;
          }

          .header-item-label-bottom {
            display: inline-block;
            width: 25%;
            height: 2px;
            background-color: #3c95a8;
            position: absolute;
            left: 50%;
            bottom: 0;
            transform: translate(-50%, 0);
          }
        }

        .header-item-label {
          font-size: 12px;
          color: #9696a9;
          font-weight: 400;
        }

        .header-item-label-bottom {
          display: none;
        }

        .chat-el-icon {
          height: 100%;
          cursor: pointer;
          box-sizing: border-box;
          display: flex;
          justify-content: center;
          align-items: center;

          i {
            font-size: 16px;
            color: #909399;
          }

          &:hover {
            i {
              color: #3b95a8;
            }

            +.chat-tabs-tip {
              display: inline-block;
            }
          }
        }

        .chat-el-badge {
          width: 16px;
          height: 16px;
          line-height: 16px;
          text-align: center;
          border-radius: 50%;
          background-color: #ff3024;
          color: #fff;
          font-size: 12px;
          position: absolute;
          // top: 2px;
          // right: 10px;
          top: 0;
          right: 6px;
        }

        .chat-tabs-tip {
          display: none;
          height: 18px;
          line-height: 18px;
          padding: 0 4px;
          background-color: #000;
          opacity: 0.5;
          font-size: 12px;
          color: #fff;
          text-align: center;
          white-space: nowrap;
          z-index: 1;
          position: absolute;
          bottom: -18px;
          left: 50%;
          transform: translate(-50%, 0);
        }
      }
    }

    //四个切换tab对应的内容
    .chat-tabs-content {
      width: 100%;
      height: calc(100% - 35px);
      overflow-x: hidden;
      overflow-y: auto;

      .tabs-content-item {
        display: none;
        width: 100%;

        &.is-active {
          display: inline-block;
        }

        //会话中列表排序
        .btn-sort-wrap {
          // width: calc(100% - 10px);
          width: 100%;
          height: 35px;
          padding: 0 10px;
          margin: 0 auto;
          border-bottom: 1px solid rgb(228, 228, 235);
          display: flex;
          justify-content: flex-end;
          align-items: center;

          .el-button {
            height: 100%;
            padding: 10px 0;
            font-size: 12px;
            color: #575766;

            span {
              i {
                margin-left: -4px;
                color: #aeaebf;
              }
            }

            :hover {
              opacity: 0.75;
            }
          }
        }

        //历史会话搜索
        .form-search-wrap {
          // width: calc(100% - 10px);
          width: 100%;
          height: 50px;
          padding: 0 16px;
          margin: 0 auto;
          display: flex;
          justify-content: flex-end;
          align-items: center;

          /deep/ {
            .el-input {
              .el-input__inner {
                font-size: 12px;
                padding: 0 8px;
              }

              .el-icon-search {
                float: right;
              }
            }
          }
        }

        //分页
        .pager {
          width: 100%;
          padding: 20px 10px;
          font-size: 12px;
          color: #909399;
          text-align: center;
        }
      }

      //滚动条样式
      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 5px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
      }

      &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: #f5f7fa;
      }

      &::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        background: #fff;
      }
    }
  }
}

//客服状态
.kf-status-dropdown {
  margin-bottom: 0;

  .el-dropdown-menu__item {
    padding: 0 16px;
    line-height: 30px;
    margin-top: 4px;
    display: flex;
    justify-content: center;
    align-items: center;

    &:first-child {
      margin-top: 0;
    }

    &.selected {
      background-color: #ebf4f6;
      color: #62aab9;
    }

    img {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 4px;
    }

    span {
      font-size: 12px;
      color: #292933;
    }
  }
}

//会话列表中排序
.sort-dropdown li.selected {
  background-color: #ebf4f6;
  color: #62aab9;
}
</style>
