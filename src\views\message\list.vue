<template>
  <div class="page">
    <xyy-list-page>
      <template slot="header">
        <div class="top box row-between column-center">
          <header>全部通知</header>
          <div class="right">
            <span>{{ unreadNum }}条未读</span>
            <span>共{{ listQuery.total }}条消息</span>
            <el-button :disabled="isDisabled" plain @click="updateAllMessageStatus">全部标记为已读</el-button>
          </div>
        </div>
      </template>
      <template slot="body">
        <div class="content">
          <dl v-for="(item,index) in list" :key="index">
            <dt>
              <header>
                <el-button>
                  <i class="el-icon-date message-icon-date"></i>
                  {{ item.time }}
                </el-button>
              </header>
            </dt>
            <dd v-for="sItem in item.data" :key="sItem.id">
              <p class="box row-start column-center notify-content">
                <span>
                  <i v-show="sItem.status==0" class="notice"></i>
                </span>
                <i v-if="sItem.linkTab === 9" class="icon-warning"></i>
                <em class="message" @click="gotoWorkorderDetail(sItem)">{{ sItem.content }}</em>
                <el-button
                  v-if="sItem.userGroupId"
                  type="text"
                  style="margin-left: 16px"
                  @click="go2UserGroupEditPage(sItem)"
                >
                  查看详情
                  <i class="el-icon-arrow-right"></i>
                </el-button>
              </p>
              <p class="box row-between column-center">
                <time>{{ sItem.gmtCreate | formatTime }}</time>
                <span @click="updateSingleMessageStatus(sItem)">
                  <b v-show="sItem.status==0">知道了</b>
                </span>
              </p>
              <!-- 子消息 -->
              <div
                v-for="v in sItem.messageRecordReplies"
                v-show="sItem.messageRecordReplies.length"
                :key="v.id"
              >
                <p class="box row-start column-center notify-content">
                  <span>
                    <i v-show="v.status==0" class="notice"></i>
                  </span>
                  <i v-if="v.linkTab === 9" class="icon-warning"></i>
                  <em class="message">{{ v.content }}</em>
                </p>
                <p class="box row-between column-center">
                  <time>{{ v.gmtCreate | formatTime }}</time>
                </p>
              </div>
            </dd>
          </dl>
        </div>
        <p class="bottom">
          <el-button v-if="isMore" plain :disabled="btnLoadMoreDisabled" @click="loadMore">加载更多...</el-button>
        </p>
      </template>
    </xyy-list-page>
  </div>
</template>

<script>
import {
  getMessageList,
  updateAllMessageStatus,
  updateSingleMessageStatus,
  getUserGroupInfo,
} from '@/api/message';
import { getWorkorderInfoAndReplyInfo } from '@/api/mySheetManage';
export default {
  name: 'List',
  data() {
    return {
      list: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      unreadNum: '',
      isDisabled: false,
      isMore: true,
      btnLoadMoreDisabled: true, //加载更多按钮的diaabled
    };
  },
  mounted() {
    this.initData();
    this.getList();
  },
  methods: {
    initData() {
      this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0,
      };
      if (this.list.length > 0) this.list.splice(0, this.list.length);
    },
    getList: function (listQuery) {
      const query = listQuery || this.listQuery;
      const { page, pageSize } = query;
      this.btnLoadMoreDisabled = true;
      getMessageList(page, pageSize)
        .then((res) => {
          if (res.code === 1) {
            const { list, pageNum, pageSize, total, unreadNum } = res.data;
            this.unreadNum = unreadNum;
            this.unreadNum > 0
              ? (this.isDisabled = false)
              : (this.isDisabled = true);
            if (pageNum >= Math.ceil(parseInt(total) / parseInt(pageSize))) {
              this.isMore = false;
            } else {
              this.isMore = true;
            }
            this.list.push(...list);
            console.log(this.list, 'this.list');
            this.listQuery = {
              ...this.listQuery,
              page: pageNum,
              pageSize,
              total,
            };
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .finally(() => {
          this.btnLoadMoreDisabled = false;
        });
    },
    updateAllMessageStatus() {
      updateAllMessageStatus().then((res) => {
        if (res.code === 1) {
          this.$XyyMessage.success('通知全部已读');
          this.initData();
          this.getList();
          this.$store.commit('message/SET_Message_isDot', false);
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    updateSingleMessageStatus(item) {
      updateSingleMessageStatus({
        id: !item.parentId ? item.id : item.parentId,
        replyId: item.parentId ? item.id : null,
      }).then((res) => {
        if (res.code === 1) {
          this.initData();
          this.getList();
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    loadMore() {
      const { page, pageSize, total } = this.listQuery;
      if (page < Math.ceil(parseInt(total) / parseInt(pageSize))) {
        this.isMore = true;
        this.listQuery.page += 1;
        this.getList(this.listQuery);
      } else {
        this.$XyyMessage.warning('没有更多的数据啦');
        this.isMore = false;
        return;
      }
    },
    // 跳转到编辑用户组页面
    async go2UserGroupEditPage(row) {
      try {
        const userGroupId = row.userGroupId;
        let res = await getUserGroupInfo({ userGroupId });
        if (res.code === 1) {
          const { description, userGroupName, versionCode } = res.data;
          this.$router.push({
            path: '/worksheet/userGroupEdit/' + userGroupId,
            query: {
              description,
              userGroupName,
              userGroupId,
              versionCode,
            },
          });
        } else {
          this.$XyyMessage.error(res.msg);
        }
      } catch (error) {}
    },

    //点击追加说明，跳转到工单详情
    gotoWorkorderDetail(notice) {
      //16:追加说明的通知
      //先查询是否有工单权限，有权限跳详情，无权限弹提示
      if (notice.noticeType === 16) {
        getWorkorderInfoAndReplyInfo({
          workorderId: notice.workorderId,
        }).then((resp) => {
          if (resp.code === 1) {
            //有权限
            this.$router.push({
              path: `/workStatus${resp.data.base_field.form_type_id}/sheetDetail/${notice.workorderId}`,
              query: {
                id: notice.workorderId,
                type: 'notice',
                currentUrl: this.$route.path,
              },
            });
          } else {
            //无权限
            this.$XyyMessage.error(resp.msg);
          }
        });
      }
    },
  },
};
</script>

<style scoped  rel="stylesheet/scss" lang="scss">
@import '@/styles/variables.scss';
.page {
  padding-left: 20px;
  padding-right: 20px;
  padding-bottom: 20px;
  overflow: auto;
  .top {
    margin-top: 12px;
    height: 36px;
    line-height: 36px;
    header {
      font-size: 18px;
      color: $menuText;
      font-weight: 600;
    }
    .right {
      font-size: 14px;
      span:first-child {
        color: $messageRed;
        margin-right: 20px;
      }
      span:nth-child(2) {
        color: #909399;
        margin-right: 20px;
      }
      .el-button {
        height: 36px;
        color: $messageText;
        font-size: 14px;
        padding-left: 12px;
        padding-right: 12px;
        line-height: 0;
        border-radius: 0;
      }
    }
  }
  .content {
    dl:first-child {
      margin-top: 0;
    }
    dl {
      margin: 0;
      margin-top: 30px;
      dt {
        header {
          .el-button {
            height: 28px;
            background: #f0f2f5;
            border-radius: 14px;
            color: $messageText;
            padding: 0;
            line-height: 28px;
            padding-left: 10px;
            padding-right: 10px;
            .message-icon-date {
              margin-right: 4px;
            }
          }
        }
      }
      dd {
        margin: 0;
        padding: 0;
        font-size: 14px;
        border-bottom: 1px solid #eee;
        .icon-warning {
          display: inline-block;
          width: 14px;
          height: 14px;
          margin: 0 4px 0 0;
          background: url('../layout/components/images/<EMAIL>')
            center;
          background-size: cover;
        }
        .notify-content {
          margin: 14px 0;
        }
        em {
          font-style: normal;
          color: #292933;
          cursor: pointer;
        }
        p:first-child {
          span {
            display: inline-block;
            margin-right: 6px;
            // width: 10px;
            // height: 10px;
            text-align: center;
            line-height: 0;
          }
        }
        p:nth-child(2) {
          padding-left: 14px;
          margin-bottom: 14px;
          time {
            color: #909399;
          }
          span {
            color: $navBarBg;
            cursor: pointer;
            b {
              font-weight: normal !important;
            }
          }
        }
      }
    }
  }
  .bottom {
    text-align: center;
    .el-button {
      height: 32px;
      padding: 0;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      color: rgba(0, 0, 0, 0.65);
      padding-left: 48px;
      padding-right: 48px;
    }
  }
}
</style>
