
<template>
  <chatcontent
    class="im_container_content"
    :data="chatdata"
    :showShade="false"
    contactNickname
    :ownerAvatarUrl="ownerAvatarUrl"
    :contactAvatarUrl="contactAvatarUrl"
    :width="width"
    :wrapBg="'white'"
    :sectionName="sectionName"
    :needEvent="needEvent"
    :maxHeight="maxHeight"
    ref="newMessage"
  ></chatcontent>
</template>

<script>
// import content from './container_message/im_ChatContent';
import chatcontent from './im_ChatContent';
export default {
  props: {
    ownerAvatarUrl: {
      type: String
    },
    contactAvatarUrl: {
      type: String
    },
    chatdata: {
      type: Array
    },
    getUpperData: {
      type: Function
      // required: true
    },
    needEvent: {
      type: Boolean,
      default: false
    },
    getUnderData: {
      type: Function
      // required: true
    },
    sectionName: {
      type: String
    },
    maxHeight: {
      type: Number,
      default: 660
    }
  },
  data() {
    return {
      upperTimes: 0,
      underTimes: 0,
      upperId: 0,
      underId: 6,
      width: 650
    };
  },
  components: { chatcontent },
  created() {
    this.initWidth();
  },
  methods: {
    initWidth() {
      this.width = 650;
    },
    //自增ID
    newMessage(message) {
      // var me = this;
      // message.id = me.underId + 1;
      // me.underId += 1;
      this.$refs.newMessage.refreshMessage(message);
    },
    scrollowToBottom() {
      this.$refs.newMessage.scrollerToBottom();
    }
  }
};
</script>

<style>
* {
  margin: 0;
  padding: 0;
}

.im_container_content h1,
h2 {
  font-weight: normal;
}

.im_container_content ul {
  list-style-type: none;
  padding: 0;
}

.im_container_content li {
  display: inline-block;
}

.im_container_content ::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
.im_container_content ::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #f5f7fa;
}
.im_container_content ::-webkit-scrollbar-track {
  /*滚动条里面轨道*/

  background: #fff;
}
</style>
