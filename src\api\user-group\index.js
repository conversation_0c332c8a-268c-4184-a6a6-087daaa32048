import request from '@/utils/request';
import { formData } from '@/utils/index';

/* 列表页 */

/* 用户组列表查询 */
export function getUserGroupList(pageNum, pageSize) {
  const userGroupName = arguments.length === 3 ? arguments[2] : '';
  return request({
    url: '/user/group',
    method: 'get',
    params: {
      pageNum,
      pageSize,
      userGroupName
    }
  });
}
/* 批量禁用用户组 type=0为禁用，type=1为启用*/
export function disableBatchUserGroup(ids, type, versionCodes) {
  return request({
    url: '/user/group/disableBatchUserGroup',
    method: 'patch',
    params: {
      ids: ids.join(','),
      versionCodes: versionCodes.join(','),
      type
    }
  });
}

/* 禁用用户组 */
export function disableUserGroup(params) {
  return request({
    url: '/user/group/disableUserGroup',
    method: 'patch',
    params: params
  });
}

/* 用户组复制 */
export function copyUserGroup(userGroupId) {
  return request({
    url: '/user/group/copyUserGroupByUserGroupId',
    method: 'post',
    data: formData({
      userGroupId
    })
  });
}

/* 删除用户组 */
export function removeUserGroup(id, versionCode) {
  return request({
    url: '/user/group',
    method: 'delete',
    params: {
      id,
      versionCode
    }
  });
}

/* 新增/编辑页 */

/**
 * 根据组ID获取用户信息
 * @param {userGroupId,type:工单分配，工单转移，工单重启 = 2,其他 = 1 }
 */
export function getUserGroupInfo(userGroupId) {
  return request({
    url: '/user/group/listUserByUserGroupId',
    method: 'get',
    params: {
      userGroupId,
      type: 1
    }
  });
}

/* 部门组织机构列表 */
export function getListDept() {
  return request({
    url: '/system/ListDept',
    method: 'get'
  });
}

/* 通过部门OA ID获取用户列表 */
export function getDeptUserList(deptOaId) {
  return request({
    url: '/system/ListUserByDeptOaId',
    method: 'get',
    params: {
      deptOaId,
      pageNum: 1,
      pageSize: 999
    }
  });
}

/* 通过工号或姓名获取用户列表 */
export function searchUserList(nameOrStaffNum, pageNum, pageSize) {
  return request({
    url: '/system/ListUserByNameOrStaffNum',
    method: 'get',
    params: {
      nameOrStaffNum,
      pageNum,
      pageSize
    }
  });
}

/* 新增用户组 */
export function addUserGroup(data) {
  return request({
    url: '/user/group',
    method: 'post',
    data: data
  });
}

/* 更新用户组 */
export function updateUserGroup(data) {
  console.log(data);

  return request({
    url: '/user/group',
    method: 'put',
    data: data
  });
}
