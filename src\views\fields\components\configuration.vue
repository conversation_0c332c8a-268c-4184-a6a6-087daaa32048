<template>
  <el-form
    ref="ruleForms"
    :model="ruleForm"
    :rules="rules"
    :disabled="disabledProps"
    class="single"
    label-width="100px"
  >
    <!-- 0单行输入 1多行 2 下拉 3 单选4 多选 5日期 6级联 87 省市区 8 电话 10 附件 9邮箱(无)未进行判断说明是每个组件都有该输入框-->
    <el-form-item label="字段名称" prop="fieldName">
      <el-input
        v-model.trim="ruleForm.fieldName"
        maxlength="20"
        class="single-input"
        placeholder="注：不能与其他字段重名"
      />
    </el-form-item>
    <el-form-item label="字段文本" prop="fieldText">
      <el-input
        v-model.trim="ruleForm.fieldText"
        class="single-input"
        maxlength="20"
        placeholder="请输入显示在页面上的文字"
      />
    </el-form-item>
    <template v-if="[0, 1, 8, 9].indexOf(keys) !== -1">
      <el-form-item label="限制字符数" prop="limitNumberCharacters">
        <el-input-number
          v-model.number.trim="ruleForm.limitNumberCharacters"
          :min="1"
          :max="keys === 1 ? 1000 : 100"
          controls-position="right"
          class="single-input"
          placeholder="请输入大于0的数字"
        ></el-input-number>
      </el-form-item>
      <el-form-item label="提示信息" prop="tips">
        <el-input v-model="ruleForm.tips" class="single-input" maxlength="128" placeholder="提示信息" />
      </el-form-item>
      <el-form-item
        :rules="keys===8?[{ validator: validateMobile, trigger: 'change' }]:(keys===9?[{ type: 'email', message: '请输入正确的邮箱地址', trigger: 'change' }]:[])"
        label="默认值"
        prop="defaultValue"
      >
        <el-input
          v-model.trim="ruleForm.defaultValue"
          :maxlength="ruleForm.limitNumberCharacters"
          class="single-input"
          placeholder="请输入默认值"
        />
      </el-form-item>
      <el-form-item label="备注说明" prop="remarks">
        <el-input
          v-model.trim="ruleForm.remarks"
          :autosize="{ minRows: 5 }"
          type="textarea"
          class="single-input"
          maxlength="100"
          show-word-limit
          placeholder="请输入备注说明"
        />
      </el-form-item>
    </template>
    <!-- 下拉 -->
    <template v-if="keys === 2">
      <el-form-item label="提示信息" prop="tips">
        <el-input v-model.trim="ruleForm.tips" class="single-input" maxlength="128" placeholder="提示信息" />
      </el-form-item>
      <el-form-item label="备注说明" prop="remarks">
        <el-input
          v-model.trim="ruleForm.remarks"
          :autosize="{ minRows: 5 }"
          type="textarea"
          class="single-input"
          maxlength="100"
          show-word-limit
          placeholder="请输入备注说明"
        />
      </el-form-item>
      <fieldsBox class="select-box">
        <div slot="header">选项设置</div>
        <div slot="main">
          <el-radio-group v-model.trim="ruleForm.optionSettings.selectOptions.optionsValue">
            <el-form-item
              v-for="(item, index) in ruleForm.optionSettings.selectOptions.optionsArray"
              :key="index"
              :prop="'optionSettings.selectOptions.optionsArray.' + index + '.optionsDesc'"
              :rules="{
                required: true, message: '选项不能为空', trigger: 'blur'
              }"
            >
              <el-radio
                :label="item.val"
                class="radioShow"
                @click.native.stop.prevent="radioClick(index, 'selectOptions')"
              >默认</el-radio>
              <el-input
                v-model.trim="item.optionsDesc"
                maxlength="20"
                class="radio-input"
                placeholder="请输入选项(必填)"
              />
              <el-button
                v-if="ruleForm.optionSettings.selectOptions.optionsArray.length>1"
                class="del-item"
                plain
                size="mini"
                @click="delClick('selectOptions', index)"
              >
                <svg-icon icon-class="del-btn" />
              </el-button>
            </el-form-item>
          </el-radio-group>
          <el-button class="add-options" icon="el-icon-plus" @click="addClick('selectOptions')">添加</el-button>
        </div>
      </fieldsBox>
    </template>
    <!-- 单选 -->
    <template v-if="keys === 3">
      <el-form-item label="备注说明" prop="remarks">
        <el-input
          v-model.trim="ruleForm.remarks"
          :autosize="{ minRows: 5 }"
          type="textarea"
          class="single-input"
          maxlength="100"
          show-word-limit
          placeholder="请输入备注说明"
        />
      </el-form-item>
      <fieldsBox class="select-box">
        <div slot="header">选项设置</div>
        <div slot="main">
          <el-radio-group v-model.trim="ruleForm.optionSettings.radioOptions.optionsValue">
            <el-form-item
              v-for="(item, index) in ruleForm.optionSettings.radioOptions.optionsArray"
              :key="index"
              :prop="'optionSettings.radioOptions.optionsArray.' + index + '.optionsDesc'"
              :rules="{required: true, message: '选项不能为空', trigger: 'blur' }"
            >
              <el-radio
                :label="item.val"
                class="radioShow"
                @click.native.stop.prevent="radioClick(index, 'radioOptions')"
              >默认</el-radio>
              <el-input
                v-model.trim="item.optionsDesc"
                maxlength="20"
                class="radio-input"
                placeholder="请输入选项(必填)"
              />
              <el-button
                v-if="ruleForm.optionSettings.radioOptions.optionsArray.length>1"
                class="del-item"
                plain
                size="mini"
                @click="delClick('radioOptions', index)"
              >
                <svg-icon icon-class="del-btn" />
              </el-button>
            </el-form-item>
          </el-radio-group>
          <el-button class="add-options" icon="el-icon-plus" @click="addClick('radioOptions')">添加</el-button>
        </div>
      </fieldsBox>
    </template>
    <!-- 多选 -->
    <template v-if="keys === 4">
      <el-form-item label="备注说明" prop="remarks">
        <el-input
          v-model.trim="ruleForm.remarks"
          :autosize="{ minRows: 5 }"
          type="textarea"
          class="single-input"
          maxlength="100"
          show-word-limit
          placeholder="请输入备注说明"
        />
      </el-form-item>
      <fieldsBox class="select-box">
        <div slot="header">选项设置</div>
        <div slot="main">
          <el-checkbox-group v-model.trim="ruleForm.optionSettings.checkedOptions.optionsValue">
            <el-form-item
              v-for="(item, index) in ruleForm.optionSettings.checkedOptions.optionsArray"
              :key="index"
              :prop="'optionSettings.checkedOptions.optionsArray.' + index + '.optionsDesc'"
              :rules="{required: true, message: '选项不能为空', trigger: 'blur' }"
            >
              <el-checkbox :label="item.val">默认</el-checkbox>
              <el-input
                v-model.trim="item.optionsDesc"
                maxlength="20"
                class="radio-input"
                placeholder="请输入选项(必填)"
              />
              <el-button
                v-if="ruleForm.optionSettings.checkedOptions.optionsArray.length>1"
                class="del-item"
                plain
                size="mini"
                @click="delClick('checkedOptions', index)"
              >
                <svg-icon icon-class="del-btn" />
              </el-button>
            </el-form-item>
          </el-checkbox-group>
          <el-button class="add-options" icon="el-icon-plus" @click="addClick('checkedOptions')">添加</el-button>
        </div>
      </fieldsBox>
    </template>
    <!-- 日期 -->
    <template v-if="keys === 5">
      <el-form-item class="selected-item" label="日期格式" prop="optionSettings.dateOptions.dateSelect">
        <el-select v-model.trim="ruleForm.optionSettings.dateOptions.dateSelect">
          <el-option
            v-for="(item, index) in ruleForm.optionSettings.dateOptions.dateArray"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="selected-item" label="默认值" prop="optionSettings.dateOptions.defaultDate">
        <el-select v-model.trim="ruleForm.optionSettings.dateOptions.defaultDate" @change="dateChange">
          <el-option
            v-for="(item, index) in ruleForm.optionSettings.dateOptions.defaultDateArray"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="提示信息" prop="tips">
        <el-input v-model.trim="ruleForm.tips" class="single-input" maxlength="128" placeholder="提示信息" />
      </el-form-item>
      <el-form-item label="备注说明" prop="remarks">
        <el-input
          v-model.trim="ruleForm.remarks"
          :autosize="{ minRows: 5 }"
          type="textarea"
          class="single-input"
          maxlength="100"
          show-word-limit
          placeholder="请输入备注说明"
        />
      </el-form-item>
    </template>
    <!-- 级联 -->
    <template v-if="keys === 6">
      <el-form-item label="提示信息" prop="tips">
        <el-input v-model.trim="ruleForm.tips" class="single-input" maxlength="128" placeholder="提示信息" />
      </el-form-item>
      <el-form-item label="备注说明" prop="remarks">
        <el-input
          v-model.trim="ruleForm.remarks"
          :autosize="{ minRows: 5 }"
          type="textarea"
          class="single-input"
          maxlength="100"
          show-word-limit
          placeholder="请输入备注说明"
        />
      </el-form-item>
      <fieldsBox class="select-box">
        <div slot="header">
          <span>选项设置</span>
          <span class="setting-btn-right">
            <el-button type="text">
              <svg-icon icon-class="import" title="导入" @click="toImport"></svg-icon>
            </el-button>
            <el-button type="text" @click="() => appendParent('treeOptions')">
              <svg-icon icon-class="add-btn" title="添加" />
            </el-button>
          </span>
          <import-file :dialog-visible.sync="importStatus" @importCallback="importOptions"></import-file>
        </div>
        <div slot="main">
          <el-tree
            :data="ruleForm.optionSettings.treeOptions.optionsArray"
            :expand-on-click-node="false"
            class="tree-box"
            node-key="id"
            default-expand-all
          >
            <div slot-scope="{ node, data }" class="custom-tree-node">
              <el-input
                v-model.trim="data.label"
                :class="{'error-color':data.label.length>20}"
                type="text"
                maxlength="20"
                placeholder="请输入选项(必填)"
              ></el-input>
              <span>
                <el-button type="text" class="del-item" @click="() => removeItem(node, data)">
                  <svg-icon icon-class="del-btn" />
                </el-button>
                <el-button type="text" @click="() => append(node, data)">
                  <svg-icon icon-class="add-btn" />
                </el-button>
              </span>
            </div>
          </el-tree>
        </div>
      </fieldsBox>
    </template>
    <template v-if="[7, 10].indexOf(keys) !== -1">
      <el-form-item label="提示信息" prop="tips">
        <el-input v-model.trim="ruleForm.tips" maxlength="128" class="single-input" placeholder="提示信息" />
      </el-form-item>
      <el-form-item label="备注说明" prop="remarks">
        <el-input
          v-model.trim="ruleForm.remarks"
          :autosize="{ minRows: 5 }"
          type="textarea"
          class="single-input"
          maxlength="100"
          show-word-limit
          placeholder="请输入备注说明"
        />
      </el-form-item>
    </template>
  </el-form>
</template>

<script>
import fieldsBox from './fields-box';
import { validateOptions, validateField } from '@/api/fields/index';
import importFile from './import-file';

let id = new Date().getTime();
export default {
  name: 'Configuration',
  components: {
    fieldsBox,
    importFile
  },
  model: {
    prop: 'ruleForm',
    event: 'input'
  },
  props: {
    disabledProps: {
      // 保存成功后 禁用表单
      type: Boolean,
      default: function() {
        return false;
      }
    },
    keys: {
      // type类型
      type: Number,
      default: function() {
        return -1;
      }
    },
    ruleForm: {
      // 参数obj
      type: Object,
      default: function() {
        return {};
      }
    }
  },
  data() {
    return {
      importStatus: false,
      rules: {
        fieldName: [
          { required: true, message: '请输入字段名称', trigger: 'blur' },
          { min: 1, max: 20, message: '长度20 个字符', trigger: 'blur' }
        ],
        fieldText: [
          { required: true, message: '请输入字段文本', trigger: 'blur' }
        ],
        limitNumberCharacters: [
          { required: true, message: '请输入限制字符数' },
          { type: 'number', message: '只能输入整数且大于0的数字' },
          { pattern: /^[0-9]+$/, message: '只能输入整数且大于0的数字' }
        ],
        'optionSettings.dateOptions.dateSelect': [
          { required: true, message: '请选择日期格式' }
        ],
        'optionSettings.dateOptions.defaultDate': [
          { required: true, message: '请选择默认值' }
        ]
      }
    };
  },
  methods: {
    validateMobile(rule, value, callback) {
      if (String(value).trim() && !/\d/.test(Number(String(value).trim()))) {
        callback(new Error('请输入正确的电话号码'));
      } else {
        callback();
      }
    },
    radioClick(index, str) {
      var optionsVal = this.ruleForm.optionSettings[str].optionsValue;
      this.ruleForm.optionSettings[str].optionsValue =
        optionsVal === this.ruleForm.optionSettings[str].optionsArray[index].val
          ? ''
          : this.ruleForm.optionSettings[str].optionsArray[index].val;
    },
    // 返回验证结果
    submitForm() {
      return this.$refs['ruleForms'].validate();
    },
    // 删除选项卡
    delClick(type, index) {
      // 编辑状态
      if (this.$route.query.id) {
        const params = {
          fieldId: this.$route.query.id,
          fieldType: this.keys,
          filedOptionKey: this.ruleForm.optionSettings[type].optionsArray[index]
            .val
        };
        validateOptions(params)
          .then(res => {
            if (res.code === 1) {
              this.delOperate(type, index);
            } else {
              this.$XyyMessage.error(res.msg);
            }
          })
          .catch(() => {});
      } else {
        this.delOperate(type, index);
      }
    },
    /**
     * 删除操作
     */
    delOperate(type, index) {
      if (type === 'checkedOptions') {
        const vals = this.ruleForm.optionSettings[type].optionsValue;
        const i = vals.indexOf(
          this.ruleForm.optionSettings[type].optionsArray[index].val
        );
        if (i > -1) {
          this.ruleForm.optionSettings[type].optionsValue.splice(i, 1);
        }
      } else {
        const val = this.ruleForm.optionSettings[type].optionsValue;
        if (
          val === this.ruleForm.optionSettings[type].optionsArray[index].val
        ) {
          this.ruleForm.optionSettings[type].optionsValue = '';
        }
      }
      this.ruleForm.optionSettings[type].optionsArray.splice(index, 1);
    },
    // 增加选项卡
    addClick(type) {
      const lengthNum = this.ruleForm.optionSettings[type].optionsArray.length;
      if (
        ['radioOptions', 'checkedOptions'].indexOf(type) !== -1 &&
        lengthNum >= 15
      ) {
        this.$XyyMessage.error('选项不能多于15个');
        return false;
      }
      if (type === 'selectOptions' && lengthNum >= 50) {
        this.$XyyMessage.error('选项不能多于50个');
        return false;
      }
      this.ruleForm.optionSettings[type].optionsArray.push({
        val: new Date().getTime()
      });
    },
    // 日期默认值
    dateChange(v) {
      const nowDate = new Date().getTime();
      const obj = {
        0: nowDate,
        1: nowDate - 5 * 24 * 3600 * 1000,
        2: nowDate - 24 * 3600 * 1000,
        3: nowDate - 7 * 24 * 3600 * 1000,
        4: ''
      };
      this.ruleForm.optionSettings.dateOptions.dateValue = obj[v];
    },
    // 级联
    append(node, data) {
      // 添加下级节点
      if (node.parent.level === 6) {
        this.$XyyMessage.error('层级不能多于7级');
        return false;
      }
      const numLevel = node.parent.level + 2;
      const newChild = { value: id++, label: numLevel + '级' };
      if (!data.children) {
        this.$set(data, 'children', []);
      }
      data.children.push(newChild);
    },
    appendParent(node) {
      // 添加第一级
      const nums = this.ruleForm.optionSettings[node].optionsArray.length + 1;
      const newChild = { value: id++, label: '一级 ' + nums };
      this.ruleForm.optionSettings[node].optionsArray.push(newChild);
    },
    removeItem(node, data) {
      if (this.$route.query.id) {
        const params = {
          fieldId: this.$route.query.id,
          fieldType: this.keys,
          filedOptionKey: data.value
        };
        validateOptions(params)
          .then(res => {
            if (res.code === 1) {
              // 删除当前的节点
              const parent = node.parent;
              const children = parent.data.children || parent.data;
              const index = children.findIndex(d => d.value === data.value);
              children.splice(index, 1);
              if (children.length === 0) parent.data.children = null;
            } else {
              this.$XyyMessage.error(res.msg);
            }
          })
          .catch(() => {});
      } else {
        // 删除当前的节点
        const parent = node.parent;
        const children = parent.data.children || parent.data;
        const index = children.findIndex(d => d.value === data.value);
        children.splice(index, 1);
        if (children.length === 0) parent.data.children = null;
      }
    },
    importOptions(datas) {
      this.ruleForm.optionSettings['treeOptions'].optionsArray = datas;
      if (!this.validateTreeOptions(datas)) {
        this.$XyyMessage.error('级联选项不能超出20字');
      }
    },
    /**
     * 校验级联
     */
    validateTreeOptions(datas) {
      let pass = true;
      for (let i = 0; i < datas.length; i++) {
        if (datas[i].label.length > 20) {
          pass = false;
          break;
        }
        if (datas[i].children) {
          pass = this.validateTreeOptions(datas[i].children);
        }
      }
      return pass;
    },
    toImport() {
      if (this.$route.query.id) {
        validateField({ fieldId: this.$route.query.id })
          .then(res => {
            if (res.code === 1) {
              if (res.data) {
                this.$XyyMsg({
                  title: '提示',
                  closeBtn: false,
                  content: `无法进行导入操作（级联选项已作为流程分支条件，导入替换新数据时会导致现有流程异常，建议先解除绑定关系）`,
                  onSuccess: () => {}
                });
                return false;
              } else {
                this.importStatus = true;
              }
            } else {
              this.$XyyMessage.error(res.msg);
              return false;
            }
          })
          .catch(() => {});
      } else {
        this.importStatus = true;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.single {
  font-size: 14px;
  padding: 0;
  list-style: none;
  .el-form-item {
    margin: 20px 0;
    /deep/ .el-input__inner {
      height: 36px;
      line-height: 36px;
    }
  }
  .custom-tree-node {
    /deep/ .el-input__inner {
      height: 36px;
      line-height: 36px;
    }
  }
  &-input {
    &.el-input-number__decrease,
    &.el-input-number__increase {
      display: none;
    }
    min-width: 260px;
  }
  .selected-item {
    .el-select {
      width: 100%;
    }
  }
  /* 级联设置 */
  .tree-box {
    .el-input {
      height: 100%;
      &.error-color {
        /deep/.el-input__inner {
          border-color: #f56c6c;
        }
      }
    }
    /deep/ .el-tree-node__content {
      margin: 20px 0;
    }
  }
  /* 选项设置 */
  .select-box {
    border: none;
    padding-bottom: 60px;
    .setting-btn-right {
      float: right;
      .el-button {
        padding: 10px 0;
      }
      .el-button + .el-button {
        margin-left: 5px;
        position: relative;
        top: -1px;
      }
    }

    .el-radio-group,
    .el-checkbox-group {
      width: 100%;
      .el-radio,
      .el-checkbox {
        margin-left: -85px;
      }
    }
    .radio-input {
      display: inline-block;
      width: 70%;
    }
    .del-item {
      border: none;
      margin-left: 10px;
      padding: 0;
    }
    .add-options {
      width: 100%;
      border-style: dashed;
    }
  }
}
</style>
<style lang="scss">
.fields-box-main {
  .el-form-item__label {
    font-weight: 400;
  }
  .tree-box {
    box-shadow: none;
    border: none;
    .el-tree-node__content {
      height: auto;
      margin: 5px 0;
      &:before {
        content: '*';
        display: inline;
        color: red;
      }
      .el-form-item {
        margin: 0;
      }
      .el-form-item__content {
        margin-left: 0 !important;
      }
    }
    .el-tree-node__children {
      overflow: inherit;
    }
  }
}
/* 多行输入下标遮挡*/
.el-textarea__inner {
  padding-bottom: 20px;
}
.el-textarea .el-input__count {
  bottom: 1px;
  right: 25px;
  width: 90%;
  background-color: #fff;
  text-align: right;
  line-height: 20px;
}

.radioShow {
  .el-radio__input .el-radio__inner,
  .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled)
    .el-radio__inner {
    box-shadow: 0 0 0 0 #fff !important;
  }
}
.single-input {
  width: 100%;
  .el-input__inner {
    text-align: left;
  }
  .el-input-number__decrease,
  .el-input-number__increase {
    display: none;
  }
}
</style>
