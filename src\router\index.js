import Vue from 'vue';
import Router from 'vue-router';

// in development-env not use lazy-loading, because lazy-loading too many pages will cause webpack hot update too slow. so only in production use lazy-loading;
// detail: https://panjiachen.github.io/vue-element-admin-site/#/lazy-loading

Vue.use(Router);

/* Layout */
import workSheet from './workSheet.js';
import error from './error.js';
import chat from './chat.js';
import config from './config.js';
import monitor from './monitor.js';
import store from '../store';
import statistics from './statistics.js';
import shop from './shop.js';
import receipt from './receipt.js';
import knowledge_base from './knowledge_base.js';
import crm from './crm.js';

/**
* hidden: true                   if `hidden:true` will not show in the sidebar(default is false)
* alwaysShow: true               if set true, will always show the root menu, whatever its child routes length
*                                if not set alwaysShow, only more than one route under the children
*                                it will becomes nested mode, otherwise not show the root menu
* redirect: noredirect           if `redirect:noredirect` will no redirect in the breadcrumb
* name:'router-name'             the name is used by <keep-alive> (must set!!!)
* meta : {
    title: 'title'               the name show in subMenu and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    breadcrumb: false            if false, the item will hidden in breadcrumb(default is true)
    mark:''                        session：会话 workSheet:工单 qs:质检  monitor:监控  statictics:统计 config :配置
  }
**/
const mainRouter = [
  ...error,
  ...config,
  ...workSheet,
  ...statistics,
  ...chat,
  ...shop,
  ...monitor,
  ...receipt,
  ...knowledge_base,
  ...crm
];
export const constantRoutes = mainRouter;



const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: mainRouter
});
// export default new Router({
//   // mode: 'history', //后端支持可开
//   scrollBehavior: () => ({
//     y: 0
//   }),
//   routes: mainRouter
// });
const router = createRouter();

export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;

