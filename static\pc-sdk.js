(function(window) {
  'use strict';

  /*帮助方法*/
  var helper = {
    // 唯一标示 uuid
    uuid: function() {
      var s = [];
      var hexDigits = '0123456789abcdef';
      for (var i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
      }
      s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
      s[8] = s[13] = s[18] = s[23] = '-';

      var uuid = s.join('');
      return uuid;
    },
    //遍历
    /**
     * @method each
     * @parame loopable 要遍历的对象
     * @parame callback 回调函数
     * @parame self 上下文
     **/

    each: function(loopable, callback, self) {
      var additionalArgs = Array.prototype.slice.call(arguments, 3);
      if (loopable) {
        if (loopable.length === +loopable.length) {
          for (var i = 0; i < loopable.length; i++) {
            callback.apply(self, [loopable[i], i].concat(additionalArgs));
          }
        } else {
          for (var item in loopable) {
            callback.apply(self, [loopable[item], item].concat(additionalArgs));
          }
        }
      }
    },
    //扩展
    extend: function(t) {
      return (
        helper.each(Array.prototype.slice.call(arguments, 1), function(e) {
          for (var n in e) void 0 !== e[n] && (t[n] = e[n]);
        }),
        t
      );
    },
    //判断是否为undefined
    isUndefined: function(t) {
      return typeof t == 'undefined';
    },
    //判断是否为string
    isString: function(t) {
      return '[object String]' == Object.prototype.toString.call(t);
    },
    //判断是否为array
    isArray: function(t) {
      return '[object Array]' === Object.prototype.toString.call(t);
    },
    //判断是否为function
    isFunction: function(t) {
      return '[object Function]' === Object.prototype.toString.call(t);
    },
    //判断是否为object
    isObject: function(t) {
      return '[object Object]' === Object.prototype.toString.call(t);
    },
    //判断是否包含
    includes: function(t, e) {
      return -1 !== t.indexOf(e);
    },
    encode: function(t) {
      var ob = {};
      for (var i in t) ob['_' + i] = t[i];
      return ob;
    },
    //去除空格
    trim: function(t) {
      return t ? t.replace(/^\s+|\s+$/g, '') : '';
    },
    //生成随机数
    random: function(min, max) {
      return Math.round(Math.random() * (max - min)) + min;
    },
    hasMobileSdk: function() {
      var t = !!(
        window.sdkTracker ||
        (window.webkit &&
          window.webkit.messageHandlers &&
          window.webkit.messageHandlers.sdkTracker)
      );
      return {
        flag: t,
        track: function(e, n) {
          t &&
            (window.sdkTracker
              ? window.sdkTracker.trackProperty(e, helper.JSONEncode(n))
              : window.webkit.messageHandlers.sdkTracker.postMessage({
                  type: 'track',
                  name: e,
                  prop: n
                }));
        },
        identify: function(e, n) {
          t &&
            (window.sdkTracker
              ? window.sdkTracker.identifyProperty(e, helper.JSONEncode(n))
              : window.webkit.messageHandlers.sdkTracker.postMessage({
                  type: 'identify',
                  name: e,
                  prop: n
                }));
        }
      };
    },
    JSONEncode: function(t) {
      var e = function(t) {
          var e = /[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,
            n = {
              '\b': '\\b',
              '\t': '\\t',
              '\n': '\\n',
              '\f': '\\f',
              '\r': '\\r',
              '"': '\\"',
              '\\': '\\\\'
            };
          return (
            (e.lastIndex = 0),
            e.test(t)
              ? '"' +
                t.replace(e, function(t) {
                  var e = n[t];
                  return 'string' == typeof e
                    ? e
                    : '\\u' + ('0000' + t.charCodeAt(0).toString(16)).slice(-4);
                }) +
                '"'
              : '"' + t + '"'
          );
        },
        n = function(t, i) {
          var r = '',
            o = 0,
            s = '',
            a = '',
            c = 0,
            u = r,
            d = [],
            f = i[t];
          switch (
            (f &&
              'object' == typeof f &&
              'function' == typeof f.toJSON &&
              (f = f.toJSON(t)),
            typeof f)
          ) {
            case 'string':
              return e(f);
            case 'number':
              return isFinite(f) ? String(f) : 'null';
            case 'boolean':
            case 'null':
              return String(f);
            case 'object':
              if (!f) return 'null';
              if (
                ((r += '    '),
                (d = []),
                '[object Array]' === Object.prototype.toString.apply(f))
              ) {
                for (c = f.length, o = 0; o < c; o += 1)
                  d[o] = n(o, f) || 'null';
                return (
                  (a =
                    0 === d.length
                      ? '[]'
                      : r
                      ? '[\n' + r + d.join(',\n' + r) + '\n' + u + ']'
                      : '[' + d.join(',') + ']'),
                  (r = u),
                  a
                );
              }
              for (s in f)
                Object.prototype.hasOwnProperty.call(f, s) &&
                  (a = n(s, f)) &&
                  d.push(e(s) + (r ? ': ' : ':') + a);
              return (
                (a =
                  0 === d.length
                    ? '{}'
                    : r
                    ? '{' + d.join(',') + u + '}'
                    : '{' + d.join(',') + '}'),
                (r = u),
                a
              );
          }
        };
      return n('', {
        '': t
      });
    },
    JSONDecode: function(t) {
      var o,
        i,
        r,
        a = {
          '"': '"',
          '\\': '\\',
          '/': '/',
          b: '\b',
          f: '\f',
          n: '\n',
          r: '\r',
          t: '\t'
        },
        c = function(t) {
          throw {
            name: 'SyntaxError',
            message: t,
            at: i,
            text: o
          };
        },
        u = function(t) {
          return (
            t && t !== r && c("Expected '" + t + "' instead of '" + r + "'"),
            (r = o.charAt(i)),
            (i += 1),
            r
          );
        },
        d = function() {
          var t,
            e = '';
          for ('-' === r && ((e = '-'), u('-')); r >= '0' && r <= '9'; )
            (e += r), u();
          if ('.' === r) for (e += '.'; u() && r >= '0' && r <= '9'; ) e += r;
          if ('e' === r || 'E' === r)
            for (
              e += r, u(), ('-' !== r && '+' !== r) || ((e += r), u());
              r >= '0' && r <= '9';

            )
              (e += r), u();
          if (((t = +e), isFinite(t))) return t;
          c('Bad number');
        },
        f = function() {
          var t,
            e,
            n,
            i = '';
          if ('"' === r)
            for (; u(); ) {
              if ('"' === r) return u(), i;
              if ('\\' === r)
                if ((u(), 'u' === r)) {
                  for (
                    n = 0, e = 0;
                    e < 4 && ((t = parseInt(u(), 16)), isFinite(t));
                    e += 1
                  )
                    n = 16 * n + t;
                  i += String.fromCharCode(n);
                } else {
                  if ('string' != typeof a[r]) break;
                  i += a[r];
                }
              else i += r;
            }
          c('Bad string');
        },
        p = function() {
          for (; r && r <= ' '; ) u();
        },
        s = function() {
          switch ((p(), r)) {
            case '{':
              return (function() {
                var t,
                  e = {};
                if ('{' === r) {
                  if ((u('{'), p(), '}' === r)) return u('}'), e;
                  for (; r; ) {
                    if (
                      ((t = f()),
                      p(),
                      u(':'),
                      Object.hasOwnProperty.call(e, t) &&
                        c('Duplicate key "' + t + '"'),
                      (e[t] = s()),
                      p(),
                      '}' === r)
                    )
                      return u('}'), e;
                    u(','), p();
                  }
                }
                c('Bad object');
              })();
            case '[':
              return (function() {
                var t = [];
                if ('[' === r) {
                  if ((u('['), p(), ']' === r)) return u(']'), t;
                  for (; r; ) {
                    if ((t.push(s()), p(), ']' === r)) return u(']'), t;
                    u(','), p();
                  }
                }
                c('Bad array');
              })();
            case '"':
              return f();
            case '-':
              return d();
            default:
              return r >= '0' && r <= '9'
                ? d()
                : (function() {
                    switch (r) {
                      case 't':
                        return u('t'), u('r'), u('u'), u('e'), !0;
                      case 'f':
                        return u('f'), u('a'), u('l'), u('s'), u('e'), !1;
                      case 'n':
                        return u('n'), u('u'), u('l'), u('l'), null;
                    }
                    c("Unexpected '" + r + "'");
                  })();
          }
        };
      var e;
      return (
        (o = t), (i = 0), (r = ' '), (e = s()), p(), r && c('Syntax error'), e
      );
    },
    //获取域名
    getDomain: function(t) {
      var e = t.match(/\/\/\S*?\//);
      return e && e.length ? e[0].replace(/\//g, '') : '';
    },
    cookie: {
      get: function(name) {
        var arr,
          reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)');
        if ((arr = window.document.cookie.match(reg))) {
          return decodeURIComponent(arr[2]);
        } else {
          return null;
        }
      },
      //遍历
      /**
       * @method set
       * @parame name value要设置的键值
       * @parame hasExpires 设置expires
       * @parame hasDomain 设置domain
       * @parame hasSecure 设置secure
       **/
      set: function(name, value, hasExpires, hasDomain, hasSecure) {
        var domain = '',
          expires = '',
          secure = '';
        if (hasDomain) {
          var hn = window.document.location.hostname.match(
              /[a-z0-9][a-z0-9\-]+\.[a-z\.]{2,6}$/i
            ),
            u = hn ? hn[0] : '';
          domain = u ? '; domain=.' + u : '';
        }
        if (hasExpires) {
          var exp = new Date();
          exp.setTime(exp.getTime() + 24 * hasExpires * 60 * 60 * 1e3),
            (expires = '; expires=' + exp.toGMTString());
        }
        if (hasSecure) {
          secure = '; secure';
        }
        window.document.cookie =
          name +
          '=' +
          encodeURIComponent(value) +
          expires +
          '; path=/' +
          domain +
          secure;
      },
      remove: function(name) {
        var hn = window.document.location.hostname.match(
            /[a-z0-9][a-z0-9\-]+\.[a-z\.]{2,6}$/i
          ),
          n = hn ? hn[0] : '';
        cookie.set(name, '', -1, '.' + n);
      }
    },
    objToStr: function(obj) {
      let paramArr = Object.keys(obj).reduce((acc, cur) => {
        acc.push(cur + '=' + obj[cur]);
        return acc;
      }, []);
      return paramArr.join('&');
    },
    //ajax请求封装
    ajax: {
      get: function() {
        // XMLHttpRequest对象用于在后台与服务器交换数据
        var xhr = new XMLHttpRequest();
        xhr.open('GET', DEFAULT_CONFIG.api_device_host, false);
        xhr.onreadystatechange = function() {
          // readyState == 4说明请求已完成
          if (xhr.readyState == 4) {
            if (xhr.status == 200 || xhr.status == 304) {
              console.log(xhr.responseText);
            }
          }
        };
        xhr.send();
      },
      post: function(data) {
        var xhr = new XMLHttpRequest();
        xhr.open('POST', DEFAULT_CONFIG.api_device_host, false);
        // 添加http头，发送信息至服务器时内容编码类型
        xhr.setRequestHeader(
          'Content-Type',
          'application/x-www-form-urlencoded'
        );
        xhr.onreadystatechange = function() {
          if (xhr.readyState == 4) {
            if (xhr.status == 200 || xhr.status == 304) {
            }
          }
        };
        xhr.send(helper.objToStr(data));
      }
    },
    isValidLink: function(t) {
      var tagName =
        t && t.tagName && t.tagName.toLowerCase ? t.tagName.toLowerCase() : '';
      var attr = t && t.getAttribute ? t.getAttribute('href') : '';
      if ('a' === tagName && attr) {
        return !/^javascript:/.test(attr);
      }
    },
    //判断是否已经绑定click事件
    hasBindClick: function(t) {
      return !!(
        t.onclick ||
        (window.jQuery &&
          (window.jQuery._data || window.jQuery.data) &&
          helper.isFunction(window.jQuery._data || window.jQuery.data) &&
          (window.jQuery._data || window.jQuery.data)(t, 'events'))
      );
    },
    //判断是否可点击
    isClickAble: function(t, e, n) {
      if (!t || t.nodeName === '#text') {
        return {
          flag: false
        };
      }
      var r =
          t && t.tagName && t.tagName.toLowerCase
            ? t.tagName.toLowerCase()
            : '',
        o = t && t.getAttribute ? t.getAttribute('type') : '',
        s = this.isValidLink(t),
        a = {
          flag: false,
          target: t,
          isValidLink: s,
          form: null
        };
      switch (r) {
        case 'a':
          if (s) {
            a.flag = true;
            break;
          }
        case 'button':
          t.disabled || (a.flag = true);
          break;
        case 'input':
          if (/button|reset|submit/.test(o) && !t.disabled) {
            a.flag = true;
            if (o === 'submit') {
              a.form = t.form;
            }
          }
          break;
        case 'body':
          a.flag = false;
          break;
        default:
          if (this.hasBindClick(t) || (helper.isFunction(e) && e(t))) {
            a.flag = true;
          } else if (!n) {
            return this.isClickAble(t.parentNode);
          }
      }
      return a;
    },
    getTagName: function(t) {
      return t && t.tagName && t.tagName.toLowerCase
        ? t.tagName.toLowerCase()
        : '';
    },
    getAttr: function(t, e) {
      return t && t.getAttribute ? t.getAttribute(e) : '';
    },
    getTextContent: function(t) {
      return (
        (t &&
          (t.textContent ||
            t.innerText ||
            this.getAttr(t, 'type') ||
            t.value)) ||
        ''
      );
    },
    query: function() {
      var t = '',
        e = window.document,
        n = /:eq\(\d+\)/g;
      if (
        (this.isString(arguments[0])
          ? (t = arguments[0])
          : ((e = arguments[0] || e), (t = arguments[1])),
        '#text' === e.nodeName)
      )
        return [];
      if (n.test(t)) {
        for (
          var r = t.split(n), o = t.match(n), s = null, a = 0, c = r.length;
          a < c;
          a++
        ) {
          var u = r[a].replace(/^>/, ':scope>');
          if (!u) break;
          if (((s = this.query(s || e, u)), !(o.length > a))) return s;
          var d = o[a].match(/\d+/)[0];
          if (!(s && d < s.length)) return [];
          s = s[d];
        }
        return [s];
      }
      if (helper.isString(t)) {
        var f = t.match(/^[a-z|>|:]*[#.]\d/);
        if (f && f.length) {
          var p = (f[0] || '').length - 1;
          t = t.slice(0, p) + '\\00003' + t.slice(p);
        }
      }
      return e.querySelectorAll(t);
    },
    getSelector: function(t) {
      for (
        var e = this.getAttr(t, 'id'),
          n = this.getTagName(t),
          r = this.trim(this.getAttr(t, 'class')).split(/\s/),
          o = [],
          s = 0,
          a = r.length;
        s < a;
        s++
      )
        new RegExp('zhuge-interface-|zhuge-auto-track-').test(r[s]) ||
          o.push(r[s]);
      return (o = o.join('.')), e ? '#' + e : n + (o = o ? '.' + o : '');
    },
    getIndexInParent: function(t) {
      for (
        var e = t.parentNode,
          n = this.getSelector(t),
          i = this.query(e, n),
          o = 0,
          s = i.length;
        o < s;
        o++
      ) {
        if (i[o] === t) return o;
      }
      return 0;
    },
    getUniqueSelector: function(t, e) {
      var n = this.getSelector(t);
      return /#/.test(n) || 'body' === this.getTagName(t)
        ? (e && (n = n + '>' + e), n)
        : t.parentNode
        ? ((n += ':eq(' + this.getIndexInParent(t) + ')'),
          e && (n = n + '>' + e),
          this.getUniqueSelector(t.parentNode, n))
        : '';
    },
    truncate: function(t, e) {
      var n;
      'string' == typeof t
        ? (n = t.slice(0, e))
        : helper.isArray(t)
        ? ((n = []),
          helper.each(t, function(t) {
            n.push(helper.truncate(t, e));
          }))
        : helper.isObject(t)
        ? ((n = {}),
          helper.each(t, function(t, i) {
            n[i] = helper.truncate(t, e);
          }))
        : (n = t);
      return n;
    },
    //获取系统版本
    getOsInfo: function() {
      var userAgent = navigator.userAgent.toLowerCase();
      var name = 'Unknown';
      var version = 'Unknown';
      if (userAgent.indexOf('win') > -1) {
        name = 'Windows';
        if (userAgent.indexOf('windows nt 5.0') > -1) {
          version = 'Windows 2000';
        } else if (
          userAgent.indexOf('windows nt 5.1') > -1 ||
          userAgent.indexOf('windows nt 5.2') > -1
        ) {
          version = 'Windows XP';
        } else if (userAgent.indexOf('windows nt 6.0') > -1) {
          version = 'Windows Vista';
        } else if (
          userAgent.indexOf('windows nt 6.1') > -1 ||
          userAgent.indexOf('windows 7') > -1
        ) {
          version = 'Windows 7';
        } else if (
          userAgent.indexOf('windows nt 6.2') > -1 ||
          userAgent.indexOf('windows 8') > -1
        ) {
          version = 'Windows 8';
        } else if (userAgent.indexOf('windows nt 6.3') > -1) {
          version = 'Windows 8.1';
        } else if (
          userAgent.indexOf('windows nt 6.2') > -1 ||
          userAgent.indexOf('windows nt 10.0') > -1
        ) {
          version = 'Windows 10';
        } else {
          version = 'Unknown';
        }
      } else if (userAgent.indexOf('iphone') > -1) {
        name = 'Iphone';
      } else if (userAgent.indexOf('mac') > -1) {
        name = 'Mac';
      } else if (
        userAgent.indexOf('x11') > -1 ||
        userAgent.indexOf('unix') > -1 ||
        userAgent.indexOf('sunname') > -1 ||
        userAgent.indexOf('bsd') > -1
      ) {
        name = 'Unix';
      } else if (userAgent.indexOf('linux') > -1) {
        if (userAgent.indexOf('android') > -1) {
          name = 'Android';
        } else {
          name = 'Linux';
        }
      } else {
        name = 'Unknown';
      }
      var os = new Object();
      os.name = name;
      os.version = version;
      return os;
    },
    //获取浏览器版本
    getBrowerInfo: function() {
      var Browser =
        Browser ||
        (function(window) {
          var document = window.document,
            navigator = window.navigator,
            agent = navigator.userAgent.toLowerCase(),
            //IE8+支持.返回浏览器渲染当前文档所用的模式
            //IE6,IE7:undefined.IE8:8(兼容模式返回7).IE9:9(兼容模式返回7||8)
            //IE10:10(兼容模式7||8||9)
            IEMode = document.documentMode,
            //chorme
            chrome = window.chrome || false,
            System = {
              //user-agent
              agent: agent,
              //是否为IE
              isIE: /trident/.test(agent),
              //Gecko内核
              isGecko:
                agent.indexOf('gecko') > 0 && agent.indexOf('like gecko') < 0,
              //webkit内核
              isWebkit: agent.indexOf('webkit') > 0,
              //是否为标准模式
              isStrict: document.compatMode === 'CSS1Compat',
              //是否支持subtitle
              supportSubTitle: function() {
                return 'track' in document.createElement('track');
              },
              //是否支持scoped
              supportScope: function() {
                return 'scoped' in document.createElement('style');
              },

              //获取IE的版本号
              ieVersion: function() {
                var rMsie = /(msie\s|trident.*rv:)([\w.]+)/;
                var ma = window.navigator.userAgent.toLowerCase();
                var match = rMsie.exec(ma);
                try {
                  return match[2];
                } catch (e) {
                  //console.log("error");
                  return IEMode;
                }
              },
              //Opera版本号
              operaVersion: function() {
                try {
                  if (window.opera) {
                    return agent.match(/opera.([\d.]+)/)[1];
                  } else if (agent.indexOf('opr') > 0) {
                    return agent.match(/opr\/([\d.]+)/)[1];
                  }
                } catch (e) {
                  //console.log("error");
                  return 0;
                }
              }
            };

          try {
            //浏览器类型(IE、Opera、Chrome、Safari、Firefox)
            System.type = System.isIE
              ? 'IE'
              : window.opera || agent.indexOf('opr') > 0
              ? 'Opera'
              : agent.indexOf('chrome') > 0
              ? 'Chrome'
              : //safari也提供了专门的判定方式
              window.openDatabase
              ? 'Safari'
              : agent.indexOf('firefox') > 0
              ? 'Firefox'
              : 'unknow';

            //版本号
            System.version =
              System.type === 'IE'
                ? System.ieVersion()
                : System.type === 'Firefox'
                ? agent.match(/firefox\/([\d.]+)/)[1]
                : System.type === 'Chrome'
                ? agent.match(/chrome\/([\d.]+)/)[1]
                : System.type === 'Opera'
                ? System.operaVersion()
                : System.type === 'Safari'
                ? agent.match(/version\/([\d.]+)/)[1]
                : '0';

            //浏览器外壳
            System.shell = function() {
              if (agent.indexOf('edge') > 0) {
                System.version =
                  agent.match(/edge\/([\d.]+)/)[1] || System.version;
                return 'edge浏览器';
              }
              //遨游浏览器
              if (agent.indexOf('maxthon') > 0) {
                System.version =
                  agent.match(/maxthon\/([\d.]+)/)[1] || System.version;
                return '傲游浏览器';
              }
              //QQ浏览器
              if (agent.indexOf('qqbrowser') > 0) {
                System.version =
                  agent.match(/qqbrowser\/([\d.]+)/)[1] || System.version;
                return 'QQ浏览器';
              }

              //搜狗浏览器
              if (agent.indexOf('se 2.x') > 0) {
                return '搜狗浏览器';
              }

              //Chrome:也可以使用window.chrome && window.chrome.webstore判断
              if (chrome && System.type !== 'Opera') {
                var external = window.external,
                  clientInfo = window.clientInformation,
                  //客户端语言:zh-cn,zh.360下面会返回undefined
                  clientLanguage = clientInfo.languages;

                //猎豹浏览器:或者agent.indexOf("lbbrowser")>0
                if (external && 'LiebaoGetVersion' in external) {
                  return '猎豹浏览器';
                }
                //百度浏览器
                if (agent.indexOf('bidubrowser') > 0) {
                  System.version =
                    agent.match(/bidubrowser\/([\d.]+)/)[1] ||
                    agent.match(/chrome\/([\d.]+)/)[1];
                  return '百度浏览器';
                }
                //360极速浏览器和360安全浏览器
                if (
                  System.supportSubTitle() &&
                  typeof clientLanguage === 'undefined'
                ) {
                  //object.key()返回一个数组.包含可枚举属性和方法名称
                  var storeKeyLen = Object.keys(chrome.webstore).length,
                    v8Locale = 'v8Locale' in window;
                  return storeKeyLen > 1 ? '360极速浏览器' : '360安全浏览器';
                }
                return 'Chrome';
              }
              return System.type;
            };

            //浏览器名称(如果是壳浏览器,则返回壳名称)
            System.name = System.shell();
          } catch (e) {
            // console.log(e.message);
          }
          return {
            client: System
          };
        })(window);
      if (Browser.client.name == undefined || Browser.client.name == '') {
        Browser.client.name = 'Unknown';
        Browser.client.version = 'Unknown';
      } else if (Browser.client.version == undefined) {
        Browser.client.version = 'Unknown';
      }
      return Browser;
    }
  };

  /*配置类*/
  var DEFAULT_CONFIG = {
    api_host:
      'https://sg.api.test.ybm100.com/sg/collection.gif?method=web_event_srv.uploadmethod',
    api_host_bac:
      'https://sg.api.test.ybm100.com/sg/collection.gif?method=web_event_srv.uploadmethod',
    //设备信息上报url
    api_device_host: 'https://sg.api.test.ybm100.com/devinfo',
    debug: false,
    platform: 'js',
    inherit_user_data: true,
    track_link_timeout: 300,
    cookie_expire_days: 365,
    cookie_cross_subdomain: true,
    cookie_secure: false,
    session_interval_mins: 30,
    app_channel: 'js',
    app_version: '1.0',
    superProperty: '{}',
    autoTrack: false,
    autoScroll: false,
    isClickAble: null,
    singlePage: false,
    is_landing: ''
  };

  /*收集类*/
  function collect(t) {
    this.config = {};
    helper.extend(this.config, t);
    this.idle = 0;
    this.last_activity = new Date();
    this.is_landing = false;
    this.startTime = 0;
  }
  /**
   * 收集初始化
   * @parame t appkey
   * @parame e 配置项
   * @parame n 函数
   */
  collect.prototype.init = function(t, e, n) {
    this._key = t;
    if (helper.isObject(e)) {
      helper.extend(this.config, e); //合并DEFAULT_CONFIG和配置项
    }
    for (var i in this.config) {
      if (helper.isObject(this.config[i])) {
        this.config[i] = helper.JSONEncode(this.config[i]);
      }
    }
    this.initDid(); //获取设备id
    this.cookie = {
      name: 'web_' + this._key,
      props: {},
      config: helper.extend({}, e)
    };
    var sn = helper.cookie.get('_web');
    if (sn && this.config.inherit_user_data) {
      helper.cookie.set(
        'web_' + this._key,
        helper.JSONEncode(this.cookie.props),
        this.config.cookie_expire_days,
        this.config.cookie_cross_subdomain,
        this.config.cookie_secure
      );
      helper.cookie.remove('_web');
    } else {
      this.cookie.props.sid = 0;
      this.cookie.props.updated = 0;
      this.cookie.props.info = 0;
      this.cookie.props.superProperty = this.config.superProperty;
      this.cookie.props.sid = 0;
      helper.cookie.set(
        'web_' + this._key,
        helper.JSONEncode(this.cookie.props),
        this.config.cookie_expire_days,
        this.config.cookie_cross_subdomain,
        this.config.cookie_secure
      );
    }
    helper.extend(this.cookie.props, {
      superProperty: this.config.superProperty
    });
    helper.cookie.set(
      'web_' + this._key,
      helper.JSONEncode(this.cookie.props),
      this.config.cookie_expire_days,
      this.config.cookie_cross_subdomain,
      this.config.cookie_secure
    );

    var r = this.session();
    if (this.config.autoTrack) {
      this.is_landing = this.checkLanding(r);
      this.initAutoTrack();
      //是否监控scroll事件
      if (this.config.autoScroll) {
        this.initLanding();
      }
    }
    //上报设备信息
    const isDeviceUpload = helper.cookie.get('isDeviceUpload');
    //如果24小时之内已经上报过一次 就不在上报
    if (isDeviceUpload == 1) return;

    const deviceData = {
      ak: this._key,
      did: this.did.props.did,
      pl: this.config.platform,
      'sys-n': helper.getOsInfo().name, //系统型号
      'sys-v': helper.getOsInfo().version, //系统版本
      'brs-n': helper.getBrowerInfo().client.name, //浏览器型号
      'brs-v': helper.getBrowerInfo().client.version, //浏览器版本
      rs: window.screen.width + 'x' + window.screen.height, //分辨率
      lang: window.navigator.language,
      tz: 60000 * -new Date().getTimezoneOffset(),
      date: new Date().getTime() * 1
    };
    helper.ajax.post(deviceData);
    helper.cookie.set(
      'isDeviceUpload',
      1,
      1,
      this.config.cookie_cross_subdomain,
      this.config.cookie_secure
    );
  };
  //获取设备id
  collect.prototype.initDid = function(t) {
    var e = helper.cookie.get('_web');
    t = t || helper.uuid();
    this.did = t;
    if (e && helper.JSONDecode(e).uuid) {
      helper.cookie.get('web_did') || helper.cookie.remove('web_' + this._key);
    }
    this.did = {
      name: 'web_did',
      props: {},
      config: helper.extend({}, this.config)
    };
    helper.extend(this.did.props, {
      did: t
    });
    helper.cookie.set(
      'web_did',
      helper.JSONEncode(this.did.props),
      this.config.cookie_expire_days,
      this.config.cookie_cross_subdomain,
      this.config.cookie_secure
    );
  };
  //判断是否是着陆页
  collect.prototype.checkLanding = function(t) {
    if (this.config.autoTrack) {
      var e = false,
        n =
          helper.getDomain(location.href) !==
          helper.getDomain(document.referrer);
      return (
        t
          ? n &&
            ((e = true),
            helper.extend(this.cookie.props, {
              landHref: location.href
            }))
          : n && location.href === this.cookie.props.landHref && (e = true),
        e
      );
    }
    // if (this.config.autoTrack) {
    //     var e = false,'
    //         hf = location.href?helper.getDomain(location.href):'',
    //         rf = document.referrer?helper.getDomain(document.referrer):'',
    //         n = hf !== rf;
    //     if(t){
    //         if(n){
    //             e = true;
    //             helper.extend(this.cookie.props,{
    //                 landHref: location.href
    //             })
    //             helper.cookie.set("web_" +this._key, helper.JSONEncode(this.cookie.props), this.config.cookie_expire_days, this.config.cookie_cross_subdomain, this.config.cookie_secure)
    //         }
    //     }else{
    //         if(n && location.href === this.cookie.props.landHref){
    //             e = true;
    //         }
    //     }
    //     return e;
    // }
  };
  //scroll事件
  collect.prototype.initLanding = function() {
    var a = {
      body: document.getElementsByTagName('body')[0],
      ready: function(t, e) {
        if (this.body) {
          t.call(e);
        } else {
          var n = this,
            i = setInterval(function() {
              window.document.body &&
                ((n.body = window.document.body), clearInterval(i), t.call(e));
            }, 50);
        }
      },
      bind: function(t, e, n, r) {
        r = r || t;
        var o = function(e) {
          if (
            ((window.target = e.target || e.srcElement),
            helper.isFunction(n)) &&
            !1 === n.call(r, e || window.event, t)
          )
            return (
              e.preventDefault && e.preventDefault(),
              e.stopPropagation && e.stopPropagation(),
              e.returnValue && (e.returnValue = false),
              e.cancelBubble && (e.cancelBubble = true),
              false
            );
        };
        if (t.addEventListener) {
          t.addEventListener(e, o, true);
        } else if (t.attachEvent) {
          t.attachEvent('on' + e, o);
        } else {
          var s = 'on' + e,
            a = t[s];
          t[s] = function(e) {
            return (
              helper.isFunction(a) && a.call(t, e || window.event),
              n.call(r, e || window.event, t)
            );
          };
        }
        return {
          unbind: function() {
            t.removeEventListener
              ? t.removeEventListener(e, o, true)
              : t.detachEvent && t.detachEvent('on' + e, o);
          }
        };
      }
    };
    var that = this,
      f,
      e = function(e) {
        if (that.config.autoTrack) {
          f && clearTimeout(f);
          f = setTimeout(function() {
            that.batchTrack([
              {
                dt: 'abp',
                eid: 'scroll',
                cuid: that.cookie.props.cuid ? that.cookie.props.cuid : '0',
                param: {
                  $page_url: window.location.href,
                  $scroll: Math.floor(
                    window.innerHeight + (window.scrollY || window.pageYOffset)
                  )
                }
              }
            ]);
          }, 50);
        }
      };
    a.ready(function() {
      a.bind(window, 'scroll', e);
    }, this);
  };
  //click pv事件
  collect.prototype.initAutoTrack = function() {
    var that = this;
    var s = [];
    var a = {
      body: document.getElementsByTagName('body')[0],
      ready: function(t, e) {
        if (this.body) {
          t.call(e);
        } else {
          var n = this,
            i = setInterval(function() {
              window.document.body &&
                ((n.body = window.document.body), clearInterval(i), t.call(e));
            }, 50);
        }
      },
      bind: function(t, e, n, r) {
        r = r || t;
        var o = function(e) {
          if (
            ((window.target = e.target || e.srcElement),
            helper.isFunction(n)) &&
            !1 === n.call(r, e || window.event, t)
          )
            return (
              e.preventDefault && e.preventDefault(),
              e.stopPropagation && e.stopPropagation(),
              e.returnValue && (e.returnValue = false),
              e.cancelBubble && (e.cancelBubble = true),
              false
            );
        };
        if (t.addEventListener) {
          t.addEventListener(e, o, true);
        } else if (t.attachEvent) {
          t.attachEvent('on' + e, o);
        } else {
          var s = 'on' + e,
            a = t[s];
          t[s] = function(e) {
            return (
              helper.isFunction(a) && a.call(t, e || window.event),
              n.call(r, e || window.event, t)
            );
          };
        }
        return {
          unbind: function() {
            t.removeEventListener
              ? t.removeEventListener(e, o, true)
              : t.detachEvent && t.detachEvent('on' + e, o);
          }
        };
      },
      _init: function() {
        that.config.singlePage && this._initHistoryHook(), this._onView();
        var t = this;
        this.ready(function() {
          t._initEventBind();
        }, this);
      },
      _initEventBind: function() {
        if (that.config.singlePage) {
          this.bind(window, 'popstate', this._onView, this);
          this.bind(window, 'hashchange', this._onView, this);
        }
        //监听页面关闭事件
        // if ('onpagehide' in window) {
        //     this.bind(window, "pagehide", this._onPageLeave, this);
        // } else {
        //     this.bind(window, "unload", this._onPageLeave, this);
        // }
      },
      _initHistoryHook: function() {
        var t = window.history,
          e = t.pushState;
        t.pushState = function(i) {
          var r = e.apply(t, arguments);
          return a._onView(), r;
        };
      },
      _onPageLeave: function() {
        var temp = JSON.parse(window.sessionStorage.getItem('lastParam'));
        if (temp) {
          var now = new Date().getTime();
          var duration = now - temp.time;
          //页面停留时间
          this.batchTrack([
            {
              dt: 'abp',
              eid: 'pageStayTime',
              cuid: that.cookie.props.cuid ? that.cookie.props.cuid : '0',
              param: {
                $page_url: temp.page_url,
                $page_stay_time: duration
              }
            }
          ]);
        }
      },
      _onView: function() {
        var param = {
          $page_url: window.location.href
        };
        var userId;
        var info = helper.cookie.get('web_info');
        if (info) {
          userId = helper.JSONDecode(info).cuid;
        }
        that.batchTrack([
          {
            dt: 'abp',
            eid: 'pv',
            cuid: userId ? userId : '0',
            param: param
          }
        ]);

        // var lastParam = {
        //     page_url: window.location.href,
        //     time: new Date().getTime()
        // }
        // var temp = JSON.parse(window.sessionStorage.getItem('lastParam'))
        // if(temp){
        //     var now = new Date().getTime();
        //     var duration = now - temp.time;
        //     console.log(`停留时间：${duration} 毫秒`)
        //     window.sessionStorage.setItem('lastParam', JSON.stringify(lastParam))
        //     var _param = Object.assign({
        //         $page_url: temp.page_url,
        //         $page_stay_time: duration
        //     }, s[0])
        //     //页面停留时间
        //     that.batchTrack([{
        //         dt: "abp",
        //         eid: "pageStayTime",
        //         cuid: that.cookie.props.cuid ? that.cookie.props.cuid:"0",
        //         param: _param
        //     }])
        // }else{
        //     window.sessionStorage.setItem('lastParam', JSON.stringify(lastParam))
        // }
      },
      getEvent: function(t) {
        var e = helper.getUniqueSelector(t.target);
        var userId;
        var info = helper.cookie.get('web_info');
        if (info) {
          userId = helper.JSONDecode(info).cuid;
        }
        if (e) {
          var n = t.target,
            i = {
              $page_url: window.location.href,
              $element_id: helper.getAttr(n, 'id'),
              $element_content: helper.getTextContent(n),
              $element_type: helper.getTagName(n),
              $element_style: helper.getAttr(n, 'class'),
              $element_selector: e,
              $element_link: helper.isValidLink
                ? helper.getAttr(n, 'href')
                : null
            };
          return {
            dt: 'abp',
            eid: 'click',
            cuid: userId ? userId : '0',
            param: i
          };
        }
      }
    };
    if (this.config.autoTrack) {
      a._init();
    }
    var t = true;

    var u = function(r) {
      if (t) {
        var o = helper.isClickAble(r.target, that.config.isClickAble);
        if (o.flag) {
          s = [];
          if (that.config.autoTrack && !helper.hasMobileSdk().flag) {
            s.push(a.getEvent(o));
          }
          s.length && that.batchTrack(s);
        }
      }
    };
    a.ready(function() {
      a.bind(a.body, 'click', u);
    }, this);
  };
  //获取用户浏览(弃用)
  collect.prototype.session = function(t) {
    var e = !1,
      n = this.cookie.props.updated,
      r = this.cookie.props.sid,
      o = 1 * new Date(),
      s = new Date();
    if (0 == r || o > n + 60 * this.config.session_interval_mins * 1e3) {
      (r = t || o), (r *= 1);

      var d = helper.getDomain(document.referrer);
      helper.extend(this.cookie.props, {
        referrerDomain: d
      });
      helper.extend(this.cookie.props, {
        sid: r
      });
      e = true;
    }
    return (
      helper.extend(this.cookie.props, {
        updated: o
      }),
      e
    );
  };
  //收集消息并上报
  collect.prototype._batchTrack = function(t, e) {
    if (!helper.hasMobileSdk().flag) {
      var obj = {},
        r = new Date();
      obj.pl = this.config.platform;
      obj.sdk = 'sg-js';
      obj.sdkv = '2.1';
      obj.dbg = this.config.debug ? 1 : 0;
      obj.ak = this._key;
      obj.tz = 60000 * -r.getTimezoneOffset();
      obj.did = this.did.props.did;
      obj.url = location.href;
      obj.pg_ttl = window.document.title;
      obj.pg_nm = '';
      obj.uuid = '';
      obj.submod = '';
      obj.e_seq_id = '';
      obj.poi_id = '';
      obj.channel = '';
      obj.longitude = '';
      obj.latitude = '';
      obj = helper.extend(obj, t);
      /*废弃
          // obj.cuid = this.cookie.props.cuid ? this.cookie.props.cuid : "0";
          // obj.ref = document.referrer;
          // obj.ref_d = document.referrer?helper.getDomain(document.referrer):'';
          // obj.ss_id = this.cookie.props.sid;
          // obj.cuid = this.cookie.props.cuid;
          // obj.ect = r.getTime();*/
      var o = [];
      o.push(obj);
      this.sendTrackRequest(o, this.prepareCallback(e, o));
    }
  };
  //回调
  collect.prototype.prepareCallback = function(t, e) {
    if (!helper.isFunction(t)) return null;
    return function(n) {
      t(n, e);
    };
  };
  collect.prototype.sendTrackRequest = function(t, e) {
    var n = helper.truncate(t, 255);
    n = helper.JSONEncode(n);
    n = encodeURIComponent(n.toString());

    // var params = {
    //     pl: this.config.platform,
    //     sdk: "sg-js",
    //     sdkv: "2.1",
    //     dbg: this.config.debug ? 1 : 0,
    //     ak: this._key,
    //     tz: 60000 * -new Date().getTimezoneOffset(),
    //     did: this.did.props.did
    // }

    // var o = {
    //     bac: this.config.api_host_bac + "&event=" + n + "&upt=" + (new Date).getTime().toString() + "&pl=" + params.pl + "&sdk=" + params.sdk + "&sdkv=" + params.sdkv + "&dbg=" + params.dbg + "&ak=" + params.ak + "&tz=" + params.tz + "&did=" + params.did,
    //     normal: this.config.api_host + "&e vent=" + n + "&upt=" + (new Date).getTime().toString() + "&pl=" + params.pl + "&sdk=" + params.sdk + "&sdkv=" + params.sdkv + "&dbg=" + params.dbg + "&ak=" + params.ak + "&tz=" + params.tz + "&did=" + params.did,
    // };
    var o = {
      bac:
        this.config.api_host_bac +
        '&event=' +
        n +
        '&upt=' +
        new Date().getTime().toString(),
      normal:
        this.config.api_host +
        '&event=' +
        n +
        '&upt=' +
        new Date().getTime().toString()
    };
    this.sendRequest(o, e);
  };
  //上报
  collect.prototype.sendRequest = function(t, e) {
    var n = new Image(),
      i = false,
      r = setTimeout(function() {
        !i && e && (e(), (i = !0));
      }, 500),
      o = function() {
        !i && e && (clearTimeout(r), e());
      };
    (n.onload = o),
      (n.onerror = function() {
        var e = new Image();
        (e.onload = o), (e.onerror = o), (e.src = t.bac);
      }),
      (n.src = t.normal);
  };
  /**
   * 定义track API
   * @parame t 事件名称
   * @parame e 事件属性
   * @parame n 回调函数
   */
  collect.prototype.track = function(t, e, n) {
    var r = helper.isObject(e) ? e : {},
      o = helper.isObject(e) ? n : e,
      s = helper.hasMobileSdk();
    if (s.flag) {
      s.track(t, r);
      if (helper.isFunction(o)) {
        o();
      }
    } else {
      this.batchTrack(
        [
          {
            dt: 'evt',
            eid: t,
            cuid: this.cookie.props.cuid ? this.cookie.props.cuid : '0',
            param: r
          }
        ],
        o
      );
    }
  };
  //开始统计事件时长
  collect.prototype.startTrack = function() {
    this.startTime = new Date().getTime();
  };
  //结束统计事件时长
  collect.prototype.endTrack = function(t, e, n) {
    if (this.startTime == 0) return;
    var durationTime = new Date().getTime() - this.startTime;
    console.log(`事件时长：${durationTime} 毫秒`);

    var r = helper.isObject(e) ? e : {},
      o = helper.isObject(e) ? n : e,
      s = helper.hasMobileSdk();
    if (s.flag) {
      s.track(t, r);
      //清空开始时长
      this.startTime = 0;
      if (helper.isFunction(o)) {
        o();
      }
    } else {
      this.batchTrack(
        [
          {
            dt: 'sdk_duration',
            eid: t,
            cuid: this.cookie.props.cuid ? this.cookie.props.cuid : '0',
            attr1: durationTime,
            param: r
          }
        ],
        o
      );
      //清空开始时长
      this.startTime = 0;
    }
  };

  //用户身份识别
  collect.prototype.identify = function(t, e, n) {
    t += '';
    var r = helper.isObject(e) ? e : {},
      o = helper.isObject(e) ? n : e;
    helper.extend(this.cookie.props, {
      cuid: t
    });
    helper.cookie.set(
      'web_info',
      helper.JSONEncode(this.cookie.props),
      this.config.cookie_expire_days,
      this.config.cookie_cross_subdomain,
      this.config.cookie_secure
    );
    this.session();
    var s = helper.hasMobileSdk();
    if (s.flag) {
      s.identify(t, r);
      if (helper.isFunction(o)) {
        o();
      }
    } else {
      if (helper.isFunction(o)) {
        o();
      }
    }
  };
  //全局添加属性
  collect.prototype.setSuperProperty = function(t) {
    if (helper.isObject(t)) {
      helper.extend(this.cookie.props, {
        superProperty: helper.JSONEncode(t)
      });
      helper.cookie.set(
        'web_' + this._key,
        helper.JSONEncode(this.cookie.props),
        this.config.cookie_expire_days,
        this.config.cookie_cross_subdomain,
        this.config.cookie_secure
      );
    }
  };
  //自定义事件 click scroll pv消息处理
  collect.prototype.batchTrack = function(t, e) {
    this.session();
    var r = new Date(),
      s = document.URL;
    if (this.is_landing && this.cookie.props.landHref) {
      s = this.cookie.props.landHref;
    }
    for (var a = 0, c = t.length; a < c; a++) {
      var u = t[a];
      if (u && u.dt) {
        var d = {
          at: u.eid,
          cuid: u.cuid,
          data: {}
        };
        if (u.attr1) {
          d.attr1 = u.attr1;
        }
        d.ect = r.getTime();
        d.tz = 60000 * -r.getTimezoneOffset();
        // d.cuid = this.cookie.props.cuid?this.cookie.props.cuid:'';
        d.ss_id = this.cookie.props.sid;
        d.url = s;
        d.ref = document.referrer;
        d.ref_d = this.cookie.props.referrerDomain
          ? this.cookie.props.referrerDomain
          : '';
        if ('evt' === u.dt) {
          d.data = helper.extend(d.data, helper.encode(u.param));
        } else {
          d.data = helper.extend(d.data, u.param);
        }
        d.data = helper.extend(
          d.data,
          helper.encode(JSON.parse(this.cookie.props.superProperty))
        );
        console.log(d);
        this._batchTrack(d, e);
      }
    }
  };

  //初始化,获取appkey及配置项，并调用collect类init方法
  window._init = function() {
    for (
      var arr = window.webSdk || [], con = new collect(DEFAULT_CONFIG), i = 0;
      i < arr.length;
      i++
    ) {
      if (arr[i][0] === 'init') {
        var c = arr.shift(),
          u = c.shift();
        con[u] && con[u].apply(con, c);
        break;
      }
    }
    window.webSdk = con;
  };
  _init();
})(window);
