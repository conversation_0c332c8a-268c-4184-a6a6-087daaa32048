<template>
  <div class="regionalMenu">
    <xyy-list-page>
      <template slot="header">
        <span style="font-size: 18px">微信访客导航菜单</span>
        <el-popover
          placement="bottom-start"
          width="300"
          trigger="hover"
          content="为了微信渠道精准分流，提高坐席服务效率，导航菜单支持菜单名自定义、拖拽排序、多服务组，优先级支持自定义。"
        >
          <span slot="reference">
            <img src="../../assets/configuration/iconhello.png" style="width: 14px;height: 14px" />
          </span>
        </el-popover>
      </template>
      <template slot="body">
        <el-form ref="form" :model="form" label-width="60px">
          <el-form-item class="shezhiyin">
            <p class="line">引导语设置</p>
          </el-form-item>
          <el-form-item label="引导语" class="yindaoyu">
            <el-input
              type="textarea"
              placeholder="请输入内容"
              v-model="form.guide"
              maxlength="100"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item class="shezhiyin">
            <p class="line">
              导航设置
              <span>员工组接入时，靠前的员工组优先级更高</span>
            </p>
          </el-form-item>
          <el-form-item class="NavigationSettings">
            <draggable
              element="ul"
              v-model="form.menus"
              :options="options"
              @update="datadragEnd"
              dragable="true"
            >
              <transition-group>
                <el-row
                  v-for="(item,index) in form.menus"
                  :key="index"
                  :class="classAcross == index ? 'greenClore':'greenCloreL'"
                  @mouseover.native="enter(index)"
                  @mouseout.native="leave(index)"
                  type="flex"
                  justify="space-between"
                >
                  <el-col :span="1">
                    <span class="circular">{{ index+1 }}</span>
                  </el-col>
                  <el-col :span="7">
                    <el-input v-model="item.menu" maxlength="20" placeholder="请输入菜单名称"></el-input>
                  </el-col>
                  <el-col :span="15">
                    <el-select v-model="item.groups" multiple placeholder="请选择">
                      <el-option
                        v-for="item in employeeGroups"
                        :key="item.id"
                        :label="item.groupName"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="1" class="removeRight">
                    <span @click="handerRemove(item,index)" v-if="showbtn == index ? true:false">
                      <img src="../../assets/configuration/remove.png" width="14" height="14" />
                    </span>
                  </el-col>
                </el-row>
              </transition-group>
            </draggable>
            <el-row type="flex" justify="space-between">
              <el-col :span="1"></el-col>
              <el-col :span="7">
                <div v-if="addShow">
                  <span class="el-icon-plus" @click="addMenu">添加菜单</span>
                </div>
              </el-col>
              <el-col :span="15"></el-col>
              <el-col :span="1"></el-col>
            </el-row>
          </el-form-item>
          <el-form-item class="NavigationSettings">
            <el-row type="flex" justify="space-between">
              <el-col :span="1"></el-col>
              <el-col :span="7">
                <xyy-button @click="handerSave">保存</xyy-button>
              </el-col>
              <el-col :span="15"></el-col>
              <el-col :span="1"></el-col>
            </el-row>
          </el-form-item>
        </el-form>
      </template>
    </xyy-list-page>
  </div>
</template>

<script>
import XyyListPage from '../../components/xyy/xyy-list-page/index';
import {
  regionalMenuTo,
  EmployeeGroups,
  getRegGroup,
  saveMenu,
} from '@/api/configuration/RegionalMenu';
import XyyButton from '../../components/xyy/xyy-button/index';
import draggable from 'vuedraggable';
export default {
  name: 'compRegionalMenu',
  components: { XyyButton, XyyListPage, draggable },
  data() {
    return {
      form: {
        guide: '',
      },
      addShow: true, // 控制添加菜单显示隐藏
      employeeGroups: [],
      options: {
        animation: 150,
        handle: '.el-row',
        filter: '',
      },
      showbtn: -1, // 删除按钮出现消失事件
      classAcross: -1, // 鼠标划伤背景
    };
  },
  created() {
    this.AccessInformation();
    this.getGroups();
  },
  methods: {
    // 添加菜单
    addMenu() {
      if (this.form.menus.length === 14) {
        this.addShow = false;
        this.$message({
          message: '最多添加15个导航菜单',
          customClass: 'messageTip',
        });
      }
      if (this.form.menus.length <= 14) {
        let index = this.form.menus.length + 1;
        this.form.menus.push({ menu: '', groups: '', sort: index });
      }
    },
    // 删除菜单;
    handerRemove(item, index) {
      if (index !== 0) {
        this.form.menus.splice(index, 1);
      }
      if (this.form.menus.length <= 15) {
        this.addShow = true;
      }
    },
    //
    datadragEnd(evt) {
      console.log('拖动前的索引 :' + evt.oldIndex);
      console.log('拖动后的索引 :' + evt.newIndex);
      let filters = this.form.menus;
      debugger;
      for (let a = 0; a < filters.length; a++) {
        filters[a].sort = a + 1;
      }
      debugger;
      this.form.menus = filters;
    },
    //  点击保存按钮事件
    handerSave() {
      // var paramsL = {}
      // var aaa = Object.assign(paramsL, th
      // 点击保存判断添加的导航设置是否为空
      let that = this;
      let len = 0;
      let leng = 0;
      this.form.menus.forEach((item, index) => {
        if (item.menu === '') {
          len++;
        }
        if (item.groups.length === 0) {
          leng++;
        }
      });
      if (len > 0) {
        this.$message('菜单名称不能为空');
        return false;
      }
      if (leng > 0) {
        this.$message('员工组不能为空');
        return false;
      }
      if (len <= 0 && leng <= 0) {
        var paramsL = {
          guide: String(this.form.guide),
          menus: this.form.menus,
        };
        saveMenu(paramsL)
          .then((response) => {
            //  保存成功出现的弹框
            if (response.code === 1) {
              this.$message('保存成功');
            } else {
              this.$message(response.msg);
            }
          })
          .catch(function (error) {
            console.log(error);
          });
      }
    },
    // 鼠标移入事件
    enter(index) {
      this.classAcross = index;
      if (index !== 0) {
        this.showbtn = index;
      }
    },
    // 鼠标移出事件
    leave(index) {
      this.classAcross = -1;
      this.showbtn = -1;
    },
    // 获取区域菜单所有数据
    AccessInformation() {
      var params;
      regionalMenuTo(params)
        .then((response) => {
          this.form = response.data;
          if (this.form.menus.length === 15) {
            this.addShow = false;
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    //  获取员工组数据
    getGroups() {
      var paramsL;
      getRegGroup(paramsL)
        .then((response) => {
          this.employeeGroups = response.data;
        })
        .catch(function (error) {
          console.log(error);
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.regionalMenu {
  /deep/ .el-form-item {
    margin-bottom: 16px !important;
    ul {
      margin-top: 0;
    }
  }
  /deep/.el-form-item.NavigationSettings {
    margin-bottom: 0 !important;
  }
  .yindaoyu {
    /deep/.el-textarea {
      width: 96.4%;
    }
  }
  .shezhiyin {
    /deep/.el-form-item__content {
      margin-left: 0 !important;
    }
  }
}
</style>
<style lang="scss">
.messageTip {
  background: rgba(255, 241, 240, 1);
  border: 1px solid rgba(255, 163, 158, 1);
  /deep/.el-message__content {
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/.el-icon-info {
    &:before {
      color: rgba(255, 48, 36, 1);
    }
  }
}
.regionalMenu {
  ul {
    padding-left: 0 !important;
    cursor: move;
  }
  .NavigationSettings {
    .el-form-item__content {
      margin-left: 0px !important;
    }
  }
  .el-icon-plus {
    cursor: pointer;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(59, 149, 168, 1);
  }
  .el-form-item__label {
    text-align: left;
  }
  .el-form {
    width: 85%;
    .el-textarea__inner {
      height: 80px;
    }
    .el-col-1 {
      /*width: 3.16667%;*/
      width: 30px;
    }
    .el-col-7 {
      padding-right: 1%;
    }
    .el-select {
      width: 100%;
    }
    .greenClore {
      background: rgba(218, 233, 236, 1);
    }
    .el-row {
      margin-bottom: 10px;
      padding-left: 1%;
      padding-top: 7px;
      padding-bottom: 7px;
      .el-col-16 {
        width: 64.66667%;
      }
      .el-col-8 {
        padding-right: 12px;
      }
    }
    .line {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(41, 41, 51, 1);
      margin-top: 0;
      margin-bottom: 0;
      span {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(174, 174, 191, 1);
        margin-left: 8px;
      }
    }
  }
  .circular {
    width: 20px;
    height: 20px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    border: 1px solid rgba(228, 228, 235, 1);
    display: inline-block;
    text-align: center;
    line-height: 20px;
    background: rgba(228, 228, 235, 1);
    color: rgba(144, 147, 153, 1);
  }
}
</style>
