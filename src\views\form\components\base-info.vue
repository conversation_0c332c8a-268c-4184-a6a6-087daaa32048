<template>
  <div class="container">
    <el-form ref="form"
             :model="form"
             :rules="rules"
             :disabled="readOnly">
      <el-form-item label="表单应用名称"
                    label-width="120px"
                    prop="name">
        <el-input v-model.trim="form.name"
                  maxlength="20"
                  placeholder="请输入" />
      </el-form-item>
      <el-form-item label="选择表单类型"
                    label-width="120px"
                    prop="formTypeId">
        <el-select v-model="form.formTypeId"
                   placeholder="请选择">
          <el-option v-for="item in options"
                     :key="item.id"
                     :label="item.name"
                     :value="item.id" />
        </el-select>
        <description :info="info"
                     @callback="toType"></description>
      </el-form-item>
      <el-form-item label="关联地区"
                    label-width="120px">
        <el-select v-model="form.areaCode"
                   filterable
                   placeholder="请选择关联地区"
                   @change="areaChange">
          <el-option v-for="item in areaData"
                     :key="item.areaCode"
                     :label="item.areaName"
                     :value="item.areaCode" />
        </el-select>
      </el-form-item>
      <el-form-item label="说明"
                    label-width="120px"
                    prop="description">
        <el-input v-model="form.description"
                  type="textarea"
                  maxlength="200"
                  rows="5"
                  placeholder="请输入" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getAllFormTypeList } from '@/api/formManage';
import { provProvince } from '@/api/configuration/RegionalMenu';
import description from '@/components/tools/description.vue';
export default {
  name: 'BaseInfo',
  components: {
    description
  },
  props: {
    form: {
      type: Object,
      default: () => { }
    }
  },
  data () {
    return {
      info: [
        {
          title: '',
          info:
            '可选择启用状态的表单应用  如需新增，请点击<a>新建表单类型</a>跳转并新建分类。'
        }
      ],
      rules: {
        name: [
          { required: true, message: '请输入表单应用名称', trigger: 'blur' },
          { max: 20, message: ' 20字以内', trigger: 'blur' }
        ],
        formTypeId: [
          { required: true, message: '请选择表单类型', trigger: 'change' }
        ],
        description: [{ max: 200, message: ' 200字以内', trigger: 'blur' }]
      },
      options: [],
      areaData: [],// 关联区域列表
      readOnly: false // 只读状态
    };
  },
  created () {
    this.getAllFormTypeList(); // 获取表单类型
    this.getAllProvProvince(); // 获取关联区域列表
    this.readOnly = this.$route.query.readOnly
      ? Boolean(this.$route.query.readOnly)
      : false;
  },
  methods: {
    // 校验表单信息
    validateForm () {
      let res = true;
      this.$refs.form.validate(valid => (valid ? (res = true) : (res = false)));
      return res;
    },

    /** 
     * 获取表单类型
     */
    getAllFormTypeList () {
      getAllFormTypeList({ status: 1 }).then(res => {
        if (res.code === 1) {
          this.options = res.data;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    /** 
     * 获取关联区域列表
     */
    getAllProvProvince () {
      provProvince().then(res => {
        if (res.code === 1) {
          this.areaData = res.data;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    areaChange (e) {
      const area = this.areaData.find((item) => {
        return item.areaCode === e
      })
      if (area) {
        this.form.areaName = area.areaName
      }
    },
    toType () {
      this.$router.push({
        path: `/worksheet/typeList`
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.container {
  width: 600px;
  margin: 150px auto 60px;
  .el-input,
  .el-select,
  .el-textarea {
    width: calc(100% - 20px);
    /deep/.el-input__inner {
      height: 36px;
      line-height: 36px;
    }
  }

  .el-button {
    height: 36px;
    padding: 0 20px;
    line-height: 36px;
  }

  /deep/.el-form-item__label {
    font-weight: normal;
  }
  .el-form-item {
    margin-bottom: 20px;
  }
}
</style>
