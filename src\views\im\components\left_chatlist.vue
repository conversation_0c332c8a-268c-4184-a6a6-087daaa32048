<template>
  <div class="left-chatlist-container">
    <template v-if="innerChatList.length">
      <template v-for="item in innerChatList">
        <!-- 会话中列表，在线样式:chat-online，其他列表无此样式 -->
        <!-- 会话中列表，离线样式:chat-offline，其他列表无此样式 -->
        <!-- 选中样式:is-active -->
        <!-- tab==='0' && (item.assignChatStatus.toString()==='0'?'chat-online':'chat-offline'), -->
        <div
          :class="[
            {'chat-online':(tab==='0' && item.assignChatStatus===0)},
            {'chat-offline':(tab==='0' && item.assignChatStatus!==0)},
            {'is-active':item.assignChatItemSelected}
          ]"
          :key="item.id"
          class="chat-item"
          @click="onClickChatItem(item)"
        >
          <!-- dialogEndScene	会话关闭类型 0-未关闭，1-系统关闭,2-访客超时关闭,3-客户关闭,4-客服关闭,5-待跟进，6-抢接关闭,7-主动转接关闭 -->
          <div class="chat-item-left">
            <img
              :src="item.assignCustomHeadImg | customHeadImgFormatter"
              class="chat-item-left__headimg"
            />
            <span class="chat-item-left__badge"></span>
          </div>
          <div class="chat-item-right">
            <div class="chat-item-right__1">
              <span class="chat-item-right__1__name">{{ item.assignCustomName }}</span>
              <span
                class="chat-item-right__1__time"
              >{{ item.assignLastMessageTime | msgTimeFormatter }}</span>
            </div>

            <div class="chat-item-right__2">
              <!-- 会话列表、留言，才展示最后一条消息和未读数量 -->
              <template v-if="['0'].includes(tab)">
                <span
                  :style="{ width:`calc(100% - ${ item.assignUnreadMessageNum?14:0 }px)` }"
                  class="chat-item-right__2__lastmsg"
                >{{ item.assignLastMessage | lastMessageFormatter }}</span>
                <span
                  v-if="item.assignUnreadMessageNum"
                  class="chat-item-right__2__unreadnum"
                >{{ item.assignUnreadMessageNum }}</span>
              </template>
              <!-- 留言，未读数量显示成小红点 -->
              <template v-else-if="['3'].includes(tab)">
                <span
                  :style="{ width:`calc(100% - ${ item.assignUnreadMessageNum?8:0 }px)` }"
                  class="chat-item-right__2__lastmsg"
                >{{ item.assignLastMessage | lastMessageFormatter }}</span>
                <span v-if="item.assignUnreadMessageNum" class="chat-item-right__2__unreadnum__dot"></span>
              </template>
            </div>

            <div class="chat-item-right__3">
              <span
                class="chat-item-right__3__chatingstate"
                v-html="chatStatusFormatter(item.assignChatStatus)"
              ></span>
              <template v-if="item.assignCustomerUnReplyFromLeaveMessageTips || item.assignCountdown">
                <span class="chat-item-right__3__leavestate">
                  <template v-if="item.assignCustomerUnReplyFromLeaveMessageTips">[留言发起未回复]</template>
                  <template v-if="item.assignCountdown">[{{ item.assignCountdown }}]</template>
                </span>
              </template>
              <!-- 会话中列表会话中不展示移除按钮 -->
              <!-- 历史记录列表不展示移除按钮 -->
              <template
                v-if="((tab==='0' && item.assignChatStatus!==0) || (tab==='1') || (tab==='3'))"
              >
                <el-tooltip effect="light" content="移除" placement="top">
                  <i
                    class="el-icon-remove chat-item-right__3__remove"
                    @click.stop="onClickRemoveChat(item)"
                  ></i>
                </el-tooltip>
              </template>
            </div>
          </div>
        </div>
      </template>
    </template>
    <template v-else>
      <div class="empty-wrap">暂无数据</div>
    </template>
  </div>
</template>

<script>
import { dialogRemove } from '@/api/im_view/index';
export default {
  name: '',
  components: {},
  filters: {
    // 客户图像
    customHeadImgFormatter(customHeadImg) {
      return customHeadImg || require('../../../../static/user_kh.png');
    },

    // 格式化消息时间
    msgTimeFormatter(timeStr) {
      let retTimeStr = '';
      try {
        if (timeStr) {
          const tDate = new Date(timeStr);
          retTimeStr = `${(tDate.getMonth() + 1)
            .toString()
            .padStart(2, 0)}-${tDate
            .getDate()
            .toString()
            .padStart(2, 0)} ${tDate
            .getHours()
            .toString()
            .padStart(2, 0)}:${tDate
            .getMinutes()
            .toString()
            .padStart(2, 0)}`;
        } else {
          retTimeStr = '';
        }
      } catch (err) {
        retTimeStr = '';
      }
      return retTimeStr;
    },

    //最后一条消息,过滤掉html标签
    lastMessageFormatter(msg) {
      let retMsg = '';
      if (msg) {
        if (msg.includes('<img') && !msg.includes('msg-card-file')) {
          retMsg = '图片消息';
        } else if (msg.includes('<video')) {
          retMsg = '视频消息';
        } else if (msg.includes('msg-card-file')) {
          retMsg = '文件消息';
        } else {
          retMsg = msg
            .replace(/\s*/g, '')
            .replace(/<[^>]+>/g, '')
            .replace(/↵/g, '')
            .replace(/[\r\n]/g, '')
            .replace(/&nbsp;/g, ''); // 依次去掉空格、html标签、↵符号、回车换行
        }
      }
      return retMsg;
    }
  },
  props: {
    tab: {
      type: String,
      required: true,
      default: '0'
    }
  },
  data() {
    return {
      innerChatList: []
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 格式化会话状态
    chatStatusFormatter(chatstatus) {
      let statusStr = '';
      // 其他三个列表
      // dialogEndScene	会话关闭类型 0-未关闭，1-系统关闭,2-访客超时关闭,3-客户关闭,4-客服关闭,5-待跟进，6-抢接关闭,7-主动转接关闭
      switch (chatstatus) {
        case 0:
          statusStr = '<span style="color:#FFA631;">[会话中]</span>';
          break;
        case 1:
          statusStr = '<span>[系统24小时自动关闭]</span>';
          break;
        case 2:
          statusStr = '<span>[访客超时]</span>';
          break;
        case 3:
          statusStr = '<span>[访客关闭]</span>';
          break;
        case 4:
          statusStr = '<span>[客服关闭]</span>';
          break;
        case 5:
          statusStr = '<span>[转入待跟进]</span>';
          break;
        case 6:
          statusStr = '<span>[强制转接]</span>';
          break;
        case 7:
          statusStr = '<span>[被接受转接]</span>';
          break;
        case 8:
          statusStr = '<span>[客服超时]</span>';
          break;
        case 'custom0':
          statusStr = '<span>[访客留言]</span>';
          break;
        default:
          statusStr = '<span> </span>';
          break;
      }
      return statusStr;
    },

    // 点击某个会话
    onClickChatItem(chat) {
      if (!chat.assignChatItemSelected) {
        this.$emit('click', { tab: this.tab, chat });
      }
    },

    // 移除会话
    onClickRemoveChat(chat) {
      dialogRemove({
        dialogId: chat.id,
        dialogType: ['1', '3', '', '2'][this.tab] // 1-会话中 2-留言 3-待跟进
      }).then(resp => {
        if (resp.code === 1) {
          this.$emit('remove', { tab: this.tab, chat, by: 'user' }); // 手动移除
        } else {
          this.$XyyMessage.error(resp.msg);
        }
      });
    },

    //refs方式更新数据
    refUpdateChatList(chatlist) {
      this.innerChatList = Array.from(chatlist);
      this.$forceUpdate();
    }
  }
};
</script>

<style lang="scss" scoped>
.left-chatlist-container {
  .chat-item {
    width: 100%;
    // padding: 15px 5px 0;
    padding: 16px 16px 0;
    cursor: pointer;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    position: relative;

    &::after {
      content: '';
      width: 100%;
      height: 1px;
      background-color: #e4e4eb;
      position: absolute;
      bottom: 0;
      left: 16px;
    }

    .chat-item-left {
      position: relative;
      margin-right: 8px;
      font-size: 0;

      .chat-item-left__headimg {
        width: 36px;
        height: 36px;
        border-radius: 50%;
      }

      .chat-item-left__badge {
        display: none;
        box-sizing: border-box;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        position: absolute;
        right: 0;
        bottom: 0;
      }
    }

    //选中样式
    &.is-active {
      background-color: #ebf4f6;
    }

    //在线样式
    &.chat-online {
      .chat-item-left__badge {
        display: inline-block;
        background-color: #67c23a;
        border: 1px solid #fff;
      }

      &.is-active {
        background-color: #ebf4f6;
      }
    }

    //离线样式
    &.chat-offline {
      .chat-item-left__headimg {
        -webkit-filter: grayscale(1);
        /* Webkit */
        filter: gray;
        /* IE6-9 */
        filter: grayscale(1);
        /* W3C */
      }

      .chat-item-left__badge {
        display: inline-block;
        background-color: #d8d8d8;
        border: 1px solid #fff;
      }

      .chat-item-right__1__name {
        color: #909398 !important;
      }

      &.is-active {
        background-color: #ebf4f6;
      }
    }

    .chat-item-right {
      flex-grow: 1;
      width: calc(100% - 36px - 8px);

      .chat-item-right__1 {
        width: 100%;
        font-size: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .chat-item-right__1__name {
          width: 62%;
          font-size: 13px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          color: #292933;
          font-weight: 500;
        }

        .chat-item-right__1__time {
          width: 38%;
          font-size: 12px;
          text-align: right;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          color: #909399;
          font-weight: 400;
        }
      }

      .chat-item-right__2 {
        width: 100%;
        font-size: 0;
        margin: 8px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .chat-item-right__2__lastmsg {
          width: calc(100% - 14px);
          font-size: 12px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          color: #909398;
          font-weight: 400;
        }

        .chat-item-right__2__unreadnum {
          width: 14px;
          height: 14px;
          line-height: 14px;
          font-size: 12px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          color: #ffffff;
          text-align: center;
          background-color: #ff3024;
          border-radius: 50%;
          font-weight: 400;
        }

        .chat-item-right__2__unreadnum__dot {
          width: 8px;
          height: 8px;
          background-color: #ff3024;
          border-radius: 50%;
          margin-right: 4px;
        }
      }

      .chat-item-right__3 {
        width: 100%;
        line-height: 14px;
        font-size: 0;
        margin: 0 0 16px;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .chat-item-right__3__chatingstate {
          // width: calc(100% - 14px);
          flex-shrink: 0;
          font-size: 12px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          color: #909398;
        }

        .chat-item-right__3__leavestate {
          flex-shrink: 0;
          color: #ff3024;
          font-size: 12px;
        }

        .chat-item-right__3__remove {
          flex-shrink: 0;
          display: none;
          width: 14px;
          height: 14px;
          line-height: 14px;
          font-size: 14px;
          color: #afc6ca;
          cursor: pointer;

          &:hover {
            color: #3c95a8;
          }
        }
      }
    }

    &:hover {
      background-color: #ebf4f6;

      .chat-item-right__3__remove {
        display: inline-block !important;
      }
    }
  }

  .empty-wrap {
    width: 100%;
    line-height: 60px;
    text-align: center;
    font-size: 14px;
    color: #909399;
  }
}
</style>
