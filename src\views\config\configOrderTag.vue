<template>
  <div class="config-order-tag-component-container">
    <div class="title">
      <span>标签管理</span>
      <el-popover placement="bottom-start"
                  width="359"
                  popper-class="popoverAll"
                  trigger="hover">
        <p>
          此处标签用于工单列表中标记处理状态
          <br />
        </p>
        <p>
          标签限10字符
          <br />
        </p>
        <p>
          标签总数限50条以内
          <br />
        </p>
        <p>
          点击“生效”按钮后，标签生效，此时客服可在工单列表中选择已更新标签
          <br />
        </p>
        <p>如未配置任何标签，工单列表-操作菜单 中不展示标签列</p>
        <div slot="reference"
             style="cursor:pointer;">
          <svg-icon class="icon-info"
                    icon-class="info"></svg-icon>
        </div>
      </el-popover>
    </div>

    <div class="boxer">
      <div class="tag-list">
        <el-tag v-for="tag in tagList"
                :key="tag.id"
                closable
                hit
                disable-transitions
                @close="tagClose(tag.id)">{{tag.tagName}}</el-tag>
      </div>
      <div class="tag-add">
        <div class="tag-add-form"
             v-show="isShowInputForm">
          <el-form :model="searchForm"
                   :rules="searchRules"
                   ref="refSearchForm">
            <el-form-item prop="inputTag">
              <el-input size="medium"
                        v-model="searchForm.inputTag"
                        placeholder="请输入10汉字内标签"
                        autocomplete="off"></el-input>
              <el-button size="medium"
                         type="primary"
                         @click="tagSave">保存</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="tag-add-btn">
          <el-button size="medium"
                     type="text"
                     @click="showInputTag">
            <i class="el-icon-circle-plus add-icon"></i>
            <span>添加标签</span>
          </el-button>
        </div>
      </div>
    </div>

    <div class="bottom">
      <el-button size="medium"
                 type="primary"
                 @click="tagsEnable">生效</el-button>
      <el-button size="medium"
                 @click="tagsClear">清除</el-button>
      <el-button size="medium"
                 @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script>
import {
  tagsGet,
  tagsValidate,
  tagsSave,
  tagsStop,
  tagsEnable,
  tagsDelete
} from '@/api/tagConfig';
export default {
  name: 'configOrderTag',
  components: {},
  filters: {},
  props: {},
  data () {
    return {
      isShowInputForm: false,
      searchForm: {
        inputTag: ''
      },
      searchRules: {
        inputTag: [
          { required: true, message: '请输入标签', trigger: 'blur' },
          { max: 10, message: '您输入内容超长，限10字符！', trigger: 'blur' }
          //{ validator: this.repeatTagValid, trigger: 'blur' } //本地校验标签重复
        ]
      },
      tagList: []
    };
  },
  computed: {},
  watch: {},
  created () { },
  mounted () {
    this.getAllTags();
  },
  methods: {
    /**
     * 查询标签列表
     */
    getAllTags () {
      this.tagList = [];
      tagsGet({
        stopState: false //是否展示停用标签	true-不展示 false-展示
      })
        .then(resp => {
          if (resp.code === 1) {
            resp.data.forEach(item => {
              this.tagList.push({
                id: item.id,
                tagName: item.tagName
              });
            });
          }
        })
        .catch(err => { });
    },

    /**
     * 添加标签，呼出标签输入框
     */
    showInputTag () {
      if (this.tagList.length >= 50) {
        this.$XyyMsg({
          title: '提示',
          closeBtn: false,
          htmlString: true,
          content: `<span style="color:#ff3024;">可创建标签最多50条</span>`,
          onSuccess: () => { }
        });
      } else {
        this.$refs['refSearchForm'].resetFields();
        this.isShowInputForm = !this.isShowInputForm;
      }
    },

    /**
     *  本地校验标签重复
     */
    repeatTagValid (rule, value, callback) {
      //本地校验
      let hasRepeatTag = false;
      this.tagList.forEach(item => {
        if (item.tagName === value) {
          hasRepeatTag = true;
        }
      });
      if (thasRepeatTag) {
        callback(new Error('标签重复'));
      } else {
        callback();
      }
    },

    /**
     * 服务器校验重复
     */
    tagsValidateServer (tagName) {
      return new Promise((resolve, reject) => {
        tagsValidate({
          tagName
        }).then(resp => {
          if (resp.code === 1) {
            //不重复
            resolve(tagName);
          } else {
            //标签重复
            this.$XyyMessage.error(resp.msg);
          }
        });
      });
    },

    /**
     * 保存输入的标签
     */
    tagSave () {
      this.$refs['refSearchForm'].validate(valid => {
        if (valid) {
          this.tagsValidateServer(this.searchForm.inputTag).then(tagName => {
            //发请求存服务器
            tagsSave({
              tagName
            }).then(resp => {
              if (resp.code === 1) {
                this.tagList.push({
                  id: resp.data.id,
                  tagName: resp.data.tagName
                });
                this.$refs['refSearchForm'].resetFields();
                this.isShowInputForm = false;
              } else {
                this.$XyyMessage.error('保存失败，请重试');
              }
            });
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },

    /**
     * 停用标签
     */
    tagClose (id) {
      tagsDelete({
        idList: id
      }).then(resp => {
        if (resp.code === 1) {
          this.tagList.forEach((item, index) => {
            if (item.id === id) {
              this.tagList.splice(index, 1);
            }
          });
        } else {
          this.$XyyMessage.error(resp.msg);
        }
      });
    },

    /**
     * 生效
     */
    tagsEnable () {
      let idArr = []; //所有标签id
      this.tagList.forEach(item => {
        idArr.push(item.id);
      });

      if (!idArr.length) {
        this.$XyyMessage.error('请先添加标签');
        return false;
      }

      this.$XyyMsg({
        title: '提示',
        htmlString: true,
        content: `<span>确定生效当前 工单-标签 规则？</span>`,
        onSuccess: () => {
          tagsEnable({
            idList: idArr.join(',')
          }).then(resp => {
            if (resp.code === 1) {
              this.$XyyMessage.success('操作成功！');
            } else {
              this.$XyyMessage.error(resp.msg);
            }
          });
        }
      });
    },

    /**
     * 清除
     */
    tagsClear () {
      let idArr = []; //所有标签id
      this.tagList.forEach(item => {
        idArr.push(item.id);
      });

      if (!idArr.length) {
        this.$XyyMessage.error('请先添加标签');
        return false;
      }

      this.$XyyMsg({
        title: '提示',
        htmlString: true,
        content: `<div style="color:#ff3024;"><span>确定清除当前 工单-标签 规则？</span><br><br><span>您将删除全部已配置标签！</span></div>`,
        onSuccess: () => {
          tagsDelete({
            idList: idArr.join(',')
          }).then(resp => {
            if (resp.code === 1) {
              this.$XyyMessage.success('操作成功！');
              this.getAllTags();
            } else {
              this.$XyyMessage.error(resp.msg);
            }
          });
        }
      });
    },

    /**
     * 取消
     */
    cancel () {
      this.$store.dispatch('tagsView/delView', this.$route);
      this.$router.push({
        path: '/worksheet/configIndex'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.config-order-tag-component-container {
  width: 100%;
  padding: 20px;

  .title {
    width: 100%;
    padding: 10px 0;
    border-bottom: 1px dashed #e4e4eb;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;

    & > span {
      margin-right: 10px;
    }
  }

  .boxer {
    width: 100%;
    height: calc(100% - 75px);
    padding: 20px;
    overflow-y: auto;

    .tag-list {
      .el-tag {
        margin: 0 10px 10px 0;
      }
    }

    .tag-add {
      .tag-add-form {
        margin-top: 20px;

        .el-input {
          width: 250px;
        }
      }

      .tag-add-btn {
        .el-button /deep/ span {
          display: flex;
          align-items: center;

          .add-icon {
            font-size: 20px;
          }
        }
      }
    }
  }

  .bottom {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}
</style>