import request from '@/utils/request-im';

/**
 * 获取faq数据
 */
export function getFAQDatas() {
  return request({
    url: '/faqcustom/init_tree',
    method: 'get'
  });
}

/**
 * 保存分类数据
 * @param {表单数据} data
 */
export function saveClassifyData(data) {
  return request({
    url: '/faqcustom/saveOrEditType',
    method: 'post',
    data
  });
}
/**
 * 保存问题数据
 */
export function saveQuestionData(data) {
  return request({
    url: '/faqcustom/saveOrEditfaq',
    method: 'post',
    data
  });
}
/**
 * 修改排序
 */
export function updateSort(data) {
  return request({
    url: '/faqcustom/batchedit',
    method: 'post',
    data
  });
}

/**
 * 删除分类数据
 */
export function delClassifyData(data) {
  return request({
    url: `/faqcustom/deltype/${data.id}`,
    method: 'delete'
  });
}

/**
 * 删除问题数据
 */
export function delQuestionData(data) {
  return request({
    url: `/faqcustom/delete/${data.id}`,
    method: 'delete'
  });
}
