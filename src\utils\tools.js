export function compareFile(newDatas, oldDatas) {
  if (newDatas.length !== oldDatas.length) {
    return true;
  } else {
    return newDatas.length ? compareArray(newDatas, oldDatas, 'path') : false;
  }
}

function compareArray(arr1, arr2, key) {
  let diff = false;
  for (let i = 0; i < arr1.length; i++) {
    const val = arr1[i][key];
    let inside = false;
    for (let j = 0; j < arr2.length; j++) {
      if (val === arr2[j][key]) {
        inside = true;
        break;
      }
    }
    if (!inside) {
      diff = true;
      break;
    }
  }
  return diff;
}

export function getDateValue(opt) {
  const nowDate = new Date().getTime();
  const dateOpts = [
    nowDate,
    nowDate - 5 * 24 * 3600 * 1000,
    nowDate - 24 * 3600 * 1000,
    nowDate - 7 * 24 * 3600 * 1000,
    ''
  ];
  return dateOpts[opt];
}

export function initFileDatas(datas) {
  if (!datas) return;
  const arr = [];
  if (typeof datas === 'string') {
    datas = JSON.parse(datas);
  }
  // debugger;
  for (let i = 0; i < datas.length; i++) {
    let _data = '';
    if (typeof datas[i] === 'string') {
      _data = JSON.parse(datas[i].replace(/(&quot;)/g, '"'));
    } else {
      _data = datas[i];
    }

    let isNewData = false;
    if (_data.thumbImagePath) {
      isNewData = _data.thumbImagePath.indexOf('https') > -1 || _data.thumbImagePath.indexOf('http') > -1;
    }
    if (_data.path) {
      isNewData = _data.path.indexOf('https') > -1 || _data.path.indexOf('http') > -1;
    }
    const obj = _data.originalFilename
      ? {
          uid: new Date().getTime() + i,
        name: _data.originalFilename, // 文件名
        url: isNewData ? _data.thumbImagePath || _data.path : BASE_URL + (_data.thumbImagePath || _data.group + '/' + _data.path), // 文件缩略图地址 或 预览地址
        path: isNewData ? _data.path : BASE_URL + _data.group + '/' + _data.path, // 文件真实地址
        raw: {
          type: getMimeType(
              _data.originalFilename.substring(
                _data.originalFilename.lastIndexOf('.') + 1
              )
            ), // 文件mime-type
            size: getFileSize(_data.fileSize) // 文件大小
          },
          percent: 100,
          data: _data // 后台所需数据
        }
      : _data;
    arr.push(obj);
  }
  return arr;
}

function getMimeType(suffix) {
  let _type = '';
  switch (suffix.toLowerCase()) {
    case 'gif':
      _type = 'image/gif';
      break;
    case 'bmp':
      _type = 'image/bmp';
      break;
    case 'jpg':
      _type = 'image/jpeg';
      break;
    case 'jpeg':
      _type = 'image/jpeg';
      break;
    case 'png':
      _type = 'image/png';
      break;
    case 'pdf':
      _type = 'application/pdf';
      break;
    case 'zip':
      _type = 'application/zip';
      break;
    case 'rar':
      _type = 'application/x-rar-compressed';
      break;
    case 'txt':
      _type = 'text/plain';
      break;
    case 'doc':
      _type = 'application/msword';
      break;
    case 'docx':
      _type =
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      break;
    case 'ppt':
      _type = 'application/vnd.ms-powerpoint';
      break;
    case 'pptx':
      _type =
        'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      break;
    case 'xls':
      _type = 'application/vnd.ms-excel';
      break;
    case 'xlsx':
      _type =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      break;
    case 'mp4':
      _type = 'video/mp4';
      break;
    case 'avi':
      _type = 'video/avi';
      break;
  }
  return _type;
}

// const BASE_URL = process.env.NODE_ENV === 'production' ? 'http://upload.ybm100.com/' : 'http://upload.test.ybm100.com/';
export const BASE_URL =
  process.env.NODE_ENV === 'production'
    ? 'http://upload.ybm100.com/'
    : 'http://upload.test.ybm100.com/';

function getFileSize(size) {
  let _size = 0;
  if (size.indexOf('MB') >= 0) {
    _size = Number(size.split('MB')[0]) * 1024000;
  } else if (size.indexOf('KB') >= 0) {
    _size = Number(size.split('KB')[0]) * 1000;
  } else {
    _size = Number(size.split('B')[0]);
  }
  return _size;
}
/** {
  "field_code": "S1164456058157142016",
  "field_name": "客户名称",
  "field_text": "客户名称",
  "field_type": 0,
  "field_value": "王六"
},
{
  "field_code": "S1164456270271483904",
  "field_name": "优先级",
  "field_text": "优先级",
  "field_type": 3,
  "field_value": {
      "option_name": "紧急",
      "option_value": "0"
  }
},**/
// <!-- 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件 -->
export function adapter(oldArr) {
  // console.log('oldArr', oldArr);
  const newArr = [];
  oldArr.forEach(item => {
    if (
      item.field_type === 0 ||
      item.field_type === 1 ||
      item.field_type === 5 ||
      item.field_type === 7 ||
      item.field_type === 8 ||
      item.field_type === 9
    ) {
      newArr.push({
        fieldCode: item.field_code,
        fieldType: item.field_type,
        fieldText: item.field_text,
        fieldSingleValue: item.field_value,
        optionName: item.field_value,
        systemFieldFlag: item.system_field_flag,
        conditionList: item.conditionList,
        authTypeCondition: item.authTypeCondition
      });
    }
    if (item.field_type === 2 || item.field_type === 3) {
      newArr.push({
        fieldCode: item.field_code,
        fieldType: item.field_type,
        fieldText: item.field_text,
        fieldSingleValue: item.field_value ? item.field_value.option_value : '',
        optionName: item.field_value ? item.field_value.option_name : '',
        systemFieldFlag: item.system_field_flag,
        conditionList: item.conditionList,
        authTypeCondition: item.authTypeCondition
      });
    }
    if (item.field_type === 4 || item.field_type === 6) {
      const fieldMultipleValue = {};
      let lable;
      let value;

      if (
        item.field_value &&
        item.field_value.option_name &&
        item.field_value.option_name.indexOf('/')
      ) {
        lable = item.field_value.option_name.split('/'); // [一级,二级,三级,四级,五级]
      } else {
        lable = [
          item.field_value
            ? item.field_value.option_name
              ? item.field_value.option_name
              : ''
            : ''
        ];
      }
      if (
        item.field_value &&
        item.field_value.option_value &&
        item.field_value.option_value.indexOf(',')
      ) {
        value = item.field_value.option_value.split(','); // [一,二,三,四,五]
      } else {
        value = item.field_value ? [item.field_value.option_value] : [];
      }
      lable.forEach((item, index) => {
        if (value[index]) {
          fieldMultipleValue[value[index]] = item;
        } // {"1003": "物流中心", "1006": "药品发错"},
      });
      newArr.push({
        fieldCode: item.field_code,
        fieldType: item.field_type,
        fieldText: item.field_text,
        fieldMultipleValue: fieldMultipleValue,
        optionName: item.field_value ? item.field_value.option_name : '',
        systemFieldFlag: item.system_field_flag,
        conditionList: item.conditionList,
        authTypeCondition: item.authTypeCondition
      });
    }
    if (item.field_type === 10) {
      newArr.push({
        fieldCode: item.field_code,
        fieldType: item.field_type,
        fieldText: item.field_text,
        fieldMultipleValue: item.field_value,
        systemFieldFlag: item.system_field_flag,
        conditionList: item.conditionList,
        authTypeCondition: item.authTypeCondition
      });
    }
  });
  return newArr;
}

// <!-- 字段类型  0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件 -->
export function typeCopyVal(oldObj, newObj) {
  // console.log('oldObj', oldObj);
  // console.log('newObj', newObj);
  // console.log('item:', oldObj.fieldType);
  if (newObj.optionSettings && typeof newObj.optionSettings === 'string') {
    var rest = newObj.optionSettings.replace(/(&quot;)/g, '"');
    newObj.optionSettings = JSON.parse(rest);
  }
  if (
    oldObj.fieldType === 0 ||
    oldObj.fieldType === 1 ||
    oldObj.fieldType === 7 ||
    oldObj.fieldType === 8 ||
    oldObj.fieldType === 9
  ) {
    newObj.defaultValue = oldObj.defaultValue;
  }
  if (oldObj.fieldType === 5) {
    newObj.optionSettings.dateOptions.dateValue =
      oldObj.optionSettings.dateOptions.dateValue;
  }
  if (oldObj.fieldType === 7) {
    newObj.optionSettings.cityOptions.optionsValue =
      oldObj.optionSettings.cityOptions.optionsValue;
    newObj.optionSettings.cityOptions.optionsArray =
      oldObj.optionSettings.cityOptions.optionsArray;
  }
  if (oldObj.fieldType === 2) {
    newObj.optionSettings.selectOptions.optionsValue =
      oldObj.optionSettings.selectOptions.optionsValue;
  }
  if (oldObj.fieldType === 3) {
    newObj.optionSettings.radioOptions.optionsValue =
      oldObj.optionSettings.radioOptions.optionsValue;
  }
  if (oldObj.fieldType === 4) {
    newObj.optionSettings.checkedOptions.optionsValue =
      oldObj.optionSettings.checkedOptions.optionsValue;
  }
  if (oldObj.fieldType === 6) {
    newObj.optionSettings.treeOptions.optionsValue =
      oldObj.optionSettings.treeOptions.optionsValue;
    newObj.optionSettings.treeOptions.templateCode =
      oldObj.optionSettings.treeOptions.templateCode;
    newObj.optionSettings.treeOptions.optionsLabel =
      oldObj.optionSettings.treeOptions.optionsLabel;
  }
  if (oldObj.fieldType === 10) {
    newObj.optionSettings.fileObj.optionsValue =
      oldObj.optionSettings.fileObj.optionsValue;
    newObj.optionSettings.fileObj.optionsArray =
      oldObj.optionSettings.fileObj.optionsArray;
  }

  return newObj;
}
/**
 * @param {*} list
 */
export function adapterList(list) {
  const newList = [];

  list.forEach(item => {
    const objs = Object.keys(item).reduce((newData, key) => {
      if (key === 's1164723673773510656') {
        newData['1164723673773510656'] = item['s1164723673773510656'];
      }
      if (key === 'workorder_num') {
        newData['workorderNum'] = item.workorder_num;
      }
      if (key === 'see_details') {
        newData['seeDetails'] = item.see_details;
      }
      if (key === 'to_processing') {
        newData['toDealWith'] = item.to_processing;
      }
      if (key === 'to_close') {
        newData['toClose'] = item.to_close;
      }
      if (key === 'urge') {
        newData['urge'] = item.urge;
      }
      if (key === 's1164456058157142016') {
        newData['1164456058157142016'] = item['s1164456058157142016'];
      }
      if (key === 's1164456270271483904') {
        newData['1164456270271483904'] = item['s1164456270271483904'];
      }
      if (key === 's1164724692221825024') {
        newData['1164724692221825024'] = item['s1164724692221825024'];
      }
      if (key === 's1164723822566445056') {
        newData['1164723822566445056'] = item['s1164723822566445056'];
      }
      if (key === 's1164723822566445056') {
        newData['1164723822566445056'] = item['s1164723822566445056'];
      }
      if (key === 'completion_time') {
        newData['completionTime'] = item['completion_time'];
      }
      if (key === 'creator_id') {
        newData['creatorId'] = item['creator_id'];
      }
      if (key === 'creator_name') {
        newData['creatorName'] = item['creator_name'];
      }
      if (key === 'customer_id') {
        newData['customer_id'] = item['customer_id'];
      }
      if(key === 's1870039678284075008') {
        newData['1870039678284075008'] = item['s1870039678284075008'];
      }
      if (key === 'current_processing_person_id') {
        newData['currentProcessingPersonId'] =
          item['current_processing_person_id'];
      }
      if (key === 'current_processing_person_name') {
        newData['currentProcessingPersonName'] =
          item['current_processing_person_name'];
      }
      if (key === 'current_state') {
        newData['currentState'] = item['current_state'];
      }
      if (key === 'editor_id') {
        newData['editorId'] = item['editor_id'];
      }
      if (key === 'editor_name') {
        newData['editorName'] = item['editor_name'];
      }
      if (key === 'end_person_id') {
        newData['endPersonId'] = item['end_person_id'];
      }
      if (key === 'end_person_name') {
        newData['endPersonName'] = item['end_person_name'];
      }
      if (key === 'form_id') {
        newData['formId'] = item['form_id'];
      }
      if (key === 'form_type_id') {
        newData['formTypeId'] = item['form_type_id'];
      }
      if (key === 'form_type_name') {
        newData['formTypeName'] = item['form_type_name'];
      }
      if (key === 'gmt_create') {
        newData['gmtCreate'] = item['gmt_create'];
      }
      if (key === 'gmt_modified') {
        newData['gmtModified'] = item['gmt_modified'];
      }
      if (key === 'workorder_id') {
        newData['id'] = item['workorder_id'];
      }
      if (key === 'latest_submission_time') {
        newData['latestSubmissionTime'] = item['latest_submission_time'];
      }
      if (key === 'org_id') {
        newData['orgId'] = item['org_id'];
      }
      if (key === 'processing_duration') {
        newData['processingDuration'] = item['processing_duration'];
      }
      if (key === 'received') {
        newData['received'] = item['received'];
      }
      if (key === 'recent_acceptance_time') {
        newData['recentAcceptanceTime'] = item['recent_acceptance_time'];
      }
      if (key === 'time_limited') {
        newData['timeLimited'] = item['time_limited'];
      }
      if (key === 'timeout_state') {
        newData['timeoutState'] = item['timeout_state'];
      }
      if (key === 'flow_id') {
        newData['flowId'] = item['flow_id'];
      }
      if (key === 'tag_id') {
        newData['tagId'] = item['tag_id'];
      }
      if (key === 'tag_name') {
        newData['tagName'] = item['tag_name'];
      }
      if (key === 'customer_satisfaction') {
        newData['customer_satisfaction'] = item['customer_satisfaction'];
        newData['evaluategmtCreate'] = item['customer_satisfaction']
          ? item['customer_satisfaction']['gmtCreate']
          : '';
      }

      return newData;
    }, {});
    newList.push(objs);
  });
  return newList;
}
// 手机号验证
export function isvalidPhone(str) {
  const reg = /^1[3|4|5|6|7|8][0-9]\d{8}$/;
  return reg.test(str);
}

/**
 * 复制文本内容
 */
export function copyValue(data) {
  // console.log('复制内容：', data);
  var oInput = document.createElement('input');
  oInput.value = data;
  document.body.appendChild(oInput);
  oInput.select(); // 选择对象
  document.execCommand('Copy'); // 执行浏览器复制命令
  oInput.className = 'oInput';
  oInput.style.display = 'none';
  document.body.removeChild(oInput);
}
