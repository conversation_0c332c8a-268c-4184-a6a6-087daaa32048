// 返回多层嵌套对象
/**
 * @function getNestedObject 多层嵌套对象 取值
 * @param {Object,Array} nestedObj 数据源对象
 * @param {Array<String>} pathArr 数据位
 * @description 保证嵌套对象取值不报错
 * @description const p = {name:'p',age:18,info:{city:'长治',like:['a','b']}} 取like的0索引
 * @description  取like的0索引 var item = getNestedObject(p, ['info','like',0]) //a
 */
export function getNestedObject(nestedObj, pathArr) {
  return pathArr.reduce(
    (obj, key) => (obj && obj[key] !== 'undefined' ? obj[key] : null),
    nestedObj
  );
}

// post 请求，使用 formData 方式提交
export function formData(options) {
  const params = new FormData();
  Object.keys(options).forEach(key => {
    params.append(key, options[key]);
  });
  return params;
}
