<template >
  <div class="knowledge-details">
    <div class="header">
      <div class="title">
        <div>
          <h3>{{form.name}}</h3>
        </div>
        <div>
          <span class="title-info">发布人:{{ form.modifyName }}</span>
          <span class="title-info">发布时间:{{form.modifyTime}}</span>
          <span class="title-info">浏览量:{{form.viewNum}}</span>
        </div>
      </div>
    </div>

    <div class="body">
      <div class="message" v-html="form.editorText"></div>
    </div>
  </div>
</template>

<script>
import filePreview from '@/components/Fields/file-preview';
import { findKnowledgeNoticeDetail } from '@/api/knowledge_base';
export default {
  components: {
    filePreview
  },
  name: 'AnnouncementDetails',
  data() {
    return {
      form: {
        viewNum: '',
        modifyTime: '',
        modifyName: '',
        name: '',
        editorText: '',
        id: ''
      }
    };
  },
  created() {
    let that = this;
    //wangEditer a标签点击事件
    window.wangEditerAClick = function(aURL) {
      let aURLHashIndex = aURL.indexOf('#');
      aURL = aURL.substring(aURLHashIndex + 1);
      that.$router.push({
        path: aURL
      });
    };
    this.getBreadcrumb();
  },
  activated() {
    this.form.id = this.$route.query.templateId
      ? this.$route.query.templateId
      : '';
    if (this.form.id) {
      findKnowledgeNoticeDetail({ id: this.form.id, onclick: true }).then(
        res => {
          if (res.code === 1) {
            this.form.name = res.data.title;
            this.form.editorText = res.data.context;
            this.form.viewNum = res.data.viewNum;
            this.form.modifyTime = res.data.modifyTime;
            this.form.modifyName = res.data.modifyName;
          } else {
            this.$XyyMessage.error(res.msg);
          }
        }
      );
    }
  },
  mounted() {},
  methods: {
    getBreadcrumb() {
      let matched = this.$route.matched.filter(item => item.name);

      const first = matched[0];
      if (first && first.name !== 'dashboard') {
        matched = [{ path: '/dashboard', meta: { title: 'Dashboard' } }].concat(
          matched
        );
      }

      this.levelList = matched.filter(
        item => item.meta && item.meta.title && item.meta.breadcrumb !== false
      );
    }
  }
};
</script>

<style lang="scss">
.knowledge-details {
  .app-breadcrumb.el-breadcrumb {
    display: inline-block;
    font-size: 14px;
    line-height: 50px;
    margin-left: 10px;
    .no-redirect {
      color: #97a8be;
      cursor: text;
    }
  }
  .header {
    margin: 0 auto;
    .title {
      text-align: center;
      // margin: 0 auto;
      // display: flex;
      // position: absolute;
      // left: 50%;
      // -webkit-transform: translateX(-50%);
      // transform: translateX(-50%);
      .title-info {
        margin-left: 20px;
      }
    }
  }
  .body {
    margin: 20px 40px;

    .message {
      // min-height: 400px;
      width: 100%;
      height: auto;
      /* table 样式 */
      /deep/ {
        table {
          border-top: 1px solid #ccc;
          border-left: 1px solid #ccc;
        }
        table td,
        table th {
          border-bottom: 1px solid #ccc;
          border-right: 1px solid #ccc;
          // padding: 3px 5px;
        }
        table th {
          background-color: #eee;
          // border-bottom: 2px solid #ccc;
          text-align: center;
        }

        /* blockquote 样式 */
        blockquote {
          display: block;
          border-left: 8px solid #d0e5f2;
          padding: 5px 10px;
          margin: 10px 0;
          line-height: 1.4;
          font-size: 100%;
          background-color: #f1f1f1;
        }

        /* code 样式 */
        code {
          display: inline-block;
          *display: inline;
          *zoom: 1;
          background-color: #f1f1f1;
          border-radius: 3px;
          padding: 3px 5px;
          margin: 0 3px;
        }
        pre code {
          display: block;
        }

        /* ul ol 样式 */
        ul,
        ol {
          margin: 0;
        }

        ul,
        dl {
          list-style-type: disc;
        }
        a {
          color: #428bca;
          text-decoration: none;
        }
        a:hover {
          text-decoration: underline;
        }
      }
    }
  }
}
</style>
