<template>
  <el-input
    ref="xyyNumber"
    :value="value"
    :step="currentStep"
    :type="String(value).indexOf('****') === -1 ? 'number' : 'text'"
    :clearable="clearable"
    :oninput="`if(value.length>${maxlength})value=value.slice(0,${maxlength})`"
    :disabled="disabled"
    :placeholder="disabled ? '' : placeholder"
    v-bind="$attrs"
    class="ymb-number"
    onmousewheel="return false"
    onkeypress="return (/[\d|\-|+|.]/.test(String.fromCharCode(event.keyCode || event.which)))"
    @focus="handleFocus"
    @blur="handleBlur"
    @change="handleChange"
  >
    <template v-if="symbol" slot="prepend">{{ symbol }}</template>
    <template v-if="unit" slot="append">{{ unit }}</template>
  </el-input>
</template>

<script>
export default {
  name: 'XyyNumber',
  props: {
    value: [String, Number],
    symbol: String,
    unit: String,
    min: Number,
    max: Number,
    precision: Number,
    step: Number,
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请输入数字'
    },
    clearable: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    maxlength() {
      return this.$attrs.maxlength || 15;
    },
    currentStep() {
      let step = 1;
      if (this.step) {
        step = this.step;
      } else {
        if (this.precision) {
          step = '0.' + '0'.repeat(this.precision - 1) + '1';
        }
      }
      return step;
    }
  },
  methods: {
    focus() {
      this.$refs.xyyNumber.focus();
    },
    handleChange(val) {
      const num = this.format(val);
      this.$emit('input', num);
      this.$emit('change', num);
    },
    handleBlur(e) {
      const val = e.target.value;
      if (val === '') {
        this.handleChange('');
      }
      this.$emit('blur', e);
    },
    handleFocus(e) {
      this.$emit('focus', e);
    },
    format(val) {
      let num = '';
      if (val && !isNaN(Number(val))) {
        num = Number(val);
        if (this.max !== undefined && num >= this.max) {
          num = this.max;
        }
        if (this.min !== undefined && num <= this.min) {
          num = this.min;
        }
        if (this.precision !== undefined) {
          num = num.toFixed(this.precision);
        }
      }
      if (num === this.value) {
        this.$refs.xyyNumber.currentValue = num;
      }
      if (num === '') {
        this.$refs.xyyNumber.currentValue = ' ';
        this.$nextTick(_ => {
          this.$refs.xyyNumber.currentValue = num;
        });
      }
      return num;
    }
  }
};
</script>

<style lang="scss">
.ymb-number {
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  input[type='number'] {
    -moz-appearance: textfield;
  }
  .el-input__inner {
    line-height: 12px;
  }
  .el-input-group__append,
  .el-input-group__prepend {
    padding: 0 8px;
  }
}
</style>
