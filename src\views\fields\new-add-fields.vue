<template>
  <div class="tabs-box">
    <!--右侧配置-->
    <fieldsBox class="tabs-box-left">
      <!-- 选择类型 -->
      <div slot="header" class="tabs-box-left-header">
        <span>字段类型</span>
        <el-select
          :disabled="state === 2"
          v-model="optionsModel.fieldType"
          style="width: 220px"
          placeholder="请选择"
          filterable
          @change="selectChange"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div v-if="optionsModel.fieldType === null" slot="main" class="choose-img">
        <p class="desc-select">
          <img src="../../assets/work_sheet/notSheet.png" alt />
          <span>请先选择字段类型</span>
        </p>
      </div>

      <div v-loading="loading" v-else slot="main">
        <configuration
          ref="optionsModel"
          :keys="optionsModel.fieldType"
          :disabled-props="disabledProps"
          v-model="optionsModel"
        />
      </div>
    </fieldsBox>
    <!--左侧预览-->
    <fieldsBox class="tabs-box-right">
      <span slot="header">预览</span>

      <preview
        v-if="optionsModel.fieldType !== null"
        slot="main"
        :keys="optionsModel.fieldType"
        :preview="optionsModel"
        :read-only="([10].includes(optionsModel.fieldType))"
        :preview-edit="true"
      />
    </fieldsBox>
    <div class="tabs-footer">
      <el-button
        :disabled-props="disabledProps"
        type="primary"
        @click="checkTimer(saveClick,'timer')()"
      >保存</el-button>
    </div>
  </div>
</template>

<script>
/* 新增自定义字段保存  新增系统字段保存  更新系统字段  更新自定义字段  查询字段信息*/
import { saveCustomField, saveSystemField } from '@/api/fields/index';
/* 查询关联字段信息 */
// import { listTemplate } from '../../api/fields/fields-comment';
import fieldsBox from './components/fields-box';
import configuration from './components/configuration';
import preview from '../../components/Fields/preview';

export default {
  name: 'NewAddFields',
  components: {
    fieldsBox,
    configuration,
    preview
  },
  data() {
    return {
      disabledProps: false, // 保存后禁用表单
      loading: false,
      state: 2, // 2 -编辑 1-新建
      type: '', // system -系统字段 custom-自定义
      options: [
        {
          label: '单行输入',
          value: 0
        },
        {
          label: '多行输入',
          value: 1
        },
        {
          label: '下拉列表',
          value: 2
        },
        {
          label: '单选',
          value: 3
        },
        {
          label: '多选',
          value: 4
        },
        {
          label: '日期',
          value: 5
        },
        {
          label: '级联',
          value: 6
        },
        {
          label: '省市区',
          value: 7
        },
        {
          label: '电话',
          value: 8
        },
        {
          label: '邮箱',
          value: 9
        },
        {
          label: '附件',
          value: 10
        }
      ],
      optionsModelOld: {},
      optionsModel: {
        systemFieldFlag: 0, // 是否是系统字段,’0’:’是’,’1’:’否’
        fieldName: '', // 字段名
        fieldText: '', // 字段文本
        fieldType: null, // 字段类型 0:单行输入,1:多行输入,2:下拉,3:单选,4:多选,5:日期,6:级联,7:省市区,8:电话,9:邮箱,10:附件
        limitNumberCharacters: '', // 限制字符数
        defaultValue: '', // 默认值
        tips: '', // 提示信息
        remarks: '', // 备注描述
        optionSettings: {
          // 选项设置卡
          /*
          cityOptions: { // 省市区
            optionsValue: [], // 省市区街道
            optionsArray: '' // 详细地址
          },
          selectOptions: { // 下拉列表
            optionsValue: 0, // 默认值
            optionsArray: [{ // 选项数组
              optionsDesc: ''
            }]
          },
          radioOptions: { // 单选 选项设置
            optionsValue: 0, // 默认值
            optionsArray: [{ // 选项数组
              optionsDesc: ''
            }]
          },
          checkedOptions: { // 多选 选项设置
            optionsValue: [0], // 默认值
            optionsArray: [{ // 选项数组
              optionsDesc: ''
            }]
          },
          dateOptions: { // 日期选项
            dateSelect: 'YYYY年MMMM月DDDD日', // 日期默认
            dateArray: [
              {
                value: 'YYYY年MMMM月DDDD日',
                label: '二零一九年/四月/十五日'
              }, {
                value: 'yy-mm-dd',
                label: '2019-04-15'
              }, {
                value: 'yy-mm-dd HH:ss:nn',
                label: '2019-04-15 17:33:45'
              }, {
                value: 'yy年mm月dd日 周ww',
                label: '2019年4月15日 周一'
              }, {
                value: 'yy-mm-dd 周ww',
                label: '2019-04-15 周一'
              }
            ],
            dateValue: new Date().getTime(),
            defaultDate: 0, // 默认值选择
            defaultDateArray: [
              {
                label: '当前时间',
                value: 0
              }, {
                label: '5天前',
                value: 1
              }, {
                label: '1天前',
                value: 2
              }, {
                label: '7天前',
                value: 3
              }, {
                label: '无',
                value: 4
              }
            ]
          },
          treeOptions: { // 级联选项
            optionsValue: [],
            optionsLabel: [],
            optionsArray: [{
              value: 1,
              label: '一级 1',
              children: [{
                value: 2,
                label: '2级'
              }]
            }]
          }*/
        }
      },
      timer: null
    };
  },
  watch: {
    // 路径切换
    $route(to, form) {
      if (to.name === 'newAddFields') this.routerClick(to);
    }
  },
  mounted() {
    this.routerClick(this.$route);
  },
  methods: {
    // 路径切换时的数据问题
    routerClick(router) {
      this.state = Number(router.query.state);
      this.type = this.$route.query.type;
      this.optionsModel.systemFieldFlag =
        router.query.type === 'system' ? 0 : 1;
      this.optionsModel.fieldType = null;
      this.$store.dispatch('tagsView/addVisitedView', router);
      this.disabledProps = false;
    },
    // 保存时进行验证 // 级联的回调判断
    getMenuBtnList(menuTreeList, menuList) {
      for (const item of menuTreeList) {
        if (item.label === '') {
          menuList.push(true);
          break;
        }
        if (item.children && item.children.length > 0) {
          this.getMenuBtnList(item.children, menuList);
        }
      }
    },
    /**
     * 校验级联
     */
    validateTreeOptions(datas) {
      let pass = true;
      for (let i = 0; i < datas.length; i++) {
        if (datas[i].label.length > 20) {
          pass = false;
          break;
        }
        if (datas[i].children) {
          pass = this.validateTreeOptions(datas[i].children);
        }
      }
      return pass;
    },
    // 保存方法
    saveClick() {
      if (this.optionsModel.fieldType === null) {
        this.$XyyMessage.error('请配置字段后保存');
        this.loading = false;
        return false;
      }
      // 级联选项判断
      if (this.optionsModel.fieldType === 6) {
        const tree = [];
        this.getMenuBtnList(
          this.optionsModel.optionSettings.treeOptions.optionsArray,
          tree
        );
        if (
          this.optionsModel.optionSettings.treeOptions.optionsArray.length ===
            0 ||
          tree.length > 0
        ) {
          this.$XyyMessage.error('请配置选项设置后保存');
          this.loading = false;
          return false;
        } else if (
          !this.validateTreeOptions(
            this.optionsModel.optionSettings.treeOptions.optionsArray
          )
        ) {
          this.$XyyMessage.error('级联选项不能超出20字');
          return false;
        }
      } else if (this.optionsModel.fieldType === 5) {
        // 如果是日期清空选中值
        this.optionsModel.optionSettings.dateOptions.dateValue = '';
      }
      this.loading = true;
      this.$refs['optionsModel'].submitForm().then(
        valid => {
          const datas = this.optionsSettings(this.optionsModel.fieldType);
          // 自定义保存 / 系统字段保存
          // const saveFun = this.type + 'Save';
          this.saveField(datas);
        },
        () => {
          this.loading = false;
        }
      );
    },
    // 提交时 optionSetting的数据
    optionsSettings(type) {
      if ([2, 3, 4, 5, 6, 7, 10].indexOf(type) === -1) {
        return '{}';
      } else {
        return JSON.stringify(this.optionsModel.optionSettings);
      }
    },
    /* 校验成功后的保存 */
    saveField(datas) {
      const params = JSON.parse(JSON.stringify(this.optionsModel));
      const that = this;
      params.optionSettings = datas;
      delete params.gmtCreate;
      delete params.gmtModified;
      for (var key in params) {
        if (key !== 'id' && params[key] === that.optionsModelOld[key]) {
          delete params[key];
        }
      }
      that[that.type + 'Save'](params, that);
    },
    /* 自定义字段保存 */
    customSave(params, that) {
      saveCustomField(params).then(res => {
        if (res.code === 1) {
          that.$XyyMessage.success('保存成功');
          that.state = 2;
          that.disabledProps = true;
          that.dispatchPath();
        } else {
          this.loading = false;
          const msg =
            res.msg === '字段名称重复'
              ? '已存在此字段名称，请修改后保存'
              : res.msg;
          this.$XyyMsg({
            content: msg,
            closeBtn: false,
            onSuccess: function() {}
          });
        }
      });
    },
    /* 系统字段 保存*/
    systemSave(params, that) {
      saveSystemField(params).then(res => {
        if (res.code === 1) {
          that.$XyyMessage.success('保存成功');
          that.state = 2;
          that.disabledProps = true;
          that.dispatchPath();
        } else {
          this.loading = false;
          const msg =
            res.msg === '字段名重复'
              ? '已存在此字段名称，请修改后保存'
              : res.msg;
          this.$XyyMsg({
            content: msg,
            closeBtn: false,
            onSuccess: function() {}
          });
        }
      });
    },
    // 选择类型
    selectChange(val) {
      const obj = {
        fieldName: '', // 字段名
        fieldText: '', // 字段文本
        limitNumberCharacters: '', // 限制字符数
        defaultValue: '', // 默认值
        tips: '', // 提示信息
        remarks: '', // 备注描述
        optionSettings: {}
      };
      obj.limitNumberCharacters =
        val === 1 ? '200' : val === 0 || val === 9 ? '50' : val === 8 ? 11 : '';
      obj.tips =
        val === 10
          ? '附件请小于500MB，格式仅限gif bmp jpg jpeg png pdf zip rar mp4 avi doc docx xls xlsx ppt pptx txt'
          : '';
      Object.assign(this.optionsModel, obj);
      this.optionsModel.optionSettings = this.changeSettings(val);
    },
    // 选择字段类型时 选项设置的参数
    changeSettings(val) {
      switch (val) {
        case 2:
          return {
            selectOptions: {
              // 下拉列表
              optionsValue: '', // 默认值
              optionsArray: [
                {
                  // 选项数组
                  optionsDesc: '',
                  val: new Date().getTime()
                }
              ]
            }
          };
        case 3:
          return {
            radioOptions: {
              // 单选 选项设置
              optionsValue: '', // 默认值
              optionsArray: [
                {
                  // 选项数组
                  optionsDesc: '',
                  val: new Date().getTime()
                }
              ]
            }
          };
        case 4:
          return {
            checkedOptions: {
              // 多选 选项设置
              optionsValue: [], // 默认值
              optionsArray: [
                {
                  // 选项数组
                  optionsDesc: '',
                  val: new Date().getTime()
                }
              ]
            }
          };
        case 5:
          return {
            dateOptions: {
              // 日期选项
              dateSelect: 'yy-mm-dd', // 日期默认
              dateArray: [
                // {
                //   value: 'YYYY年MMMM月DDDD日',
                //   label: '二零一九年/四月/十五日'
                // },
                {
                  value: 'yy-mm-dd',
                  label: '2019-04-15'
                },
                {
                  value: 'yy-mm-dd HH:ss:nn',
                  label: '2019-04-15 17:33:45'
                }
                // {
                //   value: 'yy年mm月dd日 周ww',
                //   label: '2019年4月15日 周一'
                // },
                // {
                //   value: 'yy-mm-dd 周ww',
                //   label: '2019-04-15 周一'
                // }
              ],
              dateValue: new Date().getTime(),
              defaultDate: 0, // 默认值选择
              defaultDateArray: [
                {
                  label: '当前时间',
                  value: 0
                },
                {
                  label: '5天前',
                  value: 1
                },
                {
                  label: '1天前',
                  value: 2
                },
                {
                  label: '7天前',
                  value: 3
                },
                {
                  label: '无',
                  value: 4
                }
              ]
            }
          };
        case 6:
          return {
            treeOptions: {
              // 级联选项
              optionsValue: [],
              optionsArray: [
                {
                  value: 1,
                  label: '一级 1',
                  children: [
                    {
                      value: 2,
                      label: '2级'
                    }
                  ]
                }
              ]
            }
          };
        case 7:
          return {
            cityOptions: {
              // 省市区
              optionsValue: [], // 省市区街道
              optionsArray: '' // 详细地址
            }
          };
        case 10:
          return {
            fileObj: {
              // 附件
              optionsValue: [], // 文件路径
              optionsArray: [] // 文件名
            }
          };
        default:
          return {};
      }
    },
    /* 保存跳转 标签*/
    dispatchPath() {
      const view = this.$route;
      this.$store
        .dispatch('tagsView/delView', view)
        .then(({ visitedViews }) => {
          if (this.type === 'system') {
            this.$router.push({ name: 'systemFields' });
          } else if (this.type === 'custom') {
            this.$router.push({ name: 'customFields' });
          }
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.tabs-box {
  position: relative;
  padding-bottom: 56px;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  &-left {
    min-width: 415px;
    width: 49%;
    min-height: 400px;
    height: 100%;
    overflow-y: hidden;
    padding-bottom: 60px;
    .choose-img {
      width: 240px;
      left: 50%;
      top: 50%;
      transform: translateX(-50%) translateY(-50%);
      position: absolute;
      img {
        width: 100%;
      }
      .desc-select {
        width: 100%;
        height: auto;
        color: #aeaebf;
        font-size: 14px;
        text-align: center;
        // margin-top: 100px;
        margin: 0;
      }
      span {
        position: relative;
        top: 40px;
      }
    }
    &-header {
      span {
        display: inline-block;
        width: 70px;
        text-align: right;
        margin-right: 10px;
      }
    }
  }
  &-right {
    min-width: 100px;
    width: 49%;
  }
  /deep/.el-input--suffix {
    height: 40px;
  }
  /deep/ .el-input__inner {
    height: 36px !important;
    line-height: 36px;
  }
  .tabs-footer {
    position: absolute;
    bottom: 10px;
    right: 20px;
    background: #fff;
    text-align: right;
    .el-button {
      height: 36px;
    }
  }
}
</style>
