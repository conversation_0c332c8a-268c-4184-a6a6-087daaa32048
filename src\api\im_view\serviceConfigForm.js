import request from '@/utils/request-im';
/**
 *查询模板数据
 * @param {Object}查询参数
 */
export function getTemplate() {
  return request({
    url: '/serviceSummary/selectFieldByFromId?fromId=1',
    method: 'get'
  });
}
export function saveFormDate(params) {
  return request({
    url: '/serviceSummary/saveFormDate',
    method: 'post',
    data: params
  });
}
export function updateFormDate(params) {
  return request({
    url: '/serviceSummary/updateFormDate',
    method: 'post',
    data: params
  });
}
/**
 *所有员工组
 * @param {Object}查询参数
 */
export function imKefuGroup() {
  return request({
    url: '/imKefuGroup/all',
    method: 'get'
  });
}
/**
 *员工下拉选项
 * @param {Object}查询参数
 */
export function selectAllUser(groupId) {
  return request({
    url: '/staff/selectAllUser',
    method: 'get',
    params: { groupId }
  });
}
/**
 *获取服务总结详细信息（编辑为同一接口）
 * @param {Object}查询参数
 */
export function serviceDetail(dataId = '', dialogId = '') {
  return request({
    url: '/serviceSummary/detail',
    method: 'get',
    params: { dataId, dialogId }
  });
}
/**
 *服务总结查询
 * @param {Object}查询参数
 */
export function serviceDataLists(page = 1, pageSize = 10, params) {
  return request({
    url: '/serviceSummary/dataLists?formId=1' + '&page=' + page + '&size=' + pageSize,
    method: 'post',
    data: params
  });
}
/**
 *对话搜索
 * @param {Object}查询参数
 * cid	客户ID	string
closer	关闭状态	number	0 ：请选择；1：客服关闭；2：访客关闭
endTime	结束时间	string	格式：2019-11-23 00:00:00
estimate	满意度id	number	：1：非常满意；2：满意；3：一般；4：不满意；5：非常不满意
ip	客户端 IP	string---废弃
keyword	关键字	string
maxtime	最大时长	string	时长分钟
mintime	最小时长	string	时长 分钟
myBox	是否自己的对话	number	1：是；0：否
name	客服姓名	string
nickName	客服昵称	string
page	页码	number
pageSize   每页显示数量   number
startTime	开始时间	string	格式：2019-11-13 00:00:00
userName	客服账号名	string
customName 用户名称的传参
contentWord 聊天搜索
 */
export function dialogsSearch({ cid, closer, endTime, estimate, customName, keyword, maxtime, mintime, myBox, name, nickName, page, pageSize, startTime, userName, contentWord }) {
  return request({
    url: '/dialogs/search',
    method: 'get',
    params: { cid, closer, endTime, estimate, customName, keyword, maxtime, mintime, myBox, name, nickName, page, pageSize, startTime, userName, contentWord }

  });
}
/**
 *服务总结-可搜索字段
 * @param {Object}查询参数
 */
export function beQueryiedFields() {
  return request({
    url: '/serviceSummary/beQueryiedFields?formId=1',
    method: 'get',
    params: {}
  });
}
/**
 *服务总结-获取客户名称key
 * @param {Object}查询参数
 */
export function selectCustomerNameFieldKey() {
  return request({
    url: '/serviceSummary/selectCustomerNameFieldKey',
    method: 'get',
    params: {}
  });
}

/**
 *服务总结-获取服务小结编辑记录
 * @param {Object}查询参数
 */
export function servceSummaryLog(dataId = '') {
  return request({
    url: '/serviceSummary/selectServceSummaryLog',
    method: 'get',
    params: { dataId }
  });
}