<template>
  <xyy-list-page>
    <template slot="header">
      <xyy-button icon-class="btn-add" class-name="btn-add-icon" @click="toEdit">新建模板</xyy-button>
    </template>
    <template slot="body">
      <xyy-table
        :data="list"
        :list-query="listQuery"
        :col="col"
        :operation="operation"
        @get-data="getList"
        @operation-click="operationClick"
      >
        <!-- <template slot="gmtModified" slot-scope="{col}">
          <el-table-column
            :key="col.index"
            :prop="col.index"
            :label="col.name"
            :formatter="getFormatDate"
          />
        </template>-->
      </xyy-table>
    </template>
  </xyy-list-page>
</template>

<script>
import { linkList, inDelete } from '@/api/configuration/RegionalMenu';
export default {
  name: 'compAutoReply',
  data() {
    return {
      list: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      col: [
        { index: 'name', name: '名称', ellipsis: true },
        { index: 'applicationScope', name: '应用范围', ellipsis: true },
        { index: 'appNames', name: '应用渠道', ellipsis: true },
        {
          index: 'immediate',
          name: '生效时间段',
          width: 350,
          ellipsis: true
        },
        { index: 'operation', name: '操作', width: 220, operation: true }
      ],
      operation: [
        {
          name: '编辑',
          type: 1
        },
        {
          name: '删除',
          type: 2
        }
      ]
    };
  },
  activated() {
    this.getList(this.listQuery);
  },
  methods: {
    /**
     * 跳转到模板编辑页
     */
    toEdit(data) {
      if (data) {
        this.$router.replace({
          path: `/chat/autoList/${data.id}`,
          query: { data: JSON.stringify(data) }
        });
      } else {
        this.$router.replace({
          path: `/chat/autoList/${new Date().getTime()}`
        });
      }
    },
    getList: function(listQuery) {
      const { page, pageSize } = listQuery;
      // const name = this.listQuery.creatorName;
      const param = {
        page,
        size: pageSize
      };
      linkList(param)
        .then(res => {
          console.log(res);
          const { total } = res.data;
          let list = res.data.records;
          let applicationScope = [];
          let appNames = [];
          let immediate = {};
          // 循环数组里面的数组，合并在一起
          list.forEach((outerItem, outerIndex) => {
            applicationScope = []; // 清空数组
            appNames = [];
            immediate = [];
            outerItem.imAreaGroup.forEach(item => {
              applicationScope.push(item.groupName);
            });
            outerItem.apps.forEach(item => {
              appNames.push(item.appName);
            });
            if (outerItem.immediateState === 2) {
              immediate = '永久';
              list[outerIndex] = Object.assign({}, list[outerIndex], {
                immediate: immediate
              });
            } else if (outerItem.immediateState === 1) {
              immediate = '未配置';
              list[outerIndex] = Object.assign({}, list[outerIndex], {
                immediate: immediate
              });
            }

            list[outerIndex] = Object.assign({}, list[outerIndex], {
              applicationScope: applicationScope.join(',')
            });
            list[outerIndex] = Object.assign({}, list[outerIndex], {
              appNames: appNames.join(',')
            });
          });
          this.list = list;
          this.listQuery = {
            ...this.listQuery,
            total: Number(total)
          };
        })
        .catch(() => {});
    },
    operationClick: function(type, row) {
      switch (type) {
        case 1:
          this.toEdit(row);
          break;
        case 2:
          this.$XyyMsg({
            title: '提示',
            content: '确定要删除此模板吗?',
            onSuccess: () => {
              // debugger;
              console.log(this);
              this.delData(row);
            }
          });
          break;
      }
    },
    /**
     * 删除模板数据
     */
    delData(data) {
      var id = Number(data.id);
      inDelete(id).then(res => {
        // console.log(res);
        if (res.code == 1) {
          console.log(data);
          this.$XyyMessage.success('删除成功');
          // 刷新列表
          this.getList(this.listQuery);
        } else {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: res.msg, // html代码串
            onSuccess: () => {}
          });
        }
      });
    }
    // getFormatDate: function(row, column, cellValue, index) {
    //   return new Date(cellValue + 8 * 3600 * 1000)
    //     .toJSON()
    //     .substr(0, 19)
    //     .replace('T', ' ');
    // }
  }
};
</script>

<style lang="scss" scoped>
.search-box {
  overflow: hidden;
  padding-bottom: 20px;
  border-bottom: 1px dotted #e4e4eb;
  margin-bottom: 15px;
  label {
    float: left;
    padding-right: 8px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(41, 41, 51, 1);
    line-height: 36px;
  }
  .el-input {
    width: 334px;
    float: left;
    margin-right: 15px;
    /deep/ .el-input__inner {
      height: 36px;
      line-height: 36px;
    }
  }
  button {
    width: 68px;
    height: 36px;
    float: left;
  }
}
.operation-box {
  width: auto;
  white-space: nowrap;
  letter-spacing: 0;
  margin: 0 -10px;
  .el-button {
    position: relative;
    margin: 0 10px;
  }
  .el-button::before {
    position: absolute;
    top: 14px;
    right: -10px;
    content: '';
    display: block;
    width: 1px;
    height: 12px;
    background: #dcdfe6;
  }
  .el-button:first-child {
    margin-left: 0;
  }
  .el-button:last-child {
    margin-right: 0;
  }
  .el-button:last-child::before {
    display: none;
  }
}
thead {
  th {
    text-align: center;
  }
}
</style>
