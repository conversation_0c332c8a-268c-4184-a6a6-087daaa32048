import request from '@/utils/request-im';
/**
 * 区域菜单获取信息
 * @param {Object}查询参数
 */
export function regionalMenuTo(params) {
  return request({
    url: '/areamenu/editinfo',
    method: 'get',
    params: params
  });
}

/**
 * 区域菜单 - 获取员工组
 * @param {Object}查询参数
 */
export function getGroups(params) {
  return request({
    url: '/imKefuGroup/all',
    method: 'get',
    params: params
  });
}

/**
 * 区域菜单 - 获取区域接口
 * @param {Object}查询参数
 */
export function getRegGroup(params) {
  return request({
    url: '/imKefuGroup/all',
    method: 'get',
    params: params
  });
}

/**
 * 区域菜单 - 获取区域n
 * @param {Object}查询参数
 */
export function EmployeeGroups(params) {
  return request({
    url: '/areagroup/select',
    method: 'get',
    params: params
  });
}

/**
 * 区域菜单 - 所属员工组
 * @param {Object}查询参数
 */
export function EmployeeGroups1(params) {
  return request({
    url: '/imKefuGroup/all',
    method: 'get',
    params: params
  });
}

/**
 * 保存区域菜单
 * @param {Object}查询参数
 */
export function saveMenu(params) {
  return request({
    url: '/areamenu/save',
    method: 'post',
    data: params
  });
}

/**
 * 区域设置列表
 * @param {Object}查询参数
 */
export function localeSet(params) {
  return request({
    url: '/areagroup/page',
    method: 'get',
    params: params
  });
}

/**
 * 删除某个区域设置
 * @param {Object}查询参数
 */
export function removeLocale(params) {
  return request({
    url: '/areagroup/' + params,
    method: 'delete'
  });
}
/**
 * 保存区域设置（新增/编辑）
 * @param {Object}查询参数
 */
export function newEditor(params) {
  return request({
    url: '/areagroup/savearea',
    method: 'post',
    data: params
  });
}

/**
 * 获取区域设置详细信息
 * @param {Object}查询参数
 */
export function getRegionalInfo(params) {
  return request({
    url: '/areagroup/' + params,
    method: 'get',
    params: params
  });
}

/**
 * 获取所有省份（带有城市列表）
 * @param {Object}查询参数
 */
export function provProvince(params) {
  return request({
    url: '/areagroup/list-province-with-city',
    method: 'get'
  });
}

/**
 * 区域设置-已经设置的城市列表
 * @param {Object}查询参数
 */
export function getSetUpProvince(params) {
  return request({
    url: '/areagroup/selected-citys',
    method: 'get',
    params: params
  });
}

/**
 * 根据省份ID查询城市
 * @param {Object}查询参数
 */
export function provCity(params) {
  return request({
    url: '/areagroup/select-city-by-provinceid',
    method: 'get',
    params: params
  });
}

/**
 * 微信端访客信息绑定
 * @param {Object}查询参数
 */
export function getSwitchBind(params) {
  return request({
    url: '/setting/weixinbind',
    method: 'get',
    params: params
  });
}

/**
 * 修改访客绑定信息
 * @param {Object}查询参数
 */
export function ModifySwitchBind(dialogid, params) {
  return request({
    url: '/setting/weixinbind/' + dialogid,
    method: 'put',
    params: params
  });
}

/**
 * 会话端历史记录
 * @param {Object}查询参数
 */
export function historicalRecord(params) {
  return request({
    url: '/dialogs/custom/history',
    method: 'get',
    params: params
  });
}

/**
 * 会话端历史记录
 * @param {Object}查询参数
 */
export function getConversationMessages(params) {
  return request({
    url: '/dialogs/' + params + '/messages',
    method: 'get'
  });
}
/**
 * 会话端历史记录
 * @param {Object}查询参数
 */
export function getConversation(params) {
  return request({
    url: '/dialogs/' + params + '/detail',
    method: 'get'
  });
}

/**
 * 区域菜单 - 自动回复新建模板应用范围
 * @param {Object}查询参数
 */
export function getAuto(params) {
  return request({
    url: '/areagroup/select',
    method: 'get',
    params: params
  });
}

/**
 *分页查询常用链接接口
 * @param
 */
export function linkList(params) {
  return request({
    url: '/autoReplyMsg/replyModelList',
    method: 'post',
    params
  });
}

/**
 *添加修改链接接口
 * @param
 */
export function linkDelete(param) {
  return request({
    url: '/link/delete?linkId=' + param,
    method: 'post',
    data: param
  });
}

/**
 *添加修改链接接口
 * @param
 */
export function linkEdit(param) {
  return request({
    url: '/autoReplyMsg/saveModelConfig',
    method: 'post',
    data: param
  });
}

/**
 *编辑修改链接接口
 * @param
 */
export function listEdit(param) {
  return request({
    url: '/autoReplyMsg/editModelConfig',
    method: 'post',
    data: param
  });
}


/**
 *新增问题链接接口
 * @param
 */
export function listGoods(param) {
  return request({
    url: '/autoReplyMsg/getQuestionByConfigId',
    method: 'post',
    data: param
  });
}

/**
 *删除模板接口
 * @param
 */
export function inDelete(param) {
  return request({
    url: '/autoReplyMsg/deleteById',
    method: 'post',
    data: param
  });
}
/**
 *自动回复配置-所属渠道
 * @param {Object}查询参数
 */
export function getSources(params) {
  return request({
    url: '/staff/merchants',
    method: 'get',
  });
}