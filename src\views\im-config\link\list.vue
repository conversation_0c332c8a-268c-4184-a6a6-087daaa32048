<template>
  <xyy-list-page>
    <template slot="header">
      <div class="search-box">
        <label>名称</label>
        <el-input v-model="listQuery.name" placeholder="请输入链接名称" maxlength="20" />
        <el-button type="primary" @click="getList(listQuery)">搜索</el-button>
      </div>
      <xyy-button icon-class="btn-add" class-name="btn-add-icon" @click="toEdit">新建链接</xyy-button>
    </template>
    <template slot="body">
      <xyy-table
        :data="list"
        :list-query="listQuery"
        :col="col"
        :operation="operation"
        @get-data="getList"
        @operation-click="operationClick"
      >
        <!-- <template slot="gmtModified" slot-scope="{col}">
          <el-table-column
            :key="col.index"
            :prop="col.index"
            :label="col.name"
            :formatter="getFormatDate"
          />
        </template>-->
      </xyy-table>
    </template>
  </xyy-list-page>
</template>

<script>
import { linkList, linkDelete } from '@/api/configuration/use_link';
export default {
  name: 'compLink',
  data() {
    return {
      list: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0,
        name: '',
      },
      col: [
        { index: 'title', name: '名称', ellipsis: true },
        { index: 'link', name: '链接', ellipsis: true },
        { index: 'des', name: '说明', width: 350, ellipsis: true },
        { index: 'operation', name: '操作', width: 220, operation: true },
      ],
      operation: [
        {
          name: '编辑',
          type: 1,
        },
        {
          name: '删除',
          type: 2,
        },
      ],
    };
  },
  activated() {
    this.getList(this.listQuery);
  },
  methods: {
    /**
     * 跳转到模板编辑页
     */
    toEdit(data) {
      if (data) {
        this.$router.replace({
          path: `/chat/linkEdit/${data.id}`,
          query: { data: JSON.stringify(data) },
        });
      } else {
        this.$router.replace({
          path: `/chat/linkEdit/${new Date().getTime()}`,
        });
      }
    },
    getList: function (listQuery) {
      const { page, pageSize } = listQuery;
      const name = this.listQuery.name;
      const param = {
        page,
        size: pageSize,
        title: name,
      };
      linkList(param)
        .then((res) => {
          const { total } = res.data;
          this.list = res.data.records;
          this.listQuery = {
            ...this.listQuery,
            total: Number(total),
          };
        })
        .catch(() => {});
    },
    operationClick: function (type, row) {
      switch (type) {
        case 1:
          this.toEdit(row);
          break;
        case 2:
          this.$XyyMsg({
            title: '提示',
            content: '确定要删除此链接吗?',
            onSuccess: () => {
              this.delData(row);
            },
          });
          break;
      }
    },
    /**
     * 删除模板数据
     */
    delData(data) {
      linkDelete(data.id).then((res) => {
        if (res.code === 1) {
          this.$XyyMessage.success('删除成功');
          // 刷新列表
          this.getList(this.listQuery);
        } else {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: res.msg, // html代码串
            onSuccess: () => {},
          });
        }
      });
    },
    // getFormatDate: function(row, column, cellValue, index) {
    //   return new Date(cellValue + 8 * 3600 * 1000)
    //     .toJSON()
    //     .substr(0, 19)
    //     .replace('T', ' ');
    // }
  },
};
</script>

<style lang="scss" scoped>
.search-box {
  overflow: hidden;
  padding-bottom: 20px;
  border-bottom: 1px dotted #e4e4eb;
  margin-bottom: 15px;
  label {
    float: left;
    padding-right: 8px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(41, 41, 51, 1);
    line-height: 36px;
  }
  .el-input {
    width: 334px;
    float: left;
    margin-right: 15px;
    /deep/ .el-input__inner {
      height: 36px;
      line-height: 36px;
    }
  }
  button {
    width: 68px;
    height: 36px;
    float: left;
  }
}
.operation-box {
  width: auto;
  white-space: nowrap;
  letter-spacing: 0;
  margin: 0 -10px;
  .el-button {
    position: relative;
    margin: 0 10px;
  }
  .el-button::before {
    position: absolute;
    top: 14px;
    right: -10px;
    content: '';
    display: block;
    width: 1px;
    height: 12px;
    background: #dcdfe6;
  }
  .el-button:first-child {
    margin-left: 0;
  }
  .el-button:last-child {
    margin-right: 0;
  }
  .el-button:last-child::before {
    display: none;
  }
}
</style>
