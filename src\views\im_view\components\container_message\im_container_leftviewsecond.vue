<template>
  <div class="im_container_view2">
    <!-- 消息列表 -->
    <div
      ref="lefttable"
      :style="{height:tableHeight-124+'px'}"
      class="im_container_leftview_usertable"
    >
      <div>
        <el-table
          :data="userDatas"
          :show-header="false"
          :border="false"
          :row-class-name="'im_container_leftview_table_row'"
          highlight-current-row
        >
          <el-table-column>
            <template slot-scope="scope">
              <div
                v-if="scope.row.select === true"
                class="im_container_leftview_userlist_select"
                @click="rowclickAction(scope)"
              >
                <div class="im_container_leftview_useravatar">
                  <img :src="scope.row.avatar" />
                </div>
                <div
                  :class="selectNumber === 1?'im_container_leftview_userlist_right_1':'im_container_leftview_userlist_right'"
                >
                  <div class="im_container_leftview_userlist_nametime">
                    <el-tooltip :content="scope.row.name" placement="top">
                      <div
                        class="im_container_leftview_userlist_name"
                      >{{ getSubStr(scope.row.name) }}</div>
                    </el-tooltip>
                    <div
                      class="im_container_leftview_userlist_time"
                    >{{ timechange(selectNumber === 1?scope.row.starttime:scope.row.lastMsgTime) }}</div>
                  </div>
                  <div
                    v-if="selectNumber !== 1"
                    class="im_container_leftview_userlist_content"
                  >{{ getMessageText(scope.row.content) }}</div>
                </div>
              </div>

              <div v-else class="im_container_leftview_userlist" @click="rowclickAction(scope)">
                <div class="im_container_leftview_useravatar">
                  <img :src="scope.row.avatar" />
                </div>
                <div
                  :class="selectNumber === 1?'im_container_leftview_userlist_right_1':'im_container_leftview_userlist_right'"
                >
                  <div class="im_container_leftview_userlist_nametime">
                    <el-tooltip :content="scope.row.name" placement="top">
                      <div
                        class="im_container_leftview_userlist_name"
                      >{{ getSubStr(scope.row.name) }}</div>
                    </el-tooltip>
                    <div
                      class="im_container_leftview_userlist_time"
                    >{{ timechange(selectNumber === 1?scope.row.starttime:scope.row.lastMsgTime) }}</div>
                  </div>
                  <div
                    v-if="selectNumber !== 1"
                    class="im_container_leftview_userlist_content"
                  >{{ getMessageText(scope.row.content) }}</div>
                </div>
                <div
                  v-if="scope.row.unreadNum!==0"
                  class="im_container_leftview_badge"
                >{{ scope.row.unreadNum>99?'99+':scope.row.unreadNum }}</div>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div v-if="bottonLoading" class="loading">
          <div>加载完毕</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import utils from '@/utils/form';
import {
  kfStatesKeyChange,
  YYYYMMHHToTimeStr,
  TimeToYDHMStr
} from '../im_container_tool/message_tool';
// 接口
import {
  Kefustatus,
  kfChangeState,
  kfCurrentState,
  kfdialogQueueSize
} from '@/api/im_view/index';

export default {
  props: {
    paixuAlertData: {
      type: Array,
      default: () => {
        return [
          { name: '按对话接入时间排序', value: '0' },
          { name: '按任意方新消息排序', value: '1' },
          { name: '按访客方新消息排序', value: '2' }
        ];
      }
    },
    userDataArray: {
      type: Array
    },
    tableHeight: {
      type: Number
    },
    ingNumber: {
      type: Number
    },
    selectNumber: {
      type: Number,
      default: 0
    },
    kefuInfoData: {
      type: Object
    },
    ownerAvatarUrl: {
      type: String
    }
  },
  data() {
    return {
      userDatas: [],
      lastclick: null,
      activeName: '',
      buttonData: ['会话中', '待跟进', '历史会话', '留言'],
      sortType: '', // 排序方式
      alertListHidden: true, // 排序弹窗是否隐藏
      stateListHidden: true, // 状态弹窗是否隐藏
      stateAlertData: [],
      brigeColor: '#67C23A',
      onlineStatus: 0,
      searchStr: '',
      tabledom: {},
      onLineTimes: ' 18:17:15',
      sortNumber: 0,
      userNumber: '服务人数 0/0',
      bottonLoading: false
    };
  },
  watch: {
    userDataArray(newvalue, oldvalue) {
      console.log(newvalue, 'newvalue');
      this.userDatas = newvalue;
    }
  },
  mounted() {
    this.getStateApi();

    const that = this;

    this.$refs.lefttable.onscroll = function(res) {
      if (
        res.target.scrollTop + res.target.clientHeight ===
        res.target.scrollHeight
      ) {
        setTimeout(function() {
          if (that.selectNumber !== 2 && that.userDatas.length > 7) {
            that.pushToBottom(true);
            return;
          }
          res.target.scrollTop -= 2;
          that.$emit('im_container_left_scroll');
        }, 100);
      }
    };
  },
  methods: {
    pushToBottom(type) {
      this.bottonLoading = type;
    },
    timechange(str) {
      return TimeToYDHMStr(str);
    },
    bgcolorChange() {},
    handleCommand(command) {
      if (this.kefuInfoData.status === command) {
        return;
      }
      if (this.sortNumber === 0) {
        kfChangeState({ state: command, changeReason: 1 }).then(res => {
          if (res.code === 1) {
            this.kefuInfoData.status = command;
            this.brigeColor = this.swichColor(command);
          } else {
            this.$XyyMessage.error(res.msg);
          }
        });
        return;
      }
      const stateElement = this.stateAlertData.filter(
        item => item.statusCode === command
      );
      const stateStr = stateElement[0].name;
      const tishiStr =
        '当前排队' + this.sortNumber + '人，确定切换为' + stateStr + '状态吗？';

      this.$confirm(tishiStr, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          kfChangeState({ state: command, changeReason: 1 }).then(res => {
            if (res.code === 1) {
              this.kefuInfoData.status = command;
              this.brigeColor = this.swichColor(command);
            } else {
              this.$XyyMessage.error(res.msg);
            }
          });
        })
        .catch(() => {});
    },
    handleCommandpx(command) {
      // const arr = utils.deepCopy(this.userDatas);
      if (command === '0') {
        this.userDatas.sort(this.compare('starttime'));
      } else if (command === '1') {
        this.userDatas.sort(this.compare('lastMsgTime'));
      } else {
        this.userDatas.sort(this.compare('fklastTime'));
      }
      // this.emits(this, 'send', 'leftview_data_tosort', arr);
    },
    compare(property) {
      return function(a, b) {
        var value1 = YYYYMMHHToTimeStr(a[property]);
        var value2 = YYYYMMHHToTimeStr(b[property]);
        return value2 - value1;
      };
    },
    // 选择列表类型
    selectItem(item, index) {
      this.$emit('im_container_left_clicktype', index, this.searchStr);
    },

    getSubStr(str) {
      if (!str || str.length <= 4) {
        return str;
      }
      var subStr1 = str.substr(0, 1);
      var subStr2 = str.substr(str.length - 1, 1);
      var subStr = subStr1 + '...' + subStr2;
      return subStr;
    },

    // 列表跳转过来
    tosayIng() {
      this.$nextTick(() => {
        this.userDatas = this.userDatas.map(item => {
          item.select = false;
          return item;
        });
        var item = this.userDatas[0];
        item.select = true;
      });
    },

    // 点击列表
    rowclickAction(val) {
      this.userDatas = this.userDatas.map(item => {
        item.select = false;
        return item;
      });
      var item = this.userDatas[val.$index];
      item.select = true;
      this.$emit('im_container_left_clickrow', val);
    },

    // 获取状态列表接口
    getStateApi() {
      Kefustatus().then(res => {
        this.stateAlertData = kfStatesKeyChange(res.data);
      });
    },

    // 获取客服状态接口
    kfCurrentStateApi() {
      kfCurrentState().then(res => {
        if (res.code === 1) {
          this.brigeColor = this.swichColor(row.data);
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    swichColor(type) {
      switch (type) {
        case 1:
          return '#67C23A';
          break;
        case 2:
          return '#60B1FF';
          break;
        case 3:
          return '#F08557';
          break;
        case 4:
          return '#F0AC4E';
          break;
        case 5:
          return '#999999';
          break;
        case 6:
          return '';
          break;
        case 7:
          return '#C6C6C6';
          break;
        case 8:
          return '#6098FF';
          break;

        default:
          break;
      }
    },

    // 客服信息的处理
    KFInfoData() {
      if (this.kefuInfoData === {}) {
        return;
      }
      var currentTime =
        parseInt(Date.parse(new Date()) / 1000) -
        parseInt(this.kefuInfoData.statustime / 1000);
      if (currentTime < 0) {
        currentTime = 0;
      }
      var timeStr =
        parseInt(currentTime / 3600) +
        ':' +
        parseInt((currentTime % 3600) / 60) +
        ':' +
        (currentTime % 60);
      var statu = this.stateAlertData[this.kefuInfoData.state].statusName;
      this.onLineTimes = statu + ' ' + timeStr;
      this.userNumber =
        '服务人数:' + this.ingNumber + '/' + this.kefuInfoData.maxdialog;
      this.brigeColor = this.swichColor(this.kefuInfoData.status);
    },

    // 排队数量
    KFSortNumber() {
      kfdialogQueueSize({
        t: Math.ceil(Math.random() * 1000000).toString()
      }).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.error(res.msg);
          return;
        }
        this.sortNumber = res.data;
      });
    },

    // 当前服务人数
    searchAction(val) {
      this.$emit('im_container_left_searchFunction', this.searchStr);
    },

    // 获取文本消息中的文字
    getMessageText(value) {
      if (!value || value === '') {
        return '';
      }
      if (value.indexOf('<img') != -1) {
        return '图片消息';
      }
      var con = value.replace(/\s*/g, ''); // 去掉空格
      var res = con.replace(/<[^>]+>/g, ''); // 去掉所有的html标记
      var res1 = res.replace(/↵/g, ''); // 去掉所有的↵符号
      var text = res1.replace(/[\r\n]/g, ''); // 去掉回车换行
      // var text = value.slice(start + 1, end);
      return text;
    }
  }
};
</script>

<style>
.im_container_view2 .loading {
  width: 100%;
  height: 40px;
  position: relative;
  overflow: hidden;
  text-align: center;
  margin: 5px 0;
  color: #999;
  font-size: 13px;
}
.im_container_view2 .el-table tr td .cell {
  height: auto;
  padding-left: 0px;
  padding-right: 0px;
}

.im_container_view2 .el-table tr td {
  height: auto;
  padding: 0;
  border-bottom: none;
  background: rgba(245, 247, 250, 0.4);
}

.im_container_view2 .el-table {
  border: 0px;
}

.im_container_view2 {
  width: 222px;
  display: flex;
  flex-direction: row;
  background: rgba(245, 247, 250, 0.4);
  padding-bottom: 20px;
}
</style>

<style scoped>
.im_container_header {
  width: 200px;
  height: 147px;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* background-color: red; */
}
.im_container_header_avatar {
  width: 50px;
  height: 50px;
  margin-top: 20px;
}
.im_container_header_img {
  width: 50px;
  height: 50px;
  /* margin-top: 20px; */
  border-radius: 25px;
}

.header_time {
  font-size: 12px;
  color: #9b9bab;
  margin-top: 10px;
}

.header_twotext {
  width: 80%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  color: #9b9bab;
  margin-top: 20px;
}

.im_container_body {
  width: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.im_container_buttons {
  width: 95%;
  height: 35px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  background-color: #f5f7fa;
  padding-left: 5px;
  padding-right: 5px;
  align-items: flex-end;
}

.select_button {
  font-size: 8px;
}

.no_select_button {
  font-size: 8px;
  color: #909399;
}

.button_items {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.im_container_chooseview {
  width: 100%;
  height: 35px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  padding-right: 5px;
  padding-left: 5px;
  align-items: center;
}

.im_container_leftview_userlist {
  display: flex;
  flex-direction: row;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;
}

.im_container_leftview_userlist_select {
  display: flex;
  flex-direction: row;
  width: 100%;
  background-color: #dae9ec;
  padding-left: 10px;
  padding-right: 10px;
}

.im_container_leftview_useravatar {
  margin: 14px 14px 0 0;
}
.im_container_leftview_useravatar img {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  overflow: hidden;
}
.im_container_leftview_userlist_right {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 70px;
  border-bottom: 0.5px solid #e4e4eb;
}

.im_container_leftview_userlist_right_1 {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 80px;
  border-bottom: 0.5px solid #e4e4eb;
}

.im_container_leftview_userlist_nametime {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 13px;
  padding-right: 5px;
}

.im_container_leftview_userlist_name {
  font-size: 12px;
  width: 50px;
  color: #292933;
  line-height: 20px;
  /* height: 20px; */
  /* display: flex;
  flex-direction: row;
  align-items: center; */
  /* white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; */
  /* word-break: break-all; */
}

.im_container_leftview_userlist_time {
  display: flex;
  flex-direction: row;
  align-items: center;
  color: #909399;
  padding-right: 5px;
  height: 20px;
  font-size: 12px;
}

.im_container_leftview_userlist_content {
  /* display: flex;
  flex-direction: row;
  align-items: center; */
  font-size: 12px;
  color: #575766;
  margin-top: 6px;
  width: 100px;
  height: 22px;
  line-height: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.im_container_leftview_userlist_content p {
  font-size: 12px;
  color: #575766;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.im_container_leftview_userlist_state {
  line-height: 12px;
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.im_container_leftview_usertable {
  width: 100%;
  /* height: 540px; */
  background-color: none;
  overflow: auto;
}

.im_container_leftview_table_row {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100px;
}

.im_container_leftview_table_row_1 {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 60px;
}

.im_container_leftview_badge {
  position: absolute;
  right: 10px;
  top: 40%;
  color: white;
  background-color: red;
  font-size: 12px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 6px;
  padding-right: 6px;
  border-radius: 9px;
}

.im_container_header_avatar .littleBrage {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  position: relative;
  left: 40px;
  bottom: 10px;
}

.im_container_chooseview .im_container_chooseview_alertlist {
  position: absolute;
  left: 220px;
  top: 170px;
  z-index: 60;
}
.im_container_view2 .im_container_chooseview_alert {
  position: absolute;
  left: 160px;
  top: 180px;
  z-index: 60;
}

.im_container_leftview_usertable::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
.im_container_leftview_usertable::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #f5f7fa;
}
.im_container_leftview_usertable::-webkit-scrollbar-track {
  /*滚动条里面轨道*/

  background: #fff;
}
</style>
