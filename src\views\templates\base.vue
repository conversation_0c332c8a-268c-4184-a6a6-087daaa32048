<template>
  <div class="base-setting">
    <steps :active="0"></steps>
    <el-form ref="base"
             :model="form"
             :rules="rules"
             class="base-form"
             label-width="82px">
      <el-form-item label="模板名称"
                    prop="name">
        <el-input v-model.trim="form.name"
                  :maxlength="20"
                  auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="模板类型">
        <el-radio-group v-model="form.type"
                        :disabled="Boolean(isUpdate)">
          <el-radio :label="0">发起</el-radio>
          <el-radio :label="1">处理或关闭</el-radio>
        </el-radio-group>
        <div class="help-msg">{{ info[Number(form.type)] }}</div>
      </el-form-item>
      <el-form-item label="模板说明">
        <el-input v-model="form.description"
                  :maxlength="200"
                  show-word-limit
                  auto-complete="off"
                  type="textarea"></el-input>
      </el-form-item>
      <el-form-item class="btn-container">
        <el-button type="primary"
                   @click="checkTimer(nextStep,'timer')()">下一步</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { saveBaseData, updateBaseData, getBaseData } from '@/api/templates';
import steps from './components/steps';
export default {
  name: 'TemplateBase',
  components: {
    steps
  },
  data () {
    return {
      info: [
        '发起类型的模板，可以引用在发起类型的节点上。发起模板包括系统字段和引入的自定义字段',
        '处理或关闭类型的模板，可以引用在处理或关闭类型的节点上。不包括系统字段，只包括引入的自定义字段'
      ],
      form: {
        id: '', // 模板id
        name: '', // 模板名称
        type: 0, // 模板类型 0-发起 1-处理或关闭
        description: '' // 模板描述
      },
      isUpdate: false, // 修改状态
      rules: {
        name: [
          { required: true, message: '模板名称不能为空', trigger: 'blur' },
          {
            max: 20,
            message: '不能超过20个字',
            trigger: 'blur'
          }
        ]
      },
      timer: null
    };
  },
  activated () {
    this.isUpdate = this.$route.query.isUpdate
      ? this.$route.query.isUpdate
      : false;
    this.form.id = this.$route.query.templateId
      ? this.$route.query.templateId
      : '';
    if (this.isUpdate) {
      this.$store.dispatch('tagsView/updateVisitedView', {
        ...this.$route,
        meta: {
          title: '编辑模板'
        }
      });
      getBaseData(this.form.id).then(res => {
        if (res.code === 1) {
          this.form.name = res.data.name;
          this.form.type = res.data.type;
          this.form.description = res.data.description;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    } else {
      this.form.name = '';
      this.form.description = '';
    }
  },
  methods: {
    updateTab (view, old) {
      this.$store
        .dispatch('tagsView/updateCurrentView', {
          old: old,
          view
        })
        .then(() => {
          const { fullPath } = view;
          this.$nextTick(() => {
            this.$router.replace({
              path: fullPath
            });
          });
        });
    },
    getFullPath (path, query) {
      const params = [];
      for (const key in query) {
        params.push(key + '=' + query[key]);
      }
      return path + '?' + params.join('&');
    },
    nextStep () {
      const old = this.$store.state.tagsView.visitedViews.filter(
        el => el.path === this.$route.path
      )[0];
      const view = this.$router.options.routes
        .filter(el => el.name === 'worksheet')[0]
        .children.filter(el => el.name === 'templateEditor')[0];
      this.$refs['base'].validate(pass => {
        if (pass) {
          const loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(255,255,255, 0.8)'
          });
          // 调取后台保存接口
          if (this.isUpdate) {
            updateBaseData(this.form).then(res => {
              loading.close();
              if (res.code === 1) {
                const templateId = this.form.id;
                const path = `/worksheet/templateEditor/${templateId}`;
                const query = {
                  templateId: templateId,
                  isUpdate: this.isUpdate,
                  templateType: this.form.type
                };
                if (this.$route.query.copy) {
                  query.copy = true;
                }
                this.updateTab(
                  {
                    ...view,
                    path,
                    query,
                    fullPath: this.getFullPath(path, query),
                    meta: {
                      title: '编辑模板'
                    }
                  },
                  old
                );
              } else {
                this.$XyyMessage.error(res.msg);
              }
            });
          } else {
            saveBaseData(this.form).then(res => {
              loading.close();
              if (res.code === 1) {
                const templateId = res.data;
                const path = `/worksheet/templateEditor/${templateId}`;
                const query = {
                  templateId: templateId,
                  templateType: this.form.type
                };
                this.updateTab(
                  {
                    ...view,
                    path,
                    query,
                    params: {
                      id: templateId
                    }, // 关键参数：新建的模板不会自动生成id,这里手动设置
                    fullPath: this.getFullPath(path, query)
                  },
                  old
                );
              } else {
                this.$XyyMessage.error(res.msg);
              }
            });
          }
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.base-setting {
  box-sizing: border-box;
  padding: 15px 15px 56px;
  overflow-y: auto;
  .base-form {
    width: 580px;
    margin: 50px auto 0;
    .el-form-item {
      margin-bottom: 20px;
      /deep/.el-form-item__label {
        box-sizing: border-box;
        font-size: 14px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        color: rgba(41, 41, 51, 1);
      }
      /deep/.el-form-item__content {
        .el-textarea__inner {
          height: 210px;
        }

        .el-input__inner {
          height: 35px;
          line-height: 35px;
        }

        .help-msg {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(144, 147, 153, 1);
          line-height: 20px;
        }
      }
    }
    .el-form-item.btn-container {
      position: fixed;
      bottom: 0;
      width: calc(100% - 211px);
      box-sizing: border-box;
      left: 211px;
      height: 56px;
      background: #fff;
      box-shadow: 0px -2px 2px 0px rgba(145, 144, 144, 0.11);
      margin: 0;
      text-align: right;
      button {
        height: 36px;
        margin: 10px 20px 10px 0;
      }
    }
  }
}
// /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
// ::-webkit-scrollbar {
//   width: 8px;
//   height: 8px;
//   background-color: #f1f3f5;
// }

// /*定义滚动条轨道 内阴影+圆角*/
// ::-webkit-scrollbar-track {
//   -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
//   border-radius: 4px;
//   background-color: #f1f3f5;
// }

// /*定义滑块 内阴影+圆角*/
// ::-webkit-scrollbar-thumb {
//   border-radius: 4px;
//   -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
//   background-color: #dcdfe6;
// }
</style>
