<template>
  <div class="distribute-box">
    <el-form :model="form" label-width="100px">
      <el-form-item label="留言分发设置">
        <el-radio-group v-model="form.open">
          <el-radio :label="true">开启</el-radio>
          <el-radio :label="false">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <span>每天</span>
        <el-time-select
          v-model="form.pattern"
          :picker-options="{
            start: '00:00',
            step: '00:15',
            end: '23:45'
          }"
          :disabled="!form.open"
          :editable="false"
          placeholder="选择时间"
        ></el-time-select>
        <span>分发留言</span>
      </el-form-item>
      <el-form-item class="msg-form-item">
        <label class="msg-box">设置固定分发时间，统一分发留言</label>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="save">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getDistributeData, saveDistributeData } from '@/api/im-config/comment';
export default {
  name: 'compCommentDistrbiute',
  data() {
    return {
      form: {
        open: 0,
        pattern: '',
      },
    };
  },
  mounted() {
    this.initData();
  },
  methods: {
    /**
     * 保存数据
     */
    save() {
      saveDistributeData(this.form).then((res) => {
        if (res.code === 1) {
          this.$XyyMessage.success('保存成功');
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    /**
     * 初始化数据
     */
    initData() {
      getDistributeData().then((res) => {
        if (res.code === 1) {
          this.form = {
            open: res.data.open,
            pattern: res.data.pattern,
          };
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.distribute-box {
  position: relative;
  .el-form {
    width: 400px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateY(-50%) translateX(-50%);
    .el-form-item {
      margin-bottom: 10px;
      /deep/.el-form-item__label {
        padding-right: 15px;
      }
      /deep/.el-form-item__content {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
        .el-radio {
          margin-right: 60px;
        }
        .el-date-editor {
          width: 100px;
          margin: 0 12px;
          /deep/.el-input__inner {
            height: 36px;
            line-height: 36px;
            padding: 0 12px 0 30px;
          }
        }
        label.msg-box {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(144, 147, 153, 1);
        }
        .el-button {
          height: 36px;
          padding: 0 12px;
          line-height: 36px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(87, 87, 102, 1);
          border-radius: 2px;
          border: 1px solid rgba(228, 228, 235, 1);
          &:focus,
          &:hover {
            background: #fff;
            border-color: rgba(228, 228, 235, 1);
          }
          &.el-button--primary {
            color: rgba(255, 255, 255, 1);
            padding: 0 20px;
            border: none;
          }
          &.el-button--primary:focus,
          &.el-button--primary:hover {
            background: #3b95a8;
            border-color: #3b95a8;
          }
        }
      }
      &.msg-form-item {
        /deep/.el-form-item__content {
          line-height: 20px;
        }
      }
    }
  }
}
</style>
