<template>
  <div>
    <xyy-list-page>
      <template slot="header">
        <!-- 顶部按钮 -->
        <el-row type="flex"
                class="sheeTitle"
                justify="space-between"
                align="middle">
          <span>{{ $route.meta.query&&$route.meta.query.title?$route.meta.query.title:null }}</span>
          <el-row type="flex"
                  justify="end"
                  align="middle">
            
            <el-button class="sheetMainBtn"
                       @click="batchSwitchstate">批量转移{{ batchSelection===false?'（查询）':'（转出）' }}</el-button>
            <el-button plain
                       icon="el-icon-folder-checked"
                       class="sheetMainBtn"
                       v-if="getNewSheetBatchVisible"
                       @click="getNewSheetBatch"
                       :disabled="userStatus==1">批量领取工单</el-button>
            <el-button plain
                       icon="el-icon-folder-checked"
                       class="sheetMainBtn"
                       @click="getNewSheet"
                       :disabled="userStatus==1">随机领取工单</el-button>
            <el-row style="margin:0 12px;">
              <draft :table-data="draftData"
                     @handleEdit="handleEdit"
                     @delDraft="handleDelDraft"
                     @visibleChange="draftVisibleChange"
                     @draftMenuLoad="draftMenuLoad" />
            </el-row>
            <el-button plain
                       icon="el-icon-plus"
                       class="sheetMainBtnt"
                       @click="toNewSheet">新建工单</el-button>
          </el-row>
        </el-row>
        <!-- 快捷选择tags -->
        <div class="sheeNav"
             style="display:none;">
          <div id="btnTypeBox"
               ref="showMore"
               :class="{'notActive':show_more}"
               class="navTag">
            <el-button :class="{'active':formTypeId=='','redSign':allIsRed}"
                       class="elBtn"
                       plain
                       size="small"
                       @click="handleSheetType('')">全部</el-button>
            <el-button v-for="item in allType"
                       :key="item.id"
                       :class="{'active':formTypeId==item.id,'redSign':isRedSign(item.id)}"
                       class="elBtn"
                       plain
                       size="small"
                       @click="handleSheetType(item.id)">{{ item.name }}</el-button>
          </div>
          <!-- checkTimer(,'time')(item.id) -->
          <el-button v-if="isNeedMore"
                     class="show_more"
                     type="text"
                     @click="showMore">
            {{ show_more?'展示更多':'收起更多' }}
            <i :class="[show_more?'el-icon-arrow-down':'el-icon-arrow-up', 'el-icon--right']" />
          </el-button>
          <div class="sheetState"
               style="display:none;">
            <img class="newPageIcon"
                 src="../../assets/work_sheet/newPage.png"
                 @click="newList()" />
            <el-menu :default-active="secActiveIndex"
                     mode="horizontal"
                     background-color="#F5F7FA"
                     text-color="#909399"
                     active-text-color="#3B95A8"
                     @select="selectSubClass">
              <el-menu-item class="selTag"
                            index="1">已超时{{ overTimeNum(1) }}</el-menu-item>
              <el-menu-item class="selTag"
                            index="2">即将超时{{ overTimeNum(2) }}</el-menu-item>
              <el-menu-item class="selTag"
                            index="3">我处理中的{{ restWorkorder(1) }}</el-menu-item>
              <el-menu-item class="selTag"
                            index="4">待领取的{{ restWorkorder(0) }}</el-menu-item>
              <el-menu-item class="selTag"
                            index="5">我已处理的</el-menu-item>
              <el-menu-item class="selTag"
                            index="6">我发起的</el-menu-item>
              <el-menu-item class="selTag"
                            index="7">抄送给我的</el-menu-item>
              <!-- <el-menu-item class="selTag" index="8">全部</el-menu-item> -->
            </el-menu>
          </div>
        </div>
        <!-- 查询条件 -->
        <el-form ref="formInline"
                 :inline="true"
                 :model="formInline"
                 label-position="left"
                 class="input_serch">
          <el-row>

            <el-row>
              <el-form-item class="form-item"
                            label="工单编号"
                            prop="sheetNum">
                <el-input v-model="formInline.sheetNum"
                          class="elInput"
                          size="small"
                          placeholder="请输入内容"
                          maxlength="28"
                          @blur="inputTrim()" />
                <!-- oninput="value=value.replace(/[^\d]/g,'')" -->
              </el-form-item>
              <el-form-item class="form-item" v-if="isYBMCode"
                            label="工单创建人"
                            prop="creator">
                <el-input v-model="formInline.creator"
                          class="elInput"
                          size="small"
                          placeholder="请输入内容"
                          maxlength="60"
                          @blur="inputTrim()" />
              </el-form-item>
              <el-form-item class="form-item"
                            label="订单编号"
                            prop="orderNum">
                <el-input v-model="formInline.orderNum"
                          class="elInput"
                          size="small"
                          maxlength="35"
                          placeholder="请输入内容"
                          @blur="inputTrim()" />
              </el-form-item>
              <el-form-item class="form-item"
                            label="客户名称"
                            prop="customerName">
                <el-input v-model="formInline.customerName"
                          class="elInput"
                          size="small"
                          maxlength="60"
                          placeholder="请输入内容"
                          @blur="inputTrim()" />
              </el-form-item>
              <el-form-item class="form-item" v-if="isYBMCode"
                            label="客户ID"
                            prop="customer_id">
                <el-input v-model="formInline.customer_id"
                          class="elInput"
                          size="small"
                          maxlength="20"
                          placeholder="请输入内容"
                          @blur="inputTrim()" />
              </el-form-item>
              <el-form-item class="form-item"
                            label="客户电话"
                            prop="customerPhoneNumber">
                <el-input v-model="formInline.customerPhoneNumber"
                          class="elInput"
                          size="small"
                          maxlength="20"
                          placeholder="请输入内容"
                          @blur="inputTrim()" />
              </el-form-item>
              <el-form-item class="form-item" v-if="isYBMCode"
                            label="商家ID"
                            prop="merchantId">
                <el-input v-model="formInline.merchantId"
                          class="elInput"
                          size="small"
                          maxlength="60"
                          placeholder="请输入内容"
                          @blur="inputTrim()" />
              </el-form-item>
            </el-row>

            <el-row type="flex" justify="space-between" align="middle">
              <el-form-item>
                <el-button plain
                           type="primary"
                           size="small"
                           class="searchCondition"
                           @click="handleCondition">查询</el-button>
                <el-button plain
                           size="small"
                           @click="resetForm('formInline')">重置</el-button>
              </el-form-item>
              <el-form-item class="form-item">
                <!-- 2020.4.14,rl,更多查询条件,begin -->
                <!--
                <el-dropdown ref="messageDrop" :hide-on-click="false" trigger="click">
                  <el-button plain class="sheetMainBtn">
                    更多条件
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown" class="dropDownBox">
                    <el-dropdown-item>
                      <el-form label-width="70px">
                        <el-form-item
                          v-if="secActiveIndex==5||secActiveIndex==6||secActiveIndex==7||secActiveIndex==8"
                          label="工单状态"
                          prop="customerName"
                          class="innerStyle"
                        >
                          <el-select
                            v-model="formInline.currentState"
                            class="innerElInput"
                            placeholder="请选择内容"
                            @change="queryTime()"
                          >
                            <el-option label="全部" value="全部"></el-option>
                            <el-option label="待领取" value="0"></el-option>
                            <el-option label="处理中" value="1"></el-option>
                            <el-option label="已结单" value="2"></el-option>
                            <el-option label="退回" value="3"></el-option>
                          </el-select>
                        </el-form-item>
                        <el-form-item class="innerStyle" label="发起时间" prop="dataRange">
                          <el-date-picker
                            v-model="formInline.dataRange"
                            :default-time="['00:00:00', '23:59:00']"
                            type="datetimerange"
                            range-separator="-"
                            size="small"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            format="yyyy-MM-dd HH:mm"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            text="erdf"
                            prefix-icon="el-icon-date"
                            class="innerSelTime"
                            @change="queryTime()"
                            @focus="dateTimeFocus()"
                          />
                        </el-form-item>
                      </el-form>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                -->

                <el-popover placement="bottom-end"
                            width="580"
                            trigger="click"
                            v-model="queryMoreVisible">
                  <query-more :menuIndex="secActiveIndex"
                              :formTypeId="formTypeId"
                              @hide="setVisibleQueryMore"
                              @search="searchQueryMore"></query-more>
                  <el-button slot="reference"
                            plain
                            class="sheetMainBtn">
                    更多条件
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                </el-popover>
                <!-- 2020.4.14,rl,更多查询条件,end -->
              </el-form-item>
            </el-row>

          </el-row>
        </el-form>
      </template>

      <!-- 表格 -->
      <template slot="body">
        <div class="xyyTable">
          <!-- v-if="checkPermission('btn:cs:workorderexport')" -->
          <el-dropdown v-if="checkPermission('btn:cs:workorderexport')"
                       trigger="click"
                       class="exportExcel">
            <el-button type="text"
                       icon="el-icon-upload2">导出Excel</el-button>
            <el-dropdown-menu slot="dropdown"
                              class="exoprtShow">
              <el-dropdown-item>
                <el-button type="text"
                           @click="checkTimer(exportOpen,'time3')(1)">工单明细</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text"
                           @click="checkTimer(exportOpen,'time4')(2)">流转记录</el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <!-- 数据表格 -->
          <xyy-table ref="orderTable"
                     :data="list"
                     :col="filterCol"
                     :list-query="listQuery"
                     :operation="operation"
                     :offset-top="200"
                     :has-selection="batchSelection || getNewSheetBatchVisible"
                     @get-data="batchSelection ? getBatchList() : seachDataList(arguments[0])"
                     @handleCellMouseEnter="handleCellMouseEnter"
                     @selectionCallback="selectionCallback"
                     :assignParams="assignParams"
                     @sortChange="sortChange">
            <!-- 工单类型 -->
            <template slot="formTypeName"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width">
                <template slot-scope="item">
                  <el-row>
                    <span>{{ item.row['formTypeName'] }}</span>
                  </el-row>
                </template>
              </el-table-column>
            </template>
            <!-- 来源渠道 -->
            <template slot="1164723673773510656"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width">
                <template slot-scope="item">
                  <el-row>
                    <span>{{ item.row['1164723673773510656']?item.row['1164723673773510656'].option_name:'' }}</span>
                  </el-row>
                </template>
              </el-table-column>
            </template>
            <!-- 客户名称 -->
            <template slot="1164456058157142016"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width">
                <template slot-scope="item">
                  <el-row>
                    <span>{{ item.row['1164456058157142016'] }}</span>
                  </el-row>
                </template>
              </el-table-column>
            </template>
            <!-- 来电号码 -->
            <template slot="1164723822566445056"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width">
                <template slot-scope="item">
                  <el-row>
                    <span>{{ item.row['1164723822566445056'] }}</span>
                  </el-row>
                </template>
              </el-table-column>
            </template>
            <!-- 当前受理人 -->
            <template slot="currentProcessingPersonName"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width">
                <template slot-scope="item">
                  <el-row>
                    <span>{{ item.row['currentProcessingPersonName'] }}</span>
                  </el-row>
                </template>
              </el-table-column>
            </template>
            <!-- 工单创建人 -->
            <template slot="creatorName"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width">
                <template slot-scope="item">
                  <el-row>
                    <span>{{ item.row['creatorName'] }}</span>
                  </el-row>
                </template>
              </el-table-column>
            </template>
            <!-- 工单结算人 -->
            <template slot="endPersonName"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width">
                <template slot-scope="item">
                  <el-row>
                    <span>{{ item.row['endPersonName'] }}</span>
                  </el-row>
                </template>
              </el-table-column>
            </template>

            <!-- 工单编号 -->
            <template slot="workorderNum"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width">
                <template slot-scope="item">
                  <el-popover visible-arrow="true"
                              popper-class="hoverStyle"
                              placement="right-start"
                              trigger="hover">
                    <el-row v-if="hoverItem"
                            class="title">
                      <span v-if="hoverItem">{{ hoverItem.nodeName }}</span>
                      <span v-if="hoverItem">{{ hoverItem.nodeName?'-':'' }}</span>
                      <span v-if="hoverItem">{{ hoverItem.processorName }}</span>
                      <span v-if="hoverItem && hoverItem.recordType && hoverItem.recordType ==='append'">追加记录-</span>
                      <span v-if="hoverItem && hoverItem.recordType && hoverItem.recordType ==='append'">{{ hoverItem.creatorName }}</span>
                    </el-row>
                    <el-row v-if="hoverItem"
                            class="time">
                      <span v-if="hoverItem"
                            class="node-date">{{ hoverItem.gmtCreate | dateFormat }}</span>
                    </el-row>
                    <template v-if="hoverItem">
                      <div v-if="[1,2].includes(hoverItem.auditStatus)"
                           class="content">
                        <open-info :data-info="hoverItem.reply_custom_field_info &&
                            hoverItem.reply_custom_field_info.length
                            ? adapter(hoverItem.reply_custom_field_info)
                          : []"
                                   class="field-content"></open-info>
                      </div>
                      <div v-if="hoverItem && hoverItem.auditStatus&& hoverItem.auditStatus===3">
                        <p class="content-textarea-formate">
                          退回原因：
                        <pre>{{ hoverItem.sendBackReason }}</pre>
                        </p>
                      </div>
                      <div v-if="hoverItem && hoverItem.auditStatus&& hoverItem.auditStatus===4">
                        <p class="content-textarea-formate">
                          重启原因：
                        <pre>{{ hoverItem.reason }}</pre>
                        </p>
                      </div>
                      <div v-if="hoverItem && hoverItem.recordType && hoverItem.recordType ==='append'">
                        <p class="content-textarea-formate">
                        <pre>{{ hoverItem.appendContent }}</pre>
                        </p>
                      </div>
                    </template>
                    <template v-if="!hoverItem">
                      <el-row class="noData">暂无流转记录</el-row>
                    </template>
                    <el-row slot="reference"
                            type="flex"
                            justify="start"
                            align="middle">
                      <span style="margin-right:4px;">{{ item.row['workorderNum'] }}</span>
                      <img v-if="item.row['timeoutState']==1"
                           style="height:16px;width:16px"
                           src="../../assets/work_sheet/icon-chao.png" />
                      <img v-if="item.row['timeoutState']==2"
                           style="height:16px;width:16px"
                           src="../../assets/work_sheet/icon-jchao.png" />
                      <img v-if="item.row['currentState']==3"
                           style="height:16px;width:16px"
                           src="../../assets/work_sheet/icon-tui.png" />
                    </el-row>
                  </el-popover>
                </template>
              </el-table-column>
            </template>
            <!-- 优先级 -->
            <template slot="1164456270271483904"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width">
                <template v-if="item.row['1164456270271483904']"
                          slot-scope="item">
                  <span :class="'levelBlock ' + (item.row['1164456270271483904'].option_name === '加急'? 'urgent':item.row['1164456270271483904'].option_name === '紧急'?'pressing':'plain')">{{ item.row['1164456270271483904'].option_name === '加急'?'加急':item.row['1164456270271483904'].option_name === '紧急'?'紧急':'普通' }}</span>
                </template>
              </el-table-column>
            </template>
            <!-- 问题分类 -->
            <template v-if="secActiveIndex!='4'"
                      slot="1164724692221825024"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width">
                <template slot-scope="item">
                  <span>{{ item.row['1164724692221825024']? item.row['1164724692221825024'].option_name:'' }}</span>
                </template>
              </el-table-column>
            </template>
            <!-- 当前状态 -->
            <template slot="currentState"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :width="col.width"
                               :label="col.name">
                <template slot-scope="{row}">
                  <span v-if="row.currentState === 0"
                        class="currentState1">待领取</span>
                  <span v-else-if="row.currentState === 1"
                        class="currentState1">处理中</span>
                  <span v-else-if="row.currentState === 2"
                        class="currentState2">已结单</span>
                  <span v-else-if="row.currentState === 3"
                        class="currentState3">退回</span>
                  <span v-else-if="row.currentState === 4"
                        class="currentState4">异常</span>
                  <span v-else-if="row.currentState === 5"
                        class="currentState5">作废</span>
                  <span v-else
                        class="currentState0">待领取</span>
                </template>
              </el-table-column>
            </template>
            <!-- 最近受理时间 -->
            <template slot="recentAcceptanceTime"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width"
                               :formatter="getFormatDate"
                               :sortable="col.sortable || false" />
            </template>
            <!-- 最近提交时间 -->
            <template slot="latestSubmissionTime"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width"
                               :formatter="getFormatDate"
                               :sortable="col.sortable || false" />
            </template>
            <!-- 工单发起时间 -->
            <template slot="gmtCreate"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width"
                               :formatter="getFormatDate"
                               :sortable="col.sortable || false" />
            </template>
            <!-- 工单完成时间 -->
            <template slot="completionTime"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width"
                               :formatter="getFormatDate"
                               :sortable="col.sortable || false" />
            </template>
            <template slot="processingDuration"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width"
                               :formatter="getFormatDuration" />
            </template>
            <!-- 标签 -->
            <template slot="tag"
                      slot-scope="{col}">
              <el-table-column :label="col.name"
                               :width="col.width"
                               class-name="table-tag"
                               fixed="right">
                <template slot-scope="{ row }">
                  <el-popover placement="right"
                              width="400"
                              trigger="click">
                    <el-button v-if="row.tagId"
                               size="mini"
                               class="tag-btn"
                               type="danger"
                               icon="el-icon-delete"
                               @click="clearTag(row)">清除标签</el-button>
                    <el-button v-for="(tag,key) in tags.filter(item=>item.id!=row.tagId)"
                               :key="key"
                               size="mini"
                               class="tag-btn"
                               @click="switchTag(tag,row)">{{ tag.tagName }}</el-button>
                    <el-button v-if="!row.tagId"
                               slot="reference"
                               :disabled="isDisabledTag"
                               type="info"
                               size="mini"
                               class="tag-btn"
                               icon="el-icon-plus"
                               circle></el-button>
                    <el-button v-if="row.tagId"
                               slot="reference"
                               :disabled="isDisabledTag"
                               size="mini"
                               class="tag-btn">{{ row.tagName }}</el-button>
                  </el-popover>
                </template>
              </el-table-column>
            </template>
            <!-- 评价回复时间 -->
            <template slot="evaluategmtCreate"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width"
                               :formatter="getFormatDate"
                               :sortable="col.sortable || false" />
            </template>
            <!-- 满意度评价 -->
            <template slot="customerSatisfaction"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width">
                <template slot-scope="item">
                  <el-row>
                    <span>{{ item.row['customer_satisfaction'] && item.row['customer_satisfaction'].customerSatisfaction ? '是' : '否' }}</span>
                  </el-row>
                </template>
              </el-table-column>
            </template>
            <!-- 评价维度1 -->
            <template slot="starLevel1"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width">
                <template slot-scope="{ row }">
                  <el-rate :value="row['customer_satisfaction'] && row['customer_satisfaction'].starLevel1  ? row['customer_satisfaction'].starLevel1 : 0"
                           disabled></el-rate>
                </template>
              </el-table-column>
            </template>
            <!-- 评价维度2 -->
            <template slot="starLevel2"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width">
                <template slot-scope="{ row }">
                  <el-rate :value="row['customer_satisfaction']  && row['customer_satisfaction'].starLevel2 ? row['customer_satisfaction'].starLevel2 : 0"
                           disabled></el-rate>
                </template>
              </el-table-column>
            </template>
            <!-- 评价回复 -->
            <template slot="evaluateRateReply"
                      slot-scope="{col}">
              <el-table-column :key="col.index"
                               :prop="col.index"
                               :label="col.name"
                               :width="col.width"
                               :show-overflow-tooltip="true">
                <template slot-scope="{ row }">
                  <span>{{ row['customer_satisfaction'] && row['customer_satisfaction'].evaluate ? row['customer_satisfaction'].evaluate: '' }}</span>
                </template>
              </el-table-column>
            </template>
            <!-- 工单操作 -->
            <template slot="operation"
                      slot-scope="{col}">
              <el-table-column :label="col.name"
                               :width="col.width"
                               fixed="right"
                               class-name="operation-box">
                <template slot-scope="{row}">
                  <!-- 非待领取 -->
                  <el-row v-if="row.currentState !=0">
                    <el-button v-for="item in filterOper(row)"
                               :key="item.type"
                               type="text"
                               @click="operationClick(item.type,row)">{{ item.name }}</el-button>
                  </el-row>
                  <!-- 待领取 -->
                  <el-row v-else>
                    <el-button v-if="checkPermission('btn:cs:workorderassignment')"
                               type="text"
                               @click="sheetAssignment(row)">工单分配</el-button>
                    <el-button v-if="row.urge"
                               type="text"
                               @click="operationClick(3,row)">催单</el-button>
                    <el-button v-if="row.seeDetails"
                               type="text"
                               @click="operationClick(1,row)">查看</el-button>
                  </el-row>
                </template>
              </el-table-column>
            </template>
          </xyy-table>

          <!-- 工单分配 -->
          <el-row class="assignmentSheet">
            <el-dialog :visible.sync="assignmentDialog"
                       title="工单分配"
                       width="400px"
                       @open="initAss('assSheet')"
                       @close="assCacel">
              <el-row class="sheetNumber">将工单编号：{{ workorderNum }}的工单，分配给</el-row>
              <el-form ref="assSheet"
                       :model="assSheet"
                       :rules="assRules"
                       label-width="88px"
                       class="demo-ruleForm">
                <el-form-item label="用户组"
                              prop="userGroupVal"
                              class="userGroup">
                  <el-select v-model="assSheet.userGroupVal"
                             placeholder="请选择用户组"
                             @change="handleChangeUserGroup(assSheet.userGroupVal)">
                    <el-option v-for="item in CurrentNodeGroupInfo"
                               :key="item.userGroupId"
                               :label="item.userGroupName"
                               :value="item.userGroupId" />
                  </el-select>
                </el-form-item>
                <el-form-item label="组内成员"
                              prop="memberVal"
                              class="userGroup">
                  <el-select v-model="assSheet.memberVal"
                             placeholder="请选择组内成员"
                             class="input_sheetType">
                    <el-option v-for="item in memberArray"
                               :key="item.userId"
                               :label="item.nickname"
                               :value="item.userId" />
                  </el-select>
                </el-form-item>
                <el-form-item type="flex"
                              justify="end"
                              align="end"
                              class="assSubmit">
                  <xyy-button type="normal"
                              @click="assignmentDialog=false">取 消</xyy-button>
                  <xyy-button class="queryAss"
                              type="primary"
                              @click="checkTimer(queryAss,'time')('assSheet')">确定分配</xyy-button>
                </el-form-item>
              </el-form>
            </el-dialog>
          </el-row>
          <!-- 催单说明 -->
          <el-dialog :visible.sync="urgeWorkHid"
                     class="urgeStyle"
                     title="催单"
                     width="40%"
                     @close="urgeWorkCalce">
            <el-row class="urgeTitle">
              工单所处当前节点：{{ currentNodeName }}
              <span>处理人：{{ currentProcessPersonName?currentProcessPersonName: '无' }}</span>
            </el-row>
            <el-input v-model="urgeWorkCon"
                      type="textarea"
                      placeholder="请输入催单内容"
                      maxlength="200"
                      show-word-limit
                      resize="none"
                      class="urgeCon"></el-input>
            <el-row class="urgeTip">通知方式：短信</el-row>
            <span slot="footer"
                  class="dialog-footer">
              <el-button @click="urgeWorkHid=false">取 消</el-button>
              <el-button type="primary"
                         @click="checkTimer(urgeWork,'time2')()">确 定</el-button>
            </span>
          </el-dialog>
          <expor-tip :change-export="changeExport"
                     @handleExoprClose="handleExoprClose"
                     @handleChangeExport="handleChangeExport"></expor-tip>

          <!-- 工单批量分配查询Dialog -->
          <el-row class="assignmentSheet">
            <el-dialog :visible.sync="batchSelectDialog"
                       title="工单转移"
                       width="476px">
              <el-form :model="batchQueryForm"
                       ref="refSearchForm"
                       label-width="88px"
                       class="demo-ruleForm"
                       :rules="batchSearchRules">
                <el-form-item label="工单类型"
                              prop="orderType"
                              class="userGroup">
                  <el-select v-model="batchQueryForm.orderType"
                             @change="getTypeformatList"
                             placeholder="请选择筛选条件">
                    <el-option v-for="item in allType"
                               :key="item.id"
                               :label="item.name"
                               :value="item.id"
                               :disabled="batchQueryForm.orderType!==item.id" />
                  </el-select>
                </el-form-item>

                <el-form-item label="工单模板"
                              prop="orderTemplate"
                              class="userGroup">
                  <el-select v-model="batchQueryForm.orderTemplate"
                             @change="getCurrtNodeLists"
                             placeholder="请选择筛选条件">
                    <el-option v-for="item in orderTemplateList"
                               :key="item.id"
                               :label="item.name"
                               :value="item.id" />
                  </el-select>
                </el-form-item>

                <el-form-item label="问题分类"
                              class="userGroup">
                  <el-cascader v-model="batchQueryForm.questionType"
                               :options="options"
                               :props="optionProps"
                               clearable></el-cascader>
                </el-form-item>
                <el-form-item label="当前状态"
                              prop="state"
                              class="userGroup">
                  <el-select v-model="batchQueryForm.state"
                             :disabled="['4','3','2','1'].includes(this.secActiveIndex)"
                             placeholder="请选择筛选条件"
                             @change="batchQueryForm.currentNode=''">
                    <el-option key="1"
                               label="处理中"
                               value="1" />
                    <el-option key="0"
                               label="待领取"
                               value="0" />
                    <!-- <el-option key="2" label="已结单" value="2" />
                    <el-option key="3" label="退回" value="3" />
                    <el-option key="4" label="异常" value="4" />
                    <el-option key="5" label="作废" value="5" />-->
                  </el-select>
                </el-form-item>
                <el-form-item label="客户所在地">
                  <el-select v-model="batchQueryForm.customerLoca"
                             placeholder="请选择筛选条件"
                             clearable>
                    <el-option v-for="item in customerAddList"
                               :key="item.id"
                               :label="item.sourceName"
                               :value="item.id" />
                  </el-select>
                </el-form-item>
                <!-- v-if="batchQueryForm.state=='1'||batchQueryForm.state=='0'" -->
                <el-form-item label="节点选择"
                              prop="currentNode"
                              class="userGroup">
                  <el-select v-model="batchQueryForm.currentNode"
                             placeholder="请选择筛选条件"
                             clearable>
                    <el-option v-for="item in CurrtNodeList"
                               :key="item.id"
                               :label="item.nodeName"
                               :value="item.id" />
                  </el-select>
                </el-form-item>

                <el-form-item label="当前受理人"
                              class="userGroup">
                  <el-input v-model="batchQueryForm.currentProcessor"
                            :disabled="['1', '2', '3', '5'].includes(secActiveIndex)"></el-input>
                </el-form-item>
                <el-form-item label="工单创建人"
                              class="userGroup">
                  <el-input v-model="batchQueryForm.founder"
                            :disabled="secActiveIndex==6"></el-input>
                </el-form-item>
                <el-form-item label="工单发起时间"
                              class="userGroup">
                  <el-date-picker v-if="batchSelectDialog"
                                  v-model="batchQueryForm.createTime"
                                  type="daterange"
                                  range-separator="至"
                                  start-placeholder="开始日期"
                                  end-placeholder="结束日期"
                                  format="yyyy-MM-dd HH:mm:ss"
                                  value-format="yyyy-MM-dd HH:mm:ss"
                                  :default-time="['00:00:00', '23:59:00']"></el-date-picker>
                </el-form-item>
                <el-form-item type="flex"
                              justify="end"
                              align="end"
                              class="assSubmit">
                  <xyy-button type="normal"
                              @click="batchSelectDialog=false">取 消</xyy-button>
                  <xyy-button class="queryAss"
                              type="primary"
                              @click="getBatchList(true)">确定</xyy-button>
                </el-form-item>
              </el-form>
            </el-dialog>
          </el-row>
          <!-- 工单批量分配Dialog -->
          <el-row class="assignmentSheet">
            <el-dialog :visible.sync="batchMatchDialog"
                       title="工单分配"
                       width="420px">
              <el-form :model="batchMatchForm"
                       :rules="batchSearchRules"
                       ref="batchTransferForm"
                       label-width="88px"
                       class="demo-ruleForm">
                <el-row class="sheetNumber">
                  <el-radio v-model="assRadioSign"
                            label="people">转移给人</el-radio>
                  <el-radio v-model="assRadioSign"
                            label="node">转移给节点</el-radio>
                </el-row>
                <el-form-item label="节点选择"
                              prop="nodeId"
                              class="userGroup">
                  <el-select v-model="batchMatchForm.nodeId"
                             @change="getListUserGroupsByNodeId"
                             placeholder="请选择节点">
                    <el-option v-for="item in CurrtNodeList"
                               :key="item.id"
                               :label="item.nodeName"
                               :value="item.id" />
                  </el-select>
                </el-form-item>
                <el-form-item label="用户组"
                              class="userGroup"
                              prop="userGroupId"
                              v-if="assRadioSign =='people'">
                  <el-select v-model="batchMatchForm.userGroupId"
                             @change="getUserLists"
                             placeholder="请选择用户组">
                    <el-option v-for="(item,index) in UserGroupList"
                               :key="index"
                               :label="item.userGroupName"
                               :value="item.userGroupId" />
                  </el-select>
                </el-form-item>
                <el-form-item label="组内成员"
                              class="userGroup"
                              prop="userId"
                              v-if="assRadioSign =='people'">
                  <el-select v-model="batchMatchForm.userId"
                             placeholder="请选择组内成员">
                    <el-option v-for="item in GroupUserList"
                               :key="item.staffNum"
                               :label="item.nickname"
                               :value="item.userId" />
                  </el-select>
                </el-form-item>
                <el-form-item type="flex"
                              justify="end"
                              align="end"
                              class="assSubmit">
                  <xyy-button type="normal"
                              @click="batchMatchDialog=false">取 消</xyy-button>
                  <xyy-button class="queryAss"
                              type="primary"
                              @click="handleBatchTransfer">确定</xyy-button>
                </el-form-item>
              </el-form>
            </el-dialog>
          </el-row>
          <!-- 确认转移工单提示 -->
          <el-dialog :visible.sync="batchTipsDialog"
                     width="400px">
            <span>确定批量转移工单?</span>
            <span slot="footer"
                  class="dialog-footer">
              <el-button @click="batchTipsDialog = false">取 消</el-button>
              <el-button type="primary"
                         @click="handleBatchTransfer(true)">确 定</el-button>
            </span>
          </el-dialog>
          <!-- 转移失败工单列表 -->
          <el-dialog :visible.sync="batchFailDialog"
                     width="420px">
            <!-- style="color:red;" -->
            <span>以下工单转出成功</span>
            <div style="max-height:500px;overflow-y:scroll;">
              <div v-for="item in batchFailList"
                   :key="item.workorderId"
                   style="padding-top:16px;display: flex;">
                <div style="width: 200px;padding: 0 20px;">{{ item.workorderNum }}</div>
                <div style="width: 130px;">{{ item.workTypeName }}</div>
              </div>
            </div>
            <span slot="footer"
                  class="dialog-footer">
              <el-button type="primary"
                         @click="batchFailDialog = false">确 定</el-button>
            </span>
          </el-dialog>
        </div>
      </template>
    </xyy-list-page>
  </div>
</template>

<script>
import {
  listMyCC,
  listIInitiated,
  listMyProcessed,
  listWorkorderByState,
  receivingWorkorder,
  getSheetTypeList,
  listAllStateWorkorder,
  listUnclaimedWorkorder,
  workorderListCount,
  getDraftList,
  delDraft,
  getWorkorderDraftById,
  getNodeGroupInfo,
  getUserList,
  assignmentUorkorder,
  getLatestFlowInfo,
  getCurrentUrgeCon,
  urgeWorkeApi,
  saveFlowStartTime,
  startExport,
  getTagList,
  putSwitchTag,
  getTypeformat,
  getCustomerSourceList,
  getCurrtNodeList,
  listUserGroupsByNodeId,
  seleSheetList,
  listAllWorkorderByBatch,
  setBatchTransfer,
  getListNodesByFormId,
  getUserGroupsByNodeIdAndWorkorderIds,
  getUserInfo,
  deleteTag,
  getNodeListByWorkOrderIdInfo,
  listMyFocusOn,
  receivingWorkorderByWorkOrderIds
} from '@/api/mySheetManage';
import exporTip from './components/exportTip';
import Draft from './components/draft';
import utils from '@/utils/filter';
import openInfo from './components/openInfo';
import { adapter, adapterList } from '@/utils/tools.js';
import pubUrl from './mixin/pubUrl';
import queryMore from './components/queryMore';
export default {
  name: 'SheetList',
  components: {
    Draft,
    openInfo,
    exporTip,
    queryMore,
  },
  filters: {
    dateFormat (val) {
      return utils.dataTime(val, 'yy-mm-dd HH:ss:nn');
    },
  },
  mixins: [pubUrl],
  inject: ['reload'],
  data () {
    return {
      time: null,
      time2: null,
      time3: null,
      time4: null,
      urgeWorkHid: false, // 催单
      currentProcessPersonName: '',
      currentNodeName: '',
      urgeWorkCon: '',
      urgeFlowId: '',
      urgeWorkorderId: '',
      assignmentDialog: false, // 工单分配
      CurrentNodeGroupInfo: [], //
      assSheet: {
        userGroupVal: '',
        memberVal: '',
      },
      memberArray: [],
      assRules: {
        userGroupVal: [
          { required: true, message: '请选择用户组名称', trigger: 'change' },
        ],
        memberVal: [
          { required: true, message: '请选择组内成员', trigger: 'change' },
        ],
      },
      workorderNum: '',
      allIsRed: false,
      isNewMain: false,
      isNewSub: false,
      draftData: [],
      draftPage: 1,
      draftHasNextPage: false,
      dialogVisible: false,
      formTypeId: '', // 标签类型/工单类型 all
      allType: [], // 工单类型列表
      allState: false,
      unclaimedState: false,
      secActiveIndex: '1', // 激活的index
      isCondition: false,
      formInline: {
        sheetNum: '', // 工单编号
        customerName: '', // 客户名称
        currentState: '全部', // 查询状态
        dataRange: '', // 时间范围
        orderNum: '', // 订单编号
        customerPhoneNumber:'',//客户电话
        customer_id: '', //客户ID
        creator: '', //工单创建人
        merchantId: '', // 商家ID
      },
      isNeedMore: false,
      show_more: false,
      restCountFormType: [],
      restCountStateWorkorder: [],
      restTimeoutStateWorkorder: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      list: [],
      col: [
        { index: 'workorderNum', width: 200, name: '工单编号', slot: true },
        //{ index: 'formTypeName', width: 200, name: '工单类型', slot: true },
        {
          index: '1164456058157142016',
          name: '客户名称',
          width: 200,
          slot: true,
        },
        { index: 'customer_id', name: '客户ID', width: 150 },
        {
          index: '1164723822566445056',
          name: '客户电话',
          width: 150,
          slot: true,
        },
        { index: '1870039678284075008', name: '商家ID', width: 150 },
        {
          index: '1164724692221825024',
          name: '问题分类',
          width: 200,
          slot: true,
        },
        {
          index: 'currentState',
          name: '当前状态',
          width: 90,
          slot: true,
        },
        {
          index: 'currentProcessingPersonName',
          name: '当前受理人',
          width: 150,
          slot: true,
        },
        {
          index: 'recentAcceptanceTime',
          name: '最近受理时间',
          width: 200,
          slot: true,
          sortable: 'custom',
        },
        {
          index: 'latestSubmissionTime',
          name: '最近提交时间',
          width: 200,
          slot: true,
          sortable: 'custom',
        },
        {
          index: 'creatorName',
          name: '工单创建人',
          width: 150,
          slot: true,
        },
        {
          index: 'gmtCreate',
          name: '工单发起时间',
          width: 200,
          slot: true,
          sortable: 'custom',
        },
        {
          index: '1164456270271483904',
          name: '优先级',
          width: 80,
          slot: true,
        },
        {
          index: 'endPersonName',
          name: '工单结单人',
          width: 150,
          slot: true,
        },
        {
          index: 'completionTime',
          name: '工单完成时间',
          width: 200,
          slot: true,
          sortable: 'custom',
        },
        {
          index: 'processingDuration',
          name: '工单处理时长',
          width: 200,
          slot: true,
        },
        {
          index: '1164723673773510656',
          name: '来源渠道',
          width: 150,
          slot: true,
        },
        {
          index: 'customerSatisfaction',
          name: '满意度评价',
          width: 100,
          slot: true,
        },
        {
          index: 'evaluategmtCreate',
          name: '评价时间',
          width: 200,
          slot: true,
          sortable: 'custom',
        },
        {
          index: 'starLevel1',
          name: '评价维度1',
          width: 150,
          slot: true,
        },
        {
          index: 'starLevel2',
          name: '评价维度2',
          width: 150,
          slot: true,
        },
        {
          index: 'evaluateRateReply',
          name: '评价回复',
          width: 150,
          slot: true,
        },
        {
          index: 'tag',
          name: '标签',
          width: 150,
          slot: true,
        },
        { index: 'operation', name: '操作', width: 220, slot: true },
      ],
      colYBM: [
        { index: 'workorderNum', width: 200, name: '工单编号', slot: true },
        {
          index: '1164456058157142016',
          name: '客户名称',
          width: 200,
          slot: true,
        },
        { index: 'customer_id', name: '客户ID', width: 150 },
        {
          index: '1164723822566445056',
          name: '客户电话',
          width: 150,
          slot: true,
        },
        { index: '1870039678284075008', name: '商家ID', width: 150 },
        {
          index: '1164724692221825024',
          name: '问题分类',
          width: 200,
          slot: true,
        },
        {
          index: '1164456270271483904',
          name: '优先级',
          width: 80,
          slot: true,
        },
        {
          index: 'currentState',
          name: '当前状态',
          width: 90,
          slot: true,
        },
        {
          index: 'currentProcessingPersonName',
          name: '当前受理人',
          width: 150,
          slot: true,
        },
        {
          index: 'recentAcceptanceTime',
          name: '最近受理时间',
          width: 200,
          slot: true,
          sortable: 'custom',
        },
        {
          index: 'creatorName',
          name: '工单创建人',
          width: 150,
          slot: true,
        },
        {
          index: 'endPersonName',
          name: '工单结单人',
          width: 150,
          slot: true,
        },
        {
          index: 'completionTime',
          name: '工单完成时间',
          width: 200,
          slot: true,
          sortable: 'custom',
        },
        {
          index: 'customerSatisfaction',
          name: '满意度评价',
          width: 100,
          slot: true,
        },
        {
          index: 'evaluategmtCreate',
          name: '评价时间',
          width: 200,
          slot: true,
          sortable: 'custom',
        },
        {
          index: 'starLevel1',
          name: '评价维度1',
          width: 150,
          slot: true,
        },
        {
          index: 'starLevel2',
          name: '评价维度2',
          width: 150,
          slot: true,
        },
        {
          index: 'evaluateRateReply',
          name: '评价回复',
          width: 150,
          slot: true,
        },
        {
          index: 'tag',
          name: '标签',
          width: 150,
          slot: true,
        },
        { index: 'operation', name: '操作', width: 220, slot: true },
      ],
      operation: [
        {
          name: '查看',
          type: 1,
        },
        {
          name: '去关闭',
          type: 2,
        },
        {
          name: '催单',
          type: 3,
        },
        {
          name: '去处理',
          type: 4,
        },
      ],
      hoverData: [],
      workorderId: '',
      flowId: '',
      hoverItem: [],
      changeExport: false,
      currentUrl: '',
      timer: null,
      batchSelection: true, // 1.7.4工单批量选择flag
      batchSelectDialog: false, // 批量查询Dialog
      batchMatchDialog: false, // 批量分配Dialog
      batchTipsDialog: false, // 转移前提示Dialog
      batchFailDialog: false, // 转移失败Dialog
      batchQueryForm: {
        orderType: '', // 工单类型
        questionType: '', // 问题分类
        state: '', // 当前状态
        customerLoca: '', // 客户所在地
        currentProcessor: '', // 当前受理人
        founder: '', // 工单创建人
        createTime: '', //工单发起时间
        orderTemplate: '', // 工单模板
        currentNode: '', //当前节点
      }, // 批量查询表单
      batchMatchForm: {
        nodeId: '',
        userGroupId: '',
        userId: '',
      }, // 批量转移选择表单
      assRadioSign: 'people',
      batchSelected: [], // 批量转移工单
      options: [], //问题分类数据
      CurrtNodeList: [], // 节点选择列表
      customerAddList: [], // 客户所在地列表
      orderTemplateList: [], // 工单模板列表
      UserGroupList: [], // 用户组列表
      GroupUserList: [], // 组内用户列表
      batchFailList: [], // 转移失败工单列表
      batchSearchRules: {
        orderType: [
          { required: true, message: '请选择工单类型', trigger: 'change' },
        ],
        orderTemplate: [
          { required: true, message: '请选择工单模板', trigger: 'change' },
        ],
        state: [
          { required: true, message: '请选择当前状态', trigger: 'change' },
        ],
        // currentNode: [
        //   { required: true, message: '请选择节点', trigger: 'change' },
        // ],

        nodeId: [{ required: true, message: '请选择节点', trigger: 'change' }],
        userGroupId: [
          { required: true, message: '请选择用户组', trigger: 'change' },
        ],
        userId: [
          { required: true, message: '请选择组内成员', trigger: 'change' },
        ],
      },
      optionProps: {
        value: 'typeCode',
        label: 'typeFullName',
      }, // 问题分类字段适配
      tags: [],
      queryMoreVisible: false, //更多查询条件是否可见
      queryType: '1', //1:简易查询,2:更多查询 //页面维护的查询方式，用于分页判断
      assignParams: '', //保存 查询更多 组件设置的条件,
      userStatus: 1,

      isFromCreatedHook: false, //activated钩子是否从created钩子顺序执行

      orderBy: '', //排序规则 ascending(升序)、descending(降序)
      propField: '', //排序字段
      draftRequestState: false, // 编辑草稿请求状态
      isYBMCode: false // 业务线
    };
  },
  computed: {
    isDisabledTag () {
      return !this.$store.getters.validBtn.some((item) => {
        return item.code === 'btn:cs:workordertag';
      });
    },
    filterCol () {
      let colView = this.col
      let colYBMView = this.colYBM
      if(!this.isYBMCode) {
        colView = colView.filter(colItem => colItem.index !== 'customer_id' && colItem.index !== '1870039678284075008') // 客户ID && 商家ID
        colYBMView = colYBMView.filter(colItem => colItem.index !== 'customer_id' && colItem.index !== '1870039678284075008') // 客户ID && 商家ID
      }

      return this.tags.length
        ? (this.isYBMCode?colYBMView:colView)
        : (this.isYBMCode?colYBMView.filter((item) => {
          return item.index !== 'tag';
        }):colView.filter((item) => {
          return item.index !== 'tag';
        }));
    },

    // 批量领取工单,按钮visible,只有"待领取"页面才显示
    getNewSheetBatchVisible: function(){
      return this.$route.path.includes('/listUnclaimedWorkorder')
    }
  },
  watch: {
    $route (val, old) { },
    list: {
      handler (val, oldVal) {
        const that = this;
        if (val && val.length > 0) {
          const worderId = val.map((item) => item.id);
          // 获取hoverData
          getLatestFlowInfo(worderId).then((res) => {
            // console.log('获取hoverData:', res);
            if (res.code === 1) {
              that.hoverData = res.data;
            } else {
              // console.log(res.msg);

              that.$XyyMessage.error(res.msg);
            }
          });
        }
      },
      deep: true,
    },
    batchSelection () {
      this.batchSelected = [];
      this.batchMatchForm.nodeId = '';
      this.batchMatchForm.userGroupId = '';
      this.batchMatchForm.userId = '';
      this.$refs.orderTable.clearSelection();
    },
    assRadioSign () {
      this.batchMatchForm.userGroupId = '';
      this.batchMatchForm.userId = '';
    },
    // 去除空格
    /* formInline: {
      handler(val, oldVal) {
        this.formInline.sheetNum = val.sheetNum.trim();
        this.formInline.customerName = val.customerName.trim();
        this.formInline.orderNum = val.orderNum.trim();
        this.formInline.customerPhoneNumber = val.customerPhoneNumber.trim();
      },
      deep: true
    } */
  },
  created () {
    this.isFromCreatedHook = true;
    this.getBusinessPartCode();
  },
  activated: function () {
    this.currentUrl = this.$route.path;
    this.formTypeId = this.$route.meta.query.id;
    this.secActiveIndex = this.$route.query.noticeType
      ? this.$route.query.noticeType
      : this.$route.meta.query.status
        ? this.$route.meta.query.status
        : '1';

    /*
    if (this.secActiveIndex == 4) {
      const listQuery = {
        page: 1,
        pageSize: 10,
      };
      this.seachDataList(listQuery);
    }
    */

    if (!this.isFromCreatedHook) {
      let reloadRouteList = this.$store.getters.orderModifyFlagtoList.filter(
        (item) => {
          return item === this.$route.path;
        }
      );

      if (reloadRouteList.length) {
        //根据条件重新请求列表数据
        if (this.batchSelection) {
          this.getBatchList();
        } else {
          this.seachDataList(
            Object.assign({}, this.listQuery, {
              assignParams: this.assignParams,
            })
          );
        }
        //删除当前路由
        this.$store.commit('workSheet/DEL_MODIFY_FLAG', this.$route.path);
      }
    }
  },
  deactivated () {
    this.isFromCreatedHook = false;
  },
  mounted: function () {
    setInterval(() => {
      this.userStatus = this.$store.state.message.Userstatus;
    }, 500);
    this.currentUrl = this.$route.path;
    this.batchQueryForm.orderType = this.formTypeId = this.$route.meta.query.id;

    this.secActiveIndex = this.$route.query.noticeType
      ? this.$route.query.noticeType
      : this.$route.meta.query.status
        ? this.$route.meta.query.status
        : '1';
    const listQuery = {
      page: 1,
      pageSize: 10,
    };
    this.seachDataList(listQuery);
    this.getSheetTypeList(); // 获取表单类型
    // this.getSheetTypeList(preParam); // 获取表单类型
    // this.getDraftList(); // 获取草稿信息
    this.getTags(); // 获取标签字典
    this.getCustomerSourceLists(); // 获取客户所在地
    // this.getSeleSheetList(this.batchQueryForm.orderType); // 获取工单模板列表
    setTimeout(() => {
      this.getTypeformatList(this.batchQueryForm.orderType); // 获取问题分类列表
    }, 100);
    // 批量转移默认逻辑
    // 我处理中的:当前状态为处理中，当前受理人为当前用户，不可更改
    // 我处理-已超时与即将超时:当前受理人为当前用户，不可更改
    // 我已处理的:当前受理人为当前用户，不可更改
    // 我发起的，工单创建人为当前用户，不可修改
    this.getUserInfos();
    // case '1':
    //   return '已超时';
    // case '2':
    //   return '即将超时';
    // case '3':
    //   return '我处理中的';
    // case '4':
    //   return '待领取的';
    // case '5':
    //   return '我已处理的';
    // case '6':
    //   return '我发起的';
    // case '7':
    //   return '抄送给我的';
    // case '8':
    //   return '全部';
    if (['1', '2', '3'].includes(this.secActiveIndex)) {
      this.batchQueryForm.state = '1';
    }
    if (['4'].includes(this.secActiveIndex)) {
      this.batchQueryForm.state = '0';
    }
  },
  methods: {
    adapter (data) {
      return adapter(data);
    },
    handleExoprClose () {
      this.changeExport = false;
    },
    handleChangeExport (info) {
      const that = this;
      this.changeExport = false;
      if (info === 'go') {
        that.$router.push({
          path: '/other/index/',
        });
      }
    },

    //催单显示信息
    urgeWorkCheck (row) {
      const that = this;
      that.urgeFlowId = row.flowId;
      that.urgeWorkorderId = row.id;
      getCurrentUrgeCon(row.flowId, row.id).then((res) => {
        if (res.code) {
          that.currentNodeName = res.data.currentNodeName;
          that.currentProcessPersonName = res.data.currentProcessingPersonName;
          that.urgeWorkHid = true;
        } else {
          that.$XyyMessage.warning(res.msg);
        }
      });
    },
    // 催单
    urgeWork () {
      const that = this;
      if (that.urgeWorkCon === '') {
        that.$XyyMessage.error('请输入催单内容！');
        return false;
      }
      const param = {
        flowId: that.urgeFlowId,
        urgeContents: that.urgeWorkCon,
        workorderId: that.urgeWorkorderId,
      };
      urgeWorkeApi(param).then((res) => {
        if (res.code) {
          that.urgeWorkCon = '';
          that.$XyyMessage.success('催单成功！');
        } else {
          that.urgeWorkCon = '';
          that.$XyyMessage.error(res.msg);
        }
        that.urgeWorkHid = false;
      });
    },
    urgeWorkCalce () {
      this.urgeWorkCon = '';
      this.urgeWorkHid = false;
    },
    handleCellMouseEnter (row, column, cell, event) {
      this.hoverItem = this.hoverData.find(
        (item) => item.workorderId == row.id
      );
      // console.log('this.hoverItem:', this.hoverItem);
    },
    // 工单分配查询开始
    sheetAssignment (row) {
      const that = this;
      // console.log('rowId:', row.id);

      getNodeGroupInfo(row.id).then((res) => {
        if (res.code) {
          that.workorderId = row.id;
          that.workorderNum = row.workorderNum;
          that.flowId = row.flowId;
          that.CurrentNodeGroupInfo = res.data;
          that.assignmentDialog = true;
        } else {
          that.$XyyMessage.error(res.msg);
        }
      });
    },
    handleChangeUserGroup (userGroupVal) {
      const that = this;
      this.assSheet.memberVal = '';
      this.memberArray = [];
      getUserList(userGroupVal).then((res) => {
        that.memberArray = res.data;
        // console.log('memberArray:', that.memberArray);
      });
    },
    queryAss (formName) {
      const that = this;
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const param = {
            flowId: that.flowId,
            processingPersonGroupId: that.assSheet.userGroupVal,
            processingPersonGroupName: that.CurrentNodeGroupInfo.find(
              (item) => item.userGroupId == that.assSheet.userGroupVal
            ).userGroupName,
            processingPersonId: that.assSheet.memberVal,
            processingPersonName: that.memberArray.find(
              (item) => item.userId == that.assSheet.memberVal
            ).nickname,
            workorderId: that.list.find((item) => item.id == that.workorderId)
              .id,
            workorderNum: that.workorderNum,
          };
          // console.log('工单分配请求参数：', param);
          assignmentUorkorder(param).then((res) => {
            if (res.code === 1) {
              that.$XyyMessage.success('工单分配成功');
              that.assSheet.userGroupVal = '';
              that.assSheet.memberVal = '';
              this.assignmentDialog = false;
              that.seachDataList({
                page: that.listQuery.page,
                pageSize: that.listQuery.pageSize,
                assignParams: this.assignParams,
              });
            } else {
              that.assSheet.userGroupVal = '';
              that.assSheet.memberVal = '';
              that
                .$confirm(res.msg, '提示', {
                  confirmButtonText: '确定',
                  showCancelButton: false,
                  type: 'warning',
                })
                .then(() => {
                  that.assignmentDialog = false;
                  that.seachDataList({
                    page: that.listQuery.page,
                    pageSize: that.listQuery.pageSize,
                    assignParams: this.assignParams,
                  });
                })
                .catch(() => { });

              // that.$XyyMessage.error(res.msg);
            }
          });
        } else {
          // console.log('error submit!!');
          // this.assignmentDialog = true;
          return false;
        }
      });
    },
    assCacel () {
      this.assSheet.userGroupVal = '';
      this.assSheet.memberVal = '';
      this.memberArray = [];
      this.assignmentDialog = false;
    },
    initAss (formName) {
      this.$nextTick(() => {
        this.$refs[formName].resetFields();
      });
    },
    dateTimeFocus () {
      // 条件时间查询
      const that = this;
      that.$nextTick(function () {
        document
          .getElementsByClassName('el-picker-panel')[0]
          .addEventListener('click', function () {
            that.$refs.messageDrop.show();
          });
        document.getElementsByClassName('el-time-panel')[1].style.width =
          133 + 'px';
      });
    },
    queryTime () {
      this.$refs.messageDrop.show();
    },
    draftMenuLoad () {
      if (this.draftHasNextPage) {
        this.draftPage += 1;
        this.getDraftList(this.draftPage);
      }
    },
    getDraftList (pageNum = 1, pageSize = 4) {
      const that = this;
      getDraftList({ pageNum: pageNum, pageSize: pageSize }).then(
        (response) => {
          if (response.code === 1) {
            that.draftData = that.draftData.concat(response.data.list);
            that.draftHasNextPage = response.data.hasNextPage;
            if (that.draftData !== false) {
              // 时间初始化
              // that.draftData.forEach(item => {
              //   item.gmtCreate = utils.dataTime(
              //     item.gmtCreate,
              //     'yy-mm-dd HH:ss:nn'
              //   );
              // });
            }
          } else {
            that.$XyyMessage.error(response.msg);
          }
        }
      );
    },
    draftVisibleChange () {
      // 显示草稿箱
      this.draftPage = 1;
      this.draftHasNextPage = false;
      this.draftData.splice(0, this.draftData.length);
      this.getDraftList(this.draftPage);
    },
    handleEdit (item) {
      const that = this;
      // 草稿编辑
      if (that.draftRequestState) return; // 防止多次点击取消请求导致的报错
      that.draftRequestState = true;
      getWorkorderDraftById(item.id, item.formId, item.templateId).then(
        (res) => {
          if (res.code === 1) {
            const {
              formId,
              formName,
              formTypeId,
              formTypeName,
              layout,
              draft,
              id,
              im_field
            } = res.data;
            that.$router.push({
              path: 'newSheet/' + item.id,
              query: {
                edit: true,
                formId,
                formName,
                formTypeId,
                formTypeName,
                layout,
                draft,
                id,
                im_field,
                currentUrl: that.currentUrl,
                editSuccess: "false"
              },
            });
          } else {
            this.$XyyMessage.error(res.msg || '服务异常');
          }
          // 经产品确认，获取草稿失败并不需要提示和删除草稿,待后续版本删除
          // else {
          //   const that = this;
          //   that.$XyyMsg({
          //     title: '提示',
          //     content: res.msg,
          //     closeBtn: false,
          //     onSuccess: function () {
          //       // 草稿删除
          //       that.handleDelDraft(item.id, true);
          //     },
          //   });
          // }
          // return;
        }
      ).catch(() => {

      }).finally(() => {
        that.draftRequestState = false;
      })
    },
    handleDelDraft (itemId, isQuery) {
      const that = this;
      if (!isQuery) {
        that.$XyyMsg({
          title: '提示',
          content: '确定删除此草稿？',
          onSuccess: function () {
            // 草稿删除
            delDraft(itemId).then((res) => {
              if (res.code === 1) {
                that.$XyyMessage.success('删除成功！');
                that.draftData.forEach((el, index) => {
                  if (el.id == itemId) {
                    that.draftData.splice(index, 1);
                  }
                });
              }
            });
          },
        });
      } else {
        delDraft(itemId).then((res) => {
          if (res.code === 1) {
            that.$XyyMessage.success('删除成功！');
            that.draftData.forEach((el, index) => {
              if (el.id == itemId) {
                that.draftData.splice(index, 1);
              }
            });
          }
        });
      }
    },
    newList () {
      const that = this;
      function secActiveName () {
        switch (that.secActiveIndex) {
          case '1':
            return '已超时';
          case '2':
            return '即将超时';
          case '3':
            return '我处理中的';
          case '4':
            return '待领取的';
          case '5':
            return '我已处理的';
          case '6':
            return '我发起的';
          case '7':
            return '抄送给我的';
          case '8':
            return '全部';
          case '9':
            return '我关注的';
        }
      }
      const formTypeId = that.formTypeId === '' ? 0 : that.formTypeId;
      that.$router.push({
        path:
          '/' +
          this.pubUrl +
          '/newList/' +
          formTypeId +
          '/' +
          that.secActiveIndex,
        query: {
          formTypeId: that.formTypeId,
          secActiveIndex: that.secActiveIndex,
          secActiveName: secActiveName(),
          formName: that.formTypeId
            ? that.allType.filter((el) => el.id == that.formTypeId)[0].name
            : '全部',
        },
      });
    },
    filterOper (row) {
      //row
      //   seeDetails: true, // 是否可以查看
      //   toDealWith: false, // 是否可以去处理
      //   toClose: false, // 是否可以去关闭工单
      //   urge: true, // 是否可以去催办工单

      //operation
      // { name: '查看',type: 1},
      // { name: '去关闭',type: 2},
      // { name: '催单',type: 3},
      // { name: '去处理',type: 4}

      //currentState
      //0:待领取
      //1:处理中
      //2:已结单
      //3:退回
      //4:异常
      //5:作废

      /*
      return this.operation.filter((el) => {
        return (
          row.toDealWith
            ? el.type === 4
            : (row.toClose
                ? el.type === 2
                : (row.seeDetails && row.urge && row.currentState != 2
                    ? el.type !== 2 && el.type !== 4
                    : (row.seeDetails && !row.urge
                        ? el.type !== 2 && el.type !== 4 && el.type !== 3
                        : (row.urge && row.currentState != 2
                            ? el.type !== 2 && el.type !== 4
                            : el.type === 1
                          )
                      )
                  )
              )
        )
      });
      */

      let operType=[];
      if(row.toDealWith){
        operType.push(4);
      }else{
        if(row.toClose){
          operType.push(2);
        }else{
          if(row.seeDetails && row.urge && row.currentState != 2){
            operType.push(1);
            operType.push(3);
          }else{
            if(row.seeDetails && !row.urge){
              operType.push(1);
            }else{
              if(row.urge && row.currentState != 2){
                operType.push(1);
                operType.push(3);
              }else{
                operType.push(1);
              }
            }
          }
        }
      }

      return this.operation.filter(item=>{
        return operType.includes(item.type);
      })
    },

    // 工单列表统计
    workorderListCount () {
      const that = this;
      const preParam = {};
      if (that.formTypeId !== '') {
        preParam.formTypeId = that.formTypeId;
      }
      return workorderListCount(preParam)
        .then((response) => {
          const {
            restCountFormType,
            restCountStateWorkorder,
            restTimeoutStateWorkorder,
          } = response.data;
          that.restCountFormType = restCountFormType;
          that.restCountStateWorkorder = restCountStateWorkorder;
          that.restTimeoutStateWorkorder = restTimeoutStateWorkorder;
          that.allIsRed =
            restCountStateWorkorder.some((el) => {
              return (
                (el.currentState === 0 || el.currentState === 1) &&
                el.countTotal > 0
              );
            }) ||
            restTimeoutStateWorkorder.some((el) => {
              return (
                (el.currentState === 0 || el.currentState === 1) &&
                el.countTotal > 0
              );
            });
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    isRedSign (formTypeId) {
      // 工单类型是否红色标记
      let isRedSign = false;
      if (this.restCountFormType && this.restCountFormType.length > 0) {
        this.restCountFormType.forEach(function (item) {
          if (item.countFormType == formTypeId && item.countTotal > 0) {
            isRedSign = true;
          }
        });
      }
      return isRedSign;
    },
    overTimeNum (status) {
      // 超时数量
      let countTotal = 0;
      if (
        this.restTimeoutStateWorkorder &&
        this.restTimeoutStateWorkorder.length > 0
      ) {
        this.restTimeoutStateWorkorder.forEach(function (item) {
          if (item.timeoutState === status && item.countTotal > 0) {
            countTotal = item.countTotal >= 100 ? '99+' : item.countTotal;
          }
        });
      }
      if (countTotal === 0) {
        return '';
      }
      return '(' + countTotal + ')';
    },
    restWorkorder (status) {
      // 我处理中的--待领取的
      let countTotal = 0;
      if (
        this.restCountStateWorkorder &&
        this.restCountStateWorkorder.length > 0
      ) {
        this.restCountStateWorkorder.forEach(function (item) {
          if (item.currentState === status && item.countTotal > 0) {
            countTotal = item.countTotal >= 100 ? '99+' : item.countTotal;
          }
        });
      }
      if (countTotal === 0) {
        return '';
      }
      return '(' + countTotal + ')';
    },
    handleClose (done) {
      // 工单领取
      this.$confirm('确认关闭？')
        .then((_) => {
          done();
        })
        .catch((_) => { });
    },
    toNewSheet: function () {
      const url = `/${this.pubUrl}/newSheet/${new Date().getTime()}`;
      this.$router.push({
        path: url,
        query: { formTypeId: this.formTypeId, currentUrl: this.currentUrl },
      });
    },



    // 领取工单
    getNewSheet: function () {
      // 激活领取工单
      const that = this;
      that.$XyyMsg({
        title: '提示',
        content: '确定领取工单？',
        onSuccess: function () {
          that.handleGetSheet();
        },
      });
    },
    handleGetSheet () {
      const that = this;
      receivingWorkorder({
        formTypeId: that.formTypeId,
      })
        .then((response) => {
          // console.log('领取工单:', response);
          const data = response.data;
          if (response.code === 0) {
            that.$message({
              showClose: true,
              message: '当前没有待领取的工单',
              type: 'info',
            });
          } else {
            that.formTypeId = '';
            that.secActiveIndex = '3';
            // that.seachDataList();
            that.$message({
              showClose: true,
              message: '已领取' + data + '条工单',
              type: 'success',
            });

            //需要刷新对应分类工单下的待领取页面
            this.$store.commit(
              'workSheet/SET_MODIFY_FLAG',
              `/${that.pubUrl}/listUnclaimedWorkorder`
            );

            this.$router.push({
              path: '/' + this.pubUrl + '/listMyProcessingC1',
              query: {
                t: new Date().getTime(),
              },
            });
          }
        })
        .catch(function (error) {
          that.$XyyMessage.error(error);
        });
    },
    // 批量领取工单
    getNewSheetBatch() {
      if (!this.batchSelected.length) {
        this.$XyyMessage.warning('请选择要领取的工单');
        return;
      }
      if(this.batchSelected.length > 10) {
        this.$XyyMessage.warning('单次领取工单数不能超过10条');
        return;
      }
      this.$XyyMsg({
        title: '提示',
        content: '确定批量领取工单？',
        onSuccess: () => {
          this.doReceivingWorkorderByWorkOrderIds()
        },
      });
    },
    async doReceivingWorkorderByWorkOrderIds() {
      try {
        const { code, data, msg } = await receivingWorkorderByWorkOrderIds({
          formTypeId: this.formTypeId,
          workorderIdList: this.batchSelected.map(sheet => sheet.id)
        })
        if (code == 1) {
          this.$XyyMsg({
            title: '提示',
            content: `成功领取${data}条工单` + (data != this.batchSelected.length ? `，剩余工单已被其他人领取。`: ``),
            closeBtn: false,
            onSuccess: () => {
              this.batchSelected = [] // 重置所选值
              this.$refs.orderTable.$refs.table.clearSelection()// 重置table的选中值

              this.formTypeId = '';
              this.secActiveIndex = '3';

              //需要刷新对应分类工单下的待领取页面
              this.$store.commit(
                'workSheet/SET_MODIFY_FLAG',
                `/${this.pubUrl}/listUnclaimedWorkorder`
              );

              this.$router.push({
                path: '/' + this.pubUrl + '/listMyProcessingC1',
                query: {
                  t: new Date().getTime(),
                },
              });
            },
          });
        } else {
          throw new Error(msg)
        }
      } catch(e){
        this.$XyyMessage.error(e.message);
      }
    },



    showMore: function () {
      this.show_more = !this.show_more;
      if (this.show_more === true) {
        this.isShowMore = false;
      } else {
        this.isShowMore = true;
      }
    },
    // 表单类型渲染
    getSheetTypeList (preParam) {
      const that = this;
      return getSheetTypeList(preParam)
        .then((response) => {
          if (response.msg === 'success') {
            const data = response.data;
            that.allType = data;
            // that.$nextTick(function() {
            //   // 动态计算展示条目高度 展示&&收起
            //   const showMore = that.$refs.showMore.offsetHeight;
            //   if (showMore > 53) {
            //     that.show_more = true;
            //     that.isNeedMore = true;
            //   } else {
            //     that.isNeedMore = false;
            //   }
            // });
          }
        })
        .catch(function (error) {
          that.$$XyyMessage.error(error);
        });
    },
    // 重置表单
    resetForm (formName) {
      //简易查询
      this.queryType = '1';
      this.assignParams = '';

      this.formInline.currentState = '全部';
      this.formInline.dataRange = '';
      this.$refs[formName].resetFields();
      this.listQuery = {
        page: 1,
        pageSize: 10,
      };
      this.batchSelection = false; // 重置批量转移状态
      this.orderBy = '';
      this.propField = '';
      this.$refs.orderTable.$refs.table.clearSort();
      this.seachDataList(this.listQuery);
    },
    // 表单类型切换
    handleSheetType: function (index) {
      this.formTypeId = index;
      this.isCondition = false;
      this.resetForm('formInline');
      this.listQuery.page = 1;
      this.listQuery.pageSize = 10;
      this.seachDataList(this.listQuery);
    },
    selectSubClass (index) {
      this.secActiveIndex = index;
      this.isCondition = false;
      this.resetForm('formInline');
      this.listQuery.page = 1;
      this.listQuery.pageSize = 10;
      this.seachDataList(this.listQuery);
    },
    // 条件查询
    handleCondition () {
      this.queryType = '1'; //简易查询
      this.assignParams = '';

      //重置时间字段排序
      this.orderBy = '';
      this.propField = '';
      this.$refs.orderTable.$refs.table.clearSort();

      this.isCondition = true;
      if (
        this.formInline.customerName !== '' ||
        this.formInline.dataRange !== '' ||
        this.formInline.sheetNum !== '' ||
        this.formInline.orderNum !== '' ||
        this.formInline.currentState !== '全部' ||
        this.formInline.customerPhoneNumber !=='' || 
        this.formInline.customer_id !== '' || 
        this.formInline.creator !== '' || 
        this.formInline.merchantId !== ''
      ) {
        const listQuery = {
          page: 1,
          pageSize: 10,
        };
        this.seachDataList(listQuery);
      } else {
        this.isCondition = false;
        const listQuery = {
          page: 1,
          pageSize: 10,
        };
        this.seachDataList(listQuery);
      }
    },
    // 查询列表数据
    seachDataList (listQuery) {
      this.batchSelection = false; // 重置批量转移状态
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0)',
      });
      const that = this;
      const { page, pageSize } = listQuery || this.listQuery;
      let preParam = {
        pageNum: page,
        pageSize: pageSize,
      };
      if (that.formTypeId === '') {
        //全部
        preParam.queryParameterJson = {};
      } else {
        preParam.queryParameterJson = Object.assign(
          {},
          {
            form_type_id: that.formTypeId,
          },
          this.$route.meta.query.queryParameterJson
        );
      }

      //判断是否是更多查询,2020.4.17,rl,v1.7.4
      //简易查询
      if (this.queryType === '1') {
        //客户名称
        if (that.formInline.customerName !== '') {
          preParam.queryParameterJson['s1164456058157142016'] = that.formInline.customerName;
        }
        //客户电话
        if(that.formInline.customerPhoneNumber !== ''){
          preParam.queryParameterJson['s1164723822566445056'] = that.formInline.customerPhoneNumber;
        }
        //客户ID
        if(that.formInline.customer_id !== ''){
          preParam.queryParameterJson['customer_id'] = that.formInline.customer_id;
        }
        //工单创建人
        if(that.formInline.creator !== ''){
          preParam.queryParameterJson['creator'] = that.formInline.creator;
        }
        // 商家ID
        if(that.formInline.merchantId !== '') {
          preParam.queryParameterJson['s1870039678284075008'] = that.formInline.merchantId;
        }

        //发起时间
        if (
          that.formInline.dataRange &&
          that.formInline.dataRange !== '' &&
          that.formInline.dataRange.length !== 1
        ) {
          preParam.queryParameterJson.query_start_time =
            that.formInline.dataRange[0];
          preParam.queryParameterJson.query_end_time =
            that.formInline.dataRange[1];
        }
        //工单编号
        if (that.formInline.sheetNum !== '') {
          preParam.queryParameterJson.workorder_num = that.formInline.sheetNum;
        }
        // 订单编号
        if (that.formInline.orderNum !== '') {
          preParam.queryParameterJson['s1176037234466492416'] =
            that.formInline.orderNum;
        }
        //工单状态
        if (that.formInline.currentState !== '全部') {
          preParam.queryParameterJson.current_state =
            that.formInline.currentState;
        }
      } else if (this.queryType === '2') {
        //更多条件查询
        preParam.queryParameterJson = Object.assign(
          {},
          preParam.queryParameterJson,
          listQuery.assignParams
        );
      }

      //排序查询
      if (this.orderBy && this.propField) {
        preParam.order = this.orderBy;
        preParam.prop = this.propField;
      }

      switch (that.secActiveIndex) {
        case '1':
          // 我处理-已超时
          preParam.queryParameterJson.timeout_state = 1;
          // preParam.queryParameterJson['tabType'] = 0;
          that.listWorkorderByState(preParam);
          break;
        case '2':
          // 我处理-即将超时
          preParam.queryParameterJson.timeout_state = 2;
          // preParam.queryParameterJson['tabType'] = 1;
          that.listWorkorderByState(preParam);
          break;
        case '3':
          // 我处理中的
          preParam.queryParameterJson.current_state = 1;
          // preParam.queryParameterJson['tabType'] = 2;
          that.listWorkorderByState(preParam);
          break;
        case '4':
          // 状态为待领取的
          that.listUnclaimedWorkorder(preParam);
          break;
        case '5':
          // 查询我已经处理的
          // preParam.queryParameterJson['queryMyDealt'] = true;
          // preParam.queryParameterJson['tabType'] = 4;
          that.listMyProcessed(preParam);
          break;
        case '6':
          // 查询我发起的
          // preParam.queryParameterJson['queryIInitiatedIt'] = true;
          // preParam.queryParameterJson['tabType'] = 5;
          that.listIInitiated(preParam);
          break;
        case '7':
          // 查询抄送我的
          // preParam.queryParameterJson['queryCarbonCopyMy'] = true;
          // preParam.queryParameterJson['tabType'] = 6;
          that.listMyCC(preParam);
          break;
        case '8':
          // 状态为全部
          that.listAllStateWorkorder(preParam);
          break;
        case '9':
          //查询我关注的
          that.listMyFocusOn(preParam);
          break;
      }
    },
    // 全部状态请求
    listAllStateWorkorder (preParam) {
      const that = this;
      return listAllStateWorkorder(preParam)
        .then((response) => {
          if (response.msg === 'success') {
            that.rander(response);
          } else {
            that.$XyyMessage.error(response.msg);
            that.loading.close();
          }
        })
        .catch(function (error) {
          that.$XyyMessage.error(error);
        });
    },
    // 待领取
    listUnclaimedWorkorder (preParam) {
      const that = this;
      // 待领取的状态请求
      return listUnclaimedWorkorder(preParam)
        .then((response) => {
          if (response.msg === 'success') {
            that.rander(response);
          } else {
            that.$XyyMessage.error(response.msg);
            that.loading.close();
          }
        })
        .catch(function (error) {
          that.$XyyMessage.error(error);
        });
    },
    // 我处理中/即将超时/已超时
    listWorkorderByState (preParam) {
      // console.log('preParam:', JSON.stringify(preParam));
      const that = this;
      return listWorkorderByState(preParam)
        .then((response) => {
          if (response.msg === 'success') {
            that.rander(response);
          } else {
            that.$XyyMessage.error(response.msg);
            that.loading.close();
          }
        })
        .catch(function (error) {
          that.$XyyMessage.error(error);
        });
    },
    // 我已处理
    listMyProcessed (preParam) {
      // console.log('preParam:', JSON.stringify(preParam));
      const that = this;
      return listMyProcessed(preParam)
        .then((response) => {
          if (response.msg === 'success') {
            that.rander(response);
          } else {
            that.$XyyMessage.error(response.msg);
            that.loading.close();
          }
        })
        .catch(function (error) {
          that.$XyyMessage.error(error);
        });
    },
    //我发起的
    listIInitiated (preParam) {
      // console.log('preParam:', JSON.stringify(preParam));
      const that = this;
      return listIInitiated(preParam)
        .then((response) => {
          if (response.msg === 'success') {
            that.rander(response);
          } else {
            that.$XyyMessage.error(response.msg);
            that.loading.close();
          }
        })
        .catch(function (error) {
          that.$XyyMessage.error(error);
        });
    },
    //抄送我的
    listMyCC (preParam) {
      // console.log('preParam:', JSON.stringify(preParam));
      const that = this;
      return listMyCC(preParam)
        .then((response) => {
          if (response.msg === 'success') {
            that.rander(response);
          } else {
            that.$XyyMessage.error(response.msg);
            that.loading.close();
          }
        })
        .catch(function (error) {
          that.$XyyMessage.error(error);
        });
    },
    //我关注的
    listMyFocusOn(preParam){
      const that = this;
      return listMyFocusOn(preParam)
        .then((response) => {
          if (response.code === 1) {
            that.rander(response);
          } else {
            that.$XyyMessage.error(response.msg);
            that.loading.close();
          }
        })
        .catch(function (error) {
          that.$XyyMessage.error(error);
        });
    },
    rander (response) {
      const that = this;
      const { list, total, pageSize, pageNum } = response.data;
      if (!that.isCondition) {
        // that.workorderListCount(); // 条件查询无需请求
      }
      if (response.data.list) {
        that.list = adapterList(list);
        that.listQuery = {
          page: pageNum,
          pageSize: pageSize,
          total: total,
        };
      }
      setTimeout(() => {
        that.loading.close();
      }, 200);
    },

    // 导出列表
    exportOpen (sorce) {
      const that = this;
      if (!that.list || !that.list.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！',
        });
        return false;
      }
      let preParam;
      const { page, pageSize } = this.listQuery;
      if (!that.formTypeId) {
        preParam = {
          pageNum: page,
          pageSize: pageSize,
          queryAllState: false, // 全部
          queryCarbonCopyMy: false, // 抄送我的
          queryMyDealt: false, // 我处理的
          queryIInitiatedIt: false, // 我发起的
          queryUnclaimed: false, // 待领取的
          exportSource: '我的工单',
          exportType: sorce,
          workorderJson: {},
        };
      } else {
        preParam = {
          pageNum: page,
          pageSize: pageSize,
          queryAllState: false, // 全部
          queryCarbonCopyMy: false, // 抄送我的
          queryMyDealt: false, // 我处理的
          queryIInitiatedIt: false, // 我发起的
          queryUnclaimed: false, // 待领取的
          exportSource: '我的工单',
          exportType: sorce,
          workorderJson: {
            formTypeId: that.formTypeId,
          },
        };
      }
      switch (that.secActiveIndex) {
        case '9':
          preParam.queryMyFocusOn = true;
          break;
        case '8':
          preParam.queryAllWorkOrder = true;
          // preParam.queryAllState = true;
          break;
        case '7':
          preParam.queryCarbonCopyMy = true;
          break;
        case '6':
          preParam['tabType'] = 5;
          preParam.queryIInitiatedIt = true;
          break;
        case '5':
          preParam['tabType'] = 4;
          preParam.queryMyDealt = true;
          break;
        case '4':
          preParam.queryUnclaimed = true;
          break;
        case '1':
          preParam.workorderJson.timeoutState = 1;
          // preParam['tabType'] = 0;
          break;
        case '2':
          preParam.workorderJson.timeoutState = 2;
          preParam['tabType'] = 1;
          break;
        case '3':
          preParam.workorderJson.currentState = 1;
          preParam['tabType'] = 2;
          break;
      }

      //新增更多条件查询,2020.4.17,rl
      if (this.queryType === '1') {
        //简易查询
        if (this.isCondition) {
          if (that.formInline.customerName !== '') {
            preParam.workorderJson['s1164456058157142016'] = that.formInline.customerName;
          }
          //客户电话
          if(that.formInline.customerPhoneNumber !== ''){
            preParam.workorderJson['s1164723822566445056'] = that.formInline.customerPhoneNumber;
          }
          //客户ID
          if(that.formInline.customer_id !== ''){
            preParam.workorderJson['customer_id'] = that.formInline.customer_id;
          }
          //工单创建人
          if(that.formInline.creator !== ''){
            preParam.workorderJson['creator'] = that.formInline.creator;
          }
          // 商家ID
          if(that.formInline.merchantId !== '') {
            preParam.workorderJson['s1870039678284075008'] = that.formInline.merchantId;
          }
          
          if (that.formInline.dataRange !== '') {
            preParam.workorderJson.gmtCreate = that.formInline.dataRange[0];
            preParam.workorderJson.querGmt = that.formInline.dataRange[1];
          }
          if (that.formInline.sheetNum !== '') {
            preParam.workorderJson.workorderNum = that.formInline.sheetNum;
          }
          if (that.formInline.orderNum !== '') {
            preParam.workorderJson['s1176037234466492416'] =
              that.formInline.orderNum; // 订单编号
          }
          if (that.formInline.currentState !== '全部') {
            preParam.workorderJson.currentState = that.formInline.currentState;
          }
        }
      } else if (this.queryType === '2') {
        //更多条件查询
        preParam.workorderJson = Object.assign(
          {},
          preParam.workorderJson,
          this.assignParams
        );
      }

      //排序查询
      if (this.orderBy && this.propField) {
        preParam.order = this.orderBy;
        preParam.prop = this.propField;
      }

      preParam.workorderJson = JSON.stringify(preParam.workorderJson);
      startExport(preParam).then((res) => {
        // console.log('resres:', res);
        if (res.code === 1) {
          this.changeExport = true;
        } else {
          that.$XyyMessage.error(res.msg);
        }
      });
    },
    exportExcel () {
      //   function httpPost(URL, PARAMS) {
      //     var temp = document.createElement('form');
      //     temp.action = URL;
      //     temp.method = 'get';
      //     temp.style.display = 'none';
      //     for (var x in PARAMS) {
      //       var opt = document.createElement('textarea');
      //       opt.name = x;
      //       opt.value = PARAMS[x];
      //       temp.appendChild(opt);
      //     }
      //     document.body.appendChild(temp);
      //     temp.submit();
      //   }
      //   let domain = document.domain;
      //   if (domain === 'localhost') {
      //     domain = 'ec-service.dev.ybm100.com';
      //   }
      //   httpPost('http://' + domain + '/workorder/export/excel', preParam);
    },

    //功能按钮
    operationClick (type, row) {
      //1:查看,2:去关闭,3:催单,4:去处理
      if (type === 1 || type === 2 || type === 4) {
        if (type === 4 || type === 2) {
          //记录处理时间
          saveFlowStartTime({ workorderId: row.id }).then((res) => {
            if (res.code) {
              // console.log('记录成功');
            } else {
              // console.log('记录失败');
            }
          });
        }

        this.$router.push({
          path: '/' + this.pubUrl + '/sheetDetail/' + row.id,
          query: {
            id: row.id,
            type: 'wordSheet',
            currentUrl: this.currentUrl,
          },
        });
      } else {
        //催单显示信息
        this.urgeWorkCheck(row);
      }
    },
    getFormatDuration (row, column, cellValue, index) {
      // 计算天
      var Day = Math.floor(cellValue / (24 * 3600 * 1000));
      //计算出小时数
      var leave1 = cellValue % (24 * 3600 * 1000);
      var hours = Math.floor(leave1 / (3600 * 1000));
      //计算分钟数
      var leave2 = leave1 % (3600 * 1000);
      var minutes = Math.floor(leave2 / (60 * 1000));
      //计算秒数
      var leave3 = leave2 % (60 * 1000);
      var seconds = Math.round(leave3 / 1000);

      const D = Day === 0 ? '' : Day + '天';
      const h = hours === 0 ? '' : hours + '小时';
      const m = minutes === 0 ? '' : minutes + '分钟';
      const s = seconds === 0 ? '' : seconds + '秒';
      return D + h + m + s;
    },
    getFormatDate (row, column, cellValue, index) {
      if (!cellValue) {
        return '无';
      } else {
        return new Date(cellValue + 8 * 3600 * 1000)
          .toJSON()
          .substr(0, 19)
          .replace('T', ' ');
      }
    },
    // 工单批量转移状态切换
    batchSwitchstate () {
      if (!this.batchSelection) {
        this.batchSelected = [] // 重置所选值
        this.$refs.orderTable.$refs.table.clearSelection()// 重置table的选中值
        this.batchSelectDialog = !this.batchSelectDialog;
      } else {
        const length = this.batchSelected.length;
        if (length <= 0) {
          this.$XyyMessage.warning('请选择需要转移的工单');
          return;
        }
        this.batchMatchDialog = !this.batchMatchDialog;
        // this.APINodeListByWorkOrderIdInfo(this.batchMatchDialog);
      }
    },
    // 获取工单节点列表
    // APINodeListByWorkOrderIdInfo (batchMatchDialog) {
    //   if(batchMatchDialog){
    //     const workorderIds = [];
    //     const batchLength = this.batchSelected.length;
    //     const id = this.batchQueryForm.orderTemplate;
    //     this.batchQueryForm.currentNode = '';
    //     for (let i = 0; i < batchLength; i++) {
    //       workorderIds.push(this.batchSelected[i].id);
    //     }
    //     getNodeListByWorkOrderIdInfo({
    //       workOrderId: workorderIds,
    //       formId:id
    //     }).then((res) => {
    //       this.CurrtNodeList = res.data;
    //     });
    //   }
    // },

    // 工单批量转移
    handleBatchTransfer (flag) {
      this.$refs['batchTransferForm'].validate((valid) => {
        if (!valid) {
          return;
        }

        const that = this;
        const length = this.batchSelected.length;
        const list = [];

        // if (
        //   that.batchQueryForm.state == '1' &&
        //   that.batchQueryForm.currentNode !== that.batchMatchForm.nodeId
        // ) {
        //   that.$XyyMessage.warning(
        //     '转移工单有误。选中工单中节点与 接收节点不匹配'
        //   );
        //   return;
        // }

        if (!flag) {
          this.batchTipsDialog = true;
          return;
        }

        for (let i = 0; i < length; i++) {
          list.push({
            id: this.batchSelected[i].id,
            // fromFlowId: this.batchSelected[i].flowId
          });
        }

        const param = {
          toNodeId: this.batchMatchForm.nodeId,
          toUserGroupId: this.batchMatchForm.userGroupId,
          toUserId: this.batchMatchForm.userId,
          transferType: this.batchMatchForm.userId ? 1 : 0,
          workorderIdList: list,
        };
        this.batchSelected = [];
        setBatchTransfer(param).then((res) => {
          this.batchTipsDialog = false;
          this.batchSelection = false;
          this.batchMatchDialog = false;
          this.getBatchList();
          if (res.code == 1) {
            // this.handleCondition();
            that.$XyyMessage.success('批量转移成功！');
          } else {
            const total = res.data.total;
            this.batchFailList = res.data.list;
            const length = this.batchFailList.length;
            if (length <= 0) {
              this.$XyyMessage.warning(res.msg);
            } else {
              this.$XyyMessage.warning('剩余' + total + '条转移失败');
              this.batchFailDialog = true;

            }
            // that.$XyyMessage.warning('部分转移成功！');
          }
        });
      });
    },
    //批量转移选中数据
    selectionCallback (data) {
      this.batchSelected = data;
    },
    //获取问题分类，节点选择
    getTypeformatList (id) {
      const that = this;
      this.batchQueryForm.questionType = '';
      this.batchQueryForm.orderTemplate = '';
      // 问题分类列表
      getTypeformat({ formTypeId: id }).then((res) => {
        that.options = res.data.treeOptions.optionsArray;
      });

      this.getSeleSheetList(id); // 取消页面加载时加载两次
    },
    // 获取工单模板列表
    getSeleSheetList (id) {
      const that = this;
      seleSheetList({ formTypeId: id, status: 1 }).then((res) => {
        that.orderTemplateList = res.data;
      });
    },
    // 获取客户所在地
    getCustomerSourceLists () {
      getCustomerSourceList().then((res) => {
        this.customerAddList = res.data;
      });
    },
    // 获取工单节点列表
    getCurrtNodeLists (id) {
      this.batchQueryForm.currentNode = '';
      getListNodesByFormId(id).then((res) => {
        this.CurrtNodeList = res.data;
      });
    },
    // 工单批量转移查询
    //resetOrderByFlag 是否重置时间排序字段 true:重置,false:不重置
    getBatchList (resetOrderByFlag) {
      if (resetOrderByFlag) {
        //重置时间字段排序
        this.orderBy = '';
        this.propField = '';
        this.$refs.orderTable.$refs.table.clearSort();
        this.listQuery.page = 1;
        this.listQuery.pageSize = 10;
      }

      this.$refs['refSearchForm'].validate((valid) => {
        if (!valid) {
          return;
        }
        const that = this;

        const { page, pageSize } = this.listQuery;
        const param = {
          interfaceType: that.secActiveIndex,
          // nodeId: that.batchQueryForm.currentNode,
          pageNum: page,
          pageSize: pageSize,
          queryParameterJson: {
            form_id: that.batchQueryForm.orderTemplate,
            form_type_id: that.batchQueryForm.orderType,
            // s1164724692221825024: '', // 'c1245225948782137344',
            current_state: that.batchQueryForm.state,
            // s1176037136521105408: that.batchQueryForm.customerLoca,
            // current_processing_person: that.batchQueryForm.currentProcessor,
            // creator: that.batchQueryForm.founder,
            // query_start_time: that.batchQueryForm.createTime
          },
        };

        if (that.batchQueryForm.currentNode) {
          param.nodeId = that.batchQueryForm.currentNode;
        } else {
          param.nodeId = '';
        }

        if (that.batchQueryForm.questionType) {
          param.queryParameterJson.s1164724692221825024 =
            that.batchQueryForm.questionType[
            that.batchQueryForm.questionType.length - 1
            ];
        }

        if (that.batchQueryForm.customerLoca) {
          param.queryParameterJson.s1176037136521105408 =
            that.batchQueryForm.customerLoca;
        }

        if (that.batchQueryForm.currentProcessor) {
          param.queryParameterJson.current_processing_person =
            that.batchQueryForm.currentProcessor;
        }

        if (that.batchQueryForm.founder) {
          param.queryParameterJson.creator = that.batchQueryForm.founder;
        }

        if (
          that.batchQueryForm.createTime &&
          that.batchQueryForm.createTime.length === 2
        ) {
          param.queryParameterJson.query_start_time =
            that.batchQueryForm.createTime[0];

          param.queryParameterJson.query_end_time =
            that.batchQueryForm.createTime[1];
        }

        //排序查询
        if (this.orderBy && this.propField) {
          param.order = this.orderBy;
          param.prop = this.propField;
        }

        listAllWorkorderByBatch(param).then((response) => {
          if (response.msg === 'success') {
            that.rander(response);
            this.batchSelection = true;
            this.batchSelectDialog = false;
          } else {
            that.$XyyMessage.error(response.msg);
            that.loading.close();
          }
        });
      });
    },
    // 获取用户组
    getListUserGroupsByNodeId (id) {
      const workorderIds = [];
      const batchLength = this.batchSelected.length;
      this.batchMatchForm.userGroupId = '';
      for (let i = 0; i < batchLength; i++) {
        workorderIds.push(this.batchSelected[i].id);
      }

      getUserGroupsByNodeIdAndWorkorderIds({
        nodeId: id,
        workorderIds: workorderIds.join('_'),
      }).then((res) => {
        this.UserGroupList = res.data;
        const length = this.UserGroupList.length;
        if (this.assRadioSign === 'people' && length <= 0) {
          this.$XyyMessage.warning('该节点版本无用户组，请选择其他节点！');
        }
      });
    },
    // 获取当前人信息
    getUserInfos () {
      getUserInfo().then((res) => {
        if (['1', '2', '3', '5'].includes(this.secActiveIndex)) {
          this.batchQueryForm.currentProcessor = res.data.attributes.staffNum;
        }
        if (['6'].includes(this.secActiveIndex)) {
          this.batchQueryForm.founder = res.data.attributes.staffNum;
        }
      });
    },
    // 获取组内成员
    getUserLists (id) {
      this.batchMatchForm.userId = '';
      getUserList(id).then((res) => {
        this.GroupUserList = res.data;
      });
    },
    // 获取标签字典
    getTags () {
      getTagList().then((res) => {
        this.tags = res.data;
      });
    },
    // 切换标签
    switchTag (tag, row) {
      putSwitchTag(tag.id, row.id).then((res) => {
        if (res.code === 1) {
          this.list = this.list.map((item) => {
            if (row.id === item.id) {
              item.tagId = tag.id;
              item.tagName = tag.tagName;
            }
            return item;
          });
        }
      });
    },
    // 清除标签
    clearTag (row) {
      deleteTag(row.id).then((res) => {
        if (res.code === 1) {
          this.list = this.list.map((item) => {
            if (row.id === item.id) {
              item.tagId = '';
              item.tagName = '';
            }
            return item;
          });
        }
      });
    },

    //更多查询条件是否可见
    setVisibleQueryMore () {
      this.queryMoreVisible = false;
    },

    //更多查询条件查询
    searchQueryMore (assignParams) {
      this.queryMoreVisible = false;
      this.queryType = '2';
      this.assignParams = assignParams;

      //重置时间字段排序
      this.orderBy = '';
      this.propField = '';
      this.$refs.orderTable.$refs.table.clearSort();

      this.seachDataList({
        page: 1,
        pageSize: 10,
        assignParams,
      });
    },
    // 去除前后空格
    inputTrim () {
      this.formInline.sheetNum = this.formInline.sheetNum.trim();
      this.formInline.customerName = this.formInline.customerName.trim();
      this.formInline.orderNum = this.formInline.orderNum.trim();
      this.formInline.customerPhoneNumber = this.formInline.customerPhoneNumber.trim();
      this.formInline.customer_id = this.formInline.customer_id.trim();
      this.formInline.creator = this.formInline.creator.trim();
      this.formInline.merchantId = this.formInline.merchantId.trim();
    },

    /**
     * 自定义排序
     * 重新请求列表
     */
    sortChange ({ column, prop, order }) {
      //排序方式
      switch (order) {
        case 'ascending':
          this.orderBy = 'asc';
          break;
        case 'descending':
          this.orderBy = 'desc';
          break;
        default:
          this.orderBy = '';
          break;
      }

      //排序字段
      /*
        recentAcceptanceTime(最近受理时间)    recent_acceptance_time
        latestSubmissionTime(最近提交时间)     latest_submission_time
        gmtCreate(工单发起时间)                        gmt_create
        completionTime(工单完成时间)、           completion_time
        evaluategmtCreate(评价时间)          evaluategmtCreate
      */
      switch (prop) {
        case 'recentAcceptanceTime':
          this.propField = 'recent_acceptance_time';
          break;
        case 'latestSubmissionTime':
          this.propField = 'latest_submission_time';
          break;
        case 'gmtCreate':
          this.propField = 'gmt_create';
          break;
        case 'completionTime':
          this.propField = 'completion_time';
          break;
        case 'evaluategmtCreate':
          this.propField = 'evaluategmtCreate';
          break;
        default:
          this.propField = '';
          break;
      }

      //重新请求数据
      if (this.batchSelection) {
        this.getBatchList();
      } else {
        this.seachDataList(
          Object.assign({}, this.listQuery, {
            assignParams: this.assignParams,
          })
        );
      }
    },
    /**
     * 获取业务线,判断是否是药帮忙业务线
     */
    getBusinessPartCode() {
      if (
        this.$store.getters.channel &&
        this.$store.getters.channel.businessPartCode
      ) {
        this.isYBMCode = this.$store.getters.channel.businessPartCode == 'S00009999';
      }
    }
  },
};
</script>
<style lang="scss">
.el-table tr td .cell {
  padding-left: 13px !important;
}
.hoverStyle {
  padding-bottom: 12px;
  width: 279px;
  max-height: 165px;
  overflow-y: scroll;
  overflow-x: hidden;

  .title {
    color: #292933;
    font-size: 14px;
    padding-bottom: 7px;
  }
  .time {
    color: #aeaebf;
    font-size: 12px;
    padding-bottom: 8px;
  }
  .content {
    .instrLs {
      margin-bottom: 8px;
    }
    .lsItem {
      white-space: normal;
      text-align: left;
      font-size: 14px;
      color: #909399;
      max-width: 160px;
    }
    .lsCon {
      font-size: 14px;
      color: #909399;
      text-align: left;
    }
  }
  .noData {
    text-align: center;
    margin-top: -10px;
  }
}

/* 浏览器滚动条样式 */
/* width */
.el-popover.el-popper.hoverStyle::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}
/* Track */
.el-popover.el-popper.hoverStyle::-webkit-scrollbar-track {
  background: rgb(255, 255, 255);
  border-radius: 8px;
}
/* Handle */
.el-popover.el-popper.hoverStyle::-webkit-scrollbar-thumb {
  background: rgb(201, 201, 202);
  border-radius: 8px;
}
/* Handle on hover */
.el-popover.el-popper.hoverStyle::-webkit-scrollbar-thumb:hover {
  background: rgb(162, 162, 163);
}
.urgeCon .el-textarea__inner {
  height: 211px;
}
.urgeStyle {
  .el-dialog__body {
    padding: 0 20px;
  }
  .el-dialog__title {
    color: #292933;
  }
  .el-dialog__footer {
    padding-bottom: 20px;
  }
}
/* 表格内 icon 样式 */
.el-icon-plus:before {
  background-color: transparent;
}
/* 标签按钮 */
.tag-btn {
  height: auto !important;
  margin: 2px 0;
}
.el-popover {
  text-align: unset;
}
.table-tag .cell {
  text-overflow: clip;
}
</style>
<style lang="scss" scoped>
.exoprtShow {
  padding: 0;
}
.assignmentSheet {
  .queryAss {
    background-color: rgba(59, 149, 168, 1);
    color: #fff;
    border-radius: 2px;
  }
  /deep/.el-button.el-button--primary.is-plain {
    background-color: rgba(59, 149, 168, 1);
    color: #fff;
  }
  .userGroup {
    margin-bottom: 20px;
  }
  .label {
    display: inline-block;
    width: 56px;
    text-align: right;
    color: #292933;
    margin-right: 8px;
  }
  .sheetNumber {
    font-size: 14px;
    color: rgba(144, 147, 153, 1);
    padding: 0 0 20px 40px;
  }
  .assSubmit {
    padding: 0 20px 20px 0;
  }
  /deep/.el-dialog__header {
    padding: 0 20px;
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    font-weight: 600;
    color: rgba(48, 49, 51, 1);
    border-bottom: 1px solid rgba(238, 238, 238, 1);
  }
  /deep/.el-dialog__body {
    padding: 14px 0 0 0px;
  }
  /deep/.el-input {
    width: 290px;
  }
  /deep/.el-button {
    height: 36px;
  }
  /deep/.el-form-item__label {
    width: 112px !important;
    padding: 0 12px 0 12px;
  }
}
.urgeStyle {
  font-size: 14px;
  .urgeTitle {
    color: #575766;
    font-size: 14px;
    padding-bottom: 12px;
  }
  .urgeCon .el-textarea__inner {
    height: 211px;
  }
  .urgeTip {
    padding-top: 12px;
    color: #909399;
  }
}

.el-icon-arrow-down {
  color: #c0c4cc;
}
/deep/label {
  font-weight: 400 !important;
}
.padding_r_36 {
  padding-right: 36px;
}
.padding_r_20 {
  padding-right: 20px;
}

.page {
  overflow-y: auto;
}
.sheeTitle {
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}
.searchCondition.is-plain {
  background: rgba(59, 149, 168, 1);
  color: #fff;
}
/deep/.searchCondition.is-plain:hover {
  background: rgba(40, 126, 144, 1);
}
.el-button {
  height: 36px;
}

.sheetMainBtnt {
  padding: 12px 12px;
  width: 150px;
}
.sheetMainBtn {
  padding: 12px 12px;
}
.el-button--small {
  font-size: 14px;
}
.elBtn {
  margin-left: 0;
  margin-right: 10px;
  margin-bottom: 12px;
  margin-top: 5px;
}
.elBtn:last-child {
  margin-bottom: 0;
}
.elBtn.active {
  color: #40a3b8;
  border: 1px solid #3b95a8;
}
.redSign {
  position: relative;
  z-index: 1;
}
.redSign::before {
  content: '';
  width: 8px;
  height: 8px;
  position: absolute;
  right: -3px;
  top: -4px;
  border-radius: 50%;
  background: red;
}
.selTag {
  height: 30px;
  line-height: 20px;
  padding: 0;
  margin: 8px 20px 0;
}
.selTag:nth-child(1) {
  margin-left: 5px;
}
.selTag:hover {
  background-color: rgba(245, 247, 250, 0.4) !important;
  color: #303133 !important;
}
.sheeNav {
  position: relative;
  margin-top: 15px;
  padding: 15px 133px 0 19px;
  background: rgba(245, 247, 250, 1);
  border-bottom: 1px solid #e4e4eb;
  .navTag {
    height: auto;
  }
  .navTag.notActive {
    height: 44px;
    overflow: hidden;
  }
}
/deep/.el-input__icon {
  line-height: 30px !important;
}
/deep/.el-menu.el-menu--horizontal {
  border: none;
}
/deep/.el-input--small .el-input__inner {
  height: 36px;
  line-height: 36px;
}
/deep/.el-range-editor--small.el-input__inner {
  height: 36px;
}
.show_more {
  position: absolute;
  right: 19px;
  top: 19px;
  width: 76px;
  height: 40px;
}
.el-icon--right {
  margin-left: 0;
}
.input_serch {
  margin: 20px 0 0;
  font-size: 14px;
  border-bottom: 1px dashed #e4e4eb;

  .el-form-item {
    margin-bottom: 12px;
  }
}
.elSelData {
  width: 334px;
  margin-left: 6px;
}
.el-form-item__label {
  color: #292933;
}
.innerStyle {
  width: 100%;
  /deep/.el-form-item__content {
    width: 350px;
  }
  /deep/.el-date-editor--datetimerange.el-input__inner {
    width: 100%;
  }
}
.dropDownBox {
  .el-dropdown-menu__item:hover {
    background: none;
  }
  /deep/.el-range-editor--small .el-range-input {
    font-size: 13px !important;
  }
  .innerElInput {
    width: 350px;
  }
  .innerStyle {
    margin: 20px 0 0;
  }
  .innerSelTime {
    margin-bottom: 20px;
  }
}
.tableBox {
  padding: 0 20px 20px;
}
.currentState0 {
  color: #fb720c;
}
.currentState1 {
  color: #fb720c;
}
.currentState2 {
  color: #67c23a;
}
.currentState3 {
  color: #fb720c;
}
.currentState4 {
  color: #fb720c;
}
.currentState5 {
  color: #fb720c;
}
.xyyTable {
  position: relative;
  .exportExcel {
    position: absolute;
    right: 20px;
    top: 0px;
    z-index: 10;
  }
}
.sheetState {
  position: relative;
  .newPageIcon {
    position: absolute;
    left: 800px;
    top: 9px;
    width: 16px;
    height: 16px;
    z-index: 1;
  }
}

/deep/.el-range-separator {
  padding: 0px !important;
}
/deep/.el-form-item__error {
  left: 23px !important;
}

.el-table .el-rate {
  margin-top: 10px;
}

/deep/ .el-table ::before {
  background-color: transparent !important;
}

.content-textarea-formate {
  display: flex;
  white-space: nowrap !important;

  pre {
    margin-top: 0;
    margin-bottom: 0;
    white-space: pre-wrap !important;
    font-family: inherit !important;
    word-break: break-all;
  }
}
</style>

