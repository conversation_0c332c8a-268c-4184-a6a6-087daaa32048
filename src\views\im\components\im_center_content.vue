<!-- im聊天内容组件 -->
<template>
  <div class="wxchat-container im_container_content" style="backgroundColor: white">
    <div class="window" id="window-view-container">
      <!-- data is empty -->
      <div class="loading" v-if="dataArray && dataArray.length == 0">
        <div style="margin-top: 200px;text-align:center; font-size: 16px;">
          <span>未查找到聊天记录</span>
        </div>
      </div>

      <div class="message messageContainer" v-loadmore="loadmore" @scroll="closeMenu">
        <el-divider v-if="dataArray.length != 0 && (dataArray.length + newMessage) >= total">没有更多了</el-divider>

        <div class="loadingBox" style="width:100%;height:25px;line-height: 25px;text-align: center;"
          v-if="dataArray.length != 0 && (dataArray.length + newMessage) < total">
          <i class="el-icon-loading"></i>
        </div>
        <ul>
          <template v-for="(item) in dataArray">
            <li :key="item.messageId" v-if="item.content" :messageId="item.messageId"
              :class="item.direction == 2 ? 'an-move-right' : 'an-move-left'">
              <div v-if="item.msgtype === 'endmessage'" class="time">- {{ item.content }} -</div>
              <div v-else-if="item.msgtype === 'recall'" class="recall">{{ item.content }}</div>
              <div v-else-if="item.msgtype === 'system'" class="systemmsg">
                <div class="systemstyle">
                  <div class="el-icon-warning" style="margin-right:10px;"></div>
                  <span v-html="newcontent(item.content)" style="white-space: normal; word-break: break-all; "></span>
                </div>
              </div>
              <!-- direction:1 访客(left)，2 客服(right) -->
              <div :class="'main' + (item.direction == 2 ? ' self' : '')" v-else>
                <p :class="item.direction == 2 ? 'timerows_right' : 'timerows_left'">
                  <span :title="item.customerName" class="customerName" v-text="item.customerName"
                    v-if="item.direction == 1"></span>
                  <span v-text="item.ctime"></span>
                  <span :title="item.kefuName" class="customerName" v-text="item.kefuName"
                    v-if="item.direction == 2"></span>
                </p>
                <img class="avatar" width="36" height="36" style="border-radius: 25px;"
                  :src="item.direction == 2 ? ((item.avatar && item.avatar.trim()) || require('../../../../static/user_kf.png')) : (chatInfo.customHeadImg || require('../../../../static/user_kh.png'))" />
                <div v-html="newcontent(item.content)" class="text" id="msg" @click="showRaw($event)"
                  @contextmenu="openMenu($event, item)"></div>
              </div>
            </li>
            <div class="leave-message-box" :key="item.id" v-if="showDivider(item)">
              <div class="systemmsg">
                <div class="systemstyle">
                  <div class="el-icon-warning" style="margin-right:10px;"></div>
                  <span style="white-space: normal; word-break: break-all; ">以上为留言</span>
                </div>
              </div>
            </div>

            <!-- <el-divider
              class="leave-message-box"
              :key="item.id"
              v-if="showDivider(item)"
              style="color:#909399"
            >以上为留言</el-divider>-->
          </template>
        </ul>
      </div>
    </div>
    <imgAlert ref="imgAlert"></imgAlert>
    <fileprev ref="imgDialog"></fileprev>
    <filepreview ref="fileDialog"></filepreview>
    <!-- <div v-show="recallVisible"
         class="contextmenu">
      <ul>
        <li>撤回</li>
      </ul>
    </div>-->
  </div>
</template>

<script>
import { getbeforeHistoryMessage, msgRecall } from '@/api/im_view/index';
import { queryOrderDetailUrl, queryProductDetailUrl } from '@/api/im_view/customeOrder';
import { containerMessage } from '../../im_view/components/im_container_tool/message_tool';
import imgAlert from './im_messageImgAlert';
import { log } from 'util';
import fileprev from '~/Fields/img-preview';
import filepreview from '~/Fields/pdf-preview';
export default {
  name: 'wxChat',

  components: {
    imgAlert,
    fileprev,
    filepreview
  },

  props: {
    maxHeight: {
      type: Number
    },

    getUpperData: {
      type: Function
      // required: true
    },

    getUnderData: {
      type: Function
      // required: true
    },

    chatData: {
      type: Object,
      default() {
        return {};
      }
    }
  },

  data() {
    return {
      isUpperLaoding: false,
      isUnderLaoding: false,
      isRefreshedAll: false,
      isLoadedAll: false,
      max_height: this.maxHeight,
      minHeight: 100,

      width: 820,

      // 新版本增加变量
      pageNum: 1, // 历史记录页数
      size: 50, // 历史记录条数
      total: 0, // 总条数
      newMessage: 0, // 新插入消息计数，用于计算消息总数
      dataArray: [], // 消息列表
      searchTime: null, // 历史记录回传time
      requestTime: false, // 是否正在请求历史记录
      chatInfo: {},
      leavaMessageId: '', // 消息列表中的留言ID
      file: {},
      activatedScrollTop: 0, // 用于记录页面被切换时的滚动条顶部距离
      recallVisible: false,
      top: 0,
      left: 0,
      recallMessageId: ''
    };
  },
  watch: {
    chatData: {
      handler(newVal, oldVal) {
        // this.newMessage = 0;
        // this.pageNum = 1;
        // this.dataArray = [];
        // this.searchTime = null;
        // this.requestTime = false;
        // this.total = 0;
        if (JSON.stringify(oldVal) === JSON.stringify(newVal)) return;
        Object.assign(this.$data, this.$options.data()); // 重置data数据
        this.chatInfo = newVal;
        if (this.chatInfo.currentContainerID && this.chatInfo.userId)
          this.getMessageHistory();
      }
    },
    recallVisible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu);
      } else {
        document.body.removeEventListener('click', this.closeMenu);
        // document.body.addremoveEventListenerEventListener('DOMNodeInserted', this.closeMenu);
      }
    }
  },
  deactivated: function () {
    this.activatedScrollTop = document.querySelector(
      '.messageContainer'
    ).scrollTop; // 保存聊天窗滚动条位置，切换回来的时候恢复位置
  },
  activated: function () {
    document.querySelector(
      '.messageContainer'
    ).scrollTop = this.activatedScrollTop;
    this.activatedScrollTop = 0;
  },
  mounted() {
    this.minHeight = document.getElementById(
      'window-view-container'
    ).offsetHeight;
    // this.max_height = document.getElementById(
    //   'window-view-container'
    // ).offsetHeight;

    //this.$initIndexedDB();
  },
  methods: {
    /**
     * 展示留言横线
     */
    showDivider(data) {
      return data.messageId === this.leavaMessageId;
    },
    // 读取历史聊天记录
    getMessageHistory() {
      const that = this;
      const params = {
        pageNum: that.pageNum,
        size: that.size,
        time: that.searchTime,
        userId: that.chatInfo.userId,
        appId: that.chatInfo.appId,
        guestId: that.chatInfo.oldShopId,
        currentDialogId:
          that.chatInfo.searchType !== 1
            ? that.chatInfo.currentContainerID
            : null,
        type: that.chatInfo.searchType
      };
      that.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });

      getbeforeHistoryMessage(params)
        .then(res => {
          if (res.code && res.code == 1 && res.data && res.data.records) {
            if (res.data.records.length > 0) {
              let records = this.cardMsgType201Formatter(res.data.records);
              let messageArray = {
                messages: records
              };
              let data = containerMessage(messageArray);

              // 查找会话中是否有留言
              if (!that.leavaMessageId) {
                let arrMessage = data.messages.slice(0, data.length);
                const mid = arrMessage.reverse().find(item => {
                  return item.dialogType === 2; // dialogType：2 为留言
                });
                that.leavaMessageId = mid ? mid.messageId : '';
              }

              if (that.pageNum > 1) {
                // 向数组前追加历史消息

                // 记住加载前第一条消息ID
                const messageid = document
                  .querySelector('.messageContainer ul li')
                  .getAttribute('messageid');

                // 向数组前追加消息
                that.dataArray = data.messages.concat(that.dataArray);
                // 依次将新数据追加到会话数组中
                // data.messages.forEach(element => {
                //   that.dataArray.unshift(element);
                // });

                this.$nextTick(() => {
                  // 拿到加载前的第一条消息的DOM
                  const dom = document.querySelector(
                    `.messageContainer ul li[messageid="${messageid}"]`
                  );
                  // 计算距离顶部的距离，并设定滚动条的位置

                  const offset = document.querySelector('.loadingBox')
                    ? document.querySelector('.loadingBox').offsetHeight + 38
                    : 63;
                  console.log(offset);
                  document.querySelector('.messageContainer').scrollTop =
                    dom.offsetTop - offset;
                });
              } else {
                // 重新加载新数组
                that.dataArray = data.messages;
                this.scrollerToBottom(); // 滑动到底部
              }

              that.searchTime = res.data.time
                ? Number(res.data.time)
                : that.searchTime;
              that.total = Number(res.data.total);
            } else {
              // that.pageNum--;
            }
          } else {
            that.$XyyMessage.error(res.msg || '数据异常！');
          }
          that.requestTime = false;
        })
        .finally(e => {
          that.loading.close();
        });
    },

    // 加载更多会话历史
    loadmore() {
      console.log('滚动');
      const that = this;
      if (
        !that.requestTime &&
        that.dataArray.length + that.newMessage < that.total // 解释：历史消息条数 + 插入消息数 < 总消息条数
      ) {
        that.requestTime = true;
        that.pageNum++;
        // 读取历史聊天记录
        this.getMessageHistory();
      } else {
        console.log('正在加载中！！');
      }
    },

    cardMsgType201Formatter(msgList) {
      if (msgList && msgList.length && msgList instanceof Array) {
        //名片消息，type:201
        for (let i = 0, l = msgList.length; i < l; i++) {
          if (msgList[i].type === 201) {
            //去掉名片消息中按钮的点击事件
            let isDivContains = document.getElementById('msgContentDiv');
            if (isDivContains) {
              document.body.removeChild(isDivContains);
            }

            let msgContentDiv = document.createElement('div');
            msgContentDiv.setAttribute('id', 'msgContentDiv');
            msgContentDiv.innerHTML = msgList[i].content;
            document.body.appendChild(msgContentDiv);

            let btns = document.querySelectorAll('#msgContentDiv button');
            if (btns && btns.length) {
              btns.forEach(item => {
                item.removeAttribute('onclick');
              });
            }

            msgList[i].content = document.getElementById(
              'msgContentDiv'
            ).innerHTML;

            document.body.removeChild(msgContentDiv);
          }
        }
        return msgList;
      }
    },

    // 插入新消息
    setMessage(paramsTmp) {
      const that = this;
      if (paramsTmp && paramsTmp.length && paramsTmp instanceof Array) {
        let params = this.cardMsgType201Formatter(paramsTmp);
        const messageId = params[0].messageId;
        const repeatFlag = document.querySelectorAll(
          '.messageContainer>ul>li[messageid="' + messageId + '"]'
        );
        if (messageId && repeatFlag.length <= 0) {
          // 如果存在messageId 并且没有重复
          let messageArray = {
            messages: params
          };
          try {
            let data = containerMessage(messageArray);
            that.newMessage += data.messages.length;
            this.$nextTick(() => {
              that.dataArray = that.dataArray.concat(data.messages);
            });
          } catch (err) {
            console.log('im会话，新增消息：', err);
          }
        }
      } else {
        console.log('im会话，新增消息：数据体异常！', params);
      }
      that.scrollerToBottom();
    },

    // 转接处理
    refreshMessage(message) {
      var me = this;
      me.dataArray = me.dataArray.concat(message);
      this.scrollerToBottom();
    },

    // 会话滑动到最底部
    scrollerToBottom() {
      this.$nextTick(() => {
        const messageContainer = document.querySelector('.messageContainer');
        if (messageContainer) {
          setTimeout(() => {
            messageContainer.scrollTop = messageContainer.scrollHeight + 10;
          }, 50);
        }
      });
    },

    // 消息点击事件
    async showRaw(src, item) {
      const currentTarget = event.currentTarget;
      if (currentTarget.querySelector('.order-msg-bubble')) {
        // 订单消息
        const selfTarget = currentTarget.querySelector('.order-msg-bubble')
        // 订单编号不跳转
        const eventPath = event.path || (event.composedPath && event.composedPath());
        if(eventPath.includes(selfTarget.querySelector('.order-msg-no'))) {
          return
        }
        // 未绑定事件不跳转
        if (!selfTarget.attributes['@click']) {
          return
        }
        let selfTargetValueArr = selfTarget.attributes['@click'].value.split("'");
        if (selfTargetValueArr.length && selfTargetValueArr[1].length) {
          try {
            const orderNo = selfTargetValueArr[1]
            const { code, data, msg } = await queryOrderDetailUrl({
              orderNo,
              clientType: 2, // 客户端类型: 1 app,2 web
            })
            if (code == 1) {
              window.open(data, '_blank');
            } else {
              throw new Error(msg)
            }
          } catch (e) {
            console.log(e.message)
            this.$XyyMessage.error(e.message)
          }
        }
      } else if (currentTarget.querySelector('.product-msg-bubble')) {
        // 商品消息
        const selfTarget = currentTarget.querySelector('.product-msg-bubble')
        // 订单编号不跳转
        const eventPath = event.path || (event.composedPath && event.composedPath());
        if(eventPath.includes(selfTarget.querySelector('.order-msg-no'))) {
          return
        }
        // 未绑定事件不跳转
        if (!selfTarget.attributes['@click']) {
          return
        }
        let selfTargetValueArr = selfTarget.attributes['@click'].value.split("'");
        if (selfTargetValueArr.length && selfTargetValueArr[1].length) {
          const productInfoArr = selfTargetValueArr[1].split(',')
          if (productInfoArr[0] && productInfoArr[0].length && productInfoArr[1] && productInfoArr[1].length) {
            try {
              const orderNo = productInfoArr[0]
              const skuId = productInfoArr[1]
              const { code, data, msg } = await queryProductDetailUrl({
                orderNo,
                skuId,
                clientType: 2, // 客户端类型: 1 app,2 web
              })
              if (code == 1) {
                window.open(data, '_blank');
              } else {
                throw new Error(msg)
              }
            } catch (e) {
              console.log(e.message)
              this.$XyyMessage.error(e.message)
            }
          }
        }
      } else {
        // 文件预览等
        if (!event.target.attributes['@click']) return;
        let targetValueArr = event.target.attributes['@click'].value.split("'");
        switch (targetValueArr[1].substring(targetValueArr[1].lastIndexOf('.') + 1).toLowerCase()) {
          case 'xls':
          case 'xlsx':
          case 'doc':
          case 'docx':
          case 'pdf':
            // this.$refs.fileDialog.initPdf('');
            this.$nextTick(() => {
              this.$refs.fileDialog.initPdf(targetValueArr[1]);
            });
            break;
          case 'png':
          case 'jpg':
          default:
            this.$refs.imgDialog.initImage('');
            this.$nextTick(() => {
              this.$refs.imgDialog.initImage(targetValueArr[1]);
            });
            break;
        }
      }
    },

    // 消息处理
    newcontent(content) {
      if (!content) {
        return;
      }

      let cont = content;
      try {
        if (cont.indexOf('xyyignore') === -1 && cont.indexOf('onclick') !== -1) {
          let contArr = cont.split('onclick');
          cont = contArr.join('@click');
        }
      } catch (err) {
        console.log('im消息，事件处理：', err);
      }
      return cont;
    },

    /**
     * 右键菜单
     */
    openMenu(el, item) {
      //消息为201客服 手动发送
      if (
        item.type === 201 &&
        (item.dialogId || item.dialogid) === this.chatData.currentContainerID
      ) {
        const msgTime = item.createTime || 0;
        const timeOut =
          (new Date().getTime() - new Date(msgTime).getTime()) / 1000; // 计算相差秒数

        // 如果时间正确 且 未超过三分钟（180秒）
        if (!isNaN(timeOut) && timeOut < 180) {
          event.preventDefault();
          // this.left = el.currentTarget.offsetLeft + el.offsetX  // fix 位置
          // this.top = el.pageY - 190// fix 位置
          const sX = el.clientX;
          const sY = el.clientY;
          if (document.querySelector('.im_msg_menu')) {
            document.body.removeChild(document.querySelector('.im_msg_menu'));
          }
          // var menu_dom = document.querySelector('.contextmenu');
          var menu_dom = document.createElement('div');

          menu_dom.innerHTML = '<ul><li>撤回</li></ul>';
          menu_dom.style = `top:${sY}px;left:${sX}px`;
          menu_dom.className = 'contextmenu im_msg_menu';
          menu_dom.onclick = this.messageRecall;
          document.body.appendChild(menu_dom);
          this.recallMessageId = {
            dialogId: item.dialogId || item.dialogid,
            messageId: item.messageId
          };
          this.recallVisible = true;
        }
      }
    },

    /**
     * 关闭菜单
     */
    closeMenu() {
      this.recallMessageId = '';
      this.recallVisible = false;
      if (document.querySelector('.im_msg_menu')) {
        document.body.removeChild(document.querySelector('.im_msg_menu'));
      }
    },

    /**
     * 消息撤回
     */
    messageRecall() {
      const that = this;
      const recallMessageId = that.recallMessageId;
      if (
        recallMessageId &&
        recallMessageId.dialogId &&
        recallMessageId.messageId
      ) {
        msgRecall({
          dialogId: recallMessageId.dialogId,
          recallMessageId: recallMessageId.messageId
        }).then(res => {
          if (res && res.code === 1) {
            const dom = document.querySelector(
              `.messageContainer ul li[messageid="${recallMessageId.messageId}"]`
            );
            if (dom) {
              dom.innerHTML = '<div class="recall">你撤回了一条消息</div>';
            }

            // 判断是否最后一条 消息，是则通知预览
            const lastMsg = that.dataArray[that.dataArray.length - 1];
            if (lastMsg && lastMsg.messageId === recallMessageId.messageId) {
              that.$emit('recallMessage', {
                messageId: recallMessageId.messageId,
                containerId: that.chatInfo.currentContainerID
              });
            }
          } else {
            that.$XyyMessage.error(res.msg || '撤回失败');
          }
          that.recallMessageId = '';
        });
      } else {
        that.$XyyMessage.error('缺少会话ID或消息ID');
      }
    }
  }
};
</script>
<style lang='scss' scoped>
.im_container_content h1,
h2 {
  font-weight: normal;
}

.im_container_content ul {
  list-style-type: none;
  padding: 0;
}

.im_container_content li {
  display: inline-block;
}

.im_container_content ::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 5px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}

.im_container_content ::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #f5f7fa;
}

.im_container_content ::-webkit-scrollbar-track {
  /*滚动条里面轨道*/

  background: #fff;
}

.wxchat-container {
  width: 100%;
  height: 100%;
  /* background-color: white; */
  /* z-index: 100; */
  /* position: fixed; */
  left: 0;
  top: 0;
  overflow: hidden;
  position: relative;
}

.shadow {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 50;
  width: 100%;
  height: 100%;
  /* background: #000; */
  opacity: 0.2;
}

.window {
  /* box-shadow: 1px 1px 20px -5px #000; max-width: 450px; */
  min-width: 568px;
  height: 100%;
  width: 100%;
  background: #fff;
  margin: 0 auto;
  overflow: hidden;
  padding: 0;
  position: relative;
  z-index: 51;
}

button {
  border: 0;
  background: none;
  border-radius: 0;
  text-align: center;
}

button {
  outline: none;
}

.w100 {
  width: 100%;
}

.mt5 {
  margin-top: 5px;
}

.mt10 {
  margin-top: 10px;
}

.mt20 {
  margin-top: 20px;
}

.mb10 {
  margin-bottom: 10px;
}

.mb20 {
  margin-bottom: 20px;
}

.fs0 {
  font-size: 0;
}

.title {
  background: #000;
  text-align: center;
  color: #fff;
  width: 100%;
  height: 50px;
  line-height: 50px;
  font-size: 14px;
}

.loading,
.no-more {
  text-align: center;
  color: #b0b0b0;
  line-height: 100px;
}

.no-more {
  line-height: 60px;
}

.pull-right {
  float: right;
}

.link-line {
  text-decoration: underline;
}

.message {
  height: 100%;
  padding: 10px 0;
  overflow-y: auto;
  background-color: #fff;
}

.message ul {
  overflow-y: auto;
}

.message li {
  margin-bottom: 15px;
  left: 0;
  position: relative;
  display: block;
  padding: 0 14px;
}

.message .time {
  margin: 10px 0;
  text-align: center;
  font-size: 12px;
  color: #909399;
}

.systemmsg {
  margin: 10px 0;
  text-align: center;
  font-size: 12px;
  width: 100%;
  margin-top: 20px;
  /* padding-top: 10px; */
  padding: 10px;
  color: #909399;
  border-radius: 4px;
  background-color: #dae9ec;
}

.systemstyle {
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.message .timerows_right {
  font-size: 12px;
  // margin-right: 60px;
  color: #909399;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  margin: 5px 0 5px 0;

  .customerName {
    text-align: center;
    width: 36px;
    margin-left: 7px;
    color: #333333;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.message .timerows_left {
  font-size: 12px;
  // margin-left: 60px;
  color: #909399;
  text-align: left;
  display: flex;
  justify-content: flex-start;
  margin: 5px 0 5px 0;

  .customerName {
    text-align: center;
    width: 36px;
    margin-right: 7px;
    color: #333333;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.message .text {
  display: inline-block;
  position: relative;
  /* max-width: calc(100% - 75px);
   */
  max-width: 50%;
  min-height: 35px;
  font-size: 14px;
  padding: 2px 10px 0;
  line-height: 32px;
  text-align: left;
  word-break: break-all;
  background-color: #f5f7fa;
  color: #000;
  border-radius: 4px;
  box-shadow: 0px 1px 7px -5px #000;

  /deep/img {
    margin-top: 10px !important;
    max-width: 100% !important;
  }
}

.message .avatar {
  float: left;
  margin: 0 10px 0 0;
  border-radius: 3px;
  background: #fff;
}

.message .time>span {
  display: inline-block;
  padding: 0 5px;
  font-size: 12px;
  color: #fff;
  border-radius: 2px;
  background-color: #dadada;
}

.message .system>span {
  padding: 4px 9px;
  text-align: left;
}

.message .text:before {
  content: ' ';
  position: absolute;
  top: 9px;
  right: 100%;
  border: 6px solid transparent;
  border-right-color: #fff;
}

.message .self {
  text-align: right;
}

.message .self .avatar {
  float: right;
  margin: 0 0 0 10px;
}

.message .self .text {
  background-color: #dae9ec;
}

.message .self .text:before {
  right: inherit;
  left: 100%;
  border-right-color: transparent;
  border-left-color: #dae9ec;
}

.message img {
  max-width: 200px;
}

img.static-emotion-gif,
img.static-emotion {
  vertical-align: middle !important;
}

.an-move-left {
  left: 0;
  animation: moveLeft 0.5s ease;
  -webkit-animation: moveLeft 0.5s ease;
}

.an-move-right {
  left: 0;
  animation: moveRight 0.5s ease;
  -webkit-animation: moveRight 0.5s ease;
}

.bgnone {
  background: none;
}

.message ::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 0 #ffffff;
  background: #fff;
}

@keyframes moveRight {
  0% {
    left: -20px;
    opacity: 0;
  }

  100% {
    left: 0;
    opacity: 1;
  }
}

@-webkit-keyframes moveRight {
  0% {
    left: -20px;
    opacity: 0;
  }

  100% {
    left: 0px;
    opacity: 1;
  }
}

@keyframes moveLeft {
  0% {
    left: 20px;
    opacity: 0;
  }

  100% {
    left: 0px;
    opacity: 1;
  }
}

@-webkit-keyframes moveLeft {
  0% {
    left: 20px;
    opacity: 0;
  }

  100% {
    left: 0px;
    opacity: 1;
  }
}

@media (max-width: 367px) {
  .fzDInfo {
    width: 82%;
  }
}

/deep/.el-divider__text {
  color: #909399;
  padding: 0 10px;
}

/deep/.el-divider {
  background-color: #dcdee3;
}

.leave-message-box {
  margin-bottom: 15px;
  left: 0;
  position: relative;
  display: block;
  padding: 0 14px;
}
</style>
<style lang="scss">
.im_container_content .message .recall {
  text-align: center;
  font-size: 12px;
  color: #909399;
}

.contextmenu {
  position: absolute;
  z-index: 999;
  min-width: 60px;
  background: #fff;
  padding: 3px 0px;
  font-size: 14px;
  font-family: PingFangSC, PingFangSC-Regular;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #ebeef5;

  ul {
    margin: 0;
    height: 26px;
    line-height: 26px;
    padding: 0 6px;
    cursor: pointer;

    &:hover {
      background-color: #ebf4f6;
      color: #62aab9;
    }
  }
}

.message .text>p {
  margin: initial;
}

.message .text {

  video {
    padding: 8px 0 0;
    box-sizing: border-box;
    max-width: 100% !important;
  }

  // 订单消息样式 begin
  /deep/ .order-msg-bubble {
    width: 100%;
    max-width: 360px;
    // background-color: #ffffff;
    padding: 4px 0;
    box-sizing: border-box;
    border-radius: 8px;
    cursor: pointer;

    .order-msg-content {
      width: 100%;
      padding-bottom: 10px;
      box-sizing: border-box;
      overflow: hidden;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;

      .order-msg-img {
        flex-shrink: 0;
        width: 54px;
        height: 54px;
        margin-top: 0 !important;
        border: 1px solid #eeeeee;
        box-sizing: border-box;
        border-radius: 6px;
      }

      .order-msg-info {
        flex-grow: 1;
        padding: 0 0 0 8px;
        box-sizing: border-box;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;

        .order-msg-info-sec {
          width: 100%;
          margin-bottom: 10px;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;

          &:last-child {
            margin-bottom: 0;
          }

          .order-msg-sec-ttl {
            flex-shrink: 0;
            width: 75px;
            line-height: 1;
            color: #888888;
            font-size: 14px;
            font-weight: 400;
            font-family: PingFangSC;
          }

          .order-msg-sec-cnt {
            flex-grow: 1;
            line-height: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #222222;
            font-size: 14px;
            font-weight: 400;
            font-family: PingFangSC;

            &.t-light {
              color: #fc3c17;
            }
          }
        }
      }
    }

    .order-msg-no {
      width: 100%;
      padding-top: 10px;
      box-sizing: border-box;
      border-top: 1px solid #ebe7e7;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;

      .order-msg-no-ttl {
        flex-shrink: 0;
        width: 75px;
        line-height: 1;
        color: #888888;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFangSC;
      }

      .order-msg-no-cnt {
        flex-grow: 1;
        line-height: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #222222;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFangSC;
      }
    }
  }

  // 订单消息样式 end

  // 商品消息样式 begin
  /deep/ .product-msg-bubble {
    width: 100%;
    max-width: 360px;
    // background-color: #ffffff;
    padding: 4px 0;
    box-sizing: border-box;
    border-radius: 8px;
    cursor: pointer;

    .product-msg-content {
      width: 100%;
      overflow: hidden;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;

      .product-msg-img {
        flex-shrink: 0;
        width: 54px;
        height: 54px;
        margin-top: 0 !important;
        border: 1px solid #eeeeee;
        box-sizing: border-box;
        border-radius: 6px;
      }

      .product-msg-info {
        flex-grow: 1;
        padding: 0 0 0 8px;
        box-sizing: border-box;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;

        .product-msg-info-sec {
          width: 100%;
          margin-bottom: 10px;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;

          &:last-child {
            margin-bottom: 0;
          }

          .product-msg-sec-ttl {
            flex-shrink: 0;
            width: 75px;
            line-height: 1;
            color: #888888;
            font-size: 14px;
            font-weight: 400;
            font-family: PingFangSC;
          }

          .product-msg-sec-cnt {
            flex-grow: 1;
            line-height: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #222222;
            font-size: 14px;
            font-weight: 400;
            font-family: PingFangSC;

            &.t-light {
              color: #fc3c17;
            }
          }
        }
      }
    }
  }

  // 商品消息样式 end
}
</style>
