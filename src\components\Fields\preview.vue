<template>
  <!-- 预览 or 输入 有refs即验证输入-->
  <el-form
    v-if="reFresh"
    :model="previewModal"
    :ref="refs"
    :rules="refs ? rules : {}"
    :class="['preview', previewEdit ? 'previewList' : '']"
    @submit.native.prevent
  >
    <!-- 单行预览 -->
    <template v-if="keys === 0">
      <el-form-item
        :label="previewModal.fieldText"
        :prop=" requirs ? 'defaultValue' : ''"
        class="single"
      >
        <template v-if="previewModal.fieldText.length>wordsLen" slot="label">
          <pop-label :data="previewModal.fieldText" :words-len="wordsLen"></pop-label>
        </template>
        <el-col>
          <el-input
            :placeholder="previewModal.tips"
            v-model="previewModal.defaultValue"
            :maxlength="previewModal.limitNumberCharacters"
            :disabled="readOnly || read"
          >
            <!-- 搜索按钮,订单编号 -->
            <!-- 药帮忙S00009999,宜块钱S00009997 -->
            <template v-if="
              previewModal.fieldCode == 's1176037234466492416' && 
              ['S00009999', 'S00009997'].includes(computerBusinessPartCode)"
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="commonKeywordSearch({
                  fieldCode: previewModal.fieldCode, 
                  keyword: previewModal.defaultValue.trim(),
                  disabled: readOnly || read
                })"
              ></i>
            </template>
          </el-input>
          <el-button
            v-if="delItem"
            class="close-item"
            plain
            icon="el-icon-close"
            size="mini"
            @click="delItemClick"
          />
        </el-col>
      </el-form-item>
    </template>
    <!-- 多行输入 -->
    <template v-if="keys === 1">
      <el-form-item
        :label="previewModal.fieldText"
        :prop=" requirs ? 'defaultValue' : ''"
        class="single"
      >
        <template v-if="previewModal.fieldText.length>wordsLen" slot="label">
          <pop-label :data="previewModal.fieldText" :words-len="wordsLen"></pop-label>
        </template>
        <el-col>
          <el-input
            :placeholder="previewModal.tips"
            v-model="previewModal.defaultValue"
            :autosize="{ minRows: 5 }"
            :maxlength="previewModal.limitNumberCharacters"
            :disabled="readOnly || read"
            show-word-limit
            type="textarea"
          />
          <el-button
            v-if="delItem"
            class="close-item"
            plain
            icon="el-icon-close"
            size="mini"
            @click="delItemClick"
          />
        </el-col>
      </el-form-item>
    </template>
    <!-- 下拉列表 -->
    <template v-if="keys === 2">
      <el-form-item
        :label="previewModal.fieldText"
        :prop=" requirs? 'optionSettings.selectOptions.optionsValue' : ''"
        class="single"
      >
        <template v-if="previewModal.fieldText.length>wordsLen" slot="label">
          <pop-label :data="previewModal.fieldText" :words-len="wordsLen"></pop-label>
        </template>
        <el-col>
          <el-select
            v-model="previewModal.optionSettings.selectOptions.optionsValue"
            :placeholder="previewModal.tips"
            :disabled="readOnly || read"
            filterable
          >
            <el-option
              v-for="(item, index) in previewModal.optionSettings.selectOptions.optionsArray"
              :key="index"
              :label="item.optionsDesc"
              :value="item.val?item.val:index"
            />
          </el-select>
          <el-button
            v-if="delItem"
            class="close-item"
            plain
            icon="el-icon-close"
            size="mini"
            @click="delItemClick"
          />
        </el-col>
      </el-form-item>
    </template>
    <!-- 单选 -->
    <template v-if="keys === 3">
      <el-form-item
        :label="previewModal.fieldText"
        :prop=" requirs ? 'optionSettings.radioOptions.optionsValue' : ''"
        class="single"
      >
        <template v-if="previewModal.fieldText.length>wordsLen" slot="label">
          <pop-label :data="previewModal.fieldText" :words-len="wordsLen"></pop-label>
        </template>
        <el-col>
          <el-radio-group v-model="previewModal.optionSettings.radioOptions.optionsValue">
            <el-radio
              v-for="(item, index) in previewModal.optionSettings.radioOptions.optionsArray"
              :key="index"
              :label="item.val?item.val:index"
              :disabled="readOnly || read"
            >{{ item.optionsDesc }}</el-radio>
          </el-radio-group>
          <el-button
            v-if="delItem"
            class="close-item"
            plain
            icon="el-icon-close"
            size="mini"
            @click="delItemClick"
          />
        </el-col>
      </el-form-item>
    </template>
    <!-- 多选 -->
    <template v-if="keys === 4">
      <el-form-item
        :label="previewModal.fieldText"
        :prop=" requirs? 'optionSettings.checkedOptions.optionsValue' : ''"
        class="single"
      >
        <template v-if="previewModal.fieldText.length>wordsLen" slot="label">
          <pop-label :data="previewModal.fieldText" :words-len="wordsLen"></pop-label>
        </template>
        <el-col>
          <el-checkbox-group v-model="previewModal.optionSettings.checkedOptions.optionsValue">
            <el-checkbox
              v-for="(item, index) in previewModal.optionSettings.checkedOptions.optionsArray"
              :key="index"
              :label="item.val?item.val:index"
              :disabled="readOnly || read"
              >{{ item.optionsDesc }}</el-checkbox>
          </el-checkbox-group>
          <el-button
            v-if="delItem"
            class="close-item"
            plain
            icon="el-icon-close"
            size="mini"
            @click="delItemClick"
          />
        </el-col>
      </el-form-item>
    </template>
    <!-- 日期 -->
    <template v-if="keys === 5">
      <el-form-item
        :label="previewModal.fieldText"
        :prop=" requirs ? 'optionSettings.dateOptions.dateValue' : ''"
        class="single"
      >
        <template v-if="previewModal.fieldText.length>wordsLen" slot="label">
          <pop-label :data="previewModal.fieldText" :words-len="wordsLen"></pop-label>
        </template>
        <el-col class="date-box date-field">
          <div :class="['date-show', read ? 'show-back': '']" style="width: 100%">
            <i class="el-icon-time"></i>
            <span
              v-if="previewModal.optionSettings.dateOptions.dateValue === ''"
              class="color-plah"
            >{{ previewModal.tips }}</span>
            <span v-else style="display: inline-block;width: 100%">
              {{ previewModal.optionSettings.dateOptions.dateValue | dataTime(previewModal.optionSettings.dateOptions.dateSelect) }}
              <svg-icon icon-class="close" @click.stop.prevent="delDateVal" />
            </span>
          </div>
          <el-date-picker
            v-model="previewModal.optionSettings.dateOptions.dateValue"
            :disabled="readOnly || read"
            :type="previewModal.optionSettings.dateOptions.dateSelect==='yy-mm-dd HH:ss:nn'?'datetime':'date'"
            class="date-opcity"
            value-format="timestamp"
          ></el-date-picker>

          <el-button
            v-if="delItem"
            class="close-item"
            plain
            icon="el-icon-close"
            size="mini"
            @click="delItemClick"
          />
        </el-col>
      </el-form-item>
    </template>
    <!-- 级联 -->
    <template v-if="keys === 6">
      <el-form-item
        :label="previewModal.fieldText"
        :prop=" requirs ? 'optionSettings.treeOptions.optionsValue' : ''"
        class="single"
      >
        <template v-if="previewModal.fieldText.length>wordsLen" slot="label">
          <pop-label :data="previewModal.fieldText" :words-len="wordsLen"></pop-label>
        </template>
        <el-col class="date-box">
          <el-cascader
            ref="myCascader"
            v-model="previewModal.optionSettings.treeOptions.optionsValue"
            :placeholder="previewModal.tips"
            :options="previewModal.optionSettings.treeOptions.optionsArray"
            :disabled="readOnly || read"
            popper-class="questionClass"
            filterable
            @change="handleCascader"
            @expand-change="handleExpandCascader"
          >
            <template slot-scope="{ node, data }">
              <span>{{ data.label }}</span>
            </template>
          </el-cascader>
          <el-button
            v-if="delItem"
            class="close-item"
            plain
            icon="el-icon-close"
            size="mini"
            @click="delItemClick"
          />
        </el-col>
      </el-form-item>
    </template>
    <!-- 省市区 -->
    <template v-if="keys === 7">
      <el-form-item
        :label="previewModal.fieldText"
        :prop=" requirs ? 'optionSettings.cityOptions.optionsValue' : ''"
        class="single city"
      >
        <template v-if="previewModal.fieldText.length>wordsLen" slot="label">
          <pop-label :data="previewModal.fieldText" :words-len="wordsLen"></pop-label>
        </template>
        <el-col class="date-box">
          <el-popover
            :disabled="readOnly || read"
            v-model="showCity"
            placement="bottom"
            trigger="click"
          >
            <el-tabs v-loading="loading" v-model="activeName" type="card" class="tabs-pane-box">
              <el-tab-pane label="省" name="first" class="city-item">
                <div v-if="cityArray.first && cityArray.first.length > 0">
                  <div
                    v-for="(item, index) in cityArray.first"
                    :key="index"
                    class="item-list"
                    @click="selectCity('first', 'second', item)"
                  >{{ item.areaName }}</div>
                </div>
              </el-tab-pane>
              <el-tab-pane
                :disabled="previewModal.optionSettings.cityOptions.optionsValue.length < 1"
                label="市"
                name="second"
                class="city-item"
              >
                <div v-if="cityArray.second && cityArray.second.length > 0">
                  <div
                    v-for="(item, index) in cityArray.second"
                    :key="index"
                    class="item-list"
                    @click="selectCity('second', 'third', item)"
                  >{{ item.areaName }}</div>
                </div>
              </el-tab-pane>
              <el-tab-pane
                :disabled="previewModal.optionSettings.cityOptions.optionsValue.length < 2"
                label="区"
                name="third"
                class="city-item"
              >
                <div v-if="cityArray.third && cityArray.third.length > 0">
                  <div
                    v-for="(item, index) in cityArray.third"
                    :key="index"
                    class="item-list"
                    @click="selectCity('third', 'fourth', item)"
                  >{{ item.areaName }}</div>
                </div>
              </el-tab-pane>
              <el-tab-pane
                :disabled="previewModal.optionSettings.cityOptions.optionsValue.length < 3"
                label="街道"
                name="fourth"
                class="city-item"
              >
                <div v-if="cityArray.fourth && cityArray.fourth.length > 0">
                  <div
                    v-for="(item, index) in cityArray.fourth"
                    :key="index"
                    class="item-list"
                    @click="selectCity('fourth', '', item)"
                  >{{ item.areaName }}</div>
                </div>
              </el-tab-pane>
            </el-tabs>
            <div
              slot="reference"
              :class="['date-show', read ? 'show-back': '', previewModal.optionSettings.cityOptions.optionsValue.length > 0? '' : 'color-plah']"
              @click="cityClick"
            >{{ previewModal.optionSettings.cityOptions.optionsValue.join('/') || previewModal.tips || '请选择' }}</div>
          </el-popover>
          <el-button
            v-if="delItem"
            class="close-item"
            plain
            icon="el-icon-close"
            size="mini"
            @click="delItemClick"
          />
        </el-col>
        <el-input
          v-model="previewModal.optionSettings.cityOptions.optionsArray"
          :disabled="readOnly || read"
          :autosize="{ minRows: 5 }"
          maxlength="100"
          style="margin-top: 10px"
          placeholder="请输入详细地址：如 道路、门牌号、小区、楼栋号、单元 等"
          show-word-limit
          type="textarea"
        />
      </el-form-item>
    </template>
    <!-- 电话 -->
    <template v-if="keys === 8">
      <el-form-item
        :label="previewModal.fieldText"
        :rules="[
          {required: requirs, message: '请输入必填项', trigger: 'blur'},
          {validator: validateMobile, trigger: 'change' }
        ]"
      prop="defaultValue"
      class="single"
      >
        <template v-if="previewModal.fieldText.length>wordsLen" slot="label">
          <pop-label :data="previewModal.fieldText" :words-len="wordsLen"></pop-label>
        </template>
        <el-col>
          <el-input
            :placeholder="previewModal.tips"
            v-model="previewModal.defaultValue"
            :disabled="readOnly || read"
            :maxlength="20"
          >
            <!-- 客户电话展示搜索按钮 -->
            <i
              v-if="previewModal.fieldCode === 's1164723822566445056'"
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="reqKeywordSearch(previewModal.fieldCode, previewModal.defaultValue)"
            ></i>
          </el-input>
          <el-button
            v-if="delItem"
            class="close-item"
            plain
            icon="el-icon-close"
            size="mini"
            @click="delItemClick"
          />
        </el-col>
        <div 
          v-if="searchResult && showSearchResult" 
          class="search-popup-list"
        >
          <template v-for="(item, index) in searchResult">
            <div class="search-res-item" :key="index" @click="keywordSearchSelected(item)">
              <div class="name">{{ item.realName }}</div>
              <div class="mobile">{{ item.mobile }}</div>
            </div>
          </template>
        </div>
      </el-form-item>
    </template>
    <!-- 邮箱 -->
    <template v-if="keys === 9">
      <el-form-item
        :label="previewModal.fieldText"
        :rules="[
          {required: requirs, message: '请输入必填项', trigger: 'blur'},
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'change' }
        ]"
        prop="defaultValue"
        class="single"
      >
        <template v-if="previewModal.fieldText.length>wordsLen" slot="label">
          <pop-label :data="previewModal.fieldText" :words-len="wordsLen"></pop-label>
        </template>
        <el-col>
          <el-input
            :placeholder="previewModal.tips"
            v-model="previewModal.defaultValue"
            :maxlength="previewModal.limitNumberCharacters"
            :disabled="readOnly || read"
            @change="validateEmail"
          />
          <el-button
            v-if="delItem"
            class="close-item"
            plain
            icon="el-icon-close"
            size="mini"
            @click="delItemClick"
          />
        </el-col>
      </el-form-item>
    </template>
    <!-- 附件 -->
    <template v-if="keys === 10">
      <!--:prop=" requirs ? 'optionSettings.fileObj.optionsValue' : ''"-->
      <el-form-item
        :label="previewModal.fieldText"
        :prop="requirs ? 'optionSettings.fileObj.optionsValue' : ''"
        class="single"
      >
        <template v-if="previewModal.fieldText.length>wordsLen" slot="label">
          <pop-label :data="previewModal.fieldText" :words-len="wordsLen"></pop-label>
        </template>
        <!--gif bmp jpg jpeg png pdf zip rar mp4 avi-->
        <el-upload
          ref="myUploader"
          :disabled="readOnly || read"
          :before-upload="beforeUpload"
          :action="url"
          :limit="fileNums"
          :with-credentials="true"
          :on-exceed="validateNums"
          :on-success="handleSuccess"
          :on-error="handleError"
          :on-progress="handleProgress"
          :class="[{completed:uploadList.length===fileNums}, previewNew]"
          name="files"
          multiple
          list-type="picture-card"
          accept=".gif, .bmp, .jpg, .jpeg, .png, .pdf, .zip, .rar, .mp4, .avi, .doc, .docx, .ppt, .pptx, .xls, .xlsx, .txt"
          class="upload-file-box"
        >
          <el-button
            slot="trigger"
            size="small"
            type="primary"
            @click="uploadStart"
            class="buttonBtn"
          >
            <span class="el-icon-upload2"></span>
            上传附件
          </el-button>
          <span class="uploadIcon">
            <el-popover
              placement="bottom-start"
              width="359"
              popper-class="popoverAll"
              trigger="hover"
            >
              <p
                style="color: #292933;margin: 0;"
                >附件 请小于500MB，格式仅限gif/bmp/jpg/jpeg/png/pdf/zip/rar/mp4/avi/doc/docx/xls/xlsx/ppt/pptx/txt</p>
              <div slot="reference" class="uploadIconInner">
                <svg-icon class="icon-info" icon-class="info"></svg-icon>
              </div>
            </el-popover>
          </span>
          <div slot="file" slot-scope="{file}" class="filePreview">
            <file-preview
              :file="file"
              :percent="file.percent?file.percent:0"
              :editable="true"
              @abortCallback="abortFile(file)"
              @delCallback="delFile(file)"
            />
          </div>
          <!--          <div slot="tip" class="el-upload__tip">-->
          <!--            <span :title="previewModal.tips">{{ previewModal.tips }}</span>-->
          <!--          </div>-->
        </el-upload>
        <!--<info v-if="!showPopo" :info="[{'title':previewModal.tips}]" :popClassList="popClassList"></info>-->
      </el-form-item>
    </template>

    <!-- 相似工单提醒dialog -->
    <el-dialog
      v-if="repeatWorksheet.dialogVisible"
      :visible.sync="repeatWorksheet.dialogVisible"
      custom-class="worksheet-repeat-Check"
      title="提示"
      width="400"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div>
        近期有内容相似的工单已被创建，工单编号：
        <span class="worderId" style="cursor: pointer" @click="handleViewRepeatWorksheet(repeatWorksheet.workorderId)"
        >{{ repeatWorksheet.workorderNum }}</span> 是否仍要发起？
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCreateRepeatWorksheetCancel">取 消</el-button>
        <el-button type="primary" @click="handleCreateRepeatWorksheetConfirm">继续发起</el-button>
      </span>
    </el-dialog>

    <!-- 搜索订单关联商品弹窗 -->
    <dialog-preview-product ref="refDialogPreviewProduct" @submit="emitSubmitOrderInfo"></dialog-preview-product>
  </el-form>
</template>

<script>
import { getProvinceCityArea, MIME_TYPES } from '@/api/fields';
import filterObj from '../../utils/filter';
import filePreview from '@/components/Fields/file-preview';
import { getDateValue } from '@/utils/tools.js';
import popLabel from './label';
import DialogPreviewProduct from './dialog-preview-product.vue';
import { getWorkorderRepeateCheck } from '@/api/mySheetManage';

// const id = 0;
export default {
  name: 'Preview',
  filters: {
    dataTime: filterObj.dataTime
  },
  components: {
    filePreview,
    popLabel,
    DialogPreviewProduct
  },
  model: {
    prop: 'preview',
    event: 'input'
  },
  // computed:{
  //   info(){
  //     return `[{title:${}]`
  //   }
  // },
  props: {
    previewEdit: {
      // 预览没有实际操作效果
      type: Boolean,
      default: function() {
        return false;
      }
    },
    readOnly: {
      // 是否是预览
      type: Boolean,
      default: function() {
        return false;
      }
    },
    delItem: {
      // 是否显示拖拽中的×删除
      type: Boolean,
      default: function() {
        return false;
      }
    },
    refs: {
      // ref的值
      type: String,
      default: function() {
        return '';
      }
    },
    read: {
      // 是否是只读状态
      type: Boolean,
      default: function() {
        return false;
      }
    },
    keys: {
      // 字段类型
      type: Number,
      default: function() {
        return -1;
      }
    },
    preview: {
      // 预览的参数 输入时v-model的参数
      type: Object,
      default: function() {
        return {};
      }
    },
    action: {
      // 附件上传的地址
      type: String,
      default: function() {
        return '';
      }
    },
    requirs: {
      // 新加  条件必填项
      type: Boolean,
      default: function() {
        return false;
      }
    },
    showPopo: {
      type: Boolean,
      default: function() {
        return false;
      }
    },
    showTips: {
      type: Boolean,
      default: function() {
        return false;
      }
    },
    sheetTypeVal: {
      // 表单类型
      type: String | Number,
      default: '',
    },
    workorderNo: {
      // 工单编号
      type: String,
      default: ''
    },
    sheetPreviewAll: {
      // 模板所有属性
      type: Array,
      default: () => []
    },
    searchResult: {
      type: Array,
      default: function() {
        return [];
      }
    }
  },
  data() {
    return {
      popClassList: 'popClassList',
      fileList: [],
      reFresh: true,
      previewNew: 'previewNew',
      previewModal: {}, // 引入的内容
      previewNo: {}, // 预览无实际结果
      loading: false,
      cityArray: {
        // 省市区box
        first: [],
        second: []
      },
      showCity: false, // city选择容器显示隐藏
      activeName: 'first', // city选择
      rules: {
        defaultValue: [
          { required: true, message: '请输入必填项', trigger: 'blur' }
        ],
        // 下拉
        'optionSettings.selectOptions.optionsValue': [
          { required: true, message: '请输入必填项', trigger: 'change' }
        ],
        // 日期
        'optionSettings.dateOptions.dateValue': [
          {
            type: 'date',
            required: true,
            message: '请选择时间',
            trigger: 'change'
          }
        ],
        // 多选
        'optionSettings.checkedOptions.optionsValue': [
          {
            type: 'array',
            required: true,
            message: '请至少选择一个',
            trigger: 'change'
          }
        ],
        'optionSettings.radioOptions.optionsValue': [
          { required: true, message: '请至少选择一个', trigger: 'change' }
        ], // 单选
        'optionSettings.treeOptions.optionsValue': [
          { required: true, message: '请选择', trigger: 'change' }
        ], // 级联
        'optionSettings.cityOptions.optionsValue': [
          {
            type: 'array',
            required: true,
            message: '请选择省市区',
            trigger: 'change'
          }
        ], // 省市区
        'optionSettings.fileObj.optionsValue': [
          {
            type: 'array',
            required: true,
            message: '请上传文件',
            trigger: 'click'
          }
        ] // 附件
      },
      url: process.env.BASE_API + '/fileUpload/uploadFile',
      fileNums: 10, // 文件最大上传个数
      uploadList: [], // 上传队列
      failureList: [], // 上传失败文件列表
      uploading: false, // 文件上传中
      value1: '',
      templateCode: '',
      currentOPtions: [],
      wordsLen: 4,
      selectedLabel: '', //问题分类级联选择,搜索时无法获取到label,保存递归的值用
      showSearchResult: false,

      //重复工单校验
      repeatWorksheet: {
        workorderId: '',
        workorderNum: '',
        dialogVisible: false
      },
    };
  },
  watch: {
    // 新增监听 创建工单是 切换数据
    preview: {
      handler(newName) {
        this.newPreview(newName);
        this.previewNo = JSON.parse(JSON.stringify(this.preview));
        this.previewModal = this.previewEdit ? this.previewNo : this.preview;
      },
      immediate: true,
      deep: true
    },
    requirs(n, o) {
      this.reFresh = false;
      this.$nextTick(() => {
        this.reFresh = true;
      });
      setTimeout(_ => {
        this.initFiles();
      }, 1000);
    }
  },
  computed: {
    // 获取业务线code
    computerBusinessPartCode: function() {
      if (this.$store.getters.channel && this.$store.getters.channel.businessPartCode) {
        return this.$store.getters.channel.businessPartCode;
      }
      return ''
    }
  },
  created() {
    this.newPreview(this.preview);
  },
  mounted() {
    if (
      this.previewModal.optionSettings &&
      this.previewModal.optionSettings.treeOptions &&
      this.previewModal.optionSettings.treeOptions.optionsArray
    ) {
      this.currentOPtions = JSON.parse(
        JSON.stringify(
          this.previewModal.optionSettings.treeOptions.optionsArray
        )
      );
    }
    this.initDates();
  },
  methods: {
    validateMobile(rule, value, callback) {
      if (String(value).trim() && !/^[0-9\--]*$/.test(String(value).trim())) {
        this.previewModal.defaultValue = String(value)
          .trim()
          .substring(0, this.previewModal.limitNumberCharacters);
        callback(new Error('请输入正确的电话号码'));
      } else {
        value = String(value)
          .trim()
          .substring(0, this.previewModal.limitNumberCharacters);
        this.previewModal.defaultValue = value;
        callback();
      }
    },
    /**
     * 初始化附件数据
     */
    initFiles() {
      if (this.$refs['myUploader']) {
        this.$refs['myUploader'].uploadFiles = this.uploadList;
      }
    },
    initDates() {
      if (this.keys === 5) {
        this.preview.optionSettings.dateOptions.dateValue = this.preview
          .optionSettings.dateOptions.dateValue
          ? this.preview.optionSettings.dateOptions.dateValue
          : getDateValue(this.preview.optionSettings.dateOptions.defaultDate);
      }
    },
    /**
     * 校验 邮件地址
     */
    validateEmail(val) {
      this.$refs[this.refs].validateField('defaultValue');
    },
    delDateVal() {
      this.previewModal.optionSettings.dateOptions.dateValue = '';
    },
    newPreview(newValue) {
      if (this.delItem || this.refs) {
        if (
          newValue.optionSettings !== '' &&
          typeof newValue.optionSettings === 'string'
        ) {
          const rest = newValue.optionSettings.replace(/(&quot;)/g, '"');
          newValue.optionSettings = JSON.parse(rest);
        }
        this.preview = newValue;
        if (this.keys === 10) {
          this.uploadList = [].concat(
            this.preview.optionSettings.fileObj.optionsArray
          );
        }
      }
    },
    // 选择省市区 type 当时是省？nextType 下一个tabs index 选择的地区下标
    selectCity(type, nextType, item) {
      this.activeName = nextType || 'fourth';
      // get数据
      const cityIndex = this.getIndex(type);
      this.previewModal.optionSettings.cityOptions.optionsValue[cityIndex] =
        item.areaName;
      this.previewModal.optionSettings.cityOptions.optionsValue = this.previewModal.optionSettings.cityOptions.optionsValue.slice(
        0,
        cityIndex + 1
      );
      if (nextType) {
        this.getProvinceCity(nextType, item.id);
      } else {
        this.showCity = false;
      }
    },
    // 生成省市区的下标
    getIndex(type) {
      switch (type) {
        case 'first':
          return 0;
        case 'second':
          return 1;
        case 'third':
          return 2;
        case 'fourth':
          return 3;
      }
    },
    // 返回验证结果
    submitForm() {
      return this.$refs[this.refs].validate();
    },
    // 返回删除当前input item
    delItemClick() {
      this.$emit('delItemClick');
    },
    // 附件上传成功回调
    handleSuccess(res, file) {
      if (res.code === 1) {
        if (!res.data.failFiles.length) {
          file.percent = 100;
          const _file = {
            uid: file.uid,
            name: file.name,
            url:
              res.data.baseUrl +
              (res.data.successFiles[0].thumbImagePath ||
                res.data.successFiles[0].group +
                  '/' +
                  res.data.successFiles[0].path), // 预览路径
            path:
              res.data.baseUrl +
              res.data.successFiles[0].group +
              '/' +
              res.data.successFiles[0].path, // 真实路径
            percent: 100,
            raw: {
              type: file.raw.type
                ? file.raw.type
                : 'application/x-rar-compressed',
              size: file.raw.size
            },
            data: res.data.successFiles[0]
          };
          if (!this.previewModal.optionSettings.fileObj.optionsArray) {
            this.previewModal.optionSettings.fileObj.optionsArray = [];
          }
          if (!this.previewModal.optionSettings.fileObj.optionsValue) {
            this.previewModal.optionSettings.fileObj.optionsValue = [];
          }
          this.previewModal.optionSettings.fileObj.optionsArray.push(_file);
          this.previewModal.optionSettings.fileObj.optionsValue.push(_file.url);
          // console.log(this.previewModal.optionSettings.fileObj.optionsArray);
        } else {
          this.$refs['myUploader'].uploadFiles.forEach((el, i) => {
            if (el.uid === file.uid) {
              const _file = this.$refs['myUploader'].uploadFiles.splice(i, 1);
              this.failureList = this.failureList.concat(_file);
            }
            this.uploadList = [].concat(this.$refs['myUploader'].uploadFiles);
          });

          let msg = '';
          if (this.failureList.length > 1) {
            msg = `${this.failureList[0].name}等${this.failureList.length}个文件上传失败`;
          } else {
            msg = `${this.failureList[0].name}上传失败`;
          }
          this.$XyyMessage.error(msg);
        }
      } else {
        this.$XyyMessage.error(res.msg);
      }
      this.uploading = false;
    },
    handleProgress(event, file) {
      if (file) {
        this.uploading = true;
        file.percent = Number(event.percent.toFixed(0));
        if (file.percent > 99) file.percent = 99;
      }
    },
    // 附件上传失败返回
    handleError(res) {
      this.uploading = false;
      this.$XyyMessage.error(`文件上传失败`);
    },
    /**
     * 文件个数超出回调
     */
    validateNums(file, fileList) {
      this.$XyyMessage.error(`最多上传${this.fileNums}个`);
    },
    /**
     * 上传点击事件 初始化上传失败数组
     */
    uploadStart() {
      this.failureList = [];
    },
    // 附件上传之前拦截
    beforeUpload(file) {
      this.uploadList = [].concat(file);
      if (file.size / 1024 / 1000 > 500) {
        // 附件上传大小的限制
        this.$XyyMessage.error(`附件上传大小不能超过500M`);
        return false;
      }

      if (file.type) {
        if (!MIME_TYPES.includes(file.type)) {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: `上传文件格式仅限：<span style="color:red;">gif bmp jpg jpeg png pdf zip rar mp4 avi doc docx xls xlsx ppt pptx txt</span>`, // html代码串
            onSuccess: () => {}
          });
          return false;
        }
      } else {
        // element ui 插件bug 无法检测rar rar文件以后缀方式判断
        const _type = file.name
          .slice(file.name.lastIndexOf('.') + 1)
          .toLowerCase();
        if (_type !== 'rar') {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: `上传文件格式仅限：<span style="color:red;">gif bmp jpg jpeg png pdf zip rar mp4 avi doc docx xls xlsx ppt pptx txt</span>`, // html代码串
            onSuccess: () => {}
          });
          return false;
        }
      }
    },
    // 城市选择
    cityClick() {
      if (!this.readOnly || !this.read) {
        this.getProvinceCity('first', 0);
      }
    },
    // 获取省市区
    getProvinceCity(type, parentId) {
      const that = this;
      that.loading = true;
      getProvinceCityArea({ parentId: parentId }).then(res => {
        const datas = res;
        if (datas.code === 1) {
          that.showCity = true;
          if (datas.data && datas.data.length > 0) {
            that.cityArray[type] = datas.data;
            that.$forceUpdate();
            that.loading = false;
          } else {
            that.showCity = false;
          }
        }
      });
    },
    // 级联菜单
    handleCascader(e) {
      //this.previewModal.optionSettings.treeOptions.optionsLabel = this.$refs.myCascader.getCheckedNodes()[0].pathLabels;
      this.currentOPtions = JSON.parse(
        JSON.stringify(
          this.previewModal.optionSettings.treeOptions.optionsArray
        )
      );

      this.previewModal.optionSettings.treeOptions.optionsLabel = this.getSelectedLabels(
        e,
        this.currentOPtions
      );

      const i = 0;
      this.getResult(this.currentOPtions, e, i);
      this.$set(
        this.previewModal.optionSettings.treeOptions,
        'templateCode',
        this.templateCode
      );

      this.$emit('handleProblemClass', [this.previewModal, this.templateCode], () => {
        this.$nextTick(() => {
          // 问题分类
          if(this.previewModal.fieldCode === 's1164724692221825024') {
            // 基于问题分类和订单编号，查询此订单号是否有重复工单
            // 重置重复工单参数
            this.repeatWorksheet = {
              workorderId: '',
              workorderNum: '',
              dialogCancel: () => {},
              dialogConfirm: () => {},
              dialogVisible: false
            }
            this.checkWorksheetRepeat().then(() => {
              // 无重复工单
              // 无操作
            }).catch((respRepeatWorksheet) => {
              // 有重复工单，弹窗
              this.repeatWorksheet = {
                workorderId: respRepeatWorksheet.workorder_id,
                workorderNum: respRepeatWorksheet.workorder_num,
                dialogVisible: true
              }
            })
          }
        })
      });
    },



    // 客户电话搜索
    reqKeywordSearch(fieldCode, kayword) {
      console.log('点击搜索', fieldCode, kayword);
      this.showSearchResult = true;
      this.$emit('keywordSearch', fieldCode, kayword);
    },
    // 客户电话搜索,选择
    keywordSearchSelected(item) {
      this.$emit('keywordSelected', item);
      this.showSearchResult = false;
    },

    // 通用关键词搜索
    async commonKeywordSearch({ fieldCode, keyword, disabled }) {
      if(disabled) {
        return
      }
      // 订单编号
      if(fieldCode == 's1176037234466492416') {
        if (!keyword) {
          this.$XyyMessage.error("请输入订单编号")
          return
        }
        // 基于问题分类和订单编号，查询此订单号是否有重复工单
        // 重置重复工单参数
        this.repeatWorksheet = {
          workorderId: '',
          workorderNum: '',
          dialogCancel: () => {},
          dialogConfirm: () => {},
          dialogVisible: false
        }
        this.checkWorksheetRepeat().then(() => {
          // 无重复工单
          this.$refs.refDialogPreviewProduct.init({ orderNo: keyword }) // 查询订单号关联的商品
        }).catch((respRepeatWorksheet) => {
          // 有重复工单，弹窗
          this.repeatWorksheet = {
            workorderId: respRepeatWorksheet.workorder_id,
            workorderNum: respRepeatWorksheet.workorder_num,
            dialogConfirm: () => {
              this.$refs.refDialogPreviewProduct.init({ orderNo: keyword }) // 查询订单号关联的商品 
            },
            dialogVisible: true
          }
        })
      }
    },
    // 检验新增工单是否有相同的问题分类和订单编号
    checkWorksheetRepeat() {
      return new Promise(async (resolve, reject) => {
        const fieldOrderNoProblem = []
        this.sheetPreviewAll.forEach(el => {
          // s1164724692221825024:问题分类,s1176037234466492416:订单编号
          if ( ['s1164724692221825024', 's1176037234466492416'].includes(el.fieldCode)) {
            fieldOrderNoProblem.unshift(el);
          }
        });
        console.log(fieldOrderNoProblem)
        if(fieldOrderNoProblem.length) {
          let problemClassification, orderNumber;
          fieldOrderNoProblem.forEach(el => {
            if (el.fieldCode == 's1164724692221825024') {
              let values = []
              if (el.optionSettings.treeOptions && el.optionSettings.treeOptions.optionsValue) {
                values = el.optionSettings.treeOptions.optionsValue
              }
              problemClassification = values.join(',');
            } else if (el.fieldCode === 's1176037234466492416') {
              orderNumber = el.defaultValue.trim()
            }
          })
          console.log(problemClassification,orderNumber)
          try {
            const {code, data, msg} = await getWorkorderRepeateCheck(orderNumber, problemClassification, this.sheetTypeVal, this.workorderNo)
            if (code == 1) {
              resolve()
            } else {
              reject(data)
            }
          } catch(e) {
            this.$XyyMessage.error(e.message)
          }
        } else {
          resolve()
        }
      })
    },
    // 重复工单查看
    handleViewRepeatWorksheet(id) {
      this.repeatWorksheet.dialogVisible = false
      this.$emit('onCloseRepeatWorksheetDialog')
      this.$router.push({
        path: '/workStatus' + this.sheetTypeVal + '/sheetDetail/' + id,
        query: { id: id, type: 'newSheet' }
      });
    },
    // 重复工单取消发起
    handleCreateRepeatWorksheetCancel() {
      this.repeatWorksheet.dialogVisible = false
      this.repeatWorksheet.dialogCancel && this.repeatWorksheet.dialogCancel();
    },
    // 重复工单继续发起
    handleCreateRepeatWorksheetConfirm() {
      this.repeatWorksheet.dialogVisible = false
      this.repeatWorksheet.dialogConfirm && this.repeatWorksheet.dialogConfirm();
    },
    // 订单号搜索结果,选择
    emitSubmitOrderInfo(orderInfo) {
      this.$emit('queryOrderInfo', orderInfo)
    },



    /**
     * 级联组件bug解决
     */
    getSelectedLabels(e, options) {
      const lables = [];
      e.forEach(item => {
        this.selectedLabel = '';
        this.formatLabel(item, options);
        lables.push(this.selectedLabel);
      });
      return lables;
    },
    formatLabel(value, options) {
      options.forEach(item => {
        if (value === item.value) {
          this.selectedLabel = item.label;
        } else {
          if (item.children && item.children.length) {
            this.formatLabel(value, item.children);
          }
        }
      });
    },
    getResult(arr, value, i) {
      arr.forEach(item => {
        if (item.value == value[i]) {
          if (item.children && item.children.length > 0) {
            i++;
            this.getResult(item.children, value, i);
          } else {
            this.templateCode = item.templateCode.toString().trim();
          }
        }
      });
    },
    handleExpandCascader(e) {
      if (e.length > 0) {
        this.previewModal.optionSettings.treeOptions.optionsValue = e;
      }
      // this.currentOPtions.filter(item => {
      //   if (item.value === e[e.length - 1] && item.children.length > 0) {
      //     this.currentOPtions = item.children;
      //   }
      // });
    },
    // 附件删除
    delFile(file) {
      if (this.readOnly || this.read) {
        return;
      }
      // 删除上传队列中的数据
      this.$refs['myUploader'].uploadFiles.forEach((el, i) => {
        if (el.uid === file.uid) {
          this.$refs['myUploader'].uploadFiles.splice(i, 1);
        }
      });
      this.uploadList = [].concat(this.$refs['myUploader'].uploadFiles);
      // 删除存储数据
      const arr = [].concat(
        this.previewModal.optionSettings.fileObj.optionsArray
      );
      arr.forEach((el, i) => {
        if (el.uid === file.uid) {
          this.previewModal.optionSettings.fileObj.optionsArray.splice(i, 1);
          this.previewModal.optionSettings.fileObj.optionsValue.splice(i, 1);
        }
      });
      // console.log(this.previewModal.optionSettings.fileObj.optionsArray);
    },
    /**
     * 取消上传
     */
    abortFile(file) {
      this.$refs['myUploader'].abort(file);
      this.$refs['myUploader'].uploadFiles.forEach((el, i) => {
        if (el.uid === file.uid) {
          this.$refs['myUploader'].uploadFiles.splice(i, 1);
        }
      });
      this.uploadList = [].concat(this.$refs['myUploader'].uploadFiles);
      this.uploading = false;
    }
  }
};
</script>
<style lang="scss">
.questionClass {
  .is-empty {
    height: 25px;
  }
}
</style>
<style lang="scss" scoped>
.buttonBtn {
  color: #292933;
}
/deep/.el-icon-upload2 {
  color: #292933;
}
.popClassList {
  /deep/header {
    color: #292933;
  }
}
.uploadIcon {
  position: absolute;
  top: 0;
  left: 111px;
  p {
    font-size: 12px;
  }
  .uploadIconInner {
    cursor: pointer;
    width: 20px;
    height: 20px;
  }
}
.previewNew {
  position: relative;
  /deep/.el-upload--picture-card {
    width: 102px;
    height: 36px;
    display: inline-block;
    line-height: 32px;
    border: 0 none;
    position: absolute;
    top: 0;
    left: 0;
    .el-button {
      width: 102px;
      height: 36px;
      padding-left: 12px !important;
      padding-top: 12px !important;
      background: rgba(255, 255, 255, 1);
      border-radius: 4px;
      border: 1px solid rgba(228, 228, 235, 1);
      color: rgba(87, 87, 102, 1);
    }
  }
  /deep/.el-upload-list--picture-card {
    display: inline-block;
    width: 271px;
    margin-top: 42px;
  }
  /deep/.el-upload-list__item {
    width: 126px;
    height: 30px;
    float: left;
    background: rgba(245, 247, 250, 1);
    border-radius: 4px;
    border: 1px solid rgba(228, 228, 235, 1);
    overflow: visible;
    margin: 0 6px 6px 0;
  }
}
.preview {
  text-align: left;
  /deep/.is-required {
    /deep/.el-form-item__label {
      display: flex;
      /deep/.item-label {
        text-align: left;
      }
    }
  }
  .el-form-item__label {
    line-height: 27px;
  }
  .single {
    margin: 20px;
    position: relative;
    .el-form-item__content {
      .el-col-24 {
        display: flex;
        align-items: start;
      }
    }
    span {
      display: inline-block;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    .el-input,
    .el-textarea,
    .el-select,
    .el-date-editor,
    .el-cascader {
      vertical-align: top;
      width: 100%;
    }

    .el-radio {
      position: relative;
      margin-right: 20px;
      padding-left: 20px;
      /deep/.el-radio__label {
        word-break: break-all;
        white-space: normal;
      }
      /deep/.el-radio__input {
        position: absolute;
        top: 0;
        left: 5px;
      }
    }

    .el-checkbox {
      position: relative;
      margin-right: 20px;
      padding-left: 20px;
      /deep/.el-checkbox__label {
        word-break: break-all;
        white-space: normal;
      }
      /deep/.el-checkbox__input {
        position: absolute;
        top: 2px;
        left: 5px;
      }
    }

    .el-textarea {
      /deep/.el-textarea__inner {
        height: 132px !important;
        overflow: auto;
      }
      /deep/.el-input__count {
        line-height: 20px;
        right: 20px;
        bottom: 1px;
        display: none;
      }
      &:hover {
        /deep/.el-input__count {
          display: block;
        }
      }
    }

    // 手机号搜索结果
    .search-popup-list {
      width: 100%;
      margin: 0 auto;
      max-height: 200px;
      overflow: auto;
      background-color: #fff;
      box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.13);
      border: 1px solid rgba(240, 242, 245, 1);
      z-index: 99;
      position: absolute;
      top: 38px;
      color: #292933;
      background: #fff;
      .search-res-item {
        width: 100%;
        padding: 4px 5px; 
        &:hover {
          background: #DAE9EC;
          cursor: pointer;
          color: #3B95A8;
        }
        div {
          line-height: 20px;
        }
        .name {
          width: 100%;
          font-size: 12px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }   
      }
    }
  }
  /* 日期 省市区 */
  .date-box {
    position: relative;
    height: 34px;
    line-height: 34px;
    .el-date-editor,
    .el-cascader {
      width: 100%;
    }
    > span {
      width: 100%;
      margin-right: 0;
      display: block;
    }

    .el-date-editor {
      /deep/.el-input-inner {
        line-height: 36px;
      }
    }

    &.date-field {
      .date-show.show-back {
        padding-left: 35px;
      }
    }

    .date-show {
      width: 100%;
      height: 100%;
      background: #fff;
      padding-left: 35px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      color: #606266;
      &.show-back {
        background: #f5f7fa;
        color: #c0c4cc;
        padding-left: 15px;
      }
      .el-icon-time {
        position: absolute;
        top: 50%;
        left: 15px;
        transform: translateY(-50%);
      }
      .svg-icon {
        float: right;
        width: 9px;
        height: 9px;
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .date-opcity {
      opacity: 0;
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      z-index: 10;
    }
    .color-plah {
      color: #c0c4cc;
    }
  }
  .close-item {
    border: none;
    padding: 0 15px;
  }
}
</style>
<style lang="scss">
.preview {
  /* 样式调整 begin*/
  .el-input__inner {
    height: 36px;
  }
  label.el-form-item__label {
    width: 85px;
    font-weight: normal !important;
  }
  .item-label {
    /*display:inline-block;*/
    font-weight: normal !important;
    width: 85px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-align: right;
    vertical-align: middle;
    float: left;
    font-size: 14px;
    color: #606266;
    line-height: 40px;
    padding: 0 12px 0 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
  .el-form-item__content {
    float: left;
    width: calc(100% - 85px);
  }
  /* end */
  .el-checkbox-group,
  .el-radio-group {
    word-break: break-word;
    white-space: normal;
    line-height: 20px;
    margin-top: 10px;
  }
}
.el-popover.el-popper {
  .tabs-pane-box {
    background: #fff;
    .el-tabs__header {
      margin-bottom: 0;
    }
    .el-tabs__content {
      border: 1px solid #eee;
      border-radius: 4px;
      border-top: none;
      padding: 10px;
    }
    .el-tabs__item {
      width: 70px;
      text-align: center;
    }
    .city-item {
      height: 200px;
      overflow-y: auto;
      .item-list {
        padding: 10px;
        &:hover {
          background: #dae9ec;
        }
      }
    }
  }
}
/* 级联菜单的 下拉选择*/
.el-popper.el-cascader__dropdown {
  .el-cascader-menu__wrap {
    height: auto;
    max-height: 204px;
    overflow-x: hidden;
    padding-bottom: 17px;
  }
}
.el-cascader-node:not(.is-disabled):hover {
  background: #e4e4e4;
}
.el-cascader-node:not(.is-disabled):focus {
  background: #e4e4e4;
}
.el-cascader-node.in-active-path,
.el-cascader-node.is-active {
  background: #dae9ec;
  color: #606266;
  &:hover,
  &:focus {
    background: #dae9ec;
  }
  .el-icon-check.el-cascader-node__prefix {
    display: none;
  }
}
</style>
<style lang="scss">
.el-dialog.worksheet-repeat-Check {
  width: 400px;
  height: 200px;
  .el-dialog__header {
    border: 1px solid rgba(238, 238, 238, 1);
  }
  .el-dialog__body {
    padding: 20px 20px 30px 20px;
    font-size: 14px;
    color: #292933;
    .worderId {
      color: #3b95a8;
    }
  }
}
</style>

