const utils = {
  // 处理时限列表
  timeList: [1, 2, 4, 6, 8, 12, 24, 36, 48, 72],
  // 条件符号列表
  conditionSymbolList: [
    { label: '是', value: 0 },
    { label: '不是', value: 1 },
    { label: '包含', value: 2 },
    { label: '包含任意', value: 3 },
    { label: '是', value: 4 }, // 级联 是
    { label: '不是', value: 5 } // 级联 不是
  ],
  // 对象深拷贝
  deepCopy(obj) {
    var result = Array.isArray(obj) ? [] : {};
    for (var key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          result[key] = this.deepCopy(obj[key]); // 递归复制
        } else {
          result[key] = obj[key];
        }
      }
    }
    return result;
  },
  // 校验提交数据完整性
  validateFormData(formData) {
    let [flag, res] = [true, {}];
    const data = this.deepCopy(formData);
    if (data.nodeType === 3 && data.closeWay === 1) {
      data.nodeType = 4;
    }
    const valideObj = validator[data.nodeType];

    // 终止for循环，使用break
    for (let i = 0; i < valideObj.length; i++) {
      const el = valideObj[i];
      if (!flag) break;
      flag = el.validate(data[el.fieldName], data);
      if (!flag) res = Object.assign(el, { valid: false });
    }
    if (flag) res = { valid: true };

    return res;
  },
  // 默认校验方法
  defaultValidate(val, data) {
    if (!val) return false;
    if (Array.isArray(val) && val.length === 0) return false;
    return true;
  },
  //秒转换成时分秒
  secToHMS(time) {
    return parseInt(time / 3600) + '小时' + parseInt(time % 3600 / 60) + '分' + time % 60 + '秒'
  },
  // 节点关系校验方法
  nodeRelationValidate(val, data) {
    let flag = true;
    if (!val || !val.length) {
      flag = false;
    } else {
      if (data.toNext === 0) {
        // 一个节点
        val.forEach(el => {
          if (el.nodeConditionType === 1) {
            // 其他节点
            if (!el.nextNodeId || !Number(el.nextNodeId)) flag = false;
          }
        });
      } else {
        // 多个节点
        val.forEach(el => {
          if (el.nodeConditionType === 0) {
            // 有条件节点
            if (!el.nextNodeId) flag = false;
            el.nodeConditionList.forEach(item => {
              if (!item.conditionFieldCode || item.conditionSymbol === '' || Number(item.conditionSymbol) === -1 || !String(item.valueFirst)) {
                flag = false;
              }
            });
          } else {
            // 其他节点
            if (!el.nextNodeId || !Number(el.nextNodeId)) flag = false;
          }
        });
      }
    }

    return flag;
  }
};

// 校验对象
const validator = {
  '0': [
    // 开始节点
    {
      fieldName: 'nodeName',
      msg: '请输入节点名称',
      validate: utils.defaultValidate
    },
    {
      fieldName: 'nodeUserList',
      msg: '请选择参与人',
      validate: utils.defaultValidate
    },
    {
      fieldName: 'templateCode',
      msg: '请选择模板',
      validate: utils.defaultValidate
    },
    {
      fieldName: 'nodeRelationList',
      msg: '节点关系设置不完整',
      validate: utils.nodeRelationValidate
    }
  ],
  '1': [
    // 处理节点
    {
      fieldName: 'nodeName',
      msg: '请输入节点名称',
      validate: utils.defaultValidate
    },
    {
      fieldName: 'nodeUserList',
      msg: '请选择参与人',
      validate: utils.defaultValidate
    },
    {
      fieldName: 'processTimeout',
      msg: '请选择处理时限',
      validate: utils.defaultValidate
    },
    {
      fieldName: 'templateCode',
      msg: '请选择模板',
      validate: utils.defaultValidate
    },
    {
      fieldName: 'nodeRelationList',
      msg: '节点关系设置不完整',
      validate: utils.nodeRelationValidate
    }
  ],
  '2': [
    // 抄送节点
    {
      fieldName: 'nodeName',
      msg: '请输入节点名称',
      validate: utils.defaultValidate
    },
    {
      fieldName: 'nodeUserList',
      msg: '请选择参与人',
      validate: utils.defaultValidate
    },
    {
      fieldName: 'nodeRelationList',
      msg: '节点关系设置不完整',
      validate: utils.nodeRelationValidate
    }
  ],
  '3': [
    // 关闭节点--人工关闭
    {
      fieldName: 'nodeName',
      msg: '请输入节点名称',
      validate: utils.defaultValidate
    },

    {
      fieldName: 'nodeUserList',
      msg: '请选择参与人',
      validate: utils.defaultValidate
    },
    {
      fieldName: 'processTimeout',
      msg: '请选择处理时限',
      validate: utils.defaultValidate
    },
    {
      fieldName: 'templateCode',
      msg: '请选择模板',
      validate: utils.defaultValidate
    }
  ],
  '4': [
    // 关闭节点--系统关闭
    {
      fieldName: 'nodeName',
      msg: '请输入节点名称',
      validate: utils.defaultValidate
    }
  ]
};

export default utils;
