.box {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}
.row-center{
  -webkit-box-pack: center;
  -moz-justify-content: center;
  -webkit-justify-content: center;
  justify-content: center;
}
.row-around{
  -moz-justify-content: space-around;
  -webkit-justify-content: space-around;
  justify-content: space-around;
}
.row-start{
  -webkit-box-pack: start;
  -moz-justify-content: flex-start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}
.row-between{
  -webkit-box-pack: justify;
  -moz-justify-content: space-between;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}
.row-wrap {
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.column-center{
  -webkit-box-align: center;
  -moz-align-items: center;
  -webkit-align-items: center;
  align-items: center;
}
.column-bottom{
  -webkit-box-align: end;
  -moz-align-items: end;
  -webkit-align-items: flex-end;
  align-items: flex-end;
}
.column-start{
  -webkit-box-align: start;
  -moz-align-items: start;
  -webkit-align-items: flex-start;
  align-items: flex-start;
}
.notice{
  display:inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background:#FF3024;
}
.el-button{
  font-weight:normal;
}
.ellipsis{
  white-space: nowrap;
  text-overflow:ellipsis;
  overflow:hidden;
}
.m-pop{
  width:360px;
  font-size:14px;
  font-weight:400;
  color:rgba(41,41,51,1);
  box-shadow:none !important;
}
.m-pop{
  padding-top:12px;
  max-height:400px;
  overflow: auto;
}
.m-pop header{
  margin:0;
  text-align:left;
  font-size:14px;
  color:rgba(41,41,51,1);
  max-height:30px !important;
  line-height:20px !important;
  padding:0 !important;
}
.popClassList header {
  color: rgb(230, 162, 60) !important;
}
.m-pop p{
  font-size:12px;
  color:rgba(144,147,153,1);
  line-height:17px;
  margin:0;
  margin-top:2px;
  margin-bottom:17px;
}
.m-pop p a{
  color:#3b95a8;
}

.m-pop p .el-icon-plus {
  color: #3b95a8;
  border: 1px solid #3b95a8;
  font-size: 12px;
  position: relative;
  top: -1px;
}
.explain-question{
  display:inline-block;
  width:30px;
  height:30px;
  line-height:30px;
  margin-left:10px;
}
.info-container{
  text-align:right;
}

.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.animated.infinite {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}

.animated.delay-1s {
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}

.animated.delay-2s {
  -webkit-animation-delay: 2s;
  animation-delay: 2s;
}

.animated.delay-3s {
  -webkit-animation-delay: 3s;
  animation-delay: 3s;
}

.animated.delay-4s {
  -webkit-animation-delay: 4s;
  animation-delay: 4s;
}

.animated.delay-5s {
  -webkit-animation-delay: 5s;
  animation-delay: 5s;
}

.animated.fast {
  -webkit-animation-duration: 800ms;
  animation-duration: 800ms;
}

.animated.faster {
  -webkit-animation-duration: 500ms;
  animation-duration: 500ms;
}

.animated.slow {
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
}

.animated.slower {
  -webkit-animation-duration: 3s;
  animation-duration: 3s;
}

@media (print), (prefers-reduced-motion: reduce) {
  .animated {
    -webkit-animation-duration: 1ms !important;
    animation-duration: 1ms !important;
    -webkit-transition-duration: 1ms !important;
    transition-duration: 1ms !important;
    -webkit-animation-iteration-count: 1 !important;
    animation-iteration-count: 1 !important;
  }
}

@-webkit-keyframes slideInLeft {
  from {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInLeft {
  from {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.slideInLeft {
  -webkit-animation-name: slideInLeft;
  animation-name: slideInLeft;
}
.fade-enter-active, .fade-leave-active {
  transition: opacity .5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}
.slide-fade-enter-active {
  transition: all .3s ease;
}
.slide-fade-leave-active {
  transition: all .8s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}
.slide-fade-enter, .slide-fade-leave-to
  /* .slide-fade-leave-active for below version 2.1.8 */ {
  transform: translateX(10px);
  opacity: 0;
}
