<template>
  <div id="app">
    <router-view v-if="isRouterAlive" />
  </div>
</template>

<script>
export default {
  name: 'App',
  provide() {
    return {
      reload: this.reload
    };
  },
  data() {
    return {
      isRouterAlive: true
    };
  },
  methods: {
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(function() {
        this.isRouterAlive = true;
        this.$message({
          message: '刷新成功',
          type: 'success',
          customClass: 'customClass'
        });
      });
    }
  }
};
</script>
<style lang="sass">
@import 'styles/comment-tabs.css'
</style>
<style>
.customClass {
  min-width: 150px;
}
</style>
