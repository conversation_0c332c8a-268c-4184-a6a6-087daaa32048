<template>
  <div class>
    <div class="operation">
      <el-button type="primary" size="mini" class="button_qj" @click="dialogVisible = true">抢接</el-button>
      <el-button type="primary" size="mini" class="button_zj" @click="transferAction">转给他人</el-button>
    </div>
    <div class="dialoguecontentall">
      <div class="dialoguecontent">
        <chatleftview
          ref="container_main_leftview"
          :owner-avatar-url="ownerAvatarUrl"
          :kefu-info-data="kefuInfoData"
          :select-number="messageType"
          :user-data-array="userdataIng"
          :ing-number="userdataIng.length"
          :table-height="670"
          class="im_container_main_leftview"
          @im_container_left_clickrow="chatLeftClickRow"
          @im_container_left_clicktype="chatLeftClickType"
          @im_container_left_scroll="chatmoreAction"
          @im_container_left_searchFunction="searchHisFuction"
        />
        <div>
          <topview
            :view-data="currentUserData"
            :black-list-auth="kefuInfoData.blackListAuth"
            :viewtype="topvuewType"
            :container-id="currentContainerID"
            class="im_container_chat_top_view"
            @user_top_view="userTopView"
          ></topview>
          <chatcontent
            ref="newMessage"
            :style="{height:contentMaxHeight+'px'}"
            :owner-avatar-url="ownerAvatarUrl"
            :contact-avatar-url="contactAvatarUrl"
            :chatdata="chatdata"
            :section-name="sectionName"
            :need-event="chatTopScroll"
            :max-height="contentMaxHeight"
            class="im_container_chat_content"
          />
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      :before-close="handleClose"
      title="接管提醒"
      width="30%"
      top="auto"
    >
      <span>是否接管此对话？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogTrueFn">确 定</el-button>
      </span>
    </el-dialog>
    <transdialog
      :data="transdata"
      :dialog-form-visible="transAlerShow"
      @treedialog="transdialogAction"
    ></transdialog>
  </div>
</template>

<script>
import loading from '@/mixin/loading';
import chatcontent from '../im_view/components/container_message/im_container_contentsecond';
import chatleftview from '../im_view/components/container_message/im_container_leftviewsecond';
import topview from '../im_view/components/container_message/im_container_topviewsecond';
import transdialog from '../im_view/components/container_message/treedialogsecond';
// 接口
import {
  readMessageList,
  currentMessageList,
  currentMessageListId,
  onlinekefu,
  toMessageDialog,
  dialogsMessageId,
  sayIngMessage,
} from '@/api/im_view/index';
import { getPersonalData } from '@/api/im-config/personal-setting';
// const currentMessageListId = {};
// import currentMessageListId from '@/dd.json';
// 获取到消息之后的处理方法
import {
  containerType,
  containerMessage,
  userDatasSec,
  topData,
} from '../im_view/components/im_container_tool/message_tool';

export default {
  name: 'Whole',
  components: { chatcontent, chatleftview, transdialog, topview },
  mixins: [loading],
  data() {
    return {
      contentMaxHeight: 470,
      ownerAvatarUrl: '/pc/static/user_kf.png',
      contactAvatarUrl: '/pc/static/user_kh.png',
      chatdata: [
        {
          dialogid: '1548089770856253456',
          type: 203,
          content: '欢迎来访',
          createtime: '2020-03-22 18:31:56',
          kefuid: 2027,
          uid: '0',
          msgStatus: 0,
          msgFrom: 2,
          direction: 2,
          msgtype: 'system',
          ctime: '2020-03-22 18:31:56',
        },
      ],
      sectionName: 'im_container_main',
      chatTopScroll: false, // 是否需要上拉加载更多
      // chatLeftClickRow: {},
      // chatLeftClickType: {},
      chatmoreAction: {},
      searchHisFuction: {},
      kefuInfoData: {},
      messageType: 0, // 当前列表类型
      userdata: [],
      userdataIng: [], // 会话中
      dialogVisible: false,
      cascaderVal: '',
      transAlerShow: false,
      transdata: [],
      currentUserData: { avatar: '', textData: [] },
      currentContainerID: '0', // 当前会话id
      containerDatas: {}, // 消息总数居
      isRunAndRun: true, // 消息轮询开关
      topvuewType: 'ing', // 顶部客户信息简介类型
      iskefuid: '', // 当前用户的客服ID
      isdialogid: '', // 当前对话ID
      getkefuId: '', // 马冬ID
      clickkefuid: '', // 数据中的客服ID
      selectRow: {},
    };
  },
  activated() {
    // 接收路由参数
    this.routerQueryFn();
  },
  mounted() {
    // 获取客服的基本信息
    getPersonalData().then((res) => {
      console.log(res.data.id, 'res.data.id');
      // 客服id
      this.iskefuid = res.data.id;
    });
    // 接收路由参数
    this.routerQueryFn();
    this.topViewVelue();
  },
  methods: {
    // 消息处理
    containerMassage(msg) {
      // console.log(msg, '=======send2');
      var data = containerMessage(msg);
      if (data && data.dialoginfo) {
        const oldAndNew = containerType(
          this.containerDatas,
          data,
          this.currentContainerID === data.dialoginfo.id
        );
        this.containerDatas[data.dialoginfo.id] = oldAndNew.newmsg;
        // 用户列表分类
        userDatasSec(
          oldAndNew.newmsg,
          oldAndNew.oldmsg,
          this.userdataIng,
          this.userdataWill,
          this.userdataEd
        );
      }
    },
    // 初始加载为关闭对话 ???
    getCurrentMessageList() {
      this.$store.commit('imProperty/SET_IMProperty_containerid', undefined);
      this.$store.commit('imProperty/SET_IMProperty_kfid', undefined);
      this.$store.commit('imProperty/SET_IMProperty_khid', undefined);
      currentMessageListId(this.getkefuId).then((res) => {
        // currentMessageList(this.$store.getters.imcreate).then(res => {
        // res = currentMessageListId;
        if (res.code !== 1) {
          return this.$XyyMessage.error(res.msg || '接口超时');
        }
        if (res.data) {
          this.containerDatas = {};
          this.chatdata = []; // 消息数据
          this.userdataIng = []; // 会话中
          this.userdataWill = []; // 待跟进
          this.userdataHis = []; // 历史会话
          this.userdataEd = []; // 留言

          for (let index = 0; index < res.data.length; index++) {
            const element = res.data[index];
            this.containerMassage(element);
          }
        }
        // 左侧数组无值头部信息清空
        if (this.userdataIng.length === 0) {
          this.currentUserData['avatar'] = '';
          this.currentUserData['textData'] = [];
        }
        this.chatLeftClickType(this.messageType);
        // this.getmessage(this.$store.getters.imcreate);
        // this.$store.commit('imProperty/SET_IMProperty_containercreate', '1');
        // 选中列表的第一个对话
        if (
          this.userdataIng &&
          this.userdataIng[0] &&
          this.userdataIng[0].kfid
        ) {
          this.clickkefuid = this.userdataIng[0].kfid;
        } else {
          this.clickkefuid = '';
        }
        this.chatLeftClickTypeING(1);
        this.chatLeftClickRow({ $index: 0, row: this.userdataIng[0] });
        this.$refs.container_main_leftview.tosayIng();
      });
    },
    // 点击左侧人员列表刷新消息
    chatLeftClickRow(val) {
      if (val && val.row) {
        this.clickkefuid = val.row.kfid || '';
        this.selectRow = val.row;
        this.currentContainerID = val.row.containerId || '';
        this.contactAvatarUrl = val.row.avatar;
        this.$store.commit(
          'imProperty/SET_IMProperty_containerid',
          this.currentContainerID
        );
        this.$store.commit('imProperty/SET_IMProperty_kfid', val.row.kfid);
        this.$store.commit('imProperty/SET_IMProperty_khid', val.row.khid);
        // 赋值对话ID
        this.isdialogid = val.row.containerId || '';
        switch (this.messageType) {
          case 0: {
            this.refreshMessage(this.currentContainerID);
            this.topvuewType = 'ing';
            this.chatTopScroll = false;
            this.contentMaxHeight = this.conntentHeight('show');
            this.deleteRedPop(this.currentContainerID, this.userdataIng, val);
            //this.getMessageAlone(this.currentContainerID);
            break;
          }
          case 1: {
            this.refreshMessage(this.currentContainerID);
            this.topvuewType = 'other';
            this.chatTopScroll = false;
            this.contentMaxHeight = this.conntentHeight('other');
            this.userInfomation(val.row);
            this.deleteRedPop(this.currentContainerID, this.userdataWill, val);
            //this.getMessageAlone(this.currentContainerID);
            break;
          }
          case 2: {
            this.topvuewType = 'history';
            this.chatTopScroll = false;
            this.contentMaxHeight = this.conntentHeight('history');
            this.getHistoryMessage(this.currentContainerID);
            this.userInfomation(val.row);
            break;
          }
          case 3: {
            this.refreshMessage(this.currentContainerID);
            this.topvuewType = 'other';
            this.chatTopScroll = false;
            this.contentMaxHeight = this.conntentHeight('other');
            this.userInfomation(val.row);
            this.deleteRedPop(this.currentContainerID, this.userdataEd, val);
            //this.getMessageAlone(this.currentContainerID);
            break;
          }

          default:
            break;
        }
      }
    },
    // 刷新消息
    refreshMessage(currentContainerID) {
      this.refreshChatView = true;
      this.$nextTick(() => {
        this.chatdata = [];
        this.chatdata = this.containerDatas[this.currentContainerID].messages;
        this.refreshChatView = false;
      });
      // 刷新字段条信息
      this.topViewVelue();
    },
    chatLeftClickTypeING(type) {
      // this.messageType = type;
      switch (type) {
        case 0:
          this.userdata = this.userdataIng;
          break;
        case 1:
          this.userdata = this.userdataWill;
          break;
        case 3:
          this.userdata = this.userdataEd;
          break;

        default:
          break;
      }
    },
    // 消除红点
    deleteRedPop(containerId, userdata, val) {
      this.containerDatas[containerId].dialoginfo.unreadNum = 0;
      userdata[val.$index].unreadNum = 0;
    },

    /**
     * 避免与在线会话轮询同一队列
     */
    getMessageAlone(containerId) {
      readMessageList({
        pageId: this.$store.getters.imcreate,
        dialogid:
          this.currentContainerID === '0'
            ? ''
            : containerId || this.currentContainerID,
        t: Math.ceil(Math.random() * 1000000).toString(),
      }).then((res) => {
        // if (res.code === 10001) {
        //   this.$alert('此账号已从别处登录，已停止消息读取', '提示', {
        //     confirmButtonText: '确定',
        //     callback: action => {}
        //   });
        //   this.isRunAndRun = false;
        //   return;
        // }
        if (res.data && res.data.dialoginfo) {
          if (
            res.data.messages.length !== 0 &&
            res.data.messages[0].type === 408
          ) {
            this.zhuanjiedata = res.data;
            this.zhuanjiealert = true;
          } else if (
            res.data.messages.length !== 0 &&
            res.data.messages[0].type === 410
          ) {
            const msgarr = this.userdataIng.filter(
              (item) => item.containerId === this.currentContainerID
            );
            const index = this.userdataIng.indexOf(msgarr[0]);
            this.userdataIng.splice(index, 1);
            delete this.containerDatas[this.currentContainerID];
            if (this.userdataIng.length === 0) {
              this.initContent();
            } else {
              this.nextContent();
            }
            this.$XyyMessage.success('对方已接受转接');
          } else if (
            res.data.messages.length !== 0 &&
            res.data.messages[0].type === 411
          ) {
            this.$XyyMessage.warning('对方拒绝了你的转接');
          } else {
            if (!this.containerDatas.hasOwnProperty(res.data.dialoginfo.id)) {
              this.apisayIngMessage(res.data.dialoginfo.id);
            } else {
              this.containerMassage(res.data);
              if (this.currentContainerID === res.data.dialoginfo.id) {
                const messageLength = this.containerDatas[
                  this.currentContainerID
                ].messages.length;
                const containerData = this.containerDatas[
                  this.currentContainerID
                ];
                var newmessage = containerData.messages[messageLength - 1];
                this.$refs.newMessage.newMessage(newmessage);
              }
            }
          }
        }
      });
    },

    conntentHeight(type) {
      if (type === 'other') {
        return 797 - 40 - 83;
      }
      if (type === 'history') {
        return 797 - 40 - 83;
      } else {
        return type === 'show'
          ? 797 - 40 - 70 - 170 - 47
          : 797 - 40 - 70 - 170 - 136;
      }
    },
    handleClose() {
      this.dialogVisible = false;
    },
    callvaluefn(v) {
      // 获取 cascader 子组中的值
      this.cascaderVal = v;
    },

    apiOnlinekefu() {
      this.transAlerShow = true;
      onlinekefu({ dialogid: this.containerID }).then((res) => {
        // 返回的人员数组信息
        var kefuData = res.data;
        for (let index = 0; index < kefuData.length; index++) {
          const element = kefuData[index];
          element.kefus.splice(0, 0, {
            id: element.id,
            name: element.name,
            type: element.type,
          });
          for (let index1 = 0; index1 < element.kefus.length; index1++) {
            const element1 = element.kefus[index1];
            element1['numrotio'] = '80%';
            if (element1.type === 'group') {
              element1.icon = 'el-icon-transfer-group';
            } else if (element1.type === 'kefu') {
              element1.icon = 'el-icon-transfer-user';
            }
          }
        }

        this.transdata = kefuData;
      });
    },
    // 加载会话中的一些消息
    apisayIngMessage(containerID, type) {
      sayIngMessage({ containerid: containerID }).then((res) => {
        if (res.code !== 1) {
          this.$XyyMessage.error(res.msg);
          return;
        }
        let dataArr = res.data;
        if (dataArr && dataArr.messages && dataArr.messages.length) {
          dataArr.messages.forEach((menuType, menuindex) => {
            if (menuType.type == 310) {
              dataArr.messages.splice(menuindex, 1);
            }
          });
        }
        if (type === 309) {
          this.containerMassage(dataArr);
          this.chatLeftClickType(0);
        }
        this.containerMassage(dataArr);
      });
    },
    // 抢接确认  转接给指定客服
    dialogTrueFn() {
      // this.toDialog();
      // this.dialogVisible = false;
      const obj = {};
      obj['kefuId'] = this.iskefuid;
      obj['dialogid'] = this.isdialogid;
      // 是否是自己的客服ID
      if (this.clickkefuid !== this.iskefuid) {
        toMessageDialog(obj).then((res) => {
          if (res.code === 1) {
            this.dialogVisible = false;
            this.$store.dispatch('tagsView/delView', this.$route);
            this.$router.push({
              path: '/imcontainer/list',
            });
            // 左侧菜单 高亮会话
            this.emits(this, 'send', 'nav_session', 'session');
          } else {
            this.$XyyMessage.error(res.msg || '会话已关闭');
            console.log('错误', res.msg);
          }
        });
      } else {
        this.$message({
          message: '已在当前会话列表中',
          type: 'warning',
        });
      }
    },
    // 转接他人 弹窗的方法回调
    transdialogAction(type, value) {
      const obj = {};
      obj['dialogid'] = this.isdialogid;
      obj['kefuId'] = '';
      if (type === 'clickNode') {
        if (value.type === 'kefu') {
          obj['kefuId'] = value.id;
          if (this.isdialogid) {
            // 是否是自己的客服ID
            if (this.clickkefuid !== value.id) {
              toMessageDialog(obj).then((res) => {
                if (res.code === 1) {
                  this.transAlerShow = false;
                  this.$message.success('成功转接！');
                  if (this.$route.query.dialogid) {
                    this.userdataIng = [];
                    this.chatdata = [];
                    this.currentUserData['avatar'] = '';
                    this.currentUserData['textData'] = [];
                  } else {
                    const index = this.userdataIng.findIndex(
                      (d) => d.containerId === this.selectRow.containerId
                    );
                    this.userdataIng.splice(index, 1);
                    setTimeout(() => this.getCurrentMessageList(), 300);
                  }
                  // this.getCurrentMessageList();
                } else {
                  this.$message.error(res.msg);
                }
              }),
                (error) => {
                  console.log(error);
                };
            } else {
              this.$message({
                message: '已在当前会话列表中',
                type: 'warning',
              });
            }
          } else {
            this.$message({
              message: '请选择一条对话',
              type: 'warning',
            });
          }
        } else {
          // alert('请选择组员');
          this.$message({
            message: '请选择组员',
            type: 'warning',
          });
        }
      }
      if (type === 'hiddenAction') {
        this.transAlerShow = false;
      }
      // if (type === 'hiddenAction') {
      //   // 是否隐藏
      //   this.transAlerShow = value;
      // } else if (type === 'clickNode') {
      //   // 点击跳转的节点
      //   this.$emit('clickInputTool', 'transfera', value);
      // }
    },
    // 转接弹窗弹出
    transferAction() {
      this.transAlerShow = true;
      this.apiOnlinekefu();
    },
    // 获取顶部数据
    topViewVelue() {
      const _this = this;
      if (_this.currentContainerID !== '0' && _this.messageType === 0) {
        var containerData = _this.containerDatas[_this.currentContainerID];
        if (containerData === undefined) {
          return;
        }
        _this.currentUserData = topData(containerData);
      }
    },
    // 聊天页上方的工具栏方法
    userTopView(type, data) {
      if (this.currentContainerID === '0') {
        this.$XyyMessage.error('请选择会话');
        return;
      }
      if (type === 'addblacklist') {
        this.apiAddblack(data);
      } else if (type === 'closeContainer') {
        this.apiCloseTalking();
      } else if (type === 'show') {
        this.contentMaxHeight = this.conntentHeight('close');
        this.$refs.newMessage.scrollowToBottom();
      } else if (type === 'close') {
        this.contentMaxHeight = this.conntentHeight('show');
      } else if (type === 'startContainer') {
        this.apiStartTalking();
      }
    },
    // 人员列表类型切换
    chatLeftClickType(type, searchstr) {
      this.historyPageNum = 1;
      this.messageType = type;
      this.userdata = [];
      this.$refs.container_main_leftview.pushToBottom(false);
      this.$nextTick(() => {
        switch (type) {
          case 0: {
            this.userdata = this.userdataIng;
            break;
          }
          case 1: {
            this.userdata = this.userdataWill;
            break;
          }
          case 2: {
            this.getHistoryMessageList(searchstr);
            break;
          }
          case 3: {
            this.userdata = this.userdataEd;
            break;
          }
        }
      });
    },
    // 接收路由参数
    routerQueryFn() {
      if (this.$route.query.dialogid) {
        dialogsMessageId(this.$route.query).then((res) => {
          this.containerMassage(res.data);
          // 去掉style属性
          res.data.messages.map((x) => {
            x.originalContent = x.originalContent.replace(/style/gi, '');
          });
          this.chatLeftClickRow({ $index: 0, row: this.userdataIng[0] });
          this.$refs.container_main_leftview.tosayIng();
          if (res.data && res.data.dialoginfo) {
            // 数据上的客服ID
            this.clickkefuid = res.data.dialoginfo.kefuid;
          }
        });
      } else {
        this.getkefuId = this.$route.query.kefuId;
        this.getCurrentMessageList();
      }
    },
    // 初始化聊天页面
    initContent() {
      this.chatLeftClickType(0);
      this.currentContainerID = '0';
      this.chatdata = [];
      this.currentUserData = { avatar: '', textData: [] };
    },

    // 会话中的第一个聊天会话
    nextContent() {
      this.chatLeftClickTypeING(0);
      this.chatLeftClickRow({ $index: 0, row: this.userdataIng[0] });
      this.$nextTick(() => {
        this.$refs.container_main_leftview.tosayIng();
      });
    },
    // =============
  },
};
</script>
<style>
.el-dialog {
  margin: auto;
}
</style>
<style lang="scss" scoped>
.el-dialog__wrapper {
  display: flex;
  flex-direction: column;
}
.operation {
  padding: 60px 0 20px;
  width: 1000px;
  margin: auto;
}
.dialoguecontentall {
  width: 1000px;
  margin: 0 auto;
  border: 1px solid #dcdee3;
  border-radius: 2px;
  overflow: hidden;
}
.dialoguecontent {
  display: flex;
  justify-content: center;
  min-width: 1000px;
}
.im_container_chat_content {
  width: auto;
}
.button_qj {
  width: 71px;
  height: 36px;
  border: 1px solid #e4e4eb;
  border-radius: 2px;
  color: #575766;
  font-size: 14px;
  padding-left: 26px;
  background: url(../../assets/monitor/button_qj.png) no-repeat 10px 10px / 16px
    auto;
}
.button_qj:hover {
  border: 1px solid #40a3b8;
  color: #3b95a8;
  background: url(../../assets/monitor/button_qjhover.png) no-repeat 10px 10px /
    16px auto;
}
.button_zj {
  width: 102px;
  height: 36px;
  border: 1px solid #e4e4eb;
  border-radius: 2px;
  color: #575766;
  font-size: 14px;
  padding-left: 30px;
  background: url(../../assets/monitor/button_zj.png) no-repeat 10px 10px / 16px
    auto;
}
.button_zj:hover {
  border: 1px solid #40a3b8;
  color: #3b95a8;
  background: url(../../assets/monitor/button_zjhover.png) no-repeat 10px 10px /
    16px auto;
}
</style>
