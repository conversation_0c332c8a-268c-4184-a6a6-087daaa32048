<template>
  <div class="gdnumclass">
    <xyy-list-page>
      <template slot="header">
        <el-row class="herader-title" type="flex" justify="space-between" align="middle">
          <span style="font-size:16px">工单量相关</span>
          <el-button icon="el-icon-upload2" class="export-excel" @click="exportData">导出Excel</el-button>
        </el-row>
        <el-form
          ref="listQuery"
          :rules="rules"
          :inline="true"
          :model="listQuery"
          label-position="right"
          class="search-form"
        >
          <el-row :gutter="10" type="flex" class="row-bg" justify="space-between">
            <el-form-item label="日期" prop="dataRange">
              <el-date-picker
                :picker-options="pickerOptions"
                v-model="listQuery.dataRange"
                type="daterange"
                range-separator="-"
                size="small"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                text="erdf"
                prefix-icon="el-icon-date"
                class="innerSelTime"
                @change="queryTime()"
                @focus="dateTimeFocus()"
              />
            </el-form-item>
            <!--工单类型-->
            <el-form-item label="工单类型" prop="sheetType">
              <el-select v-model="listQuery.sheetType" placeholder="全部">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in oderTypeDta"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <!--客户所在地-->
            <el-form-item label="客户所在地" prop="CustLocation">
              <el-select v-model="listQuery.CustLocation" placeholder="全部">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in oderCityData"
                  :key="item.id"
                  :label="item.sourceName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row style="text-align:right;float:right">
            <el-form-item>
              <div class="searchTwoButton">
                <el-button
                  type="primary"
                  size="small"
                  @click="checkTimer(handerSearch('listQuery'))"
                >查询</el-button>
                <el-button size="small" @click="checkTimer(oldSearch('listQuery'))">重置</el-button>
              </div>
            </el-form-item>
          </el-row>
        </el-form>
      </template>
      <template slot="body">
        <div class="info-container">
          <info :info="infodata()"></info>
        </div>
        <xyy-table
          :is-stripe="false"
          :data="list"
          :col="col"
          :offset-top="240"
          :is-pagination="false"
        ></xyy-table>
      </template>
    </xyy-list-page>
    <expor-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    ></expor-tip>
  </div>
</template>

<script>
import exporTip from '@/views/work-sheet/components/exportTip';
import { mockGDType, mockGDUser } from './components/mock';
import { exportData } from '@/api/formManage';
import {
  selectWorkorderList,
  getCustomerSourceList,
  getWorkorderTypeList
} from '@/api/nodemanager';
import chosseTypeAlert from './components/commonDialog';
import formTypeListVue from '../form/form-type-list.vue';
export default {
  name: 'gdnum',
  components: {
    chosseTypeAlert,
    exporTip
  },
  data() {
    return {
      changeExport: false,
      loading: false,
      mocktype: this.APIMockGDType(),
      mockCity: this.APICityGDType(),
      oderTypeDta: [],
      oderCityData: [],
      dataRange: '',
      formAdressId: '',
      formTypeId: '',
      created: false,
      list: [],
      listQuery: {
        dataRange: [], // 日期
        sheetType: '', // 工单类型
        CustLocation: '' // 客户所在地
      },
      rules: {
        dataRange: [
          { required: true, message: '日期为必填项', trigger: 'blur' }
        ]
      },
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.minDate = minDate;
          this.maxDate = maxDate;
        },
        disabledDate: time => {
          const curDate = new Date().getTime();
          const two = 365 * 2 * 24 * 3600 * 1000;
          const twoyear = curDate - two;
          const three = 92 * 24 * 3600 * 1000;
          if (this.minDate) {
            return (
              time.getTime() > Date.now() ||
              time.getTime() < twoyear ||
              time > new Date(this.minDate.getTime() + three) ||
              time < new Date(this.minDate.getTime() - three)
            );
          }
          return time.getTime() > Date.now() || time.getTime() < twoyear;
        }
      },
      col: [
        {
          index: 'reportDate',
          name: '日期',
          width: 118,
          fixed: true,
          resizable: true
        },
        {
          index: 'todayAddNum',
          name: '工单数量',
          smallName: '当日新增',
          width: 95,
          resizable: true
        },
        {
          index: 'todayAddClosedNum',
          name: '结案工单',
          smallName: '当日新增',
          width: 95,
          resizable: true
        },
        {
          index: 'todayResetNum',
          name: '重启工单',
          smallName: '当日新增',
          width: 95,
          resizable: true
        },
        {
          index: 'todayAddClosedTime',
          name: '平均完成时长(小时)',
          smallName: '当日新增',
          width: 160,
          resizable: true
        },
        {
          index: 'historyClosedNum',
          name: '结案工单',
          smallName: '历史工单',
          width: 95,
          resizable: true
        },
        {
          index: 'historyResetNum',
          name: '重启工单',
          smallName: '历史工单',
          width: 95,
          resizable: true
        },
        {
          index: 'historyColsedTime',
          name: '平均完成时长(小时)',
          smallName: '历史工单',
          width: 160,
          resizable: true
        },
        {
          index: 'closedAll',
          name: '结案工单',
          smallName: '所有工单',
          width: 95,
          resizable: true
        },
        {
          index: 'resetAll',
          name: '重启工单',
          smallName: '所有工单',
          width: 95,
          resizable: true
        },
        {
          index: 'closedTimeAll',
          name: '平均完成时长(小时)',
          smallName: '所有工单',
          width: 160,
          resizable: true
        },

        { index: 'closeRate12', name: '12H结案率', resizable: true },
        { index: 'closeRate24', name: '24H结案率', resizable: true },
        { index: 'closeRate48', name: '48H结案率', resizable: true },
        { index: 'closeRate72', name: '72H结案率', resizable: true },
        {
          index: 'todayCloseAssigin',
          name: '邀评工单',
          smallName: '当日新增',
          width: 95,
          resizable: true
        },
        {
          index: 'historyCloseAssign',
          name: '邀评工单',
          smallName: '历史工单',
          width: 95,
          resizable: true
        },
        {
          index: 'closeAssignAll',
          name: '邀评工单',
          smallName: '所有工单',
          width: 95,
          resizable: true
        },

        {
          index: 'todayAssign',
          name: '已评工单',
          smallName: '当日新增',
          width: 95,
          resizable: true
        },
        {
          index: 'historyAssign',
          name: '已评工单',
          smallName: '历史工单',
          width: 95,
          resizable: true
        },
        {
          index: 'assignAll',
          name: '已评工单',
          smallName: '所有工单',
          width: 95,
          resizable: true
        },
        {
          index: 'todayAssignRate',
          name: '参评率',
          smallName: '当日新增',
          width: 85,
          resizable: true
        },
        {
          index: 'historyAssignRate',
          name: '参评率',
          smallName: '历史工单',
          width: 85,
          resizable: true
        },
        {
          index: 'allAssignRate',
          name: '参评率',
          smallName: '所有工单',
          width: 85,
          resizable: true
        },
        {
          index: 'vdoing1One',
          name: '维度一1星工单数',
          width: 145,
          resizable: true
        },
        {
          index: 'vdoing1Two',
          name: '维度一2星工单数',
          width: 145,
          resizable: true
        },
        {
          index: 'vdoing1Three',
          name: '维度一3星工单数',
          width: 145,
          resizable: true
        },
        {
          index: 'vdoing1Four',
          name: '维度一4星工单数',
          width: 145,
          resizable: true
        },
        {
          index: 'vdoing1Five',
          name: '维度一5星工单数',
          width: 145,
          resizable: true
        },
        {
          index: 'vdoing2One',
          name: '维度二1星工单数',
          width: 145,
          resizable: true
        },
        {
          index: 'vdoing2Two',
          name: '维度二2星工单数',
          width: 145,
          resizable: true
        },
        {
          index: 'vdoing2Three',
          name: '维度二3星工单数',
          width: 145,
          resizable: true
        },
        {
          index: 'vdoing2Four',
          name: '维度二4星工单数',
          width: 145,
          resizable: true
        },
        {
          index: 'vdoing2Five',
          name: '维度二5星工单数',
          width: 145,
          resizable: true
        },
        {
          index: 'assignNum',
          name: '分配工单数',
          width: 145,
          resizable: true
        },
        {
          index: 'transferNum',
          name: '转移工单数',
          width: 145,
          resizable: true
        }
      ]
    };
  },
  created() {
    this.getNowTimeDate();
    this.APIGetCustomerSourceList();
    this.APIGetWorkorderTypeList();
    this.created = true;
  },
  activated() {
    // if (!this.created) {
    //   this.APISelectWorkorderList();
    // }
    this.created = false;
  },
  methods: {
    handleChangeExport(info) {
      const that = this;
      this.changeExport = false;
      if (info === 'go') {
        that.$router.push({
          path: '/other/index/'
        });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    exportData() {
      const that = this;
      if (!that.list || !that.list.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      const params = {
        endTime: new Date(that.listQuery.dataRange[1]).Format('yy-MM-dd'),
        startTime: new Date(that.listQuery.dataRange[0]).Format('yy-MM-dd'),
        formTypeId: that.listQuery.sheetType
          ? parseInt(that.listQuery.sheetType)
          : null,
        customerSource: that.listQuery.CustLocation
          ? that.listQuery.CustLocation
          : null
      };
      exportData(params).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.changeExport = true;
      });
    },
    oldSearch() {
      this.getNowTimeDate();
      this.listQuery.sheetType = '';
      this.listQuery.CustLocation = '';
      this.APISelectWorkorderList();
    },
    handerSearch(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.APISelectWorkorderList();
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 时间格式化
    getFormatDate: function(row, column, cellValue, index) {
      return new Date(cellValue + 8 * 3600 * 1000)
        .toJSON()
        .substr(0, 19)
        .replace('T', ' ');
    },
    // 当前时间
    getNowTimeDate() {
      const time = new Date(8 * 3600 * 1000 + +new Date())
        .toJSON()
        .substr(0, 10);

      this.listQuery.dataRange = [time, time];
    },
    dateTimeFocus() {
      this.minDate = undefined;
      // 条件时间查询
      const that = this;
      that.$nextTick(function() {
        document
          .getElementsByClassName('el-picker-panel')[0]
          .addEventListener('click', function() {
            // that.$refs.messageDrop.show();
          });
      });
    },
    queryTime() {
      // this.$refs.messageDrop.show();
    },
    getSummaries() {
      const sums = [];
      this.col.forEach((column, index) => {
        if (index === 0) {
          sums[column.index] = '合计';
          return;
        }
        const values = this.list.map(item => Number(item[column.index]));
        if (!values.every(value => isNaN(value))) {
          sums[column.index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          // sums[index] += ' 元';
        } else {
          sums[column.index] = '';
        }
      });

      this.list.push(sums);
    },

    changeToNumber(data) {
      const newdata = {};
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        for (const key in element) {
          if (element.hasOwnProperty(key)) {
            const objelement = element[key];
            newdata;
          }
        }
        newdata[element];
      }
    },
    infodata() {
      return [
        {
          title: '工单数量（当日新增）',
          info: '工单创建时间在当日内，计算这些工单的总量'
        },
        {
          title: '结案工单数（当日新增）',
          info:
            '工单创建时间在当日内，工单完结时间也在当日内，计算这些工单的总量。 注：完结后重启，算完结1，重启1；同一条多次完结算1'
        },
        {
          title: '重启工单数（当日新增）',
          info:
            '工单创建时间在当日内，工单重启时间也在当日内，计算这些工单的总量'
        },
        {
          title: '平均完成时长（当日新增）',
          info:
            '工单创建时间在当日内，工单完结时间也在当日内，计算这些工单从创建时间到完结时间的平均用时。（完结后重启的工单也算完结）'
        },
        {
          title: '结案工单（历史工单）',
          info:
            '工单创建时间早于当日，工单完结时间在当日内，计算这些工单的总量   注：完结后重启，算完结1，重启1；同一条多次完结算1'
        },
        {
          title: '重启工单（历史工单）',
          info: '工单创建时间早于当日，工单重启时间在当日内，计算这些工单的总量'
        },
        {
          title: '平均完成时长（历史工单）',
          info:
            '工单创建时间早于当日，工单完结时间在当日内，计算这些工单 从创建时间到完结时间的平均用时。（完结后重启的工单也算完结）'
        },
        {
          title: '结案工单（所有工单）',
          info:
            '工单完结时间在当日内，不论工单创建时间是哪天，计算这些工单的总量结案工单数（所有工单）=结案工单数（当日新增）+结案工单数（历史工单）'
        },
        {
          title: '重启工单（所有工单）',
          info:
            '工单重启时间在当日内，不论工单创建时间是哪天，计算这些工单的总量重启工单数（所有工单）=重启工单数（当日新增）+重启工单数（历史工单）'
        },
        {
          title: '平均完成时长（所有工单）',
          info:
            '工单完结时间在当日内，不论工单创建时间是哪天，计算这些工单创建时间到完结时间的平均用时（完结后重启的工单也算完结）'
        },
        {
          title: '12H结案率',
          info:
            '当日已完结的所有工单中，完结时间<=12小时的工单数/结案工单数（所有工单）'
        },
        {
          title: '24H结案率',
          info:
            '当日已完结的所有工单中，完结时间<=24小时的工单数/结案工单数（所有工单）'
        },
        {
          title: '48H结案率',
          info:
            '当日已完结的所有工单中，完结时间<=48小时的工单数/结案工单数（所有工单）'
        },
        {
          title: '72H结案率',
          info:
            '当日已完结的所有工单中，完结时间<=72小时的工单数/结案工单数（所有工单）'
        },

        {
          title: '邀评工单数（当日新增）',
          info:
            '工单创建时间在当日内，工单完结时间在当日内，邀请评价在当日内，计算这些工单总量'
        },
        {
          title: '邀评工单数（历史工单）',
          info:
            '工单创建时间早于当日，工单完结时间在当日内，邀请评价在当日内，计算这些工单总量'
        },
        {
          title: '邀评工单数（所有工单）',
          info:
            '工单完结时间在当日内，邀请评价在当日内，不论工单创建时间是哪天，计算这些工单总量'
        },
        {
          title: '已评价工单数（当日新增）',
          info: '工单创建时间在当日内，客户评价工单在当日内，计算这些工单总量'
        },
        {
          title: '已评价工单数（历史工单）',
          info: '工单创建时间早于当日，客户评价工单在当日内，计算这些工单总量'
        },
        {
          title: '已评价工单数（所有工单）',
          info: '客户评价工单在当日内，不论工单创建时间是哪天，计算这些工单总量'
        },
        {
          title: '参评率（当日新增）',
          info: '已评价工单数（当日新增）/邀评工单数（当日新增）'
        },
        {
          title: '参评率（历史工单）',
          info: '已评价工单数（历史工单）/邀评工单数（历史工单）'
        },
        {
          title: '参评率（所有工单）',
          info: '已评价工单数（所有工单）/邀评工单数（所有工单）'
        },

        {
          title: '维度一1星工单数',
          info: '已评价工单数（所有工单）中维度一为一星，计算这些工单总量'
        },
        {
          title: '维度一2星工单数',
          info: '已评价工单数（所有工单）中维度一为二星，计算这些工单总量'
        },
        {
          title: '维度一3星工单数',
          info: '已评价工单数（所有工单）中维度一为三星，计算这些工单总量'
        },
        {
          title: '维度一4星工单数',
          info: '已评价工单数（所有工单）中维度一为四星，计算这些工单总量'
        },
        {
          title: '维度一5星工单数',
          info: '已评价工单数（所有工单）中维度一为五星，计算这些工单总量'
        },
        {
          title: '维度二1星工单数',
          info: '已评价工单数（所有工单）中维度二为一星，计算这些工单总量'
        },
        {
          title: '维度二2星工单数',
          info: '已评价工单数（所有工单）中维度二为二星，计算这些工单总量'
        },
        {
          title: '维度二3星工单数',
          info: '已评价工单数（所有工单）中维度二为三星，计算这些工单总量'
        },
        {
          title: '维度二4星工单数',
          info: '已评价工单数（所有工单）中维度二为四星，计算这些工单总量'
        },
        {
          title: '维度二5星工单数',
          info: '已评价工单数（所有工单）中维度二为五星，计算这些工单总量'
        },
        {
          title: '分配工单数',
          info: '操作工单分配的时间在当日内，计算这些工单总量'
        },
        {
          title: '转移工单数',
          info: '操作工单转移的时间在当日内，计算这些工单总量'
        }
      ];
    },

    /**
     * 工单量报表API
     */

    APISelectWorkorderList() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      selectWorkorderList({
        customerSource: this.listQuery.CustLocation,
        endTime: this.listQuery.dataRange[1],
        formTypeId: this.listQuery.sheetType,
        startTime: this.listQuery.dataRange[0]
      }).then(res => {
        this.loading.close();
        let dataListList = res.data;
        if (
          !dataListList &&
          typeof dataListList !== 'undefined' &&
          dataListList != 0
        ) {
          this.$XyyMessage.success('暂时无数据');
          this.list = [];
          return;
        }
        if (res.code === 1) {
          if (res.data.length === 0) {
            this.$XyyMessage.success('暂时无数据');
          }
          this.list = res.data;
        } else {
          this.$XyyMessage.error(res.msg);
        }
        // this.getSummaries();
      });
    },
    APIGetCustomerSourceList() {
      getCustomerSourceList().then(res => {
        if (res.code === 1) {
          this.oderCityData = res.data;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    APIGetWorkorderTypeList() {
      getWorkorderTypeList().then(res => {
        if (res.code === 1) {
          this.oderTypeDta = res.data;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    APIMockGDType() {
      return mockGDType();
    },
    APICityGDType() {
      return mockGDUser();
    }
  }
};
</script>
<style scoped lang="scss">
/deep/.el-date-editor {
  /*width: 490px;*/
}
/deep/.page-header {
  padding-bottom: 0 !important;
}
</style>
<style>
.search-form {
  border-bottom: 1px dashed #e4e4eb;
  overflow: hidden;
}
.herader-title {
  font-size: 14px;
  color: #393943;
  padding-bottom: 20px;
  border-bottom: 1px dashed #e4e4eb;
  margin-bottom: 20px;
}
.gdnumclass .page-header {
}

.gdnumclass .el-table th > .cell {
  position: relative;
  word-wrap: normal;
  text-overflow: ellipsis;
  vertical-align: middle;
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  /* display: flex;
    flex-direction: column;
    align-items: flex-start; */
  padding-top: 10px;
  padding-bottom: 10px;
}

.gdnumclass .el-table th > .cell p {
  display: block;
  margin-block-start: 0px;
  margin-block-end: 0px;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  text-align: right;
}

.gdnumclass .el-table th > .cell small {
  font-size: 12px;
  color: #909399;
  display: block;
  margin-block-start: 0px;
  margin-block-end: 0px;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  text-align: right;
}

.gdnumclass .el-table tr td .cell {
  height: 40px;
  line-height: 40px;
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.gdnum_input_group .el-form-item__error {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  margin-left: 15px;
}
</style>

<style scoped lang="scss">
/deep/.el-table {
  /deep/.el-table__fixed {
    height: auto !important;
    bottom: 16px;
    margin-bottom: 0 !important;
  }
  /deep/.el-table__fixed-body-wrapper {
    top: 67px !important;
  }
}
/deep/.el-table__body tr td {
  border-bottom: 1px solid #ebeef5;
  background: #fff;
}
.el-menu-item.is-active {
  color: #3b95a8 !important;
}
/deep/.el-input__inner {
  height: 36px !important;
  line-height: 36px !important;
}
/deep/.el-button {
  height: 36px;
}
.el-menu.el-menu--horizontal {
  border-bottom: 1px dashed #e4e4eb;
  padding-bottom: 20px;
}
.search-box {
  width: 344px;
  float: right;
  .el-input {
    width: calc(100% - 76px);
    /deep/ .el-input__inner {
      height: 36px;
      line-height: 36px;
    }
  }
  button {
    width: 68px;
    height: 36px;
    float: right;
  }
}
.status-tip {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin: 0 2px 2px 0;
  &.open {
    background: #67c23a;
  }
  &.close {
    background: #ff3024;
  }
}

.toprow {
  display: flex;
  flex-direction: row;
}

.timediv {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 14px;
  margin-right: 30px;
}

/*.innerSelTime {*/
/*margin-left: 15px;*/
/*}*/

.gdnumheader {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.gdnum_input_mainheader {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.gdnum_input_group {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.gdnum_button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}

.searchTwoButton {
  width: 100%;
}
</style>

