<template>
  <div class="drug-container">
    <div class="drug-from">
      <div class="drug-name">
        <div class="drug-left">药品名称</div>
        <div class="drug-right">
          <el-input
            v-model="drugName"
            type="text"
            size="medium"
            placeholder="请填写药品名称"
            maxlength="50"
            show-word-limit
          ></el-input>
        </div>
      </div>
      <div class="drug-description">
        <div class="drug-left">描述</div>
        <div class="drug-right">
          <!--:autosize="{ minRows: 2, maxRows: 4}"  -->
          <el-input
            v-model="description"
            type="textarea"
            size="medium"
            placeholder="请输入文本"
            autosize
            maxlength="200"
            show-word-limit
          ></el-input>
        </div>
      </div>
      <div class="drug-number">
        <div class="drug-left">批号</div>
        <div class="drug-right">
          <div v-for="(item,index) in batchInfo" :key="index" class="number-row">
            <el-input
              v-model="item.batchNumber"
              type="text"
              size="medium"
              placeholder="请填写批号"
              maxlength="50"
              show-word-limit
            ></el-input>
            <el-button size="medium" class="add" @click="addNumber">添加</el-button>
            <el-button size="medium" class="delete" @click="deleteNumber(index)">删除</el-button>
          </div>
        </div>
      </div>
      <div class="drug-status">
        <el-button size="medium" class="drug-preview" @click="previewNumber">预览</el-button>
        <el-button size="medium" class="drug-cancel" @click="cancelNumber">取消</el-button>
        <el-button size="medium" class="drug-release" type="primary" @click="releaseNumber">发布</el-button>
      </div>
    </div>
    <!-- 药品召回信息登记 -->
    <el-dialog :visible.sync="dialogVisible" title="药品召回信息登记">
      <previewinfor :goods-data="drugParam"></previewinfor>
    </el-dialog>
  </div>
</template>

<script>
import { postDrugList } from "@/api/shop/index";
import previewinfor from "./previewinfor.vue";
export default {
  name: "",
  components: { previewinfor },
  filters: {},
  data() {
    return {
      drugName: "",
      description: "",
      batchInfo: [{ batchNumber: "" }],
      number: [],
      drugParam: {},
      prevInfor: {},
      dialogVisible: false,
      releaseFlag: true,
      drugNumber: false
    };
  },
  computed: {},
  watch: {},
  mounted() {},
  created() {},
  methods: {
    addNumber() {
      this.batchInfo.push({ batchNumber: "" });
    },
    deleteNumber(index) {
      this.batchInfo.splice(index, 1);
    },
    previewNumber() {
      this.getDatalist();
      this.dialogVisible = true;
    },
    cancelNumber() {
      this.gotoNewDrugRecall();
    },
    releaseNumber() {
      if (this.releaseFlag) {
        this.releaseFlag = false;
        this.getDatalist();
        if (this.drugParam.drugName == "") {
          this.$message("药品名称不能为空");
        } else if (this.drugParam.description == "") {
          this.$message("描述不能为空");
        } else if (this.drugNumber) {
          this.$message("批号不能为空");
        } else {
          postDrugList(this.drugParam)
            .then(response => {
              if (response.code === 1) {
                this.$message(response.msg);
                setTimeout(e => {
                  this.gotoNewDrugRecall();
                }, 500);
              } else {
                this.$message(response.msg);
              }
            })
            .catch(function(error) {
              this.$message("发布失败");
              this.$router.push({
                path: "/recall/newdrugRecall"
              });
            });
        }
        setTimeout(e => {
          this.releaseFlag = true;
        }, 1000);
      } else {
      }
    },
    gotoNewDrugRecall() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({
        path: "/recall/drugRecall"
      });
    },
    getDatalist() {
      this.drugParam = {};
      this.drugParam.drugName = this.drugName;
      this.drugParam.description = this.description;
      this.drugParam.batchInfos = this.batchInfo;
      this.drugParam.batchInfos.some(item => {
        if(item.batchNumber==''){
          this.drugNumber = true;
          return true
        }else{
          this.drugNumber = false;
        }
      });
    }
  }
};
</script>

<style lang="scss" >
.drug-container {
  .drug-from {
    width: 800px;
    margin: 20px auto;
    & > div {
      overflow: hidden;
    }
    .drug-name {
    }
    .drug-description {
    }
    .drug-number {
      .number-row {
        &:nth-of-type(1) {
          .add {
            display: inline-block;
          }
          .delete {
            display: none;
          }
        }
        margin-bottom: 20px;
        .add {
          margin-left: 10px;
          display: none;
        }
      }
    }
    .drug-left {
      float: left;
      height: 36px;
      line-height: 36px;
      margin-right: 10px;
      width: 80px;
    }
    .drug-right {
      //   width: 500px;
      float: left;
      margin-bottom: 20px;
      .el-input {
        width: 500px;
      }
      .el-textarea {
        width: 500px;
      }
    }
    .drug-status {
      button {
        margin: 20px 40px;
      }
    }
  }
  .el-dialog {
    margin-top: 15vh;
    width: 350px;
    max-height: 700px;
    overflow: auto;
    .el-dialog__body {
      padding-top: 0;
    }
  }
}
</style>
