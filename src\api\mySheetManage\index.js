import request from "@/utils/request";
import { formData } from "@/utils/index";

// ------------------------表单应用接口--------------------------
/**
 *领取工单
 * @param {params}查询参数
 */
// eslint-disable-next-line space-before-function-paren
export function receivingWorkorder(params) {
  return request({
    url: "workorder/receivingWorkorder",
    method: "get",
    params: params
  });
}

/**
 *导出工单
 * @param {params}查询参数
 */
// eslint-disable-next-line space-before-function-paren
export function exportExcel(params) {
  return request({
    url: "workorder/export/excel",
    method: "get",
    params: params
  });
}

/**
 *获取工单类型列表
 * @param {Object}查询参数
 */
// eslint-disable-next-line space-before-function-paren
export function getSheetTypeList(params) {
  return request({
    url: "form/type/listFormType",
    method: "get",
    params: params
  });
}

/**
 *根据工单类型获取工单列表
 * @param {Object}  id status 0:禁用，1：启用
 */
export function seleSheetList(params) {
  return request({
    url: "form/base/listForm",
    method: "get",
    params: params
  });
}

/**
 *根据表单id查询发起节点模板字段及权限
 * @param { formId: 1 }
 */
export function getNodeList(params) {
  return request({
    url: "form/node/auth/listFieldAndAuthByFormId",
    method: "get",
    params: params
  });
}

/**
 *获取工单记录信息
 * @param {*}
 */
export function getWorkRecordInfo(params) {
  return request({
    url: "workorder/getWorkorderRecordInfo",
    method: "get",
    params: params
  });
}

/**
 *获取工单详情
 * @param {*}
 */
export function getWorkorderInfoAndReplyInfo(params) {
  return request({
    url: "workorder/query/getWorkorderInfo",
    method: "get",
    params: params
  });
}

/**
 *保存工单
 * @param
 */
export function saveWorkorder(param) {
  return request({
    url: "workorder/operation/saveWorkorder",
    method: "post",
    data: param
  });
}

/**
 * 查询工单全部状态
 * @param
 */
export function listAllWorkorder(param) {
  return request({
    url: "workorder/query/listAllWorkorder",
    method: "get",
    params: param
  });
}

/**
 * 查询工单全部状态
 * @param
 */
export function listAllStateWorkorder(param) {
  return request({
    // url: 'workorder/listAllStateWorkorder',
    url: "workorder/query/listAllWorkorder",
    method: "get",
    params: param
  });
}

/**
 * 历史工单查询条件
 */
export function imWorkOrderSelectDefaultField() {
  return request({
    url: "workorder/query/imWorkOrderSelectDefaultField",
    method: "get",
  });
}

/**
 * 查询工单待领取状态
 * @param
 */
// eslint-disable-next-line space-before-function-paren
export function listUnclaimedWorkorder(param) {
  return request({
    url: "workorder/query/listUnclaimedWorkorder",
    method: "get",
    params: param
  });
}

/**
 * 查询工单我处理中/即将超时/已超时
 * @param
 */
// eslint-disable-next-line space-before-function-paren
export function listWorkorderByState(param) {
  return request({
    url: "workorder/query/listMyProcessing",
    method: "get",
    params: param
  });
}
/**
 * 查询工单我发起的
 * @param
 */
// eslint-disable-next-line space-before-function-paren
export function listIInitiated(param) {
  return request({
    url: "workorder/query/listIInitiated",
    method: "get",
    params: param
  });
}
/**
 * 查询工单我已处理
 * @param
 */
// eslint-disable-next-line space-before-function-paren
export function listMyProcessed(param) {
  return request({
    url: "workorder/query/listMyProcessed",
    method: "get",
    params: param
  });
}
/**
 * 查询工单抄送给我
 * @param
 */
// eslint-disable-next-line space-before-function-paren
export function listMyCC(param) {
  return request({
    url: "workorder/query/listMyCC",
    method: "get",
    params: param
  });
}

/**
 * 查询工单我关注的
 * @param {*} param 
 */
export function listMyFocusOn(param){
  return request({
    url:'workorder/query/listMyFocusOn',
    method:'get',
    params:param
  })
}

/**
 *工单回复
 * @param {object}
 */
export function saveWorkorderReply(param) {
  return request({
    url: "workorder/reply/saveWorkorderReply",
    method: "post",
    data: param
  });
}

/**
 *根据工单id查询当前流程节点字段列表
 * @param {object}
 */
export function listReplyFieldByWorkorderId(param) {
  return request({
    url: "form/node/auth/listReplyFieldByWorkorderId",
    method: "get",
    params: param
  });
}

/**
 *查询问题分类模板字段信息
 * @param {object}
 */
export function listTemplateFieldAuthByCode(
  templateCode,
  workorderId = "",
  formTypeId
) {
  return request({
    url: "templateFieldAuth/listTemplateFieldAuthByCode",
    method: "get",
    params: { templateCode, workorderId, formTypeId }
  });
}

/**
 *工单列表统计
 * @param {object}
 */
export function workorderListCount(param) {
  return request({
    url: "workorder/query/workorderListCount",
    method: "get",
    params: param
  });
}

/**
 *追加说明
 * @param
 */
export function saveWorkorderAppendNote(param) {
  return request({
    url: "appendNote/saveWorkorderAppendNote",
    method: "post",
    data: formData(param)
  });
}

/**
 *重启工单
 * @param {object}
 */
export function restartWorkorder(object) {
  return request({
    url: "workorder/restartWorkorder",
    method: "post",
    data: formData(object)
  });
}

/**
 *退回工单
 * @param {object}
 */
export function saveSendBackWorkOrder(object) {
  return request({
    url: "workorder/reply/saveSendBackWorkOrder",
    method: "post",
    data: formData(object)
  });
}

/**
 * 2020.4.15,rl,新版接口
 * 退回工单
 * @param {object}
 */
export function saveSendBackWorkOrderNew(object) {
  return request({
    url: "workorder/reply/saveSendBackWorkOrderNew",
    method: "post",
    data: formData(object)
  });
}

/**
 *作废工单
 * @param
 */
export function updateWorkOrderToVoid(param) {
  return request({
    url: "workorder/updateWorkOrderToVoid",
    method: "patch",
    data: formData(param)
  });
}

/**
 *修改工单
 * @param
 */
export function updateWorkorder(param) {
  return request({
    url: "workorder/operation/updateWorkorder",
    method: "patch",
    data: param
  });
}

/**
 *完结工单
 * @param
 */
export function updateWorkorderEnd(param) {
  return request({
    url: "workorder/updateWorkorderEnd",
    method: "patch",
    data: formData(param)
  });
}

/**
 * 获取工单流转记录
 * @param {工单id} id
 */
export function getWorkorderRecords(params) {
  return request({
    url: "/workorder/query/listWorkorderRecordInfo",
    method: "get",
    params: params
  });
}

/**
 * 删除当前追加说明
 * @param {追加说明id} id
 */
export function delAppendRecord(id) {
  return request({
    url: "/appendNote",
    method: "delete",
    data: formData({ id: id })
  });
}
/**
 * 获取用户信息
 */
export function getUserInfo() {
  return request({
    url: "/system/index",
    method: "get"
  });
}

/**
 * 草稿列表查询
 * @param {pageNum} pageNum
 * @param {pageSize} pageSize
 */
export function getDraftList(params) {
  return request({
    url: "/workorder/draft",
    method: "get",
    params: params
  });
}

/**
 * 根据草稿ID查询草稿
 * @param {workorderDraftId} id
 */
export function getWorkorderDraftById(workorderDraftId, formId, templateId) {
  return request({
    url: "/workorder/draft/getWorkorderDraftById",
    method: "get",
    params: { workorderDraftId, formId, templateId }
  });
}

/**
 *草稿列表新增
 * @param {object}
 */
export function addDraftItem(object) {
  return request({
    url: "/workorder/draft",
    method: "post",
    data: formData(object)
  });
}

/**
 *删除草稿
 * @param {*} 草稿id
 */
export function delDraft(workorderDraftId) {
  return request({
    url: "/workorder/draft",
    method: "delete",
    data: formData({ workorderDraftId })
  });
}

/**
 *更新草稿
 * @param {*} 草稿id
 */
export function updateDraft(params) {
  return request({
    url: "/workorder/draft",
    method: "put",
    data: formData(params)
  });
}

/**
 *记录流程第一次处理的时间
 * @param {*} 工单ID
 */
export function saveFlowStartTime(params) {
  return request({
    url: "/auditFlow/saveFlowStartTime",
    method: "post",
    data: formData(params)
  });
}

/**
 * 新建工单重复校验
 * @param {orderNumber, problemClassification}订单编号/问题分类
 */
export function getWorkorderRepeateCheck(
  orderNumber,
  problemClassification,
  formTypeId,
  workorderNo = ''
) {
  return request({
    url: "/workorder/getWorkorderRepeateCheck",
    method: "get",
    params: { orderNumber, problemClassification, formTypeId, workorderNo }
  });
}

/**
 * 获取指定工单集合最近处理信息
 * @param {workorderId} workorderId
 */
export function getLatestFlowInfo(object) {
  return request({
    url: "/workorder/query/listLatestFlowInfoByWorkorderIds",
    method: "post",
    data: object
  });
}
/**
 * 工单可分配组查询
 * @param {workorderId} workorderId
 */
export function getNodeGroupInfo(workorderId) {
  return request({
    url: "/workorder/getCurrentNodeGroupInfo",
    method: "get",
    params: { workorderId }
  });
}

/**
 * 根据组ID获取用户信息(工单分配，转移，重启专用)
 * @param {userGroupId,type:工单分配，工单转移，工单重启 = 2,其他 = 1 }
 */
export function getUserList(userGroupId) {
  return request({
    url: "/user/group/listUserByUserGroupId",
    method: "get",
    params: { userGroupId, type: 2 }
  });
}

/**
 *工单分配
 * @param {object}
 */
export function assignmentUorkorder(object) {
  return request({
    url: "/workorder/assignmentUorkorder",
    method: "post",
    data: formData(object)
  });
}
/**
 *工单页面的导出按钮
 * @param {object}
 */
export function startExport(object) {
  return request({
    url: "/workOrder/export/startExport",
    method: "post",
    data: formData(object)
  });
}

/**
 * 工单转移查询节点列表
 * @param {formId} formId
 */
export function getCurrtNodeList(formId, workorderId) {
  return request({
    url: "/form/node/listNodesByFormIdExcludeStartAndCcNodes",
    method: "get",
    params: { formId, workorderId }
  });
}

/**
 * 查询节点用户组列表
 * @param {nodeId} nodeId
 */
export function listUserGroupsByNodeId(nodeId, workorderId) {
  return request({
    url: "/form/node/listUserGroupsByNodeId",
    method: "get",
    params: { nodeId, workorderId }
  });
}

/**
 *工单转移
 * @param {object}
 */
export function workorderTransfer(object) {
  return request({
    url: "/workorder/transfer",
    method: "post",
    data: formData(object)
  });
}

/**
 * 催单显示信息
 * @param {lowId,workorderId} lowId,workorderId
 */
export function getCurrentUrgeCon(flowId, workorderId) {
  return request({
    url: "/urge/getCurrentNodeInfoByWorkorderId",
    method: "get",
    params: { flowId, workorderId }
  });
}

/**
 *催单
 * @param {object}
 */
export function urgeWorkeApi(object) {
  return request({
    url: "/urge/urgeWorkorder",
    method: "post",
    data: formData(object)
  });
}

/**
 * 重复工单校验参数设置
 * @param {lowId,workorderId} lowId,workorderId
 */
export function getRepeateCheckSetting() {
  return request({
    url: "/workorder/getRepeateCheckSetting",
    method: "get",
    params: {}
  });
}

/**
 *修改工单校验参数设置
 * @param {*}
 */
export function updateRepeateCheckSetting(params) {
  return request({
    url: "/workorder/updateRepeateCheckSetting",
    method: "put",
    data: formData(params)
  });
}

export function getWorkorderLocationCheck(params) {
  return request({
    url: "/workorder/getCustomerLocationCheck",
    method: "get",
    params
  });
}

/**
 * 再次发起
 *
 */
export function startAgai(object) {
  return request({
    url: "/workorder/operation/startAgain",
    method: "post",
    data: object
  });
}

export function getRecordTimeInfo(params) {
  return request({
    url: "/workorder/query/getFlowRecordDetails",
    method: "get",
    params
  });
}

/**
 *设置工单领取数
 * @param {number}
 */
export function updateDrawStatus(params) {
  return request({
    url: "/form/drawNum/updateDrawStatus",
    method: "get",
    params
  });
}

/**
 *获取工单领取数
 * @param {number}
 */
export function getFormDrawNumList(params) {
  return request({
    url: "/form/drawNum/getFormDrawNumList",
    method: "get",
    params
  });
}

/**
 * 获取标签列表（字典，仅查询启用的）
 * @param {number}
 */
export function getTagList(params) {
  return request({
    url: "/sys/tags/get",
    method: "get",
    params: {
      stopState: true
    }
  });
}

/**
 * 切换标签
 * @param tagId {number}
 * @param workorderId {string}
 */
export function putSwitchTag(tagId, workorderId) {
  return request({
    url: "/sys/tags/switch",
    method: "put",
    data: formData({ tagId, workorderId })
  });
}

/**
 *获取问题分类树
 */
export function getTypeformat(params) {
  return request({
    url: "/sys/dic/get/typeformat",
    method: "get",
    params
  });
}

/**
 *获取客户所在地
 */
export function getCustomerSourceList(params) {
  return request({
    url: "/workorderReport/getCustomerSourceList",
    method: "get",
    params
  });
}

/**
 *工单批量转移查询
 */
export function listAllWorkorderByBatch(params) {
  return request({
    url: "/workorder/query/listAllWorkorderByBatch",
    method: "get",
    params
  });
}

/*
 * 清除工单上的标签
 * @param workorderId {string}
 */
export function deleteTag(workorderId) {
  return request({
    url: "/sys/tags/delete/relation",
    method: "delete",
    data: formData({ workorderId })
  });
}

/**
 *批量工单转移
 */
export function setBatchTransfer(param) {
  return request({
    url: "/workorder/batchTransfer",
    method: "post",
    data: param
  });
}
/**
 * 查询指定工单标签
 * @param workorderId {string}
 */
export function getOrderDetailTag(workorderId) {
  return request({
    url: "/sys/tags/get/by/workorderId",
    method: "get",
    params: { workorderId }
  });
}
/**
 *通过表单id获取节点列表
 * @param {formId} 表单id
 */
export function getListNodesByFormId(formId) {
  return request({
    url: "/form/node/listNodesByFormIdAndHandleType",
    method: "get",
    params: { formId }
  });
}
/**
 *通过节点ID和工单ID列表查询用户组列表
 * @param {nodeId，workorderIds} 表单id，多个工单ID用_分隔，如：111111_22222
 */
export function getUserGroupsByNodeIdAndWorkorderIds(params) {
  return request({
    url: "/form/node/getUserGroupsByNodeIdAndWorkorderIds",
    method: "get",
    params
  });
}

/**
 * 工单的关注/取关
 * @param status {string} 关注/取关状态，0-取关，1-关注
 * @param workorderId {string} 工单id
 */
export function focusOnWorkOrder(params) {
  return request({
    url: '/workorder/operation/focusOnWorkOrder',
    method: 'post',
    data: params
  })
}

/**
 * 获取指定工单集合下的节点集合
 * @param {workorderId} workorderId
 */
export function getNodeListByWorkOrderIdInfo(object) {
  return request({
    url: "/form/node/nodeListByWorkOrderId",
    method: "post",
    data: object
  });
}

/**
 * 获取渠道来源字典
 */
export function getListSourceChannel(){
  return request({
    url:'/workorder/query/listSourceChannel',
    method:'get'
  })
}

/**
 * 手机号查询店铺列表
 */
 export function getMerchantByAccountMobile(params){
  return request({
    url:'/workorder/getMerchantByAccountMobile',
    method:'get',
    params: params
  })
}

// 根据订单号查询商品集合
export function queryProductListByOrderNo(params){
  return request({
    url:'/order/getOrderInfo',
    method:'get',
    params: params
  })
}

// 通过id批量领取工单
export function receivingWorkorderByWorkOrderIds(object) {
  return request({
    url: "/workorder/receivingWorkorderByWorkOrderIds",
    method: "post",
    data: object
  });
}