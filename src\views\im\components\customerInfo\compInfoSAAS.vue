<template>
  <div class="comp-info-SAAS-component-container">
    <!-- 智慧脸APP -->
    <template v-if="prop.appIdAdapter === 21">
      <div class="search-customer-content">
        <template v-if="customerInfo.id">
          <comp-base-info-detail-saas :customer="customerInfo" :show-bind="false"></comp-base-info-detail-saas>
        </template>
        <template v-else>
          <div class="content-empty">
            <img src="../../../../assets/common/no_custome_info.png" alt="暂无客户信息" />
          </div>
        </template>
      </div>
    </template>
    <!-- 智慧脸PC -->
    <!-- 微信H5 -->
    <template v-else-if="prop.appIdAdapter === 20 || prop.appIdAdapter === 22">
      <!-- 无绑定的客户信息，展示搜索框 -->
      <template v-if="!customerInfoHas">
        <div class="search-customer-form">
          <!-- 搜索框 -->
          <el-autocomplete
            ref="searchRef"
            v-model.trim="shopNameModel"
            placeholder="检索客户进行绑定"
            popper-class="select-list-box"
            clearable
            :fetch-suggestions="querySearchAsync"
            @select="handleShopNameSelect"
          >
            <svg-icon
              slot="suffix"
              ref="searchIcon"
              class="svg-icon"
              icon-class="search"
              style="margin:0 5px;top:13px;position:relative;float:right"
            ></svg-icon>
          </el-autocomplete>
        </div>
        <div class="search-customer-content">
          <template v-if="customerInfoSearch.id">
            <comp-base-info-detail-saas :customer="customerInfoSearch" :show-bind="true"></comp-base-info-detail-saas>
          </template>
          <template v-else>
            <div class="content-empty">
              <img src="../../../../assets/common/no_custome_info.png" alt="暂无客户信息" />
            </div>
          </template>
        </div>
      </template>
      <!-- 有绑定的客户信息，展示客户信息 -->
      <template v-else>
        <div class="binded-customer-info">
          <comp-base-info-detail-saas :customer="customerInfo" :show-bind="false"></comp-base-info-detail-saas>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
import compBaseInfoDetailSaas from './compBaseInfoDetailSAAS';
import {
  getCustomeDetails,
  getListMerchantByName,
} from '@/api/im_view/customeInfo';
export default {
  name: 'compInfoSAAS',
  components: {
    compBaseInfoDetailSaas,
  },
  filters: {},
  props: {
    prop: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  data() {
    return {
      shopNameModel: '', //药店名
      customerInfoSearch: {}, //客户信息搜索结果

      customerInfoHas: false, //是否有绑定的客户信息,true:展示客户信息,false:展示搜索,
      customerInfoIsBind: false, //是否绑定了客户信息
      customerInfo: {}, //绑定的客户信息
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    /**
     * 重置组件
     */
    reset() {
      Object.assign(this.$data, this.$options.data()); // 重置data数据
    },

    /**
     * 初始化数据
     */
    init() {
      this.reset();
      this.queryCustomInfo();
    },

    /**
     * 查询客户信息
     */
    queryCustomInfo() {
      const loading = this.$loading({
        target: document.querySelector('.im-config'),
        lock: true,
        spinner: 'el-icon-loading',
        text: '加载中...',
        background: 'rgba(255,255,255, 0.8)',
      });

      getCustomeDetails({
        id: this.prop.khid,
        appId: this.prop.appId,
        businessPartCode: this.prop.businessPartCode,
        channelId: this.prop.channelId,
      })
        .then((resp) => {
          if (resp.code === 1) {
            if (resp.data && resp.data.id) {
              this.shopNameModel = '';
              this.customerInfoHas = true;
              this.customerInfoIsBind = true;
              this.customerInfo = resp.data;
              this.customerInfoSearch = {};
            } else {
              this.shopNameModel = '';
              this.customerInfoHas = false;
              this.customerInfoIsBind = false;
              this.customerInfo = {};
              this.customerInfoSearch = {};
            }
          } else {
            this.$XyyMessage.error(resp.msg);
          }
        })
        .finally(() => {
          loading.close();
        });
    },

    /**
     * 输入药店名搜索
     */
    querySearchAsync(queryString, cb) {
      if (!queryString) {
        cb([]);
        return false;
      }

      getListMerchantByName({
        name: this.shopNameModel,
        appId: this.prop.appId,
        businessPartCode: this.prop.businessPartCode,
        channelId: this.prop.channelId,
      }).then((resp) => {
        if (resp.code === 1) {
          let options = [];
          if (resp.data && resp.data.length) {
            options = resp.data.map((item) => {
              return { value: item['customerName'], id: item['merchantId'] };
            });
          }
          cb(options);
        } else {
          this.$XyyMessage.warning(resp.msg);
        }
      });
    },

    /**
     * 选择搜索项
     */
    handleShopNameSelect(item) {
      const loading = this.$loading({
        target: document.querySelector('.im-config'),
        lock: true,
        spinner: 'el-icon-loading',
        text: '加载中...',
        background: 'rgba(255,255,255, 0.8)',
      });

      getCustomeDetails({
        id: item.id,
        appId: this.prop.appId,
        businessPartCode: this.prop.businessPartCode,
        channelId: this.prop.channelId,
      })
        .then((resp) => {
          if (resp.code === 1) {
            if (resp.data && resp.data.id) {
              this.customerInfoSearch = resp.data;
            }
          } else {
            this.$XyyMessage.warning(resp.msg);
          }
        })
        .finally(() => {
          loading.close();
        });
    },

    /**
     * 药帮忙输入药店名搜索,绑定操作
     */
    emitCustomerBind(newCustomerInfo) {
      //初始化信息
      this.shopNameModel = '';
      this.customerInfoHas = true;
      this.customerInfoIsBind = true;
      this.customerInfo = Object.assign({}, this.customerInfoSearch);
      this.$XyyMessage.success('绑定成功');

      //更新左侧和中间组件信息
      if (newCustomerInfo) {
        this.oldCustomerId = this.prop.khid;
        newCustomerInfo.oldShopId = this.oldCustomerId;
        this.$emit('bindRefreshChatInfo', newCustomerInfo); // 更新绑定客户后的会话信息
      } else {
        this.$XyyMessage.warning('绑定信息缺失');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.comp-info-SAAS-component-container {
  width: 100%;
  height: 100%;

  .search-customer-form {
    width: 100%;
    padding: 20px;

    .el-autocomplete {
      width: 100%;
    }
  }

  .search-customer-content {
    width: 100%;
    height: calc(100% - 80px);
    padding: 20px;
    position: relative;

    .content-empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

      img {
        width: 200px;
      }
    }
  }

  .binded-customer-info {
    width: 100%;
    padding: 20px;
  }
}
</style>