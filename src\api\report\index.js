import request from '@/utils/request';
// import { formData } from "@/utils/index";

/* 客户来源所在地*/
export function getCustomerSourceList() {
  return request({
    url: '/workorderReport/getCustomerSourceList',
    method: 'get'
  });
}
/* 获取工单类型下拉列表*/
export function getWorkorderTypeList() {
  return request({
    url: '/workorderReport/getWorkorderTypeList',
    method: 'get'
  });
}

/* 查询人员相关指标报表*/
export function getGroupByStaff(params) {
  return request({
    // url: '/workorderReport/groupByStaff',
    url:
      '/workorderReport/sumForStaff?pageSize=' +
      params.pageSize +
      '&pageNum=' +
      params.page,
    method: 'post',
    data: params
  });
}

/* 查询相关明细报表*/
export function getGroupByDetail(params) {
  return request({
    url:
      '/workorderReport/groupByStaff?pageSize=' +
      params.pageSize +
      '&pageNum=' +
      params.page,
    method: 'post',
    data: params
  });
}
/* 工单节点相关指标报表*/
export function getGroupByPool(params) {
  return request({
    url:
      '/workorderReport/groupByPool?pageSize=' +
      params.pageSize +
      '&pageNum=' +
      params.page,
    method: 'post',
    data: params
  });
}
/* 节点相关报表-导出*/
export function nodeExportData(params) {
  return request({
    url: '/workOrder/export/getNodeExport',
    method: 'get',
    params: params
  });
}
/* 导出用户组人员报表*/
export function userExportData(params) {
  return request({
    url: '/workOrder/export/sumForStaffExport',
    method: 'get',
    params: params
  });
}
/* 人员相关明细报表-导出*/
export function userExportDetil(params) {
  return request({
    // url: '/workorderReport/groupByStaffExport',
    url: '/workOrder/export/groupByStaffExport',
    method: 'get',
    params: params
  });
}
/* 查人员列表*/
export function getUserGroupList(params) {
  return request({
    url: '/user/group/listUserByNameOrStaffNum',
    method: 'get',
    params: params
  });
}
/* 人员相关汇总报表 */
export function getUserSummary(params) {
  return request({
    url:
      '/workorderReport/sumForDay?pageSize=' +
      params.pageSize +
      '&pageNum=' +
      params.page,
    method: 'post',
    data: params
  });
}

/* 用户组相关汇总表-导出*/
export function Summaryexport(params) {
  return request({
    url: '/workOrder/export/sumForDayExport',
    method: 'get',
    params: params
  });
}

/**
 * 获取工单类型下拉列表
 */
export function apiGetOrderType() {
  return request({
    url: '/workorderReport/getWorkorderTypeList',
    method: 'get'
  });
}

/**
 * 获取客户来源所在地列表
 */
export function apiGetCustomerSource() {
  return request({
    url: '/workorderReport/getCustomerSourceList',
    method: 'get'
  });
}

/**
 * 获取客户来源所在地列表
 * @param {object} params
 * @param params.customerSource 客户来源地
 * @param params.startTime
 * @param params.endTime
 * @param params.formTypeId 工单类型
 * @param params.problemClassification 问题分类
 */
export function apiGetQuestionReport(params) {
  return request({
    url: '/workorderReport/selectQuestionTypeList',
    method: 'post',
    params
  });
}

/**
 *问题分类树
 * @param formTypeId 父id
 */
export function apigetProblemTree(formTypeId) {
  return request({
    url: '/businessType/tree',
    method: 'get',
    params: { formTypeId }
  });
}

/**
 *工单量相关导出相关报表
 * @param {object} params 客户来源地
 * @param params.startTime
 * @param params.endTime
 * @param params.formTypeId 工单类型
 */
export function apiExportExcel(params) {
  return request({
    url: '/workOrder/export/getProblemClassificationExport',
    params
  });
}
/* 查询工单信息报表 */
export function listWorkOrderInfoReport(params) {
  return request({
    url: '/workOrderInfo/listWorkOrderInfoReport',
    method: 'get',
    params: params
  });
}

/* 工单信息报表导出 */
export function getWorkOrderInfoExport(params) {
  return request({
    url: '/workOrderInfo/getWorkOrderInfoExport',
    method: 'get',
    params: params
  });
}

/* 查看工单节点信息 */
export function getNodeInfoByWorkOrderId(params) {
  return request({
    url: '/workOrderInfo/getNodeInfoByWorkOrderId',
    method: 'get',
    params: params
  });
}

/* 获取问题分类下拉列表 */
export function getAllTree(params) {
  return request({
    url: '/businessType/tree',
    method: 'get',
    params: params
  });
}
