<template>
  <div>
    <xyy-list-page class="seachim">
      <template slot="header">
        <!--搜索栏-->
        <query-search
          :options="searchOptions"
          style="margin-bottom: 12px"
          @fetch-data="handleSearch"
          @reset-opt="resetClick"
          @export-excel="exportData"
        ></query-search>
      </template>
      <!-- :style="`width: ${tableWidth+70}px`" -->
      <template slot="body">
        <xyy-table
          :data="tableData"
          :list-query="listQuery"
          :col="columns"
          :has-selection="false"
          :has-index="true"
          @get-data="getList(listQuery)"
          @operation-click="operationClick"
          @handleCellClick="handleCellClick"
        >
          <template slot="submitTime" slot-scope="{col}">
            <el-table-column
              :key="col.index"
              :prop="col.index"
              :label="col.name"
              :width="col.width"
              :formatter="formatSubmitTime"
            />
            <!-- <template slot-scope="item">
              <span v-if="item.submitTime">{{ item.submitTime | dateFormat }}</span>
            </template>-->
          </template>
          <template slot="invoiceType" slot-scope="{col}">
            <el-table-column
              :key="col.index"
              :prop="col.index"
              :label="col.name"
              :formatter="formatInvoiceType"
            />
          </template>
        </xyy-table>
      </template>
    </xyy-list-page>
    <expor-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    ></expor-tip>
  </div>
</template>

<script>
import moment from 'moment';
import exporTip from '@/views/work-sheet/components/exportTip';
import querySearch from '../components/query-form.vue';
import searchConfig from './searchConfig';
import { getCustomInfoList, exportCustomInfoList } from '@/api/shop';
const dateTime = [
  moment()
    .subtract(30, 'days')
    .format('YYYY-MM-DD') + ' 00:00:00',
  moment().format('YYYY-MM-DD') + ' 23:59:59'
];
export default {
  name: 'CustomerInfoRegistration',
  components: { querySearch, exporTip },
  data() {
    return {
      tableWidth: 0, // 表格的宽度
      changeExport: false,
      isSelf: false,
      tableData: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      startTime: '',
      endTime: '',
      columns: [
        {
          index: 'submitTime',
          name: '提交时间',
          width: 190,
          filter: val => {
            return moment(val).format('YYYY-MM-DD HH:mm:ss');
          }
        },
        { index: 'drugName', name: '药品名称' },
        { index: 'invoiceType', name: '发票类型' },
        { index: 'pharmacyName', name: '药店名称' },
        { index: 'phone', name: '注册手机号' },
        { index: 'mailDelivery', name: '邮寄快递' },
        { index: 'logisticsNumber', name: '物流单号' },
        { index: 'btachList', name: '批号与数量', ellipsis: true }
      ],
      invoiceType: {
        '10': '电子发票',
        '20': '纸质发票',
        '30': '专票'
      },
      searchOptions: searchConfig
    };
  },
  activated() {
    this.$nextTick(() => this.getList(this.listQuery));
    // this.setTableWidth();
  },
  methods: {
    setTableWidth() {
      const arr = this.columns;
      let width = 0;
      arr.forEach(item => {
        width += item.width;
      });
      this.tableWidth = width + 70;
    },
    // 查询搜索栏的操作
    handleSearch(data) {
      this.listQuery = JSON.parse(
        JSON.stringify({ ...this.listQuery, ...data })
      );
      this.getList(this.listQuery);
    },
    resetClick(data) {
      const pageParams = {
        page: 1,
        pageSize: 10,
        total: 0
      };
      const newObj = { ...pageParams, ...data };
      this.getList(newObj);
    },
    formatSubmitTime(row, column, cellValue, index) {
      console.log(cellValue, 'cellValue');
      // return cellValue ? moment(cellValue).format('YYYY-MM-DD HH:mm:ss') : '';
    },
    formatInvoiceType(row, column, cellValue, index) {
      // return this.invoiceType[cellValue];
    },
    async exportData(data) {
      if (!this.tableData.length) {
        return this.$message.info('无导出数据！');
      }
      const startTime =
        data.submitTime &&
        data.submitTime.length &&
        moment(data.submitTime[0]).valueOf();
      const endTime =
        data.submitTime &&
        data.submitTime.length &&
        moment(data.submitTime[1]).valueOf();
      const params = {
        drugName: data.drugName || null,
        startTime: +startTime || null,
        endTime: +endTime || null,
        invoiceType: data.invoiceType || null,
        logisticsNumber: data.logisticsNumber || null,
        mailDelivery: data.mailDelivery || null,
        pharmacyName: data.pharmacyName || null,
        phone: data.phone || null
      };
      try {
        const res = await exportCustomInfoList(params);
        if (res && res.code === 1) {
          this.changeExport = true;
        } else {
          this.$XyyMessage.error(res.msg);
        }
      } catch (error) {
        console.log(error);
      }
    },
    handleCellClick(row, column, cell, event) {
      // this.rowId = row.id; // row.id
      console.log(row);
    },
    resetForm(formName) {
      this.search.endTime = '';
      this.$refs[formName].resetFields();
    },
    async getList(data) {
      this.startTime = moment(dateTime[0]).valueOf();
      this.endTime = moment(dateTime[1]).valueOf();
      const { page, pageSize } = data || this.listQuery;
      // console.log(page, pageSize);
      const startTime =
        data.submitTime &&
        data.submitTime.length &&
        moment(data.submitTime[0]).valueOf();
      const endTime =
        data.submitTime &&
        data.submitTime.length &&
        moment(data.submitTime[1]).valueOf();
      const params = {
        pageNum: page,
        pageSize: pageSize,
        drugName: data.drugName || null,
        startTime: +startTime || this.startTime,
        endTime: +endTime || this.endTime,
        invoiceType: data.invoiceType || null,
        logisticsNumber: data.logisticsNumber || null,
        mailDelivery: data.mailDelivery || null,
        pharmacyName: data.pharmacyName || null,
        phone: data.phone || null
      };
      const res = await getCustomInfoList(params);
      if (res && res.code === 1) {
        const list = res.data && res.data.list;
        this.listQuery.total = res.data && res.data.total;
        if (list && list.length) {
          list.forEach(ele => {
            ele.invoiceType = this.invoiceType[ele.invoiceType];
            ele.submitTime =
              moment(ele.submitTime).format('YYYY-MM-DD HH:mm:ss') || '';
            let str = '';
            (ele.btachList || []).forEach(e => {
              str += ` ${e.batch_number}: ${e.batch_number_total}; `;
            });
            ele.btachList = str;
          });
        }
        this.tableData = list;
      } else {
        this.$XyyMessage.error(res.msg);
      }
    },
    // 操作回调
    operationClick: function(type, row) {},
    handleExoprClose() {
      this.changeExport = false;
    },
    handleChangeExport(info) {
      const that = this;
      this.changeExport = false;
      if (info === 'go') {
        that.$router.push({
          path: '/other/index/'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ .search-btns-box {
  width: 265px;
}
</style>
