<template>
  <div class="page">
    <xyy-list-page>
      <template slot="header">
        <el-row class="herader-title" type="flex" justify="space-between" align="middle">
          <span style="font-size:16px">节点相关报表</span>
          <el-button icon="el-icon-upload2" class="export-excel" @click="exportData">导出Excel</el-button>
        </el-row>
        <el-form
          ref="listQuery"
          :rules="rules"
          :model="listQuery"
          :inline="true"
          label-position="right"
          class="search-form"
        >
          <el-row :gutter="10" type="flex" class="row-bg" justify="space-between">
            <!-- 发起时间-->
            <el-form-item label="日期" prop="dataRange">
              <el-date-picker
                v-model="listQuery.dataRange"
                :picker-options="pickerOptions"
                type="daterange"
                range-separator="-"
                size="small"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                text="erdf"
                prefix-icon="el-icon-date"
                class="innerSelTime"
                @focus="dateTimeFocus()"
              />
            </el-form-item>
            <!--工单类型-->
            <el-form-item class="form-item" label="工单类型" prop="workOrderType">
              <el-select v-model="listQuery.workOrderType" placeholder="请选择">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in sheetTypeList"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <!--客户所在地-->
            <el-form-item class="form-item" label="客户所在地" prop="customArea">
              <el-select v-model="listQuery.customArea" placeholder="请选择">
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in workLocation"
                  :key="item.id"
                  :label="item.sourceName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row :gutter="10" type="flex" class="row-bg" justify="space-between">
            <el-form-item label="节点" prop="pool" class="nodeClass">
              <el-input v-model="listQuery.pool" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item style="text-align:right;float:right">
              <el-button
                plain
                type="primary"
                size="small"
                class="searchCondition"
                @click="handerSearch('listQuery')"
              >查询</el-button>
              <el-button plain size="small" @click="resetForm">重置</el-button>
            </el-form-item>
          </el-row>
        </el-form>
      </template>
      <template slot="body">
        <div class="info-container">
          <info :info="info"></info>
        </div>
        <xyy-table
          :data="list"
          :col="col"
          :list-query="listQuery"
          :is-pagination="true"
          :is-stripe="false"
          @get-data="handerSearch('listQuery')"
        >
          <template slot="poolName" slot-scope="{col}">
            <el-table-column
              :key="col.index"
              :prop="col.index"
              :label="col.name"
              :width="col.width"
              :fixed="col.fixed"
              :min-width="150"
              show-overflow-tooltip
            ></el-table-column>
          </template>
        </xyy-table>
      </template>
    </xyy-list-page>
    <expor-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    ></expor-tip>
  </div>
</template>

<script>
import exporTip from '@/views/work-sheet/components/exportTip';
import {
  getCustomerSourceList,
  getWorkorderTypeList,
  getGroupByPool,
  nodeExportData
} from '@/api/report/index';
export default {
  name: 'NodeRelate',
  components: {
    exporTip
  },
  data() {
    return {
      changeExport: false,
      info: [
        {
          title: '超时处理工单数',
          info:
            '节点接到工单时间为当日，当日超时，超时后被处理，计算这些工单的总数'
        },
        {
          title: '接收工单总次数',
          info:
            '节点接到工单时间为当日，无论工单创建时间为哪天，计算这些工单的总次数注：同一条工单多次流转同一节点，计为n'
        },
        {
          title: '接收工单总数',
          info:
            '节点接到工单时间为当日，无论工单创建时间为哪天，计算这些工单的总数' +
            '注：同一条工单多次流转同一节点，计为1'
        },
        {
          title: '超时工单总数',
          info: '节点接到工单时间为当日，当日超时，计算这些工单的总数'
        },
        {
          title: '领取工单数',
          info: '节点接到工单时间为当日，领取时间也为当日，计算这些工单的总数'
        },
        {
          title: '领取超时工单数',
          info:
            '节点接到工单时间为当日，当日超时，超时后被领取，领取时间为当日，计算这些工单的总数'
        },
        {
          title: '超时工单数（未领取）',
          info:
            '节点接到工单时间为当日，当日超时，超时后未被领取，计算这些工单的总数'
        },
        {
          title: '工单平均处理时长',
          info: '各节点从接到工单到回复提交之间的时长，计算平均值'
        },
        {
          title: '首次响应工单数',
          info: '首次点击“去处理”的时间在当日内，计算各节点这些工单的总量'
        },
        {
          title: '领取当日首次响应工单数',
          info:
            '领取当日首次响应工单数 = 某部门/坐席 当天领取，并且首次响应的工单数。如当日响应多次仅计一次'
        },
        {
          title: '平均首次响应时长',
          info:
            '工单响应时间在当日内，响应时长=工单响应时间-工单领取时间，计算各节点这些工单总量的平均用时'
        }
      ],
      list: [], // 表单数据
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0,
        dataRange: [new Date(new Date().setHours(0, 0, 0, 0)), new Date()], // 日期
        workOrderType: '', // 工单类型
        customArea: '', // 客户所在地,
        pool: ''
      },
      minDate: '',
      maxDate: '',
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.minDate = minDate;
          this.maxDate = maxDate;
        },
        disabledDate: time => {
          const curDate = new Date().getTime();
          const two = 365 * 2 * 24 * 3600 * 1000;
          const twoyear = curDate - two;
          const three = 92 * 24 * 3600 * 1000;
          if (this.minDate) {
            return (
              time.getTime() > Date.now() ||
              time.getTime() < twoyear ||
              time > new Date(this.minDate.getTime() + three) ||
              time < new Date(this.minDate.getTime() - three)
            );
          }
          return time.getTime() > Date.now() || time.getTime() < twoyear;
        }
      },
      rules: {
        dataRange: [
          { required: true, message: '日期为必填项', trigger: 'blur' }
        ]
      },

      col: [
        {
          index: 'poolName',
          name: '节点',
          slot: true,
          fixed: true,
          resizable: true
        },
        {
          index: 'timeoutHandlerNum',
          name: '超时处理工单数',
          width: 150,
          resizable: true
        },
        {
          index: 'totalAcceptNum',
          name: '接收工单总次数',
          width: 150,
          resizable: true
        },
        {
          index: 'allReceivedCount',
          name: '接收工单总数',
          width: 130,
          resizable: true
        },
        {
          index: 'allOvertimeCount',
          name: '超时工单总数',
          width: 130,
          resizable: true
        },
        {
          index: 'allTakeCount',
          name: '领取工单数',
          width: 130,
          resizable: true
        },
        {
          index: 'allOvertimeTakeCount',
          name: '领取超时工单数',
          width: 150,
          resizable: true
        },
        {
          index: 'allOvertimeUnTakeCount',
          name: '超时工单数',
          smallName: '未领取',
          width: 130,
          resizable: true
        },
        {
          index: 'avgHandleUseTime',
          name: '工单平均处理时长(秒)',
          width: 180,
          resizable: true
        },
        {
          index: 'firstResponseCount',
          name: '首次响应工单数',
          width: 150,
          resizable: true
        },
        {
          // 2020.4.1,rl,v1.7.4
          index: 'currentTakeFirstResponseCount',
          name: '领取当日首次响应工单数',
          width: 200,
          resizable: true
        },
        {
          index: 'avgfirstResponseCount',
          name: '平均首次响应时长(秒)',
          width: 180,
          resizable: true
        }
      ],
      sheetTypeList: [],
      workLocation: [] // 客户所在地
    };
  },
  created() {
    this.getWorkorderTypeList();
    this.getCustomerSourceList();
  },
  mounted() {
    // this.handerSearch('listQuery');
  },
  methods: {
    handleChangeExport(info) {
      const that = this;
      this.changeExport = false;
      if (info === 'go') {
        that.$router.push({
          path: '/other/index/'
        });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    exportData() {
      const that = this;
      if (!that.list || !that.list.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      const { dataRange } = this.listQuery;
      const params = JSON.parse(JSON.stringify(this.listQuery));
      delete params.dataRange;
      params.startDate = new Date(dataRange[0]).Format('yy-MM-dd');
      params.endDate = new Date(dataRange[1]).Format('yy-MM-dd');
      nodeExportData(params).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.changeExport = true;
      });
    },
    dateTimeFocus() {
      this.minDate = undefined;
      // 条件时间查询
      const that = this;
      that.$nextTick(function() {
        document
          .getElementsByClassName('el-picker-panel')[0]
          .addEventListener('click', function() {
            // that.$refs.messageDrop.show();
          });
      });
    },
    // 点击搜索事件
    handerSearch(name) {
      const that = this;
      this.$refs[name].validate(valid => {
        if (valid) {
          const { dataRange } = this.listQuery;
          const params = JSON.parse(JSON.stringify(this.listQuery));
          delete params.dataRange;
          params.startDate = new Date(dataRange[0]).Format('yy-MM-dd');
          params.endDate = new Date(dataRange[1]).Format('yy-MM-dd');
          getGroupByPool(params).then(res => {
            const dataListList = res.data;
            if (
              !dataListList &&
              typeof dataListList !== 'undefined' &&
              dataListList !== 0
            ) {
              this.$XyyMessage.success('暂时无数据');
              this.list = [];
              return;
            }
            if (res.code !== 1) {
              this.$XyyMessage.warning(res.msg);
              return;
            }
            if (res.data.list && res.data.list.length === 0) {
              this.$XyyMessage.success('暂时无数据');
            }
            this.list = res.data.list;
            const { total, pageSize, pageNum } = res.data;
            that.listQuery = {
              ...that.listQuery,
              page: pageNum,
              pageSize,
              total: Number(total)
            };
          });
        }
      });
    },
    getWorkorderTypeList() {
      getWorkorderTypeList()
        .then(res => {
          if (res.code !== 1) {
            this.$XyyMessage.warning(res.msg);
            return;
          }
          this.sheetTypeList = res.data;
        })
        .catch(err => {
          console.log(err);
        });
    },
    getCustomerSourceList() {
      getCustomerSourceList().then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.workLocation = res.data;
      });
    },
    resetForm() {
      this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0,
        dataRange: [new Date(new Date().setHours(0, 0, 0, 0)), new Date()], // 日期
        workOrderType: '', // 工单类型
        customArea: '', // 客户所在地,
        pool: ''
      };
    }
  }
};
</script>

<style scoped lang="scss">
/deep/.nodeClass {
  /deep/.el-form-item__label {
    padding-left: 10px;
  }
}
/deep/.el-table {
  td.is-hidden {
    .cell {
      visibility: inherit !important;
    }
  }
}
/deep/.el-form-item__label {
  font-weight: 500;
}
/deep/.el-table {
  /deep/.el-table__fixed {
    z-index: 1;
  }
  /deep/.el-table__body-wrapper {
    z-index: 1;
  }
  /deep/.el-table__fixed-body-wrapper {
    top: 65px !important;
  }
}
/deep/.page-header {
  padding-bottom: 0 !important;
}
.search-form {
  border-bottom: 1px dashed #e4e4eb;
}
.herader-title {
  font-size: 14px;
  color: #393943;
  padding-bottom: 20px;
  border-bottom: 1px dashed #e4e4eb;
  margin-bottom: 20px;
}
/deep/.el-table__body tr td {
  border-bottom: 1px solid #ebeef5;
  background: #fff;
}
/deep/.el-date-editor {
  /*width: 450px;*/
}
/deep/.el-input__inner {
  height: 36px !important;
  line-height: 36px !important;
}
/deep/.el-button {
  height: 36px;
}
.searchCondition.is-plain {
  background: rgba(59, 149, 168, 1);
  color: #fff;
}
/deep/.searchCondition.is-plain:hover {
  background: rgba(40, 126, 144, 1);
}
/deep/.el-table th {
  padding-top: 10px;
  padding-bottom: 10px;
  text-align: right;
  padding-right: 19px;
  &:first-child {
    text-align: left;
  }
  p {
    margin: 0;
    height: 20px;
  }
  small {
    color: #909399;
    font-size: 12px;
    height: 17px;
  }
}
</style>
