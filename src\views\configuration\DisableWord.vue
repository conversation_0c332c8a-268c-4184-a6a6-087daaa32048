<template>
  <div class="disableLang">
    <xyy-list-page>
      <template slot="header">
        <el-form :inline="true" :model="formTitle" label-position="left" ref="formTitle">
          <el-row>
            <el-col :span="2">
              <xyy-button @click="newIncrease">
                <span style="font-size: 18px">+</span>新建禁用词
              </xyy-button>
            </el-col>
          </el-row>
        </el-form>
      </template>

      <template slot="body">
        <div class="xyyTable">
          <xyy-table
            :col="col"
            :data="tableData"
            :list-query="listQuery"
            :offset-top="240"
            @get-data="seachDataList"
          >
            <template slot="words" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :label="col.name"
                :prop="col.index"
                :width="col.width"
              ></el-table-column>
            </template>
            <template slot="createtime" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :label="col.name"
                :prop="col.index"
                :width="col.width"
              ></el-table-column>
            </template>

            <template slot="status" slot-scope="{col}">
              <el-table-column :key="col.index" :label="col.name">
                <template slot-scope="{row}">
                  <span v-if="row.status === 1" class="currentState1" @click="enableClick(row)">启用</span>
                  <span
                    v-else-if="row.status === 0"
                    class="currentState1"
                    @click="disableClick(row)"
                  >禁用</span>
                </template>
              </el-table-column>
            </template>
            <template slot="operation" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :label="col.name"
                :width="col.width"
              >
                <template slot-scope="scope">
                  <el-row>
                    <el-button type="text" @click.native="handerEditor(scope.$index, scope.row)">编辑</el-button>
                    <el-button type="text" @click.native="handerDelete(scope.$index, scope.row)">删除</el-button>
                  </el-row>
                </template>
              </el-table-column>
            </template>
          </xyy-table>
        </div>
      </template>
    </xyy-list-page>
    <!--新建禁用词-开始-->
    <el-dialog :visible.sync="dialogEditor" title="添加禁用词" class="dialogInner" width="400px">
      <el-form :inline="true" :model="formTitle" label-position="left" ref="formTitle">
        <el-input v-model="formTitle.search" maxlength="30"></el-input>
      </el-form>

      <span class="dialog-footer" slot="footer">
        <el-button @click="dialogCancel">取 消</el-button>
        <el-button type="primary" @click="dialogDetermine">确 定</el-button>
      </span>
    </el-dialog>
    <!--新建禁用词-结束-->
    <!--编辑禁用词-开始-->
    <el-dialog :visible.sync="dialogEditorB" title="编辑禁用词" class="dialogInner" width="400px">
      <el-form :inline="true" :model="formTitleB" label-position="left" ref="formTitle">
        <el-input v-model="formTitleB.searchB" maxlength="30"></el-input>
      </el-form>

      <span class="dialog-footer" slot="footer">
        <el-button @click="dialogCancelB">取 消</el-button>
        <el-button @click="dialogDetermineB" type="primary">确 定</el-button>
      </span>
    </el-dialog>
    <!--编辑禁用词-结束-->
  </div>
</template>

<script>
import TemplatePreview from '../form/components/template-preview/index';
import XyyButton from '../../components/xyy/xyy-button/index';
import XyyTable from '../../components/xyy/xyy-table/index';
import {
  disableWordList,
  addWordList,
  aremoveWordList,
  editorWordList,
  enableDisable,
} from '@/api/configuration/BlacklistManage';
export default {
  name: 'compDisableWord',
  components: { XyyTable, XyyButton, TemplatePreview },
  data() {
    return {
      dialogEditor: false, // 新建禁用词弹框
      dialogEditorB: false, // 控制编辑禁用词
      rowlist: [], // 存储整行
      formTitle: {
        search: '', // title搜索
      },
      formTitleB: {
        searchB: '', // title搜索
      },
      // 表格显示的数据
      tableData: [],

      listQuery: {
        page: 1, // 当前页数
        pageSize: 10, // 每页显示条目个数
        total: 0, // 总条目数
      },
      col: [
        { index: 'words', width: 600, name: '禁用词', slot: true },
        { index: 'createtime', name: '添加日期' },
        { index: 'status', name: '启用状态', slot: true },
        { index: 'operation', name: '操作', slot: true, width: 130 },
      ],
      dataInput: '', // input框的数据
    };
  },
  methods: {
    // 新建禁用词取消事件
    dialogCancel() {
      this.dialogEditor = false;
    },
    // 新建禁用词确定事件
    dialogDetermine() {
      //   获取搜索框里面的数据 传给后台
      const searchDate = {
        words: this.formTitle.search,
      };

      addWordList(searchDate)
        .then((response) => {
          // 保存后 获取禁用词列表数据
          this.listQuery.page = 1;
          this.seachDataList(this.listQuery);
        })
        .catch(function (error) {
          console.log(error);
        });
      // 传给后台以后 数据清空 搜索框消失 重新更新数据
      this.formTitle.search = '';
      this.dialogEditor = false;
    },
    // 编辑禁用词取消事件
    dialogCancelB() {
      this.dialogEditorB = false;
    },
    // 编辑禁用词确定事件
    dialogDetermineB() {
      let searchDate = {
        id: this.rowlist.id,
        words: this.formTitleB.searchB,
      };
      this.listQuery.page = 1;
      editorWordList(searchDate)
        .then((response) => {
          // 保存后 获取禁用词列表数据
          this.seachDataList(this.listQuery);
        })
        .catch(function (error) {
          console.log(error);
        });
      this.dialogEditorB = false;
    },
    // 删除按钮
    handerDelete(index, row) {
      this.$XyyMsg({
        title: '提示',
        content: '确定删除此表单应用吗？',
        onSuccess: () => {
          let searchDate = {
            id: row.id,
          };
          aremoveWordList(searchDate)
            .then((response) => {
              // 保存后 获取禁用词列表数据
              this.seachDataList(this.listQuery);
            })
            .catch(function (error) {
              console.log(error);
            });
        },
      });
    },
    // 点击启用
    enableClick(row) {
      const params = {
        id: row.id,
        status: 0,
      };
      enableDisable(params)
        .then((response) => {
          if (response.code === 1) {
            // 改变显示文字
            row.status = 0;
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    // 点击禁用
    disableClick(row) {
      let params = {
        id: row.id,
        status: 1,
      };
      enableDisable(params)
        .then((response) => {
          if (response.code) {
            // 改变显示文字
            row.status = 1;
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    // 点击编辑按钮
    handerEditor(index, row) {
      this.formTitleB.searchB = row.words;
      this.rowlist = row;
      this.dialogEditorB = true;
    },
    // 查询列表数据
    seachDataList(listQuery) {
      // 查询列表数据
      const that = this;
      const { page, pageSize } = listQuery || this.listQuery;
      const preParam = {
        pageNum: page,
        pageSize: pageSize,
      };

      disableWordList(listQuery)
        .then((response) => {
          const { pageNum, pageSize, total } = response.data;
          response.data.content.forEach(function (item, index) {
            that.$set(item, 'dsableWord', true);
            that.$set(item, 'newWords', '');
          });
          this.tableData = response.data.content;
          console.log('this.tableData');
          console.log(this.tableData);
          this.listQuery = {
            ...this.listQuery,
            page: pageNum,
            pageSize: pageSize,
            total: total,
          };
        })
        .catch(function (error) {
          console.log(error);
        });
    },

    // 点击新增按钮事件
    newIncrease() {
      this.dialogEditor = true;
    },
    // 点击启用状态按钮
    enableBtn(item) {
      if (item.row.address === 0) {
        item.row.address = 1;
      } else {
        item.row.address = 0;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.operation-box {
  width: auto;
  white-space: nowrap;
  letter-spacing: 0;
  margin: 0 -10px;
  .el-button {
    position: relative;
    margin: 0 10px;
  }
  .el-button::before {
    position: absolute;
    top: 14px;
    right: -10px;
    content: '';
    display: block;
    width: 1px;
    height: 12px;
    background: #dcdfe6;
  }
  .el-button:first-child {
    margin-left: 0;
  }
  .el-button:last-child {
    margin-right: 0;
  }
  .el-button:last-child::before {
    display: none;
  }
}
.disableLang {
  .el-input__inner {
    height: 37px;
    line-height: 37px;
    margin-right: 5px;
  }
}
</style>
<style scoped lang="scss">
.disableLang {
  .disableLangInput {
    display: flex;
    > div {
      margin-right: 5px;
    }
  }
}
</style>
