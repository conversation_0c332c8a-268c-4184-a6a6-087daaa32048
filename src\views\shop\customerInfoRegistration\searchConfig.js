import moment from 'moment';
const dateTime = [
  moment()
    .subtract(30, 'days')
    .format('YYYY-MM-DD') + ' 00:00:00',
  moment().format('YYYY-MM-DD') + ' 23:59:59'
];
const searchOptions = [
  {
    label: '时间',
    type: 'datetimerange',
    key: 'submitTime',
    defaultValue: dateTime,
    value: dateTime,
    clearable: false,
    required: false,
    valueFormat: 'yyyy-MM-dd HH:mm:ss',
    moreThanToday: true,
    // span: 8,
    labelWidth: '40px'
  },
  {
    label: '药品名称',
    type: 'default',
    key: 'drugName',
    value: '',
    placeholder: '请输入药品名称',
    required: false,
    maxlength: 50,
    labelWidth: '70px'
    // colWidth: ''
  },
  {
    label: '发票类型',
    type: 'list',
    key: 'invoiceType',
    value: '',
    placeholder: '请选择发票类型',
    required: false,
    clearable: true,
    options: [
      {
        value: '10',
        label: '电子发票'
      },
      {
        value: '20',
        label: '纸质发票'
      },
      {
        value: '30',
        label: '专票'
      }
    ],
    labelWidth: '70px'
  },
  {
    label: '药店名称',
    type: 'default',
    key: 'pharmacyName',
    value: '',
    placeholder: '请输入药店名称',
    required: false,
    labelWidth: '70px'
    // colWidth: ''
  },
  {
    label: '注册手机号',
    type: 'default',
    key: 'phone',
    value: '',
    placeholder: '请输入注册手机号',
    required: false,
    labelWidth: '85px'
    // colWidth: ''
  },
  {
    label: '邮寄快递',
    type: 'default',
    key: 'mailDelivery',
    value: '',
    placeholder: '请输入邮寄快递',
    required: false,
    labelWidth: '70px'
    // colWidth: ''
  },
  {
    label: '物流单号',
    type: 'default',
    key: 'logisticsNumber',
    value: '',
    placeholder: '请输入物流单号',
    required: false,
    labelWidth: '70px'
  },
  {
    label: '',
    labeClass: 'label-button',
    type: 'button',
    className: 'search-btns-box',
    btns: [
      {
        name: '查询',
        key: 'search',
        type: 'primary',
        size: 'small',
        className: ''
      },
      {
        name: '重置',
        key: 'reset',
        type: '',
        size: 'small',
        className: ''
        // method: this.exportData()
      },
      {
        name: '导出excel',
        key: 'custom',
        type: '',
        size: 'small',
        className: 'export-excel'
        // method: this.exportData()
      }
    ]
  }
];
export default searchOptions;
