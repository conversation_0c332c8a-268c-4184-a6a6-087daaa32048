@import './tableBase.scss';
// base color
$blue:#324157;
$light-blue:#3A71A8;
$red:#C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow:#FEC171;
$panGreen: #30B08F;
$messageRed:#FF3024;
$messageText:#575766;
//sidebar
$menuText:#303133;
$menuActiveText : #3B95A8;
$subMenuActiveText:$menuActiveText; //https://github.com/ElemeFE/element/issues/12951
$subMenuActiveSt:#F0F2F5;

$menuBg:#fff;
$menuHover:#DAE9EC;
$menuActive:#DAE9EC;

$subMenuBg:#fff;
$subMenuHover:#DAE9EC;
$subMenuActive:#fff;
$subMenuBg1:#F5F7FA; // 未选中的背景色
$subMenuActive1:#DAE9EC; // 修改后的选中菜单的背景色

$sideBarWidth: 210px;

$navBarBg:#3B95A8; // 导航条色彩
$navLogoBg:#40A3B8; // LOGO色彩BG
$navLogoText:#fff; // LOGO文字色彩
$TagsViewHeight:64px; // 导航条色彩
$navBarHeight:64px; // 导航条色彩

$appMainBg:#F0F2F5; // 主视图背景

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  subMenuActive:$subMenuActiveSt;
  navBarBg:$navBarBg;
  navLogoBg:$navLogoBg;
  navLogoText:$navLogoText;
  navBarHeight:$navBarHeight;
  TagsViewHeight:$TagsViewHeight;
  appMainBg:$appMainBg;
  menuActive:$menuActive;
}
