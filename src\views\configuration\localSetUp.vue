<template>
  <div>
    <xyy-list-page>
      <template slot="header">
        <el-form
          :model="listQuery"
          ref="listQuery"
          label-width="100px"
          class="demo-form-inline search-box"
          :inline="true"
        >
          <el-form-item label="组合名称" prop="groupName">
            <el-input v-model="listQuery.groupName" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="包含省市" prop="cityName">
            <el-input v-model="listQuery.cityName" placeholder="请输入省市名称"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="query" type="primary">查询</el-button>
            <el-button @click="resetForm('listQuery')">重置</el-button>
          </el-form-item>
        </el-form>
      </template>
      <template slot="body">
        <div class="xyyTable">
          <el-button @click="jumpNewLocal" class="btnNew">
            <span class="el-icon-plus"></span>新建地理组合
          </el-button>
          <xyy-table
            :data="Datelist"
            :col="col"
            :list-query="listQuery"
            :offset-top="240"
            @get-data="seachDataList"
          >
            <!--地理名称-->
            <template slot="groupName" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :label="col.name"
                :width="col.width"
                show-overflow-tooltip
              ></el-table-column>
            </template>
            <!--备注-->
            <template slot="groupDes" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :label="col.name"
                :width="col.width"
              ></el-table-column>
            </template>
            <!--包含省市-->
            <template slot="cityNames" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :label="col.name"
                :width="col.width"
                show-overflow-tooltip
              ></el-table-column>
            </template>
            <!--对应员工组-->
            <template slot="groupNames" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :label="col.name"
                :width="col.width"
              ></el-table-column>
            </template>
            <!--最近修改时间-->
            <template slot="modifyTime" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :label="col.name"
                :width="col.width"
              ></el-table-column>
            </template>
            <!--最近修改人-->
            <template slot="modifyName" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :label="col.name"
                :width="col.width"
              ></el-table-column>
            </template>
            <!--操作-->
            <template slot="operation" slot-scope="{col}">
              <el-table-column
                :key="col.index"
                :prop="col.index"
                :label="col.name"
                :width="col.width"
              >
                <template slot-scope="scope">
                  <el-row>
                    <el-button type="text" @click="handerEditor(scope.$index, scope.row)">编辑</el-button>
                    <el-button type="text" @click="handerDelete(scope.$index, scope.row)">删除</el-button>
                  </el-row>
                </template>
              </el-table-column>
            </template>
          </xyy-table>
        </div>
      </template>
    </xyy-list-page>
  </div>
</template>

<script>
import XyyListPage from '../../components/xyy/xyy-list-page/index';
import { localeSet, removeLocale } from '@/api/configuration/RegionalMenu';
export default {
  name: 'compLocalSetUp',
  components: { XyyListPage },
  data() {
    return {
      Datelist: [],
      listQuery: {
        page: 1,
        pageSize: 10,
        total: 0,
        cityName: '', // 城市名称
        groupName: '', // 区域分组的名称
      },
      col: [
        { index: 'groupName', name: '地理名称', slot: true },
        { index: 'groupDes', name: '备注', slot: true },
        { index: 'cityNames', name: '包含省市', slot: true },
        { index: 'groupNames', name: '对应员工组', slot: true },
        { index: 'modifyTime', name: '最近修改时间', slot: true },
        { index: 'modifyName', name: '最近修改人', slot: true },
        { index: 'operation', name: '操作', slot: true, width: 130 },
      ],
    };
  },
  activated() {
    this.query();
  },
  methods: {
    // 查询表单
    query() {
      this.listQuery.page = 1;
      this.listQuery.pageSize = 10;
      this.seachDataList(this.listQuery);
    },
    // 表格内部编辑 跳转到编辑页面
    handerEditor(index, row) {
      this.$router.push({
        path: '/chat/localSetUpNew',
        query: {
          obj: row,
        },
      });
    },
    // 数据删除事件(表格内部删除事件)
    handerDelete(index, row) {
      let params = {
        page: this.listQuery.page,
        size: this.listQuery.pageSize,
        cityName: this.listQuery.cityName,
        groupName: this.listQuery.groupName,
      };
      this.$confirm('确定删除当前区域分组？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        removeLocale(row.id)
          .then((response) => {
            if (response.code === 0) {
              this.$message(response.msg);
              return;
            }
            this.seachDataList(params);
            this.$message('删除成功');
          })
          .catch(function (error) {
            console.log(error);
          });
      });
    },
    // 重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    // table获取数据事件  localeSet
    seachDataList(listQuery) {
      const { page, pageSize } = listQuery || this.listQuery;
      listQuery = {
        page: page,
        size: pageSize,
        cityName: this.listQuery.cityName, // 城市名称
        groupName: this.listQuery.groupName, // 区域分组的名称
      };
      console.log(listQuery);
      //   申请接口带入参数查询数据
      localeSet(listQuery)
        .then((response) => {
          const { current, size, total } = response.data;
          this.Datelist = response.data.records;
          this.listQuery = {
            ...this.listQuery,
            page: Number(current),
            pageSize: Number(size),
            total: Number(total),
          };
        })
        .catch(function (error) {
          console.log(error);
        });
    },

    // 新建地里组合事件
    jumpNewLocal() {
      this.$router.push({
        path: '/chat/localSetUpNew',
      });
    },
  },
};
</script>

<style scoped lang="scss">
.xyyTable {
  .table-containter {
    margin-top: 20px;
  }
  .btnNew {
    border-radius: 2px;
    border: 1px solid rgba(59, 149, 168, 1);
    color: rgba(59, 149, 168, 1);
  }
}
.search-box {
  border-bottom: 1px dashed #e4e4eb;
}
</style>
