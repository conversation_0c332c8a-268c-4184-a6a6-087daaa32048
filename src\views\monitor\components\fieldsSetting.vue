<template>
  <div>
    <el-dialog
      :visible="status"
      :title="title"
      custom-class="user-group-box"
      top="0"
      @close="close"
    >
      <div class="user-content">
        <div class="user-list">
          <div class="search-box">
            <el-input
              v-model="searchKey"
              placeholder="请输入字段名"
              maxlength="20"
              suffix-icon="el-icon-search"
            />
          </div>
          <div class="main-box">
            <el-checkbox-group v-if="filterUserList.length" v-model="leftCheckedDatas">
              <el-checkbox
                v-for="el in filterUserList"
                :label="el"
                :key="el.index"
                @change="leftToRight"
              >{{ `${el.name}` }}</el-checkbox>
              <!-- <div v-for="(item, index) in filterUserList" :key="item.index" class="checkBox">
                <div style="width:25px;display:inline-block">
                  <el-checkbox
                    :key="item.index"
                    :label="item"
                    @change="getUserInfo($event, item.index, index)"
                  >&nbsp;</el-checkbox>
                </div>

                <span
                  :key="item.index"
                  :class="{active: item.active}"
                  style="font-size:14px;cursor: pointer;"
                  @click="getUserInfo($event, item.index, index)"
                >{{ item.name }}</span>
              </div>-->
            </el-checkbox-group>
            <span v-else class="empty-result">暂未找到相关字段</span>
          </div>
        </div>
        <div class="user-info">
          <div class="title-box">已选字段({{this.rightDatas.length}})</div>
          <el-row>
            <!-- <div class="main-box">
              <el-col v-for="item in rightDatas" :key="item.index" :span="8">{{ item.name }}</el-col>
            </div>-->
            <div class="tag-list">
              <el-tag
                v-for="tag in rightDatas"
                :key="tag.index"
                closable
                hit
                disable-transitions
                @close="tagClose(tag.index)"
              >{{tag.name}}</el-tag>
            </div>
          </el-row>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">完成</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSettingPersonnelMonitoringShowFieldData } from '@/api/monitor/index.js';
export default {
  props: {
    status: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '提示'
    },
    formTypeId: {
      type: String,
      required: true
    },
    checked: {
      type: Array,
      default: function() {
        return [];
      }
    },
    leftDatas: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      searchKey: '',
      nodeUserList: [],
      rightDatas: [], // 右侧用户数据
      leftCheckedDatas: [] // 左侧选中用户数据
    };
  },
  computed: {
    filterUserList() {
      return this.nodeUserList.filter(
        el =>
          this.formatStr(el.name).search(this.formatStr(this.searchKey)) >= 0,
        this
      );
    }
  },
  watch: {
    status(val, old) {
      if (val) {
        this.initDatas();
      }
    }
  },
  mounted() {
    this.initDatas();
  },
  methods: {
    formatStr(val) {
      val = val.replace(/[\'\"\\\/\b\f\n\r\t]/g, '');
      val = val.replace(/[\@\#\$\%\^\&\*\(\)\{\}\:\"\L\<\>\?\[\]]/g, '');
      return val;
    },
    initDatas() {
      this.initUserList();
    },
    initUserList(cb) {
      this.searchKey = '';
      this.rightDatas = this.checked;

      this.nodeUserList = this.leftDatas.map(el => {
        return el;
      });
      this.leftCheckedDatas = this.nodeUserList.filter(el =>
        this.rightDatas.map(_el => _el.index).includes(el.index)
      );
    },

    /**
     * 选择右侧表单数据
     */
    leftToRight() {
      this.rightDatas = JSON.parse(JSON.stringify(this.leftCheckedDatas));
    },
    /**
     * 关闭回调
     */
    close() {
      this.$emit('update:status', false);
    },
    /**
     * 保存回调
     */
    save() {
      if (!this.rightDatas.length) {
        this.$XyyMessage.error('未有选中项，请重新选择');
        return;
      }
      const sendData = {
        formTypeId: this.formTypeId,
        fieldList: []
      };
      const arr = [];
      this.rightDatas.forEach(item => {
        arr.push({ fieldKey: item.index, fieldValue: item.name });
      });
      sendData.fieldList = arr;
      /*
      人员监控设置配置字段
      */
      getSettingPersonnelMonitoringShowFieldData(sendData).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.$emit('callback', this.rightDatas);
        this.close();
      });
    },
    /**
     * 停用标签
     */
    tagClose(id) {
      if (!this.rightDatas.length) {
        this.$XyyMessage.error('未有选中项，请重新选择');
        return;
      }
      this.leftCheckedDatas.forEach((item, index) => {
        if (item.index === id) {
          this.leftCheckedDatas.splice(index, 1);
        }
      });
      this.rightDatas = JSON.parse(JSON.stringify(this.leftCheckedDatas));
    }
  }
};
</script>

<style lang="scss">
.el-dialog.user-group-box {
  width: 750px;
  height: 500px;
  transform: translateY(-50%);
  top: 50%;
  .el-dialog__header {
    height: 50px;
    padding: 15px 20px;
    .el-dialog__title {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(41, 41, 51, 1);
      height: 22px;
      line-height: 22px;
    }
    .el-dialog__headerbtn {
      top: 18px;
    }
  }
  .el-dialog__body {
    padding: 5px 20px 0;
    height: calc(100% - 126px);
    .user-content {
      height: 100%;
      .user-list {
        float: left;
        width: 290px;
        height: 100%;
        border: 1px solid rgba(228, 228, 235, 1);
        div.search-box {
          height: 55px;
          background: #f0f2f5;
          padding: 12px 20px 11px;
          .el-input {
            /deep/.el-input__inner {
              height: 32px;
              line-height: 32px;
            }
            /deep/.el-input__icon.el-icon-search {
              line-height: 32px;
            }
          }
        }
        div.main-box {
          padding: 20px;
          position: relative;
          height: calc(100% - 55px);
          overflow-y: auto;
          .el-checkbox {
            display: block;
            margin-bottom: 10px;
          }
          .active {
            color: #3b95a8;
          }
          .empty-result {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translateX(-50%) translateY(-50%);
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(174, 174, 191, 1);
          }
        }
      }
      .user-info {
        float: right;
        width: calc(100% - 298px);
        height: 100%;
        overflow: auto;
        border: 1px solid rgba(228, 228, 235, 1);
        div.title-box {
          height: 55px;
          line-height: 55px;
          background: rgba(240, 242, 245, 1);
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: rgba(41, 41, 51, 1);
          padding: 0 20px;
        }

        div.tag-list {
          padding: 20px;
          .el-tag {
            margin: 0 10px 10px 0;
          }
        }
        // div.main-box {
        //   padding: 20px;
        //   .el-col {
        //     margin-bottom: 10px;
        //   }
        // }
      }
    }
  }

  .el-dialog__footer {
    padding: 20px;
    .el-button {
      height: 36px;
      line-height: 36px;
      padding: 0 19px;
    }
  }
}
</style>
