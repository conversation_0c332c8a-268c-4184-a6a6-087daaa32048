<template>
  <div class="common-use">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="快捷回复" name="index1">
        <div class="common-search" @keyup.enter="searchResult('index1')">
          <el-input v-model.trim="keyWorkds" placeholder="请输入关键字" maxlength="20">
            <i slot="suffix" class="el-input__icon el-icon-search" @click="searchResult('index1')"></i>
          </el-input>
        </div>
        <el-collapse v-model="activeNames">
          <el-collapse-item
            v-for="(item,index) in replyKeyWorkds.alltypes"
            :name="index"
            :key="index"
          >
            <template slot="title">
              <div class="folding">
                <span>{{ item.name }}</span>
              </div>
            </template>
            <div
              v-for="(info,sIndex) in replyKeyWorkds.allinfos"
              v-if="item.id==info.typeid"
              :key="sIndex"
              :class="sIndex==activeIndex?'common-list-hover':null"
              class="text item common-list box row-start column-center"
              @click="activeIndex=sIndex;handleListClick(info)"
            >
              <span></span>
              <span>{{ info.question }}</span>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-tab-pane>
      <el-tab-pane label="常用图片" name="index2">
        <div class="common-search" @keyup.enter="searchResult('index2')">
          <el-input v-model.trim="keyWorkds" placeholder="请输入关键字" maxlength="20">
            <i slot="suffix" class="el-input__icon el-icon-search" @click="searchResult('index2')"></i>
          </el-input>
        </div>
        <ul class="pic-container">
          <li
            v-for="(item,index) in commonPic "
            :key="index"
            class="common-pic"
            @click="handleListClick(item)"
          >
            <div>
              <img :src="item.smallurl" alt />
            </div>
            <el-tooltip :content="item.name" placement="top">
              <p>{{ getSubStr(item.name) }}</p>
            </el-tooltip>
          </li>
        </ul>
      </el-tab-pane>
      <el-tab-pane label="常用链接" name="index3">
        <div class="common-search" @keyup.enter="searchResult('index3')">
          <el-input v-model.trim="keyWorkds" placeholder="请输入关键字" maxlength="20">
            <i slot="suffix" class="el-input__icon el-icon-search" @click="searchResult('index3')"></i>
          </el-input>
        </div>
        <ul class="common-link">
          <li v-for="(item,index) in commonLink " :key="index" @click="handleListClick(item)">
            <p class="box row-start column-center ellipsis">
              <span></span>
              <span class="ellipsis">{{ item.title }}</span>
            </p>
            <p>{{ item.link }}</p>
          </li>
        </ul>
      </el-tab-pane>
      <el-tab-pane label="FAQ" name="index4">
        <div class="common-search" @keyup.enter="searchResult('index4')">
          <el-input v-model.trim="keyWorkds" placeholder="请输入关键字" maxlength="20">
            <i slot="suffix" class="el-input__icon el-icon-search" @click="searchResult('index4')"></i>
          </el-input>
        </div>
        <el-collapse v-model="FaqAcitve">
          <el-collapse-item v-for="(item,index) in FAQs" :name="index" :key="index">
            <template slot="title">
              <div class="folding">
                <span>{{ item.name }}</span>
              </div>
            </template>
            <div
              v-for="(info,sIndex) in item.fas"
              :key="sIndex"
              :class="sIndex==activeIndex?'common-list-hover':null"
              class="text item common-list box row-start column-center"
              @click="activeIndex=sIndex;handleListClick(info)"
            >
              <span></span>
              <span class="ellipsis">{{ info.question }}</span>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {
  getReplyKeyWords,
  getCommonPic,
  getCommonLink,
  getFAQ
} from '@/api/im_view/commonUse';
export default {
  name: 'CommonUse',
  data() {
    return {
      activeName: 'index1',
      cloneReplyKeyWorkds: {},
      replyKeyWorkds: {
        allinfos: [],
        alltypes: []
      },
      cloneCommonPic: [],
      commonPic: [],
      commonLink: [],
      cloneCommonLink: [],
      FAQs: [],
      keyWorkds: '',
      activeNames: [],
      FaqAcitve: [],
      cloneFAQs: [],
      activeIndex: null
    };
  },
  watch: {
    activeName: function(newVal, oldVal) {
      // 切换标题调用接口
      switch (newVal) {
        case 'index1':
          this.getReplyKeyWords();
          break;
        case 'index2':
          this.getCommonPic();
          break;
        case 'index3':
          this.getCommonLink();
          break;
        case 'index4':
          this.getFAQ();
          break;
      }
    }
  },
  mounted() {
    this.getReplyKeyWords();
  },
  methods: {
    getSubStr(str) {
      if (!str || str.length <= 8) {
        return str;
      }
      let strarr = str.split('.');
      var subStr1 = strarr[0].substr(0, 2);
      var subStr2 = strarr[0].substr(strarr[0].length - 2, 2);
      var subStr = subStr1 + '...' + subStr2 + '.' + strarr[1];
      return subStr;
    },
    handleClick() {
      this.keyWorkds = '';
    },
    searchResult(name) {
      switch (name) {
        case 'index1':
          if (!this.keyWorkds) {
            this.replyKeyWorkds = JSON.parse(
              JSON.stringify(this.cloneReplyKeyWorkds)
            );
            this.activeNames.splice(0, this.activeNames.length);
            return;
          }
          var arr = [];
          this.replyKeyWorkds.allinfos.splice(
            0,
            this.replyKeyWorkds.allinfos.length
          );
          this.replyKeyWorkds.alltypes.splice(
            0,
            this.replyKeyWorkds.alltypes.length
          );
          this.cloneReplyKeyWorkds.allinfos.forEach(item => {
            if (item.question.indexOf(this.keyWorkds) >= 0) {
              this.replyKeyWorkds.allinfos.push(item);
              if (arr.indexOf(item.typeid) < 0) {
                arr.push(item.typeid);
                this.cloneReplyKeyWorkds.alltypes.forEach(typeItem => {
                  if (item.typeid == typeItem.id) {
                    this.replyKeyWorkds.alltypes.push(typeItem);
                    this.activeNames.push(
                      this.replyKeyWorkds.alltypes.length - 1
                    );
                  }
                });
              }
            }
          });
          break;
        case 'index2':
          this.commonPic = this.cloneCommonPic.filter(item => {
            return item.name.indexOf(this.keyWorkds) >= 0;
          });
          break;
        case 'index3':
          this.commonLink = this.cloneCommonLink.filter(item => {
            return item.title.indexOf(this.keyWorkds) >= 0;
          });
          break;
        case 'index4':
          if (!this.keyWorkds) {
            this.FAQs = JSON.parse(JSON.stringify(this.cloneFAQs));
            this.FaqAcitve.splice(0, this.activeNames.length);
            return;
          }
          this.FAQs = this.cloneFAQs.filter(item => {
            if (item.fas.length > 0) {
              var arr = [];
              item.fas.forEach(info => {
                if (info.question.indexOf(this.keyWorkds) >= 0) {
                  arr.push(info);
                }
              });
              if (arr.length > 0) {
                item.fas = arr;
                return item;
              }
            }
          });
          this.FaqAcitve.push(this.FAQs.length - 1);
          break;
      }
    },
    getReplyKeyWords() {
      getReplyKeyWords().then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.cloneReplyKeyWorkds = JSON.parse(JSON.stringify(res.data));
        this.replyKeyWorkds = JSON.parse(JSON.stringify(res.data));
      });
    },
    getCommonPic() {
      getCommonPic().then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.commonPic = JSON.parse(JSON.stringify(res.data));
        this.cloneCommonPic = JSON.parse(JSON.stringify(res.data));
      });
    },
    getCommonLink() {
      getCommonLink().then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.commonLink = JSON.parse(JSON.stringify(res.data));
        this.cloneCommonLink = JSON.parse(JSON.stringify(res.data));
      });
    },
    getFAQ() {
      getFAQ().then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.warning(res.msg);
          return;
        }
        this.cloneFAQs = JSON.parse(JSON.stringify(res.data));
        this.FAQs = JSON.parse(JSON.stringify(res.data));
      });
    },
    handleListClick(item) {
      this.emits(this, 'send', 'kuaijiehuifu_kfim', item);
      // this.$store.commit('imProperty/SET_RETURN_RESULT', item);
    }
  }
};
</script>

<style scoped lang="scss">
@mixin dot {
  display: inline-block;
  width: 5px;
  height: 5px;
  margin-left: 7px;
  margin-right: 7px;
  border-radius: 100%;
  background: rgba(220, 222, 227, 1);
}
/deep/.el-tabs__content {
  overflow-y: hidden !important;
}
.endSession {
  text-align: center;
  position: relative;
  &:after {
    content: '';
    width: 52px;
    height: 1px;
    border-bottom: 1px solid #ccc;
    display: inline-block;
    position: absolute;
    top: 10px;
    right: 29px;
  }
  &:before {
    content: '';
    width: 52px;
    height: 1px;
    border-bottom: 1px solid #ccc;
    display: inline-block;
    position: absolute;
    top: 11px;
    left: 29px;
  }
}
/deep/ .page-header {
  display: none;
}
/deep/ .el-collapse-item__content {
  padding-bottom: 0;
}
/deep/ .page .page-body {
  padding-left: 0;
  padding-right: 0;
}
.customerService {
  color: rgba(59, 149, 168, 1);
}
.visitors {
  color: rgba(230, 162, 60, 1);
}
/deep/ .el-collapse-item__header {
  position: relative;
  /*height: 36px;*/
  min-height: 36px;
  line-height: 36px;
  .folding {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding-left: 20px;
    background: #fafbfb;
    border: 1px solid #f2f4f5;
    padding: 0 15px;
    padding-left: 28px;
  }
  .el-collapse-item__arrow {
    position: absolute;
    left: 10px;
  }
}
.common-use {
  .el-tabs {
    /deep/.el-tabs__item {
      color: #909399 !important;
      background: none !important;
      border-bottom: 2px solid #e4e7ed;
    }
  }
  /deep/.el-tabs__item.is-active {
    color: #3b95a8 !important;
    border-bottom: 2px solid #3b95a8 !important;
  }
  /deep/.el-tabs__content {
    border: none !important;
    padding: 0 !important;
  }
  .common-search {
    margin-top: 16px;
    margin-bottom: 16px;
  }
  /deep/.el-collapse-item__arrow {
    color: #909399;
    font-szie: 8px;
  }
  .folding span {
    color: #575766;
    font-size: 14px;
  }
  .common-list {
    min-height: 36px;
    /*height:36px;*/
    color: #292933;
    font-size: 14px;
    padding-left: 10px;
    span:first-child {
      @include dot;
    }
    &:hover {
      background: rgba(218, 233, 236, 1) !important;
    }
  }
  .common-list-hover {
    background: rgba(218, 233, 236, 1) !important;
  }
  .pic-container {
    margin: 0;
    padding: 0;
    overflow: hidden;
    .common-pic {
      margin-right: 18px;
      margin-bottom: 20px;
      float: left;
      div {
        width: 74px;
        height: 74px;
        border: 1px solid rgba(228, 228, 235, 1);
        img {
          width: 100%;
          height: 100%;
        }
      }
      p {
        margin: 0;
        margin-top: 4px;
        color: #575766;
        font-size: 12px;
      }
    }
    .common-pic:nth-of-type(5n + 0) {
      margin-right: 0 !important;
    }
  }
  .common-link {
    margin: 0;
    padding: 0;
    font-size: 14px;
    li {
      margin-top: 14px;
      p {
        marin: 0;
        /*height: 20px;*/
        line-height: 20px;
        &:first-child {
          color: #292933;
          span:first-child {
            @include dot;
          }
          span:last-child {
            display: inline-block;
            max-width: calc(100% - 30px);
          }
        }
        &:last-child {
          padding-left: 20px;
          color: #3b95a8;
          margin-top: 2px;
        }
      }
    }
  }
}
</style>
