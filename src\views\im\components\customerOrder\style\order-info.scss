.sss-order-info-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  padding-bottom: 16px;
  .no-order-info {
    position: absolute;
    left: 50%;
    top: 50px;
    transform: translateX(-50%);
  }
  .order-list {
    width: 100%;
  }
}
.order-list li {
  margin-top: 10px;
  margin-bottom: 10px;
  background: rgba(255, 255, 255, 1);
  border-radius: 2px;
  border: 1px solid rgba(241, 242, 244, 1);
}

.order-list li header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 36px;
  background: rgba(241, 242, 244, 0.33);
  line-height: 36px;
  padding: 0 12px;
  span {
    font-size: 12px;
  }
  &:first-child {
    color: rgba(144, 147, 153, 1);
  }
  .status-name {
    padding: 2px 8px;
    line-height: 13px;
    height: 16px;
    border-radius: 2px;
    font-size: 12px;
  }
}

.order-list-wrapper .img-box {
  width: 100px;
  height: 100px;
  margin: 11px 20px 12px 12px;
}

.order-list-wrapper .img-box img {
  max-width: 100%;
}

.order-list-wrapper .detail {
  width: calc(100% - 132px);
}

.order-list-wrapper .detail header {
  padding-left: 0;
  padding-right: 12px;
  background: #fff;
}

.order-list-wrapper .detail header h2 {
  width: 70%;
  height: 36px;
  line-height: 36px;
  max-width: 270px;
  font-size: 14px;
  margin: 0;
  padding: 0;
}
.order-list-wrapper .detail time {
  display: block;
  height: 17px;
  font-size: 12px;
  color: rgba(144, 147, 153, 1);
  line-height: 17px;
  margin-top: 7px;
}

.order-list-wrapper .detail p {
  margin-top: 14px;
  margin-right: 0;
  height: 22px;
  line-height: 22px;
  color: rgba(87, 87, 102, 1);
}

.order-list-wrapper .detail p span:first-child {
  margin-right: 10px;
}

.order-list-wrapper .detail p span:last-child {
  font-size: 12px;
}

.order-list-wrapper .detail p span:last-child i {
  font-size: 16px;
}
// 1、订单信息分类新增；全部、未完成、已完成、已关闭
// 其中-全部-分类下操作同原系统操作不变
// 2、新增分类下包含状态说明
// 2.1）未完成分类下包含状态为：
// 未支付、配送中、出库中、订单审核中、退款审核中
// 2.2）已完成包含状态为：
// 已完成
// 2.3）已关闭包含状态为：
// 已取消、已退款、已删除、已拆单
/* 订单状态：1-待配送，2-配送中，3-已配送，4-取消，5-已删除,6-已拆单,7-出库中,10-未支付,
90-退款审核中, 91-已退款, 20-已送达, 21-已拒签 --%> */
.status-20 {
  color: rgba(93, 177, 51, 1);
  background: rgba(230, 251, 220, 1);
}

.status-10,
.status-2,
.status-7,
.status-3 {
  color: rgba(230, 162, 60, 1);
  background: rgba(253, 246, 236, 1);
}

.status-4,
.status-91,
.status-90,
.status-5,
.status-21 {
  color: rgba(255, 48, 36, 1);
  background: rgba(255, 233, 232, 1);
}
