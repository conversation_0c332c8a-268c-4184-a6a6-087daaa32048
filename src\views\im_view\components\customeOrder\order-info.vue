<template>
  <div class="sss-order-info-wrap">
    <ul v-if="orderLists.length" class="order-list">
      <li v-for="item in orderLists" :key="item.orderNo + Date.now()">
        <header class="order-header">
          <span>
            订单编号:
            &nbsp; {{ item.orderNo }}
          </span>
          <span :class="'status-'+item.status" class="status-name">{{ item.statusName }}</span>
        </header>
        <div class="box row-start order-list-wrapper">
          <div class="row-center img-box">
            <img :src="item.imageUrl ? item.imageUrl : ''" :alt="item.merchantName" />
          </div>
          <div class="detail">
            <header class="box row-between column-start">
              <h2 class="ellipsis">
                <el-tooltip
                  :content="item.merchantName"
                  class="item"
                  effect="dark"
                  placement="top-start"
                >
                  <span style="font-size: 14px; color: #000;">{{ item.merchantName }}</span>
                </el-tooltip>
              </h2>
              <el-button
                :disabled="!item.orderNo"
                type="text"
                style="font-size: 12px;"
                @click="viewOrderDetail(item)"
              >
                查看
                <i class="el-icon-arrow-right"></i>
              </el-button>
            </header>
            <time>{{ item.createTime|formatTime }}</time>
            <p class="box row-start">
              <span>共{{ item.varietyNum }}种商品</span>
              <span>
                实付总额：¥
                <i>{{ item.money }}</i>
              </span>
            </p>
          </div>
        </div>
      </li>
    </ul>
    <div v-else class="no-order-info">
      <img src="../../../../assets/common/no_order.png" alt="暂无数据" width="220px" />
    </div>
  </div>
</template>
<script>
export default (function() {
  return {
    props: {
      orderData: {
        type: Array,
        default: () => []
      },
      radioStatus: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        orderLists: []
      };
    },
    watch: {
      orderData(newV, oldV) {
        this.orderLists = [];
        if (newV && newV.length) {
          this.orderLists = newV;
        }
      }
    },
    methods: {
      getData() {},
      viewOrderDetail(item) {
        // const url = item.orderDetailUrl
        //   ? item.orderDetailUrl
        //   : `//new-admin.test.ybm100.com/orderManagement/order/${item.orderNo}`;
        window.open(item.orderDetailUrl, '_blank');
      }
    }
  };
})();
</script>
<style lang="scss" scoped>
@import './style/order-info.scss';
</style>
