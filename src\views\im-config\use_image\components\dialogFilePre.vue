<template>
  <div class="preview-box">
    <div :class="{isAllCheck:isallcheck}" class="file-preview">
      <img v-if="previewType==='image'" :src="file.url" class="imageFile" />
      <div :class="{uploading:percent !==0}" class="cover">
        <div class="tools">
          <span class="operate-tools">
            <i v-if="!editable" class="el-icon-download" title="下载" @click="download"></i>
            <i v-if="editable" class="el-icon-delete" title="删除" @click="$emit('delCallback')"></i>
          </span>
        </div>
      </div>
    </div>
    <el-input
      ref="fileName"
      v-model="file.name"
      class="file-name"
      size="small"
      @focus="handlefocus"
      @blur="handleBlur"
    />
  </div>
</template>

<script>
import { FILE_PATH } from '@/api/fields';
export default {
  props: {
    file: {
      type: Object,
      default: () => {
        return {
          uid: '', // 唯一标识
          name: '', // 文件名
          url: '', // 文件缩略图地址 或 预览地址
          path: '', // 文件真实地址
          raw: {
            type: '', // 文件mime-type
            size: 0 // 文件大小
          },
          data: '' // 后台所需数据
        };
      }
    },
    isallcheck: {
      type: Boolean,
      default: false
    },
    // 上传进度
    percent: {
      type: Number,
      default: 100
    },
    // 编辑状态
    editable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      modefiName: false,
      imageType: '',
      basePath: FILE_PATH,
      isOpen: false, // 预览状态
      // imgTop: 0, // 图片距离顶部位置
      // imgSelfTop: 0, // 图片自身Y轴偏移
      // imgRotate: 0, // 图片翻转角度
      // imgScale: 1, // 图片放大倍数
      // imgWidth: 0, // 图片显示宽度
      // imgHeight: 0, // 图片显示高度
      // pdfPath: 'https://view.officeapps.live.com/op/view.aspx?src=',
      ignore: ['zip', 'word', 'excel', 'ppt', 'avi', 'txt']
    };
  },
  computed: {
    imgStyle() {
      return {
        width: this.imgWidth + 'px',
        height: this.imgHeight + 'px',
        top: this.imgTop,
        transform: `translateY(${this.imgSelfTop}) scale(${this.imgScale}) rotate(${this.imgRotate}deg)`
      };
    },
    previewType() {
      const _type = 'image';
      return _type;
    },
    percentSize() {
      return (
        ((this.file.raw.size * this.percent) / 102400000).toFixed(2) + 'MB'
      );
    },
    previewSize() {
      return this.file.raw.size < 1000
        ? this.file.raw.size + 'B'
        : this.file.raw.size < 1024000
        ? (this.file.raw.size / 1000).toFixed(2) + 'KB'
        : (this.file.raw.size / 1024000).toFixed(2) + 'MB';
    },
    filePath() {
      return this.file.path ? this.file.path : this.file.url;
    }
  },
  methods: {
    handlefocus() {
      this.modefiName = true;

      const name = this.file.name;
      const type = name.slice(name.lastIndexOf('.'));
      console.log(type);

      this.imageType = type;
      this.file.name = name.slice(0, name.lastIndexOf('.'));
    },
    handleBlur() {
      this.modefiName = false;
      if (this.file.name == '') {
        this.$XyyMessage.error('图片名称不能为空！');
        this.file.name = this.file.name + this.imageType;
        this.$refs['fileName'].focus();
        return false;
      }
      this.file.name = this.file.name + this.imageType;
      console.log('修改后的文件', this.file);
    }
  }
};
</script>

<style lang="scss" scoped>
.preview-box {
  line-height: 0;
  float: left;
  width: 101px;
  height: 118px;
  margin-right: 10px;
  .imageFile {
    max-width: 76px;
    max-height: 76px;
  }
  .file-preview {
    width: 89px;
    height: 89px;
    padding: 8px;
    overflow: hidden;
    position: relative;
    margin: 0;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 2px;
    border: 1px solid rgba(228, 228, 235, 1);
    &.isAllCheck {
      border: 1px solid rgba(59, 149, 168, 1);
    }
    i.file-icon {
      width: 100%;
      position: relative;
      top: 50%;
      transform: translateY(-50%);
      float: left;
    }

    i.file-icon {
      display: inline-block;
      height: 100%;
      box-sizing: border-box;
    }

    .cover {
      display: none;
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: 1;
      // .el-progress {
      //   width: 100%;
      //   /deep/.el-progress-bar__outer {
      //     background-color: #909399;
      //     .el-progress-bar__inner {
      //       background-color: #fff;
      //     }
      //   }
      //   /deep/.el-progress__text {
      //     color: #fff;
      //     transform: translateY(50%);
      //     font-size: 8px !important;
      //     font-family: PingFangSC-Regular, PingFang SC;
      //     font-weight: 400;
      //   }
      // }
      // i.percent-size {
      //   display: inline-block;
      //   height: 12.3px;
      //   font-size: 8px;
      //   font-family: PingFangSC-Regular, PingFang SC;
      //   font-weight: 400;
      //   color: rgba(255, 255, 255, 1);
      //   font-style: normal;
      //   position: absolute;
      //   top: 50%;
      //   transform: translateY(-100%);
      // }
      .el-button.file-abort {
        display: none;
        font-size: 10px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(77, 223, 254, 1);
        position: absolute;
        bottom: 7px;
        left: 50%;
        transform: translateX(-50%);
        padding: 0;
      }
      .operate-tools {
        width: 100%;
        text-align: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        i {
          color: #fff;
          font-size: 20px;
          cursor: pointer;
          margin: 0 5px;
        }
      }
      &.uploading {
        display: block;
      }
    }

    &:hover {
      .cover {
        display: block;
        .el-button.file-abort {
          display: block;
        }
      }
    }
  }
  .file-name {
    width: 99px;
    display: inline-block;
    text-align: center;
    height: 24px;
    font-size: 14px;
    font-weight: none;
    margin: 4px 0 0;
    color: rgba(87, 87, 102, 1);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    /deep/.el-input__inner {
      height: 24px !important;
      line-height: 24px !important;
    }
  }
}
.preview-box:first-child {
  margin-left: 0;
}

.preview-box:last-child::after {
  content: '';
  clear: both;
}
</style>

<style lang="scss">
.el-dialog.preview-dialog {
  height: 500px;
  top: 50%;
  transform: translateY(-50%);
  overflow: hidden;
  /deep/.el-dialog__header {
    height: 60px;
    padding: 15px 16px 20px 20px;
    border-bottom: 1px solid #dcdfe6;
    .preview-title {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(41, 41, 51, 1);
      line-height: 25px;
      height: 25px;
      span.preview-size {
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(96, 98, 102, 1);
        margin-left: 14px;
      }
      span.preview-tools {
        float: right;
        margin-right: 40px;
        border-right: 1px solid #dcdfe6;
        user-select: none;
        padding-right: 10px;
        i {
          color: #c0c4cc;
          margin: 0 10px;
          cursor: pointer;
        }
        .svg-icon {
          margin: 0 10px;
          cursor: pointer;
        }
      }
    }
  }
  /deep/ .el-dialog__body {
    height: calc(100% - 57px);
    overflow: auto;
    padding: 0;
    position: relative;
    img {
      position: relative;
    }
    video {
      width: 100%;
      height: calc(100% - 5px);
    }
    object {
      width: 100%;
      height: calc(100% - 5px);
    }
  }
}
</style>

