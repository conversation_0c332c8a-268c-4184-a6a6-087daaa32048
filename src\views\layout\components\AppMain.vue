<template>
  <section v-loading="pageLoading"
           class="app-main"
           @click="closeBox">
    <TagsView />
    <!-- {{$route.fullPath}}
    {{$route.path}} -->
    <transition name="fade-transform"
                mode="out-in">
      <keep-alive :exclude="excludeViews">
        <!-- <keep-alive> -->
        <template v-if="$route.meta.componentName">
          <router-view :key="$route.path"
                       class="page-view" />
        </template>
        <template v-else>
          <router-view :key="$route.fullPath"
                       class="page-view" />
        </template>

      </keep-alive>
    </transition>
    <!-- <transition name="fade-transform" mode="out-in">
      <keep-alive>
        <router-view v-if="$route.meta.keepAlive" :key="$route.fullPath" class="page-view" />
      </keep-alive>
      <router-view v-if="!$route.meta.keepAlive" :key="$route.fullPath" class="page-view" />
    </transition>-->
  </section>
</template>

<script>
import TagsView from './TagsView';
export default {
  name: 'AppMain',
  components: {
    TagsView,
  },
  computed: {
    pageLoading: function () {
      return this.$store.getters.pageLoading;
    },

    // cachedViews: function () {
    //   return this.$store.getters.cachedViews.join(',') + ',SheetList';
    // },

    excludeViews: function () {
      // excludeViews：关闭view记录的暂时需要清除的view，这个变量会稍后变更为空
      return this.$store.getters.excludeViews;
    },

    /* routerKey: function() {
      let t = '';
      if (
        this.$store.getters.visitedViews.every(
          route => route.fullPath !== this.$route.fullPath
        )
      ) {
        t = new Date().getTime();
      }
      return this.$route.fullPath + t;
    } 
    */
  },
  watch: {},
  mounted () { },
  methods: {
    closeBox () {
      this.$store.commit('message/SET_Message_status', 0);
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';
.app-main {
  /*50 = navbar  */
  //min-height: calc(100vh - 50px);
  height: 100%;
  position: relative;
  overflow: hidden;
  background: $appMainBg;
  padding: 0 16px 20px;
  .page-view {
    width: 100%;
    height: calc(100% - 40px);
    background: #fff;
    overflow-y: auto;
  }
  /* .page-box {
    padding: 0 20px 20px;
    width: 100%;
    height: 100%;
    .page-view {
      width: 100%;
      height: 100%;
      background: #fff;
    }
  } */
}
.hasTagsView {
  // .app-main {
  //   /* 84 = navbar + tags-view = 50 + 34 */
  //   min-height: calc(100vh - (#{$navBarHeight} + #{$TagsViewHeight}));
  // }
  .fixed-header + .app-main {
    //    padding-top: calc((#{$navBarHeight} + #{$TagsViewHeight}) - 15px);
    padding-top: 69px;
    padding-bottom: 10px;
    // margin-bottom:20px;
  }
}
</style>
