<template>
  <div>
    <div v-if="show" class="preview">
      <div class="preview_hade">
        <h3>{{ drugName }}药品召回信息登记</h3>
        <span>&emsp;{{ description }}</span>
      </div>
      <el-form ref="from" :model="form" :rules="rules" class="link-boxxiao">
        <el-form-item label="1.药店名称" prop="name">
          <el-input v-model.trim="form.name" placeholder="请输入药店名称" type="text" />
        </el-form-item>
        <el-form-item label="2.注册手机号码" prop="phone">
          <el-input
            v-model="form.phone"
            placeholder="请输入手机号码"
            onkeyup="this.value=this.value.replace(/\D/g, '')"
          />
        </el-form-item>
        <el-form-item label prop="num">
          <span slot="label" style="color: red; margin-right: 4px">*</span>
          <label slot="label">3.批号（只填写剩余库存）</label>
          <div v-for="(item,index) in list" :key="index" class="pihao_num">
            <span>{{ item.batchNumber }}</span>
            <el-input
              v-model.trim="item.batchNumberTotal"
              placeholder="请输入数量"
              style="width: 68%;float: right;"
              onkeyup="this.value=this.value.replace(/\D/g, '')"
            />
          </div>
        </el-form-item>
        <div class="radio-group-box">
          <el-form-item label="4.发票类型（纸质发票必须随货退回，否则会影响您的退款）" prop="radio">
            <el-radio v-model="radio" label="10" class="danxaun">电子普票</el-radio>
            <el-radio v-model="radio" label="20" class="danxaun">纸质普票</el-radio>
            <el-radio v-model="radio" label="30" class="danxaun">专票</el-radio>
          </el-form-item>
        </div>
        <el-form-item label="5.邮寄快递" prop="express">
          <el-input v-model.trim="form.express" placeholder="请输入邮寄快递" />
        </el-form-item>
        <el-form-item label="6.物流单号" prop="logistics">
          <el-input v-model.trim="form.logistics" placeholder="请输入物流单号" />
        </el-form-item>
        <el-form-item label=" ">
          <el-button type="primary" style="width: 100%;" @click="checkTimer(save,'timer')()">提交</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { getPreview, setPreview } from '@/api/shop';
import axios from 'axios';
export default {
  name: 'Index',
  components: {},

  data() {
    var isMobileNumber = (rule, value, callback) => {
      if (!value) {
        return new Error('请输入电话号码');
      } else {
        const reg = /^1[3|4|5|7|8|9][0-9]\d{8}$/;
        const isPhone = reg.test(value);
        value = Number(value); // 转换为数字
        if (typeof value === 'number' && !isNaN(value)) {
          // 判断是否为数字
          value = value.toString(); // 转换成字符串
          if (value.length < 0 || value.length > 12 || !isPhone) {
            // 判断是否为11位手机号
            callback(new Error('请输入正确手机号码格式'));
          } else {
            callback();
          }
        } else {
          callback(new Error('请输入电话号码'));
        }
      }
    };
    var isTelOrFax = (rule, value, callback) => {
      if (!value) {
        return new Error('名称不能为空');
      } else if (value == null || value == undefined || value == ' ') {
        callback(new Error('名称不能为空'));
      } else if (value.length > 51) {
        callback(new Error('不能超过50个字符'));
      }
    };

    var isTelOrF = (rule, value, callback) => {
      if (!value) {
        return new Error('批号不能为空');
      } else if (value == null || value == undefined || value == ' ') {
        callback(new Error('批号不能为空'));
      } else if (value.length > 51) {
        callback(new Error('不能超过50个字符'));
      }
    };
    var isTelOr = (rule, value, callback) => {
      if (!value) {
        return new Error('邮寄快递不能为空');
      } else if (value == null || value == undefined || value == ' ') {
        callback(new Error('邮寄快递不能为空'));
      } else if (value.length > 51) {
        callback(new Error('不能超过50个字符'));
      }
    };
    var isTel = (rule, value, callback) => {
      if (!value) {
        return new Error('物流单号不能为空');
      } else if (value == null || value == undefined || value == ' ') {
        callback(new Error('物流单号不能为空'));
      } else if (value.length > 51) {
        callback(new Error('不能超过50个字符'));
      }
    };
    return {
      form: {
        name: '',
        phone: '',
        express: '',
        logistics: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          {
            validator: isTelOrFax,
            trigger: 'blur'
          }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { validator: isMobileNumber, trigger: 'blur' }
        ],

        // num: [
        //   { required: true, message: '请输入数量' },
        //   { validator: isTelOrF, trigger: 'blur' }
        // ],
        radio: [{ required: true, trigger: 'blur' }],
        express: [
          { required: true, message: '请输入邮寄快递', trigger: 'blur' },
          { validator: isTelOr, trigger: 'blur' }
        ],
        logistics: [
          { required: true, message: '请输入物流单号', trigger: 'blur' },
          { validator: isTel, trigger: 'blur' }
        ]
      },
      list: [
        // { batchNumber: 1, batchNumberTotal: '' },
        // { batchNumber: 2, batchNumberTotal: '' }
      ],
      radio: '',
      drugName: '',
      description: '',
      batchNumberTotal: '',
      timer: '',
      show: true,
      demo: '',
      value: '',
      recallId: '',
      businessPartCode: '',
      urlOrigin: ''
    };
  },
  created: function() {
    document.title = '药品召回信息登记';
    document.head
      .querySelector('[name=viewport]')
      .setAttribute(
        'content',
        'width=device-width,initial-scale=1,user-scalable=no'
      );

    console.log(process.env.NODE_ENV);
    switch (process.env.NODE_ENV) {
      case 'production':
        this.urlOrigin = '//saoma.ybm100.com';
        break;
      default:
        this.urlOrigin = window.location.origin;
        break;
    }
  },
  mounted() {
    // const recallId = {
    //   recallId: this.recallId
    // };
    this.recallId = this.$route.query.id;
    this.businessPartCode = this.$route.query.businessPartCode || '';
    // var api =
    //   '//ec-service.stage.ybm100.com/workOrderRecall/getRecallById?recallId=' +
    //   this.recallId;
    // var api =
    //   '//saoma.ybm100.com/workOrderRecall/getRecallById?recallId=' +
    //   this.recallId;

    let api = `${this.urlOrigin}/workOrderRecall/getRecallById?recallId=${this.recallId}`;

    axios({
      url: api,
      method: 'get',
      headers: {
        businessPartCode: this.businessPartCode
      }
    }).then(res => {
      console.log(res.data);
      if (res.data.code == 0) {
        this.$XyyMessage.error(res.data.msg);
        this.show = false;
      } else if (res.data.code == 1) {
        const goods = res.data.data;
        this.drugName = goods.drugName;
        this.description = goods.description;
        this.description = goods.description;
        this.list = goods.batchInfos;
      }
    });

    // setPreview(recallId).then(res => {
    //   console.log(res.data);
    //   const goods = res.data;
    //   this.drugName = goods.drugName;
    //   this.description = goods.description;
    //   this.list = goods.batchInfos;
    // });
  },
  methods: {
    save() {
      let flag = false;
      if (this.form.name === '') {
        this.$message.error('请填写名称');
        return;
      }

      if (this.form.phone === '') {
        this.$message.error('请填写手机号');
        if (/^1[345789]\d{9}$/.test(Number(this.form.phone))) {
          this.$message.error('手机号码有误，请重填');
          return false;
        }
        return;
      }
      this.list.forEach(item => {
        flag = !item.batchNumberTotal;
      });
      if (flag) return this.$message.error('请填写批号');
      if (this.radio === '') {
        this.$message.error('请选择发票类型');
        return;
      }
      if (this.form.express === '') {
        this.$message.error('请填写邮寄快递');
        return;
      }
      if (this.form.logistics === '') {
        this.$message.error('请填写物流单号');
        return;
      }
      const param = {
        pharmacyName: this.form.name,
        phone: this.form.phone,
        batchInfos: this.list,
        invoiceType: this.radio,
        mailDelivery: this.form.express,
        logisticsNumber: this.form.logistics,
        csRecallId: this.recallId,
        drugName: this.drugName
      };
      var paramil = JSON.stringify(param);
      console.log(paramil);
      // getPreview(paramil).then(res => {
      //   console.log('124');
      //   console.log(res);
      // });

      axios({
        //url: '//ec-service.stage.ybm100.com/recallInfos/submit',
        //url: '//saoma.ybm100.com/recallInfos/submit',
        url: `${this.urlOrigin}/recallInfos/submit`,
        method: 'post',
        data: paramil,
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          businessPartCode: this.businessPartCode
        }
      }).then(res => {
        console.log(res);
        if (res.data.code === 0) {
          this.$XyyMessage.error(res.data.msg);
        } else if (res.data.code === 1) {
          // this.$XyyMessage.success('提交成功');
          this.$alert('提交成功', '提示', {
            confirmButtonText: '确定',
            callback: action => {
              location.reload();
            }
          });
        }
      });
    }
  }
};
</script>

<style lang="scss" >
.preview_hade {
  width: 96%;
  margin: auto;
}
.link-boxxiao {
  width: 96%;
  margin: auto;
}
.pihao_num {
  display: inline-block;
  width: 100%;
  line-height: 48px;
}
.pihao_num span {
  width: 26%;
  display: inline-block;
}
.radio-group-box {
  height: 195px;
  /deep/ label {
    width: 100%;
  }
}
.danxaun {
  // display: table-caption;
  height: 30px;
  line-height: 30px;
  display: initial;
}
.el-form-item__label {
  text-align: -webkit-auto;
}
.radio-group-box {
  height: 130px;
}
.el-message-box__wrapper {
  top: 0;
  left: 0;
}
.el-message-box {
  width: 500px;
}
.im-confirm-msg-box {
  width: 450px;
  .cancel-button-class {
    float: right;
    margin-left: 10px;
    color: #fff;
    background-color: #3b95a8;
    border-color: #3b95a8;
  }
  /deep/ .el-button--primary {
    color: #606266;
    background-color: transparent;
    border-color: #dcdfe6;
    &:hover {
      color: #3b95a8;
      border-color: #c4dfe5;
      background-color: #ebf4f6;
    }
  }
}
</style>
