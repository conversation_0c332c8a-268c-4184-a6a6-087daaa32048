import Layout from '@/views/layout/Layout';
export default [
  {
    path: '/seesion_config',
    component: Layout,
    meta: {
      title: '会话分配',
      mark: 'config',
      code: 'menu:csim:assign'
    },
    children: [
      {
        path: 'list',
        name: 'list',
        meta: {
          title: '会话分配',
          code: 'menu:csim:assign',
          mark: 'config'
        },
        component: () => import('@/views/configuration/seesion_config')
      }
    ]
  },
  {
    path: '/PersonnelManage',
    component: Layout,
    meta: {
      title: '人员管理',
      code: 'menu:csim:staff',
      mark: 'config'
    },
    children: [
      {
        path: 'list',
        name: 'list',
        meta: {
          title: '人员管理',
          code: 'menu:csim:staff',
          mark: 'config'
        },
        component: () => import('@/views/configuration/PersonnelManage')
      },
      {
        path: 'addPersonnel',
        name: 'addPersonnel',
        meta: {
          title: '添加人员',
          code: 'menu:csim:staff',
          mark: 'config'
        },
        component: () => import('@/views/configuration/addPersonnel'),
        hidden: true
      }
    ]
  },
  {
    path: '/employeeList',
    component: Layout,
    name: 'employeeList',
    redirect: 'employeeList/list',
    meta: {
      affix: true,
      code: 'menu:csim:staffgroup',
      mark: 'config'
    },
    children: [
      {
        path: 'list',
        name: 'list',
        meta: {
          title: '员工组',
          code: 'menu:csim:staffgroup',
          mark: 'config'
        },
        component: () => import('@/views/im-config/employees/list')
      },
      {
        path: 'employeeEdit/:id',
        name: 'employeeEdit',
        meta: {
          title: '新建员工组',
          code: 'menu:csim:staffgroup',
          mark: 'config'
        },
        component: () => import('@/views/im-config/employees/edit'),
        hidden: true
      }
    ]
  },
  {
    path: '/localSetUp',
    component: Layout,
    meta: {
      title: '地域组管理',
      code: 'menu:csim:areagroup',
      mark: 'config'
    },
    children: [
      {
        path: 'localSetUpNew',
        name: 'localSetUpNew',
        meta: {
          title: '地域组设置',
          code: 'menu:csim:areagroup',
          mark: 'config'
        },
        component: () => import('@/views/configuration/localSetUpNew'),
        hidden: true
      },
      {
        path: 'list',
        name: 'list',
        meta: {
          title: '区域设置',
          code: 'menu:csim:areagroup',
          mark: 'config'
        },
        component: () => import('@/views/configuration/localSetUp')
      }
    ]
  },
  {
    path: '/regionalMenu',
    component: Layout,
    meta: {
      title: '微信地域菜单',
      code: 'menu:csim:areamenu',
      mark: 'config'
    },
    children: [
      {
        path: 'list',
        name: 'list',
        meta: {
          title: '微信地域菜单',
          mark: 'config'
        },
        component: () => import('@/views/configuration/regionalMenu')
      }
    ]
  },
  {
    path: '/personalSetting',
    component: Layout,
    name: 'personalSetting',
    redirect: 'personalSetting/list',
    meta: {
      affix: true,
      code: 'menu:csim:setmyinfo',
      mark: 'config'
    },
    children: [
      {
        path: 'list',
        name: 'list',
        meta: {
          title: '个人设置',
          code: 'menu:csim:setmyinfo',
          mark: 'config'
        },
        component: () => import('@/views/im-config/personal-setting/edit')
      }
    ]
  },
  {
    path: '/WeLanguage',
    component: Layout,
    meta: {
      title: '欢迎语',
      code: 'menu:csim:sethi',
      mark: 'config'
    },
    children: [
      {
        path: 'list',
        name: 'list',
        meta: {
          title: '欢迎语',
          code: 'menu:csim:sethi',
          mark: 'config'
        },
        component: () => import('@/views/configuration/WeLanguage')
      }
    ]
  },
  {
    path: '/auto_news',
    component: Layout,
    meta: {
      title: '自动消息配置',
      code: 'menu:csim:autoreply',
      mark: 'config'
    },
    children: [
      {
        path: 'list',
        name: 'autoNews',
        meta: {
          title: '自动消息配置',
          code: 'menu:csim:autoreply',
          mark: 'config'
        },
        component: () => import('@/views/configuration/auto_news')
      }
    ]
  },

  {
    path: '/DisableWord',
    component: Layout,
    meta: {
      title: '禁用词',
      code: 'menu:csim:forbiddenwords',
      mark: 'config'
    },
    children: [
      {
        path: 'list',
        name: 'list',
        meta: {
          title: '禁用语',
          code: 'menu:csim:forbiddenwords',
          mark: 'config'
        },
        component: () => import('@/views/configuration/DisableWord')
      }
    ]
  },
  {
    path: '/quickReply',
    component: Layout,
    name: 'quickReply',
    redirect: 'quickReply/list',
    meta: {
      affix: true,
      code: 'menu:csim:quickreply',
      mark: 'config'
    },
    children: [
      {
        path: 'list',
        name: 'list',
        meta: {
          title: '快捷回复',
          code: 'menu:csim:quickreply',
          mark: 'config'
        },
        component: () => import('@/views/im-config/quick-reply/list')
      }
    ]
  },
  {
    path: '/image',
    component: Layout,
    name: 'image',
    redirect: 'image/list',
    meta: {
      affix: true,
      code: 'menu:csim:picture',
      mark: 'config'
    },
    children: [
      {
        path: 'list',
        name: 'list',
        meta: {
          title: '常用图片',
          code: 'menu:csim:picture',
          mark: 'config'
        },
        component: () => import('@/views/im-config/use_image/list')
      }
    ]
  },
  {
    path: '/link',
    component: Layout,
    name: 'link',
    redirect: 'link/list',
    meta: {
      affix: true,
      code: 'menu:csim:link',
      mark: 'config'
    },
    children: [
      {
        path: 'list',
        name: 'list',
        meta: {
          title: '常用链接',
          code: 'menu:csim:link',
          mark: 'config'
        },
        component: () => import('@/views/im-config/link/list')
      },
      {
        path: 'linkEdit/:id',
        name: 'linkEdit',
        meta: {
          title: '新建链接',
          code: 'menu:csim:link',
          mark: 'config'
        },
        component: () => import('@/views/im-config/link/edit'),
        hidden: true
      }
    ]
  },

  {
    path: '/faq',
    component: Layout,
    name: 'faq',
    redirect: 'faq/list',
    meta: {
      affix: true,
      code: 'menu:csim:faq',
      mark: 'config'
    },
    children: [
      {
        path: 'list',
        name: 'list',
        meta: {
          title: 'FAQ',
          code: 'menu:csim:faq',
          mark: 'config'
        },
        component: () => import('@/views/im-config/faq/list')
      }
    ]
  },
  {
    path: '/BlacklistManage',
    component: Layout,
    meta: {
      title: '黑名单管理',
      code: 'menu:csim:blacklist',
      mark: 'config'
    },
    children: [
      {
        path: 'list',
        name: 'BlacklistManage',
        meta: {
          title: '黑名单管理',
          code: 'menu:csim:blacklist',
          mark: 'config'
        },
        component: () => import('@/views/configuration/BlacklistManage')
      }
    ]
  },
  {
    path: '/serviceConfig',
    component: Layout,
    meta: {
      title: '服务总结',
      code: 'menu:csim:servicesummary',
      mark: 'config'
    },
    children: [
      {
        path: 'list',
        name: 'list',
        meta: {
          title: '服务总结',
          code: 'menu:csim:servicesummary',
          mark: 'config'
        },
        component: () => import('@/views/configuration/serviceConfig')
      }
    ]
  },
  {
    path: '/visitorNavigation',
    component: Layout,
    meta: {
      title: '访客导航菜单',
      code: 'menu:csim:areamenu',
      mark: 'config'
    },
    children: [
      {
        path: 'list',
        name: 'list',
        meta: {
          title: '访客导航菜单',
          code: 'menu:csim:areamenu',
          mark: 'config'
        },
        component: () => import('@/views/configuration/visitorNavigation')
      }
    ]
  },

  {
    path: '/commentDistrbiute',
    component: Layout,
    name: 'commentDistrbiute',
    redirect: 'commentDistrbiute/list',
    meta: {
      affix: true,
      code: 'menu:csim:assignleavemsg',
      mark: 'config'
    },
    children: [
      {
        path: 'list',
        name: 'list',
        meta: {
          title: '留言分发',
          code: 'menu:csim:assignleavemsg',
          mark: 'config'
        },
        component: () => import('@/views/im-config/comment/distribute')
      }
    ]
  }

];
