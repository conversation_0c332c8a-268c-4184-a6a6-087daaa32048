<template>
  <div class="left-chatlist-container">
    <el-card class="box-card" shadow="never" style="width: 100%;min-height:600px">
      <template v-if="innerChatList.length">
        <template v-for="item in innerChatList">
          <!-- chat-online -->
          <!-- 选中样式:is-active -->
          <div
            :class="[
            {'chat-online':true},
            {'is-active':item.assignChatItemSelected}
          ]"
            :key="item.currentVersion"
            class="chat-item"
            @click="onClickChatItem(item)"
          >
            <div class="chat-item-right">
              <div class="chat-item-right__1">
                <span class="chat-item-right__1__name">V{{ item.currentVersion }}</span>
                <span class="chat-item-right__1__time">
                  {{
                  item.modifyTime | dateFormat
                  }}
                </span>
              </div>

              <div class="chat-item-right__2">
                <span
                  :style="{ width:`calc(100% - ${ item.viewCount?14:0 }px)` }"
                  class="chat-item-right__2__lastmsg"
                >{{ item.modifyName }}</span>
              </div>
            </div>
          </div>
        </template>
      </template>
      <template v-else>
        <div class="empty-wrap">暂无数据</div>
      </template>
    </el-card>
  </div>
</template>

<script>
import utils from '@/utils/filter';
export default {
  name: 'LeftChatlist',
  components: {},
  filters: {
    dateFormat(val) {
      return utils.dataTime(val, 'yy-mm-dd HH:ss:nn');
    }
  },
  props: {},
  data() {
    return {
      innerChatList: [],
      isCheck: true
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 点击某个会话
    onClickChatItem(chat) {
      this.$emit('callback', chat);
    },

    //refs方式更新数据
    refUpdateChatList(chatlist) {
      this.innerChatList = Array.from(chatlist);
      // if (this.isCheck) {
      //   this.innerChatList[0].assignChatItemSelected = true;
      //   this.isCheck = false;
      // }
      this.$forceUpdate();
    }
  }
};
</script>

<style lang="scss" scoped>
.left-chatlist-container {
  .box-card {
    /deep/.el-card__body {
      padding: 0px;
    }
  }
  .chat-item {
    width: 100%;
    // padding: 15px 5px 0;
    padding: 16px 16px 0;
    cursor: pointer;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    position: relative;

    &::after {
      content: '';
      width: 100%;
      height: 1px;
      background-color: #e4e4eb;
      position: absolute;
      bottom: 0;
      left: 16px;
    }

    //选中样式
    &.is-active {
      background-color: #ebf4f6;
    }

    //在线样式
    &.chat-online {
      .chat-item-left__badge {
        display: inline-block;
        background-color: #67c23a;
        border: 1px solid #fff;
      }

      &.is-active {
        background-color: #ebf4f6;
      }
    }

    .chat-item-right {
      flex-grow: 1;
      width: calc(100% - 36px - 8px);

      .chat-item-right__1 {
        width: 100%;
        font-size: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .chat-item-right__1__name {
          width: 40%;
          font-size: 13px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          color: #292933;
          font-weight: 500;
        }

        .chat-item-right__1__time {
          width: 60%;
          font-size: 12px;
          text-align: right;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          color: #909399;
          font-weight: 400;
        }
      }

      .chat-item-right__2 {
        width: 100%;
        font-size: 0;
        margin: 8px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .chat-item-right__2__lastmsg {
          width: calc(100% - 14px);
          font-size: 12px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          color: #909398;
          font-weight: 400;
        }

        .chat-item-right__2__unreadnum {
          width: 14px;
          height: 14px;
          line-height: 14px;
          font-size: 12px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          color: #ffffff;
          text-align: center;
          background-color: #ff3024;
          border-radius: 50%;
          font-weight: 400;
        }

        .chat-item-right__2__unreadnum__dot {
          width: 8px;
          height: 8px;
          background-color: #ff3024;
          border-radius: 50%;
          margin-right: 4px;
        }
      }
    }
  }

  .empty-wrap {
    width: 100%;
    line-height: 60px;
    text-align: center;
    font-size: 14px;
    color: #909399;
  }
}
</style>
