<template>
  <div class="comp-info-CRM-component-container">
    <div class="crm-saler-wrapper">
      <!-- 销售简介 -->
      <template v-if="isShowCRMSalerCustomerDetail==='0'">
        <header class="header-title">销售信息</header>
        <div class="saler-info-section">
          <div class="saler-info__item">
            <span class="saler-info__item__field">销售姓名：</span>
            <span class="saler-info__item__value">{{ crmSalerInfo.realName }}</span>
          </div>
          <div class="saler-info__item">
            <span class="saler-info__item__field">岗位名称：</span>
            <span class="saler-info__item__value">{{ crmSalerInfo.dptName }}</span>
          </div>
          <div class="saler-info__item">
            <span class="saler-info__item__field">工号：</span>
            <span class="saler-info__item__value">{{ crmSalerInfo.staffNum }}</span>
          </div>
          <div class="saler-info__item">
            <span class="saler-info__item__field">ID：</span>
            <span class="saler-info__item__value">{{ crmSalerInfo.oaId }}</span>
          </div>
        </div>

        <!-- 列表和详情切换 -->
        <div class="saler-customer-list">
          <header class="customer-list__header">当前销售私海客户表</header>
          <div class="customer-list__search">
            <el-input v-model.trim="crmShopNameModel" clearable size="medium" placeholder="请输入"></el-input>
            <el-button size="medium" type="primary" @click="handleSearchPrivateCustomerBtnClick">搜索</el-button>
          </div>
          <div class="custom-list__content">
            <template v-if="crmSalerPrivateCustomerList.length">
              <div class="custom-list__content__wrapper">
                <div
                  class="custom-list__content__item"
                  v-for="customer in crmSalerPrivateCustomerList"
                  :key="customer.id"
                  @click="queryCRMCustomerDetail(customer)"
                >{{ customer.customerName }}</div>
              </div>
              <el-pagination
                background
                layout="prev, pager, next"
                :pager-count="5"
                :current-page="crmSalerPrivateCustomerList_pageNum"
                :total="crmSalerPrivateCustomerList_total"
                @current-change="crmSalerPrivateCustomerListPageChange"
              ></el-pagination>
            </template>
            <template v-else>
              <div class="content-empty">
                <img src="../../../../assets/common/no_custome_info.png" alt="暂无客户信息" />
              </div>
            </template>
          </div>
        </div>
      </template>
      <template v-else-if="isShowCRMSalerCustomerDetail==='1'">
        <div class="saler-customer-detail">
          <header class="header-title">
            <el-link icon="el-icon-arrow-left" :underline="false" @click="backToCustomerList">返回</el-link>
            <el-divider direction="vertical"></el-divider>
            <span>基本信息</span>
          </header>
          <comp-base-info-detail-ybm :customer="crmSalerCustomerInfo" :show-bind="false"></comp-base-info-detail-ybm>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import compBaseInfoDetailYbm from './compBaseInfoDetailYBM';
import {
  getCrmSaleInfo,
  privateCustomerQuery,
  getCustomeDetails,
} from '@/api/im_view/customeInfo';
export default {
  name: 'compInfoCRM',
  components: {
    compBaseInfoDetailYbm,
  },
  filters: {},
  props: {
    prop: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  data() {
    return {
      crmSalerInfo: {}, //豆芽销售信息
      crmShopNameModel: '', //豆芽搜索药店名
      crmSalerPrivateCustomerList_pageNum: 1,
      crmSalerPrivateCustomerList_pageSize: 10,
      crmSalerPrivateCustomerList_total: 0,
      crmSalerPrivateCustomerList: [], //豆芽销售关联的所有药店
      isShowCRMSalerCustomerDetail: '0', //是否展示销售关联药店的详情,0:列表,1:详情
      crmSalerCustomerInfo: {}, //销售关联药店的详情
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    /**
     * 重置组件
     */
    reset() {
      Object.assign(this.$data, this.$options.data()); // 重置data数据
    },

    /**
     * 初始化数据
     */
    init() {
      this.reset();
      this.queryCrmSalerInfo();
      this.queryCrmSalerPrivateCustomer();
    },

    /**
     * 加载豆芽客户信息
     */
    queryCrmSalerInfo() {
      getCrmSaleInfo({
        userId: this.prop.khid,
      }).then((resp) => {
        if (resp.code === 1) {
          this.crmSalerInfo = resp.data;
        } else {
          this.$XyyMessage.error(resp.msg);
        }
      });
    },

    /**
     * 加载豆芽私海用户
     */
    queryCrmSalerPrivateCustomer() {
      const loading = this.$loading({
        target: document.querySelector('.im-config'),
        lock: true,
        spinner: 'el-icon-loading',
        text: '加载中...',
        background: 'rgba(255,255,255, 0.8)',
      });

      privateCustomerQuery({
        dialogId: this.prop.containerid,
        keyword: this.crmShopNameModel,
        pageNum: this.crmSalerPrivateCustomerList_pageNum,
        pageSize: this.crmSalerPrivateCustomerList_pageSize,
      })
        .then((resp) => {
          if (resp.code === 1) {
            if (resp.data) {
              this.crmSalerPrivateCustomerList = resp.data.list || [];
              this.crmSalerPrivateCustomerList_total =
                Number(resp.data.total) || 0;
            }
          } else {
            this.$XyyMessage.error(resp.msg);
          }
        })
        .finally(() => {
          loading.close();
        });
    },

    /**
     * 私海客户搜索查询
     */
    handleSearchPrivateCustomerBtnClick() {
      this.crmSalerPrivateCustomerList_pageNum = 1;
      this.queryCrmSalerPrivateCustomer();
    },

    /**
     * 私海客户搜索查询翻页
     */
    crmSalerPrivateCustomerListPageChange(pageNum) {
      this.crmSalerPrivateCustomerList_pageNum = pageNum;
      this.queryCrmSalerPrivateCustomer();
    },

    /**
     * 查询豆芽菜单关联药店详情
     */
    queryCRMCustomerDetail(customer) {
      const loading = this.$loading({
        target: document.querySelector('.im-config'),
        lock: true,
        spinner: 'el-icon-loading',
        text: '加载中...',
        background: 'rgba(255,255,255, 0.8)',
      });

      getCustomeDetails({
        id: customer.merchantId,
        appId: this.prop.appId,
        businessPartCode: this.prop.businessPartCode,
        channelId: this.prop.channelId,
      })
        .then((resp) => {
          if (resp.code === 1) {
            if (resp.data && resp.data.id) {
              this.crmSalerCustomerInfo = resp.data;
              this.isShowCRMSalerCustomerDetail = '1';
            } else {
              this.$XyyMessage.error('无客户信息');
            }
          } else {
            this.$XyyMessage.error(resp.msg);
          }
        })
        .finally(() => {
          loading.close();
        });
    },
    /**
      返回
     */
    backToCustomerList() {
      this.isShowCRMSalerCustomerDetail = '0';
      this.crmSalerCustomerInfo = {};
    },
  },
};
</script>

<style lang="scss" scoped>
.comp-info-CRM-component-container {
  width: 100%;
  height: 100%;

  .crm-saler-wrapper {
    width: 100%;
    height: 100%;
    padding: 20px 20px 0;

    .header-title {
      width: 100%;
      font-size: 16px;
      font-weight: 600;
      color: #303133;

      .el-link {
        margin-top: -4px;
      }

      .el-divider--vertical {
        margin: -2px 12px 0px 10px;
      }
    }

    .saler-info-section {
      width: 100%;
      padding: 20px 0;
      border-bottom: 1px dashed #dcdee3;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      flex-wrap: wrap;

      .saler-info__item {
        width: 49%;
        line-height: 20px;
        margin-bottom: 10px;
        flex-grow: 0;
        flex-shrink: 0;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;

        &:nth-child(even) {
          margin-left: 2%;
        }

        .saler-info__item__field {
          width: 70px;
          color: #292933;
          font-size: 14px;
          font-weight: 500;
        }

        .saler-info__item__value {
          width: calc(100% - 70px);
          color: #575766;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }

    // 私海客户列表
    .saler-customer-list {
      width: 100%;
      height: calc(100% - 18px - 101px);
      // display: none;

      &.is-active {
        display: inline-block;
      }

      .customer-list__header {
        width: 100%;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        padding: 20px 0;
      }

      .customer-list__search {
        width: 100%;
        padding-bottom: 20px;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .el-button {
          margin-left: 10px;
        }
      }

      .custom-list__content {
        width: 100%;
        height: calc(100% - 58px - 56px);
        position: relative;

        .custom-list__content__wrapper {
          width: 100%;
          height: calc(100% - 48px);
          overflow-y: scroll;

          .custom-list__content__item {
            width: 100%;
            min-height: 30px;
            padding: 10px 0;
            font-size: 14px;
            color: #575766;
            cursor: pointer;

            &:hover {
              background-color: #ebf4f6;
            }
          }

          & + .el-pagination {
            text-align: right;
            padding: 0;
            padding-top: 10px;
            padding-bottom: 10px;

            /deep/ {
              .btn-prev {
                margin-left: 0;
              }

              .btn-next {
                margin-right: 0;
              }
            }
          }
        }

        .content-empty {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);

          img {
            width: 200px;
          }
        }
      }
    }

    //私海客户详情
    .saler-customer-detail {
      width: 100%;
      height: calc(100% - 18px - 101px);
      // display: none;

      .header-title {
        margin-bottom: 20px;
      }

      &.is-active {
        display: inline-block;
      }

      .customer-detail__header {
        width: 100%;
        padding: 20px 0 30px;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;

        .el-button {
          position: absolute;
          left: 0;
        }
      }
    }
  }
}
</style>