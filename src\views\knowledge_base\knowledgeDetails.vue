<template >
  <div class="knowledge-details">
    <el-breadcrumb class="app-breadcrumb" separator="/">
      <el-breadcrumb-item v-for="(item) in levelList" :key="item">
        <span class="no-redirect">{{ item }}</span>
      </el-breadcrumb-item>
    </el-breadcrumb>
    <el-form ref="from" :model="form" label-width="80px">
      <div class="header">
        <div class="title">
          <div>
            <h3>{{form.name}}</h3>
          </div>
          <div>
            <template>
              <template v-if="!isFollowed">
                <xyy-button
                  size="mini"
                  type="normal"
                  icon-class="icon-star-off"
                  @click="favoriteConfirmHandler"
                >收藏</xyy-button>
              </template>
              <template v-else-if="isFollowed">
                <xyy-button
                  size="mini"
                  type="normal"
                  icon-class="icon-star-on"
                  @click="favoriteCancelHandler"
                >已收藏</xyy-button>
              </template>
            </template>
            <span class="title-info">发布人:{{ form.modifyName }}</span>
            <span class="title-info">发布时间:{{form.modifyTime}}</span>
            <span class="title-info">浏览量:{{form.viewNum}}</span>
          </div>
        </div>
      </div>

      <div class="body">
        <div class="message" v-html="form.editorText"></div>
        <!-- 附件 -->
        <!-- class="marginTopList"  -->
        <el-form-item
          label="附件"
          class="marginTopList"
          :class="[{completedNo:uploadList.length===fileNumsNo} ]"
        >
          <el-upload
            :disabled="true"
            ref="myUploader"
            :action="urlAction"
            :limit="fileNums"
            :with-credentials="true"
            :class="[{completed:true}, previewNew ]"
            name="files"
            multiple
            list-type="picture-card"
            accept=".gif, .bmp, .jpg, .jpeg, .png, .pdf, .zip, .rar, .mp4, .avi, .doc, .docx, .ppt, .pptx, .xls, .xlsx, .txt"
            class="upload-file-box"
          >
            <div slot="file" slot-scope="{file}" class="filePreview">
              <file-preview
                :file="file"
                :percent="file.percent?file.percent:0"
                @abortCallback="abortFile(file)"
                @delCallback="delFile(file)"
              />
            </div>
          </el-upload>
        </el-form-item>

        <el-form-item label="标签" :class="[{tagNo:tagList.length===fileNumsNo} ]">
          <!-- 标签管理 -->
          <div class="boxer">
            <div class="tag-list">
              <el-tag
                v-for="tag in tagList"
                :key="tag.tagName"
                :hit="false"
                color="#fff"
              >{{tag.tagName}}</el-tag>
            </div>
          </div>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import filePreview from '@/components/Fields/file-preview';
import { getKnowledgeManagementDetail } from '@/api/knowledge_base';
import {
  compareFile,
  initFileDatas,
  adapter,
  typeCopyVal
} from '@/utils/tools.js';
import utils from '@/utils/filter';
import pathToRegexp from 'path-to-regexp';
import { favoriteConfirm, favoriteCancel } from '@/api/knowledge_base';
export default {
  components: {
    filePreview
  },
  name: 'KnowledgeDetails',
  data() {
    return {
      levelList: [],
      form: {
        viewNum: 0,
        modifyTime: '',
        modifyName: '',
        name: '',
        editorText: '',
        id: '',
        currentVersion: ''
      },
      tagList: [],
      previewNew: 'previewNew',
      urlAction: process.env.BASE_API + '/fileUpload/uploadFile',
      fileNumsNo: 0,
      fileNums: 10, // 文件最大上传数
      uploadList: [], // 上传队列
      failureList: [], // 上传失败文件列表
      uploadingList: false, // 文件上传中
      previewModal: [], // 附件上传返回值的数组
      previewModalarr: [], // 向后台传的数据
      isFollowed: false //-1:初始不显示
    };
  },
  created() {
    // this.getBreadcrumb();
    let that = this;

    //wangEditer a标签点击事件
    window.wangEditerAClick = function(aURL) {
      let aURLHashIndex = aURL.indexOf('#');
      aURL = aURL.substring(aURLHashIndex + 1);
      that.$router.push({
        path: aURL
      });
    };
  },
  mounted() {},
  activated() {
    setTimeout(_ => {
      this.initFiles();
    }, 1000);
    this.form.id = this.$route.query.templateId
      ? this.$route.query.templateId
      : '';
    // this.form.currentVersion = this.$route.query.current_version
    //   ? this.$route.query.current_version
    //   : '';
    this.tagList = [];
    // 查询知识管理详情
    getKnowledgeManagementDetail({
      id: this.form.id
      // currentVersion: this.form.currentVersion
    }).then(res => {
      if (res.code === 1) {
        this.form.name = res.data.title;
        this.form.editorText = res.data.content;
        this.form.viewNum = res.data.viewCount;
        this.isFollowed = res.data.favorite;
        if (res.data.modifyTime == null) {
          modifyTime = '-';
        } else {
          this.form.modifyTime = utils.dataTime(
            res.data.modifyTime,
            'yy-mm-dd HH:ss:nn'
          );
        }
        this.levelList = res.data.imKnowledgeClassificationNames;
        this.form.modifyName = res.data.modifyName;
        if (res.data.synonyms) {
          const tagList = res.data.synonyms.split(',');
          tagList.forEach(item => {
            this.tagList.push({
              tagName: item
            });
          });
        }
        const enclosureItem = this.initEnclosure(res.data.attachment);
        this.uploadList = [].concat(enclosureItem);
      } else {
        this.$XyyMessage.error(res.msg);
      }
    });
  },
  methods: {
    // 附件初始化
    initEnclosure(item) {
      return initFileDatas(item);
    },
    /**
     * 初始化附件数据
     */
    initFiles() {
      if (this.$refs['myUploader']) {
        this.$refs['myUploader'].uploadFiles = this.uploadList;
      }
    },
    /**
     * 取消上传
     */
    abortFile(file) {
      this.$refs['dialogUploader'].abort(file);
      this.$refs['dialogUploader'].uploadFiles.forEach((el, i) => {
        if (el.uid === file.uid) {
          this.$refs['dialogUploader'].uploadFiles.splice(i, 1);
        }
      });
      this.uploadList = [].concat(this.$refs['dialogUploader'].uploadFiles);
      this.uploading = false;
    },
    delFile(file) {
      // 删除上传队列中的数据
      this.$refs['dialogUploader'].uploadFiles.forEach((el, i) => {
        if (el.uid === file.uid) {
          this.$refs['dialogUploader'].uploadFiles.splice(i, 1);
        }
      });
    },
    // 收藏
    favoriteConfirmHandler() {
      let params = {
        knowledgeId: this.form.id
      };
      favoriteConfirm(params)
        .then(res => {
          if (res.code === 1) {
            this.isFollowed = true;
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(function(error) {});
    },
    // 取消收藏
    favoriteCancelHandler() {
      let params = {
        knowledgeId: this.form.id
      };
      favoriteCancel(params)
        .then(res => {
          if (res.code === 1) {
            this.isFollowed = false;
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(function(error) {});
    }
  }
};
</script>

<style lang="scss">
.knowledge-details {
  .app-breadcrumb.el-breadcrumb {
    display: inline-block;
    font-size: 14px;
    line-height: 50px;
    margin-left: 10px;
    .no-redirect {
      color: #97a8be;
      cursor: text;
    }
  }
  .header {
    margin: 0 auto;
    .title {
      text-align: center;
      // margin: 0 auto;
      // display: flex;
      // position: absolute;
      // left: 50%;
      // -webkit-transform: translateX(-50%);
      // transform: translateX(-50%);
      .title-info {
        margin-left: 20px;
      }
    }
  }
  .body {
    margin: 20px 40px;

    .message {
      // min-height: 400px;
      width: 100%;
      height: auto;
      /* table 样式 */
      /deep/ {
        table {
          border-top: 1px solid #ccc;
          border-left: 1px solid #ccc;
        }
        table td,
        table th {
          border-bottom: 1px solid #ccc;
          border-right: 1px solid #ccc;
          // padding: 3px 5px;
        }
        table th {
          background-color: #eee;
          // border-bottom: 2px solid #ccc;
          text-align: center;
        }

        /* blockquote 样式 */
        blockquote {
          display: block;
          border-left: 8px solid #d0e5f2;
          padding: 5px 10px;
          margin: 10px 0;
          line-height: 1.4;
          font-size: 100%;
          background-color: #f1f1f1;
        }

        /* code 样式 */
        code {
          display: inline-block;
          *display: inline;
          *zoom: 1;
          background-color: #f1f1f1;
          border-radius: 3px;
          padding: 3px 5px;
          margin: 0 3px;
        }
        pre code {
          display: block;
        }

        /* ul ol 样式 */
        ul,
        ol {
          margin: 0;
        }

        ul,
        dl {
          list-style-type: disc;
        }
        a {
          color: #428bca;
          text-decoration: none;
        }
        a:hover {
          text-decoration: underline;
        }
      }
    }
    .completedNo {
      display: none;
    }
    .tagNo {
      display: none;
    }
    .marginTopList {
      .previewNew {
        position: relative;
        /deep/.el-upload--picture-card {
          width: 102px;
          height: 36px;
          display: inline-block;
          line-height: 32px;
          border: 0 none;
          position: absolute;
          top: 0;
          left: 0;
          .el-button {
            width: 102px;
            height: 36px;
            background: rgba(255, 255, 255, 1);
            border-radius: 2px;
            border: 1px solid rgba(228, 228, 235, 1);
            color: rgba(87, 87, 102, 1);
          }
        }
        /deep/.el-upload-list--picture-card {
          display: inline-block;
          width: 271px;
          margin-top: 40px;
        }
        /deep/.el-upload-list__item {
          width: 126px;
          height: 30px;
          margin-bottom: 15px;
          display: inline-block;
          background: rgba(245, 247, 250, 1);
          border-radius: 2px;
          border: 1px solid rgba(228, 228, 235, 1);
          overflow: visible;
        }
      }
      .upload-file-box.completed {
        /deep/.el-upload--picture-card {
          display: none;
        }
      }
      /deep/.el-upload--picture-card {
        width: 0px;
        height: 0px;
      }
    }

    .boxer {
      width: 100%;
      height: calc(100% - 75px);
      overflow-y: auto;

      .tag-list {
        .el-tag {
          margin: 0 10px 10px 0;
          border: 0;
        }
      }
    }
  }
}
</style>
