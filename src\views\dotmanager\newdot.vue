<template>
  <div class="newnode-content">
    <el-form
      ref="form"
      :model="form"
      :label-width="labelWidth"
      :rules="rules"
      :hide-required-asterisk="true"
    >
      <div v-if="fromType === 'createdot'" class="pagetitle">新建{{ getNodeType(form.nodeType) }}</div>
      <div v-else class="pagetitle">编辑{{ getNodeType(form.nodeType) }}</div>
      <div class="form-item">
        <!-- 节点名称 start-->
        <el-form-item label="节点名称" prop="nodeName" style="margin-bottom:10px;">
          <el-input v-model.trim="form.nodeName" :disabled="editable" maxlength="20" />
        </el-form-item>
        <!-- 节点名称 end-->

        <!-- 节点类型 start-->
        <el-form-item style="margin-bottom:10px;" label="节点类型">
          <span>{{ getNodeType(form.nodeType) }}</span>
        </el-form-item>

        <!-- 关闭方式 start -->
        <el-form-item v-if="form.nodeType === '3'" label="关闭方式">
          <el-radio-group v-model="form.closeWay" :disabled="editable">
            <el-radio :label="0">人工手动关闭</el-radio>
            <el-radio :label="1">系统自动关闭</el-radio>
          </el-radio-group>
          <div class="explain">
            <el-popover placement="bottom" trigger="hover">
              <div>如选择系统自动关闭，则不需做其他设置，工单流转到关闭节点则自动关闭工单，无需人工操作；如选择人工手动关闭，则需配置以下。</div>
              <div slot="reference">
                <svg-icon class="icon-info" icon-class="info"></svg-icon>
              </div>
            </el-popover>
          </div>
        </el-form-item>
        <!-- 关闭方式 end-->

        <!-- 选择模板 start-->
        <el-form-item v-if="form.nodeType !== '2' && form.closeWay !== 1" label="选择模板">
          <el-select
            :disabled="editable"
            v-model="form.templateCode"
            filterable
            popper-class="select-class-option"
            placeholder="请选择选择模板"
          >
            <el-option value label="请选择模板" />
            <el-option
              v-for="item in templateList"
              :key="item.id"
              :label="item.name"
              :value="item.versionCode"
            />
          </el-select>
          <el-button
            v-if="form.templateCode !== ''"
            :disabled="editable"
            style="margin-left:12px;"
            type="text"
            @click="openPreview"
          >预览模板</el-button>

          <div class="explain">
            <el-popover placement="bottom" trigger="hover">
              <div v-if="form.nodeType === '0'">
                即新建工单时所需的模板，可以选择发起类型的模板。如无模板可点击
                <el-button type="text" @click="creatModule()">新建模板</el-button>
              </div>
              <div v-if="form.nodeType === '1'">
                即处理回复工单时所需的模板，可以选择处理或关闭类型的模板。如无模板可点击
                <el-button type="text" @click="creatModule()">新建模板</el-button>
              </div>
              <div v-if="form.nodeType === '3'">
                即关闭工单时所需的模板，可以选择处理或关闭类型的模板。如无模板可点击
                <el-button type="text" @click="creatModule()">新建模板</el-button>
              </div>
              <div slot="reference">
                <svg-icon class="icon-info" icon-class="info"></svg-icon>
              </div>
            </el-popover>
          </div>
        </el-form-item>
        <!-- 选择模板 end-->

        <!-- 参与人 start-->
        <el-form-item v-if="form.closeWay !== 1" label="参与人">
          <el-tag
            v-for="(user, index) in formUserList"
            :key="index"
            :disable-transitions="false"
            :closable="!editable"
            @close="delUser(index)"
          >{{ user.userGroupName }}</el-tag>

          <div class="explain">
            <el-popover placement="bottom" trigger="hover">
              <div v-if="form.nodeType === '0'">
                即哪些用户可以新建此种工单，可选择一个或多个用户组。如无用户组可点击
                <el-button type="text" @click="creatUserGroup()">新建用户组</el-button>
              </div>
              <div v-if="form.nodeType === '1'">
                即流程到达此节点时需由哪些用户处理工单，可选择一个或多个用户组。如无用户组可点击
                <el-button type="text" @click="creatUserGroup()">新建用户组</el-button>
              </div>
              <div v-if="form.nodeType === '2'">
                即工单流转过程中需抄送哪些用户查看，可选择一个或多个用户组。如无用户组可点击
                <el-button type="text" @click="creatUserGroup()">新建用户组</el-button>
              </div>
              <div v-if="form.nodeType === '3'">
                即流程到达此节点时需由哪些用户关闭工单，可选择一个或多个用户组。如无用户组可点击
                <el-button type="text" @click="creatUserGroup()">新建用户组</el-button>
              </div>
              <div slot="reference">
                <el-button
                  :disabled="editable"
                  class="form-btn"
                  type
                  icon="el-icon-plus"
                  size="small"
                  @click="openUserBox"
                >添加</el-button>
              </div>
            </el-popover>
          </div>

          <div v-if="form.nodeType === '0'" style="color:#909399;">
            注：发起工单时选择问题分类对应不同的模板供用户填写，规则在
            <el-button type="text" @click="jumtoModeType()">问题分类-模板设置</el-button>中设置
          </div>
          <div v-if="form.nodeType === '1'">
            <el-checkbox
              :disabled="editable"
              v-model="form.responsibly"
              :true-label="1"
              :false-label="0"
              style="color:#909399;"
            >处理人全程负责（流程多次流转到本节点，系统指给本节点第一次处理的员工，无需领取）</el-checkbox>
          </div>
          <div v-if="form.nodeType === '3'">
            <el-checkbox
              :disabled="editable"
              v-model="form.closeBy"
              :true-label="1"
              :false-label="0"
              style="color:#909399;"
            >由所选用户组中参与流程的参与人关闭，而非整个用户组都有权限关闭</el-checkbox>
          </div>
        </el-form-item>
        <!-- 参与人 end-->

        <!-- 处理时限 start -->
        <el-form-item
          v-if="(form.nodeType === '1'||form.nodeType === '3')&& form.closeWay !== 1"
          label="处理时限"
        >
          <el-select v-model="form.processTimeout" :disabled="editable" placeholder="请选择时间">
            <el-option :value="0" label="请选择时间" />
            <el-option value="1" label="1小时" />
            <el-option label="2小时" value="2" />
            <el-option label="4小时" value="4" />
            <el-option label="6小时" value="6" />
            <el-option label="8小时" value="8" />
            <el-option label="12小时" value="12" />
            <el-option label="24小时" value="24" />
            <el-option label="36小时" value="36" />
            <el-option label="48小时" value="48" />
            <el-option label="72小时" value="72" />
          </el-select>
          <div class="explain">
            <el-popover placement="bottom" trigger="hover">
              <div>即节点限时机制，工单到达节点后开始计时，如超过所选时限后工单仍未处理完，则节点超时</div>
              <div slot="reference">
                <svg-icon class="icon-info" icon-class="info"></svg-icon>
              </div>
            </el-popover>
          </div>
          <!-- <span style="margin-left:12px;">小时</span> -->
        </el-form-item>
        <!-- 处理时限 end -->
      </div>
    </el-form>

    <footer class="saveButton">
      <el-button type="primary" @click="checkTimer(toRequestAction,'timer')()">保存</el-button>
    </footer>

    <!-- 参与人选择弹窗 start -->
    <el-dialog :visible.sync="userListVisible" class="usergroupclass" title="选择用户组" width="737px">
      <div class="user-content">
        <div class="user-list">
          <div class="search-box">
            <el-input
              v-model="searchKey"
              placeholder="搜索用户组名称"
              maxlength="20"
              suffix-icon="el-icon-search"
            />
          </div>
          <div class="main-box">
            <el-checkbox-group
              v-if="filterUserList.length"
              v-model="checkedUser"
              @change="clickchange"
            >
              <div v-for="(item, index) in filterUserList" :key="index" class="checkBox">
                <div style="width:25px;display:inline-block">
                  <el-checkbox
                    :value="item.versionCode"
                    :label="item.versionCode"
                    @change="getUserInfo($event, item.id, index)"
                  >&nbsp;</el-checkbox>
                </div>
                <span
                  :key="item.userGroupName"
                  :class="{active: item.active}"
                  style="font-size:14px;cursor: pointer;"
                  @click="getUserInfo($event, item.id, index)"
                >{{ item.userGroupName }}</span>
              </div>
            </el-checkbox-group>
            <span v-else class="empty-result">暂未找到相关用户组</span>
          </div>
        </div>
        <div v-loading="userGroupLoading" class="user-info">
          <div class="title-box">组内成员信息</div>
          <el-row>
            <!-- <el-col :span="24" style="font-size:14px;font-weight:600">组内成员信息</el-col> -->
            <div class="main-box">
              <el-col
                v-for="item in checkedUserInfoList"
                :key="item.userId"
                :span="8"
              >{{ item.nickname }}</el-col>
            </div>
          </el-row>
          <!-- <el-row style="border-top: 1px solid #dcdfe6;">
            <el-col :span="24" style="font-size:14px;font-weight:600; margin-top:10px;">已选成员信息</el-col>
            <div>
              <el-col v-for="item in allCheckUser()" :key="item" :span="8">{{ item }}</el-col>
            </div>
          </el-row>-->
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="userListVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmAddUser">确 定</el-button>
      </div>
    </el-dialog>
    <!--  参与人选择弹窗 end -->

    <!--  设置联动条件弹窗 end -->
    <form-view ref="formView" :list="previewList" :layDatas="layDatas" title="预览模板" />
  </div>
</template>

<script>
import {
  getListTemplate,
  getUserList,
  getUserInfo,
  updateNode,
  getFiledListByTemplateId
} from '@/api/formManage';
import {
  nodePoolPost,
  nodePoolPut,
  getNodeById,
  checkBeforeUpdate
} from '@/api/nodemanager';
import utils from '@/utils/form';
import formView from '../form/components/template-preview';
export default {
  name: 'NodeInfo',
  components: { formView },
  filters: {
    filterNodeType: function(value) {
      const map = {
        '0': '发起节点',
        '1': '处理节点',
        '2': '抄送节点',
        '3': '关闭节点'
      };
      return map[value];
    },
    parseFiledType: function(value) {
      const map = {
        '0': '单行输入',
        '1': '多行输入',
        '2': '下拉',
        '3': '单选',
        '4': '多选',
        '5': '日期',
        '6': '级联',
        '7': '省市区',
        '8': '电话',
        '9': '邮箱',
        '10': '附件'
      };
      return map[value];
    }
  },
  data() {
    return {
      oldTemplatCode: '', // 旧的模板code
      userGroupLoading: false,
      form: {},
      fromType: '',
      timer: null,
      timer1: null,
      editable: true, // 操作按钮：0保存 1编辑
      labelWidth: '80px',
      userListVisible: false,
      loading: false,
      rules: {
        nodeName: [
          { required: true, message: '请输入节点名称', trigger: 'blur' },
          { max: 20, message: ' 20字以内', trigger: 'blur' }
        ]
      },
      formUserList: [],
      nodeUserList: [],
      checkedUser: [],
      checkedUserInfoList: [],
      allchekedUserList: [],
      previewList: [], // 预览列表
      templateList: [], // 模板列表
      nodeList: [], // 当前节点外的节点集合
      filedList: [], // 字段集合（包含类型：下拉、单选、多选）
      handlerObj: {}, // 当前字段权限处理对象
      conditionAuthList: [],
      nodeConditionForm: {
        // 设置联动条件form
        authId: '', // 节点字段权限id
        conditionFieldId: '', // 条件字段id
        conditionSymbol: '', // 条件符号
        formId: '', // 表单id
        valueFirst: '', // 第一个值
        valueSecond: '', // 第二个值
        authTypeCondition: 2, // 满足条件的权限类型 0-隐藏 1-只读 2-编辑
        requiredCondition: 0, // 满足条件后是否必填 0-否 1-是
        fieldType: 2, // 表单组件类型
        defaultValue: '', // 表单组件默认值
        optionSettings: {
          // 选项设置卡

          selectOptions: {
            // 下拉列表
            optionsValue: 0, // 默认值
            optionsArray: [
              {
                // 选项数组
                optionsDesc: '我是假数据1'
              },
              {
                // 选项数组
                optionsDesc: '我是假数据2'
              },
              {
                // 选项数组
                optionsDesc: '我是假数据3'
              }
            ]
          },
          radioOptions: {
            // 单选 选项设置
            optionsValue: 0, // 默认值
            optionsArray: [
              {
                // 选项数组
                optionsDesc: '0'
              },
              {
                // 选项数组
                optionsDesc: '1'
              },
              {
                // 选项数组
                optionsDesc: '3'
              }
            ]
          },
          checkedOptions: {
            // 多选 选项设置
            optionsValue: [0], // 默认值
            optionsArray: [
              {
                // 选项数组
                optionsDesc: '1'
              },
              {
                // 选项数组
                optionsDesc: '2'
              }
            ]
          }
        } // 选项设置
      },
      nextNodeId: '', // 下一节点id,下一步为一个节点时有效
      defaultNodeRelation:
        // 页面节点关系
        {
          formId: this.formId, // 表单id
          nextNodeId: '', // 下一节点id
          nodeId: '', // 节点id
          nodeConditionType: 1, // 节点类型： 0-有条件的节点 1-其他节点
          nodeConditionList: [
            {
              authId: '', // 节点字段权限id
              conditionFieldId: '', // 条件字段id
              conditionSymbol: '', // 条件符号
              formId: '', // 表单id
              valueFirst: '', // 第一个值
              valueSecond: '', // 第二个值
              fieldType: 2, // 表单组件类型
              defaultValue: '', // 表单组件默认值
              optionSettings: {
                // 选项设置卡

                selectOptions: {
                  // 下拉列表
                  optionsValue: '', // 默认值
                  optionsArray: [
                    // {
                    //   // 选项数组
                    //   optionsDesc: '我是假数据1'
                    // },
                    // {
                    //   // 选项数组
                    //   optionsDesc: '我是假数据2'
                    // },
                    // {
                    //   // 选项数组
                    //   optionsDesc: '我是假数据3'
                    // }
                  ]
                },
                radioOptions: {
                  // 单选 选项设置
                  optionsValue: '', // 默认值
                  optionsArray: [
                    // {
                    //   // 选项数组
                    //   optionsDesc: '0'
                    // },
                    // {
                    //   // 选项数组
                    //   optionsDesc: '1'
                    // },
                    // {
                    //   // 选项数组
                    //   optionsDesc: '3'
                    // }
                  ]
                },
                checkedOptions: {
                  // 多选 选项设置
                  optionsValue: [], // 默认值
                  optionsArray: [
                    // {
                    //   // 选项数组
                    //   optionsDesc: '1'
                    // },
                    // {
                    //   // 选项数组
                    //   optionsDesc: '2'
                    // }
                  ]
                }
              } // 选项设置
            }
          ] // 节点跳转关系条件
        },
      searchKey: '', // 用户组搜索
      layDatas: '' // 布局数据
    };
  },
  computed: {
    filterUserList() {
      return this.nodeUserList.filter(
        el =>
          this.formatStr(el.userGroupName).search(
            this.formatStr(this.searchKey)
          ) >= 0,
        this
      );
    },
    conditionSymbolList() {
      return utils.conditionSymbolList;
    },
    filterConditionAuthList() {
      return this.conditionAuthList.filter(
        el => el.fieldId !== this.handlerObj.fieldId
      );
    }
  },

  mounted() {
    this.initform();
    if (this.$route.query.type === 'createdot') {
      this.form.nodeType = this.$route.query.dotType || this.form.nodeType;
      this.fromType = 'createdot';
      this.$store.dispatch('tagsView/updateVisitedView', {
        ...this.$route,
        meta: {
          title: '新建' + this.getNodeType(this.form.nodeType)
        }
      });
      this.getListTemplate(true); // 获取模板列表
    }

    this.$nextTick(() => {
      this.editable = this.form.editable;
      if (
        this.$route.query.type === 'updatedot' &&
        this.$route.query.formid != undefined
      ) {
        this.fromType = 'updatedot';
        this.APIGetNodeById(this.$route.query.formid);
      }
    });
  },
  methods: {
    formatStr(val) {
      val = val.replace(/[\'\"\\\/\b\f\n\r\t]/g, '');
      val = val.replace(/[\@\#\$\%\^\&\*\(\)\{\}\:\"\L\<\>\?\[\]]/g, '');
      return val;
    },
    initform() {
      this.form = {
        nodeName: '', // 模板ID
        nodeType: undefined,
        nodeTitle: undefined,
        closeBy: undefined,
        closeWay: this.$route.query.dotType === '3' ? 0 : undefined,
        creatorId: undefined,
        creatorName: undefined,
        deleted: undefined,
        description: undefined,
        duplicate: undefined,
        editorId: undefined,
        editorName: undefined,
        gmtCreate: undefined,
        gmtModified: undefined,
        id: undefined,
        orgId: undefined,
        processTimeout: undefined,
        responsibly: undefined,
        status: undefined,
        templateCode: this.$route.query.dotType === '2' ? undefined : '',
        versionCode: undefined,
        versionEndTime: undefined,
        userGroupCodes: undefined
      };
    },
    openUserBox() {
      this.userListVisible = true;
      this.initUserList();
    },
    initUserList(cb) {
      this.searchKey = '';
      this.getUserList(list => {
        // this.checkedUserInfoList = list.filter(el =>
        //   this.checkedUser.map(_el => _el).includes(el.versionCode)
        // );
        this.formUserList = list.filter(el =>
          this.checkedUser.map(_el => _el).includes(el.versionCode)
        );
      });
    },

    // 获取用户信息
    getUserList(cb) {
      getUserList({ pageSize: 1000, pageNum: 1 }).then(res => {
        if (res && res.code) {
          this.nodeUserList = res.data.list.map(el => {
            el.active = false;
            return el;
          });
          if (cb) {
            cb(this.nodeUserList);
          }
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    // 获取模板列表
    getListTemplate(isFirst) {
      getListTemplate({ type: this.form.nodeType === '0' ? '0' : '1' }).then(
        res => {
          if (res.code === 1) {
            this.templateList = res.data;
          } else {
            this.$XyyMessage.error(res.msg);
          }
        }
      );
    },

    // 添加参与人
    confirmAddUser() {
      if (!this.checkedUser.length) {
        this.$XyyMessage.error('请先选择用户');
        return false;
      }
      this.userListVisible = false;
      const tem = [];
      this.nodeUserList.forEach(el => {
        if (this.checkedUser.includes(el.versionCode)) {
          tem.push(
            Object.assign(el, {
              userGroupId: el.id,
              formId: this.formId,
              nodeId: this.form.id
            })
          );
        }
      });
      this.formUserList = Object.assign([], tem);
    },
    // 删除参与人
    delUser(index) {
      this.formUserList.splice(index, 1);
      this.checkedUser.splice(index, 1);
    },
    // 打开模板预览弹窗
    openPreview() {
      let tempid = '';
      for (let index = 0; index < this.templateList.length; index++) {
        const element = this.templateList[index];
        if (element.versionCode === this.form.templateCode) {
          tempid = element.id;
        }
      }
      const params = {
        // templateId: 6,
        templateId: tempid,
        needSystemField: false
      };
      if (!params.templateId) return false;
      getFiledListByTemplateId(params).then(res => {
        this.previewList = res.data.list;
        this.layDatas = res.data.layout;
        if (!this.layDatas && this.previewList.length) {
          let arr = [];
          for (let i = 0; i < this.previewList.length; i++) {
            arr.push([1]);
          }
          this.layDatas = arr.map(cols => cols.join('-')).join();
        }
        this.previewList.length &&
          this.previewList.forEach(el => {
            el.optionSettings = JSON.parse(
              el.optionSettings.replace(/&quot;/g, '"')
            );
          });
        this.$refs.formView.show();
      });
    },

    // 字段类型改变
    /**
     * @isEcho  {是否回显}
     */
    handleFiledChange(val, isEcho, valueFirst) {
      const item = this.conditionAuthList.filter(el => el.fieldId === val)[0];
      // 赋值表单类型和表单数据
      if (typeof item.optionSettings === 'string') {
        item.optionSettings = JSON.parse(
          item.optionSettings.replace(/&quot;/g, '"')
        );
      }
      Object.assign(this.nodeConditionForm, {
        optionSettings: item.optionSettings,
        fieldType: Number(item.fieldType),
        conditionSymbol: isEcho ? this.nodeConditionForm.conditionSymbol : ''
      });
      if (!isEcho) {
        (function(which, keys) {
          which.$nextTick(function() {
            which.$refs['preview'].resetValue(keys);
          });
        })(this, item.fieldType);
      } else {
        (function(which, keys, valueFirst) {
          which.$nextTick(function() {
            which.$refs['preview'].initData(keys, valueFirst);
          });
        })(this, item.fieldType, valueFirst);
      }
    },

    // 获取用户组用户详情
    getUserInfo(isChecked, userGroupId, index) {
      if (isChecked) {
        this.nodeUserList.forEach((el, index) => {
          el.active = false;
          this.nodeUserList.splice(index, 1, el);
        });
        this.nodeUserList[index].active = true;
        this.userGroupLoading = true;
        getUserInfo(userGroupId).then(res => {
          this.userGroupLoading = false;
          if (res.code) {
            this.checkedUserInfoList = res.data;
            // if (isChecked === true) {
            //   this.allchekedUserList[index] = res.data;
            // }
          }
        });
      } else {
        // this.allchekedUserList[index] = [];
        this.checkedUserInfoList = [];
      }
    },

    allCheckUser() {
      const alluser = [];
      for (const key in this.allchekedUserList) {
        const data = this.allchekedUserList[key];
        for (let index = 0; index < data.length; index++) {
          const element = data[index];
          if (alluser.indexOf(element.nickname) === -1) {
            alluser.push(element.nickname);
          }
        }
      }
      return alluser;
    },

    // 是否需要已选成员信息
    findCheckUser(groupArray) {
      for (let index = 0; index < groupArray.length; index++) {
        const element = groupArray[index];
        this.getUserInfo(true, element.userGroupId, element.index);
      }
    },

    toRequestAction() {
      if (this.fromType === 'createdot') {
        this.ApiNodePoolPost();
      } else {
        this.APINodePoolPut();
      }
    },

    getNodeType(type) {
      if (type === '0') {
        return '发起节点';
      } else if (type === '1') {
        return '处理节点';
      } else if (type === '2') {
        return '抄送节点';
      } else {
        return '关闭节点';
      }
    },

    toLastView(visitedViews, view) {
      let lastview;
      for (let index = 0; index < visitedViews.length; index++) {
        const element = visitedViews[index];
        if (element.name === 'dotManagerIndex') {
          lastview = element;
        }
      }
      if (lastview) {
        this.$router.push(lastview);
      } else {
        this.$router.push('/');
      }
    },

    canPass() {
      if (this.form.closeWay === 1) {
        this.form.userGroupCodes = undefined;
        this.form.processTimeout = undefined;
        this.form.templateCode = undefined;
        this.form.closeBy = undefined;
        this.form.description = undefined;
      }

      if (this.form.nodeName === '') {
        this.$XyyMessage.error('请输入节点名称');
        return false;
      }

      if (this.form.nodeName.length > 20) {
        this.$XyyMessage.error('节点名称不可超过20个字');
        return false;
      }

      if (this.form.nodeType === '3' && this.form.closeWay === undefined) {
        this.$XyyMessage.error('请选择关闭方式');
        return false;
      }

      if (this.form.templateCode === '' && this.form.nodeType !== '2') {
        this.$XyyMessage.error('请选择模板');
        return false;
      }

      if (this.checkedUser.length === 0 && this.form.closeWay !== 1) {
        this.$XyyMessage.error('请选择用户组');
        return false;
      }
      if (
        this.form.processTimeout != undefined &&
        this.form.processTimeout.indexOf('小时') !== -1
      ) {
        this.form.processTimeout = this.form.processTimeout.substring(
          0,
          this.form.processTimeout.indexOf('小时')
        );
      }
      if (
        this.form.processTimeout != undefined &&
        this.form.processTimeout === '请选择时间'
      ) {
        this.form.processTimeout = '0';
      }
      if (
        this.form.processTimeout === 0 ||
        (this.form.nodeType !== '1' &&
          this.form.nodeType !== '3' &&
          this.form.closeWay !== 0)
      ) {
        this.form.processTimeout = undefined;
      }
      if (
        (this.form.processTimeout === undefined &&
          this.form.nodeType === '1') ||
        (this.form.processTimeout === undefined &&
          this.form.nodeType === '3' &&
          this.form.closeWay === 0)
      ) {
        this.$XyyMessage.error('请选择处理时限');
        return false;
      }
      return true;
    },

    jumtoModeType() {
      this.$router.push({
        name: 'configuration'
      });
    },

    creatUserGroup() {
      this.$router.push({
        path: '/worksheet/userGroupAdd/' + new Date().getTime()
      });
    },

    creatModule() {
      this.$router.push({
        name: 'templateBase'
      });
    },

    /**
     * APIthis.form.
     */

    /**
     * 新建节点
     */
    ApiNodePoolPost() {
      const copydata = utils.deepCopy(this.form);
      this.form.userGroupCodes = this.checkedUser.join(',');
      if (this.canPass() === false) {
        return;
      }
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      nodePoolPost(this.form).then(res => {
        this.loading.close();
        if (res.code === 1) {
          this.$XyyMessage.success('节点保存成功');
          this.$store
            .dispatch('tagsView/delView', this.$route)
            .then(({ visitedViews }) => {
              this.toLastView(visitedViews, this.$route);
            });
        } else {
          this.form = copydata;
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    /**
     * 更新节点
     */
    APINodePoolPut() {
      const copydata = utils.deepCopy(this.form);
      this.form.userGroupCodes = this.checkedUser.join(',');
      if (this.canPass() === false) {
        return;
      }

      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });

      const that = this;
      this.APICheckBeforeUpdate(function(next) {
        if (next === true) {
          nodePoolPut(that.form).then(res => {
            that.loading.close();
            if (res.code === 1) {
              that.$XyyMessage.success('节点更新成功');
              that.$store
                .dispatch('tagsView/delView', that.$route)
                .then(({ visitedViews }) => {
                  that.toLastView(visitedViews, that.$route);
                });
            } else {
              that.form = copydata;
              that.$XyyMessage.error(res.msg);
            }
          });
        }
      });
    },

    // 更新前的验证
    APICheckBeforeUpdate(callback) {
      // if (this.oldTemplatCode === this.form.templateCode) {
      //   callback(true);
      //   return;
      // }
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      checkBeforeUpdate({
        id: this.form.id,
        templateCode: this.form.templateCode
      }).then(res => {
        if (res.code === 1) {
          this.loading.close();
          this.$XyyMsg({
            title: '提示',
            content: res.msg,
            onSuccess: () => {
              callback(true);
            }
          });
        } else if (res.code === 2) {
          callback(true);
        } else {
          this.loading.close();
          this.$XyyMessage.error(res.msg);
          callback(false);
        }
      });
    },

    // 根据id查询子节点数据
    APIGetNodeById(formid) {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      });
      getNodeById({ id: formid }).then(res => {
        this.loading.close();
        if (res.code === 1) {
          const data = res.data;
          this.checkedUser = [];
          this.formUserList = data.userGroups;
          if (data.userGroups !== undefined) {
            for (let index = 0; index < data.userGroups.length; index++) {
              const element = data.userGroups[index];
              this.checkedUser.push(element.userGroupCode);
            }
          }

          this.form = res.data;
          this.form.nodeType = this.form.nodeType.toString();
          this.form.userGroups = undefined;
          this.form.processTimeout =
            res.data.processTimeout === 0
              ? '请选择时间'
              : res.data.processTimeout + '小时';
          // this.oldTemplatCode = res.data.templateCode;
          this.$store.dispatch('tagsView/updateVisitedView', {
            ...this.$route,
            meta: {
              title: '编辑' + this.getNodeType(this.form.nodeType)
            }
          });
          this.getListTemplate(true); // 获取模板列表
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    // 从表单数据中提取需提交的字段
    getUseFormData(fields, data) {
      const o = {};
      for (const k in data) {
        if (fields.hasOwnProperty(k)) {
          o[k] = data[k];
        }
      }
      return o;
    },

    clickchange(val) {}
  }
};
</script>
<style lang="scss">
.select-class-option {
  z-index: 8 !important;
}

.newnode-content .el-input__inner {
  height: 36px;
}

.newnode-content .usergroupclass .el-dialog__body {
  padding: 30px 20px;
  padding-top: 11px;
  padding-bottom: 15px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}
</style>
<style lang="scss" scoped>
.chooseModele {
  display: flex;
  flex-direction: row;
}

.saveButton {
  position: absolute;
  bottom: 0;
  right: 0;
  margin-right: 20px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}

.newnode-content {
  position: relative;
  padding-left: 10px;
  padding-top: 15px;
  .pagetitle {
    font-size: 16px;
    font-weight: 800;
    margin-left: 10px;
    margin-bottom: 20px;
  }
  .form-item {
    width: 100%;
    .el-input {
      width: 420px;
    }
    .el-select {
      width: 420px;
    }
    .el-tag {
      margin-right: 10px;
      height: 36px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(87, 87, 102, 1);
      background: rgba(245, 247, 250, 1);
      border-radius: 2px;
      border: 1px solid rgba(228, 228, 235, 1);
      line-height: 36px;
    }
    .filed-auth {
      margin-top: 13px;
      background: rgba(245, 247, 250, 1);
      border-radius: 2px;
      border: 1px solid rgba(228, 228, 235, 1);
      border-bottom: none;
      & .el-row:first-child {
        font-family: PingFangSC-Medium;
        font-weight: 600;
        color: rgba(41, 41, 51, 1);
        background: rgba(240, 242, 245, 1);
      }
      .el-row {
        padding: 0 20px;
        border-bottom: 1px solid #e4e4eb;
        .condition-display {
          height: 30px;
          line-height: 30px;
          background: rgba(235, 235, 239, 1);
          margin-bottom: 12px;
          box-sizing: border-box;
          padding: 0 12px;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFangSC;
          font-weight: 400;
          color: rgba(144, 147, 153, 1);
          span {
            float: left;
            width: 30%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          span:first-child {
            margin-right: 50px;
          }
          .el-button {
            font-size: 12px;
            height: 100%;
            font-family: PingFangSC-Regular, PingFangSC;
            font-weight: 400;
            color: rgba(255, 156, 8, 1);
            float: right;
            cursor: pointer;
            padding: 0;
            &.is-disabled {
              color: #c0c4cc;
            }
          }
        }
      }
      .title {
        font-family: PingFangSC-Regular;
        font-weight: 600;
        color: rgba(41, 41, 51, 1);
      }
    }
    .node-condition {
      margin-top: 13px;
      background: rgba(245, 247, 250, 1);
      border-radius: 2px;
      border: 1px solid rgba(228, 228, 235, 1);
      border-bottom: none;
      .el-input {
        width: 240px;
      }
      .el-select {
        width: 240px;
      }
      .node-item {
        position: relative;
        padding: 30px;
        border-bottom: 1px solid #e4e4eb;
        .numbers-icon {
          display: inline-block;
          width: 20px;
          height: 20px;
          background: #e4e4eb;
          position: absolute;
          color: #909399;
          font-size: 12px;
          z-index: 9999999999;
          line-height: 19px;
          text-align: center;
          border-radius: 10px;
          top: 54px;
          left: 5px;
          z-index: 0;
        }
        .add-node {
          text-align: center;
          background: rgba(255, 255, 255, 1);
          border-radius: 2px;
          border: 1px dashed rgba(220, 223, 230, 1);
          .el-button--text {
            width: 100%;
          }
        }
        .node-card {
          position: relative;
          margin-bottom: 15px;
          .el-icon-minus {
            position: absolute;
            width: 16px;
            height: 16px;
            border: 1px solid rgba(59, 149, 168, 1);
            color: rgba(59, 149, 168, 1);
            top: 12px;
            right: -8px;
            cursor: pointer;
            &.first {
              left: 300px;
            }
          }
          .preview {
            /deep/.el-form-item__content {
              width: 100%;
              /deep/.el-input__inner {
                min-height: 40px !important;
              }
            }
          }
        }
      }
    }
    .form-btn.el-button {
      height: 36px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(87, 87, 102, 1);
      position: relative;
      top: -1px;
    }
    .explain {
      display: inline-block;
      margin-left: 12px;
    }
  }
  .user-content {
    height: 468px;
    width: 697px;
    .user-list {
      float: left;
      width: 290px;
      height: 100%;
      border: 1px solid rgba(228, 228, 235, 1);
      div.search-box {
        height: 55px;
        background: #f0f2f5;
        padding: 12px 20px 11px;
        .el-input {
          /deep/.el-input__inner {
            height: 32px;
            line-height: 32px;
          }
          /deep/.el-input__icon.el-icon-search {
            line-height: 32px;
          }
        }
      }
      div.main-box {
        padding: 20px;
        position: relative;
        height: calc(100% - 55px);
        overflow-y: auto;
        .el-checkbox {
          display: block;
          margin-bottom: 10px;
        }
        .active {
          color: #3b95a8;
        }
        .empty-result {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translateX(-50%) translateY(-50%);
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(174, 174, 191, 1);
        }
      }
    }
    .user-info {
      float: right;
      width: calc(100% - 298px);
      height: 100%;
      overflow: auto;
      border: 1px solid rgba(228, 228, 235, 1);
      div.title-box {
        height: 55px;
        line-height: 55px;
        background: rgba(240, 242, 245, 1);
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(41, 41, 51, 1);
        padding: 0 20px;
      }
      div.main-box {
        padding: 20px;
        .el-col {
          margin-bottom: 10px;
        }
      }
    }
  }
  .form-inline {
    display: inline-block;
  }
  .condition-dialog {
    .preview {
      /deep/.el-form-item__content {
        width: 100%;
        /deep/.el-input__inner {
          min-height: 40px !important;
        }
      }
    }
  }
}
</style>

