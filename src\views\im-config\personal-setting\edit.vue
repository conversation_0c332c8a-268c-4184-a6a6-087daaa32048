<template>
  <div class="setting-box">
    <el-form :model="form" label-width="80px">
      <el-form-item label="用户名">
        <el-input v-model="form.username" :disabled="true"></el-input>
      </el-form-item>
      <el-form-item label="工牌号">
        <el-input v-model="form.code" :disabled="true"></el-input>
      </el-form-item>
      <el-form-item label="姓名">
        <el-input v-model="form.name" :disabled="true"></el-input>
      </el-form-item>
      <el-form-item label="昵称">
        <el-input v-model="form.nickname" :disabled="true"></el-input>
        <el-button type="text" @click="toEdit('nickname')">编辑</el-button>
      </el-form-item>
      <el-form-item class="avatar-box" label-width="0">
        <el-avatar :size="70" :src="form.avatar?form.avatar:defaultAvatar" shape="square"></el-avatar>
        <el-upload :show-file-list="false" :http-request="upload" action>
          <el-button plain icon="el-icon-upload2">更换头像</el-button>
        </el-upload>
      </el-form-item>
      <!-- <el-form-item label="生日">
        <el-date-picker v-model="form.birthday" :disabled="true" type="date"></el-date-picker>
        <el-button type="text" @click="toEdit('birthday')">编辑</el-button>
      </el-form-item>-->
      <el-form-item label="服务数">
        <el-input v-model="form.maxdialog" :disabled="true"></el-input>
      </el-form-item>
      <el-form-item label="角色">
        <el-input v-model="form.roleName" :disabled="true"></el-input>
      </el-form-item>
      <!-- <el-form-item label="上级">
        <el-input v-model="form.boss" :disabled="true"></el-input>
      </el-form-item>
      <el-form-item label="业务">
        <el-input v-model="form.business" :disabled="true"></el-input>
      </el-form-item>-->
      <el-form-item label="备注">
        <el-input v-model="form.remark" :disabled="true"></el-input>
        <el-button type="text" @click="toEdit('remark')">编辑</el-button>
      </el-form-item>
    </el-form>
    <el-dialog
      :visible.sync="open"
      :title="title"
      :close-on-click-modal="false"
      custom-class="personal-edit"
      top="0"
    >
      <!-- <el-date-picker v-if="type==='birthday'" v-model="birthday" type="date" placeholder="选择日期"></el-date-picker> -->
      <el-form>
        <el-form-item :label="title" label-width="80">
          <el-input v-if="type==='nickname'" v-model="nickname"></el-input>
          <el-input v-else v-model="remark"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  upload,
  getPersonalData,
  savePersonalData
} from '@/api/im-config/personal-setting';
import _avatar from '@/assets/common/user-avatar.png';
export default {
  name: 'compPersonalSetting',
  data() {
    return {
      form: {
        username: '',
        code: '',
        name: '',
        nickname: '',
        roleName: '',
        avatar: '',
        // birthday: '',
        maxdialog: 0,
        // boss: '',
        // business: '',
        remark: ''
      },
      title: '',
      open: false,
      type: '',
      // birthday: '',
      remark: '',
      nickname: '',
      defaultAvatar: _avatar
    };
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      getPersonalData()
        .then(res => {
          if (res.code === 1) {
            this.form = {
              username: res.data.username,
              code: res.data.code,
              name: res.data.name,
              nickname: res.data.nickname,
              roleName: res.data.roleName,
              avatar: res.data.avatar,
              // birthday: res.data.birthday,
              maxdialog: res.data.maxdialog,
              // boss: res.data.boss,
              // business: '',
              remark: res.data.remark
            };
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },
    upload(obj) {
      upload({ file: obj.file })
        .then(res => {
          if (res.code === 1) {
            this.handleSuccess(res.data, obj.file);
          } else {
            this.$XyyMessage.error(res.msg);
          }
        })
        .catch(() => {});
    },
    handleSuccess(path, file) {
      // this.form.avatar = path;
      savePersonalData({
        op: 'avatar',
        avatar: path
      }).then(res => {
        if (res.code === 1) {
          this.$XyyMessage.success('修改成功');
          this.initData();
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },
    toEdit(type) {
      this.title = type === 'remark' ? '备注' : '昵称';
      this.type = type;
      // this.birthday = this.form.birthday;
      this.remark = this.form.remark;
      this.nickname = this.form.nickname;
      this.open = true;
    },
    close() {
      this.open = false;
    },
    save() {
      if (this.type === 'nickname') {
        // this.form.nickname = this.nickname;
        savePersonalData({
          op: this.type,
          nickname: this.nickname
        }).then(res => {
          if (res.code === 1) {
            this.$XyyMessage.success('修改成功');
            this.initData();
          } else {
            if (res) {
              this.$XyyMessage.error(res.msg);
            }
          }
        });
      } else {
        // this.form.remark = this.remark;
        savePersonalData({
          op: this.type,
          remark: this.remark
        }).then(res => {
          if (res.code === 1) {
            this.initData();
            this.$XyyMessage.success('修改成功');
          } else {
            if (res) {
              this.$XyyMessage.error(res.msg);
            }
          }
        });
      }
      this.close();
    }
  }
};
</script>

<style lang="scss" scoped>
.setting-box {
  position: relative;
  .el-form {
    width: 400px;
    margin: 30px auto;
    .el-form-item {
      margin-bottom: 20px;
      /deep/.el-form-item__label {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(41, 41, 51, 1);
      }
      &.avatar-box {
        position: absolute;
        top: 30px;
        left: 90px;
      }
      .el-input {
        width: calc(100% - 50px);
        /deep/.el-input__inner {
          height: 36px;
          line-height: 36px;
          border: 1px solid rgba(228, 228, 235, 1);
        }
      }
      .el-avatar {
        background: #fff;
        margin: 0 15px 5px;
      }
      .el-button {
        height: 36px;
        line-height: 36px;
        padding: 0;
        margin-left: 12px;
      }
      .el-upload {
        .el-button {
          padding: 0 11px;
          border: 1px solid #e4e4eb;
          color: #575766;
          margin-left: 0;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.el-dialog.personal-edit {
  width: 400px;
  top: 50%;
  transform: translateY(-50%);
  .el-dialog__header {
    padding: 15px 20px;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: rgba(41, 41, 51, 1);
    .el-dialog__headerbtn {
      top: 17px;
    }
    border: 1px solid #f1f1f5;
  }
  .el-dialog__body {
    padding: 40px 20px;
    .el-input {
      // width: 100%;
      width: calc(100% - 80px) !important;
      .el-input__inner {
        height: 36px;
        line-height: 36px;
      }
    }
  }
  .el-dialog__footer {
    height: 56px;
    padding: 0 20px;
    box-sizing: border-box;
    .el-button {
      padding: 0 20px;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      font-weight: 400;
      font-family: PingFangSC-Regular, PingFang SC;
      &.el-button--default {
        border: 1px solid rgba(228, 228, 235, 1);
        color: rgba(87, 87, 102, 1);
        &:hover,
        &:focus {
          border: 1px solid rgba(228, 228, 235, 1);
          color: rgba(87, 87, 102, 1);
        }
      }
    }
  }
}
</style>
