<!-- im聊天中间组件 -->
<template>
  <div style="height:100%" :hidden="refreshChatView" class="im_container_midview">
    <div style="border: 1px solid #dcdee3;height:100%">
      <topview
        ref="container_main_topview"
        :view-data="currentUserData"
        :black-list-auth="kefuInfoData.blackListAuth"
        :viewtype="topvuewType"
        :container-id="currentContainerID"
        class="im_container_chat_top_view"
        @user_top_view="userTopView"
      ></topview>
      <chatcontent
        class="im_container_chat_content"
        ref="newMessage"
        :style="{height:calaHeight}"
        :sectionName="sectionName"
        :needEvent="chatTopScroll"
        :chatData="{
          userId,
          searchType,
          currentContainerID,
          appId:chatInfo.appId,
          oldShopId:chatInfo.oldShopId,
          avatar:kefuInfoData.avatar,
          customHeadImg:chatInfo.customHeadImg
        }"
        @recallMessage="recallMessage"
      ></chatcontent>

      <div class="im-view-center-chat-input-container" v-if="topvuewType === 'ing'">
        <chatinput
          ref="contentinput"
          :container-id="currentContainerID"
          :uploadurl="imgUploadUrl"
          :fileuploadurl="fileUploadUrl"
          :contentEdit="editorText"
          @clickInputTool="clickInputTool"
          @sendFileMessage="sendFileMessage"
        ></chatinput>
        <div
          style="width:100%; background-color:white; height:50px;display: flex;justify-content: flex-end;align-items: center;"
        >
          <el-button
            style="margin-right:12px;z-index:99"
            size="small"
            type="success"
            class="send-chat-msg"
            @click="apisendmessage()"
          >发送</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
//例如：import 《组件名称》 from '《组件路径》';

import topview from './components/im_center_topview';
import chatcontent from './components/im_center_content';
import chatinput from './components/im_center_chatinput';
import { topData } from '../im_view/components/im_container_tool/message_tool';
import {
  transferSend,
  sendMessage,
  kfAdvice,
  addblack,
  settodooo,
  closeContainer,
  startChart,
  startConversation,
  sendcard
} from '@/api/im_view/index';
import { log } from 'util';
export default {
  components: {
    topview,
    chatcontent,
    chatinput
  },
  data() {
    return {
      userId: null,
      searchType: null,
      chatType: '', // 会话类型

      refreshChatView: false,
      currentUserData: { avatar: '', textData: [] },
      kefuInfoData: {},
      topvuewType: 'ing', // 顶部客户信息简介类型
      currentContainerID: '0', // 当前会话id
      contentMaxHeight: 265, // 聊天记录窗体高度
      sectionName: 'im_container_main',

      imgUploadUrl: process.env.BASE_API_IM + '/im-file/uploadimg',
      fileUploadUrl: process.env.BASE_API_IM + '/im-file/uploadFile',
      chatTopScroll: true, // 是否需要上拉加载更多
      editorText: {
        editorTextIn: '',
        editorTextIndex: 1
      },
      chatInfo: {}, // 会话基本信息
      chatdata: [], // 聊天记录数据
      chatContentData: [],
      chatContente: '', // 输入框的内容
      loading: ''
    };
  },
  //监听属性 类似于data概念
  computed: {
    calaHeight: function() {
      return 'calc(100% - ' + this.contentMaxHeight + 'px)';
    }
  },
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    // 选中会话
    // type：会话中 0 , 待跟进 1 , 历史会话 2 , 留言 3 , 搜索 4
    chatSelect({ type, userId, chat, kefuInfo }) {
      const that = this;
      if (
        that.userId !== userId ||
        that.chatType !== type ||
        that.currentContainerID !== chat.id
      ) {
        // 三个会话相关ID相同则不重新获取数据
        const cnt = that.editorText;
        Object.assign(that.$data, that.$options.data()); // 重置data数据
        that.editorText = cnt;
        that.userId = userId;
        that.chatType = type;
        that.chatInfo = chat;
        that.currentContainerID = that.chatInfo.id;
        that.chatInfo.customHeadImg =
          that.chatInfo.customHeadImg || require('../../../static/user_kh.png');
        that.currentUserData = topData({ dialoginfo: that.chatInfo });
        that.kefuInfoData = kefuInfo;
        this.refreshEditContent(that.currentContainerID);
        this.$nextTick(() => {
          this.$refs.container_main_topview.upAction();
        });
      }
      switch (type) {
        case '0': {
          // 会话中
          // searchType: 1-搜索查询（传：type,userId）2-待跟进.历史.留言（传：type,currentDialogId,userId）3-会话中（type,currentDialogId,userId,time(查询第二页的时间)）
          // dialogEndScene:0-未关闭，1-系统关闭,2-访客超时关闭,3-客户关闭,4-客服关闭,5-待跟进，6-抢接关闭,7-主动转接关闭
          if (chat.dialogEndScene === 0) {
            that.topvuewType = 'ing';
            this.contentMaxHeight = this.conntentHeight('show');
          } else {
            that.topvuewType = 'history';
            this.contentMaxHeight = this.conntentHeight('history');
          }
          that.searchType = 3;
          break;
        }
        case '1': {
          // 待跟进
          that.topvuewType = 'other';
          that.searchType = 2;
          this.contentMaxHeight = this.conntentHeight('other');
          break;
        }
        case '2': {
          // 历史会话
          that.topvuewType = 'history';
          that.searchType = 2;
          this.contentMaxHeight = this.conntentHeight('history');
          break;
        }
        case '3': {
          // 留言
          that.topvuewType = 'other';
          that.searchType = 2;
          this.contentMaxHeight = this.conntentHeight('other');
          break;
        }
        case '4': {
          // 搜索会话
          that.topvuewType = 'other';
          that.searchType = 1;
          this.contentMaxHeight = this.conntentHeight('other');
          break;
        }
      }
    },
    // 更新编辑器内容
    refreshEditContent(currentContainerID) {
      const that = this;
      that.$nextTick(() => {
        that.editorText.editorTextIn = '';
        that.editorText.editorTextIn = that.$store.getters.editContentData(
          currentContainerID
        );
        that.editorText.editorTextIndex++;
      });
    },
    // 发送消息
    apisendmessage() {
      const that = this;
      if (!that.currentContainerID || that.currentContainerID === '0') {
        this.$XyyMessage.error('请选择会话');
        return;
      }
      if (!this.chatContente.replace(/<br \/>|<p>|<\/p>/g, '').trim().length) {
        this.$XyyMessage.error('请不要发送空信息');
        this.$refs.contentinput.cleanInput();
        return;
      }

      let chatcopy = this.chatContente;
      for (let index = 0; index < this.chatContentData.length; index++) {
        const element = this.chatContentData[index];
        if (chatcopy.indexOf(element.oldvalue) !== -1) {
          const copycontent = chatcopy.replace(
            element.oldvalue,
            element.newvalue
          );
          chatcopy = copycontent;
        }
      }

      sendMessage(this.currentContainerID, {
        dialogid: this.currentContainerID,
        msgText: chatcopy
      }).then(res => {
        if (res.code === 1) {
          this.chatContente = '';
          this.$refs.contentinput.cleanInput();
        } else {
          this.$message.error(res.msg);
        }
      });
    },

    /**
     * 撤回消息
     */
    recallMessage(params) {
      this.$emit('recallMessage', params);
    },

    //上传附件，直接发送消息
    sendFileMessage(messageTxt) {
      sendMessage(this.currentContainerID, {
        dialogid: this.currentContainerID,
        msgText: messageTxt
      }).then(res => {
        if (res.code === 1) {
        } else {
          this.$message.error(res.msg);
        }
      });
    },

    // 插入新消息
    setMessage(params) {
      this.$refs.newMessage.setMessage(params);
    },

    // 编辑器事件回调
    clickInputTool(type, data, contenttype) {
      switch (type) {
        case 'sendcard': {
          sendcard(this.currentContainerID).then(resp => {
            if (resp.code == 1) {
            } else {
              this.$XyyMessage.error(resp.msg);
            }
          });
          break;
        }
        case 'transfera': {
          this.transfera(data);
          break;
        }
        case 'history': {
          this.history();
          break;
        }
        case 'assess': {
          this.apikfAdvice();
          break;
        }
        case 'changeContent': {
          this.chatContente = data;
          if (contenttype === 'kjhf') {
            return;
          }
          // const str = this.chatContente.substring(
          //   this.chatContente.length - 17,
          //   this.chatContente.length
          // );

          // if (str.indexOf('<br />') !== -1) {
          //   this.chatContente = this.chatContente.substring(
          //     0,
          //     this.chatContente.length - 17
          //   );
          //   this.chatContente = this.haveAndDeletebr(this.chatContente);
          //   if (this.chatContente === '') {
          //     return;
          //   }
          //   // this.apisendmessage();
          // }

          break;
        }
        case 'changeSrc': {
          this.chatContentData.push(data);
          break;
        }
      }
    },
    //
    haveAndDeletebr(str) {
      const str1 = str.substring(str.length - 17, str.length);

      if (str1.indexOf('<br />') !== -1) {
        str = str.substring(0, str - 17);
      }
      return str;
    },
    // 会话转接
    transfera(content) {
      transferSend({
        did: this.currentContainerID,
        id: content.id,
        type: content.type
      }).then(res => {
        if (res.code === 1) {
          this.$XyyMessage.success('转接请求发送成功');
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    // 邀请用户评价
    apikfAdvice() {
      kfAdvice({ dialogid: this.currentContainerID }).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.error(res.msg);
        }
      });
    },

    // 切换到 右侧会话历史记录
    history(content) {
      this.$parent.tohistory();
      // alert('history');
    },

    // 聊天页上方的工具栏方法,包含（展开，收起，关闭黑名单，结束会话，发起会话）
    userTopView(type, data) {
      if (type === 'addblacklist') {
        // 添加黑名单
        this.apiAddblack(data);
      } else if (type === 'closeContainer') {
        // 结束会话
        this.apiCloseTalking();
      } else if (type === 'startContainer') {
        // 发起会话
        this.apiStartTalking();
      } else if (type === 'show') {
        // 展开
        this.contentMaxHeight = this.conntentHeight('close');
      } else if (type === 'close') {
        // 关闭
        this.contentMaxHeight = this.conntentHeight('show');
      }
    },

    // 发起会话
    apiStartTalking() {
      const that = this;
      if (!that.currentContainerID || that.currentContainerID === '0') {
        this.$XyyMessage.error('请选择会话');
        return;
      }
      let params = {};
      if (that.chatType == '4' && that.currentContainerID) {
        // 搜索发起会话
        params = {
          startType: that.chatType,
          merchantId: that.currentContainerID,
          appId: that.chatInfo.appId
        };
      } else if (that.currentContainerID) {
        // 非搜索发起
        params = {
          startType: ['0', '2'].includes(that.chatType) ? 5 : that.chatType,
          dialogId: that.currentContainerID,
          appId: that.chatInfo.appId
        };
      } else {
        that.$XyyMessage.error('缺少会话ID');
        return;
      }

      // 发起会话
      startConversation(params)
        .then(res => {
          if (res.code === 1) {
            that.$XyyMessage.success('发起会话成功');
            //只有待跟进、留言列表需要移除
            if (that.chatType === '1' || that.chatType === '3') {
              this.$parent.reChatRemoveAuto({
                tab: that.chatType,
                chat: { id: that.currentContainerID }
              });
            }
          } else {
            that.$XyyMessage.error(res.msg, 'apiStartTalking');
          }
        })
        .catch(err => {
          console.log(err, 'apiStartTalking:err');
        });
    },

    // 结束会话
    apiCloseTalking() {
      const that = this;
      if (!that.currentContainerID || that.currentContainerID === '0') {
        this.$XyyMessage.error('请选择会话');
        return;
      }
      const containerId = that.currentContainerID;
      this.$confirm('确定结束对话？', '提示', {
        distinguishCancelAndClose: true,
        customClass: 'im-confirm-msg-box',
        cancelButtonClass: 'cancel-button-class',
        confirmButtonText: '会话转至待跟进',
        cancelButtonText: '已处理结束会话',
        type: 'warning'
      })
        .then(() => {
          let loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(255,255,255, 0.8)'
          });
          // 转待跟进
          settodooo({ dialogid: containerId })
            .then(res => {
              if (res.code === 1) {
                this.$XyyMessage.success('转待跟进成功');
                this.$parent.closeChatRemoveAuto({
                  tab: that.chatType,
                  chat: { id: containerId },
                  removeTo: 'followed'
                });
              } else {
                this.$XyyMessage.error(res.msg || '服务异常');
              }
            })
            .catch(error => {
              console.log(error, 'settodooo');
            })
            .finally(() => {
              loading.close();
              this.$nextTick(() => {
                this.$refs.container_main_topview.upAction();
              });
            });
        })
        .catch(error => {
          if (error === 'cancel') {
            let loading = this.$loading({
              lock: true,
              text: 'Loading',
              spinner: 'el-icon-loading',
              background: 'rgba(255,255,255, 0.8)'
            });
            // 会话处理结束
            closeContainer({ dialogid: containerId })
              .then(res => {
                if (res.code === 1) {
                  this.$XyyMessage.success('会话结束成功');
                  this.$parent.closeChatRemoveAuto({
                    tab: that.chatType,
                    chat: { id: containerId },
                    removeTo: 'historical'
                  });
                } else {
                  this.$XyyMessage.error(res.msg || '服务异常');
                }
              })
              .catch(error => {
                console.log(error, 'closeContainer');
              })
              .finally(() => {
                loading.close();
                this.$nextTick(() => {
                  this.$refs.container_main_topview.upAction();
                });
              });
          } else {
            console.log(error, 'error');
          }
        });
    },

    // 添加黑名单
    apiAddblack(value) {
      const that = this;
      if (!that.currentContainerID || that.currentContainerID === '0') {
        this.$XyyMessage.error('请选择会话');
        return;
      }
      addblack({
        dialogId: this.currentContainerID,
        remark: value.remark,
        timeLimit: value.time
      }).then(res => {
        if (res.code !== 1) {
          this.$XyyMessage.error(res.msg);
        } else {
          this.$XyyMessage.success('添加黑名单成功');
        }
      });
    },

    // 重置聊天窗体
    resetChat() {
      Object.assign(this.$data, this.$options.data()); // 重置data数据
    },

    // 计算各状态下会话聊天组件的高度
    conntentHeight(type) {
      if (type === 'history' || type === 'other') {
        return 81;
      } else {
        return type === 'show' ? 45 + 220 : 134 + 220;
      }
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang='scss' scoped>
//@import url(); 引入公共css类
.im-view-center-chat-input-container {
  height: 210px;
}
//以下老组件
.im_container_midview {
  display: flex;
  flex-direction: column;
  min-width: 584px;
  width: 836px;
  overflow: hidden;
  max-width: 836px;
  @media screen and (min-width: 1200px) and (max-width: 1679px) {
    min-width: 584px;
  }
  @media screen and (min-width: 1680px) and (max-width: 1919px) {
    min-width: 664px;
  }
  padding: 16px 0 16px 16px;
  /* height: 775px; */
  background-color: #fff;
}

.im_container_chat_content {
  background-color: white;
}
.el-button--success {
  color: #ffffff;
  background-color: #3b95a8;
  border-color: #3b95a8;
}
</style>
