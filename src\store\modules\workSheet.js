const state = {
  orderModifyFlagtoList: []
};
const mutations = {
  SET_MODIFY_FLAG: (state, Modify) => {
    state.orderModifyFlagtoList.push(Modify);
  },
  DEL_MODIFY_FLAG: (state, Modify) => {
    for (let i = 0, l = state.orderModifyFlagtoList.length; i < l; i++) {
      if (state.orderModifyFlagtoList[i] === Modify) {
        state.orderModifyFlagtoList.splice(i, 1);
        break;
      }
    }
  }
};
export default {
  namespaced: true,
  state,
  mutations
};
