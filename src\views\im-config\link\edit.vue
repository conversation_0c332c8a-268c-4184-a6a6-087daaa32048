<template>
  <div class="im-link-box">
    <el-form ref="from" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="链接" prop="link">
        <el-input v-model="form.link" />
      </el-form-item>
      <el-form-item label="说明" prop="describe">
        <el-input v-model="form.describe" type="textarea" />
      </el-form-item>
      <el-form-item label=" ">
        <el-button type="primary" @click="checkTimer(save,'timer')()">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { linkEdit } from '@/api/configuration/use_link';
export default {
  data() {
    return {
      id: '',
      form: {
        name: '',
        link: '',
        describe: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { max: 20, message: '不超过20个字', trigger: 'blur' }
        ],
        link: [{ required: true, message: '请输入链接', trigger: 'blur' }],
        describe: [{ max: 200, message: '不超过200个字', trigger: 'blur' }]
      },
      timer: ''
    };
  },
  mounted() {
    if (this.$route.query.data) {
      const { id, title, link, des } = JSON.parse(this.$route.query.data);
      this.id = id;
      this.form.name = title;
      this.form.link = link;
      this.form.describe = des;
    }
  },
  activated() {
    if (this.$route.query.data) {
      this.$store.dispatch('tagsView/updateVisitedView', {
        ...this.$route,
        meta: {
          title: '编辑链接'
        }
      });
    }
  },
  methods: {
    save() {
      // createTime	创建时间	string	不必填
      // des	链接描述	string	不必填
      // link	链接	string	必填
      // linkId	链接id		不必填，传空添加链接，不为空编辑链接
      // title	链接名称	string	必填
      const param = {
        id: this.id,
        title: this.form.name,
        link: this.form.link,
        des: this.form.describe,
        createTime: ''
      };
      linkEdit(param).then(res => {
        if (res.code === 1) {
          if (this.id) {
            this.$XyyMessage.success('修改成功！');
          } else {
            this.$XyyMessage.success('添加成功！');
          }

          this.$store.dispatch('tagsView/delView', this.$route);
          this.$router.push({
            path: '/chat/link'
          });
        } else {
          this.$XyyMessage.error(res.msg);
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.im-link-box {
  .el-form {
    width: calc(100% - 655px);
    margin: 20px auto;
    .el-form-item {
      margin-bottom: 20px;

      /deep/.el-form-item__label {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(41, 41, 51, 1);
      }
      .el-input {
        /deep/.el-input__inner {
          height: 36px;
          line-height: 36px;
          border: 1px solid rgba(228, 228, 235, 1);
        }
      }
      .el-textarea {
        /deep/.el-textarea__inner {
          height: 211px;
          border: 1px solid rgba(228, 228, 235, 1);
        }
      }
      .el-button {
        height: 36px;
        padding: 0 12px;
        line-height: 36px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
        border-radius: 2px;
        border: 1px solid rgba(228, 228, 235, 1);
        &:focus,
        &:hover {
          background: #fff;
          border-color: rgba(228, 228, 235, 1);
        }
        &.el-button--primary {
          color: rgba(255, 255, 255, 1);
          padding: 0 20px;
          border: none;
        }
        &.el-button--primary:focus,
        &.el-button--primary:hover {
          background: #3b95a8;
          border-color: #3b95a8;
        }
      }
      /deep/ .el-form-item__content {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(87, 87, 102, 1);
      }
    }
  }
}
</style>
