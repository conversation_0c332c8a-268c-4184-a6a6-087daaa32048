<template lang="html">
  <div class="editor">
    <div ref="toolbar" class="toolbar">
    </div>
    <div ref="editor" class="text"></div>
    <p v-if='isShow' style="color:red;text-align: right;margin-top: 0;">编辑内容不能超过100个字</p>
    <span class="limitNum"><span id="spanId">{{ spanInfo }}</span>/100</span>
    <span class="elIconDelete" @click="deleteStudent"><img src="../../../assets/configuration/remove.png" width="14" height="14"></span>
  </div>
</template>

<script>
import E from 'wangeditor';
import emojiJSON from './emoji.json';
export default {
  name: 'editoritem',
  data() {
    return {
      // uploadPath,
      editor: null,
      info_: null,
      spanInfo: 0,
      isChange: false,
      isShow: false,
      emojiList: emojiJSON
    };
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    index: {
      type: Number,
      required: true
    },
    value: {
      type: String,
      default: ''
    },
    isClear: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    isClear(val) {
      // 触发清除文本域内容
      if (val) {
        this.editor.txt.clear();
        this.info_ = null;
      }
    },
    value: function(value, e) {
      if (!this.isChange) {
        if (value !== this.editor.txt.html()) {
          //          获取纯文字 在获取img 相加就可以了
          var temp = this.value.replace(/<\/?.+?>/g, '');
          var result = temp.replace(/ /g, ''); //result为获取冲文字得到后的内容
          //         获取img标签
          var re = /<img[^>]+>/g;
          var ImgA = this.value.match(re);
          let imgLen;
          if (ImgA) {
            imgLen = ImgA.length;
          }
          if (!ImgA) {
            imgLen = 0;
          }
          this.spanInfo = imgLen + result.length;
          if (this.spanInfo > 100) {
            this.isShow = true;
            // this.spanInfo = 100
            // this.editor.txt.html('<p>' + this.value.substr(0, 100) + '</p>')
          } else {
            this.isShow = false;
          }
          // this.editor.txt.html('<p>' + this.value.substr(0, 100) + '</p>')
          this.editor.txt.html(this.value);
        }
      }
      this.isChange = false;
    }
    //value为编辑框输入的内容，这里我监听了一下值，当父组件调用得时候，如果给value赋值了，子组件将会显示父组件赋给的值
  },
  mounted() {
    this.seteditor();
    //          获取纯文字 在获取img 相加就可以了
    var temp = this.value.replace(/<\/?.+?>/g, '');
    var result = temp.replace(/ /g, ''); //result为获取冲文字得到后的内容
    //         获取img标签
    var re = /<img[^>]+>/g;
    var ImgA = this.value.match(re);
    let imgLen;
    if (ImgA) {
      imgLen = ImgA.length;
    }
    if (!ImgA) {
      imgLen = 0;
    }
    this.spanInfo = imgLen + result.length;
    if (this.spanInfo > 100) {
      this.isShow = true;
      // this.spanInfo = 100
      // this.editor.txt.html('<p>' + this.value.substr(0, 100) + '</p>')
    } else {
      this.isShow = false;
    }
    // this.editor.txt.html('<p>' + this.value.substr(0, 100) + '</p>')
    this.editor.txt.html(this.value);
  },
  methods: {
    seteditor() {
      // http://192.168.2.125:8080/admin/storage/create
      this.editor = new E(this.$refs.toolbar, this.$refs.editor);
      this.editor.config.emotions = [
        {
          title: '默认',
          type: 'image',
          content: this.emojiList
        }
      ];
      this.editor.config.uploadImgShowBase64 = false; // base 64 存储图片
      this.editor.config.uploadImgServer =
        'http://otp.cdinfotech.top/file/upload_images'; // 配置服务器端地址
      this.editor.config.uploadImgHeaders = {}; // 自定义 header
      this.editor.config.uploadFileName = 'file'; // 后端接受上传文件的参数名
      this.editor.config.uploadImgMaxSize = 2 * 1024 * 1024; // 将图片大小限制为 2M
      this.editor.config.uploadImgMaxLength = 6; // 限制一次最多上传 3 张图片
      this.editor.config.uploadImgTimeout = 3 * 60 * 1000; // 设置超时时间

      // 配置菜单
      this.editor.config.menus = [
        'emoticon' // 表情
      ];
      var change = this.isChange;
      setInterval(() => {
        change = true;
      }, 500);
      this.editor.config.onchange = html => {
        this.isChange = change;
        this.info_ = html; // 绑定当前逐渐地值
        //          获取纯文字 在获取img 相加就可以了
        var temp = this.info_.replace(/<\/?.+?>/g, '');
        var result = temp.replace(/ /g, ''); //result为获取冲文字得到后的内容
        //         获取img标签
        var re = /<img[^>]+>/g;
        var ImgA = this.info_.match(re);
        let imgLen;
        if (ImgA) {
          imgLen = ImgA.length;
        }
        if (!ImgA) {
          imgLen = 0;
        }
        this.spanInfo = imgLen + result.length;
        //        去掉除img以外的所有标签；
        var regL = /<\/?(?!img)[a-z]+?[^>]*>/gi;
        var z = this.info_.replace(regL, '');
        this.info_ = z;
        if (this.spanInfo > 100) {
          this.isShow = true;
          // this.spanInfo = 100
          // this.editor.txt.html('<p>' + this.info_.substr(0, 100) + '</p>')
        } else {
          this.isShow = false;
        }
        this.$emit('change', this.info_); // 将内容同步到父组件中
      };
      // 创建富文本编辑器
      this.editor.create();
    },
    deleteStudent() {
      this.$emit('deleteIndex', this.index);
    }
  }
};
</script>

<style lang="scss">
.editor {
  width: 100%;
  margin: 0 auto;
  position: relative;
  // height: 99px;
  border: 1px solid #ccc;
  line-height: 25px;
  .limitNum {
    position: absolute;
    right: 6px;
    bottom: 0px;
    color: #909399;
  }
  .elIconDelete {
    position: absolute;
    right: -5%;
    top: 15%;
    cursor: pointer;
  }
  .toolbar {
    position: absolute;
    bottom: 0;
  }
  .w-e-toolbar .w-e-menu {
    padding: 0 10px;
  }
  .text {
    height: 98px;
    z-index: auto !important;
    .w-e-panel-container {
      z-index: 99999999;
      left: -1px;
      top: 100%;
      width: 70% !important;
      margin-left: 0 !important;
    }
    .w-e-text {
      overflow-y: auto;
    }
  }
}
</style>
