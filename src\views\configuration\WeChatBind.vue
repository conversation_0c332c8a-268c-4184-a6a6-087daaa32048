<template>
  <div class="WeChatBindTop">
    <xyy-list-page>
      <template slot="body">
        <div class="WeChatBind">
          <el-form ref="form" :model="form" label-width="150px">
            <el-form-item label="访客微信号绑定：">
              <el-radio-group v-model="form.switch">
                <el-radio :label="1">开启</el-radio>
                <el-radio :label="0">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item>微信端访客微信号与客户信息关联，绑定，再次访问即可直接获取客户信息</el-form-item>
            <el-form-item class="saveList">
              <el-button type="primary" @click="onSave">保存</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </xyy-list-page>
  </div>
</template>

<script>
import XyyListPage from '../../components/xyy/xyy-list-page/index';
import {
  getSwitchBind,
  ModifySwitchBind,
} from '@/api/configuration/RegionalMenu';
export default {
  name: 'compWeChatBind',
  components: { XyyListPage },
  data() {
    return {
      form: {
        switch: 0,
      },
    };
  },
  created() {
    this.getSwitch();
  },
  methods: {
    // 保存
    onSave() {
      const that = this;
      ModifySwitchBind(this.form.switch)
        .then((response) => {
          if (response.data === '保存成功') {
            that.$message('保存成功');
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    // 获取访客绑定按钮开关
    getSwitch() {
      let paramsL;
      getSwitchBind(paramsL)
        .then((response) => {
          this.form.switch = response.data['on-off'];
        })
        .catch(function (error) {
          console.log(error);
        });
    },
  },
};
</script>

<style scoped lang="scss">
.WeChatBindTop {
  /deep/.page {
    /deep/.page-body {
      height: 100% !important;
    }
  }
}
.WeChatBind {
  color: #909399;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .el-form {
    width: 40%;
    .el-form-item {
      margin-bottom: 0 !important;
    }
    .saveList {
      .el-form-item__content {
        margin-top: 10px;
      }
    }
  }
}
</style>
