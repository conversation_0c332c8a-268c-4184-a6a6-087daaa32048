import Vue from 'vue';
import 'babel-polyfill';
import Viewer from 'v-viewer';
import 'viewerjs/dist/viewer.css';
import 'normalize.css/normalize.css'; // A modern alternative to CSS resets
import ElementUI from 'element-ui';
// import 'element-ui/lib/theme-chalk/index.css'
import '@/styles/element-variables.scss';
import locale from 'element-ui/lib/locale/lang/zh-CN'; // lang i18n
import '@/styles/index.scss'; // global css
import '@/styles/xyy/index.scss'; // xyy css
import '@/styles/common.css'; // xyy css
import App from './App';
import store from './store';
import router from './router';
import VTree from 'vue-tree-halower';
import 'vue-tree-halower/dist/halower-tree.min.css'; // 你可以自定义树的样式

import '@/icons'; // icon
import '@/permission'; // permission control
import xyy from '@/components/xyy';
import Toast from '@/components/Toast';
import VueKindEditor from 'vue-kindeditor';
import 'kindeditor/kindeditor-all-min.js';
import 'kindeditor/themes/default/default.css';

import CM from 'components-mediator';
// import indexedDB from '@/utils/indexedDB';
/**
 * This project originally used easy-mock to simulate data,
 * but its official service is very unstable,
 * and you can build your own service if you need it.
 * So here I use Mock.js for local emulation,
 * it will intercept your request, so you won't see the request in the network.
 * If you remove `../mock` it will automatically request easy-mock data.
 */
// import '../mock'; // simulation data
Vue.config.errorHandler = function (err, vm, info) {
  // window.zhuge.track('error', {
  //   errmsg: err, // 错误信息
  //   path: window.location.href,
  //   info: info, // 详情信息
  //   time: new Date().getTime()
  // }, function () {
  //   // 在控制台打印错误信息
  //   console.log(err, vm, info);
  // });
  try {
    webSdk.track('error', {
      time: newDateFormatter(),
      path: window.location.href,
      errmsg: err, // 错误信息
      info: info, // 详情信息
    });
  } catch (error) {
    console.error(error);
  }
  console.error(err); // 使被vue捕获的异常也能被打印
};

/**
* 获取时间格式字符串到毫秒
*/
function newDateFormatter() {
  const d = new Date();
  return `${d.getFullYear()}-${(d.getMonth() + 1)
    .toString()
    .padStart(2, 0)}-${d
      .getDate()
      .toString()
      .padStart(2, 0)} ${d
        .getHours()
        .toString()
        .padStart(2, 0)}:${d
          .getMinutes()
          .toString()
          .padStart(2, 0)}:${d
            .getSeconds()
            .toString()
            .padStart(2, 0)}.${d.getMilliseconds().toString().padStart(3, 0)}`;
}

Vue.use(ElementUI, {
  locale
});
Vue.use(CM, { locale });
Vue.use(VTree);
Vue.use(xyy);
// Vue.use(indexedDB);
Vue.prototype.$toast = Toast;
Vue.prototype.checkPermission = function (code) {
  const currentId = window.localStorage.getItem('currentId');
  const item = store.getters.validBtn.filter(item => {
    return item.code === code;
  });
  return item.length !== 0;
};
Vue.prototype.changeStatus = function (value, strArr) {
  return strArr[parseInt(value)];
};
Vue.directive('loaded-callback', {
  inserted: function (el, binding, vnode) {
    binding.value(el, binding, vnode);
  }
});

Vue.directive('loadmore', {
  inserted: function (el, binding, vnode) {
    el.addEventListener('scroll', function () {
      if (this.scrollTop <= 20) {
        binding.value();
      }
    });
  }
});

window.Date.prototype.Format = function (fmt) {
  var o = {
    'M+': this.getMonth() + 1, // 月份
    'd+': this.getDate(), // 日
    'h+': this.getHours(), // 小时
    'm+': this.getMinutes(), // 分
    's+': this.getSeconds(), // 秒
    'q+': Math.floor((this.getMonth() + 3) / 3), // 季度
    S: this.getMilliseconds() // 毫秒
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, this.getFullYear() + '');
  }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      );
    }
  }
  return fmt;
};

Vue.filter('formatTime', function (value) {
  if (!value) return '';
  return new Date(value).Format('yy-MM-dd hh:mm:ss');
});
Vue.config.productionTip = false;

Vue.prototype.checkTimer = function () {
  // args 参数为fn,timer
  const args = Array.from(arguments);
  const _this = this;
  return function () {
    // arguments 为fn参数
    _this[args[1]]++;
    if (_this[args[1]] > 1) {
      return false;
    } else {
      setTimeout(_ => {
        _this[args[1]] = null;
      }, 1000);
      if (typeof args[0] === 'function') {
        return args[0].apply(this, arguments);
      }
    }
  };
};
Vue.filter('typeToName', function (value) {
  if (value === '') return '';
  let str = '';
  switch (parseInt(value)) {
    case 0:
      str = '单行文本';
      break;
    case 1:
      str = '多行文本';
      break;
    case 2:
      str = '下拉框';
      break;
    case 3:
      str = '单选';
      break;
    case 4:
      str = '多选';
      break;
  }
  return str;
});

Vue.use(VueKindEditor);
window.KindEditor.lang({
  emoticonss: '插入表情',
  imageinsert: '插入图片',
  fileinsert: '发送附件(支持mov/avi/mp4视频，表格，pdf)',
  transfer: '转接会话',
  history: '查看历史',
  assess: '邀请评价',
  sendcard: '发送名片'
});
Vue.use(Viewer);
Viewer.setDefaults({
  Options: {
    inline: true,
    button: true,
    navbar: true,
    title: true,
    toolbar: true,
    tooltip: true,
    movable: true,
    zoomable: true,
    rotatable: true,
    scalable: true,
    transition: true,
    fullscreen: true,
    keyboard: true,
    url: 'data-source'
  }
});

Vue.directive('paste', {
  bind(el, binding, vnode) {
    el.addEventListener('paste', function (event) {
      binding.value(event);
    });
  }
});
new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
});
