<template>
  <div class="business-content">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span class="titlefont">业务监控</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="downImg">保存图片</el-button>
      </div>
      <div style="width:100%">
        <div id="main" style="width: 100%;height:300px;"></div>
      </div>
    </el-card>
  </div>
</template>

<script>
import echarts from 'echarts';
export default {
  name: 'BusinessMonitor',
  data() {
    return {
      DataUrl: ''
    };
  },
  mounted() {
    // this.initChart();
  },
  methods: {
    initChart(arr) {
      const [xData, data1, data2] = arr;
      const myChart = echarts.init(document.getElementById('main'));

      // 指定图表的配置项和数据
      const option = {
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis'
        },
        // tooltip: {
        //   formatter: (params, ticket) => {
        //     console.log('执行了-----', params);
        //     const index = params.seriesIndex;
        //     console.log();
        //   }
        // },
        legend: {
          data: ['排队数', '通话数'],
          icon: 'circle',
          bottom: 0
        },
        calculable: true,
        xAxis: {
          data: xData,
          axisLabel: {
            interval: 0, // 横轴信息全部显示
            fontSize: 9
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '排队数',
            type: 'bar',
            data: data1,
            animation: false,
            label: {
              show: true
            },
            itemStyle: {
              color: '#3B95A8'
            }
          },
          {
            name: '通话数',
            type: 'bar',
            data: data2,
            animation: false,
            label: {
              show: true,
              position: 'inside'
            },
            itemStyle: {
              color: '#C6C6C6'
            }
          }
        ]
      };

      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option);

      // 暴露DataURL
      this.DataUrl = myChart.getDataURL({
        pixelRatio: 1,
        backgroundColor: '#fff'
      });
    },
    downImg() {
      this.downloadFile('业务监控.png', this.DataUrl);
    },
    // 下载
    downloadFile(fileName, content) {
      // content = content.substring(content.indexOf(',') + 1);
      const aLink = document.createElement('a');
      const blob = this.base64ToBlob(content); // new Blob([content]);
      const evt = document.createEvent('HTMLEvents');
      evt.initEvent('click', true, true);// initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
      aLink.download = fileName;
      aLink.href = URL.createObjectURL(blob);
      aLink.dispatchEvent(new MouseEvent('click', { bubbles: true, cancelable: true, view: window }));// 兼容火狐
    },
    // base64转blob
    base64ToBlob(code) {
      const parts = code.split(';base64,');
      const contentType = parts[0].split(':')[1];
      const raw = window.atob(parts[1]);
      const rawLength = raw.length;

      const uInt8Array = new Uint8Array(rawLength);

      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i);
      }
      return new Blob([uInt8Array], { type: contentType });
    }
  }
};
</script>
<style lang="scss" scoped>
.titlefont{font-weight: bold;font-size: 16px;}
</style>>
