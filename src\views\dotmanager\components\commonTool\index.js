// 深拷贝
export function deepClone(data) {
  var type = typeof (data);
  var obj;

  if (isArrayFn(data)) {
    obj = [];
  } else if (type === 'object' && !isArrayFn(data)) {
    obj = {};
  } else {
    // 不再具有下一层次
    return data;
  }
  if (isArrayFn(data)) {
    for (var i = 0, len = data.length; i < len; i++) {
      obj.push(deepClone(data[i]));
    }
  } else if (type === 'object' && !isArrayFn(data)) {
    for (var key in data) {
      obj[key] = deepClone(data[key]);
    }
  }
  return obj;
}

// 获取字典中所有的key
export function getObjectKeys(object) {
  var keys = [];
  for (var property in object) {
    keys.push(property);
  }
  return keys;
}
